package com.inossem.wms.common.model.sap.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SAP采购订单删除行项目参数
 */
@Data
public class HXPurchaseOrderDeleteItem {
    
    @ApiModelProperty(value = "行项目号 EBELP")
    private String itemCode = "";

    @ApiModelProperty(value = "物料编码 MATNR")
    private String matCode = "";
    
    @ApiModelProperty(value = "删除标识 LOEKZ")
    private String deleteFlag = ""; 
    
    @ApiModelProperty(value = "收货已完成标识 ELIKZ")
    private String receiptCompletedFlag = "";

    @ApiModelProperty(value = "工厂 WERKS")
    private String ftyCode = "";
} 