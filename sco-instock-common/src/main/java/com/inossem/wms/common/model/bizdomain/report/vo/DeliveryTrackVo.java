package com.inossem.wms.common.model.bizdomain.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 入库物资跟踪 查询出参
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "入库物资跟踪 查询出参传输对象", description = "入库物资跟踪 查询出参")
public class DeliveryTrackVo {
    @ApiModelProperty(value = "采购订单号" , example = "4500000001")
    @ExcelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行项目号" , example = "0010")
    @ExcelProperty(value = "采购订单行项目号")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "合同号" , example = "20201101001")
    @ExcelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同编号" , example = "20201101001")
    @ExcelIgnore
    private String contractName;

    @ApiModelProperty(value = "供应商" , example = "英诺森")
    @ExcelProperty(value = "供应商")
    private String supplierName;

    @ApiModelProperty(value = "供应商编码" , example = "60000001")
    @ExcelIgnore
    private String supplierCode;

    @ApiModelProperty(value = "到货通知单" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "到货通知单")
    private String deliveryCode;

    @ApiModelProperty(value = "到货通知日期" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "到货通知日期")
    private Date deliveryCompleteDate;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    @ExcelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "单位编码", example = "7")
    @ExcelIgnore
    private String unitCode;

    @ApiModelProperty(value = "单位描述", example = "个")
    @ExcelProperty(value = "计量单位")
    private String unitName;

    @ApiModelProperty(value = "到货数量", example = "个")
    @ExcelProperty(value = "到货数量")
    private String deliveryQty;


    @ApiModelProperty(value = "物料组code" , example = "g1")
    @ExcelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组描述" , example = "物料组描述")
    @ExcelProperty(value = "物料组描述")
    private String matGroupName;

    @ApiModelProperty(value = "工厂code" , example = "8000")
    @ExcelIgnore
    private String ftyCode;

    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    @ExcelProperty(value = "工厂描述")
    private String ftyName;

    @ApiModelProperty(value = "库存地点code" , example = "2700")
    @ExcelIgnore
    private String locationCode;

    @ApiModelProperty(value = "库存地点描述" , example = "英诺森001")
    @ExcelProperty(value = "库存地点描述")
    private String locationName;

    @ApiModelProperty(value = "到货登记单" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "到货登记单")
    private String registerCode;

    @ApiModelProperty(value = "103物料凭证" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "103物料凭证")
    private String registerMatDocCode;

    @ApiModelProperty(value = "103凭证日期" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "103凭证日期 ")
    private Date registerMatDocDate;

    @ApiModelProperty(value = "操作人" , example = "英诺森仓库沈阳")
    @ExcelIgnore
    private String registerUserCode;

    @ApiModelProperty(value = "操作人" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "操作人")
    private String registerUserName;

    @ApiModelProperty(value = "送货车辆" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "送货车辆")
    private String deliverycar;

    @ApiModelProperty(value = "存放地点" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "质检会签单" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "质检会签单")
    private String signInspectCode;

    @ApiModelProperty(value = "质检完成时间" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "质检完成时间")
    private Date signInspectCompleteDate;

    @ApiModelProperty(value = "不符合项通知单" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "不符合项通知单")
    private String inconformityNoticeCode;

    @ApiModelProperty(value = "入库单" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "入库单")
    private String inputCode;

    @ApiModelProperty(value = "105物料凭证" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "105物料凭证")
    private String inputMatDocCode;

    @ApiModelProperty(value = "105凭证日期" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "105凭证日期")
    private Date inputDocDate;

    @ApiModelProperty(value = "上架完成日期" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "上架完成日期")
    private Date inputDate;

    @ApiModelProperty(value = "操作人" , example = "英诺森仓库沈阳")
    @ExcelIgnore
    private String inputUserCode;

    @ApiModelProperty(value = "操作人" , example = "英诺森仓库沈阳")
    @ExcelProperty(value = "操作人")
    private String inputUserName;

    @ApiModelProperty(value = "仓位" , example = "00")
    @ExcelProperty(value = "仓位")
    private String binCode;

    @ApiModelProperty(value = "入库数量" , example = "50")
    @ExcelProperty(value = "入库数量")
    private BigDecimal qty;

//    @ApiModelProperty(value = "入库金额" , example = "151692536512514")
//    @ExcelProperty(value = "入库金额")
//    private String inputPrice;

    private BigDecimal dmbtr;

}
