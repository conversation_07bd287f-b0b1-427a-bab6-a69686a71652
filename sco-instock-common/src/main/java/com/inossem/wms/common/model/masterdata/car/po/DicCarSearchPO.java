package com.inossem.wms.common.model.masterdata.car.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@ApiModel(value = "车辆管理查询入参类", description = "车辆管理查询入参类")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DicCarSearchPO extends PageCommon {

    private Long id;

    @ApiModelProperty(value = "车辆编码" , example = "x")
    private String carCode;

    @ApiModelProperty(value = "车辆类型" , example = "x")
    private String carType;

    @ApiModelProperty(value = "车辆类型描述" , example = "x")
    private String carTypeName;

    @ApiModelProperty(value = "吊带编码" , example = "x")
    private String slingCode;
}
