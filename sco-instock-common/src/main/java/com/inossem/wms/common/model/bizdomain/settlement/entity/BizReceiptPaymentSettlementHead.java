package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentSettlementHead", description = "付款结算抬头表")
@TableName("biz_receipt_payment_settlement_head")
public class BizReceiptPaymentSettlementHead implements Serializable {
    private static final long serialVersionUID = 7572686469107359088L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "付款结算单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "结算描述", example = "2500")
    private String settlementDesc;

    @ApiModelProperty(value = "结算类型，1资金计划，2托收PO,3油品结算", example = "2500")
    private Integer settlementType;


    @ApiModelProperty(value = "合同id", example = "PD01000216")
    private Long contractId;

    @ApiModelProperty(value = "托收po批次id", example = "PD01000216")
    private Long deliveryId;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "付款币种")
    private Integer currency;

    @ApiModelProperty(value = "付款金额", example = "2500")
    private BigDecimal qty;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "扩展属性 - 不含税价格")
    private BigDecimal noTaxValue;

    @ApiModelProperty(value = "扩展属性 - 税码")
    private Integer taxCode;

    @ApiModelProperty(value = "合同总价含税", example = "2500")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "扩展属性 - 含税总计")
    private BigDecimal totalTaxIncluded;

    @ApiModelProperty(value = "扩展属性 - 本次发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "本币币种")
    private Integer invoiceCurrency;

    @ApiModelProperty(value = "扩展属性 - 申请支付金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "扩展属性 - 汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "扩展属性 - 汇率日期")
    private Date exchangeRateDate;

    @ApiModelProperty(value = " 扣款信息1")
    private BigDecimal deduct1;

    @ApiModelProperty(value = " 扣款信息2")
    private BigDecimal deduct2;

    @ApiModelProperty(value = " 扣款信息3")
    private BigDecimal deduct3;

    @ApiModelProperty(value = " 扣款信息34")
    private BigDecimal deduct4;

    @ApiModelProperty(value = " 扣款信息5")
    private BigDecimal deduct5;

    @ApiModelProperty(value = " 扣款信息6")
    private BigDecimal deduct6;

    @ApiModelProperty(value = " 扣款信息7")
    private BigDecimal deduct7;

    @ApiModelProperty(value = " 扣款信息8")
    private BigDecimal deduct8;

    @ApiModelProperty(value = " 扣款信息9")
    private BigDecimal deduct9;

    @ApiModelProperty(value = " 扣款信息10")
    private BigDecimal deduct10;

    @ApiModelProperty(value = " 扣款信息11")
    private BigDecimal deduct11;

    @ApiModelProperty(value = " 扣款信息12")
    private BigDecimal deduct12;

    @ApiModelProperty(value = " 扣款信息13")
    private BigDecimal deduct13;

    @ApiModelProperty(value = " 扣款信息14")
    private BigDecimal deduct14;

    @ApiModelProperty(value = " 扣款信息15")
    private BigDecimal deduct15;

    @ApiModelProperty(value = " 扣款信息16")
    private BigDecimal deduct16;

    @ApiModelProperty(value = " 扣款信息17")
    private BigDecimal deduct17;

    @ApiModelProperty(value = " 扣款信息18")
    private BigDecimal deduct18;


    @ApiModelProperty(value = " 是否完全结清")
    private Integer isCompletePaid;


    @ApiModelProperty(value = "住房结算ids")
    private String roomSettlementIds;


    @ApiModelProperty(value = "审批主管")
    private String approveUserCode;


}
