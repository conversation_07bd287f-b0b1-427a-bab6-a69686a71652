package com.inossem.wms.common.model.metadata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2021/03/02 19:56
 */
@Data
public class MetadataSelfFieldVO {

    @ApiModelProperty(value = "attrImport" , example = "String")
    private String attrImport;

    @ApiModelProperty(value = "字段是否于子属性或关联属性重复" , example = "0")
    private Integer isRepeat;

    @ApiModelProperty(value = "字段是否可编辑（0：否 1：是）" , example = "1")
    private Integer canEdit;

    @ApiModelProperty(value = "id" , example = "1")
    private Long id;

    @ApiModelProperty(value = "元数据ID" , example = "1")
    private Long metadataId;

    @ApiModelProperty(value = "字段名" , example = "String")
    private String colName;

    @ApiModelProperty(value = "属性名" , example = "单号")
    private String attrName;

    @ApiModelProperty(value = "属性类型名称" , example = "String")
    private String attrType;

    @ApiModelProperty(value = "关联表" , example = "String")
    private String rlatTableName;

    @ApiModelProperty(value = "关联源属性" , example = "String")
    private String sourceAttrName;

    @ApiModelProperty(value = "关联目标属性" , example = "String")
    private String targetAttrName;

    @ApiModelProperty(value = "字段备注" , example = "String")
    private String colRemark;

    @ApiModelProperty(value = "字段长度" , example = "225")
    private Integer colLength;

    @ApiModelProperty(value = "小数点" , example = "3")
    private Integer colDecimalPlace;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

}
