package com.inossem.wms.common.model.erp.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @Author: jhr
 * @Date: 2021/5/12 14:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ErpCostCenterQueryPO {
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "成本中心编码" , example = "ccc")
    private String costCenterCodeOrName;
}
