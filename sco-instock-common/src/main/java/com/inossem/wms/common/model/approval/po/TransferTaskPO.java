package com.inossem.wms.common.model.approval.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 转办任务入参
 *
 * <AUTHOR>
 * @date 2020/7/28 16:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "转办传输对象", description = "转办传输对象")
public class TransferTaskPO {

    @ApiModelProperty(value = "流程实例ID", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID", example = "1", required = true)
    private String taskId;

    @ApiModelProperty(name = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "用户code", example = "Admin", required = true)
    private String userCode;

    @ApiModelProperty(value = "用户名", example = "Admin", required = true)
    private String userName;

    @ApiModelProperty(value = "实际批注文本")
    private String commentMsg;

    @ApiModelProperty(value = "审批过程中上传的附件id列表")
    private List<Long> fileIdList;

    @ApiModelProperty(value = "审批过程中上传的图片id列表")
    private List<Long> imgIdList;

}
