package com.inossem.wms.common.model.stock.key;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 批次库存唯一索引
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "批次库存唯一索引", description = "批次库存唯一索引")
public class StockBatchKey implements Serializable {

    @ApiModelProperty(value = "工厂", example = "0001", required = true)
    protected Long ftyId;
    @ApiModelProperty(value = "库存地点", example = "0001", required = true)
    protected Long locationId;
    @ApiModelProperty(value = "物料编码", example = "0001", required = true)
    protected Long matId;
    @ApiModelProperty(value = "批次", example = "0001", required = true)
    protected Long batchId;

    public StockBatchKey(Long matId, Long batchId, Long ftyId, Long locationId) {
        this.matId = matId;
        this.batchId = batchId;
        this.ftyId = ftyId;
        this.locationId = locationId;
    }
}
