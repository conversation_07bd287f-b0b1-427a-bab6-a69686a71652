package com.inossem.wms.common.enums.contract;

import com.inossem.wms.common.model.common.enums.EnumContractPurchaseLocationMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同采购地枚举
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Getter
@AllArgsConstructor
public enum EnumContractPurchaseLocation {

    DOMESTIC(10, "中国采购"),
    PAKISTAN(20, "巴基斯坦采购"),
    THIRD_COUNTRY(30, "第三国采购");

    private final Integer code;
    private final String desc;

    private static List<EnumContractPurchaseLocationMapVO> list;

    public static List<EnumContractPurchaseLocationMapVO> toList() {
        if (list == null) {
            List<EnumContractPurchaseLocationMapVO> listInner = new ArrayList<>();
            EnumContractPurchaseLocation[] ary = EnumContractPurchaseLocation.values();
            for (EnumContractPurchaseLocation e : ary) {
                EnumContractPurchaseLocationMapVO vo = new EnumContractPurchaseLocationMapVO();
                vo.setPurchaseLocation(e.getCode());
                listInner.add(vo);
            }
            list = listInner;
        }
        return list;
    }

    public static EnumContractPurchaseLocation getByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (EnumContractPurchaseLocation e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
} 
