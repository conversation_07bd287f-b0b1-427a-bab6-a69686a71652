package com.inossem.wms.common.model.bizdomain.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.util.excel.DateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 出库台账 查询出参
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "出库台账 查询出参传输对象", description = "出库台账 查询出参")
public class OutputLedgerVo {

    @ExcelProperty(value = "公司代码")
    private String corpCode;
    @ExcelProperty(value = "工厂")
    private String ftyName;
    @ExcelProperty(value = "库存地点")
    private String locationName;
    @ExcelProperty(value = "物料编码")
    private String matCode;
    @ExcelProperty(value = "物料描述")
    private String matName;
    @ExcelProperty(value = "物料描述英文")
    private String matNameEn;
    @ExcelProperty(value = "制造商零件编号")
    private String extManufacturerPartNumber;
    @ExcelProperty(value = "基本物料")
    private String extMainMaterial;
    @ExcelProperty(value = "行业标准描述")
    private String extIndustryStandardDesc;
    @ExcelProperty(value = "物料组")
    private String matGroupName;
    @ExcelProperty(value = "领料申请单")
    private String receiptCode;
    @ExcelProperty(value = "领料单描述")
    private String remark;
    @ExcelIgnore
    private String innerPlan;
    @ExcelProperty(value = "是否计划内领用")
    private String innerPlanStr;
    @ExcelProperty(value = "状态备注")
    private String statusRemark;
    @ExcelProperty(value = "领料人")
    private String userName;
    @ExcelProperty(value = "部门")
    private String deptName;
//    @ExcelProperty(value = "科室")
//    private String deptOfficeName;
    @ExcelProperty(value = "申请单完成时间")
    private Date applyTime;
    @ExcelProperty(value = "领料出库单")
    private String outputReceiptCode;
    @ExcelIgnore
    private Integer receiptStatus;
    @ExcelProperty(value = "出库单状态")
    private String receiptStatusI18n;
    @ExcelProperty(value = "下架请求单")
    private String reqReceiptCode;
    @ExcelIgnore
    private Integer preReceiptStatus;
    @ExcelProperty(value = "下架单状态")
    private String preReceiptStatusI18n;
    @ExcelProperty(value = "下架完成时间")
    private Date reqTime;
    @ExcelProperty(value = "发货人姓名")
    private String outUserName;
    @ExcelProperty(value = "物料凭证")
    private String matDocCode;
    @ExcelProperty(value = "物料凭证行号")
    private String matDocRid;
    @ExcelProperty(value = "数量")
    private String qty;
    @ExcelProperty(value = "单位")
    private String unitName;
    @ExcelProperty(value = "批次号")
    private String batchCode;
//    @ExcelProperty(value = "WBS要素")
//    private String specStockCode;
//    @ExcelProperty(value = "WBS描述")
//    private String specStockName;
    @ExcelProperty(value = "入库日期", converter = DateConverter.class)
    private Date inputDate;
    @ExcelProperty(value = "过账日期", converter = DateConverter.class)
    private Date postingDate;
    @ExcelProperty(value = "凭证日期", converter = DateConverter.class)
    private Date docDate;
//    @ExcelProperty(value = "是否返回")
//    private String isReturnFlag;
//    @ExcelProperty(value = "对方科目描述")
//    private String subjectDesc;
//    @ExcelProperty(value = "项目定义")
//    private String projectDef;
//    @ExcelProperty(value = "项目名称")
//    private String projectName;
//    @ExcelProperty(value = "工单")
//    private String ticketOrder;
//    @ExcelProperty(value = "工单名称")
//    private String ticketOrderName;
//    @ExcelProperty(value = "库存地点描述")
//    private String locationDesc;
    @ApiModelProperty(value = "工厂code" , example = "8000")
    @ExcelIgnore
    private String ftyCode;
    @ApiModelProperty(value = "库存地点code" , example = "2700")
    @ExcelIgnore
    private String locationCode;
    @ApiModelProperty(value = "工单" , example = "7")
    @ExcelIgnore
    private String workReceipt;
    @ApiModelProperty(value = "工单" , example = "7")
    @ExcelIgnore
    private String workReceiptName;
    @ApiModelProperty(value = "物料组code", example = "g1")
    @ExcelIgnore
    private String matGroupCode;
    @ApiModelProperty(value = "领料人编码", example = "13221321")
    @ExcelIgnore
    private String userCode;
    @ApiModelProperty(value = "部门编码", example = "13221321")
    @ExcelIgnore
    private String deptCode;
    @ApiModelProperty(value = "科室编码", example = "13221321")
    @ExcelIgnore
    private String deptOfficeCode;
    @ApiModelProperty(value = "单位编码" , example = "7")
    @ExcelIgnore
    private String unitCode;
    @ApiModelProperty(value = "出库人编码" , example = "1")
    @ExcelIgnore
    private String outUserCode;
    @ExcelIgnore
    private Integer isWriteOff;
    @ExcelIgnore
    private Long itemId;
    @ExcelProperty(value = "仓位")
    private String binCode;
    @ApiModelProperty(value = "出库单行项目备注" , example = "备注信息")
    @ExcelProperty(value = "出库单行项目备注")
    private String outputReceiptItemRemark;
}
