package com.inossem.wms.common.enums.purchase;

import com.inossem.wms.common.model.common.enums.EnumPurchaseTypeMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 采购类型枚举
 */
@Getter
@AllArgsConstructor
public enum EnumPurchaseType {

    PRODUCTION_MATERIAL(1, "生产物资类"),
    NON_PRODUCTION_MATERIAL(2, "非生产物资类"),
    SERVICE(3, "服务类"),
    CONSTRUCTION(4, "施工类"),
    ASSET(5, "资产类"),
    D2D_DELIVERY(6, "门到门送货"),
    OIL(7, "油品类");

    private final Integer code;
    private final String desc;

    private static List<EnumPurchaseTypeMapVO> list;

    public static List<EnumPurchaseTypeMapVO> toList() {
        if (list == null) {
            List<EnumPurchaseTypeMapVO> listInner = new ArrayList<>();
            EnumPurchaseType[] ary = EnumPurchaseType.values();
            for (EnumPurchaseType e : ary) {
                EnumPurchaseTypeMapVO vo = new EnumPurchaseTypeMapVO();
                vo.setDemandType(e.getCode());
                listInner.add(vo);
            }
            list = listInner;
        }
        return list;
    }

    public static EnumPurchaseType getByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (EnumPurchaseType e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
} 
