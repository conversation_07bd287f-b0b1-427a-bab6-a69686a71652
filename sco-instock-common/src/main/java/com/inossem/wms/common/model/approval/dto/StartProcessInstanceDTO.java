package com.inossem.wms.common.model.approval.dto;

import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 发起流程
 * 
 * <AUTHOR>
 * @date 2020/7/28 15:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "发起流程传输对象", description = "发起流程传输对象")
public class StartProcessInstanceDTO {
    @ApiModelProperty(value = "发起人ID", example = "1", required = true)
    private Long userId;

    @ApiModelProperty(value = "发起人code", example = "Admin", required = true)
    private String userCode;

    @ApiModelProperty(value = "流程定义KEY", example = "key", required = true)
    private String processDefinitionKey;

    @ApiModelProperty(value = "业务KEY", example = "businessKey", required = true)
    private String businessKey;

    @ApiModelProperty(value = "流程变量", required = true)
    private Map<String, Object> variables;
}
