package com.inossem.wms.common.model.bizdomain.output.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 出库配货查询PO:仅比BizReceiptOutputItemDTO多itemDTOList
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存特性查询PO", description = "库存特性查询PO")
public class BizReceiptOutputSearchPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "已配货行项目")
    List<BizReceiptOutputItemDTO> itemDTOList;

    @ApiModelProperty(value = "是否是领料申请-领料申请需要按照物料描述模糊查询" , example = "1")
    private Integer isApplyFlag;

    /* ********************** 扩展字段开始 *************************/
    @ApiModelProperty(value = "批次图片")
    List<BizBatchImgDTO> batchImgList;
    @ApiModelProperty(value = "行项目配货信息列表")
    @SonAttr(sonTbName = "biz_receipt_output_bin", sonTbFkAttrName = "itemId")
    private List<BizReceiptOutputBinDTO> itemInfoList;
    @ApiModelProperty(value = "特征列表")
    @SonAttr(sonTbName = "biz_receipt_assemble", sonTbFkAttrName = "receiptItemId")
    private List<BizReceiptAssembleDTO> assembleDTOList;
    @ApiModelProperty(value = "出库单code" , example = "CK01000400")
    private String receiptCode;
    @ApiModelProperty(value = "单据类型 销售出库411 预留出库412 采购退货413 领料出库414 报废出库415 其他出库416 配送出库419")
    private Integer receiptType;
    @ApiModelProperty(value = "10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成、70已退库" , example = "10")
    private Integer receiptStatus;
    @ApiModelProperty(value = "订单要求出库数量" , example = "100")
    private BigDecimal receiptQty;
    @ApiModelProperty(value = "已创建数" , example = "20")
    private BigDecimal createdQty;
    @ApiModelProperty(value = "已发货数量(ZFHSL)" , example = "10")
    private BigDecimal submitQty;
    @ApiModelProperty(value = "可退库数" , example = "10")
    private BigDecimal canReturnQty;
    @ApiModelProperty(value = "可出库数" , example = "10")
    private BigDecimal availableQty;
    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;
    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    private String locationCode;
    @ApiModelProperty(value = "库存地点描述" , example = "英诺森001")
    private String locationName;
    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;
    @ApiModelProperty(value = "计量单位名称" , example = "立方米")
    private String unitName;
    @ApiModelProperty(value = "移动类型编码" , example = "301")
    private String moveTypeCode;
    @ApiModelProperty(value = "移动类型描述" , example = "Q转Q(工厂)")
    private String moveTypeName;
    @ApiModelProperty(value = "移动类型特殊库存标识" , example = "Q")
    private String moveTypeSpecStock;
    @ApiModelProperty(value = "小数位" , example = "3")
    private Integer decimalPlace;
    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;
    @ApiModelProperty(value = "工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;
    @ApiModelProperty(value = "仓库编码" , example = "S200")
    private String whCode;
    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    private String whName;
    @ApiModelProperty(value = "是否启用仓位" , example = "1")
    private Integer binEnabled;
    @ApiModelProperty(value = "用户编码" , example = "管理员")
    private String createUserName;
    @ApiModelProperty(value = "预留单行号" , example = "1")
    private String preReceiptRid;
    @ApiModelProperty(value = "前置单据code" , example = "0000001601")
    private String preReceiptCode;
    @ApiModelProperty(value = "特征code" , example = "Q")
    private String specCode;
    @ApiModelProperty(value = "特征值" , example = "Q")
    private String specValue;
    @ApiModelProperty(value = "物料erp批次是否启用" , example = "1")
    private Integer matBatchErpEnabled;
    @ApiModelProperty(value = "物料生产批次是否启用" , example = "1")
    private Integer matBatchProductEnabled;
    @ApiModelProperty(value = "物料包装是否启用" , example = "1")
    private Integer matPackageEnabled;
    @ApiModelProperty(value = "erp创建人描述" , example = "管理员")
    private String erpCreateUserName;
    @ApiModelProperty(value = "erp创建时间" , example = "2021-05-11")
    private Date erpCreateTime;
    @ApiModelProperty(value = "成本中心描述" , example = "成本中心描述")
    private String reserveCostCenterName;
    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    private String specStockCode;
    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    /* ********************** 扩展字段结束 *************************/
    @ApiModelProperty(value = "收货方描述" , example = "1")
    private String receivingPartyName;
    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_output_head", sourceAttrName = "receiptCode,receiptType,receiptStatus",
            targetAttrName = "receiptCode,receiptType,receiptStatus")
    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "出库单行项目号" , example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "行项目配货出库数量总数" , example = "10")
    private BigDecimal qty;

    @ApiModelProperty(value = "已作业数量" , example = "5")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "erp批次" , example = "00016603")
    private String batchErp;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "订单单位id" , example = "7")
    private Long unitId;

    @RlatAttr(rlatTableName = "erp_reserve_receipt_head", sourceAttrName = "receiptCode,erpCreateUserName,erpCreateTime",
            targetAttrName = "preReceiptCode,erpCreateUserName,erpCreateTime")
    @ApiModelProperty(value = "前序单据head表id" , example = "151692536512513")
    private Long preReceiptHeadId;

    @RlatAttr(rlatTableName = "erp_reserve_receipt_item", sourceAttrName = "receiptQty,submitQty,reserveCostCenterName,receivingPartyName,specStockCode",
            targetAttrName = "receiptQty,submitQty,reserveCostCenterName,receivingPartyName,specStockCode")
    @ApiModelProperty(value = "前序单据item表id" , example = "151692536512516")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前序单据类型" , example = "511")
    private Integer preReceiptType;

    @ApiModelProperty(value = "订单要求出库数量" , example = "100")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head表id" , example = "152808500297729")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item表id" , example = "152412159541249")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号" , example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度" , example = "2015-05-11")
    private String matDocYear;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销、70已退库" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "是否冲销【1是，0否】" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销凭证号" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过账时间" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销凭证行号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销凭证年度行号" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "批次id" , example = "5")
    private Long leisureBatchId;
    
    @ApiModelProperty(value = "批次号" , example = "5")
    private String batchCode;

    @ApiModelProperty(value = "成套物料编码")
    private String mMatCode;
    private String mMatName;
    private String functionalLocationCode;
    private String extend20;
    private String extend24;
    private String extend29;

    @ApiModelProperty(value = "采购订单")
    private String referReceiptCode;

    @ApiModelProperty(value = "采购订单行号")
    private String referReceiptRid;

    @ApiModelProperty(value = "需求计划")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求人")
    private String demandUserName;


    @ApiModelProperty(value = "单据描述" , example = "RK0001000633")
    private String des;

    @ApiModelProperty(value = "领用类型 1 手工领用 2需求计划领用" )
    private Integer receiveType;

    @ApiModelProperty(value = "SAP响应字段 MFRPN 制造商零件编号")
    private String extManufacturerPartNumber;

    @ApiModelProperty(value = "SAP响应字段 WRKST 基本物料")
    private String extMainMaterial;

    @ApiModelProperty(value = "SAP响应字段 NORMT 行业标准描述")
    private String extIndustryStandardDesc;

}
