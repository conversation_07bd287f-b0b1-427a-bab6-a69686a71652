package com.inossem.wms.common.model.bizdomain.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 到货接收跟踪报表 查询出参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "到货接收跟踪报表 查询出参传输对象", description = "到货接收跟踪报表 查询出参")
public class ArrivalTrackVO {

    @ApiModelProperty(value = "工厂id")
    @ExcelIgnore
    private Long ftyId;

    @ApiModelProperty(value = "到货登记单")
    @ExcelProperty(value = "到货登记单")
    private String receiptCode;

    @ApiModelProperty(value = "箱件创建时间")
    @ExcelProperty(value = "箱件创建时间")
    private Date createTime;

    @ApiModelProperty(value = "箱件提交时间")
    @ExcelProperty(value = "箱件提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "滞留天数")
    @ExcelProperty(value = "滞留天数")
    private Integer delayDays;

    @ApiModelProperty(value = "是否进口核安全设备")
    @ExcelIgnore
    private Integer isSafe;

    @ApiModelProperty(value = "是否进口核安全设备")
    @ExcelProperty(value = "是否进口核安全设备")
    private String isSafeStr;

    @ApiModelProperty(value = "采购类型")
    @ExcelIgnore
    private Integer procurementMethod;

    @ApiModelProperty(value = "采购类型")
    @ExcelProperty(value = "采购类型")
    private String procurementMethodStr;

    @ApiModelProperty(value = "完成状态")
    @ExcelProperty(value = "完成状态")
    private String receiptStatus;

    @ApiModelProperty(value = "箱件编号")
    @ExcelProperty(value = "箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "包装方式")
    @ExcelProperty(value = "包装方式")
    private String packageType;

    @ApiModelProperty(value = "箱件尺寸（米）")
    @ExcelProperty(value = "箱件尺寸（米）")
    private String caseSize;

    @ApiModelProperty(value = "长（CM）")
    @ExcelProperty(value = "长（CM）")
    private String extend70;

    @ApiModelProperty(value = "宽（CM）")
    @ExcelProperty(value = "宽（CM）")
    private String extend71;

    @ApiModelProperty(value = "高（CM）")
    @ExcelProperty(value = "高（CM）")
    private String extend72;

    @ApiModelProperty(value = "毛重（千克）")
    @ExcelProperty(value = "毛重（千克）")
    private String caseWeight;

    @ApiModelProperty(value = "箱件状态")
    @ExcelIgnore
    private Integer visualCheck;

    @ApiModelProperty(value = "成套箱件状态")
    @ExcelIgnore
    private Integer unitizedVisualCheck;

    @ApiModelProperty(value = "箱件状态")
    @ExcelProperty(value = "箱件状态")
    private String visualCheckStr;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String waybillRemark;
}
