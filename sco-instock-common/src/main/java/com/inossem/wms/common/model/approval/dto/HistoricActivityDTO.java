package com.inossem.wms.common.model.approval.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 历史活动传输对象
 * 
 * <AUTHOR>
 * @date 2020/7/27 17:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "历史活动传输对象", description = "历史活动传输对象")
public class HistoricActivityDTO {

    @ApiModelProperty(value = "批注" , example = "批注")
    private String comment;

    @ApiModelProperty(value = "经办人姓名" , example = "管理员")
    private String assigneeName;

    @ApiModelProperty(value = "activityId", example = "1")
    private String activityId;

    @ApiModelProperty(value = "activity名称", example = "1")
    private String activityName;

    @ApiModelProperty(value = "activity类型", example = "1")
    private String activityType;

    @ApiModelProperty(value = "流程定义ID", example = "1", required = true)
    private String processDefinitionId;

    @ApiModelProperty(value = "流程实例ID" , example = "282501" , required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID", example = "1", required = true)
    private String taskId;

    @ApiModelProperty(value = "经办人" , example = "Admin" )
    private String assignee;

    @ApiModelProperty(value = "开始时间", example = "2021-05-10")
    private Date startTime;

    @ApiModelProperty(value = "结束时间", example = "2022-05-10")
    private Date endTime;

    @ApiModelProperty(value = "持续时间", example = "10")
    private Long durationInMillis;

    @ApiModelProperty(value = "删除原因", example = "删除")
    private String deleteReason;
}
