package com.inossem.wms.common.model.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysMetadataModelParamObj对象", description="")
@TableName("sys_metadata_model_param_obj")
public class SysMetadataModelParamObj implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "po类名" , example = "String")
    private String className;

    @ApiModelProperty(value = "包路径" , example = "String")
    private String packagePath;

    @ApiModelProperty(value = "是否是分页查询参数" , example = "1")
    private Integer isPaging;

    @ApiModelProperty(value = "类描述（备注）" , example = "备注")
    private String remark;


}
