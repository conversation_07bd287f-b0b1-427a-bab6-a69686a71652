package com.inossem.wms.common.constant.mq;

/**
 * mq tag常量类
 *
 * <AUTHOR>
 */
public class TagConst {

    /**
     * 推送MQ - 生成用户与报表文件关系
     */
    public static final String GEN_USER_FILE = "gen_user_file";

    /**
     * 推送MQ - 更新用户与报表文件关系
     */
    public static final String UPDATE_USER_FILE = "update_user_file";

    /**
     * 推送MQ - 前置单据生成验收入库单
     */
    public static final String GEN_INSPECT_INPUT_STOCK = "gen_inspect_input_stock";

    /**
     * 推送MQ - 前置单据生成采购入库单
     */
    public static final String GEN_PURCHASE_INPUT_STOCK = "gen_purchase_input_stock";

    /**
     * 推送MQ - 前置单据生成生产入库单
     */
    public static final String GEN_PRODUCT_INPUT_STOCK = "gen_product_input_stock";

    /**
     * 推送MQ - 前置单据生成采购入库单
     */
    public static final String GEN_WORTHLESS_INPUT_STOCK = "gen_worthless_input_stock";

    /**
     * 推送MQ - 前置单据生成借用出库单
     */
    public static final String GEN_BORROW_OUTPUT_STOCK = "gen_borrow_output_stock";

    /**
     * 推送MQ - 前置单据生成维修出库单
     */
    public static final String GEN_REPAIR_OUTPUT_STOCK = "gen_repair_output_stock";

    /**
     * 推送MQ - 前置单据生成工具归还单
     */
    public static final String GEN_BORROW_INPUT_STOCK = "gen_borrow_input_stock";

    /**
     * 推送MQ - 前置单据生成到货登记单
     */
    public static final String GEN_ARRIVAL_REGISTER_STOCK = "gen_arrival_register_stock";

    /**
     * 推送MQ - 前置单据生成分配质检单
     */
    public static final String GEN_DISTRIBUTE_INSPECT_STOCK = "gen_distribute_inspect_stock";
    /**
     * 推送MQ - 前置单据生成物资返运单
     */
    public static final String GEN_MAT_RETURN = "gen_mat_return";

    /**
     * 推送MQ - 前置单据生成质检会签单
     */
    public static final String GEN_SIGN_INSPECT_STOCK = "gen_sign_inspect_stock";

    /**
     * 推送MQ - 前置单据生成不符合项处置单
     */
    public static final String GEN_INCONFORMITY_MAINTAIN_STOCK = "gen_inconformity_maintain_stock";

    /**
     * 推送MQ - 前置单据生成退库质检单
     */
    public static final String GEN_MAT_REQ_RETURN_INSPECT_STOCK = "gen_mat_req_return_inspect_stock";

    /**
     * 推送MQ - 前置单据生成退库不合格项维护单
     */
    public static final String GEN_MAT_REQ_RETURN_INCONFORMITY_MAINTAIN_STOCK = "gen_mat_req_return_inconformity_maintain_stock";

    /**
     * 推送MQ - 前置单据生成成套设备退库不合格项维护单
     */
    public static final String GEN_UNITIZED_MAT_REQ_RETURN_INCONFORMITY_MAINTAIN_STOCK = "gen_unitized_mat_req_return_inconformity_maintain_stock";

    /**
     * 推送MQ - 前置单据生成领料退库单
     */
    public static final String GEN_MAT_REQ_RETURN_STOCK = "gen_mat_req_return_stock";

    /**
     * 推送MQ - 前置单据生成退转库质检单
     */
    public static final String GEN_TRANSFER_RETURN_INSPECT_STOCK = "gen_transfer_return_inspect_stock";

    /**
     * 推送MQ - 前置单据生成成套设备退转库质检单
     */
    public static final String GEN_UNITIZED_MAT_REQ_RETURN_STOCK = "gen_unitized_mat_req_return_stock";

    /**
     * 推送MQ - 前置单据生成退转库不合格项维护单
     */
    public static final String GEN_TRANSFER_RETURN_INCONFORMITY_MAINTAIN_STOCK = "gen_transfer_return_inconformity_maintain_stock";

    /**
     * 推送MQ - 前置单据生成退转库入库单
     */
    public static final String GEN_TRANSFER_RETURN_STOCK = "gen_transfer_return_stock";

    /**
     * 推送MQ - 前置单据生成采购退货单
     */
    public static final String GEN_PURCHASE_RETURN_STOCK = "gen_purchase_return_stock";

    /**
     * 推送MQ - 前置单据生成寿期检定结果维护单
     */
    public static final String GEN_LIFETIME_MAINTAIN_STOCK = "gen_lifetime_maintain_stock";
    public static final String GEN_LIFETIME_MAINTAIN_STOCK_CE = "gen_lifetime_maintain_stock_ce";

    /**
     * 推送MQ - 前置单据生成报废冻结单
     */
    public static final String GEN_SCRAP_FREEZE_STOCK = "gen_scrap_freeze_stock";

    /**
     * 推送MQ - 前置单据生成维保计划创建单
     */
    public static final String GEN_MAINTAIN_PLAN_STOCK = "gen_maintain_plan_stock";

    /**
     * 推送MQ - 前置单据生成维保结果维护单
     */
    public static final String GEN_MAINTAIN_RESULT_STOCK = "gen_maintain_result_stock";
    public static final String GEN_MAINTAIN_RESULT_STOCK_CE = "gen_maintain_result_stock_ce";

    /**
     * 推送MQ - 前置单据生成成套设备到货登记单
     */
    public static final String GEN_UNITIZED_ARRIVAL_REGISTER_STOCK = "gen_unitized_arrival_register_stock";

    /**
     * 推送MQ - 前置单据生成成套设备分配质检单
     */
    public static final String GEN_UNITIZED_DISTRIBUTE_INSPECT_STOCK = "gen_unitized_distribute_inspect_stock";

    /**
     * 推送MQ - 前置单据生成成套设备质检会签单
     */
    public static final String GEN_UNITIZED_SIGN_INSPECT_STOCK = "gen_unitized_sign_inspect_stock";

    /**
     * 推送MQ - 前置单据生成成套设备质量差异不符合项处置单
     */
    public static final String GEN_UNITIZED_QUALITY_INCONFORMITY_MAINTAIN_STOCK = "gen_unitized_quality_inconformity_maintain_stock";

    /**
     * 推送MQ - 前置单据生成成套设备数量差异不符合项处置单
     */
    public static final String GEN_UNITIZED_NUMBER_INCONFORMITY_MAINTAIN_STOCK = "gen_unitized_number_inconformity_maintain_stock";

    /**
     * 推送MQ - 前置单据生成成套设备数量差异不符合项通知单
     */
    public static final String GEN_UNITIZED_NUMBER_INCONFORMITY_NOTICE_STOCK = "gen_unitized_number_inconformity_notice_stock";

    /**
     * 推送MQ - 前置单据生成成套设备验收入库单
     */
    public static final String GEN_UNITIZED_INSPECT_INPUT_STOCK = "gen_unitized_inspect_input_stock";

    /**
     * 推送MQ - 保存盘点凭证仓位库存批次库存
     */
    public static final String GEN_STOCKTAKING_DOC = "gen_stocktaking_doc";

    /**
     * 推送MQ - 物料标签打印
     */
    public static final String PRINT_MAT_LABEL = "print_mat_label";


    /**
     * 推送MQ - 到货登记待检标识-打印
     */
    public static final String PRINT_RECEIPT_REGISTER_BOX_LABEL = "print_output_box_label";
    /**
     * 推送MQ - 验收入库单-物料标签打印-PDA
     *  <p>暂存物项入库单-物料标签打印</p>
     *  <p>废旧物资入库-物料标签打印</p>
     *  <p>退旧物资入库-物料标签打印</p>
     *  <p>闲置物资入库-物料标签打印</p>
     *  <p>其他入库单-物料标签打印</p>
     *  <p>退转库过账-物料标签打印</p>
     */
    public static final String PRINT_INSPECT_INPUT_BOX_LABEL = "print_inspect_input_box_label";
    public static final String PRINT_INSPECT_INPUT_BOX_LABEL_UNITIZED = "print_inspect_input_box_label_unitized";

    /**
     * 推送MQ - 闲置物资转库-物料标签打印
     */
    public static final String PRINT_TRANSPORT_LEISURE_BOX_LABEL = "print_transport_leisure_box_label";

    /**
     * 推送MQ - 生成上架请求
     */
    public static final String GENERATE_LOAD_REQ_TAGS = "generate_load_req";

    /**
     * 推送MQ - 生成下架请求
     */
    public static final String GENERATE_UNLOAD_REQ_TAGS = "generate_unload_req";

    /**
     * 推送MQ - 生成码盘单
     */
    public static final String GENERATE_PALLET_SORTING_TAGS = "generate_pallet_sorting";

    /**
     * 推送MQ - 验收入库上架作业回调
     */
    public static final String TASK_INSPECT_INPUT_CALLBACK = "task_inspect_input_callback";

    /**
     * 推送MQ - 成套设备验收入库上架作业回调
     */
    public static final String TASK_UNITIZED_INSPECT_INPUT_CALLBACK = "task_unitized_inspect_input_callback";

    /**
     * 推送MQ - 生产入库上架作业回调
     */
    public static final String TASK_PRODUCTION_INPUT_CALLBACK = "task_production_input_callback";

    /**
     * 推送MQ - 采购入库上架作业回调
     */
    public static final String TASK_PURCHASE_INPUT_CALLBACK = "task_purchase_input_callback";

    /**
     * 推送MQ - 零价值入库上架作业回调
     */
    public static final String TASK_WORTHLESS_INPUT_CALLBACK = "task_worthless_input_callback";

    /**
     * 推送MQ - 其他入库上架作业回调
     */
    public static final String TASK_OTHER_INPUT_CALLBACK = "task_other_input_callback";
    /**
     * 推送MQ - 暂存入库上架作业回调
     */
    public static final String TASK_TEMPORARY_STORAGE_INPUT_CALLBACK = "task_temporary_storage_input_callback";
    public static final String UNITIZED_TASK_TEMPORARY_STORAGE_INPUT_CALLBACK = "unitized_task_temporary_storage_input_callback";

    /**
     * 推送MQ - 退旧入库上架作业回调
     */
    public static final String TASK_RETURN_OLD_INPUT_CALLBACK = "task_return_old_input_callback";

    /**
     * 推送MQ - 闲置物资上架作业回调
     */
    public static final String TASK_LEISURE_INPUT_CALLBACK = "task_leisure_input_callback";

    /**
     * 推送MQ - 删除作业请求行项目
     */
    public static final String DEL_RECEIPT_REQ_ITEM = "del_receipt_req_item";

    /**
     * 推送MQ - 领料出库下架回调
     */
    public static final String TASK_MAT_REQ_OUTPUT_CALLBACK = "task_mat_req_output_callback";

    /**
     * 推送MQ - 领料出库下架回调
     */
    public static final String TASK_TRANSFER_RETURN_MAT_REQ_OUTPUT_CALLBACK = "task_transferreturn_mat_req_output_callback";


    /**
     * 推送MQ - 成套领料出库下架回调
     */
    public static final String UNITIZED_TASK_MAT_REQ_OUTPUT_CALLBACK = "unitized_task_mat_req_output_callback";

    /**
     * 推送MQ - 采购退货下架回调
     */
    public static final String TASK_PURCHASE_RETURN_CALLBACK = "task_purchase_return_callback";

    /**
     * 推送MQ - 其他出库下架回调
     */
    public static final String TASK_OTHER_OUTPUT_CALLBACK = "task_other_output_callback";

    /**
     * 推送MQ - 其他出库下架回调
     */
    public static final String TASK_TOOL_REPAIR_OUTPUT_CALLBACK = "task_tool_repair_output_callback";

    /**
     * 推送MQ - 其他出库下架回调
     */
    public static final String TASK_WASTE_OUTPUT_CALLBACK = "task_waste_output_callback";

    /**
     * 推送MQ - 退旧出库下架回调
     */
    public static final String TASK_RETURN_OLD_OUTPUT_CALLBACK = "task_return_old_output_callback";

    /**
     * 推送MQ - 零价值出库下架回调
     */
    public static final String TASK_WORTHLESS_OUTPUT_CALLBACK = "task_worthless_output_callback";

    /**
     * 推送MQ - 报废出库下架回调
     */
    public static final String TASK_SCRAP_OUTPUT_CALLBACK = "task_scrap_output_callback";

    /**
     * 推送MQ - 临时出库下架回调
     */
    public static final String TASK_TEMP_OUTPUT_CALLBACK = "task_temp_output_callback";

    /**
     * 推送MQ - 销售出库下架回调
     */
    public static final String TASK_SALE_OUTPUT_CALLBACK = "task_sale_output_callback";

    /**
     * 推送MQ - 领料退库上架作业回调
     */
    public static final String TASK_MAT_REQ_RETURN_CALLBACK = "task_mat_req_return_callback";

    /**
     * 推送MQ - 成套设备领料退库上架作业回调
     */
    public static final String TASK_UNITIZED_MAT_REQ_RETURN_CALLBACK = "task_unitized_mat_req_return_callback";

    /**
     * 推送MQ - 退转库入库上架作业回调
     */
    public static final String TASK_TRANSFER_RETURN_CALLBACK = "task_transfer_return_callback";

    /**
     * 推送MQ - 销售退库上架作业回调
     */
    public static final String TASK_SALE_RETURN_CALLBACK = "task_sale_return_callback";

    /**
     * 推送MQ - 转储下架回调
     */
    public static final String TASK_TRANSPORT_UNLOAD_CALLBACK = "task_transport_unload_callback";

    /**
     * 推送MQ - 成套设备转储下架回调
     */
    public static final String TASK_UNITIZED_TRANSPORT_UNLOAD_CALLBACK = "task_unitized_transport_unload_callback";

    /**
     * 推送MQ - 调拨下架回调
     */
    public static final String TASK_TRANSPORT_OUT_UNLOAD_CALLBACK = "task_transport_out_unload_callback";

    /**
     * 推送MQ - 闲置物资出库下架回调
     */
    public static final String TASK_LEISURE_OUT_UNLOAD_CALLBACK = "task_leisure_out_unload_callback";


    /**
     * 推送MQ - 转储上架回调
     */
    public static final String TASK_TRANSPORT_LOAD_CALLBACK = "task_transport_load_callback";

    /**
     * 推送MQ - 成套转储上架回调
     */
    public static final String TASK_UNITIZED_TRANSPORT_LOAD_CALLBACK = "task_unitized_transport_load_callback";

    /**
     * 推送MQ - 调拨上架回调
     */
    public static final String TASK_TRANSPORT_IN_LOAD_CALLBACK = "task_transport_in_load_callback";

    /**
     * 推送MQ - 转性上架回调
     */
    public static final String TASK_TRANSFER_LOAD_CALLBACK = "task_transfer_load_callback";
    /**
     * 推送MQ - 成套设备转性上架回调
     */
    public static final String TASK_UNITIZED_TRANSFER_LOAD_CALLBACK = "task_unitized_transfer_load_callback";
    /**
     * 推送MQ - 推送UMS
     */
    public static final String GEN_UMS_MESSAGE = "gen_ums_message";
    /**
     * 推送MQ - 推送UMS
     */
    public static final String UPDATE_UMS_MESSAGE = "update_ums_message";

    /**
     * 推送MQ - 推送UMS
     */
    public static final String UPDATE_UMS_MESSAGE_OTHER = "update_ums_message_other";
    /**
     * 推送MQ - 审批转办，推送更新UMS数据
     */
    public static final String UPDATE_UMS_MESSAGE_BY_TRANSFER_TASK = "update_ums_message_by_transfer_task";

    /**
     * 审批回调-工器具遗失登记
     */
    public static final String APPROVAL_CALLBACK_LOSE_REGISTER = "approval_callback_lose_register";

    /**
     * 审批回调-工器具损坏登记
     */
    public static final String APPROVAL_CALLBACK_DAMAGE_REGISTER = "approval_callback_damage_register";

    /**
     * 审批回调-工器具维保记录维护
     */
    public static final String APPROVAL_CALLBACK_TOOL_MAINTAIN = "approval_callback_tool_maintain";

    /**
     * 审批回调-工器具维修接收
     */
    public static final String APPROVAL_CALLBACK_TOOL_REPAIR_RECEIVE = "approval_callback_tool_repair_receive";

    /**
     * 审批回调-工器具报废申请
     */
    public static final String APPROVAL_CALLBACK_SCRAP_APPLY = "approval_callback_scrap_apply";

    /**
     * 审批回调-开箱计划
     */
    public static final String APPROVAL_CALLBACK_UNITIZED_BOX_PLAN = "approval_callback_unitized_box_plan";

    /**
     * 审批回调-质检会签
     */
    public static final String APPROVAL_CALLBACK_SIGN_INSPECT = "approval_callback_sign_inspect";

    /**
     * 审批回调-不符合项处置
     */
    public static final String APPROVAL_CALLBACK_INCONFORMITY_MAINTAIN = "approval_callback_inconformity_maintain";

    /**
     * 审批回调-成套不符合项处置
     */
    public static final String APPROVAL_CALLBACK_UNITIZED_INCONFORMITY_MAINTAIN = "approval_callback_unitized_inconformity_maintain";

    /**
     * 审批回调-寿期创建
     */
    public static final String APPROVAL_CALLBACK_LIFETIME_APPRAISAL = "APPROVAL_CALLBACK_LIFETIME_APPRAISAL";

    /**
     * 审批回调-寿期维护
     */
    public static final String APPROVAL_CALLBACK_LIFETIME_MAINTAIN = "approval_callback_lifetime_maintain";
    public static final String APPROVAL_CALLBACK_UNITIZED_LIFETIME_MAINTAIN = "approval_callback_unitized_lifetime_maintain";

    /**
     * 审批回调-需求管理
     */
    public static final String APPROVAL_REQUIRE = "approval_callback_require";
    /**
     * 审批回调-领料申请
     */
    public static final String APPROVAL_MATERIAL_OUT_APPLY = "approval_callback_material_out_apply";

    /**
     * 审批回调-退转库领料申请
     */
    public static final String APPROVAL_TRANSFERR_ETURN_MATERIAL_OUT_APPLY = "approval_callback_transfer_return_material_out_apply";

    /**
     * 审批回调-成套设备退转库领料申请
     */
    public static final String APPROVAL_UNITIZED_TRANSFER_RETURN_MATERIAL_OUT_APPLY = "approval_callback_unitized_transfer_return_material_out_apply";

    /**
     * 审批回调-成套领料申请
     */
    public static final String APPROVAL_CALLBACK_UNITIZED_MATERIAL_OUT_APPLY = "approval_callback_unitized_material_out_apply";

    /**
     * 审批回调-退库申请
     */
    public static final String APPROVAL_CALLBACK_MAT_REQ_RETURN_APPLY = "approval_callback_mat_req_return_apply";

    /**
     * 审批回调-退转库申请
     */
    public static final String APPROVAL_CALLBACK_TRANSFER_RETURN_APPLY = "approval_callback_transfer_return_apply";

    /**
     * 审批回调-压水堆退转库申请
     */
    public static final String APPROVAL_CALLBACK_UNITIZED_TRANSFER_RETURN_APPLY = "approval_callback_unitized_transfer_return_apply";

    /**
     * 审批回调-退旧物资申请
     */
    public static final String APPROVAL_CALLBACK_RETURN_OLD_APPLY = "approval_callback_return_old_apply";

    /**
     * 审批回调-采购退货申请
     */
    public static final String APPROVAL_CALLBACK_PURCHASE_RETURN_APPLY = "approval_callback_purchase_return_apply";

    /**
     * 审批回调-转性申请
     */
    public static final String APPROVAL_TRANSFER_NATURE_APPLY = "approval_transfer_nature_apply";
    public static final String APPROVAL_UNITIZED_TRANSFER_NATURE_APPLY = "approval_unitized_transfer_nature_apply";

    /**
     * 审批回调-转码申请
     */
    public static final String APPROVAL_TRANSPORT_MAT_APPLY = "approval_transport_mat_apply";

    /**
     * 审批回调-盘点报告
     */
    public static final String APPROVAL_STOCKTAKING_REPORT_APPLY = "approval_stocktaking_report_apply";
    public static final String APPROVAL_UNITIZED_STOCKTAKING_REPORT_APPLY = "approval_unitized_stocktaking_report_apply";
    public static final String APPROVAL_UNITIZED_STOCKTAKING_PLAN_REPORT_APPLY = "approval_unitized_stocktaking_plan_report_apply";

    /**
     * 审批回调-到货通知
     */
    public static final String APPROVAL_DELIVERY_NOTICE_APPLY = "approval_delivery_notice_apply";

    /**
     * 审批回调-紧急领用出库申请
     */
    public static final String APPROVAL_EMERGENCY_MAT_ORDER_OUT_APPLY = "approval_emergency_mat_order_out_apply";

    /**
     * 审批回调-专用工器具借用申请
     */
    public static final String APPROVAL_TOOL_BORROW_APPLY = "approval_tool_borrow_apply";

    /**
     * 审批回调-门到门送货申请
     */
    public static final String APPROVAL_D2D_DELIVERY_APPLY = "approval_d2d_delivery_apply";

    /**
     * 审批回调-物料编码申请
     */
    public static final String APPROVAL_MAT_APPLY = "approval_mat_apply";

    /**
     * 审批回调-差异通知
     */
    public static final String APPROVAL_DIFF_NOTICE_APPLY = "approval_diff_notice_apply";

    /**
     * 审批回调-闲置物资申请
     */
    public static final String APPROVAL_CALLBACK_IDLE_MATERIAL_APPLY = "approval_callback_idle_material_apply";
    public static final String APPROVAL_CALLBACK_UNITIZED_IDLE_MATERIAL_APPLY = "approval_callback_unitized_idle_material_apply";

    /**
     * 审批回调-闲置物资入库申请
     */
    public static final String APPROVAL_CALLBACK_IDLE_MATERIAL_INPUT_APPLY = "approval_callback_idle_material_input_apply";

    /**
     * 审批回调-维保创建
     */
    public static final String APPROVAL_CALLBACK_MAINTAIN_APPLY = "approval_callback_maintain_apply";
    public static final String APPROVAL_CALLBACK_MAINTAIN_RESULT = "approval_callback_maintain_result";
    public static final String APPROVAL_CALLBACK_UNITIZED_MAINTAIN_APPLY = "approval_callback_unitized_maintain_apply";
    public static final String APPROVAL_CALLBACK_UNITIZED_MAINTAIN_RESULT = "approval_callback_unitized_maintain_result";
    /**
     * 审批回调-暂存物资申请
     */
    public static final String APPROVAL_TS_INPUT_APPLY = "approval_ts_input_apply";
    public static final String APPROVAL_UNITIZED_TS_INPUT_APPLY = "approval_unitized_ts_input_apply";
    /**
     * 审批回调-暂存物资延期
     */
    public static final String APPROVAL_UNITIZED_TS_DELAY = "approval_unitized_ts_delay";

    /**
     * 审批回调-暂存物资领用申请
     */
    public static final String APPROVAL_TS_OUT_APPLY = "approval_ts_out_apply";
    public static final String APPROVAL_UNITIZED_TS_OUT_APPLY = "approval_unitized_ts_out_apply";

    /**
     * 审批回调-盘点完成，差异提交审批
     */
    public static final String APPROVAL_STOCKTAKING_DIFF_SUBMIT = "approval_stocktaking_diff_submit";
    /**
     * 推送MQ-暂存领用出库
     */
    public static final String TEMP_STORE_OUTPUT = "temp_store_output";
    public static final String UNITIZED_TEMP_STORE_OUTPUT = "unitized_temp_store_output";

    /**
     * 推送MQ - 更新出库单已退库数量
     */
    public static final String UPDATE_OUTPUT_RETURN_QTY = "update_output_return_qty";

    /**
     * 推送MQ - 查询采购退货前序采购验收单行项目
     */
    public static final String GET_PURCHASE_RETURN_PRE_INSPECTION_ITEM = "get_purchase_return_pre_inspection_item";

    /**
     * 推送MQ - 查询用户列表
     */
    public static final String GET_SYS_USER_LIST = "get_sys_user_list";

    /**
     * 推送MQ - wcs生成任务指令集
     */
    public static final String WCS_GENERATE_INSTRUCTION = "wcs_generate_instruction";

    /**
     * 推送MQ - 修改仓位是否为空状态
     */
    public static final String UPDATE_BIN_EMPTY_STATUS = "update_bin_empty_status";

    /**
     * 推送MQ - 根据前续单据生成临时入库单
     */
    public static final String GEN_TEMP_INPUT_STOCK = "gen_temp_input_stock";

    /**
     * 推送MQ - 临时入库上架作业回调
     */
    public static final String TASK_TEMP_INPUT_CALLBACK = "task_temp_input_callback";
    /**
     * 推送MQ -废旧物资入库上架作业回调
     */
    public static final String TASK_WASTER_MATERIALS_INPUT_CALLBACK = "task_waster_materials_input_callback";

    /**
     * 推送MQ -物资返运通知回调
     */
    public static final String UNITIZED_MATERIAL_RETUR_NOTICE_CALLBACK = "unitzed_material_return_notice_callback";

    /**
     * 推送MQ - 冲销单据修改请求
     */
    public static final String RECEIPT_WRITE_OFF_MODIFY_REQ_ITEM = "receipt_write_off_modify_req_item";

    /**
     * 推送MQ - 判断出库单是否关联退库单
     */
    public static final String IS_REL_RETURN_RECEIPT = "is_rel_return_receipt";

    /**
     * 推送MQ - 过门回调领料出库单
     */
    public static final String PASS_DOOR_MAT_REQ_OUTPUT_CALLBACK = "pass_door_mat_req_output_callback";

    /**
     * 推送MQ - 过门回调其他出库单
     */
    public static final String PASS_DOOR_OTHER_OUTPUT_CALLBACK = "pass_door_other_output_callback";

    /**
     * 推送MQ - 过门回调其他出库单
     */
    public static final String PASS_DOOR_LEISURE_OUTPUT_CALLBACK = "pass_door_leisure_output_callback";


    /**
     * 推送MQ - 过门回调采购退货单
     */
    public static final String PASS_DOOR_PURCHASE_RETURN_CALLBACK = "pass_door_purchase_return_callback";

    /**
     * 推送MQ - 过门回调销售出库单
     */
    public static final String PASS_DOOR_SALE_OUTPUT_CALLBACK = "pass_door_sale_output_callback";

    /**
     * 推送MQ - 过门回调报废出库单
     */
    public static final String PASS_DOOR_SCRAP_OUTPUT_CALLBACK = "pass_door_scrap_output_callback";

    /**
     * 推送MQ - 过门回调报废出库单
     */
    public static final String PASS_DOOR_WORTHLESS_OUTPUT_CALLBACK = "pass_door_worthless_output_callback";

    /**
     * 推送MQ - 电子秤移动记录上报
     */
    public static final String ELECTRONIC_SCALE_RECORD_REPORT = "electronic_scale_record_report";

    /**
     * 推送MQ - 生成采集任务
     */
    public static final String GEN_COLLECTION_TASK = "gen_collection_task";

    /**
     * 推送MQ - 维保出库提交回调，生成维保记录单
     */
    public static final String TASK_TOOL_MAINTAIN_OUTPUT_CALLBACK = "task_tool_maintain_output_callback";

    /**
     * 推送MQ - 工器具报废申请提交回调，生成报废出库单
     */
    public static final String TASK_TOOL_SCRAP_OUTPUT_CALLBACK = "task_tool_scrap_output_callback";

    /**
     * 审批回调- 成套设备退库申请
     */
    public static final String APPROVAL_CALLBACK_UNITIZED_MAT_REQ_RETURN_APPLY = "approval_callback_unitized_mat_req_return_apply";

    /**
     * 审批回调- 成套设备UP码拆分
     */
    public static final String APPROVAL_CALLBACK_UNITIZED_UP_SPLIT = "approval_callback_unitized_up_split";

    /**
     * 审批回调- 物料主数据视图审批
     */
    public static final String APPROVAL_CALLBACK_MATERIAL_VIEW_AUDIT = "approval_callback_material_view_audit";


    public static final String GEN_INCONFORMITY_NOTICE = "GEN_INCONFORMITY_NOTICE";

    /**
     * 审批回调-需求计划
     */
    public static final String APPROVAL_DEMAND_PLAN = "approval_callback_demand_plan";

    /**
     * 审批回调-住房申请
     */
    public static final String APPROVAL_ROOM_CHECK_IN_REQ = "approval_callback_room_check_in_req";

    /**
     * 审批回调-供应商
     */
    public static final String APPROVAL_CALLBACK_SUPPLIER = "approval_callback_supplier";


    /**
     * 审批回调-离岸送货
     */
    public static final String APPROVAL_OFFSHORE_DELIVERY = "approval_callback_offshore_delivery";


    /**
     * 推送MQ - 生成付款计划
     */
    public static final String TASK_GEN_PAYMENT_PLAN = "task_gen_payment_plan";

    /**
     * 审批回调-油品po采购
     */
    public static final String APPROVAL_OIL_PO_PURCHASE = "approval_oil_po_purchase";

    /**
     * 审批回调-付款结算
     */
    public static final String APPROVAL_PAYMENT_SETTLEMENT = "approval_payment_settlement";

    /**
     * 审批回调-资金计划
     */
    public static final String APPROVAL_CAPITAL_PLAN = "approval_capital_plan";

    /**
     * 审批回调-采购申请
     */
    public static final String APPROVAL_PURCHASE_APPLY = "approval_purchase_apply";

    /**
     * 审批回调-直接采购
     */
    public static final String APPROVAL_DIRECT_PURCHASE = "approval_direct_purchase";

    /**
     * 审批回调-报废申请
     */
    public static final String APPROVAL_SCRAP_APPLY = "approval_scrap_apply";

    /**
     * 审批回调-采购结算
     */
    public static final String APPROVAL_PAYMENT_REGISTER = "approval_payment_register";

    /**
     * 审批回调-服务工程确认
     */
    public static final String APPROVAL_CONTRACT_CONFIRM = "approval_contract_confirm";
}
