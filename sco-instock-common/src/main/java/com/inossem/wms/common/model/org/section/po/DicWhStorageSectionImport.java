package com.inossem.wms.common.model.org.section.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 存储区传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "存储区传输对象", description = "存储区传输对象")
public class DicWhStorageSectionImport implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/
    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码" , example = "S200")
    @ExcelProperty(value = "仓库编码", index =0)
    private String whCode;

    /**
     * 仓库描述
     */
    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    @ExcelIgnore
    private String whName;

    /**
     * 存储类型编码
     */
    @ApiModelProperty(value = "存储类型编码" , example = "801")
    @ExcelProperty(value = "存储类型编码", index =1)
    private String typeCode;

    /**
     * 存储类型描述
     */
    @ApiModelProperty(value = "存储类型描述" , example = "入库临时区")
    @ExcelIgnore
    private String typeName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @ExcelIgnore
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    @ExcelIgnore
    private Long whId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    @ExcelIgnore
    private Long typeId;

    @ApiModelProperty(value = "存储区code" , example = "s008")
    @ExcelProperty(value = "存储区", index =2)
    private String sectionCode;

    @ApiModelProperty(value = "存储区描述" , example = "存储区008")
    @ExcelProperty(value = "存储区描述", index =3)
    private String sectionName;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    @ExcelIgnore
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    @ExcelIgnore
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    @ExcelIgnore
    private Long modifyUserId;

}
