package com.inossem.wms.common.model.bizdomain.input.vo;

import java.util.Date;
import java.util.List;

import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 入库单前续单据【生产订单 or 采购订单】
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-24
 */
@ApiModel(value = "入库单前续单据【生产订单 or 采购订单】", description = "入库单前续单据【生产订单 or 采购订单】")
@Data
public class BizReceiptInputPreHeadVo {

    @ApiModelProperty(value = "參考单据号" , example = "SH01000005")
    private String referReceiptCode;

    @ApiModelProperty(value = "SAP订单创建时间" , example = "2021-05-11")
    private Date erpCreateTime;

    @ApiModelProperty(value = "SAP订单创建人name" , example = "管理员")
    private String erpCreateUserName;

    @ApiModelProperty(value = "行项目-入库单")
    private List<BizReceiptInputItemDTO> children;

}
