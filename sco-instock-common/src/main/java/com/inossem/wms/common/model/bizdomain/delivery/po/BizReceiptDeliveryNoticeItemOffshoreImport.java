package com.inossem.wms.common.model.bizdomain.delivery.po;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 送货通知-导入物项清单
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptDeliveryNoticeItemOffshoreImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "报关分类")
    private String customsClass;

    @ExcelProperty(value = "倍率")
    private BigDecimal taxRate;
}
