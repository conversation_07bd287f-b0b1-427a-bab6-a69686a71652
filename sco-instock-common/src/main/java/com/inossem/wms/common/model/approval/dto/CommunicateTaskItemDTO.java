package com.inossem.wms.common.model.approval.dto;

import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.file.img.entity.BizCommonImage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 沟通任务行项目DTO
 * 沟通任务中的每个被沟通人都有一条行项目记录
 *
 * <AUTHOR>
 * @date 2025/05/15
 **/
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@ApiModel(value = "CommunicateTaskItemDTO", description = "沟通任务行项目DTO")
public class CommunicateTaskItemDTO {

    @ApiModelProperty(value = "被沟通人的用户编码")
    private String userCode;

    @ApiModelProperty(value = "被沟通人的用户姓名")
    private String userName;

    @ApiModelProperty(value = "回复沟通时提交的办理意见")
    private String commentMsg;

    @ApiModelProperty(value = "回复状态，1：已回复，0：未回复")
    private String approveStatus = EnumRealYn.FALSE.getStrValue();

    @ApiModelProperty(value = "是否已经取消，1：已取消，0：未取消")
    private String isCancel = EnumRealYn.FALSE.getStrValue();

    @ApiModelProperty(value = "回复沟通的时间")
    private Date submitTime;

    @ApiModelProperty(value = "回复沟通过程中上传的附件id列表")
    private List<Long> fileIdList;

    @ApiModelProperty(value = "回复沟通过程中上传的图片id列表")
    private List<Long> imgIdList;

    @ApiModelProperty(value = "附件文件列表")
    private List<BizCommonFile> attachmentFileList;

    @ApiModelProperty(value = "附件图片列表")
    private List<BizCommonImage> attachmentImgList;
}
