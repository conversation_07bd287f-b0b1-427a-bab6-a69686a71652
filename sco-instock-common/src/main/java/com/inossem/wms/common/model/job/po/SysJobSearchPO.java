package com.inossem.wms.common.model.job.po;

import java.io.Serializable;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/3/3 17:15
 *
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SysJob对象", description = "调度查询对象")
public class SysJobSearchPO extends PageCommon implements Serializable {
    private static final long serialVersionUID = -4907169113422358062L;
    @ApiModelProperty(value = "任务名称" , example = "test")
    private String jobName;

    @ApiModelProperty(value = "任务组名" , example = "1")
    private String jobGroup;

    @ApiModelProperty(value = "状态（0正常 1暂停）" , example = "0")
    private String status;

    @ApiModelProperty(value = "调用目标字符串" , example = "com.inossem.wms.system.job.controller.JobController.test")
    private String invokeTarget;
}
