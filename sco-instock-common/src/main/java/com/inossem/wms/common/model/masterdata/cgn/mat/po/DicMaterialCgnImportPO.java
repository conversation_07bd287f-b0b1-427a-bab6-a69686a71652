package com.inossem.wms.common.model.masterdata.cgn.mat.po;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;

/**
 * 项目物料码管理
 *
 * <AUTHOR>
 * @since 2024-07-17
 */

@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "项目物料码管理导入")
public class DicMaterialCgnImportPO {

    @ExcelProperty(value = "物料码(全)(必填)", index = 0)
    @ApiModelProperty(value = "项目物料码/物资编码")
    private String cgnMatCode;

    @ExcelProperty(value = "简称(必填)", index = 1)
    @ApiModelProperty(value = "简称")
    private String cgnMatName;

    @ExcelProperty(value = "裕量分类(甲供必填/乙供不填)", index = 2)
    @ApiModelProperty(value = "裕量计算分类码")
    private String marginCategoryCode;



    public String getCgnMatCode() {
        return cgnMatCode;
    }

    public void setCgnMatCode(String cgnMatCode) {
        this.cgnMatCode = cgnMatCode;
    }

    public String getMarginCategoryCode() {
        return marginCategoryCode;
    }

    public void setMarginCategoryCode(String marginCategoryCode) {
        this.marginCategoryCode = marginCategoryCode;
    }

    public String getCgnMatName() {
        return cgnMatName;
    }

    public void setCgnMatName(String cgnMatName) {
        this.cgnMatName = cgnMatName;
    }
}
