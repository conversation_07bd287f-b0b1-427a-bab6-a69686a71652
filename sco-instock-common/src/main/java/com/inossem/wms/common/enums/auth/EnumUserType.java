package com.inossem.wms.common.enums.auth;

import com.inossem.wms.common.model.common.enums.auth.UserMapVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户类型
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-19
 */
@AllArgsConstructor
public enum EnumUserType {

    // 内部用户
    INSIDE_SYSUSER(10),
    // 外部供应商用户
    OUTSIDE_SUPPLIER_USER(20),
    // 外部客户用户
    OUTSIDE_CUSTOMER_USER(30),
    // 外部业务用户
    OUTSIDE_BUSINESS_USER(40),
    // 接口用户
    INTERFACE_USER(50),;

    public static List<UserMapVo> list;
    @Getter
    private final Integer value;

    public static List<UserMapVo> toList() {
        if (list == null) {
            List<UserMapVo> listInner = new ArrayList<>();
            EnumUserType[] ary = EnumUserType.values();
            for (EnumUserType e : ary) {
                UserMapVo tempMap = new UserMapVo();
                tempMap.setUserType(e.getValue());
                listInner.add(tempMap);
            }
            list = listInner;
        }
        return list;
    }

}
