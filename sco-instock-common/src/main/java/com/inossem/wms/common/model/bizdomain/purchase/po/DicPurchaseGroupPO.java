package com.inossem.wms.common.model.bizdomain.purchase.po;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.inossem.wms.common.model.common.base.PageCommon;

/**
 * 采购组PO
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DicPurchaseGroupPO对象", description="DicPurchaseGroupPO对象")
public class DicPurchaseGroupPO extends PageCommon {

    @ApiModelProperty(value = "采购组编码")
    private String purchaseGroupCode;

}
