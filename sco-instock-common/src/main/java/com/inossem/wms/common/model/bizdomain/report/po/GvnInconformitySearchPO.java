package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * GVN统计报表 PO
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "GvnInconformitySearchPO", description = "GvnInconformitySearchPO")
public class GvnInconformitySearchPO extends PageCommon {

    @ApiModelProperty(value = "当前日期时间")
    private Date now;

    @ApiModelProperty(value = "差异处置单号")
    private String receiptCode;
    private List<String> receiptCodeList;

    @ApiModelProperty(value = "物料编码")
    private String matCode;
    private List<String> matCodeList;

    @ApiModelProperty(value = "数量差异处置创建时间")
    private Date createTimeStart;

    @ApiModelProperty(value = "数量差异处置创建时间")
    private Date createTimeEnd;

    @ApiModelProperty(value = "数量差异处置完成时间")
    private Date modifyTimeStart;

    @ApiModelProperty(value = "数量差异处置完成时间")
    private Date modifyTimeEnd;

    @ApiModelProperty(value = "处置周期")
    private Integer maintainDays;

    @ApiModelProperty(value = "处置状态")
    private String maintainStatus;

}
