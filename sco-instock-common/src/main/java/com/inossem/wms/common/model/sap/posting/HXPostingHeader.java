package com.inossem.wms.common.model.sap.posting;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SAP物料凭证过账抬头参数
 */
@Data
public class HXPostingHeader {
    

    @ApiModelProperty(value = "过账日期 BUDAT")
    private String postingDate = "";

    @ApiModelProperty(value = "凭证日期 BLDAT")
    private String docDate = "";

    @ApiModelProperty(value = "单据id")
    private Long receiptId;

    @ApiModelProperty(value = "参考凭证号 BKTXT")   
    private String receiptCode = "";

    @ApiModelProperty(value = "参考凭证类型")
    private Integer receiptType;

    @ApiModelProperty(value = "领料类型")
    private Integer receiveType;

    @ApiModelProperty(value = "行项目")
    private List<HXPostingItem> items;

    @ApiModelProperty(value = "识别码")
    private String gmCode;
} 
