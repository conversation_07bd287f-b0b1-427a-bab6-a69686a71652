package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * StocktakingItemKey设计用于
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-06-03
 */
@EqualsAndHashCode
@Data
@Accessors(chain = true)
public class StocktakingItemKey {

    private Long whId;
    private Long typeId;
    private Long binId;
    private Long batchId;

}
