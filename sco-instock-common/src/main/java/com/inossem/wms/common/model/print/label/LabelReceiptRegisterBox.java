package com.inossem.wms.common.model.print.label;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> wang
 * <p>
 * 领料出库单打印实体对象
 * </p>
 * @date 2022/5/31 10:27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "到货登记-打印待检标识-PDA打印", description = "到货登记-打印待检标识-PDA")
public class LabelReceiptRegisterBox implements Serializable {

    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;


    @ApiModelProperty(value = "采购订单")
    private String receiptCode;

    @ApiModelProperty(value = "采购订单描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "采购员")
    private String purchaseUserName;

    @ApiModelProperty(value = "接货人")
    private String receiver;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "箱件数量")
    private Long caseCount;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "需求部门")
    private String needDeptCode;

    @ApiModelProperty(value = "需求人")
    private String applyUserName;

    @ApiModelProperty(value = "供应商  ")
    private String supplierName;

    @ApiModelProperty(value = "备注  ")
    private String itemRemark;

    @ApiModelProperty(value = "是否是RFID标签  0普通标签 1RFID标签" , example = "1")
    private Integer labelIsRFID;


}
