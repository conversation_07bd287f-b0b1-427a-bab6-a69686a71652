package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.enums.report.EnumStockAgeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 库存积压报表 查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存积压报表 查询入参传输对象", description = "库存积压报表 查询入参")
public class StockAgeAnalysePO {

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;


    @ApiModelProperty(value = "积压类型分类")
    private List<EnumStockAgeType> ageTypeList;


}
