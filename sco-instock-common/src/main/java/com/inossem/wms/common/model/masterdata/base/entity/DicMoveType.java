package com.inossem.wms.common.model.masterdata.base.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 移动类型表，转储专用
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicMoveType对象", description = "移动类型表，转储专用")
@TableName("dic_move_type")
public class DicMoveType implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "移动类型" , example = "3010")
    private String moveTypeCode;

    @ApiModelProperty(value = "特殊库存标识" , example = "Q")
    private String specStock;

    @ApiModelProperty(value = "移动类型描述" , example = "Q转Q(工厂)")
    private String moveTypeName;

    @ApiModelProperty(value = "单据类型 214 其他入库 416 其他出库  511转储" , example = "214")
    private Integer receiptType;

    @ApiModelProperty(value = "反向标识 0正向 1反向" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "反向移动类型" , example = "301")
    private String moveTypeCodeWriteOff;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
