package com.inossem.wms.common.model.bizdomain.input.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/1 16:30
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "工器具主数据出参", description = "工器具主数据出参")
public class ToolsMaterialInfoVO implements Serializable {


    @ExcelIgnore
    @ApiModelProperty(value = "是否冻结" , example = "0")
    private Long isFreeze;

    @ExcelIgnore
    @ApiModelProperty(value = "批次信息id" , example = "152286145871874")
    private Long id;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    @ExcelProperty(value = "物料描述")
    private String matName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码" , example = "")
    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "送检部门")
    @ExcelProperty(value = "送检部门")
    private String toolInspectUnit;

    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号")
    private String formatCode;

    @ExcelIgnore
    @ApiModelProperty(value = "工具类型" , example = "1221")
    private Long toolTypeId;

    @ApiModelProperty(value = "工具类型" , example = "1221")
    @ExcelProperty(value = "工具类型")
    private String toolTypeName;

    @ExcelIgnore
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "计量单位id" , example = "7")
    private Long unitId;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码" , example = "7")
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 单位Name
     */
    @ApiModelProperty(value = "单位Name" , example = "7")
    @ExcelProperty(value = "计量单位描述")
    private String unitName;

    /**
     * 出厂编码
     */
    @ApiModelProperty(value = "出厂编码" , example = "7")
    @ExcelProperty(value = "出厂编码")
    private String outFtyCode;

    /**
     * 维保日期
     */
    @ApiModelProperty(value = "维保日期")
    @ExcelProperty(value = "维保日期")
    private Date maintenanceDate;

    /**
     * 维保周期
     */
    @ApiModelProperty(value = "维保周期")
    @ExcelProperty(value = "维保周期")
    private Integer maintenanceCycle;

    /**
     * 维保有效期
     * @deprecated 使用maintenanceValidDate替换
     */
    @ExcelIgnore
    @Deprecated
    @ApiModelProperty(value = "维保有效期")
    private Date maintenancePeriod;

    /**
     * 维保有效期
     */
    @ApiModelProperty(value = "维保有效期 -- 工器具-新")
    @ExcelProperty(value = "维保有效期")
    private Date maintenanceValidDate;

    /**
     * 维护预警期（天）
     */
    @ApiModelProperty(value = "维护预警期")
    @ExcelProperty(value = "维保预警期")
    private Integer maintenanceWarningPeriod;

    /**
     * 所属部门 ID
     */
    @ExcelIgnore
    @ApiModelProperty(value = "所属部门id")
    private Long deptId;

    /**
     * 存储类型ID
     */
    @ExcelIgnore
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    /**
     * 存储类型描述
     */
    @ApiModelProperty(value = "存储类型描述" , example = "xxx")
    @ExcelProperty(value = "存储类型描述")
    private String typeName;

    @ExcelIgnore
    @ApiModelProperty(value = "仓位id")
    private Long binId;

    /**
     * 仓位
     */
    @ExcelIgnore
    @ApiModelProperty(value = "仓位")
    private Long binName;

    /**
     * 数量
     */
    @ExcelIgnore
    @ApiModelProperty(value = "数量")
    private Integer num;

    /**
     * 工器具的工具编码
     */
    @ApiModelProperty(value = "工器具的工具编码")
    @ExcelProperty(value = "工具系统编码")
    private String batchCode;

    /**
     * 保养大纲
     */
    @ExcelIgnore
    @ApiModelProperty(value = "保养大纲")
    private String maintenanceProgram;

    @ApiModelProperty(value = "工具状态描述")
    @ExcelProperty(value = "管理状态")
    private String toolStatusI18n;

    /**
     * 工具状态
     */
    @ExcelIgnore
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;

    /**
     * 采购订单编码
     */
    @ExcelIgnore
    @ApiModelProperty(value = "采购订单编码" , example = "4500000001")
    private String purchaseReceiptCode;
    /**
     * 采购订单编码
     */
    @ExcelIgnore
    @ApiModelProperty(value = "采购订单行项目号" , example = "4500000001")
    private String purchaseReceiptRid;

    /**
     * 采购订单行号
     */
    @ExcelIgnore
    @ApiModelProperty(value = "采购订单行号" , example = "0010")
    private String purchaseReceiptItemId;
    /**
     * 来源入库订单号
     */
    @ExcelIgnore
    @ApiModelProperty(value = "来源入库订单号")
    private Long inputHeadId;

    /**
     * 来源单号
     */
    @ApiModelProperty(value = "来源单号")
    @ExcelProperty(value = "来源单号")
    private String sourceBillCode;

    /**
     * 生产厂家
     */
    @ExcelIgnore
    @ApiModelProperty(value = "生产厂家" , example = "Inossem")
    private String supplierName;


    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    @ExcelProperty(value = "生产日期")
    private Date productionDate;

    /**
     * 在库天数
     */
    @ApiModelProperty(value = "在库天数")
    @ExcelProperty(value = "在库天数")
    private Long inDays;

    @ExcelIgnore
    @ApiModelProperty(value = "特殊库存类型 E 现有订单 K 寄售（供应商） O 供应商分包库存 Q 项目库存 ")
    private String specStock;

    @ExcelIgnore
    @ApiModelProperty(value = "特殊库存代码")
    private String specStockCode;

    @ExcelIgnore
    @ApiModelProperty(value = "特殊库存描述")
    private String specStockName;

    @ExcelIgnore
    @ApiModelProperty(value = "备注")
    private String description;

    @ExcelIgnore
    @ExcelProperty(value = "仓位编码")
    private String binCode;

    @ApiModelProperty(value = "计量分级" , example = "")
    @ExcelProperty(value = "计量分级")
    private String metrologyClassification;

    @ApiModelProperty(value = "准确度等级" , example = "")
    @ExcelProperty(value = "准确度等级")
    private String levelOfAccuracy;

    @ApiModelProperty(value = "量程" , example = "")
    @ExcelProperty(value = "量程")
    private String toolsRange;

    @ExcelIgnore
    @ApiModelProperty(value = "所属部门描述")
    private String deptName;

    @ExcelIgnore
    @ApiModelProperty(value = "所属科室描述")
    private String deptOfficeName;

    @ExcelIgnore
    @ApiModelProperty(value = "所在人描述")
    private String locateUserName;

    @ApiModelProperty(value = "借用部门描述")
    @ExcelProperty(value = "借用部门")
    private String borrowDeptName;

    @ApiModelProperty(value = "借用科室描述")
    @ExcelProperty(value = "借用科室")
    private String borrowDeptOfficeName;

    @ApiModelProperty(value = "借用人描述")
    @ExcelProperty(value = "借用人")
    private String borrowUserName;

    @ExcelIgnore
    @ApiModelProperty(value = "借用类型")
    private Integer borrowType;

    @ApiModelProperty(value = "扩展属性 - 借用类型")
    @ExcelProperty(value = "借用类型")
    private String borrowTypeI18n;

    @ApiModelProperty(value = "工器具 - 管理状态备注")
    @ExcelProperty(value = "管理状态备注")
    private String toolManageStatusRemark;

    @ApiModelProperty(value = "工器具 - 使用人")
    @ExcelProperty(value = "使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用地点")
    @ExcelProperty(value = "使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    @ExcelProperty(value = "使用事由")
    private String toolUseReason;

    @ApiModelProperty(value = "工器具 - 技术参数")
    @ExcelProperty(value = "技术参数")
    private String technicalSpecification;

    @ApiModelProperty(value = "工器具 - 来源单号")
    @ExcelProperty(value = "来源单号")
    private String toolSourceReceiptCode;

    @ExcelIgnore
    @ApiModelProperty(value = "工器具 - 来源单抬头id")
    private Long toolSourceReceiptHeadId;

    @ExcelIgnore
    @ApiModelProperty(value = "工器具 - 来源单据类型")
    private Integer toolSourceReceiptType;

    @ApiModelProperty(value = "入库时间")
    @ExcelProperty(value = "入库时间")
    private Date inputDate;

}
