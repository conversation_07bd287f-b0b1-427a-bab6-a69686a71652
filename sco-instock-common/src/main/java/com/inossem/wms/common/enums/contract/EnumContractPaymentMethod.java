package com.inossem.wms.common.enums.contract;

import com.inossem.wms.common.model.common.enums.EnumContractPaymentMethodMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同支付方式枚举
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Getter
@AllArgsConstructor
public enum EnumContractPaymentMethod {

    PAYMENT_0001(1,"C001","施工: 进度款90% 结算款5%质保金5%"),
    PAYMENT_0002(2,"C002","服务:进度款100%"),
    PAYMENT_0003(3,"C003","服务:进度款95% 进度完结5%"),
    PAYMENT_0004(4,"C004","离岸托收:发货款100%"),
    PAYMENT_0005(5,"C005","代进口采购:预付款X% 到货款100% 付款退款X%"),
    PAYMENT_0006(6, "C006","物资采购:到货款95% 质保金5%(验收后180天)"),
    PAYMENT_0007(7, "C007","施工: 进度款95% 质保金5%"),
    PAYMENT_0009(9, "C099","其他");

    private final Integer code;
    private final String desc;
    private final String remark;

    private static List<EnumContractPaymentMethodMapVO> list;

    public static List<EnumContractPaymentMethodMapVO> toList() {
        if (list == null) {
            List<EnumContractPaymentMethodMapVO> listInner = new ArrayList<>();
            EnumContractPaymentMethod[] ary = EnumContractPaymentMethod.values();
            for (EnumContractPaymentMethod e : ary) {
                EnumContractPaymentMethodMapVO vo = new EnumContractPaymentMethodMapVO();
                vo.setPaymentMethod(e.getCode());
                listInner.add(vo);
            }
            list = listInner;
        }
        return list;
    }

    public static EnumContractPaymentMethod getByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (EnumContractPaymentMethod e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
} 
