package com.inossem.wms.common.model.print.label;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wang
 * <p>
 *     验收入库单-物料标签打印-PDA
 * </p>
 * @date 2022/6/7 13:08
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = " 验收入库单-物料标签打印-PDA", description = "验收入库单-物料标签打印-PDA")
public class LabelReceiptInputBox implements Serializable {

    @ApiModelProperty(value = "物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "采购订单编码" , example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;

    // 物料类别

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "寿期到期日期" , example = "2021-05-10")
    private Date lifetimeDate;

    @ApiModelProperty(value = "批次code")
    private String batchCode;

    @ApiModelProperty(value = "创建时间-入库日期" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "工厂名称" , example = "英诺森沈阳工厂")
    private String ftyCode;

    @ApiModelProperty(value = "维保日期（保质期=维保日期-生产日期）")
    private Date maintenanceDate;

    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "0")
    private Integer isSingle;

    @ApiModelProperty(value = "凭证时间-入库日期" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "包装形式")
    private String packageTypeI18n;

    @ApiModelProperty(value = "标签类型 1：RFID抗金属  2：RFID非抗金属 0：普通标签" , example = "1")
    private Integer tagType;

    @ApiModelProperty(value = "是否是RFID标签  0普通标签 1RFID标签" , example = "1")
    private Integer labelIsRFID;

    @ApiModelProperty(value = "rfid" , example = "100012")
    private String rfidCode;


    private Long matId;

    private Long ftyId;

    private Long batchId;

    private Long labelId;

    /**
     * 总货架寿命
     */
    private Integer shelfLifeMax;

    private Integer printNum;
    private String matDocCode;
    private String matDocRid;
    private Long itemId;

    @ApiModelProperty(value = "入库类型")
    private String inputType;

    @ApiModelProperty(value = "填充属性 - 接收库存地点" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "采购包")
    private String extend2;

    @ApiModelProperty(value = "物资ID")
    private String extend20;

    @ApiModelProperty(value = "安全分级")
    private String extend26;

    @ApiModelProperty(value = "质保分级")
    private String extend27;

    @ApiModelProperty(value = "物料类型")
    private String extend28;

    @ApiModelProperty(value = "UP码")
    private String extend29;
}
