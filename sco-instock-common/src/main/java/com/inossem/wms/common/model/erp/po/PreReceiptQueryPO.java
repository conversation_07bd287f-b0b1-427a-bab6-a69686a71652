package com.inossem.wms.common.model.erp.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PreReceiptQueryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "前序单据号", example = "3100000001")
    private String preReceiptCode;

    @ApiModelProperty(value = "采购订单号" , example = "4500000001")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "前续单据类型 领料出库414  预留单450", example = "414")
    private Integer preReceiptType;

    @ApiModelProperty(value = "查询起始时间", example = "2018-11-09")
    private Date startTime;

    @ApiModelProperty(value = "查询截至时间", example = "2018-11-29")
    private Date endTime;

    @ApiModelProperty(value = "库存地点权限" , example = "2100")
    private List<Long> locationIdList;

    @ApiModelProperty(value = "单据类型 领料出库414" , example = "414")
    private List<Integer> outputReceiptTypeList;

    @ApiModelProperty(value = "物料编码(主设备编码)" , example = "M001005")
    private String parentMatCode;

    @ApiModelProperty(value = "物料描述(主设备描述)")
    private String parentMatName;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "物料编码(子设备编码)" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述(子设备描述)")
    private String matName;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "UP码")
    private String extend29;

    @ApiModelProperty(value = "物资编码")
    private String extend20;

    @ApiModelProperty(value = "规格型号")
    private String extend24;

}
