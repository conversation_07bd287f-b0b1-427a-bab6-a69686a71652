package com.inossem.wms.common.model.metadata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2021/03/02 19:58
 */
@Data
public class MetadataFieldVO {

    private Long id;

    @ApiModelProperty(value = "元数据ID" , example = "1")
    private Long metadataId;

    @ApiModelProperty(value = "属性名称" , example = "String")
    private String attrName;

    @ApiModelProperty(value = "属性类型名称" , example = "String")
    private String attrType;

    @ApiModelProperty(value = "关联表" , example = "String")
    private String rlatTableName;

    @ApiModelProperty(value = "原属性名" , example = "String")
    private String sourceAttrName;

    @ApiModelProperty(value = "目标属性名" , example = "String")
    private String targetAttrName;

    @ApiModelProperty(value = "子表外键属性名" , example = "String")
    private String sonTbFkAttrName;

    @ApiModelProperty(value = "工程中的-maven模块名称后的--包前缀" , example = "String")
    private String projectPackageName;

    @ApiModelProperty(value = "工程中的-maven模块名称" , example = "String")
    private String projectModel;

    @ApiModelProperty(value = "实体备注名称" , example = "String")
    private String entityName;

}
