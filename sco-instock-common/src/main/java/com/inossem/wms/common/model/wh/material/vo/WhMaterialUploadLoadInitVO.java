package com.inossem.wms.common.model.wh.material.vo;


import com.inossem.wms.common.model.dictionary.entity.DicDictionary;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel(value="上下架策略VO", description="仓库物料主数据表")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WhMaterialUploadLoadInitVO {

    @ApiModelProperty(value = "上架策略")
    List<DicDictionary> loadList;

    @ApiModelProperty(value = "下架策略策略")
    List<DicDictionary> unloadList;

}
