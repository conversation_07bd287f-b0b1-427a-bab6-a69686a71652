package com.inossem.wms.common.model.bizdomain.settlement.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptCapitalPlanSearchPO", description = "资金计划查询入参")
public class BizReceiptCapitalPlanSearchPO extends PageCommon {

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "资金计划描述", example = "152214349873153")
    private String planDescribe;

    @ApiModelProperty(value = "单据状态", example = "2500")
    private Integer receiptStatus;

    @ApiModelProperty(value = "状态集合", example = "2500")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "合同编号", example = "2500")
    private String contractCode;

    @ApiModelProperty(value = "合同名称", example = "2500")
    private String contractName;

    @ApiModelProperty(value = "付款预算单号", example = "2500")
    private String paymentPlanReceiptCode;
}
