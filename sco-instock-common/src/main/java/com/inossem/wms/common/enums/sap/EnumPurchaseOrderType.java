package com.inossem.wms.common.enums.sap;

/**
 * 采购订单类型枚举
 */
public enum EnumPurchaseOrderType {
    
    STANDARD_PO("ZY01", "标准采购订单"),
    ASSET_PO("ZY02", "资产类采购订单"),
    SERVICE_PO("ZY03", "服务类采购订单"),
    EXPENSE_PO("ZY04", "费用类采购订单"),
    CONSTRUCTION_PO("ZY05", "施工类采购订单"),
    RETURN_PO("ZY06", "退货采购订单");

    private String code;
    private String name;

    EnumPurchaseOrderType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举
     */
    public static EnumPurchaseOrderType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (EnumPurchaseOrderType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断code是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}
