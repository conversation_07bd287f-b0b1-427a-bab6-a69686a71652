package com.inossem.wms.common.model.bizdomain.logistics.vo;

import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 物流清关费用前续单据【采购订单】
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-24
 */
@ApiModel(value = "物流清关费用前续单据【采购订单】", description = "物流清关费用前续单据【采购订单】")
@Data
public class BizReceiptLogisticsPreHeadVo {

    @ApiModelProperty(value = "參考单据号" , example = "SH01000005")
    private String referReceiptCode;

    @ApiModelProperty(value = "SAP订单创建时间" , example = "2021-05-11")
    private Date erpCreateTime;

    @ApiModelProperty(value = "SAP订单创建人name" , example = "管理员")
    private String erpCreateUserName;

    @ApiModelProperty(value = "行项目-验收单")
    private List<BizReceiptLogisticsItemDTO> children;

    @ApiModelProperty(value = "采购员编号" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "采购员名称" , example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "合同号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "合同描述" , example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "填充属性 - 供应商名称" , example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码" , example = "60000001")
    private String supplierCode;

}
