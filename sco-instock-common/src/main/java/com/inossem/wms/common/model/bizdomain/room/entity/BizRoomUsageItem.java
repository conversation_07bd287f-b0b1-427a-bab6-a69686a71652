package com.inossem.wms.common.model.bizdomain.room.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 房间使用信息明细表，记录了本次房间使用过程中，入住人的相关信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomUsageItem对象", description="房间使用信息明细表，记录了本次房间使用过程中，入住人的相关信息")
@TableName("biz_room_usage_item")
public class BizRoomUsageItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "房间id")
    private Long roomId;

    @ApiModelProperty(value = "住房申请单行项目id")
    private Long checkInReqItemId;

    @ApiModelProperty(value = "退房申请单行项目id")
    private Long checkOutReqItemId;

    @ApiModelProperty(value = "入住人姓名")
    private String checkInUserName;

    @ApiModelProperty(value = "入住人姓别（1：男，2：女，3：未知）")
    private Integer checkInUserSex;

    @ApiModelProperty(value = "入住人身份证号")
    private String checkInUserIdNumber;

    @ApiModelProperty(value = "入住人护照号")
    private String checkInUserPassportNumber;

    @ApiModelProperty(value = "入住时间")
    private Date checkInTime;

    @ApiModelProperty(value = "搬出时间")
    private Date checkOutTime;

    @ApiModelProperty(value = "身份证/护照图片id1")
    private Long identificationImgId1;

    @ApiModelProperty(value = "身份证/护照图片id2")
    private Long identificationImgId2;

    @ApiModelProperty(value = "身份证/护照图片id3")
    private Long identificationImgId3;

    @ApiModelProperty(value = "身份证/护照图片id4")
    private Long identificationImgId4;

    @ApiModelProperty(value = "身份证/护照图片id5")
    private Long identificationImgId5;

    @ApiModelProperty(value = "签证图片id1")
    private Long visaImgId1;

    @ApiModelProperty(value = "签证图片id2")
    private Long visaImgId2;

    @ApiModelProperty(value = "签证图片id3")
    private Long visaImgId3;

    @ApiModelProperty(value = "签证图片id4")
    private Long visaImgId4;

    @ApiModelProperty(value = "签证图片id5")
    private Long visaImgId5;

    @ApiModelProperty(value = "结算时间（此时间之前的费用已结清）")
    private Date settlementTime;


}
