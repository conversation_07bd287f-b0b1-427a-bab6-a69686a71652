package com.inossem.wms.common.model.label.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 标签表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizStockdocLabelData对象", description = "盘点凭证标签表")
@TableName("biz_stockdoc_label_data")
public class BizStockdocLabelData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "标签id" , example = "157691964096513")
    private Long labelId;

    @ApiModelProperty(value = "标签编码" , example = "D00000000000011344")
    private String labelCode;

    @ApiModelProperty(value = "标签类型 0：普通标签 1：RFID抗金属  2：RFID非抗金属" , example = "0")
    private Integer labelType;

    @ApiModelProperty(value = "标签模型【1-物料 2-仓位 3-托盘】" , example = "1")
    private Integer labelModel;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private Long matId;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点" , example = "10")
    private Long locationId;

    @ApiModelProperty(value = "批次号" , example = "151692536512514")
    private Long batchId;

    @ApiModelProperty(value = "SN编码" , example = "D00000000000011344")
    private String snCode;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "仓库编码" , example = "S200")
    private Long whId;

    @ApiModelProperty(value = "存储类型编码" , example = "801")
    private Long typeId;

    @ApiModelProperty(value = "仓位编码" , example = "00")
    private Long binId;

    @ApiModelProperty(value = "托盘编码" , example = "P001")
    private Long cellId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "源标签id【拆包】" , example = "0")
    private Long sourceLabelId;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "操作数量", name = "10")
    @TableField(exist = false)
    private BigDecimal operationQty;
    @TableField(exist = false)
    private Integer rid;

    @ApiModelProperty(value = "期间")
    private Date postTime;

    @ApiModelProperty(value = "head表主键"  )
    private Long headId;

}
