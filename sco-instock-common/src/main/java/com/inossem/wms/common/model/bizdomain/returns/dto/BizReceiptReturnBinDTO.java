package com.inossem.wms.common.model.bizdomain.returns.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecClassifyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 退库单配货明细传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "退库单配货明细传输对象", description = "退库单配货明细传输对象")
public class BizReceiptReturnBinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "出库单号" , example = "CK01000400")
    private String outputCode;

    @ApiModelProperty(value = "出库数量" , example = "100")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "已退库数量" , example = "10")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "可操作数量" , example = "10")
    private BigDecimal operationalQty;

    @ApiModelProperty(value = "所基于的出库单配货bin表对应itemId" , example = "155168456900610")
    private Long outputItemId;

    @ApiModelProperty(value = "仓位编码" , example = "00")
    private String binCode;

    @ApiModelProperty(value = "存储类型编码" , example = "801")
    private String typeCode;

    @ApiModelProperty(value = "存储类型描述" , example = "入库临时区")
    private String typeName;

    @ApiModelProperty(value = "扩展属性 - 批次图片")
    private List<BizBatchImgDTO> batchImgList;

    @ApiModelProperty(value = "填充属性 - 批次信息")
    private BizBatchInfoDTO batchInfo;

    @ApiModelProperty(value = "扩展属性 - 物料批次特性")
    private BizSpecClassifyDTO materialSpecDTO;

    @ApiModelProperty(value = "填充属性 - 打印机IP" , example = "127.0.0.1")
    private String printerIp;

    @ApiModelProperty(value = "填充属性 - 打印机端口" , example = "6100")
    private String printerPort;

    @ApiModelProperty(value = "扩展属性 - 1:默认  0：非默认" , example = "1")
    private Integer printerDefault;

    @ApiModelProperty(value = "扩展属性 - 是否是便携式普通打印机 0：否  1：是" , example = "1")
    private Integer printerIsPortable;

    @ApiModelProperty(value = "扩展属性 - 待码盘数量" , example = "10")
    private BigDecimal unCodeDiskQty;

    @ApiModelProperty(value = "扩展属性 - 标签与单据关联数据")
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptBinId")
    private List<BizLabelReceiptRelDTO> labelReceiptRelDTOList;

    @ApiModelProperty(value = "扩展属性 - 打印状态名称 1-已打印 0-未打印" , example = "0")
    private String printStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 单品/批次  0批次 1单品" , example = "1")
    private String isSingleI18n;

    @ApiModelProperty(value = "标签类型  1：RFID抗金属  2：RFID非抗金属 3：普通标签" , example = "1")
    private String tagTypeI18n;

    @ApiModelProperty(value = "前序物料凭证行项目")
    private String preMatDocRid;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "item表id" , example = "149901631619075")
    private Long itemId;

    @ApiModelProperty(value = "退库单配货序号" , example = "1")
    private String bid;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "存储单元id" , example = "152758218981377")
    private Long cellId;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*,isSingle,tagType", targetAttrName = "batchInfo,isSingle,tagType")
    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "作业数量" , example = "10")
    private BigDecimal taskQty;

    @RlatAttr(rlatTableName = "biz_receipt_output_bin", sourceAttrName = "headId,itemId,qty,returnQty,matDocRid", targetAttrName = "outputHeadId,outputItemId,outputQty,returnQty,preMatDocRid")
    @ApiModelProperty(value = "所基于的出库单配货bin表id" , example = "155295103909889")
    private Long outputBinId;

    @ApiModelProperty(value = "bin打印状态 1-已打印 0-未打印" , example = "1")
    private Integer printStatus;

    @ApiModelProperty(value = "打印份数" , example = "10")
    private Integer printNum;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    private Integer isSingle;

    @ApiModelProperty(value = "标签类型 0：普通标签  1：RFID抗金属  2：RFID非抗金属" , example = "10")
    private Integer tagType;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;

    @ApiModelProperty(value = "到期日期" , example = "2021-05-11")
    private Date arrivalDate;
    /* ********************** 顺序填充字段开始 *************************/

    @RlatAttr(rlatTableName = "biz_receipt_output_head", sourceAttrName = "receiptCode", targetAttrName = "outputCode")
    @ApiModelProperty(value = "所基于的出库单配货bin表对应headId" , example = "155295043092481")
    private Long outputHeadId;

    /* ********************** 顺序填充字段结束 *************************/
    @ApiModelProperty(value = "扩展属性 - 预留单号")
    private String reserveReceiptCode;

    @ApiModelProperty(value = "扩展属性 - 预留单行号")
    private String reserveReceiptRid;
}
