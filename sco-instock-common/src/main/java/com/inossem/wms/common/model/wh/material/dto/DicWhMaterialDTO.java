package com.inossem.wms.common.model.wh.material.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DicWhMaterial对象", description="仓库物料主数据表")
public class DicWhMaterialDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material",sourceAttrName = "matName,matCode",targetAttrName ="matName,matCode" )
    private Long matId;

    @ApiModelProperty("物料编码")
    private String matCode;

    @ApiModelProperty("物料名称")
    private String matName;

    @ApiModelProperty(value = "仓库地址")
    private String address;

    @ApiModelProperty(value = "是否使用")
    private Boolean isUsed;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "物料仓库明细")
    @SonAttr(sonTbFkAttrName = "headId",sonTbName = "dic_wh_material_item")
    private List<DicWhMaterialItemDTO> dicWhMaterialItems;

    @ApiModelProperty(value = "存储单元类型")
    private Long storeCellType;

    @ApiModelProperty(value = "存储类型ID")
    private Long storeTypeId;

}
