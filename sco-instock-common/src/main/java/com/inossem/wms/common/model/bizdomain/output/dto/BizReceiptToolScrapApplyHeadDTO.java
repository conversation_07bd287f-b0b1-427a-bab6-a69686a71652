package com.inossem.wms.common.model.bizdomain.output.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 报废申请单抬头传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "报废申请单抬头传输对象", description = "报废申请单抬头传输对象")
public class BizReceiptToolScrapApplyHeadDTO implements Serializable {

    /* ********************** 扩展字段开始 *************************/

    private static final long serialVersionUID = 12423424123123L;
    @SonAttr(sonTbName = "biz_receipt_tool_scrap_apply_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptToolScrapApplyItemDTO> itemDTOList;
    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据日志")
    List<BizCommonReceiptOperationLogDTO> logList;
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    List<BizCommonReceiptAttachment> fileList;
    @ApiModelProperty(value = "单据流")
    List<BizCommonReceiptRelationDTO> relationList;
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;
    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;
    @ApiModelProperty(value = "凭证日期" , example = "2021-05-11")
    private Date docDate;
    @ApiModelProperty(value = "过账日期" , example = "2021-05-11")
    private Date postingDate;
    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;
    @ApiModelProperty(value = "单据移动类型id，其他出库用于显示出库类型" , example = "3011")
    private Long moveTypeId;
    @ApiModelProperty(value = "单据移动类型，其他出库用于显示出库类型" , example = "301")
    private String moveTypeCode;
    @ApiModelProperty(value = "单据移动类型特殊库存类型，其他出库用于显示出库类型" , example = "Q")
    private String moveTypeSpecStock;
    @ApiModelProperty(value = "单据移动类型描述，其他出库用于显示出库类型描述" , example = "Q转Q(工厂)")
    private String moveTypeName;
    @ApiModelProperty(value = "单据名称" , example = "领料出库单")
    private String receiptTypeI18n;
    /* ********************** 扩展字段结束 *************************/
    @ApiModelProperty(value = "单据状态名称" , example = "草稿")
    private String receiptStatusI18n;
    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "出库单编码" , example = "CK01000400")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 销售出库411 预留出库412 采购退货413 领料出库414 报废出库415 其他出库416" , example = "411出库单")
    private Integer receiptType;

    @ApiModelProperty(value = "10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成、70已退库" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
