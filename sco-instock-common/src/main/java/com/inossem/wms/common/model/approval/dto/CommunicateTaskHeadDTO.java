package com.inossem.wms.common.model.approval.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 沟通任务抬头DTO
 * 每发起一次沟通任务，都会有一条沟通任务抬头记录
 *
 * <AUTHOR>
 * @date 2025/05/15
 **/
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@ApiModel(value = "CommunicateTaskHeadDTO", description = "沟通任务抬头DTO")
public class CommunicateTaskHeadDTO {

    @ApiModelProperty(value = "沟通发起人的用户编码")
    private String userCode;

    @ApiModelProperty(value = "沟通发起人的用户姓名")
    private String userName;

    @ApiModelProperty(value = "是否允许多级沟通，1：允许多级沟通，0或空或其他：不允许多级沟通")
    private String isCreateChild;

    @ApiModelProperty(value = "是否隐藏沟通意见，1：隐藏沟通意见，0或空或其他：不隐藏沟通意见")
    private String isHideCommunicationOpinions;

    @ApiModelProperty(value = "限制子级沟通范围，列表为空时子级沟通可以任选沟通人，列表有值时子级沟通只能选列表内的人员")
    private List<String> childUserCodeList;

    @ApiModelProperty(value = "沟通任务行项目列表")
    List<CommunicateTaskItemDTO> itemList = new ArrayList<>();
}
