package com.inossem.wms.common.model.bizdomain.room.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 住房退订单行项目DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomReceiptCheckOutItemDTO", description="住房退订单行项目DTO")
public class BizRoomReceiptCheckOutItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "入住人姓名")
    private String checkInUserName;

    @ApiModelProperty(value = "入住人姓别（1：男，2：女，3：未知）")
    private Integer checkInUserSex;

    @ApiModelProperty(value = "入住人姓别国际化")
    private String checkInUserSexI18n;

    @ApiModelProperty(value = "入住人身份证号")
    private String checkInUserIdNumber;

    @ApiModelProperty(value = "入住人护照号")
    private String checkInUserPassportNumber;

    @ApiModelProperty(value = "入住时间")
    private Date checkInTime;

    @ApiModelProperty(value = "行项目文本")
    private String itemStatusI18n;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "房间使用记录抬头表id")
    @RlatAttr(rlatTableName = "biz_room_usage_head", sourceAttrName = "contractId,contractCode,contractName", targetAttrName = "contractId,contractCode,contractName")
    private Long roomUsageHeadId;

    @ApiModelProperty(value = "房间使用记录行项目表id")
    @RlatAttr(rlatTableName = "biz_room_usage_item", sourceAttrName = "checkInUserName,checkInUserSex,checkInUserIdNumber,checkInUserPassportNumber,checkInTime", targetAttrName = "checkInUserName,checkInUserSex,checkInUserIdNumber,checkInUserPassportNumber,checkInTime")
    private Long roomUsageItemId;

    @ApiModelProperty(value = "房间id（冗余存储）")
    private Long roomId;

    @ApiModelProperty(value = "房间编号【楼栋号-房间号】（冗余存储）")
    private String roomCode;


}
