package com.inossem.wms.common.model.bizdomain.matview.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 物料主数据视图审批-工厂级别
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@TableName("biz_material_view_audit_factory")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物料主数据视图审批-工厂级别ExportVO")
public class BizMaterialViewAuditFactoryExportVO  {

    @ExcelProperty(value = "*物料编码")
    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "*工厂" )
    @ApiModelProperty(value = "工厂")
    private String ftyCode;

    @ExcelProperty(value = "*库存地点")
    @ApiModelProperty(value = "库存地点")
    private String locationCode;

    @ExcelProperty(value = "采购组")
    @ApiModelProperty(value = "采购组")
    private String purchaseGroupCode;

    @ExcelProperty(value = "*负责部门")
    @ApiModelProperty(value = "负责部门")
    private String deptCode;

    @ExcelProperty(value = "*负责科室")
    @ApiModelProperty(value = "负责科室")
    private String officeCode;

    @ExcelProperty(value = "生产仓储地点" )
    @ApiModelProperty(value = "生产仓储地点")
    private String produceLocationCode;

    @ExcelProperty(value = "*物资分类" )
    @ApiModelProperty(value = "物资分类1-行政物资；2-生产物资-常规备件，包括消耗性材料；3-生产物资-战略备件4-生产物资-变更改造物资；5-生产物资-工器具;6-生产物资-其它自管物资")
    private Integer matCategory;

    @ExcelProperty(value = "*包装方式" )
    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ExcelProperty(value = "存储条件")
    @ApiModelProperty(value = "存储条件")
    private String storageCondition;

    @ExcelProperty(value = "*存储方式" )
    @ApiModelProperty(value = "存储方式 0 平放；1 直立存放；2 悬挂； 3 朝上存放；4 非关闭状态；5 倒置存放；")
    private Integer storageType;

    @ExcelProperty(value = "*验收方式" )
    @ApiModelProperty(value = "验收方式 1 用户试验验收；2 用户参与验收；3 仓库独立验收")
    private Integer inspectType;

    @ExcelProperty(value = "*制造商零件编号" )
    @ApiModelProperty(value = "制造商零件编号")
    private String manufacturerPartNumber;

    @ExcelProperty(value = "是否SPV备件")
    @ApiModelProperty(value = "是否SPV备件 下拉选项包括X:是空:否")
    private String spv;

    @ExcelProperty(value = "是否带放射性" )
    @ApiModelProperty(value = "是否带放射性X:是空:否")
    private String radioactive;

    @ExcelProperty(value = "一回路禁用材料")
    @ApiModelProperty(value = "一回路禁用材料X:是空:否")
    private String oneLoopDisable;

    @ExcelProperty(value = "危险物料号")
    @ApiModelProperty(value = "危险物料号")
    private String hazardousMatCode;

    @ExcelProperty(value = "RCCE等级")
    @ApiModelProperty(value = "RCCE等级 K1； K2； K3； NO")
    private String rcceLevel;

    @ExcelProperty(value = "是否受控")
    @ApiModelProperty(value = "是否受控X:是空:否")
    private String controlled;

    @ExcelProperty(value = "设备集成商")
    @ApiModelProperty(value = "设备集成商")
    private String equipmentIntegrator;

    @ExcelProperty(value = "制造商编号" )
    @ApiModelProperty(value = "制造商编号")
    private String manufacturerCode;

    @ExcelProperty(value = "制造厂图纸号")
    @ApiModelProperty(value = "制造厂图纸号")
    private String manufacturerMapCode;

    @ExcelProperty(value = "制造厂图项号")
    @ApiModelProperty(value = "制造厂图项号")
    private String manufacturerPictureItemCode;

    @ExcelProperty(value = "核安全等级")
    @ApiModelProperty(value = "核安全等级  N1；N2；N3；S1；S2；LS；1E； NO+；NO。")
    private String nuclearSafeLevel;

    @ExcelProperty(value = "特定工厂的物料状态")
    @ApiModelProperty(value = "特定工厂的物料状态 Z1-冻结采购；01-全部冻结；")
    private String specFtyMatStatus;

    @ExcelProperty(value = "冻结原因")
    @ApiModelProperty(value = "冻结原因" +
            "1-待澄清；\n" +
            "2-待替代论证；\n" +
            "3-被改造；\n" +
            "4-不采购的父码；\n" +
            "5-自制；\n" +
            "6-供应商只成套供应；\n" +
            "7-不满足现场使用要求； \n" +
            "8-库存量过高；\n" +
            "9-被替代-原物项不满足现场使用；\n" +
            "10-被替代-原物项可用；\n" +
            "11-休眠备件")
    private Integer freezeReason;

    @ExcelProperty(value = "*批次管理")
    @ApiModelProperty(value = "批次管理 X:是；空:否")
    private String batchManagement;

    @ExcelProperty(value = "原产地国")
    @ApiModelProperty(value = "原产地国")
    private String originCountry;

    @ExcelProperty(value = "MRP类型" )
    @ApiModelProperty(value = "MRP类型 PD 只监控需求；VB 只监控库存；ND 不运行MRP，仅维护英文字母")
    private String mrpType;

    @ExcelProperty(value = "批量大小")
    @ApiModelProperty(value = "批量大小 EX 无固定批量类型； FX 固定批量类型； HB 补货到最大库存，仅维护英文字母")
    private String batchSize;

    @ExcelProperty(value = "再订购点")
    @ApiModelProperty(value = "再订购点")
    private String reorderPoint;

    @ExcelProperty(value = "最大库存水平")
    @ApiModelProperty(value = "最大库存水平")
    private BigDecimal stockMaximum;

    @ExcelProperty(value = "固定批量大小")
    @ApiModelProperty(value = "固定批量大小")
    private BigDecimal fixedBatch;

    @ExcelProperty(value = "收货处理时间")
    @ApiModelProperty(value = "收货处理时间(天数)")
    private Integer receiveProcessTime;

    @ExcelProperty(value = "计划交货时间")
    @ApiModelProperty(value = "计划交货时间（天数）")
    private Integer planDeliveryTime;

    @ExcelProperty(value = "安全库存")
    @ApiModelProperty(value = "安全库存")
    private BigDecimal safeQty;

    @ExcelProperty(value = "最小安全库存")
    @ApiModelProperty(value = "最小安全库存")
    private BigDecimal safeMinQty;

    @ExcelProperty(value = "后继的物料")
    @ApiModelProperty(value = "后继的物料")
    private String followUpMatCode;

    @ExcelProperty(value = "发货单位")
    @ApiModelProperty(value = "发货单位")
    private String sendGoodsUnit;

    @ExcelProperty(value = "工器具类型")
    @ApiModelProperty(value = "工器具类型 1 通用工器具;2 专用工器具")
    private Long toolsType;

    @ExcelProperty(value = "备件分类")
    @ApiModelProperty(value = "备件分类 A-易损耗备件B-非易损耗备件C-非备件D-Null")
    private String sparePartType;

    @ExcelProperty(value = "有寿期的整体备件")
    @ApiModelProperty(value = "有寿期的整体备件 X:是；空:否")
    private String lifetimeSparePart;

    @ExcelProperty(value = "是否抗震")
    @ApiModelProperty(value = "是否抗震 X:是；空:否")
    private String antiSeismic;

    @ExcelProperty(value = "是否循环设备")
    @ApiModelProperty(value = "是否循环设备 X:是；空:否")
    private String loopDevice;

    @ExcelProperty(value = "维保周期(月)")
    @ApiModelProperty(value = "维保周期(月)")
    private Integer maintenanceCycle;

    @ExcelProperty(value = "技术支持文件编号")
    @ApiModelProperty(value = "技术支持文件编号")
    private String supportFileCode;

    @ExcelProperty(value = "第一次使用提醒")
    @ApiModelProperty(value = "第一次使用提醒")
    private String firstUseNotice;

    @ExcelProperty(value = "物项替代号")
    @ApiModelProperty(value = "物项替代号")
    private String itemReplaceCode;

    @ExcelProperty(value = "可被以下物资替代")
    @ApiModelProperty(value = "可被以下物资替代")
    private String useReplaceMatCode;

    @ExcelProperty(value = "替代通知号" )
    @ApiModelProperty(value = "替代通知号")
    private String noticeReceiptCode;

    @ExcelProperty(value = "是否核安全报检")
    @ApiModelProperty(value = "是否核安全报检 X:是；空:否")
    private String nuclearSafeInspect;

    @ExcelProperty(value = "负责人工号")
    @ApiModelProperty(value = "负责人工号")
    private String userCode;

    @ExcelProperty(value = "移动平均价")
    @ApiModelProperty(value = "移动平均价")
    private BigDecimal moveAvgPrice;
}
