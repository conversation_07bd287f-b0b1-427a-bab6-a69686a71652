package com.inossem.wms.common.enums.apply;

import com.inossem.wms.common.model.common.enums.apply.BudgetAccountMapVO;
import com.inossem.wms.common.model.common.enums.apply.BudgetClassMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 预算分类-预算科目
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumBudget {

    EnumBudget1("budgetClass1", "剥采成本", "budgetAccount1", "剥采施工费"),
    EnumBudget2("budgetClass1", "剥采成本", "budgetAccount2", "挖机维保成本"),
    EnumBudget3("budgetClass1", "剥采成本", "budgetAccount3", "宇通维保成本"),
    EnumBudget4("budgetClass1", "剥采成本", "budgetAccount4", "临工维保成本"),
    EnumBudget5("budgetClass1", "剥采成本", "budgetAccount5", "辅助机械维保"),
    EnumBudget6("budgetClass1", "剥采成本", "budgetAccount6", "剥采-柴油"),
    EnumBudget7("budgetClass1", "剥采成本", "budgetAccount7", "备件挖机"),
    EnumBudget8("budgetClass1", "剥采成本", "budgetAccount8", "备件卡车"),
    EnumBudget9("budgetClass1", "剥采成本", "budgetAccount9", "备件辅助机械（装载机 推土机平地机等）"),
    EnumBudget10("budgetClass1", "剥采成本", "budgetAccount10", "剥离设备润滑油"),
    EnumBudget11("budgetClass2", "输煤系统", "budgetAccount11", "输煤系统运维"),
    EnumBudget12("budgetClass2", "输煤系统", "budgetAccount12", "输煤系统备件"),
    EnumBudget13("budgetClass2", "输煤系统", "budgetAccount13", "输煤系统-润滑油"),
    EnumBudget14("budgetClass2", "输煤系统", "budgetAccount14", "输煤系统电费"),
    EnumBudget15("budgetClass3", "疏干系统", "budgetAccount15", "疏干运维"),
    EnumBudget16("budgetClass3", "疏干系统", "budgetAccount16", "疏干材料备件及消耗品"),
    EnumBudget17("budgetClass3", "疏干系统", "budgetAccount17", "发电-燃料（重油）"),
    EnumBudget18("budgetClass3", "疏干系统", "budgetAccount18", "发电-燃料（柴油）"),
    EnumBudget19("budgetClass3", "疏干系统", "budgetAccount19", "电厂借用"),
    EnumBudget20("budgetClass3", "疏干系统", "budgetAccount20", "重油光伏备件及消耗品"),
    EnumBudget21("budgetClass3", "疏干系统", "budgetAccount21", "重油电站-润滑油"),
    EnumBudget22("budgetClass3", "疏干系统", "budgetAccount22", "CSR重油光伏区域除草"),
    EnumBudget23("budgetClass3", "疏干系统", "budgetAccount23", "固定人工-重油光伏"),
    EnumBudget24("budgetClass4", "其他费用", "budgetAccount24", "固定资产采购"),
    EnumBudget25("budgetClass4", "其他费用", "budgetAccount25", "水处理备件及消耗品"),
    EnumBudget26("budgetClass4", "其他费用", "budgetAccount26", "水处理运维"),
    EnumBudget27("budgetClass4", "其他费用", "budgetAccount27", "固定人工维保-其他"),
    EnumBudget28("budgetClass4", "其他费用", "budgetAccount28", "轻车维保"),
    EnumBudget29("budgetClass4", "其他费用", "budgetAccount29", "空调维保"),
    EnumBudget30("budgetClass4", "其他费用", "budgetAccount30", "道路维护"),
    EnumBudget31("budgetClass4", "其他费用", "budgetAccount31", "零星工程"),
    EnumBudget32("budgetClass4", "其他费用", "budgetAccount32", "全场电控系统备件"),
    EnumBudget33("budgetClass4", "其他费用", "budgetAccount33", "零星备件加工"),
    EnumBudget34("budgetClass4", "其他费用", "budgetAccount34", "急需在岸备件采购"),
    EnumBudget35("budgetClass4", "其他费用", "budgetAccount35", "辅料工具采购框架"),
    EnumBudget36("budgetClass4", "其他费用", "budgetAccount36", "水泵维修"),
    EnumBudget37("budgetClass4", "其他费用", "budgetAccount37", "车间设备备件采购"),
    EnumBudget38("budgetClass4", "其他费用", "budgetAccount38", "矿区材料"),
    EnumBudget39("budgetClass4", "其他费用", "budgetAccount39", "气体框架采购"),
    EnumBudget40("budgetClass4", "其他费用", "budgetAccount40", "软管框架采购"),
    EnumBudget41("budgetClass4", "其他费用", "budgetAccount41", "实验室耗材"),
    EnumBudget42("budgetClass4", "其他费用", "budgetAccount42", "其他类（生产指挥车/空调）润滑油"),
    EnumBudget43("budgetClass4", "其他费用", "budgetAccount43", "其他（电瓶轮胎）"),
    EnumBudget44("budgetClass4", "其他费用", "budgetAccount44", "复垦"),
    EnumBudget45("budgetClass4", "其他费用", "budgetAccount45", "边坡检测服务"),
    EnumBudget46("budgetClass4", "其他费用", "budgetAccount46", "设计技术服务"),
    EnumBudget47("budgetClass4", "其他费用", "budgetAccount47", "环境咨询EHS "),
    EnumBudget48("budgetClass4", "其他费用", "budgetAccount48", "端帮煤科研项目"),
    EnumBudget49("budgetClass4", "其他费用", "budgetAccount49", "特种设备年检服务"),
    EnumBudget50("budgetClass4", "其他费用", "budgetAccount50", "疏干水综合利用"),
    EnumBudget51("budgetClass4", "其他费用", "budgetAccount51", "保险"),
    EnumBudget52("budgetClass4", "其他费用", "budgetAccount52", "基建期遗留工作"),
    EnumBudget53("budgetClass4", "其他费用", "budgetAccount53", "运营期-在建工程"),
    EnumBudget54("budgetClass4", "其他费用", "budgetAccount54", "直接进口成本关税"),
    EnumBudget55("budgetClass4", "其他费用", "budgetAccount55", "直接进口成本滞港费及第三方费用"),
    EnumBudget56("budgetClass4", "其他费用", "budgetAccount56", "代进口服务费用"),
    EnumBudget57("budgetClass4", "其他费用", "budgetAccount57", "全程物流服务费用");

    @Getter
    private final String budgetClass;
    @Getter
    private final String budgetClassName;
    @Getter
    private final String budgetAccount;
    @Getter
    private final String budgetAccountName;

    public static List<BudgetClassMapVO> list;

    public static List<BudgetClassMapVO> toList() {
        if (list == null) {
            EnumBudget[] ary = EnumBudget.values();
            Map<String, String> map = new HashMap();
            List<BudgetClassMapVO> classList = new LinkedList<>();
            for (int num = 0; num < ary.length; num++) {
                EnumBudget budget = ary[num];
                String key = budget.getBudgetClass();
                BudgetAccountMapVO accountMap = new BudgetAccountMapVO();
                accountMap.setBudgetAccount(budget.getBudgetAccount());
                if (map == null || !map.containsKey(key)) {
                    map.put(key, key);

                    BudgetClassMapVO classMap = new BudgetClassMapVO();
                    classMap.setBudgetClass(budget.getBudgetClass());
                    List<BudgetAccountMapVO> accountList = new ArrayList<>();
                    accountList.add(accountMap);
                    classMap.setChildList(accountList);
                    classList.add(classMap);

                } else {
                    classList.get(classList.size() - 1).getChildList().add(accountMap);
                }
            }
            list = classList;
        }
        return list;
    }
}
