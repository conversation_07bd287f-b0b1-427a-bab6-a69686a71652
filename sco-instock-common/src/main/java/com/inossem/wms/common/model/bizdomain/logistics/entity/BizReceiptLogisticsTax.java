package com.inossem.wms.common.model.bizdomain.logistics.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物流清关费用税费", description="物流清关费用税费")
@TableName("biz_receipt_logistics_tax")
public class BizReceiptLogisticsTax implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id" , example = "157490654281729")
    private Long headId;


    @ApiModelProperty(value = "总关税")
    private BigDecimal totalTax;

    @ApiModelProperty(value = "总关税币种")
    private Integer totalTaxCurrency;

    @ApiModelProperty(value = "总关税供应商")
    private Long totalTaxSupplierId;

    @ApiModelProperty(value = "总关税供应商编码")
    private String totalTaxSupplierCode;

    @ApiModelProperty(value = "总关税供应商名称")
    private String totalTaxSupplierName;    

    @ApiModelProperty(value = "总附加关税")
    private BigDecimal totalAdditionalTax;

    @ApiModelProperty(value = "总附加关税币种")
    private Integer totalAdditionalTaxCurrency;

    @ApiModelProperty(value = "总附加关税供应商")
    private Long totalAdditionalTaxSupplierId;

    @ApiModelProperty(value = "总附加关税供应商编码")
    private String totalAdditionalTaxSupplierCode;

    @ApiModelProperty(value = "总附加关税供应商名称")
    private String totalAdditionalTaxSupplierName;

    @ApiModelProperty(value = "总销售税")
    private BigDecimal totalSalesTax;

    @ApiModelProperty(value = "总销售税币种")
    private Integer totalSalesTaxCurrency;  

    @ApiModelProperty(value = "总销售税供应商")
    private Long totalSalesTaxSupplierId;

    @ApiModelProperty(value = "总销售税供应商编码")
    private String totalSalesTaxSupplierCode;   

    @ApiModelProperty(value = "总销售税供应商名称")
    private String totalSalesTaxSupplierName;


    @ApiModelProperty(value = "总附加销售税")
    private BigDecimal totalAdditionalSalesTax; 

    @ApiModelProperty(value = "总附加销售税币种")
    private Integer totalAdditionalSalesTaxCurrency;    

    @ApiModelProperty(value = "总附加销售税供应商")
    private Long totalAdditionalSalesTaxSupplierId;

    @ApiModelProperty(value = "总附加销售税供应商编码")
    private String totalAdditionalSalesTaxSupplierCode;

    @ApiModelProperty(value = "总附加销售税供应商名称")
    private String totalAdditionalSalesTaxSupplierName; 


    @ApiModelProperty(value = "总调节税")
    private BigDecimal totalAdjustmentTax;  

    @ApiModelProperty(value = "总调节税币种")
    private Integer totalAdjustmentTaxCurrency;

    @ApiModelProperty(value = "总调节税供应商")
    private Long totalAdjustmentTaxSupplierId;

    @ApiModelProperty(value = "总调节税供应商编码")
    private String totalAdjustmentTaxSupplierCode;  

    @ApiModelProperty(value = "总调节税供应商名称")
    private String totalAdjustmentTaxSupplierName;

    @ApiModelProperty(value = "总所得税")
    private BigDecimal totalIncomeTax;

    @ApiModelProperty(value = "总所得税币种")
    private Integer totalIncomeTaxCurrency;

    @ApiModelProperty(value = "总所得税供应商")
    private Long totalIncomeTaxSupplierId;  

    @ApiModelProperty(value = "总所得税供应商编码")
    private String totalIncomeTaxSupplierCode;

    @ApiModelProperty(value = "总所得税供应商名称")
    private String totalIncomeTaxSupplierName;


    @ApiModelProperty(value = "Totall GST")
    private BigDecimal totalGst;

    @ApiModelProperty(value = "总GST币种")
    private Integer totalGstCurrency;

    @ApiModelProperty(value = "总GST供应商")
    private Long totalGstSupplierId;

    @ApiModelProperty(value = "总GST供应商编码")
    private String totalGstSupplierCode;    

    @ApiModelProperty(value = "总GST供应商名称")
    private String totalGstSupplierName;


    @ApiModelProperty(value = "总联邦消费税")
    private BigDecimal totalFederalConsumptionTax;


    @ApiModelProperty(value = "总联邦消费税币种")
    private Integer totalFederalConsumptionTaxCurrency; 

    @ApiModelProperty(value = "总联邦消费税供应商")
    private Long totalFederalConsumptionTaxSupplierId;

    @ApiModelProperty(value = "总联邦消费税供应商编码")
    private String totalFederalConsumptionTaxSupplierCode;

    @ApiModelProperty(value = "总联邦消费税供应商名称")
    private String totalFederalConsumptionTaxSupplierName;  


    @ApiModelProperty(value = "总逾期罚款")
    private BigDecimal totalLatePenalty;

    @ApiModelProperty(value = "总逾期罚款币种")
    private Integer totalLatePenaltyCurrency;

    @ApiModelProperty(value = "总逾期罚款供应商")
    private Long totalLatePenaltySupplierId;    

    @ApiModelProperty(value = "总逾期罚款供应商编码")
    private String totalLatePenaltySupplierCode;

    @ApiModelProperty(value = "总逾期罚款供应商名称")
    private String totalLatePenaltySupplierName;

    @ApiModelProperty(value = "总发票缺失")
    private BigDecimal totalInvoiceMissing;

    @ApiModelProperty(value = "总发票缺失币种")
    private Integer totalInvoiceMissingCurrency;

    @ApiModelProperty(value = "总发票缺失供应商")
    private Long totalInvoiceMissingSupplierId;

    @ApiModelProperty(value = "总发票缺失供应商编码")
    private String totalInvoiceMissingSupplierCode;

    @ApiModelProperty(value = "总发票缺失供应商名称")
    private String totalInvoiceMissingSupplierName; 


    @ApiModelProperty(value = "总GD提交费")
    private BigDecimal totalGdSubmissionFee;

    @ApiModelProperty(value = "总GD提交费币种")
    private Integer totalGdSubmissionFeeCurrency;   

    @ApiModelProperty(value = "总GD提交费供应商")
    private Long totalGdSubmissionFeeSupplierId;

    @ApiModelProperty(value = "总GD提交费供应商编码")
    private String totalGdSubmissionFeeSupplierCode;

    @ApiModelProperty(value = "总GD提交费供应商名称")
    private String totalGdSubmissionFeeSupplierName;    



    @ApiModelProperty(value = "总基建税")
    private BigDecimal totalInfrastructureTax;  

    @ApiModelProperty(value = "总基建税币种")
    private Integer totalInfrastructureTaxCurrency;

    @ApiModelProperty(value = "总基建税供应商")
    private Long totalInfrastructureTaxSupplierId;

    @ApiModelProperty(value = "总基建税供应商编码")
    private String totalInfrastructureTaxSupplierCode;  

    @ApiModelProperty(value = "总基建税供应商名称")
    private String totalInfrastructureTaxSupplierName;

    @ApiModelProperty(value = "总印花税")
    private BigDecimal totalStampDuty;  

    @ApiModelProperty(value = "总印花税币种")
    private Integer totalStampDutyCurrency;

    @ApiModelProperty(value = "总印花税供应商")
    private Long totalStampDutySupplierId;

    @ApiModelProperty(value = "总印花税供应商编码")
    private String totalStampDutySupplierCode;  

    @ApiModelProperty(value = "总印花税供应商名称")
    private String totalStampDutySupplierName;  


    @ApiModelProperty(value = "总检验检测费")
    private BigDecimal totalInspectionFee;  

    @ApiModelProperty(value = "总检验检测费币种")
    private Integer totalInspectionFeeCurrency; 

    @ApiModelProperty(value = "总检验检测费供应商")
    private Long totalInspectionFeeSupplierId;  

    @ApiModelProperty(value = "总检验检测费供应商编码")
    private String totalInspectionFeeSupplierCode;

    @ApiModelProperty(value = "总检验检测费供应商名称")
    private String totalInspectionFeeSupplierName;  


    @ApiModelProperty(value = "总出口报关服务费")
    private BigDecimal totalExportCustomsServiceFee;    

    @ApiModelProperty(value = "总出口报关服务费币种")
    private Integer totalExportCustomsServiceFeeCurrency;   

    @ApiModelProperty(value = "总出口报关服务费供应商")
    private Long totalExportCustomsServiceFeeSupplierId;    

    @ApiModelProperty(value = "总出口报关服务费供应商编码")
    private String totalExportCustomsServiceFeeSupplierCode;    

    @ApiModelProperty(value = "总出口报关服务费供应商名称")
    private String totalExportCustomsServiceFeeSupplierName;    



    @ApiModelProperty(value = "总海/空运费")
    private BigDecimal totalOceanFreight;

    @ApiModelProperty(value = "总海/空运费币种")
    private Integer totalOceanFreightCurrency;  

    @ApiModelProperty(value = "总海/空运费供应商")
    private Long totalOceanFreightSupplierId;   

    @ApiModelProperty(value = "总海/空运费供应商编码")
    private String totalOceanFreightSupplierCode;   

    @ApiModelProperty(value = "总海/空运费供应商名称")
    private String totalOceanFreightSupplierName;   

    @ApiModelProperty(value = "总进口清关服务费")
    private BigDecimal totalImportCustomsServiceFee;    

    @ApiModelProperty(value = "总进口清关服务费币种")
    private Integer totalImportCustomsServiceFeeCurrency;   

    @ApiModelProperty(value = "总进口清关服务费供应商")
    private Long totalImportCustomsServiceFeeSupplierId;    

    @ApiModelProperty(value = "总进口清关服务费供应商编码")
    private String totalImportCustomsServiceFeeSupplierCode;    

    @ApiModelProperty(value = "总进口清关服务费供应商名称")
    private String totalImportCustomsServiceFeeSupplierName;    
    
    @ApiModelProperty(value = "总内陆运输费")
    private BigDecimal totalInlandTransportFee; 

    @ApiModelProperty(value = "总内陆运输费币种")
    private Integer totalInlandTransportFeeCurrency;       

    @ApiModelProperty(value = "总内陆运输费供应商")
    private Long totalInlandTransportFeeSupplierId;     

    @ApiModelProperty(value = "总内陆运输费供应商编码")
    private String totalInlandTransportFeeSupplierCode;    

    @ApiModelProperty(value = "总内陆运输费供应商名称")
    private String totalInlandTransportFeeSupplierName; 
    
    @ApiModelProperty(value = "总进口拖车押车费 （车船直取）")
    private BigDecimal totalImportTruckFee;

    @ApiModelProperty(value = "总进口拖车押车费币种")
    private Integer totalImportTruckFeeCurrency;

    @ApiModelProperty(value = "总进口拖车押车费供应商")
    private Long totalImportTruckFeeSupplierId; 

    @ApiModelProperty(value = "总进口拖车押车费供应商编码")
    private String totalImportTruckFeeSupplierCode; 

    @ApiModelProperty(value = "总进口拖车押车费供应商名称")
    private String totalImportTruckFeeSupplierName;  

}
