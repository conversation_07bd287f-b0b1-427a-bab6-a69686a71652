package com.inossem.wms.common.model.bizdomain.input.po;


import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> wang
 * @description 工器具主数据查询入参类
 * @date 2022/4/1 16:31
 */

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "工器具主数据查询入参类", description = "工器具主数据查询入参类")
@Data
public class ToolsMaterialInfoSearchPO extends PageCommon {

    @ApiModelProperty(value = "出厂编码")
    private String outFtyCode;

    @ApiModelProperty(value = "冻结描述" , example = "已冻结")
    private String isFreezeI18n;

    @ApiModelProperty(value = "是否冻结【1是，0否】", example = "0")
    private Integer isFreeze;

    /**
     * 工器具的工具编码
     */
    @ApiModelProperty(value = "工器具的工具编码")
    private String batchCode;

    @ApiModelProperty(value = "批次信息id" , example = "152286145871874")
    private Long batchId;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "工具类型id" , example = "1231")
    private Long toolTypeId;

    /**
     * 维保周期
     */
    @ApiModelProperty(value = "维保周期")
    private Integer maintenanceCycle;

    /**
     * 维护预警期（天）
     */
    @ApiModelProperty(value = "维护预警期")
    private Integer maintenanceWarningPeriod;

    /**
     * 特殊库存类型 E 现有订单 K 寄售（供应商） O 供应商分包库存 Q 项目库
     */
    @ApiModelProperty(value = "特殊库存类型")
    private String specStock;


    /**
     * @deprecated 使用 toolManageStatusRemark 替换
     */
    @Deprecated
    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "管理状态备注")
    private String toolManageStatusRemark;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private Integer toolStatus;

    /**
     * 送检单位
     */
    @ApiModelProperty(value = "送检单位")
    private String toolInspectUnit;

    /**
     * 借用人
     */
    @ApiModelProperty(value = "借用人")
    private String borrowUserName;

    /**
     * 借用部门
     */
    @ApiModelProperty(value = "借用部门")
    private String borrowDeptName;

    /**
     * 维保有效期
     */
    @ApiModelProperty(value = "维保有效期")
    private Date maintenanceValidDate;

    /**
     * 存储类型id
     */
    @ApiModelProperty(value = "存储类型id")
    private Long typeId;

    @ApiModelProperty(value = "规格型号")
    private String formatCode;

    @ApiModelProperty("库存ID")
    private Long locationId;

}
