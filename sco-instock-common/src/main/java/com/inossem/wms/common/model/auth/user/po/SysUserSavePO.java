package com.inossem.wms.common.model.auth.user.po;

import java.io.Serializable;

import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户保存对象入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "用户保存对象入参", description = "用户保存对象入参")
public class SysUserSavePO implements Serializable {

    private static final long serialVersionUID = 228437837993262961L;

    @ApiModelProperty(value = "用户对象")
    private SysUserDTO sysUserDto;

}
