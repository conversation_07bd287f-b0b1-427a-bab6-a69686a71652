package com.inossem.wms.common.model.bizdomain.threedimensional.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 仓位使用概况
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "仓位使用概况", description = "仓位使用概况 查询出参")
public class ThreeDimensionalBinStatDataVO {

    @ApiModelProperty(value = "库房编码" , example = "A01")
    String storageType;

    @ApiModelProperty(value = "总仓位数" , example = "100")
    Integer binNum;

    @ApiModelProperty(value = "空仓位数" , example = "100")
    Integer emptyBinNum;

    @ApiModelProperty(value = "库存数" , example = "100")
    BigDecimal stockNum;

}
