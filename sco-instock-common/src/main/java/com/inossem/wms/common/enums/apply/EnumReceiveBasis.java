package com.inossem.wms.common.enums.apply;

import com.inossem.wms.common.model.common.enums.apply.ReceiveBasisMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/23 9:49
 * @desc EnumReceiveBasis 紧急领用出库-领用依据枚举
 */
@Getter
@AllArgsConstructor
public enum EnumReceiveBasis {
    /**
     * 紧急抢修单编号
     */
    EMERGENCY_REPAIR_ORDER(1, "紧急抢修单编号"),
    /**
     * 应急抢修审批表编号
     */
    EMERGENCY_REPAIR_APPROVAL(2, "应急抢修审批表编号"),
    /**
     * 财务账期关闭
     */
    FINANCIAL_ACCOUNT_PERIOD_CLOSED(3, "财务账期关闭"),
    /**
     * 仓储管理系统故障
     */
    THE_WAREHOUSE_MANAGEMENT_SYSTEM_IS_FAULTY(4, "仓储管理系统故障"),
    /**
     * 领用部门负责人或部门生产值班负责人审批
     */
    LEADERSHIP_APPROVAL(5, "领用部门负责人或部门生产值班负责人审批");

    private final Integer value;

    private final String desc;


    public static List<ReceiveBasisMapVO> list;

    public static List<ReceiveBasisMapVO> toList() {
        if (list == null) {
            List<ReceiveBasisMapVO> listInner = new ArrayList<>();
            EnumReceiveBasis[] ary = EnumReceiveBasis.values();
            for (EnumReceiveBasis e : ary) {
                ReceiveBasisMapVO tempMap = new ReceiveBasisMapVO();
                tempMap.setReceiveBasis(e.getValue());
                listInner.add(tempMap);
            }
            list = listInner;
        }
        return list;
    }

    public static String getDescByValue(Integer val) {
        EnumReceiveBasis[] ary = EnumReceiveBasis.values();
        for (EnumReceiveBasis e : ary) {
            if (e.getValue().equals(val)) {
                return e.getDesc();
            }
        }
        return null;
    }
}
