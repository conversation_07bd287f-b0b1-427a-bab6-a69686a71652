package com.inossem.wms.common.model.masterdata.costcenter.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 成本中心查询参数
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("成本中心查询参数")
public class DicCostCenterSearchPO extends PageCommon {

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;
    
    @ApiModelProperty("成本中心名称")
    private String costCenterName;
} 