package com.inossem.wms.common.model.stock.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓位库存凭证Po
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "StockInsDocBinPagePO", description = "仓位库存凭证表po")
public class StockInsDocBinPagePO extends PageCommon {

    @ApiModelProperty(value = "物料编码")
    private String matCode;
    private List<String> matCodeList;

    @ApiModelProperty(value = "批次编码")
    private String batchCode;
    private List<String> batchCodeList;

    @ApiModelProperty(value = "前序单据号")
    private String preReceiptCode;

    @ApiModelProperty(value = "参考单据号")
    private String referReceiptCode;

    @ApiModelProperty(value = "凭证创建时间起始", example = "2018-12-22")
    private Date createTime;

    @ApiModelProperty(value = "凭证创建时间起始", example = "2018-12-22")
    private Date createTimeStart;

    @ApiModelProperty(value = "凭证创建时间截至", example = "2018-12-23")
    private Date createTimeEnd;

    @ApiModelProperty(value = "物料凭证")
    private String matDocCode;
}
