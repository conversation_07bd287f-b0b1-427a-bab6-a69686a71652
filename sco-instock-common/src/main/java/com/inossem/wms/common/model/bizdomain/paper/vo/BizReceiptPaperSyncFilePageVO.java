package com.inossem.wms.common.model.bizdomain.paper.vo;

import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
* 设计原图纸同步文件记录
*
* <AUTHOR>
* @since 2024-08-21
*/
@Data
@TableName("biz_receipt_paper_sync_file")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="设计原图纸同步文件记录PageVO")
public class BizReceiptPaperSyncFilePageVO {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String fileName;

    @ApiModelProperty(value = "是否已同步 1:是;0:否")
    private Integer sync;

    @ApiModelProperty(value = "同步文件夹")
    private String syncDate;

    @ApiModelProperty(value = "同步异常记录")
    private String errorLog;

    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;




}
