package com.inossem.wms.common.model.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 元数据，父子属性表
 * 
 * <AUTHOR>
 * @date 2021/03/02 19:50
 */
@TableName("metadata_self_field")
@Data
@Accessors(chain = true)
public class MetadataSelfField {

    @ApiModelProperty(value = "字段是否可编辑（0：否 1：是）" , example = "1")
    @TableField(exist = false)
    private Integer canEdit;

    @ApiModelProperty(value = "attrImport" , example = "String")
    @TableField(exist = false)
    private String attrImport;

    @ApiModelProperty(value = "字段是否于子属性或关联属性重复" , example = "0")
    @TableField(exist = false)
    private Integer isRepeat;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "元数据ID" , example = "1")
    private Long metadataId;

    @ApiModelProperty(value = "列名" , example = "String")
    private String colName;

    @ApiModelProperty(value = "元数据ID" , example = "1")
    private String attrName;

    @ApiModelProperty(value = "属性类型名称" , example = "String")
    private String attrType;

    @ApiModelProperty(value = "关联表表名" , example = "String")
    private String rlatTableName;

    @ApiModelProperty(value = "关联源属性" , example = "String")
    private String sourceAttrName;

    @ApiModelProperty(value = "关联目标属性" , example = "String")
    private String targetAttrName;

    @ApiModelProperty(value = "字段备注" , example = "String")
    private String colRemark;

    @ApiModelProperty(value = "字段长度" , example = "225")
    private Integer colLength;

    @ApiModelProperty(value = "小数点" , example = "3")
    private Integer colDecimalPlace;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

}
