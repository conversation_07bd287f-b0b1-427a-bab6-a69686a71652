package com.inossem.wms.common.model.bizdomain.output.vo;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 出库列表VO
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class  BizReceiptOutputPageVO {

    /* ********************** 扩展字段开始 *************************/

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "发料人用户")
    private String sendMatUserName;
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;
    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;
    @ApiModelProperty(value = "修改人编码" , example = "Admin")
    private String modifyUserCode;
    @ApiModelProperty(value = "修改人名称" , example = "管理员")
    private String modifyUserName;
    @ApiModelProperty(value = "凭证日期" , example = "2021-05-11")
    private Date docDate;
    @ApiModelProperty(value = "过账日期" , example = "2021-05-11")
    private Date postingDate;
    @ApiModelProperty(value = "单据移动类型，其他出库用于显示出库类型" , example = "301")
    private String moveTypeCode;
    @ApiModelProperty(value = "单据移动类型特殊库存类型，其他出库用于显示出库类型" , example = "Q")
    private String moveTypeSpecStock;
    @ApiModelProperty(value = "单据移动类型描述，其他出库用于显示出库类型描述" , example = "Q转Q(工厂)")
    private String moveTypeName;
    @ApiModelProperty(value = "单据名称" , example = "领料出库单")
    private String receiptTypeI18n;
    @ApiModelProperty(value = "单据状态名称" , example = "草稿")
    private String receiptStatusI18n;
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;
    @ApiModelProperty(value = "工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;
    @ApiModelProperty(value = "借用方式")
    private Integer borrowType;
    @ApiModelProperty(value = "借用方式描述")
    private String borrowTypeI18n;

//    @SonAttr(sonTbName = "biz_receipt_output_info", sonTbFkAttrName = "headId")
//    private BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO;


    @ApiModelProperty(value = "领料单号-废弃")
    private String receiptNum;

    @ApiModelProperty(value = "领料申请单号")
    private String preReceiptCode;

    @ApiModelProperty(value = "预留单号")
    private Long reservedOrderCode;

    @ApiModelProperty(value = "领料单描述")
    private String receiptRemark;

    @ApiModelProperty(value = "领料人")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "matReceiverCode,matReceiverName")
    private Long matReceiverId;

    @ApiModelProperty(value = "填充属性 -领料人编码" , example = "Admin")
    private String matReceiverCode;

    @ApiModelProperty(value = "填充属性 - 领料人名称" , example = "管理员")
    private String matReceiverName;


    @ApiModelProperty(value = "领用部门")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "matDeptCode,matDeptName")
    private Long matDeptId;

    @ApiModelProperty(value = "填充属性 - 领用部门编码")
    private String matDeptCode;

    @ApiModelProperty(value = "填充属性 - 领用部门描述")
    private String matDeptName;

    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

    @ApiModelProperty(value = "安装位置")
    private String installSite;

    @ApiModelProperty(value = "安装系统")
    private String installSystem;

    /* ********************** 扩展字段结束 *************************/
    @ApiModelProperty(value = "库存地点名称")
    private String locationName;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    private Long id;

    @ApiModelProperty(value = "出库单编码" , example = "CK01000400")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 销售出库411 预留出库412 采购退货413 领料出库414 报废出库415 其他出库416" , example = "411")
    private Integer receiptType;

    @ApiModelProperty(value = "10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成、70已退库" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "描述" , example = "描述")
    private String des;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "领料出库单信息id")
    @RlatAttr(rlatTableName = "biz_receipt_output_info",
            sourceAttrName = "receiptNum,receiptRemark,matReceiver,matDept,applyTime,installSite,installSystem",
            targetAttrName = "receiptNum,receiptRemark,matReceiver,matDept,applyTime,installSite,installSystem")
    private Long outInfoId;

    @ApiModelProperty(value = "移动类型id")
    private Long moveTypeId;

    @ApiModelProperty(value = "实际领料人姓名")
    private String actualReceiverName;
    
    @ApiModelProperty(value = "领用单号")
    private String applyReceiptCode;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "sendMatUserName")
    @ApiModelProperty(value = "发料人用户id")
    private Long sendMatUserId;

    @ApiModelProperty(value = "提交人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "填充属性 -提交人编码" , example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称" , example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "领用类型 1 手工领用 2需求计划领用" )
    private Integer receiveType;

    @ApiModelProperty(value = "领用类型 1 手工领用 2需求计划领用" )
    private String receiveTypeI18n;

    @ApiModelProperty(value = "是否精准预留（0:否；1:是）")
    private Integer isExact;

    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String island;

    @ApiModelProperty(value = "出库日期")
    private Date outTime;
}
