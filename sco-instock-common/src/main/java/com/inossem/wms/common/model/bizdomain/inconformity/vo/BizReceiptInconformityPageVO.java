package com.inossem.wms.common.model.bizdomain.inconformity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 不符合项分页查询出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="不符合项分页查询出参", description="不符合项分页查询出参")
public class BizReceiptInconformityPageVO implements Serializable {

    private static final long serialVersionUID = -6010478944638627346L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态描述")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "差异类型")
    private Integer differentType;

    @ApiModelProperty(value = "差异类型描述")
    private String differentTypeI18n;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "需求人编码", example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "需求人描述", example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "采购订单号")
    private String referReceiptCode;

    @ApiModelProperty(value = "采购员编码")
    private String purchaserCode;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty(value = "采购负责人")
    private String purchaserManagerName;

    @ApiModelProperty(value = "质检会签单id")
    private Long inspectId;

    @ApiModelProperty(value = "质检会签单号")
    private String inspectReceiptCode;

    @ApiModelProperty(value = "通知单号id")
    private Long preId;

    @ApiModelProperty(value = "通知单号")
    private String preReceiptCode;

    private String contractCode;

    private String purchaseCode;
}
