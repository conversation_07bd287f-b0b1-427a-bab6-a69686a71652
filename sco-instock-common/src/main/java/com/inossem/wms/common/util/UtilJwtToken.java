package com.inossem.wms.common.util;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.CompressionCodecs;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

/**
 * <AUTHOR>
 * @date 2021/2/27 16:57
 *
 */
@Component
public class UtilJwtToken {

    /**
     * 创建token
     *
     * @param userCode 用户编码
     * @param userInfo 用户对象字符串
     * @return java.lang.String
     * @date 2021/2/27 17:10
     * <AUTHOR>
     */
    public static String createToken(String userCode, String userKey, String userInfo, Long tokenExpiration, String tokenSignKey) {
        return Jwts.builder().setSubject(userCode).claim(Const.USER_KEY, userKey).claim(Const.USER, userInfo)
            .setExpiration(new Date(System.currentTimeMillis() + tokenExpiration * 60000)).signWith(SignatureAlgorithm.HS512, tokenSignKey)
            .compressWith(CompressionCodecs.GZIP).compact();
    }

    /**
     * 创建刷新token
     * 
     * @param userName 用户编码
     * @param tokenExpiration 过期时间
     * @param tokenSignKey 密钥
     * @return String
     */
    public static String createToken(String userName, Long tokenExpiration, String tokenSignKey) {
        return Jwts.builder().setSubject(userName).setExpiration(new Date(System.currentTimeMillis() + tokenExpiration * 60000))
            .signWith(SignatureAlgorithm.HS512, tokenSignKey).compressWith(CompressionCodecs.GZIP).compact();
    }

    public static Claims getTokenBody(String token, String tokenSignKey) {
        return Jwts.parser().setSigningKey(tokenSignKey).parseClaimsJws(token).getBody();
    }

    /**
     * 获取请求头中的token
     *
     * @param request 请求体
     * @return java.lang.String
     * @date 2021/3/16 10:37
     * <AUTHOR>
     */
    public static String getToken(HttpServletRequest request, String header) {
        // 获取Token字符串，token 置于 header 里
        String token = request.getHeader(header);
        if (!StringUtils.hasText(token)) {
            token = request.getParameter(header);
        }
        if (token == null || "".equals(token.trim())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_UNAUTH);
        }
        return token;
    }

}
