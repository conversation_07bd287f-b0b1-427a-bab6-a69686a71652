package com.inossem.wms.common.model.approval.dto;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 通过/拒绝审批传输对象
 * 
 * <AUTHOR>
 * @date 2020/7/28 15:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "通过/拒绝审批传输对象", description = "通过/拒绝审批传输对象")
public class CompleteTaskDTO extends BaseDTO {
    @ApiModelProperty(value = "任务ID", example = "1", required = true)
    private String taskId;

    @ApiModelProperty(value = "流程变量", required = true)
    private Map<String, Object> variables;

    @ApiModelProperty(value = "批注" , example = "批注")
    private String comment;

    @ApiModelProperty(value = "申领人", example = "Admin", required = true)
    private String claimUser;

    @ApiModelProperty(value = "驳回到哪个审批节点", example = "EnumApprovalNode中的值")
    private String rejectApprovalNode;

    @ApiModelProperty(value = "是否驳回审批通过后跳转回当前节点（1：驳回的审批再次通过后直接跳转回当前审批节点，0或空或其他：驳回的审批通过后再次按照流程图执行审批）")
    private Integer isRejectApprovalJumpCurrenNode;

    @ApiModelProperty(value = "审批过程中上传的附件id列表")
    private List<Long> fileIdList;

    @ApiModelProperty(value = "审批过程中上传的图片id列表")
    private List<Long> imgIdList;

    @ApiModelProperty(value = "单据id")
    private Long receiptId;
}
