package com.inossem.wms.common.model.bizdomain.transport.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 转储单明细表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptTransportItem对象", description = "转储单明细表")
public class BizReceiptTransportExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "转储单号")
    @ApiModelProperty(value = "转储单号", example = "ZC01000318")
    private String receiptCode;

    @ExcelIgnore
    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    private Long createUserId;

    @ExcelProperty(value = "创建人")
    @ApiModelProperty(value = "创建人名称", example = "管理员")
    private String createUserName;

    @ExcelProperty(value = "创建时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ExcelProperty(value = "过账日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ApiModelProperty(value = "过账日期", example = "2021-05-10")
    private Date postingDate;

    @ExcelProperty(value = "凭证日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ApiModelProperty(value = "凭证日期", example = "2021-05-10")
    private Date docDate;

    @ExcelProperty(value = "物料凭证")
    @ApiModelProperty(value = "物料凭证", example = "51111111")
    private String matDocCode;

    @ExcelProperty(value = "整理人")
    @ApiModelProperty(value = "整理人 - 暂时不知道", example = "51111111")
    private String person;

    @ExcelProperty(value = "转储单描述")
    @ApiModelProperty(value = "转储单描述")
    private String des;

    @ExcelIgnore
    @ApiModelProperty(value = "发出物料id", example = "1")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "outputMatCode,outputMatName,matNameEn")
    private Long outputMatId;

    @ExcelProperty(value = "物料编码")
    @ApiModelProperty(value = "发出物料编码", example = "M001005")
    private String outputMatCode;

    @ExcelProperty(value = "物料描述")
    @ApiModelProperty(value = "发出物料名称", example = "物料描述001003")
    private String outputMatName;

    @ExcelProperty(value = "物料描述英文")
    @ApiModelProperty(value = "物料描述英文", example = "物料描述001003")
    private String matNameEn;

    @ExcelIgnore
    @ApiModelProperty(value = "发出物料单位id", example = "7")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "outputUnitCode,outputUnitName")
    private Long outputUnitId;

    @ExcelProperty(value = "计量单位编码")
    @ApiModelProperty(value = "发出单位编码", example = "M3")
    private String outputUnitCode;

    @ExcelProperty(value = "计量单位么描述")
    @ApiModelProperty(value = "发出单位名称", example = "立方米")
    private String outputUnitName;

    @ExcelIgnore
    @ApiModelProperty(value = "移动类型id", example = "3010")
    @RlatAttr(rlatTableName = "dic_move_type", sourceAttrName = "moveTypeCode", targetAttrName = "moveTypeCode")
    private Long moveTypeId;

    @ExcelProperty(value = "移动类型")
    @ApiModelProperty(value = "移动类型", example = "301")
    private String moveTypeCode;

    @ExcelIgnore
    @ApiModelProperty(value = "发出库存地点id", example = "145729754562561")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "outputLocationCode,outputLocationName")
    private Long outputLocationId;

    @ExcelProperty(value = "发出库存地点编码")
    @ApiModelProperty(value = "发出库存地点编码", example = "2800")
    private String outputLocationCode;

    @ExcelProperty(value = "发出库存地点描述")
    @ApiModelProperty(value = "发出库存地点名称", example = "英诺森004")
    private String outputLocationName;

    @ExcelIgnore
    @ApiModelProperty(value = "发出仓位id", example = "155336845623297")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "outputBinCode")
    private Long outputBinId;

    @ExcelProperty(value = "发出仓位")
    @ApiModelProperty(value = "发出仓位", example = "00")
    private String outputBinCode;

    @ExcelIgnore
    @ApiModelProperty(value = "发出批次id", example = "1")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "batchCode", targetAttrName = "outputBatchCode")
    private Long outputBatchId;

    @ExcelProperty(value = "发出批次")
    @ApiModelProperty(value = "发出批次", example = "00")
    private String outputBatchCode;

    @ExcelProperty(value = "转储数量")
    @ApiModelProperty(value = "转储数量", example = "100")
    private BigDecimal qty;

    @ExcelIgnore
    @ApiModelProperty(value = "接收库存地点id", example = "145725436526593")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "inputLocationCode,inputLocationName")
    private Long inputLocationId;

    @ExcelProperty(value = "接收库存地点编码")
    @ApiModelProperty(value = "接收库存地点编码", example = "2800")
    private String inputLocationCode;

    @ExcelProperty(value = "接收库存地点描述")
    @ApiModelProperty(value = "接收库存地点名称", example = "英诺森004")
    private String inputLocationName;

    @ExcelIgnore
    @ApiModelProperty(value = "单据状态名称", example = "草稿")
    private Integer receiptStatus;

    @ExcelProperty(value = "单据状态")
    @ApiModelProperty(value = "单据状态", example = "草稿")
    private String receiptStatusI18n;

}
