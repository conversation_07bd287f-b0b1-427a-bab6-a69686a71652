package com.inossem.wms.common.model.bizdomain.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/9/6 9:14
 * @desc StockCompareVO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存对比结果报表", description = "库存对比结果报表")
public class StockCompareVO {

    @ApiModelProperty(value = "工厂", example = "J047")
    @ExcelProperty(value = "工厂")
    private String ftyCode;

    @ApiModelProperty(value = "物料编码", example = "102012")
    @ExcelProperty(value = "物料编号")
    private String matCode;

    @ApiModelProperty(value = "物料描述", example = "物料描述001003")
    @ExcelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "单位", example = "M3")
    @ExcelProperty(value = "单位")
    private String unitCode;

    @ApiModelProperty(value = "单位", example = "M3")
    @ExcelIgnore
    @ExcelProperty(value = "单位")
    private String unitName;
    
    @ApiModelProperty(value = "sap库存数量", example = "100")
    @ExcelProperty(value = "SAP数量")
    private BigDecimal sapQty;

    @ApiModelProperty(value = "wms库存数量", example = "20")
    @ExcelProperty(value = "WMS数量")
    private BigDecimal wmsQty;


    @ApiModelProperty(value = "差异", example = "80")
    @ExcelProperty(value = "差异")
    private BigDecimal diffQty;


}
