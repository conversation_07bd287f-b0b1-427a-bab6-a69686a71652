package com.inossem.wms.common.model.bizdomain.contract.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "合同报表列表展示VO", description = "合同列表展示VO")
public class BizReceiptContractLedgerListExportVO {

    @ExcelProperty(value = "合同编号")
    private String receiptCode;

    @ExcelProperty(value = "合同名称")
    private String contractName;

    @ExcelIgnore
    private Integer receiptType;
    @ExcelProperty(value = "合同类型")
    private String receiptTypeI18n;
    @ExcelIgnore
    private Integer contractSubType;
    @ExcelIgnore
    private String contractSubTypeI18n;

    @ExcelIgnore
    private Integer firstParty;
    @ExcelProperty(value = "甲方")
    private String firstPartyI18n;

    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    @ExcelProperty(value = "供应商代码")
    private String supplierCode;

    @ExcelProperty(value = "签订日期")
    private Date contractSignDate;

    @ExcelIgnore
    private Integer currency;
    @ExcelProperty(value = "币种")
    private String currencyI18n;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelIgnore
    private Integer receiptStatus;
    @ExcelProperty(value = "合同状态")
    private String receiptStatusI18n;

    @ExcelProperty(value = "合同创建人")
    private String createUserName;

    @ExcelProperty(value = "合同创建时间")
    private Date createTime;

    @ExcelProperty(value = "送货单号")
    private String deliveryReceiptCode;

    @ExcelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ExcelProperty(value = "总价(不含税)")
    private BigDecimal noTaxAmount;

}
