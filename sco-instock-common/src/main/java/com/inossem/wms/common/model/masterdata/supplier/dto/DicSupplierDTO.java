package com.inossem.wms.common.model.masterdata.supplier.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商主数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicSupplier对象", description = "供应商主数据")
@TableName("dic_supplier")
public class DicSupplierDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 - 单据附件 - 营业执照")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "dic_supplier_user_rel", sonTbFkAttrName = "supplierId")
    @ApiModelProperty(value = "填充属性 - 账号信息")
    private List<DicSupplierUserRelDTO> userList;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "主u账号")
    private String uAccount;

    @ApiModelProperty(value = "法人")
    private String legalPerson;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "开户行名称")
    private String bankName;

    @ApiModelProperty(value = "账户")
    private String bankAccount;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "社会信用代码/纳税人识别码")
    private String creditCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "逻辑删除字段")
    @TableLogic(delval = "id")
    private Long isDelete;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "修改人编码")
    private String modifyUserCode;

    @ApiModelProperty(value = "修改人")
    private String modifyUserName;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "停用,启用,待审批,已拒绝")
    private Integer receiptStatus;
    private String receiptStatusI18n;

    @ApiModelProperty(value = "供应商类型")
    private Integer supplierType;
    private String supplierTypeI18n;

    @ApiModelProperty(value = "供应商分类")
    private Integer supplierClass;
    private String supplierClassI18n;

    @ApiModelProperty(value = "供应商评定")
    private String supplierLevel;

    @ApiModelProperty(value = "供应商属性(多选)")
    private String supplierAttr;

    @ApiModelProperty(value = "物料等级(多选)")
    private String matLevel;

    @ApiModelProperty(value = "业务范围(多选)")
    private String scope;

    @ApiModelProperty(value = "注册资金")
    private BigDecimal registeredCapital;

    @ApiModelProperty(value = "成立时间")
    private Date establishTime;

    @ApiModelProperty(value = "引入时间")
    private Date introductionTime;

    @ApiModelProperty(value = "有效开始时间")
    private Date startTime;

    @ApiModelProperty(value = "有效截止时间")
    private Date endTime;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "地区")
    private String area;

    @ApiModelProperty(value = "是否内贸代理商【1是0否】")
    private Integer isInlandAgent;

    @ApiModelProperty(value = "收款单位开户行")
    private String payeeBankName;

    @ApiModelProperty(value = "收款单位账号")
    private String payeeAccountNumber;

}
