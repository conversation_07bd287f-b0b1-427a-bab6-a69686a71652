package com.inossem.wms.common.model.bizdomain.task.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 作业单 item
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

@TableName("biz_receipt_task_item")
@ApiModel(value = "BizReceiptTaskItem对象", description = "")
public class BizReceiptTaskItem implements Serializable {

    private static final long serialVersionUID = -8987739160630413900L;

    @ApiModelProperty(value = "保养备注")
    private String mainRequirement;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "作业单行项目号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "作业申请head表id" , example = "155164919005185")
    private Long taskReqHeadId;

    @ApiModelProperty(value = "作业申请item表id" , example = "152044279234563")
    private Long taskReqItemId;

    @ApiModelProperty(value = "原存储类型id" , example = "152218403667969")
    private Long sourceTypeId;

    @ApiModelProperty(value = "原仓位id" , example = "152218489651201")
    private Long sourceBinId;

    @ApiModelProperty(value = "原存储单元id" , example = "152758218981377")
    private Long sourceCellId;

    @ApiModelProperty(value = "目标储存类型id" , example = "152407786979334")
    private Long targetTypeId;

    @ApiModelProperty(value = "目标仓位id" , example = "152218758087657")
    private Long targetBinId;

    @ApiModelProperty(value = "目标存储单元id" , example = "152758218981377")
    private Long targetCellId;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "拣选" , example = "10")
    private BigDecimal pickQty;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
    private Long matId;
    private String specStockCode;

    @ApiModelProperty(value = "物料组id" )
    private Long matGroupId;

    @ApiModelProperty(value = "提交时间" )
    private Date submitTime;

    @ApiModelProperty(value = "提交人id" )
    private Long submitUserId;

}
