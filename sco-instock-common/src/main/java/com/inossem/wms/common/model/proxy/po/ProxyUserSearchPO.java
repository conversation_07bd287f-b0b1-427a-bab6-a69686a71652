package com.inossem.wms.common.model.proxy.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
* @Author: SCDM-Tools-constructor
* @Date:   2023-07-14
*/

@Data
@TableName("biz_proxy_user")
@ApiModel(value = "ProxyUser查询PO对象", description = "查询PO")
public class ProxyUserSearchPO extends PageCommon {

	private Date startDate;

	private Date endDate;

	private String createUserName;

	private String delUserName;

}
