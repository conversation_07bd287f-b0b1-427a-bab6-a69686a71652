package com.inossem.wms.common.model.auth.user.vo;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户分页对象出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "用户分页对象出参", description = "用户分页对象出参")
public class SysUserPageVO {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/

    @ApiModelProperty(value = "部门id", example = "1100", required = true)
    private String deptCode;

    @ApiModelProperty(value = "部门名称", example = "采购部", required = true)
    private String deptName;

    @ApiModelProperty(value = "公司编码" , example = "1000", required = false)
    private String corpCode;

    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;

    @ApiModelProperty(value = "用户类型名称" , example = "类型")
    private String userTypeI18n;

    @ApiModelProperty(value = "是否被锁定描述" , example = "是")
    private String isLockedI18n;

    @ApiModelProperty(value = "冻结描述" , example = "已冻结")
    private String isFreezeI18n;

    @ApiModelProperty(value = "是否单点登录描述" , example = "否")
    private String isSynI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "用户ID", name = "userId", example = "1")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户编码" , example = "Admin")
    private String userCode;

    @ApiModelProperty(value = "用户名称" , example = "管理员")
    private String userName;

    @ApiModelProperty(value = "密码" , example = "123456")
    private String password;

    @ApiModelProperty(value = "10内部用户， 20外部供应商，30外部客户用户，40外部业务用户，50接口用户" , example = "10")
    private Integer userType;

    @ApiModelProperty(value = "是否被锁定 0-FALSE 1-true" , example = "0")
    private Integer isLocked;

    @ApiModelProperty(value = "0-未冻结，1-已冻结" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "上次登录时间" , example = "2021-05-10")
    private Date lastLogin;

    @ApiModelProperty(value = "登录错误重试次数" , example = "2")
    private Integer failAttempts;

    @ApiModelProperty(value = "上次登录错误时间试次数" , example = "2021-05-10")
    private Date lastFailAttempt;

    @ApiModelProperty(value = "用户有效期始" , example = "2021-05-10")
    private Date validityBeginDate;

    @ApiModelProperty(value = "用户有效期止" , example = "2022-05-10")
    private Date validityEndDate;

    @ApiModelProperty(value = "组织ID" , example = "1")
    private String orgCode;

    @ApiModelProperty(value = "员工ID" , example = "1")
    private String employeeCode;

    @ApiModelProperty(value = "岗位" , example = "1")
    private String userPost;

    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司id" , example = "1", required = false)
    private Long corpId;

    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    @ApiModelProperty(value = "部门id", example = "100", required = true)
    private Long deptId;

    @ApiModelProperty(value = "手机号" , example = "1300000000")
    private String phoneNumber;

    @ApiModelProperty(value = "邮箱" , example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "是否单点登录【1是，0否】" , example = "1")
    private Integer isSyn;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "上次修改密码时间" , example = "2021-05-10")
    private Date lastPasswordModifyTime;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
}
