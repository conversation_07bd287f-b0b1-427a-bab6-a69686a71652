package com.inossem.wms.common.model.bizbasis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 单据附件表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizCommonReceiptAttachment对象", description = "单据附件表")
@TableName("biz_common_receipt_attachment")
public class BizCommonReceiptAttachment implements Serializable {

    private static final long serialVersionUID = 1L;


    /* ********************** 扩展字段开始 *************************/

    @TableField(exist = false)
    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @TableField(exist = false)
    @ApiModelProperty(value = "填充属性 - 文件名称" , example = "文件1")
    private String fileName;

    @TableField(exist = false)
    @ApiModelProperty(value = "填充属性 - 文件引用id", example = "文件1")
    private String fileRefId;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "物料id", example = "151561399500801", required = false)
    private Long matId;

    @ApiModelProperty(value = "批次信息id", example = "151561399500801", required = false)
    private Long batchId;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = false)
    private Long receiptHeadId;

    @ApiModelProperty(value = "itemId", example = "151561399500801", required = false)
    private Long receiptItemId;

    @ApiModelProperty(value = "文件编码" , example = "145928744927233")
    @RlatAttr(rlatTableName = "biz_common_file", sourceAttrName = "fileName,fileRefId", targetAttrName = "fileName,fileRefId")
    private Long fileId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
