package com.inossem.wms.common.model.metadata.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * PrototypeInfo.yml文件中的模块信息
 * 
 * <AUTHOR>
 * @date 2021/03/16 09:26
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PrototypeInfoYmlFileModuleDTO", description = "PrototypeInfo.yml文件中的模块信息")
public class PrototypeInfoYmlFileModuleDTO {

    @ApiModelProperty(value = "模块名" , example = "String")
    private String moduleName;

    @ApiModelProperty(value = "模块关键字" , example = "String")
    private String moduleKeyword;

    @ApiModelProperty(value = "模块描述" , example = "String")
    private String moduleDesc;

    @ApiModelProperty(value = "标识模块内文件是否可以被拆解成代码段（Yes：可以被拆解，No：不可以被拆解）" , example = "Yes")
    private String isCanSplit;

    @ApiModelProperty(value = "文件列表" , example = "String")
    private List<String> fileList;

    @ApiModelProperty(value = "子模块信息")
    private List<PrototypeInfoYmlFileModuleDTO> subModuleList;
}
