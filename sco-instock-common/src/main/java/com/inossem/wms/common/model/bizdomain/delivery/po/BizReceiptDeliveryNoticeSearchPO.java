package com.inossem.wms.common.model.bizdomain.delivery.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 送货通知单列表查询po
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "送货通知单列表查询对象", description = "送货通知单列表查询对象")
public class BizReceiptDeliveryNoticeSearchPO extends PageCommon {

    @ApiModelProperty(value = "送货通知单号" , example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据号" , example = "4500000060")
    private String preReceiptCode;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "状态列表" , example = "10,20")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据状态" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "采购订单号" , example = "4500000060")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "查询起始时间", example = "2018-11-09")
    private Date startTime;

    @ApiModelProperty(value = "查询截至时间", example = "2018-11-29")
    private Date endTime;

    @ApiModelProperty(value = "创建人")
    private String userName;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "需求人")
    private String applyUserName;

    // 到货通知描述
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "采购负责人")
    private String purchasePersonName;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty(value = "发货类型")
    private Integer sendType;

    @ApiModelProperty(value = "发货类型列表")   
    private List<Integer> sendTypeList;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;   

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "送货通知单号", example = "SH01000006")
    private String deliveryCode;

    @ApiModelProperty(value = "描述")
    private String description;

}
