package com.inossem.wms.common.model.bizdomain.input.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 入库单列表查询po
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "入库单列表查询对象", description = "入库单列表查询对象")
public class BizReceiptInputSearchPO extends PageCommon {



	/* ********************** 高级搜索字段开始 *************************/
	private String mainMatId;
	private String mainMatCode;
	private String mainMatName;
	private String matId;
	//private String matCode;//子设备编码(CTxxxxxxxxx)
	//private String matName;//子设备描述
	private String extend20;//物资编码（广核物料编码）
	private String extend66;//合同名称
	private String extend65;//合同号
	private String functionalLocationCode;//功能位置码
	private String extend2;//采购包（LOT包号）
	private String extend24;//规格型号
	private String extend29;//up码
	private String extend30;//up码类型
	private String remark;//备注
	/* ********************** 高级搜索字段结束 *************************/

	/*########################################################### 工器具字段开始 ###########################################################*/

	/**
	 * 批次号 对应的工具编码
	 */
	@ApiModelProperty(value = "工具编码" , example = "RK0001000633")
	private String batchCode;

	@ApiModelProperty(value = "工具编码" , example = "RK0001000633")
	private String toolCode;

	@ApiModelProperty(value = "物料描述" , example = "", required = false )
	private String matName;

	@ApiModelProperty(value = "物料编码" , example = "PZ21081603", required = false )
	private String matCode;

	@ApiModelProperty(value = "子设备物料编码")
	private String childMatCode;

	@ApiModelProperty(value = "子设备物料id")
	private Long childMatId;

	@ApiModelProperty(value = "基础单据号" , example = "RK0001000633")
	private String referReceiptCode;

	@ApiModelProperty(value = "是否开启到期预警")
	private Boolean isExpireWarn;

	@ApiModelProperty(value = "验收单号" , example = "RK0001000633")
	private String inspectCode;

	/*########################################################### 工器具字段结束 ###########################################################*/


	@ApiModelProperty(value = "入库单号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

	@ApiModelProperty(value = "前线单据号" , example = "RK0001000633")
	private String preReceiptCode;

    @ApiModelProperty(value = "状态列表" , example = "10,20")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

	@ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
	private Long id;

	@ApiModelProperty(value = "物料凭证号" , example = "PZ21081603", required = false )
	private String matDocCode;

	@ApiModelProperty(value = "单据状态" , example = "10", required = false )
	private Integer receiptStatus;

	@ApiModelProperty(value = "凭证时间" , example = "2021-05-01", required = false)
	private Date docDate;

	@ApiModelProperty(value = "凭证创建时间-开始" , example = "2021-05-01", required = false)
	private Date postCreateTime;

	@ApiModelProperty(value = "凭证创建时间-结束" , example = "2021-05-01", required = false)
	private Date postEndTime;

	@ApiModelProperty(value = "创建人姓名", example = "管理员")
	private String createUserName;

	@ApiModelProperty(value = "创建人姓名-数据库字段名映射", example = "管理员")
	private String userName;

	@ApiModelProperty(value = "前续单据head主键" , example = "111")
	private Long preReceiptHeadId;

	@ApiModelProperty(value = "到货通知冲销凭证104")
	private String deliveryWriteOffDoc;

	@ApiModelProperty(value = "验收入库冲销凭证106")
	private String inputWriteOffDoc;

	@ApiModelProperty(value = "工具编码", example = "工具001")
	private String toolId;

	@ApiModelProperty(value = "开始时间" , example = "2021-05-01", required = false)
	private Date startTime;

	@ApiModelProperty(value = "结束时间" , example = "2021-05-01", required = false)
	private Date endTime;

	@ApiModelProperty(value = "质检会签单号")
	private String inspectReceiptCode;

	@ApiModelProperty(value = "不符合项处置单号")
	private String inconformityReceiptCode;

	@ApiModelProperty(value = "采购订单")
	private String purchaseReceiptCode;
	private String purchaseCode;
	private String deliveryNoticeDescribe;

	@ApiModelProperty(value = "库存地点id", example = "145725436526593")
	private Long locationId;

	@ApiModelProperty(value = "库存地点id集合")
	private List<Long> locationIdList;

	@ApiModelProperty(value = "单据描述" , example = "RK0001000633")
	private String des;

	@ApiModelProperty(value = "合同id")
	private Long contractId;

	@ApiModelProperty(value = "发票号")
	private String invoiceNo;
}
