package com.inossem.wms.common.model.bizdomain.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 合同行项目
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptContractItem对象", description="合同行项目")
@TableName("biz_receipt_contract_item")
public class BizReceiptContractItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "行号")
    private String rid;

    @ApiModelProperty(value = "合同头表ID")
    private Long headId;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前序单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据头id")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前序单据行项目id")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前序单据编码,采购申请")
    private String preReceiptCode;

    @ApiModelProperty(value = "前序单据行号，采购申请")
    private String preReceiptRid;

    @ApiModelProperty(value = "物料ID", notes = "必填,关联物料主数据")
    private Long matId;

    @ApiModelProperty(value = "计量单位ID", notes = "必填,关联单位主数据")
    private Long unitId;

    @ApiModelProperty(value = "物料组ID", notes = "必填,关联物料组主数据")
    private Long matGroupId;

    @ApiModelProperty(value = "合同数量/框架协议数量", notes = "必填")
    private BigDecimal qty;

    @ApiModelProperty(value = "合同未清数量")
    private BigDecimal unContractQty;

    @ApiModelProperty(value = "采购申请数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "采购未清数量")
    private BigDecimal unReceiveQty;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "税码", notes = "必填,参考EnumContractTaxRate:10-0%进项税中国,20-17%进项税中国,30-13%进项税中国,40-13%进项税巴基斯坦,50-15%进项税巴基斯坦,60-18%进项税巴基斯坦")
    private Integer taxCode;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "不含税单价/框架协议单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "含税单价", notes = "必填")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "不含税总价")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "含税总价")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "工厂ID")
    private Long factoryId;

    @ApiModelProperty(value = "需求计划单ID")
    private Long demandPlanHeadId;

    @ApiModelProperty(value = "需求计划行id")
    private Long demandPlanItemId;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求人ID")
    private Long demandPersonId;

    @ApiModelProperty(value = "需求部门ID")
    private Long demandDeptId;

    @ApiModelProperty(value = "已收货数量")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "已发货数量")
    private BigDecimal sendQty;

    @ApiModelProperty(value = "待验收数量")
    private BigDecimal unInspectQty;

    @ApiModelProperty(value = "已入库数量")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "验收不合格数量")
    private BigDecimal inspectUnqualifiedQty;

    @ApiModelProperty(value = "已退货数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "备注")
    private String itemRemark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @ApiModelProperty(value = "修改人编码")
    private String modifyUserCode;

    @ApiModelProperty(value = "修改人名称")
    private String modifyUserName;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;

    @ApiModelProperty(value = "资产卡片编码")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片名称")
    private String assetCardDesc;

    @ApiModelProperty(value = "品名")
    private String productName;

    @ApiModelProperty(value = "WBS编号")
    private String wbsNo;

    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    @ApiModelProperty(value = "资产卡片id")
    private Long assetCardId;

    @ApiModelProperty(value = "资产卡片子编码")
    private String assetCardSubCode;

    @ApiModelProperty(value = "WBS id")
    private Long wbsId;

    @ApiModelProperty(value = "成本中心id")
    private Long costCenterId;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "年份")
    private Integer budgetYear;

    @ApiModelProperty(value = "预算分类")
    private String budgetClass;

    @ApiModelProperty(value = "预算科目")
    private String budgetAccount;

    @ApiModelProperty(value = "门到门送货数量")
    private BigDecimal d2dDeliveryQty;

    @ApiModelProperty(value = "门到门未清数量")
    private BigDecimal unD2dDeliveryQty;
    
}
