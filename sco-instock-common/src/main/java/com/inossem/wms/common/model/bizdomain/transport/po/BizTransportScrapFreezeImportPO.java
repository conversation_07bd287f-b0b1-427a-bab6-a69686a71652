package com.inossem.wms.common.model.bizdomain.transport.po;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "报废冻结申请导入", description = "闲置物资申请导入数据传输对象")
public class BizTransportScrapFreezeImportPO {

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    @ExcelProperty(value = "工厂编码", index =0)
    private String ftyCode;

    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    @ExcelProperty(value = "库存地点编码", index =1)
    private String locationCode;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码", index =2)
    private String matCode;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码", index =3)
    private String typeCode;

    @ApiModelProperty(value = "仓位编码" , example = "M001005")
    @ExcelProperty(value = "仓位编码", index =4)
    private String binCode;

    @ApiModelProperty(value = "批次id" , example = "5")
    @ExcelProperty(value = "批次id", index =5)
    private Long batchId;

    @ApiModelProperty(value = "冻结数量" , example = "5")
    @ExcelProperty(value = "冻结数量", index =6)
    private BigDecimal freezeQty;

}
