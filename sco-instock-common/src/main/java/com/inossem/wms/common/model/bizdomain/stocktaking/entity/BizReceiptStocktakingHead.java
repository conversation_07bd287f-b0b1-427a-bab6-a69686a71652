package com.inossem.wms.common.model.bizdomain.stocktaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 库存盘点抬头表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptStocktakingHead对象", description = "库存盘点抬头表")
@TableName("biz_receipt_stocktaking_head")
public class BizReceiptStocktakingHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "计划日期-开始", example = "2021-05-11")
    private Date beginDate;

    @ApiModelProperty(value = "计划日期-结束", example = "2021-05-12")
    private Date endDate;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    private String locationId;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private String whId;

    @ApiModelProperty(value = "盘点方式：1-明盘，2-盲盘", example = "1")
    private Integer stocktakingMode;

    @ApiModelProperty(value = "盘点人", example = "1")
    private Long stocktakingUserId;

    @ApiModelProperty(value = "盘点日期", example = "2021-05-11")
    private Date stocktakingDate;

    @ApiModelProperty(value = "盘点类型：0-首盘，1-复盘", example = "0")
    private Integer isReplay;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "动态盘点类型：0-非动态，1-动态", example = "0")
    private Integer isAuto;

    @ApiModelProperty(value = "复盘单据主键id" , example = "159843409264782", required = false)
    private Long upHeadId;

    @ApiModelProperty(value = "按物料1，仓位0" , example = "0")
    private Integer isAppointMat;

    @ApiModelProperty(value = "是否电子秤盘点 0-不是 1-是", example = "0")
    private Integer isElectronicScale;

    @ApiModelProperty(value = "盘点模式【1：专项盘点；2：计划盘点；3：交易盘点】")
    private Integer stocktakingType;

    @ApiModelProperty(value = "源盘点单head_id，多次盘点按业务要求均使用首盘单head_id")
    private Long originReceiptHeadId;

    @ApiModelProperty(value = "源盘点单编码")
    private String originReceiptCode;

    @ApiModelProperty(value = "盘点查询的库存状态")
    private Integer isSearchFreeze;

    @ApiModelProperty(value = "关联盘点凭证")
    private Long stocktakingDocHeadId;

    @ApiModelProperty(value = "首盘盘点单head_id")
    private Long firstReceiptHeadId;

    @ApiModelProperty(value = "最后一次复盘盘点单head_id")
    private Long lastReceiptHeadId;

    @ApiModelProperty(value = "抽盘比率")
    private BigDecimal spotCheckRatio;
}
