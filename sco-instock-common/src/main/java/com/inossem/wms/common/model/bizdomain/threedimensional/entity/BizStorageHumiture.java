package com.inossem.wms.common.model.bizdomain.threedimensional.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 存储类型温湿度关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizStorageHumiture对象", description = "存储类型温湿度关系表")
@TableName("biz_storage_humiture")
public class BizStorageHumiture implements Serializable {

    private static final long serialVersionUID = 134534524234234L;

    @TableId(value = "device_id", type = IdType.INPUT)
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "存储类型")
    private Long typeId;

    @ApiModelProperty(value = "温度")
    private Double temperature;

    @ApiModelProperty(value = "湿度")
    private Double humidity;


}
