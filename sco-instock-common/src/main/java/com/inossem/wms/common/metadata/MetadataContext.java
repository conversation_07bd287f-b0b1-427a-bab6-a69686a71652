package com.inossem.wms.common.metadata;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Map;

import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.util.UtilMetadata;
import com.inossem.wms.common.util.UtilString;

/**
 * <AUTHOR>
 * @date 2021/2/1 14:33
 */
@Component
public class MetadataContext implements ApplicationRunner {

    /**
     * 表名与mybatisPlus的service对应的Map key : tableName value : service实体
     */
    private static final Map<String, BaseDataWrap> mPlusServiceMap = new Hashtable<>();
    /**
     * 表名与entity的class对应的Map key : tableName value : entity的class
     */
    private static final Map<String, Class> metadataEntityClassMap = new Hashtable<>();

    private static final Map<String, Class> classesMap = new Hashtable<>();
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 根据表名取得对应的mybatisPlus的service
     *
     * @param tableName 表名
     * @return
     */
    public static BaseDataWrap getMplusService(String tableName) {
        if (mPlusServiceMap.containsKey(tableName)) {
            return mPlusServiceMap.get(tableName);
        } else {
            return null;
        }
    }

    /**
     * 根据entityClass取得对应的mybatisPlus的service
     *
     * @param entityClass entity的class
     * @return
     */
    public static IService getMplusService(Class entityClass) {
        // 该entity对应的数据库表名
        String tableName = UtilMetadata.getTableName(entityClass);

        return MetadataContext.getMplusService(tableName);
    }

    /**
     * 根据表名取得对应的entity的class
     *
     * @param tableName 表名
     * @return
     */
    public static Class getEntityClass(String tableName) {
        if (metadataEntityClassMap.containsKey(tableName)) {
            return metadataEntityClassMap.get(tableName);
        } else {
            return null;
        }
    }

    public static void loadClassByLoader(ClassLoader load) {
        Enumeration<URL> urls = null;
        try {
            urls = load.getResources("");
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FILE_NOT_FOUND_EXCEPTION);
        }

        while (urls.hasMoreElements()) {
            URL url = urls.nextElement();
            // 文件类型（其实是文件夹）
            if ("file".equals(url.getProtocol())) {
                loadClassByPath(null, url.getPath(), load);
            }
        }
    }

    public static void loadClassByPath(String root, String path, ClassLoader load) {
        File f = new File(path);
        if (root == null) {
            root = f.getPath();
        }
        // 判断是否是class文件
        if (f.isFile() && f.getName().matches("^.*\\.class$")) {
            try {
                String classPath = f.getPath();
                // 截取出className 将路径分割符替换为.（windows是\ linux、mac是/）
                String className = classPath.substring(root.length() + 1, classPath.length() - 6).replace('/', '.').replace('\\', '.');
                String[] splitStrs = className.split("[.]");
                classesMap.put(splitStrs[splitStrs.length - 1], load.loadClass(className));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else {
            File[] fs = f.listFiles();
            if (fs == null) {
                return;
            }
            for (File file : fs) {
                loadClassByPath(root, file.getPath(), load);
            }
        }
    }

    /**
     * 获取初始化的class
     *
     * @param className
     * @return
     */
    public Class getInitClassByName(String className) {
        return classesMap.get(className);
    }

    /**
     * 项目启动后执行一次
     *
     * @param args
     *
     */
    @Override
    public void run(ApplicationArguments args) {
        // 初始化加载
        initData();
        initClass();
    }

    public void initClass() {
        loadClassByLoader(this.getClass().getClassLoader());
    }

    /**
     * List<MetadataSonFieldVo> 为了获取类似数据的元数据包名
     *
     * @return
     */
    public String getAttrNameList(String attrType, boolean isPackage) {
        if (UtilString.isNullOrEmpty(attrType) || !attrType.contains("List")) {
            return attrType;
        }
        attrType = attrType.replace("List", "").replace("<", "").replace(">", "");
        if (isPackage) {
            attrType.replace("Vo", "").replace("Po", "").replace("Dto", "");
        }
        return attrType;
    }

    /**
     * 初始化加载
     */
    public void initData() {
        // 所有实现了IService接口的bean
        Map<String, BaseDataWrap> beans = applicationContext.getBeansOfType(BaseDataWrap.class);

        // 遍历mybatisPlus的service
        for (BaseDataWrap iService : beans.values()) {
            // 取出该mybatisPlus的service对应的entity类
            Class entityClass = UtilMetadata.getParameterizedTypeClass(iService.getClass().getSuperclass(), 1);

            if (entityClass == null) {
                continue;
            }

            // 该mybatisPlus的service对应的数据库表名
            String tableName = UtilMetadata.getTableName(entityClass);

            mPlusServiceMap.put(tableName, iService);

            metadataEntityClassMap.put(tableName, entityClass);
        }
    }
}
