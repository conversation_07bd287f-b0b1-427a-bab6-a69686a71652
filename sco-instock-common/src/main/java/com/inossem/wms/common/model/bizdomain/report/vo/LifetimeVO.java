package com.inossem.wms.common.model.bizdomain.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.util.excel.DateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 寿期台账 VO
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "寿期台账 查询出参传输对象", description = "寿期台账 查询出参")
public class LifetimeVO {

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    @ExcelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    @ExcelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    @ExcelProperty(value = "库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "批次号" , example = "2008310001")
    @ExcelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "寿期检定单号")
    @ExcelProperty(value = "寿期检定单号")
    private String receiptCode;

    @ApiModelProperty(value = "寿期检定单描述")
    @ExcelProperty(value = "寿期检定单描述")
    private String remark;

    @ApiModelProperty(value = "寿期检定单创建时间")
    @ExcelProperty(value = "寿期检定单创建时间", converter = DateConverter.class)
    private Date createTime;

    @ApiModelProperty(value = "寿期检定单提交人")
    @ExcelProperty(value = "寿期检定单提交人")
    private String submitUserName;

    @ApiModelProperty(value = "寿期检定结果单号")
    @ExcelProperty(value = "寿期检定结果单号")
    private String receiptCodeResult;

    @ApiModelProperty(value = "寿期结果维护创建时间")
    @ExcelProperty(value = "寿期结果维护创建时间", converter = DateConverter.class)
    private Date createTimeResult;

    @ApiModelProperty(value = "寿期结果维护提交人")
    @ExcelProperty(value = "寿期结果维护提交人")
    private String submitUserNameResult;

    @ApiModelProperty(value = "检定数量")
    @ExcelProperty(value = "检定数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "单位描述")
    @ExcelProperty(value = "单位描述")
    private String unitName;

    @ApiModelProperty(value = "仓位编码")
    @ExcelProperty(value = "仓位编码")
    private String binCode;

    @ApiModelProperty(value = "包装方式")
    @ExcelIgnore
    private Integer packageType;

    @ExcelProperty(value = "包装方式")
    @ApiModelProperty(value = "包装方式")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存放方式")
    @ExcelIgnore
    private Integer depositType;

    @ApiModelProperty(value = "存放方式")
    @ExcelProperty(value = "存放方式")
    private String depositTypeI18n;

    @ApiModelProperty(value = "检定人")
    @ExcelProperty(value = "检定人")
    private String inspectUserName;

    @ApiModelProperty(value = "需求人")
    @ExcelProperty(value = "需求人")
    private String applyUserName;

    @ApiModelProperty(value = "需求部门")
    @ExcelProperty(value = "需求部门")
    private String applyUserDeptName;

    @ApiModelProperty(value = "生产日期")
    @ExcelProperty(value = "生产日期", converter = DateConverter.class)
    private Date productDate;

    @ApiModelProperty(value = "寿期")
    @ExcelProperty(value = "寿期")
    private String shelfLifeMax;

    @ApiModelProperty(value = "到期日期")
    @ExcelProperty(value = "到期日期", converter = DateConverter.class)
    private Date expireDate;

    @ApiModelProperty(value = "检定结果")
    @ExcelIgnore
    private Integer inspectResult;

    @ApiModelProperty(value = "检定结果")
    @ExcelProperty(value = "检定结果")
    private String inspectResultI18n;

    @ApiModelProperty(value = "延期日期")
    @ExcelProperty(value = "延期日期", converter = DateConverter.class)
    private Date delayDate;

    @ApiModelProperty(value = "移动原因")
    @ExcelIgnore
    private String scrapCause;

    @ApiModelProperty(value = "移动原因")
    @ExcelProperty(value = "移动原因")
    private String scrapCauseI18n;

    @ApiModelProperty(value = "寿期检定结果备注")
    @ExcelProperty(value = "寿期检定结果备注")
    private String remarkResult;
}
