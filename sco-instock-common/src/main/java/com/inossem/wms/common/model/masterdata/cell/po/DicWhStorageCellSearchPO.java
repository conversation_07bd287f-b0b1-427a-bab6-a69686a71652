package com.inossem.wms.common.model.masterdata.cell.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 存储单元主数据查询入参对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元主数据查询入参对象", description = "存储单元主数据查询入参对象")
public class DicWhStorageCellSearchPO extends PageCommon {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "托盘编号" , example = "P001")
    private String cellCode;

    @ApiModelProperty(value = "托盘类型 0 轻型 1 重型" , example = "0")
    private Integer cellType;

    @ApiModelProperty(value = "物料主键" , example = "P001")
    private Long matId;

    @ApiModelProperty(value = "托盘类型 0 轻型 1 重型" , example = "0")
    private List<Integer> cellTypeList;

    @ApiModelProperty(value = "是否为采集任务" , example = "1500")
    private Boolean isCollection;
}
