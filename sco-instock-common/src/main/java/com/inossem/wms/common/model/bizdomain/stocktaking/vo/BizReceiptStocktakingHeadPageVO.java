package com.inossem.wms.common.model.bizdomain.stocktaking.vo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 库存盘点抬头表分页出参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点抬头表分页出参类", description = "库存盘点抬头表分页出参类")
public class BizReceiptStocktakingHeadPageVO implements Serializable {

    private static final long serialVersionUID = 2004111634145166618L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "源盘点单head_id，多次盘点按业务要求均使用首盘单head_id")
    private Long originReceiptHeadId;

    @ApiModelProperty(value = "源盘点单编码")
    private String originReceiptCode;

    @ApiModelProperty(value = "盘点方式描述", example = "明盘")
    private String stocktakingModeI18n;

    @ApiModelProperty(value = "盘点类型描述", example = "首盘")
    private String isReplayI18n;

    @ApiModelProperty(value = "盘点人描述", example = "管理员")
    private String stocktakingUserName;

    @ApiModelProperty(value = "创建人描述", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "单据状态描述" , example = "草稿")
    private String receiptStatusI18n;

    @SonAttr(sonTbName = "biz_receipt_stocktaking_user", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 -盘点人信息集合")
    private List<BizReceiptStocktakingUserDTO> stocktakingUserList;

    @ApiModelProperty(value = "按物料1，仓位0", example = "仓位")
    private String isAppointMatI18n;

    @ApiModelProperty(value = "是否动态盘点：0-否，1-是", example = "否")
    private String isAutoI18n;

    @ApiModelProperty(value = "扩展属性 - 盘点模式模式【1：专项盘点；2：计划盘点；3：交易盘点】")
    private String stocktakingTypeI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "计划日期-开始", example = "2021-05-11")
    private Date beginDate;

    @ApiModelProperty(value = "计划日期-结束", example = "2021-05-12")
    private Date endDate;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "盘点方式：1-明盘，2-盲盘", example = "1")
    private Integer stocktakingMode;

    @ApiModelProperty(value = "盘点人", example = "1")
    private Long stocktakingUserId;

    @ApiModelProperty(value = "盘点日期", example = "2021-05-11")
    private Date stocktakingDate;

    @ApiModelProperty(value = "盘点类型：0-首盘，1-复盘", example = "0")
    private Integer isReplay;

    @ApiModelProperty(value = "是否电子秤盘点 0-不是 1-是", example = "0")
    private Integer isElectronicScale;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "是否动态盘点：0-否，1-是", example = "否")
    private Integer isAuto;

    @ApiModelProperty(value = "按物料1，仓位0" , example = "仓位")
    private Integer isAppointMat;

    @ApiModelProperty(value = "盘点模式【1：专项盘点；2：计划盘点；3：交易盘点】")
    private Integer stocktakingType;

    @ApiModelProperty(value = "关联盘点凭证")
    private Long stocktakingDocHeadId;

    @ApiModelProperty(value = "关联盘点凭证描述")
    private String stocktakingDocHeadRemark;
}
