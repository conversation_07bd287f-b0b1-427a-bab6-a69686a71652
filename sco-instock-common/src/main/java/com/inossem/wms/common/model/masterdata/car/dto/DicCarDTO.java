package com.inossem.wms.common.model.masterdata.car.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆管理传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="车辆管理传输对象", description="车辆管理传输对象")
public class DicCarDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "车辆类型")
    private String carType;

    @ApiModelProperty(value = "车辆类型")
    private String carTypeName;

    @ApiModelProperty(value = "车辆编码")
    private String carCode;

    @ApiModelProperty(value = "车辆描述")
    private String carName;

    @ApiModelProperty(value = "吊带标号")
    private String slingCode;

    @ApiModelProperty(value = "是否禁用【1是，0否】")
    private Integer isDisabled;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人id")
    private String createUserName;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
