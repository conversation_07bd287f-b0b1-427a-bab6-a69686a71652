package com.inossem.wms.common.model.bizdomain.room.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 房间主数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoom对象", description="房间主数据表")
@TableName("biz_room")
public class BizRoom implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "房间编号【楼栋号-房间号】")
    private String roomCode;

    @ApiModelProperty(value = "楼栋号")
    private String buildingNo;

    @ApiModelProperty(value = "房间号")
    private String roomNo;

    @ApiModelProperty(value = "床位数")
    private Integer bedCount;

    @ApiModelProperty(value = "水电费用")
    private BigDecimal waterElectricityCost;

    @ApiModelProperty(value = "当前房间使用信息id(biz_room_usage_head表id)")
    private Long currentRoomUsageHeadId;


}
