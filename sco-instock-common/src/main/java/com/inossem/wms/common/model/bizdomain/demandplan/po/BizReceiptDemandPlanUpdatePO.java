package com.inossem.wms.common.model.bizdomain.demandplan.po;

import java.io.Serializable;
import java.util.List;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptDemandPlanUpdatePO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "需求计划行项目列表", required = true)
    private List<BizReceiptDemandPlanItemDTO> itemList;

}
