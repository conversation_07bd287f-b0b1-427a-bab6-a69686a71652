package com.inossem.wms.common.model.bizdomain.report.po;

import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 入库台账报表 PO
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InputLedgerPO", description="InputLedgerPO")
public class UnitizedInputLedgerPO extends PageCommon {

    @ApiModelProperty(value = "采购订单" , example = "4500000001")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ExcelProperty(value = "主设备编码")
    private String mainMatCode;

    @ApiModelProperty(value = "子设备编码" , example = "101232492")
    private String matCode;

    @ApiModelProperty(value = "批次号" , example = "301232492")
    private String batchCode;

    @ApiModelProperty(value = "需求部门描述" , example = "维修部")
    private String applyUserDeptName;

    @ApiModelProperty(value = "需求科室描述" , example = "维修支持科")
    private String applyUserOfficeName;

    @ApiModelProperty(value = "到货通知单号" )
    private String noticeReceiptCode;

    @ApiModelProperty(value = "到货登记单号" , example = "DJ301232492")
    private String registerReceiptCode;

    @ApiModelProperty(value = "到货登记过账日期查找起始时间" , example = "2023-08-02")
    private Date registerPostStartTime;

    @ApiModelProperty(value = "到货登记过账日期查找截至时间" , example = "2023-08-02")
    private Date registerPostEndTime;

    @ApiModelProperty(value = "到货登记凭证日期查找起始时间" , example = "2023-08-02")
    private Date registerDocDateStartTime;

    @ApiModelProperty(value = "到货登记凭证日期查找截至时间" , example = "2023-08-02")
    private Date registerDocDateEndTime;

    @ApiModelProperty(value = "质检会签单号" , example = "HQ301232492")
    private String counterSignReceiptCode;

    @ApiModelProperty(value = "验收入库单号" , example = "RK301232492")
    private String inspectInputReceiptCode;

    @ApiModelProperty(value = "验收入库过账时间查找起始时间" , example = "2023-08-02")
    private Date inspectInputPostStartTime;

    @ApiModelProperty(value = "验收入库过账时间查找截至时间" , example = "2023-08-02")
    private Date inspectInputPostEndTime;

    @ApiModelProperty(value = "验收入库凭证时间查找起始时间" , example = "2023-08-02")
    private Date inspectInputDocDateStartTime;

    @ApiModelProperty(value = "验收入库凭证时间查找截至时间" , example = "2023-08-02")
    private Date inspectInputDocDateEndTime;

    @ApiModelProperty(value = "移动类型" )
    private String moveType;

    @ApiModelProperty(value = "移动类型")
    private List<String> moveTypeList;

    @ApiModelProperty(value = "描述" )
    private String matName;
    private List<Long> locationIdList;
}
