package com.inossem.wms.common.model.org.factory.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工厂主数据表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicFactory对象", description = "工厂主数据表")
@TableName("dic_factory")
public class DicFactory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "公司id" , example = "1", required = false)
    private Long corpId;

    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "地址" , example = "新地中心")
    private String address;

    @ApiModelProperty(value = "是否为零价值工厂【1是，0否】" , example = "0")
    private Integer isWorthless;

    @ApiModelProperty(value = "是否为闲置物资工厂【1是，0否】" , example = "0")
    private Integer isLeisure;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
