package com.inossem.wms.common.model.bizdomain.contract.vo;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "合同列表展示VO", description = "合同列表展示VO")
public class BizReceiptContractListVO {

    @ApiModelProperty(value = "主键id", example = "159843409264782")
    private Long id;

    @ApiModelProperty(value = "合同编号", example = "HT241024001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "300")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "合同名称", example = "2024年第一季度采购合同")
    private String contractName;

    @ApiModelProperty(value = "合同类型", example = "1")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同子类型", example = "10")
    private Integer contractSubType;

    @ApiModelProperty(value = "采购地", example = "10")
    private Integer purchaseLocation;

    @ApiModelProperty(value = "签订合同日期", example = "2024-01-01")
    private Date contractSignDate;

    @ApiModelProperty(value = "交货期/服务期", example = "2024-12-31")
    private Date deliveryDate;

    @ApiModelProperty(value = "交货地址", example = "10")
    private Integer deliveryAddress;

    @ApiModelProperty(value = "有效期开始", example = "2024-01-01")
    private Date validityStartDate;

    @ApiModelProperty(value = "有效期截止", example = "2024-12-31")
    private Date validityEndDate;

    @ApiModelProperty(value = "甲方", example = "10")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商ID", example = "1001")
    private Long supplierId;

    @ApiModelProperty(value = "供应商编码", example = "S001")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "上海某某贸易有限公司")
    private String supplierName;

    @ApiModelProperty(value = "创建时间", example = "2024-10-24")
    private Date createTime;

    @ApiModelProperty(value = "创建人工号", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "单据类型名称", example = "合同")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态名称", example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "合同类型名称", example = "生产物资类")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "合同子类型名称", example = "常规合同")
    private String contractSubTypeI18n;

    @ApiModelProperty(value = "采购地名称", example = "国内采购")
    private String purchaseLocationI18n;

    @ApiModelProperty(value = "交货地址名称", example = "直接交到卡拉奇港")
    private String deliveryAddressI18n;

    @ApiModelProperty(value = "甲方名称", example = "上海电气集团")
    private String firstPartyI18n;
} 