package com.inossem.wms.common.model.stock.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓位库存重量dto
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StockBinWeightDTO", description="仓位库存重量表")
public class StockBinWeightDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始↓ *************************/

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    /* ********************** 扩展字段结束↑ *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "批次信息id")
    private Long batchId;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "存储类型id")
    private Long typeId;

    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "存储单元id")
    private Long cellId;

    @ApiModelProperty(value = "库存总重量（非限制库存数量+在途库存数量+质检库存数量+冻结库存数量+紧急库存数量+临时库存数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

}
