package com.inossem.wms.common.model.bizdomain.stocktaking.vo;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
//import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.inossem.wms.common.enums.stocktaking.EnumStocktakingDiffType;

/**
 * StocktakingDiffTypeExcelConvertor设计用于
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-06-11
 */
public class StocktakingDiffTypeExcelConvertor implements Converter<Object> {
    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

//    @Override
//    public Object convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
//        return null;
//    }
//
//    @Override
//    public CellData convertToExcelData(Object o, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
//        if (o instanceof Integer) {
//            Integer diffType = (Integer) o;
//            if (EnumStocktakingDiffType.INITIALIZATION.getValue().equals(o)) {
//                return new CellData("--");
//            } else if (EnumStocktakingDiffType.NO_DIFFERENCE.getValue().equals(o)) {
//                return new CellData("无差异");
//            } else if (EnumStocktakingDiffType.INVENTORY_PROFIT.getValue().equals(o)) {
//                return new CellData("盘盈");
//            } else if (EnumStocktakingDiffType.INVENTORY_LOSS.getValue().equals(o)) {
//                return new CellData("盘亏");
//            }
//        }
//        return new CellData(o);
//    }

    @Override
    public Object convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }


    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Object> context) throws Exception {
        Object o = context.getValue();
        if (o instanceof Integer) {
            Integer diffType = (Integer) o;
            if (EnumStocktakingDiffType.INITIALIZATION.getValue().equals(o)) {
                return new WriteCellData("--");
            } else if (EnumStocktakingDiffType.NO_DIFFERENCE.getValue().equals(o)) {
                return new WriteCellData("无差异");
            } else if (EnumStocktakingDiffType.INVENTORY_PROFIT.getValue().equals(o)) {
                return new WriteCellData("盘盈");
            } else if (EnumStocktakingDiffType.INVENTORY_LOSS.getValue().equals(o)) {
                return new WriteCellData("盘亏");
            }
        }
        return new WriteCellData(String.valueOf(o));
    }
}
