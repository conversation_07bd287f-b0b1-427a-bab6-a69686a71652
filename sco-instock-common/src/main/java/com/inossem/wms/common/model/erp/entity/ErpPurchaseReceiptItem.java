package com.inossem.wms.common.model.erp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 采购订单行项目明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

@TableName("erp_purchase_receipt_item")
@ApiModel(value = "ErpPurchaseReceiptItem对象", description = "采购订单行项目明细表")
public class ErpPurchaseReceiptItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head主键id" , example = "111")
    private Long headId;

    @ApiModelProperty(value = "采购订单行号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "合同号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "合同行号" , example = "0010")
    private String contractRid;

    @ApiModelProperty(value = "合同描述" , example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "合同创建人", example = "张三")
    private String contractCreateUserName;

    @ApiModelProperty(value = "客户编号" , example = "1")
    private String customerCode;

    @ApiModelProperty(value = "客户名称" , example = "1")
    private String customerName;

    @ApiModelProperty(value = "需求申请单号" , example = "1000000001")
    private String requireApplicationCode;

    @ApiModelProperty(value = "需求申请行号" , example = "0010")
    private String requireApplicationRid;

    @ApiModelProperty(value = "采购申请号" , example = "3000000001")
    private String purchaseApplicationCode;

    @ApiModelProperty(value = "采购申请行号" , example = "0010")
    private String purchaseApplicationRid;

    @ApiModelProperty(value = "品牌信息" , example = "11111")
    private String brandmodel;

    @ApiModelProperty(value = "计划配送日期" , example = "2021-05-11")
    private Date deliveryDatePlan;

    @ApiModelProperty(value = "送货日期" , example = "2021-05-11")
    private Date deliveryTime;

    @ApiModelProperty(value = "送货日期" , example = "1")
    private String needDeptCode;

    @ApiModelProperty(value = "订单数量(订购数量)" , example = "10")
    private BigDecimal receiptQty;

    @ApiModelProperty(value = "已采购数量" , example = "10")
    private BigDecimal submitQty;

    @ApiModelProperty(value = "已质检数量" , example = "10")
    private BigDecimal inspectQty;

    @ApiModelProperty(value = "可送货数量" , example = "10")
    private BigDecimal canDeliveryQty;

    @ApiModelProperty(value = "可出库数量" , example = "10")
    private BigDecimal canOutputQty;

    @ApiModelProperty(value = "erp批次" , example = "00016603")
    private String batchErp;

    @ApiModelProperty(value = "单价" , example = "10.1")
    private BigDecimal price;

    @ApiModelProperty(value = "金额" , example = "20.1")
    private BigDecimal money;

    @ApiModelProperty(value = "订单单位" , example = "1")
    private Long unitId;

    @ApiModelProperty(value = "公司编码" , example = "1000", required = false)
    private Long corpId;

    @ApiModelProperty(value = "工厂" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "仓库号" , example = "1")
    private Long whId;

    @ApiModelProperty(value = "库存地点" , example = "10")
    private Long locationId;

    @ApiModelProperty(value = "物料" , example = "145914045988865")
    private Long matId;

    @ApiModelProperty(value = "填充属性 - 物料描述备份", example = "物料描述001003")
    private String matNameBack;

    @ApiModelProperty(value = "移动类型" , example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "需求人编码" , example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "需求人描述" , example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "需求人部门编码" , example = "")
    private String applyUserDeptCode;

    @ApiModelProperty(value = "需求人部门描述" , example = "管理员")
    private String applyUserDeptName;

    @ApiModelProperty(value = "需求人科室编码" , example = "Admin")
    private String applyUserOfficeCode;

    @ApiModelProperty(value = "需求人科室描述" , example = "管理员")
    private String applyUserOfficeName;

    @ApiModelProperty(value = "SAP领用单位" , example = "领用单位")
    private String applyCompany;

    @ApiModelProperty(value = "采购员编号" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "采购员名称" , example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "采购组编号" , example = "A01")
    private String purchaseGroupCode;

    @ApiModelProperty(value = "采购组描述" , example = "英诺森科采购员一")
    private String purchaseGroupName;

    @ApiModelProperty(value = "采购组织编号" , example = "718A")
    private String purchaseOrganizationCode;

    @ApiModelProperty(value = "采购组织描述" , example = "英诺森采购组织")
    private String purchaseOrganizationName;

    @ApiModelProperty(value = "供应商编码" , example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称" , example = "邯郸市邯山荷华商贸有限公司")
    private String supplierName;

    @ApiModelProperty(value = "特殊库存类型 E 现有订单 K 寄售（供应商） O 供应商分包库存 Q 项目库存" , example = "E")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "WBS编码-外码" , example = "wbsCode2")
    private String whCodeOut;

    @ApiModelProperty(value = "特殊库存描述" , example = "wbsName2")
    private String specStockName;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "项目类别")
    private String projectType;

    @ApiModelProperty(value = "科目类别")
    private String subjectType;
    private String guaranteePeriod;

    @ApiModelProperty(value = "总账科目文本")
    private String generalLedgerAccountText;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    private Integer deleteTag;

    @ApiModelProperty(value = "税码")
    private Integer taxCode;
}
