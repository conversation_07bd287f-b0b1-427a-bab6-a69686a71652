package com.inossem.wms.common.model.bizdomain.inconformity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "差异通知导出vo", description = "差异通知导出vo")
public class BizReceiptInconformityExportVO implements Serializable {


    private static final long serialVersionUID = -7721592532064095330L;

    @ExcelProperty(value = "差异通知单号", index = 0)
    @ApiModelProperty("差异通知单号")
    private String receiptCode;

    @ExcelIgnore
    @ApiModelProperty(value = "差异类型")
    private Integer differentType;

    @ExcelProperty(value = "差异类型", index = 1)
    @ApiModelProperty("差异类型")
    private String differentTypeI18n;

    @ExcelProperty(value = "创建人", index = 2)
    @ApiModelProperty("创建人")
    private String createUserName;

    @ExcelProperty(value = "创建时间", index = 3)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ExcelIgnore
    private Integer receiptStatus;

    @ExcelProperty(value = "单据状态", index = 4)
    private String receiptStatusI18n;

    @ExcelProperty(value = "采购员", index = 5)
    private String purchaserManagerName;

    @ExcelProperty(value = "合同号", index = 6)
    private String contractCode;

    @ExcelProperty(value = "合同名称", index = 7)
    private String contractName;

    @ExcelProperty(value = "物料编码", index = 8)
    private String matCode;

    @ExcelProperty(value = "物料描述", index = 9)
    private String matName;

    @ExcelProperty(value = "计量单位", index = 10)
    private String unitName;

    @ExcelProperty(value = "采购订单", index = 11)
    private String purchaseCode;

    @ExcelProperty(value = "采购订单行项目", index = 12)
    private String purchaseRid;

    @ExcelProperty(value = "未到货数量", index = 13)
    private BigDecimal unarrivalQty;

    @ExcelProperty(value = "不合格数量", index = 14)
    private BigDecimal qty;

    @ExcelProperty(value = "车辆编号", index = 15)
    private String carCode;

    @ExcelProperty(value = "司机姓名", index = 16)
    private String driverName;

    @ExcelProperty(value = "联系方式", index = 17)
    private String contactWay;

    @ExcelProperty(value = "发票号", index = 18)
    private String invoiceNo;

    @ExcelProperty(value = "发票日期", index = 19)
    private Date invoiceDate;

    @ExcelProperty(value = "建议处置方式", index = 20)
    private String disposalMethodI18n;

    @ExcelIgnore
    @ApiModelProperty(value = "处置方式")
    private Integer disposalMethod;

    @ExcelProperty(value = "差异描述", index = 21)
    private String inconformityReason;

    @ExcelProperty(value = "需求计划单号", index = 22)
    private String demandPlanCode;

    @ExcelProperty(value = "需求人", index = 23)
    private String demandPerson;

}
