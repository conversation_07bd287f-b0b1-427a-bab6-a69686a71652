package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 借用申请单行项目明细传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="借用申请单行项目明细传输对象", description="借用申请单行项目明细传输对象")
public class BizReceiptApplyItemExportDTO implements Serializable {

    @ExcelIgnore
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ExcelIgnore
    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ExcelIgnore
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ExcelIgnore
    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ExcelIgnore
    @ApiModelProperty(value = "报废申请单号")
    private String receiptCode;

    @ExcelProperty(value = "行号", index = 0)
    private String rid;

    @ExcelProperty(value = "物料编码", index = 1)
    @ApiModelProperty(value = "填物料编码" , example = "M001005")
    private String matCode;

    @ExcelProperty(value = "物料描述", index = 2)
    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ExcelProperty(value = "计量单位", index = 3)
    private String unitName;

    @ExcelProperty(value = "批次", index = 4)
    @ApiModelProperty(value = "批次编码" , example = "100001")
    private String batchCode;

    @ExcelProperty(value = "库存数量", index = 5)
    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ExcelProperty(value = "申请数量", index = 6)
    @ApiModelProperty(value = "申请数量")
    private BigDecimal qty;

    @ExcelIgnore
    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ExcelIgnore
    @ApiModelProperty(value = "需求人")
    private String demandUserName;

    @ExcelIgnore
    @ApiModelProperty(value = "入库日期" , example = "2021-05-10")
    private Date inputDate;

}
