package com.inossem.wms.common.model.log.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "LogSapLogDTO对象", description = "SAP异常日志表")
public class LogSapLogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    private Long id;

    @ApiModelProperty(value = "SAP返回结果" , example = "String")
    private String sapResult;

    @ApiModelProperty(value = "返回信息" , example = "String")
    private String returnMsg;

    @ApiModelProperty(value = "请求来源" , example = "String")
    private String requestSource;

    @ApiModelProperty(value = "接口类型" , example = "String")
    private String interfaceType;

    @ApiModelProperty(value = "设备编号" , example = "String")
    private String equipmentNumber;

    @ApiModelProperty(value = "单据编码" , example = "String")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" , example = "211")
    private Integer receiptType;

    @ApiModelProperty(value = "单据类型国际化" , example = "String")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "数据流向" , example = "String")
    private String dataFlowDirection;

    @ApiModelProperty(value = "请求url" , example = "String")
    private String url;

    @ApiModelProperty(value = "入参" , example = "String")
    private String inParam;

    @ApiModelProperty(value = "出参" , example = "String")
    private String outParam;

    @ApiModelProperty(value = "用户编码" , example = "管理员")
    private String userName;

    @ApiModelProperty(value = "请求类型" , example = "String")
    private String requestType;

    @ApiModelProperty(value = "创建日期" , example = "2021-05-11")
    private Date createDate;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private LocalTime createTime;

    @ApiModelProperty(value = "处理结果描述" , example = "")
    private String execStatus;

    @ApiModelProperty(value = "处理结果编码" , example = "")
    private String execCode;

    @ApiModelProperty(value = "处理说明" , example = "")
    private String execDescription;

    @ApiModelProperty(value = "接口名称" , example = "")
    private String interfaceName;

    @ApiModelProperty(value = "业务单据主键")
    private String receiptId;

    @ApiModelProperty(value = "接口状态（0失败 1成功）" )
    private Integer interfaceStatus;

    @ApiModelProperty(value = "接口描述" , example = "String")
    private String interfaceDescribe;

}
