package com.inossem.wms.common.model.bizdomain.service.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务工单抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptServiceHead对象", description = "服务工单抬头表")
public class BizReceiptServiceHeadDTO implements Serializable {

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "供应商主数据集合")
    private List<DicSupplier> supplierList;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称", example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 单据类型")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "供应商编码", example = "1000")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "英诺森")
    private String supplierName;

    @SonAttr(sonTbName = "biz_receipt_service_facility_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "服务工单设施行项目列表")
    List<BizReceiptServiceFacilityItemDTO> facilityItemDTOList;

    @SonAttr(sonTbName = "biz_receipt_service_cost_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "服务工单费用行项目列表")
    List<BizReceiptServiceCostItemDTO> costItemDTOList;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据日志")
    List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_common_receipt_relation", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据流")
    List<BizCommonReceiptRelationDTO> relationList;

    /* ********************** 扩展字段开始 *************************/

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("单据号")
    private String receiptCode;

    @ApiModelProperty("单据类型")
    private Integer receiptType;

    @ApiModelProperty("单据状态")
    private Integer receiptStatus;

    @ApiModelProperty("描述")
    private String des;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("费用合计（USD）")
    private BigDecimal totalAmount;

    @ApiModelProperty("结算日期")
    private Date settlementDate;

    @ApiModelProperty("结算金额（USD）")
    private BigDecimal settlementAmount;

    @ApiModelProperty("备注")
    private String remark;

    @TableLogic
    @ApiModelProperty("是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty("创建人id")
    private Long createUserId;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

}
