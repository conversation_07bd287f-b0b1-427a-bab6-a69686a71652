package com.inossem.wms.common.model.masterdata.invoice.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
@ApiModel(value = "发票主数据查询入参类", description = "发票主数据查询入参类")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DicInvoiceSearchPO extends PageCommon {

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "是否过滤空发票")
    private Integer filterEmptyInvoice;
}
