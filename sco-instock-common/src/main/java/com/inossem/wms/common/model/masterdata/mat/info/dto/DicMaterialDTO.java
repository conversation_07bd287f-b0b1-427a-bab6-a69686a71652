package com.inossem.wms.common.model.masterdata.mat.info.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 物料主数据数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "物料主数据数据传输对象", description = "物料主数据数据传输对象")
public class DicMaterialDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/

    @ApiModelProperty(value = "入库数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  Long inputNum;

    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "采购订单")
    private BigDecimal purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单头" )
    private BigDecimal purchaseReceiptHeadId;

    @ApiModelProperty(value = "采购订单行项目" )
    private BigDecimal purchaseReceiptItemId;

    @ApiModelProperty(value = "前置单据id" , example = "149901631619073")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前置单据行项目号" , example = "1")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "保养大纲" , example = "保养大纲")
    private String maintenanceProgram;

    @ApiModelProperty(value = "生产厂家" , example = "Inossem")
    private String supplierName;

    @ApiModelProperty(value = "生产日期" , example = "2020-02-02")
    private Date productionDate;

    @ApiModelProperty(value = "维保日期" , example = "1")
    private Date maintenanceDate;

    @ApiModelProperty(value = "维保周期" , example = "1")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "工具类型id" , example = "1231")
    private String toolTypeId;

    @ApiModelProperty(value = "出厂编码" , example = "3")
    private String outFtyCode;

    @ApiModelProperty(value = "仓位id" , example = "1123")
    private Long binId;

    @ApiModelProperty(value = "已入库数量" , example = "10")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "出库数量" , example = "5")
    private BigDecimal qty;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称" , example = "立方米")
    private String unitName;

    /**
     * 物料组编码
     */
    @ApiModelProperty(value = "物料组编码" , example = "g1")
    private String matGroupCode;

    /**
     * 物料组名称
     */
    @ApiModelProperty(value = "物料组名称" , example = "物料组1")
    private String matGroupName;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码" , example = "A1")
    private String matTypeCode;

    /**
     * 物料类型名��
     */
    @ApiModelProperty(value = "物料类型名称" , example = "物料类型1")
    private String matTypeName;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /**
     * 是否冻结描述
     */
    @ApiModelProperty(value = "是否冻结描述" , example = "否")
    private String isFreezeI18n;

    /**
     * 是否危险物料描述
     */
    @ApiModelProperty(value = "是否危险物料描述" , example = "否")
    private String isDangerousI18n;

    /**
     * 是否超出容差
     */
    @ApiModelProperty(value = "是否超出容差" , example = "否")
    private Boolean isBeyondWeightTolerance;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private String depositTypeI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "成套主物料id")
    private Long parentMatId;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "品名（英文）" , example = "物料描述001003")
    private String matNameEn;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "计量单位id" , example = "7")
    private Long unitId;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组id" , example = "1")
    private Long matGroupId;

    @RlatAttr(rlatTableName = "dic_material_type", sourceAttrName = "matTypeCode,matTypeName", targetAttrName = "matTypeCode,matTypeName")
    @ApiModelProperty(value = "物料类型id", example = "1")
    private Long matTypeId;

    @ApiModelProperty(value = "长度" , example = "100")
    private BigDecimal length;

    @ApiModelProperty(value = "宽度" , example = "100")
    private BigDecimal width;

    @ApiModelProperty(value = "高度" , example = "100")
    private BigDecimal height;

    @ApiModelProperty(value = "长度/宽度/高度的单位" , example = "M")
    private String unitLength;

    @ApiModelProperty(value = "毛重" , example = "100")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重" , example = "100")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "重量容差" , example = "正负5%")
    private BigDecimal weightTolerance;

    @ApiModelProperty(value = "重量的单位" , example = "KG")
    private String unitWeight;

    @ApiModelProperty(value = "体积" , example = "1000000")
    private BigDecimal volume;

    @ApiModelProperty(value = "体积的单位" , example = "M3")
    private String unitVolume;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLife;

    @ApiModelProperty(value = "保质期的单位 D-天 M-月" , example = "M")
    private String unitShelfLife;

    @ApiModelProperty(value = "是否启用保质期【1是，0否】" , example = "1")
    private Integer isShelfLife;

    @ApiModelProperty(value = "是否冻结【1是，0否】" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "是否危险物料【1是，0否】" , example = "0")
    private Integer isDangerous;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "最小货架寿命")
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "存储级别，SAP库存确定组")
    private String eprio;
    // 非成套子部件物料标识:1-非成套子部件物料;0-成套子部件物料;
    private Integer mainFlag;

    @ApiModelProperty(value = "成套设备自编码物料置为1，其他物料为0")
    private Integer isCtCode;


    /* 2024-07-29 SDW二期新增字段 */
    @ApiModelProperty(value = "SAP响应字段 ZZJBWL 基本物料")
    private String extBasicMaterial;

    @ApiModelProperty(value = "SAP响应字段 ZZBDJ 质保等级")
    private String extWarrantyLevel;

    @ApiModelProperty(value = "SAP响应字段 ZZBDJT 质保等级描述")
    private String extWarrantyLevelDesc;

    @ApiModelProperty(value = "SAP响应字段 ZSFHJG 是否核安全局监管备件")
    private String extIsUnderNuclearSafetySupervision;

    @ApiModelProperty(value = "SAP响应字段 ZSFHJGT 是否核安全局监管备件描述")
    private String extIsUnderNuclearSafetySupervisionDesc;

    @ApiModelProperty(value = "SAP响应字段 ZZZTZ 制造厂图纸号")
    private String extManufacturerDrawingNumber;

    @ApiModelProperty(value = "SAP响应字段 ZZZTX 制造厂图项号")
    private String extManufacturerDrawingItemNumber;

    @ApiModelProperty(value = "SAP响应字段 ZZCCJ 制造厂家")
    private String extManufacturer;

    @ApiModelProperty(value = "SAP响应字段 ZZZSBH 制造商编号")
    private String extManufacturerCode;

    @ApiModelProperty(value = "SAP响应字段 ZZZSBHT 制造商名称")
    private String extManufacturerName;

    @ApiModelProperty(value = "SAP响应字段 MFRPN 制造商零件编号")
    private String extManufacturerPartNumber;

    @ApiModelProperty(value = "SAP响应字段 ZSFFSX 是否带放射性")
    private String extIsRadioactive;

    @ApiModelProperty(value = "SAP响应字段 ZSFFSXT 是否带放射性描述")
    private String extIsRadioactiveDesc;

    @ApiModelProperty(value = "SAP响应字段 ZWZFL 物资分类")
    private String extMaterialClassification;

    @ApiModelProperty(value = "SAP响应字段 ZWZFLT 物资分类描述")
    private String extMaterialClassificationDesc;

    @ApiModelProperty(value = "SAP响应字段 ZCGDDWB 采购订单文本")
    private String extPurchaseOrderText;

    @ApiModelProperty(value = "SAP响应字段 BKLAS 评估类")
    private String extEvaluationClassification;

    @ApiModelProperty(value = "SAP响应字段 BKBEZ 评估类的描述")
    private String extEvaluationClassificationDesc;

    @ApiModelProperty(value = "SAP响应字段 WRKST 基本物料")
    private String extMainMaterial;

    @ApiModelProperty(value = "SAP响应字段 NORMT 行业标准描述")
    private String extIndustryStandardDesc;

    @ApiModelProperty(value = "去年采购量", example = "1000.00")
    private BigDecimal lastYearPurchaseQty;

    @ApiModelProperty(value = "去年消耗量", example = "800.00")
    private BigDecimal lastYearConsumeQty;

}
