package com.inossem.wms.common.model.bizdomain.stocktaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 库存盘点物料明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptStocktakingBin对象", description = "库存盘点物料明细表")
@TableName("biz_receipt_stocktaking_bin")
public class BizReceiptStocktakingBin implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点id", example = "157329202937857")
    private Long headId;

    @ApiModelProperty(value = "行項目id", example = "157329202937858")
    private Long itemId;

    @ApiModelProperty(value = "盘点凭证行项目配货序号", example = "1")
    private String bid;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    private Long locationId;

    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "存储区id", example = "1")
    private Long typeId;

    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "存储单元" , example = "P001")
    private Long cellId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "基本计量单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "库存数量" , example = "10")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "库存重量" , example = "10")
    private BigDecimal stockQtyWeight;

    @ApiModelProperty(value = "盘点数量", example = "100")
    private BigDecimal qty;

    @ApiModelProperty(value = "盘点重量", example = "100")
    private BigDecimal qtyWeight;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号" , example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度" , example = "2015-05-11")
    private String matDocYear;

    @ApiModelProperty(value = "盘点差异 0 初始化 1 无差异 2 盘盈 3 盘亏", example = "0")
    private Integer diffType;

    @ApiModelProperty(value = "复盘盘点差异 0 初始化 1 无差异 2 盘盈 3 盘亏", example = "0")
    private Integer secondaryDiffType;

    @ApiModelProperty(value = "状态：10-草稿,20-已提交", example = "10")
    private Integer matStatus;

    @ApiModelProperty(value = "修改状态【1是，0否】", example = "1")
    private Integer modifyStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "转入数量")
    private BigDecimal transportInputQty;

    @ApiModelProperty(value = "转出数量")
    private BigDecimal transportOutputQty;

    @ApiModelProperty(value = "货位转入数量")
    private BigDecimal binInputQty;

    @ApiModelProperty(value = "货位转出数量")
    private BigDecimal binOutputQty;

    @ApiModelProperty(value = "盘点计数确认时间")
    private Date countingConfirmTime;

    @ApiModelProperty(value = "盘点计数人")
    private Long countingUserId;

    @ApiModelProperty(value = "盘点备注")
    private String remark;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "物料组id" )
    private Long matGroupId;

    @ApiModelProperty(value = "源盘点单bin_id")
    private Long originBinId;

    @ApiModelProperty(value = "首盘盘点单bin_id")
    private Long firstBinId;

    @ApiModelProperty(value = "最后一次复盘盘点单bin_id")
    private Long lastBinId;

    @ApiModelProperty(value = "是否计数 0 不计数 1计数")
    private Integer isCount;
}
