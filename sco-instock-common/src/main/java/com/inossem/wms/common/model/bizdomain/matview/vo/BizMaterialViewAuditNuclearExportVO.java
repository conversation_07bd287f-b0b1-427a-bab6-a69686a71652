package com.inossem.wms.common.model.bizdomain.matview.vo;

import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 物料主数据视图审批-核电级别
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@TableName("biz_material_view_audit_nuclear")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物料主数据视图审批-核电级别ExportVO")
public class BizMaterialViewAuditNuclearExportVO  {
}
