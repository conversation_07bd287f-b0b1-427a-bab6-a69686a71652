package com.inossem.wms.common.model.bizdomain.contract.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BizReceiptContractItemGroupDTO对象", description = "合同行项目分组DTO")
public class BizReceiptContractItemGroupDTO implements Serializable {
    private static final long serialVersionUID = 4001543376378602100L;

    @ApiModelProperty(value = "预算年度")
    private String budgetYear;

    @ApiModelProperty(value = "预算分类")
    private String budgetClass;

    @ApiModelProperty(value = "预算科目")
    private String budgetAccount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "含税总价（换算汇率后）")
    private BigDecimal taxAmount;
}
