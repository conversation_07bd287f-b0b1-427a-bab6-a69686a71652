package com.inossem.wms.common.model.masterdata.budget.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.masterdata.budget.dto.DicBudgetSubjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "预算分类分页对象出参", description = "预算分类分页对象出参")
public class DicBudgetClassifyPageVO implements Serializable {
    private static final long serialVersionUID = 7690296224268618052L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "预算分类编码")
    private String budgetClassifyCode;

    @ApiModelProperty(value = "预算分类描述")
    private String budgetClassifyName;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @SonAttr(sonTbName = "dic_budget_subject", sonTbFkAttrName = "budgetClassifyId")
    private List<DicBudgetSubjectDTO> dicBudgetSubjectDTOList;
}
