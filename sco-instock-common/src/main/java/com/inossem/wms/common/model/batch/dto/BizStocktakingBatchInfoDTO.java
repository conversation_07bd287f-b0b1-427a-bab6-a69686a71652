package com.inossem.wms.common.model.batch.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 盘点凭证批次信息传输对象
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "批次信息传输对象", description = "批次信息传输对象")
public class BizStocktakingBatchInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "入库数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  Long inputNum;

    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "前置单据id" , example = "149901631619073")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前置单据行项目号" , example = "1")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "仓位id" , example = "1123")
    private Long binId;

    @ApiModelProperty(value = "已入库数量" , example = "10")
    private BigDecimal inputQty;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "单位ID" , example = "1")
    private Long unitId;
    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;
    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;
    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;
    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    @ApiModelProperty(value = "扩展属性 - 单品/批次  0批次 1单品" , example = "1")
    private String isSingleI18n;

    @ApiModelProperty(value = "仓位Code")
    private String binCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "标签类型  1：RFID抗金属  2：RFID非抗金属 3：普通标签" , example = "1")
    private String tagTypeI18n;

    @ApiModelProperty(value = "工具类型名称")
    private String toolTypeName;

    @ApiModelProperty(value = "工器具编码首字母配置序列code")
    private String toolCodingInitial;

    @ApiModelProperty(value = "工具状态描述")
    private String toolStatusI18n;

    @ApiModelProperty(value = "维保有效期")
    private Date maintenanceInDate;

    @ApiModelProperty(value = "入库明细表id")
    private Long inputBinId;

    @ApiModelProperty(value = "是否主要配件描述【0：否；1：是】")
    private String isMainPartsI18n;

    @ApiModelProperty(value = "成套主部件工厂code")
    private String unitizedFtyCode;

    @ApiModelProperty(value = "成套主部件工厂name")
    private String unitizedFtyName;

    @ApiModelProperty(value = "成套主部件库存地点code")
    private String unitizedLocationCode;

    @ApiModelProperty(value = "成套主部件库存地点name")
    private String unitizedLocationName;

    @ApiModelProperty(value = "填充属性 - 所属部门")
    private String deptCode;

    @ApiModelProperty(value = "填充属性 - 所属部门描述")
    private String deptName;

    @ApiModelProperty(value = "填充属性 - 所属科室")
    private String deptOfficeCode;

    @ApiModelProperty(value = "填充属性 - 所属科室描述")
    private String deptOfficeName;

    @ApiModelProperty(value = "填充属性 - 所在人")
    private String locateUserCode;

    @ApiModelProperty(value = "填充属性 - 所在人描述")
    private String locateUserName;

    @ApiModelProperty(value = "填充属性 - 借用部门")
    private String borrowDeptCode;

    @ApiModelProperty(value = "填充属性 - 借用部门描述")
    private String borrowDeptName;

    @ApiModelProperty(value = "填充属性 - 借用科室")
    private String borrowDeptOfficeCode;

    @ApiModelProperty(value = "填充属性 - 借用科室描述")
    private String borrowDeptOfficeName;

    @ApiModelProperty(value = "填充属性 - 借用人")
    private String borrowUserCode;

    @ApiModelProperty(value = "填充属性 - 借用人描述")
    private String borrowUserName;

    @ApiModelProperty(value = "扩展属性 - 借用类型")
    private String borrowTypeI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,unitId,packageType", targetAttrName = "matCode,matName,unitId,packageType")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    private Integer packageType;

    private String packageTypeI18n;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName,corpId", targetAttrName = "ftyCode,ftyName,corpId")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;


    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司编码", example = "1000", required = false)
    private Long corpId;

    @ApiModelProperty(value = "公司编码", example = "1000", required = false)
    private String corpCode;

    @ApiModelProperty(value = "公司描述", example = "示例公司", required = false)
    private String corpName;

    @ApiModelProperty(value = "批次code" , example = "100001")
    private String batchCode;

    @ApiModelProperty(value = "冻结标识【1是，0否】" , example = "1")
    private Integer isFreeze;

    @ApiModelProperty(value = "采购订单head表id" , example = "111")
    private Long purchaseReceiptHeadId;

    @ApiModelProperty(value = "采购订单item表id" , example = "111")
    private Long purchaseReceiptItemId;

    @ApiModelProperty(value = "采购订单号" , example = "4500000001")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行项目号" , example = "0010")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "生产订单head表id" , example = "1")
    private Long productionReceiptHeadId;

    @ApiModelProperty(value = "生产订单item表id" , example = "1")
    private Long productionReceiptItemId;

    @ApiModelProperty(value = "生产订单号" , example = "SC001")
    private String productionReceiptCode;

    @ApiModelProperty(value = "生产订单行项目号" , example = "1")
    private String productionReceiptRid;

    @ApiModelProperty(value = "erp批次" , example = "00016603")
    private String batchErp;

    @ApiModelProperty(value = "入库日期" , example = "2021-05-10")
    private Date inputDate;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productionDate;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLine;

    @ApiModelProperty(value = "供应商编码" , example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "供应商描述" , example = "邯郸市邯山荷华商贸有限公司")
    private String supplierName;

    @ApiModelProperty(value = "前续物料id" , example = "158977300168706")
    private Long preMatId;

    @ApiModelProperty(value = "前续工厂id" , example = "145344002326529")
    private Long preFtyId;

    @ApiModelProperty(value = "前续批次id" , example = "100001")
    private Long preBatchId;

    @ApiModelProperty(value = "特殊库存类型 E 现有订单 K 寄售（供应商） O 供应商分包库存 Q 项目库存" , example = "E")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "特殊库存描述" , example = "wbsName2")
    private String specStockName;

    @ApiModelProperty(value = "需求部门" , example = "1100")
    private String demandDept;

    @ApiModelProperty(value = "有效期" , example = "2022-05-22")
    private Date validityDate;

    @ApiModelProperty(value = "合同编号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "合同描述" , example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "客户编码" , example = "1")
    private String customerCode;

    @ApiModelProperty(value = "客户描述" , example = "客户")
    private String customerName;

    @ApiModelProperty(value = "标签类型 1：RFID抗金属  2：RFID非抗金属 0：普通标签" , example = "1")
    private Integer tagType;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    private Integer isSingle;

    @ApiModelProperty(value = "验收单head表id" , example = "149759851560961")
    private Long inspectHeadId;

    @ApiModelProperty(value = "验收单item表id" , example = "149759862046721")
    private Long inspectItemId;

    @ApiModelProperty(value = "验收单号" , example = "ZJ01000098")
    private String inspectCode;

    @ApiModelProperty(value = "验收日期" , example = "2021-05-10")
    private Date inspectDate;

    @ApiModelProperty(value = "验收人" , example = "1")
    private Long inspectUserId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "工具类型")
    @RlatAttr(rlatTableName = "dic_tool_type", sourceAttrName = "toolTypeName,toolCodingInitial", targetAttrName = "toolTypeName,toolCodingInitial")
    private Long toolTypeId;

    @ApiModelProperty(value = "工具状态")
    private Integer toolStatus;

    @ApiModelProperty(value = "维保日期")
    private Date maintenanceDate;

    @ApiModelProperty(value = "维保周期")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "维保预警器")
    private Integer maintenanceWarningPeriod;

    @ApiModelProperty(value = "保养大纲")
    private String maintenanceProgram;

    @ApiModelProperty(value = "出厂编码")
    private String outFtyCode;

    @ApiModelProperty(value = "规格型号")
    private String formatCode;

    @ApiModelProperty(value = "工器具送检单位")
    private String toolInspectUnit;

    @ApiModelProperty(value = "工器具送检日期")
    private Date toolInspectDate;

    @ApiModelProperty(value = "废旧物资-原价" , example = "20.1")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "废旧物资-折旧价" , example = "1.1")
    private BigDecimal depreciationPrice;

    @ApiModelProperty(value = "废旧物资-处置评估价" , example = "3.1")
    private BigDecimal appraisalPrice;

    @ApiModelProperty(value = "废旧物资-是否危险【1是，0否】")
    private Integer isDanger;

    @ApiModelProperty(value = "废旧物资-是否危险【1是，0否】")
    private String isDangerI18n;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "是否闲置")
    private Integer isLeisure;

    @ApiModelProperty(value = "暂存人")
    private String tempStoreUser;

    @ApiModelProperty(value = "填充属性 - 暂存部门名称")
    private String tempStoreDeptName;
    @ApiModelProperty(value = "填充属性 - 暂存部门code")
    private String tempStoreDeptCode;
    @ApiModelProperty(value = "填充属性 - 暂存科室名称")
    private String tempStoreDeptOfficeName;
    @ApiModelProperty(value = "填充属性 - 暂存科室code")
    private String tempStoreDeptOfficeCode;

    @ApiModelProperty(value = "暂存部门")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptName,deptCode", targetAttrName = "tempStoreDeptName,tempStoreDeptCode")
    private Long tempStoreDeptId;

    @ApiModelProperty(value = "暂存科室")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeName,deptOfficeCode", targetAttrName = "tempStoreDeptOfficeName,tempStoreDeptOfficeCode")
    private Long tempStoreDeptOfficeId;

    @ApiModelProperty(value = "需求人编码" , example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "需求人描述" , example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "需求人部门编码" , example = "")
    private String applyUserDeptCode;

    @ApiModelProperty(value = "需求人部门描述" , example = "管理员")
    private String applyUserDeptName;

    @ApiModelProperty(value = "需求人科室编码" , example = "Admin")
    private String applyUserOfficeCode;

    @ApiModelProperty(value = "需求人科室描述" , example = "管理员")
    private String applyUserOfficeName;

    @ApiModelProperty(value = "计量分级" , example = "")
    private String metrologyClassification;

    @ApiModelProperty(value = "准确度等级" , example = "")
    private String levelOfAccuracy;

    @ApiModelProperty(value = "量程" , example = "")
    private String toolsRange;

    @ApiModelProperty(value = "是否主要配件【0：否；1：是】")
    private Integer isMainParts;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "尾差")
    private BigDecimal remainder;

    @ApiModelProperty(value = "工器具 - 管理状态备注")
    private String description;

    @ApiModelProperty(value = "工器具-所属部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

    @ApiModelProperty(value = "工器具-所属科室id")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeCode,deptOfficeName", targetAttrName = "deptOfficeCode,deptOfficeName")
    private Long deptOfficeId;

    @ApiModelProperty(value = "工器具-创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "locateUserCode,locateUserName")
    private Long locateUserId;

    @ApiModelProperty(value = "工器具-借用部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "borrowDeptCode,borrowDeptName")
    private Long borrowDeptId;

    @ApiModelProperty(value = "工器具-借用科室id")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeCode,deptOfficeName", targetAttrName = "borrowDeptOfficeCode,borrowDeptOfficeName")
    private Long borrowDeptOfficeId;

    @ApiModelProperty(value = "工器具-借用人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "borrowUserCode,borrowUserName")
    private Long borrowUserId;

    @ApiModelProperty(value = "工器具-借用类型")
    private Integer borrowType;

    @ApiModelProperty(value = "成套主部件工厂id")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "unitizedFtyCode,unitizedFtyName")
    private Long unitizedFtyId;

    @ApiModelProperty(value = "成套主部件库存地点id")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "unitizedLocationCode,unitizedLocationName")
    private Long unitizedLocationId;

//    @ApiModelProperty(value = "箱号")
//    private String extend1;

    @ApiModelProperty(value = "采购包")
    private String extend2;

//    @ApiModelProperty(value = "质量符合性声明编号")
//    private String extend3;
//
//    @ApiModelProperty(value = "发货批次号")
//    private String extend4;
//
//    @ApiModelProperty(value = "装箱单版本")
//    private String extend5;
//
//    @ApiModelProperty(value = "箱内UP件数")
//    private String extend6;
//
//    @ApiModelProperty(value = "箱内物资简述")
//    private String extend7;
//
//    @ApiModelProperty(value = "岛别")
//    private String extend8;
//
//    @ApiModelProperty(value = "机组")
//    private String extend9;
//
//    @ApiModelProperty(value = "清洁度等级")
//    private String extend10;
//
//    @ApiModelProperty(value = "储存级别")
//    private String extend11;
//
//    @ApiModelProperty(value = "包装类型")
//    private String extend12;
//
//    @ApiModelProperty(value = "是否进口物资")
//    private String extend13;
//
//    @ApiModelProperty(value = "是否包含大件")
//    private String extend14;
//
//    @ApiModelProperty(value = "供货商是否参加开箱检验")
//    private String extend15;
//
//    @ApiModelProperty(value = "状态")
//    private String extend16;
//
//    @ApiModelProperty(value = "发货人ID")
//    private String extend17;
//
//    @ApiModelProperty(value = "收货人ID")
//    private String extend18;
//
//    @ApiModelProperty(value = "装箱单编制人")
//    private String extend19;

    @ApiModelProperty(value = "物资ID")
    private String extend20;

//    @ApiModelProperty(value = "SAP物料号")
//    private String extend21;
//
//    @ApiModelProperty(value = "旧物料号")
//    private String extend22;
//
//    @ApiModelProperty(value = "VPRM编码")
//    private String extend23;

    @ApiModelProperty(value = "规格型号")
    private String extend24;

    @ApiModelProperty(value = "材质")
    private String extend25;

    @ApiModelProperty(value = "安全分级")
    private String extend26;

    @ApiModelProperty(value = "质保分级")
    private String extend27;

    @ApiModelProperty(value = "物料类型")
    private String extend28;

    @ApiModelProperty(value = "UP码")
    private String extend29;

//    @ApiModelProperty(value = "UP类型")
//    private String extend30;

    @ApiModelProperty(value = "部件编号")
    private String extend31;

//    @ApiModelProperty(value = "箱序号")
//    private String extend32;
//
//    @ApiModelProperty(value = "拆分件编码")
//    private String extend33;

    @ApiModelProperty(value = "制造号")
    private String extend34;

    @ApiModelProperty(value = "炉批号")
    private String extend35;

    @ApiModelProperty(value = "贮存有效期")
    private String extend36;

    @ApiModelProperty(value = "工程图号")
    private String extend37;

    @ApiModelProperty(value = "图纸版本")
    private String extend38;

    @ApiModelProperty(value = "部件序号")
    private String extend39;

    @ApiModelProperty(value = "供应商内部物资编码")
    private String extend40;

    @ApiModelProperty(value = "合格证号")
    private String extend41;

    @ApiModelProperty(value = "供应商物资编码")
    private String extend42;

    @ApiModelProperty(value = "供应商图号")
    private String extend43;

    @ApiModelProperty(value = "标准号")
    private String extend44;

    @ApiModelProperty(value = "规格型号参数 ")
    private String extend45;

    @ApiModelProperty(value = "制造厂")
    private String extend46;

    @ApiModelProperty(value = "制造厂参考号")
    private String extend47;

    @ApiModelProperty(value = "工程图项号")
    private String extend48;

    @ApiModelProperty(value = "EOMM号")
    private String extend49;

    @ApiModelProperty(value = "EOMR号")
    private String extend50;

//    @ApiModelProperty(value = "运营备件编码")
//    private String extend51;
//
//    @ApiModelProperty(value = "备注（移交）")
//    private String extend52;
//
//    @ApiModelProperty(value = "备件信息审核意见")
//    private String extend53;
//
//    @ApiModelProperty(value = "备注")
//    private String extend54;
//
//    @ApiModelProperty(value = "设备主体标识")
//    private String extend55;
//
//    @ApiModelProperty(value = "更新时间")
//    private String extend56;
//
//    @ApiModelProperty(value = "更新人")
//    private String extend57;
//
//    @ApiModelProperty(value = "创建人")
//    private String extend58;
//
//    @ApiModelProperty(value = "状态标识")
//    private String extend59;
//
    @ApiModelProperty(value = "保质期")
    private String extend60;
    @ApiModelProperty(value = "保养周期")
    private String extend61;
    @ApiModelProperty(value = "订单号")
    private String extend62;
    @ApiModelProperty(value = "行项目号")
    private String extend63;
    @ApiModelProperty(value = "箱号")
    private String extend64;
    @ApiModelProperty(value = "合同号")
    private String extend65;
    @ApiModelProperty(value = "合同名称")
    private String extend66;
    @ApiModelProperty(value = "箱内UP件数")
    private String extend67;
    @ApiModelProperty(value = "清洁度等级")
    private String extend68;
    @ApiModelProperty(value = "预计发货日期")
    private String extend69;
    @ApiModelProperty(value = "长(米)")
    private String extend70;
    @ApiModelProperty(value = "高(米)")
    private String extend71;
    @ApiModelProperty(value = "宽(米)")
    private String extend72;
    @ApiModelProperty(value = "运单备注")
    private String extend73;
    @ApiModelProperty(value = "SAP物料号")
    private String extend74;
    private String guaranteePeriod;
    private String caseWeight;
    private String caseCode;
    private String packageForm;
    private BigDecimal arrivalQty;
    private Date maintenanceDateNormal;
    private Date maintenanceDatePro;
    // 寿期过期时间
    private Date lifetimeDate;

    @ApiModelProperty(value = "工器具 - 管理状态备注")
    private String toolManageStatusRemark;

    @ApiModelProperty(value = "工器具 - 使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    private String toolUseReason;

    @ApiModelProperty(value = "工器具 - 技术参数")
    private String technicalSpecification;

    @ApiModelProperty(value = "工器具 - 维保有效期（维保到期时间）")
    private Date maintenanceValidDate;

    @ApiModelProperty(value = "工器具 - 来源单号")
    private String toolSourceReceiptCode;

    @ApiModelProperty(value = "工器具 - 来源单号抬头表id")
    private Long toolSourceReceiptHeadId;

    @ApiModelProperty(value = "工器具 - 来源单据类型")
    private Integer toolSourceReceiptType;

    @ApiModelProperty(value = "填充属性 - 暂存前序单据code")
    private String tempStorePreReceiptCode;

    @ApiModelProperty(value = "暂存到期日期")
    private Date tempStoreExpireDate;
    
    @ApiModelProperty(value = "填充属性 - 质检会签行项目备注信息")
    private String counterSignRemark;
    
    @ApiModelProperty(value = "填充属性 - 保养要求")
    private String mainRequirement;

    @ApiModelProperty(value = "采购方式 1:联合采购;2:自主采购")
    private Integer procurementMethod;

    @ApiModelProperty(value = "NCR编号")
    private String ncr;

    @ApiModelProperty(value = "期间")
    private Date postTime;

    @ApiModelProperty(value = "head表主键"  )
    private Long headId;
}