package com.inossem.wms.common.model.label.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 标签-单据与标签关联关系记录表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizLabelReceiptRel对象", description = "标签-单据与标签关联关系记录表")
@TableName("biz_label_receipt_rel")
public class BizLabelReceiptRel implements Serializable {

    private static final long serialVersionUID = 1090698747421516274L;
    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "标签id" , example = "157691964096513")
    private Long labelId;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据head表id" , example = "155163729920001")
    private Long receiptHeadId;

    @ApiModelProperty(value = "单据item表id" , example = "152412159541249")
    private Long receiptItemId;

    @ApiModelProperty(value = "单据bin表id" , example = "157691955707907")
    private Long receiptBinId;

    @ApiModelProperty(value = "前序单据类型" , example = "511")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据id" , example = "157691955707905")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前序单据行项目" , example = "157691955707906")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前序单据bid" , example = "0")
    private Long preReceiptBinId;

    @ApiModelProperty(value = "状态【暂定-根据具体项目】 10：代码盘 20:已码盘 30：已过门 40：已出库 50：待上架" , example = "10")
    private Integer status;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @TableField(exist = false)
    private Integer rid;

}
