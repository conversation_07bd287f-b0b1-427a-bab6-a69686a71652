package com.inossem.wms.common.model.bizdomain.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptContractReceivingItem", description = "合同收货行项目")
@TableName("biz_receipt_contract_receiving_item")
public class BizReceiptContractReceivingItem implements Serializable {

    private static final long serialVersionUID = -3095648602659073296L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "行号")
    private String rid;

    @ApiModelProperty(value = "合同头表ID")
    private Long headId;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前序单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据头id")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前序单据行项目id")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "品名")
    private String productName;

    @ApiModelProperty(value = "物料组ID", notes = "必填,关联物料组主数据")
    private Long matGroupId;

    @ApiModelProperty(value = "WBS id")
    private Long wbsId;

    @ApiModelProperty(value = "WBS编号")
    private String wbsNo;

    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    @ApiModelProperty(value = "合同数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "合同未清数量")
    private BigDecimal unContractQty;

    @ApiModelProperty(value = "可收货数量")
    private BigDecimal canDeliveryQty;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "不含税单价/框架协议单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "合同数量/框架协议数量", notes = "必填")
    private BigDecimal qty;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "资产卡片编码")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片名称")
    private String assetCardDesc;

    @ApiModelProperty(value = "资产卡片id")
    private Long assetCardId;

    @ApiModelProperty(value = "资产卡片子编码")
    private String assetCardSubCode;

    @ApiModelProperty(value = "物料凭证编号", example = "5211111111")
    private String matDocCode;


    private String matDocRid;

    @ApiModelProperty(value = "冲销凭证号", example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "过帐日期", example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "凭证时间", example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "物料凭证年度", example = "5211111111")
    private String matDocYear;

    @ApiModelProperty(value = "冲销凭证时间", example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期", example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "是否过账【1是，0否】")
    private Integer isPost;

    @ApiModelProperty(value = "是否冲销【1是，0否】")
    private Integer isWriteOff;

    @ApiModelProperty(value = "备注")
    private String itemRemark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;


    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "含税单价/框架协议单价")
    private BigDecimal taxPrice;


}
