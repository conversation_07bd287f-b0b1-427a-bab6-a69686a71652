package com.inossem.wms.common.model.bizdomain.stocktaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 库存盘点凭证抬头表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptStocktakingDocHead对象", description = "库存盘点抬头表")
@TableName("biz_receipt_stocktaking_doc_head")
public class BizReceiptStocktakingDocHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" )
    private Integer receiptType;

    @ApiModelProperty(value = "盘点凭证状态:10-草稿,90-已完成")
    private Integer receiptStatus;

    @ApiModelProperty(value = "盘点凭证描述")
    private String remark;

    @ApiModelProperty(value = "期间")
    private Date postTime;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;
}
