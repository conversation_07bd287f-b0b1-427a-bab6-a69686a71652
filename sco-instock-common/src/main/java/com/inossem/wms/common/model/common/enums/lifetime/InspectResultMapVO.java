package com.inossem.wms.common.model.common.enums.lifetime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 检定结果下拉返回对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "检定结果下拉返回对象", description = "检定结果下拉返回对象")
public class InspectResultMapVO implements Serializable {

    private static final long serialVersionUID = 3960650557275853661L;

    @ApiModelProperty(value = "检定结果")
    private Integer inspectResult;

    @ApiModelProperty(value = "检定结果描述")
    private String inspectResultI18n;

}
