package com.inossem.wms.common.util;

import java.math.BigDecimal;

/**
 * 数字类型对象工具类
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 2021/2/28
 */
public class UtilNumber {

    /**
     * 判断数值类型对象是否<b>为<code>null</code><b/>
     * @param num 数值类型对象，适用于所有Number类的子类
     * @return 如果num是<code>null</code>返回<code>true</code>, 否则返回<code>false</code>
     * <AUTHOR> <<EMAIL>>
     */
    public static boolean isNull(Number num) {
        return num == null;
    }

    /**
     * 判断数值类型对象是否<b>不为<code>null</code><b/>
     * @param num 数值类型对象，适用于所有Number类的子类
     * @return 如果num不是<code>null</code>返回<code>true</code>, 否则返回<code>false</code>
     * <AUTHOR> <<EMAIL>>
     */
    public static boolean isNotNull(Number num) {
        return num != null;
    }

    /**
     * 判断数值类型对象是否<b>为<code>null</code>或值为0<b/>
     * @param num 数值类型对象，适用于所有Number类的子类
     * @return 如果num是<code>null</code>或值为0, 返回<code>true</code>, 否则返回<code>false</code>
     * <AUTHOR> <<EMAIL>>
     */
    public static boolean isEmpty(Number num) {
        if (num == null) {
            return true;
        }
        if (num instanceof Byte) {
            return num.byteValue() == 0;
        } else if (num instanceof Short) {
            return num.shortValue() == 0;
        } else if (num instanceof Integer) {
            return num.intValue() == 0;
        } else if (num instanceof Long) {
            return num.longValue() == 0;
        } else if (num instanceof Float) {
            return num.floatValue() == 0;
        } else if (num instanceof Double) {
            return num.doubleValue() == 0;
        } else if (num instanceof BigDecimal) {
            return ((BigDecimal) num).compareTo(BigDecimal.ZERO) == 0;
        }
        return false;
    }

    /**
     * 判断数值类型对象是否<b>不为<code>null</code>或值不为0<b/>
     * @param num 数值类型对象，适用于所有Number类的子类
     * @return 如果num不是<code>null</code>或值为0, 返回<code>true</code>, 否则返回<code>false</code>
     * <AUTHOR> <<EMAIL>>
     */
    public static boolean isNotEmpty(Number num) {
        if (num == null) {
            return false;
        }
        if (num instanceof Byte) {
            return num.byteValue() != 0;
        } else if (num instanceof Short) {
            return num.shortValue() != 0;
        } else if (num instanceof Integer) {
            return num.intValue() != 0;
        } else if (num instanceof Long) {
            return num.longValue() != 0;
        } else if (num instanceof Float) {
            return num.floatValue() != 0;
        } else if (num instanceof Double) {
            return num.doubleValue() != 0;
        } else if (num instanceof BigDecimal) {
            return ((BigDecimal) num).compareTo(BigDecimal.ZERO) != 0;
        }
        return false;
    }

    /**
     * 判断一个数字是否为1
     * 
     * @param i 判断内容值
     * @return 是否为1
     */
    public static boolean valueIsTrue(Integer i) {
        return i != null && i.equals(1);
    }

    /**
     * 判断一个数字是否为0
     *
     * @param i 判断内容值
     * @return 是否为1
     */
    public static boolean valueIsZeroTrue(Integer i) {
        return i != null && i.equals(0);
    }

    /**
     * 判断数值类型对象是否是<b>正数<b/>
     * @param num 数值类型对象，适用于所有Number类的子类
     * @return 如果num是正数（不包含0）, 返回<code>true</code>, 否则返回<code>false</code>
     * <AUTHOR> <<EMAIL>>
     */
    @SuppressWarnings("unused")
    public static boolean isPositive(Number num) {
        if (num == null) {
            return false;
        }
        if (num instanceof Byte) {
            return num.byteValue() > 0;
        } else if (num instanceof Short) {
            return num.shortValue() > 0;
        } else if (num instanceof Integer) {
            return num.intValue() > 0;
        } else if (num instanceof Long) {
            return num.longValue() > 0;
        } else if (num instanceof Float) {
            return num.floatValue() > 0;
        } else if (num instanceof Double) {
            return num.doubleValue() > 0;
        } else if (num instanceof BigDecimal) {
            return ((BigDecimal) num).compareTo(BigDecimal.ZERO) > 0;
        }
        return false;
    }

    /**
     * 判断数值类型对象是否是<b>负数<b/>
     * @param num 数值类型对象，适用于所有Number类的子类
     * @return 如果num是负数（不包含0）, 返回<code>true</code>, 否则返回<code>false</code>
     * <AUTHOR> <<EMAIL>>
     */
    @SuppressWarnings("unused")
    public static boolean isNegative(Number num) {
        if (num == null) {
            return false;
        }
        if (num instanceof Byte) {
            return num.byteValue() < 0;
        } else if (num instanceof Short) {
            return num.shortValue() < 0;
        } else if (num instanceof Integer) {
            return num.intValue() < 0;
        } else if (num instanceof Long) {
            return num.longValue() < 0;
        } else if (num instanceof Float) {
            return num.floatValue() < 0;
        } else if (num instanceof Double) {
            return num.doubleValue() < 0;
        } else if (num instanceof BigDecimal) {
            return ((BigDecimal) num).compareTo(BigDecimal.ZERO) < 0;
        }
        return false;
    }

    /**
     * 金额转大写
     * @param num
     * @return
     */
    public static String numToStr(int num) {
        String[] chinese = new String[]{"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万"};
        String[] numChinese = new String[]{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String str = String.valueOf(num);
        char[] chars = str.toCharArray();
        StringBuffer sb = new StringBuffer();

        for (int i = 0; i < chars.length; ++i) {
            sb.append(numChinese[Integer.valueOf(String.valueOf(chars[i])).intValue()]).append(chinese[chars.length - i - 1]);
        }

        return sb.toString();
    }

    /**
     * 金额转大写
     * @param bigDecimal
     * @return
     */
    public static String bigDecimalToLocalStr(BigDecimal bigDecimal) {
        String[] chinese = new String[]{"", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "万"};
        String[] numChinese = new String[]{"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
        String[] afterChinese = new String[]{"角", "分","厘"};
        String str = String.valueOf(bigDecimal);
        String[] arr = str.split("\\.");
        char[] chars = arr[0].toCharArray();
        StringBuffer sb = new StringBuffer();

        for(int i = 0; i < chars.length; ++i) {
            sb.append(numChinese[Integer.valueOf(String.valueOf(chars[i])).intValue()]).append(chinese[chars.length - i - 1]);
        }

        if (arr.length == 1) {
            return sb.toString() + "元整";
        } else if (arr[1].length() > 3) {
            throw new IllegalArgumentException("人民币大写转换BigDecimal只能保留3位小数");
        } else {
            sb.append("元");
            char[] chars1 = arr[1].toCharArray();

            for(int i = 0; i < chars1.length; ++i) {
                sb.append(numChinese[Integer.valueOf(String.valueOf(chars1[i])).intValue()]).append(afterChinese[i]);
            }

            return sb.toString();
        }
    }


}
