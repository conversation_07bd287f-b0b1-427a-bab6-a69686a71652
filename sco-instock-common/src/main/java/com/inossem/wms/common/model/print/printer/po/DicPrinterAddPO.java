package com.inossem.wms.common.model.print.printer.po;

import com.inossem.wms.common.model.print.printer.dto.DicPrinterDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/***
 * <AUTHOR>
 * @date 2021-05-31 14:50:38
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "打印机主数据新增/修改入参类", description = "打印机主数据新增/修改入参类")
public class DicPrinterAddPO{

    private static final long serialVersionUID = -6661152208172473789L;

    @ApiModelProperty(value = "打印机主数据入参")
    private DicPrinterDTO dicPrinter;

}
