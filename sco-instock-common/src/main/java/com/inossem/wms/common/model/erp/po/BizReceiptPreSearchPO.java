package com.inossem.wms.common.model.erp.po;

import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 各单据获取erp查询条件
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-24
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "各单据获取erp查询条件", description = "各单据获取erp查询条件")
public class BizReceiptPreSearchPO {

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "采购订单号" , example = "4500000001")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "生产订单号" , example = "SC001")
    private String productionReceiptCode;

    @ApiModelProperty(value = "合同号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "需求计划单号" , example = "20201101001")
    private String demandPlanCode;

    @ApiModelProperty(value = "SAP订单创建开始时间" , example = "2021-05-11")
    private Date erpCreateStartTime;

    @ApiModelProperty(value = "SAP订单创建结束时间" , example = "2021-05-11")
    private Date erpCreateEndTime;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "采购退货标识 0-采购订单, 1-采购退货订单" , example = "0")
    private Integer isReturnFlag;

    @ApiModelProperty(value = "借用出库单单号,维修出库单单号")
    private String outputReceiptCode;

    @ApiModelProperty(value = "差异类型")
    private Integer differentType;

    @ApiModelProperty(value = "质检会签单单号")
    private String inspectReceiptCode;

    @ApiModelProperty(value = "合同描述" , example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "借用人id" , example = "1L")
    private Long borrowUserId;

    @ApiModelProperty(value = "是否来自ncn菜单", example = "1L")
    private Integer fromNcn;

    @ApiModelProperty(value = "采购订单号")
    private String referReceiptCode;

    @ApiModelProperty(value = "合同子类型")
    private List<Integer> contractSubTypeList;

    @ApiModelProperty(value = "单据状态")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "采购类型")
    private Integer purchaseType;


    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "甲方名称")
    private List<Integer> firstPartyList;


    private String contractRid;

    List<BizReceiptContractItemDTO> itemDTOList;

    @ApiModelProperty(value = "是否门到门送货")
    private Integer isD2dDelivery;
}
