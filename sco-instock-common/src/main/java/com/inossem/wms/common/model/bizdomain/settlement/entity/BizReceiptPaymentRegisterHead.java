package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentRegisterHead", description = "付款登记抬头表")
@TableName("biz_receipt_payment_register_head")
public class BizReceiptPaymentRegisterHead implements Serializable {
    private static final long serialVersionUID = -3539120111074364348L;


    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "付款结算单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "结算单id", example = "PD01000216")
    private Long settlementId;

    @ApiModelProperty(value = "登记描述", example = "PD01000216")
    private String registerDesc;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;


    @ApiModelProperty(value = "已支付合同币种金额", example = "2500")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "未支付合同币种金额", example = "2500")
    private BigDecimal notPaidAmount;


    @ApiModelProperty(value = "本次支付卢比金额", example = "1")
    private BigDecimal qty;

    @ApiModelProperty(value = "本次支付日期", example = "1")
    private Date payDateTime;

    @ApiModelProperty(value = "收款单位开户行")
    private String payeeBankName;

    @ApiModelProperty(value = "收款单位账号")
    private String payeeAccountNumber;

    @ApiModelProperty(value = "付款方式(1电汇/TT、2信用证/LC、3其他other)")
    private Integer paymentType;

    @ApiModelProperty(value = "付款说明")
    private String paymentDesc;

}
