package com.inossem.wms.common.model.masterdata.budget.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "年度预算", description = "年度预算")
@TableName("dic_annual_budget")
public class DicAnnualBudget implements Serializable {
    private static final long serialVersionUID = 8397783812196374513L;


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "预算年份")
    private String year;

    @ApiModelProperty(value = "所属预算分类id")
    private Long budgetClassifyId;

    @ApiModelProperty(value = "所属预算科目id")
    private Long budgetSubjectId;

    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;

    @ApiModelProperty(value = "已有合同金额")
    private BigDecimal existingContractAmount;

    @ApiModelProperty(value = "已提报预算金额")
    private BigDecimal existingPurchaseAmount;

    @ApiModelProperty(value = "已提报-未生成合同金额")
    private BigDecimal notGenerateContractAmount;

    @ApiModelProperty(value = "可提报最大金额")
    private BigDecimal canDeclareAmount;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
