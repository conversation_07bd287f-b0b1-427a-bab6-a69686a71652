package com.inossem.wms.common.model.approval.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 对应activiti model
 * 
 * <AUTHOR>
 * @date 2021/3/15 16:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "模型传输对象", description = "模型传输对象")
public class ModelDTO {
    @ApiModelProperty(value = "模型id", example = "1")
    private String id;

    @ApiModelProperty(value = "名称", example = "名称")
    private String name;

    @ApiModelProperty(value = "key", example = "key")
    private String key;

    @ApiModelProperty(value = "类别", example = "category")
    private String category;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-10", required = false, name = "createTime")
    private Date createTime;

    @ApiModelProperty(value = "更新时间",  example = "2021-05-10")
    private Date lastUpdateTime;

    @ApiModelProperty(value = "版本", example = "1")
    private Integer version;

    @ApiModelProperty(value = "部署id", example = "deploymentId")
    private String deploymentId;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据类型名称", example  = "领料出库", required = true)
    private String receiptTypeI18n;
}
