package com.inossem.wms.common.model.masterdata.mat.info.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物资返运行项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizMaterialReturnItem对象", description="物资返运行项目表")
@TableName("biz_material_return_item")
public class BizMaterialReturnItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "入库单行项目序号")
    private String referReceiptRid;

    @ApiModelProperty(value = "采购订单号")
    private String referReceiptCode;

    @ApiModelProperty(value = "采购订单head表id")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "采购订单item表id")
    @RlatAttr(rlatTableName = "erp_purchase_receipt_item", sourceAttrName = "contractCode,receiptQty", targetAttrName = "contractCode,receiptQty")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    private Long unitId;


    @ApiModelProperty(value = "填充功能属性-合同号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "返运数量" , example = "10")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "包装形式" , example = "10")
    private String packageStyle;

    @ApiModelProperty(value = "返运原因" , example = "10")
    private String returnReason;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "多供的物料编码")
    private String extraMatCode;

    @ApiModelProperty(value = "多供的物料描述")
    private String extraMatName;

    @ApiModelProperty(value = "多供物料的单位id")
    private Long extraUnitId;

    @ApiModelProperty(value = "多供物料填写的需求人")
    private String extraApplyUser;


}
