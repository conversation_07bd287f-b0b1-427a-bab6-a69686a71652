package com.inossem.wms.common.model.common.enums.register;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 外观检查下拉返回对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "外观检查下拉返回对象", description = "外观检查下拉返回对象")
public class VisualCheckMapVO implements Serializable {

    private static final long serialVersionUID = -4060436409010251949L;

    @ApiModelProperty(value = "外观检查")
    private Integer visualCheck;

    @ApiModelProperty(value = "外观检查描述")
    private String visualCheckI18n;

}
