package com.inossem.wms.common.model.bizdomain.apply.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 查询领料单入参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="查询领料单入参", description="查询领料单入参")
public class BizReceiptApplySearchReqPlanPO implements Serializable {

    private static final long serialVersionUID = 8822125030297256947L;

    @ApiModelProperty(value = "单据号" , example = "4500000001")
    private String receiptCode;

    @ApiModelProperty(value = "领料单描述" , example = "单据备注")
    private String remark;

    @ApiModelProperty(value = "领料单创建人code" , example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "工单")
    private String workOrder;

    private List<Long> itemIdList;
    private List<String> ftyCodeList;

    @ApiModelProperty(value = "状态备注(已结算、已退库)")
    private String statusRemark;
}
