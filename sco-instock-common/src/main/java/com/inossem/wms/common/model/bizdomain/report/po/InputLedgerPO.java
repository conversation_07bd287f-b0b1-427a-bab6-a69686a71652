package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 入库台账报表 PO
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InputLedgerPO", description="InputLedgerPO")
public class InputLedgerPO extends PageCommon {

    @ApiModelProperty(value = "送货通知单号")
    private String deliveryCode;
    @ApiModelProperty(value = "合同号")
    private String contractCode;
    @ApiModelProperty(value = "发运批次号")
    private String sendBatch;
    @ApiModelProperty(value = "物料编码")
    private String matCode;
    @ApiModelProperty(value = "物流清关单号")
    private String logisticsCode;
    @ApiModelProperty(value = "到货登记单号")
    private String registerCode;
    @ApiModelProperty(value = "质检会签单号")
    private String inspectSignCode;
    @ApiModelProperty(value = "验收入库单号")
    private String inputCode;
    @ApiModelProperty(value = "验收入库过账日期")
    private Date postingDateStart;
    private Date postingDateEnd;

}
