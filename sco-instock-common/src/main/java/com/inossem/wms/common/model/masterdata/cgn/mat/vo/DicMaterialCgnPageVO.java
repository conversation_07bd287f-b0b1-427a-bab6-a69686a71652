package com.inossem.wms.common.model.masterdata.cgn.mat.vo;

import java.util.Date;

import com.inossem.wms.common.annotation.RlatAttr;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目物料码管理
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@TableName("dic_material_cgn")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "项目物料码管理分页")
public class DicMaterialCgnPageVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "项目物料码/物资编码")
    private String cgnMatCode;

    @ApiModelProperty(value = "项目物料简称")
    private String cgnMatName;

    @RlatAttr(rlatTableName = "dic_margin_category", sourceAttrName = "marginCategoryCode", targetAttrName = "marginCategoryCode")
    @ApiModelProperty(value = "裕量计算分类id")
    private Long marginCategoryId;

    @ApiModelProperty(value = "裕量计算分类编码")
    private String marginCategoryCode;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
