package com.inossem.wms.common.model.bizdomain.collection.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 *  采集任务列表查询 po
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptCollectionTaskSearchPO", description="BizReceiptCollectionTaskSearchPO")
public class BizReceiptCollectionTaskSearchPO extends PageCommon {

    @ApiModelProperty(value = "查询条件")
    private String queryCondition;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "电子秤ID")
    private String electronicScaleId;

    @ApiModelProperty(value = "仓位编码")
    private String binCode;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
