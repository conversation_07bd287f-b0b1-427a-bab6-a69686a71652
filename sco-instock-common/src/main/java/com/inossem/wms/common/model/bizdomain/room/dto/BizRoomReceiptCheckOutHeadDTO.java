package com.inossem.wms.common.model.bizdomain.room.dto;

import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 住房退订单抬头DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomReceiptCheckOutHeadDTO", description="住房退订单抬头DTO")
public class BizRoomReceiptCheckOutHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /* ********************** 扩展字段开始 *************************/

    @SonAttr(sonTbName = "biz_room_receipt_check_out_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "住房退订单行项目列表")
    List<BizRoomReceiptCheckOutItemDTO> itemList;

    @SonAttr(sonTbName = "biz_room_receipt_check_out_room", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "住房退订单房间列表")
    List<BizRoomReceiptCheckOutRoomDTO> roomList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态名称")
    private String receiptStatusI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 住房退订：9304")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "房间使用结束时间（退订时间）")
    private Date endUsageTime;

    @ApiModelProperty(value = "退订原因")
    private String reqReason;


}
