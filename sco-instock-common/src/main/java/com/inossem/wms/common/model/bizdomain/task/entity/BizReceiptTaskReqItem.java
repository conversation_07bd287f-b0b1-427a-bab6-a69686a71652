package com.inossem.wms.common.model.bizdomain.task.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 作业请求 item
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

@TableName("biz_receipt_task_req_item")
@ApiModel(value = "BizReceiptTaskReqItem对象", description = "")
public class BizReceiptTaskReqItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "保养备注")
    private String mainRequirement;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "作业申请行项目rid" , example = "1")
    private String rid;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "前续单据headId" , example = "151559623213057")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据行id" , example = "151565235191810")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续订单bin id" , example = "144980056277019")
    private Long preReceiptBinId;


    @ApiModelProperty(value = "参考单id" , example = "152044279234561")
    private Long referReceiptHeadId;


    @ApiModelProperty(value = "参考单据行id" , example = "152044279234562")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据配货id" , example = "1")
    private Long referReceiptBinId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "已作业数量" , example = "5")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "基本计量单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证号" , example = "511111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行项目" , example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "推荐仓位id" , example = "152407797465099")
    private Long recommendBinId;

    @ApiModelProperty(value = "特性code" , example = "biz_batch_info.batch_erp,biz_batch_info.spec_stock_code")
    private String specCode;

    @ApiModelProperty(value = "特性值" , example = "111111,1")
    private String specValue;

    @ApiModelProperty(value = "特性库存类型" , example = "Q")
    private String specStock;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
    private String specStockCode;

    @ApiModelProperty(value = "物料组id" )
    private Long matGroupId;

    @ApiModelProperty(value = "合并下架请求单id" )
    private Long mergeReqHeadId;

    @ApiModelProperty(value = "合并下架请求单行id" )
    private Long mergeReqItemId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

}
