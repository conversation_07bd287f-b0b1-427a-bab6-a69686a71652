package com.inossem.wms.common.model.org.location.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "库存地点查询入参类", description = "库存地点查询入参类")
@Data
public class DicStockLocationSearchPO extends PageCommon {

    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "库存地点描述" , example = "英诺森001")
    private String locationName;

}
