package com.inossem.wms.common.model.masterdata.storagebin.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "仓位查询入参类", description = "仓位查询入参类")
@Data
public class DicWhStorageBinSearchPO extends PageCommon {

    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "仓库编码" , example = "S01")
    private String whCode;

    @ApiModelProperty(value = "仓库ID" , example = "155336845623297")
    private Long whId;

    @ApiModelProperty(value = "物料ID" , example = "155336845623297")
    private Long matId;

    @ApiModelProperty(value = "仓位编码" , example = "00")
    private String binCode;

    @ApiModelProperty(value = "存储类型编码" , example = "801")
    private String typeCode;

    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @ApiModelProperty(value = "存储类型ID列表" , example = "[155336768028673, 155336768028674]")
    private List<Long> typeIdList;

    @ApiModelProperty(value = "存储区" , example = "s008")
    private Long sectionId;

    @ApiModelProperty(value = "存储区编码" , example = "s008")
    private String sectionCode;

    @ApiModelProperty(value = "单据类型" , example = "801")
    private String receiptType;

    @ApiModelProperty(value = "库存状态" , example = "10")
    private Integer stockStatus;

    @ApiModelProperty(value = "是否超重")
    private String overWeight;
}
