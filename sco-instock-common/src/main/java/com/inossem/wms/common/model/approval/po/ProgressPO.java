package com.inossem.wms.common.model.approval.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/3/18 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ProgressPO {
    @NotBlank(message = "流程实例ID不能为空!")
    @ApiModelProperty(value = "流程实例ID" , example = "282501" , required = true)
    private String processInstanceId;
}