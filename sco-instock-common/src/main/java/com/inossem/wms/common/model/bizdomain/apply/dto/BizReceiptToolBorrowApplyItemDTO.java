package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/30 12:01
 * @desc BizReceiptToolBorrowApplyItemDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "专用工器具借用行项目传输对象", description = "专用工器具借用行项目传输对象")
public class BizReceiptToolBorrowApplyItemDTO implements Serializable {
    private static final long serialVersionUID = 6583279736857158594L;


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head主键", example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键", example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型", example = "240")
    private Integer referReceiptType;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "batchInfoDTO")
    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "sourceTypeCode,sourceTypeName")
    @ApiModelProperty(value = "源存储类型ID")
    private Long sourceTypeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "sourceBinCode")
    @ApiModelProperty(value = "源仓位ID")
    private Long sourceBinId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "targetTypeCode,targetTypeName")
    @ApiModelProperty(value = "目标存储类型ID")
    private Long targetTypeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "targetBinCode")
    @ApiModelProperty(value = "目标仓位ID")
    private Long targetBinId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "借出数量或归还数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "已归还数量")
    private BigDecimal returnedQty;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "延期原因")
    private String delayReason;

    @ApiModelProperty(value = "归还状态，1合格 0不合格,默认合格")
    private Integer returnStatus;

    @ApiModelProperty(value = "归还状态说明")
    private String returnStatusExplain;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    //---------------------------------------------------------------------------------------//

    @ApiModelProperty(value = "操作数量")
    private BigDecimal operationQty;

    @ApiModelProperty(value = "填充属性 - 批次")
    private BizBatchInfoDTO batchInfoDTO;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销", example = "10")
    private String itemStatusI18n;

    @ApiModelProperty(value = "填充属性 - 仓库编码", example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述", example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 接收库存地点", example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name", example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 计量单位编码", example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称", example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 源存储类型code")
    private String sourceTypeCode;

    @ApiModelProperty(value = "填充属性 - 源存储类型name")
    private String sourceTypeName;

    @ApiModelProperty(value = "填充属性 - 目标存储类型code")
    private String targetTypeCode;

    @ApiModelProperty(value = "填充属性 - 目标存储类型name")
    private String targetTypeName;

    @ApiModelProperty(value = "填充属性 - 源仓位code")
    private String sourceBinCode;

    @ApiModelProperty(value = "填充属性 - 目标仓位code")
    private String targetBinCode;

    @ApiModelProperty(value = "行项目列表")
    List<BizLabelReceiptRelDTO> labelDataList;

    // 单据关联标签
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptItemId")
    private List<BizLabelReceiptRelDTO> relDTOList;
    @SonAttr(sonTbName = "biz_receipt_assemble", sonTbFkAttrName = "receiptItemId")
    private List<BizReceiptAssembleDTO> assembleDTOList;
}
