package com.inossem.wms.common.util.excel;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.util.excel.pdf.Excel2Pdf;
import com.inossem.wms.common.util.excel.pdf.ExcelObject;
import com.itextpdf.text.DocumentException;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <p>
 * Pdf转换工具类(主要用法)
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
public class UtilPdf {

    public static void excelToPdf(String xlsFileName, String pdfFileName) {
        try {
            FileInputStream fis1 = new FileInputStream(new File(xlsFileName));
            FileOutputStream fos = new FileOutputStream(new File(pdfFileName));
            Excel2Pdf pdf = new Excel2Pdf(new ExcelObject(xlsFileName, fis1), fos);
            pdf.convert();
        } catch (DocumentException | IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }
}
