package com.inossem.wms.common.model.auth.rel.vo;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.auth.rel.dto.SysResourcesDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资源配置返回对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "资源配置返回对象", description = "资源配置返回对象")
public class SysResourcesVO implements Serializable {

    private static final long serialVersionUID = -9101151057475632483L;

    /* ********************** 扩展字段 *************************/

    /**
     * 菜单集合
     */
    @ApiModelProperty(value = "菜单集合")
    private SysResourcesDTO menu;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", name = "userId", example = "1")
    private Long userId;

    /**
     * 用户名编码
     */
    @ApiModelProperty(value = "用户编码" , example = "Admin")
    private String userCode;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户编码" , example = "管理员")
    private String userName;

    /**
     * 部门主键
     */
    @ApiModelProperty(value = "部门主键"  , example = "145230623997953")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门"  )
    private String deptCode;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门"  )
    private String deptName;

    /**
     * 公司主键
     */
    @ApiModelProperty(value = "公司主键"  , example = "145230623997953")
    private Long corpId;

    /**
     * 公司描述
     */
    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "功能编号" , example = "menu" )
    private String resourcesCode;

    @ApiModelProperty(value = "功能名称" , example = "主菜单管理" )
    private String resourcesName;

    @ApiModelProperty(value = "功能图标" , example = "iconxitongguanli" )
    private String resourcesIcon;

    @ApiModelProperty(value = "Java权限的路径" , example = "" )
    private String resourcesJavaPath;

    @ApiModelProperty(value = "web权限的路径" , example = "menu/index.vue" )
    private String resourcesWebPath;

    @ApiModelProperty(value = "web的url" , example = "menu" )
    private String resourcesUrl;

    @ApiModelProperty(value = "web的url" , example = "" )
    private String activeMenu;

    @ApiModelProperty(value = "面包屑激活" , example = "1" )
    private Integer activeBreadcrumb;

    @ApiModelProperty(value = "上一级模块ID" , example = "153866890969089" )
    private Long parentId;

    @ApiModelProperty(value = "菜单显示顺序" , example = "1" )
    private Integer displayIndex;

    @ApiModelProperty(value = "是否可用,1可用,0不可用" , example = "1" )
    private Integer enabled;

    @ApiModelProperty(value = "资源类型,0 web,1 android" , example = "0" )
    private Integer resourcesType;

    @ApiModelProperty(value = "首页快捷方式,0 不可选,1可选" , example = "1" )
    private Integer shortcut;

    @ApiModelProperty(value = "待办项 0不可选 1 可选" , example = "1" )
    private Integer todolist;

    @ApiModelProperty(value = "待办项目sql", example = "")
    private String todolistSql;

    @ApiModelProperty(value = "待办状态  例(10,20,30)" , example = "10")
    private String todolistReceiptStatus;

    @ApiModelProperty(value = "0-需要授权访问，1-登录后就可以访问，3-匿名访问" , example = "0")
    private Integer permissionType;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
