package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentSettlementHeadDTO", description = "付款结算")
public class BizReceiptPaymentSettlementItemDTO implements Serializable {
    private static final long serialVersionUID = -1357226878628621521L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "biz_receipt_payment_plan_head", sourceAttrName = "*", targetAttrName = "paymentPlanHeadDTO")
    @ApiModelProperty(value = "付款计划抬头id", example = "157490654281729")
    private Long paymentPlanId;

//    @RlatAttr(rlatTableName = "biz_receipt_input_head", sourceAttrName = "*", targetAttrName = "inputHeadDTO")
    @ApiModelProperty(value = "入库单抬头id", example = "157490654281729")
    private Long inputHeadId;

    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "*", targetAttrName = "deliveryNoticeHeadDTO")
    @ApiModelProperty(value = "托收po 批次 行项目id ", example = "157490654281729")
    private Long deliveryHeadId;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "填充属性-离岸送货单编码")
    private String deliveryCode;

    @ApiModelProperty(value = "是否计划外", example = "2500")
    private Integer isBeyondPlan;


    private BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO;

    private BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO;

//    private BizReceiptInputHeadDTO inputHeadDTO;
}
