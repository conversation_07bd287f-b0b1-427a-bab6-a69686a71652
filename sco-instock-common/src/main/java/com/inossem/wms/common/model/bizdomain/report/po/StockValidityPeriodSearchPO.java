package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/9/9 10:49
 * @desc StockValidityPeriodSearchPO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存有效期预警 查询入参传输对象", description = "库存有效期预警 查询入参")
public class StockValidityPeriodSearchPO extends PageCommon {

    @ApiModelProperty(value = "主设备编码")
    private String mainMatCode;

    @ApiModelProperty(value = "主设备编码")
    private List<String> mainMatCodeList;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料编码")
    private List<String> matCodeList;

    @ApiModelProperty(value = "物资编码")
    private String extend20;

    @ApiModelProperty(value = "物资编码")
    private List<String> extend20List;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "功能位置码")
    private List<String> functionalLocationCodeList;

    @ApiModelProperty(value = "up码")
    private String extend29;

    @ApiModelProperty(value = "up码")
    private List<String> extend29List;

    @ApiModelProperty(value = "箱号")
    private String caseCode;

    @ApiModelProperty(value = "箱号")
    private List<String> caseCodeList;

    @ApiModelProperty(value = "生产日期开始")
    private Date productionDateStart;

    @ApiModelProperty(value = "生产日期结束")
    private Date productionDateEnd;

    @ApiModelProperty(value = "到期日期开始")
    private Date lifeTimeDateStart;

    @ApiModelProperty(value = "到期日期结束")
    private Date lifeTimeDateEnd;

    @ApiModelProperty(value = "临期天数开始")
    private Integer closeLifeStart;

    @ApiModelProperty(value = "临期天数结束")
    private Integer closeLifeEnd;

    @ApiModelProperty(value = "临期天数")
    private Integer closeLife;

    @ApiModelProperty(value = "规格型号")
    private String extend24;

    // 0  “全部”、
    // 1  “已过期”、
    // 2  “1个月内到期”、
    // 3  “3个月内到期”、
    // 4  “3个月以上到期”
    @ApiModelProperty(value = "到期类型，")
    private Integer closeType;

    @ApiModelProperty(value = "采购包")
    private String extend2;

    @ApiModelProperty(value = "采购包")
    private List<String> extend2List;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单号")
    private List<String> purchaseReceiptCodeList;

    private Integer isSafe;


}
