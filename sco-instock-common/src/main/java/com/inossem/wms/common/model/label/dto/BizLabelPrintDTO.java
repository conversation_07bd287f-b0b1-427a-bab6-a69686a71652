package com.inossem.wms.common.model.label.dto;

import com.inossem.wms.common.model.print.label.LabelReceiptRegisterBox;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> wang
 * <p>
 *     领料申请单打印入参
 * </p>
 * @date 2022/5/31 11:13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "打印传输对象", description = "打印传输对象")
public class BizLabelPrintDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单据抬头主键", required = true)
    private Long headId;

    @ApiModelProperty(value = "数据列表")
    private List<?> labelBoxList;

    @ApiModelProperty(value = "printerIp打印机IP" , example = "127.0.0.1")
    private String printerIp;

    @ApiModelProperty(value = "printerPort打印机端口" , example = "6100")
    private Integer printerPort;

    @ApiModelProperty(value = "printerIp打印机IP" , example = "127.0.0.1")
    private String printerIp1;

    @ApiModelProperty(value = "printerPort打印机端口" , example = "6100")
    private Integer printerPort1;

    @ApiModelProperty(value = "printerIp打印机IP" , example = "127.0.0.1")
    private String printerIp2;

    @ApiModelProperty(value = "printerPort打印机端口" , example = "6100")
    private Integer printerPort2;

    @ApiModelProperty(value = "printerIp打印机IP" , example = "127.0.0.1")
    private String printerIp3;

    @ApiModelProperty(value = "printerPort打印机端口" , example = "6100")
    private Integer printerPort3;

    @ApiModelProperty(value = "printer打印机份数" , example = "127")
    private Integer printNum;

    @ApiModelProperty(value = "printerIsPortable是否是便携式普通打印机 0：否  1：是" , example = "0")
    private Integer printerIsPortable;

    @ApiModelProperty(value = "1：RFID抗金属  2：RFID非抗金属 3：普通标签" , example = "3")
    private Integer labelType;

    List<PrintItemDTO> printItemDTOList;

}
