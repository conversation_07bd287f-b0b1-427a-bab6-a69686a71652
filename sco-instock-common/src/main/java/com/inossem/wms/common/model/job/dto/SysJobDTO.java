package com.inossem.wms.common.model.job.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/3/3 17:26
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SysJob对象", description = "定时任务调度表")
public class SysJobDTO implements Serializable {

    /* ********************** 扩展字段开始 *************************/

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "任务ID", example = "152634667368449", required = true)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "任务名称" , example = "test")
    private String jobName;

    @ApiModelProperty(value = "任务组名" , example = "1")
    private String jobGroup;

    @ApiModelProperty(value = "调用目标字符串" , example = "com.inossem.wms.system.job.controller.JobController.test")
    private String invokeTarget;

    @ApiModelProperty(value = "cron执行表达式" , example = "0 * 17 * * ?")
    private String cronExpression;

    @ApiModelProperty(value = "计划执行错误策略（1立即执行 2执行一次 3放弃执行）" , example = "1")
    private String misfirePolicy;

    @ApiModelProperty(value = "是否并发执行（0允许 1禁止）" , example = "1")
    private String concurrent;

    @ApiModelProperty(value = "状态（0正常 1暂停）" , example = "0")
    private String status;

    @ApiModelProperty(value = "备注信息" , example = "备注信息")
    private String remark;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;


}
