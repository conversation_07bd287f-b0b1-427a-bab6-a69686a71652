package com.inossem.wms.common.model.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2021/03/02 19:58
 */
@TableName("metadata")
@Data
public class Metadata {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "模块key" , example = "1")
    private String moduleKey;

    @ApiModelProperty(value = "包名" , example = "String")
    private String packageName;

    @ApiModelProperty(value = "模板名" , example = "String")
    private String templateName;

    @ApiModelProperty(value = "表" , example = "String")
    private String tableName;

    @ApiModelProperty(value = "类名" , example = "String")
    private String className;

    @ApiModelProperty(value = "元数据类型" , example = "String")
    private String metadataType;

    @ApiModelProperty(value = "工程中的-maven模块名称后的--包前缀" , example = "String")
    private String projectPackageName;

    @ApiModelProperty(value = "工程中的-maven模块名称" , example = "String")
    private String projectModel;

    @ApiModelProperty(value = "实体备注名称" , example = "String")
    private String entityName;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

}
