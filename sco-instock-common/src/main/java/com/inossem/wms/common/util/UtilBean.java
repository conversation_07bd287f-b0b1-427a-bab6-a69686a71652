package com.inossem.wms.common.util;

import lombok.Cleanup;
import lombok.NonNull;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;

import java.io.*;

/**
 * <p>
 * UtilBean是设计用于提供JavaBean对象的常用方法，包括属性值复制等<br/>
 * 代替原有InStock中的UtilBean.getBean方法
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2020-08-04
 */
public class UtilBean {

    /**
     * 将一个对象的属性值通过set&get方法，覆盖到另一个对象的同名属性中
     * 如果出现属性名相同 但是 泛型不同的集合字段时 会出现强制转换的数据问题
     *
     * @param source 源对象
     * @param target 目标对象
     * <AUTHOR> <<EMAIL>>
     */
    public static void copy(@NonNull Object source, @NonNull Object target) {
        // 使用Spring提供的BeanUtils提供的方法
        BeanUtils.copyProperties(source, target);
    }

    /**
     * 取得一个对象的新类型的实例并将源对象的同名属性覆盖到新对象中
     * 如果出现属性名相同 但是 泛型不同的集合字段时 会出现强制转换的数据问题
     *
     * @param source 源对象
     * @param targetClass 目标对象
     * @return 新类型的实例
     */
    @SneakyThrows
    public static <T> T newInstance(Object source, Class<T> targetClass) {
        T target = targetClass.getDeclaredConstructor().newInstance();
        if (source != null) {
            UtilBean.copy(source, target);
        }
        return target;
    }

    /**
     * 对象深拷贝工具方法 实现方式为先将源对象序列化再反序列化回来
     *
     * @param sourceObject 源对象
     * @param <T> 源对象泛型
     * @return 新对象 与源对象属性值相同
     * <AUTHOR> <<EMAIL>>
     */
    @SneakyThrows
    @SuppressWarnings("unchecked")
    public static <T> T deepCopy(@NonNull T sourceObject) {
        @Cleanup
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        @Cleanup
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(outputStream);
        objectOutputStream.writeObject(sourceObject);
        @Cleanup
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        @Cleanup
        ObjectInputStream objectInputStream = new ObjectInputStream(inputStream);
        return (T) objectInputStream.readObject();
    }

    /**
     * 对象深拷贝工具方法 实现方式为先将源对象序列化再反序列化回来 本方法针对目标对象与源对象类型不同的情况 在反序列化之后做了一次浅拷贝的类型转换
     * 如果出现属性名相同 但是 泛型不同的集合字段时 会出现强制转换的数据问题
     *
     * @param sourceObject 源对象
     * @param targetClass 目标对象class对象
     * @param <S> 源对象泛型
     * @param <T> 目标对象泛型
     * @return 目标对象
     */
    public static <S extends Serializable, T extends Serializable> T deepCopyNewInstance(@NonNull S sourceObject, Class<T> targetClass) {
        S cloneObject = deepCopy(sourceObject);
        return UtilBean.newInstance(cloneObject, targetClass);
    }

}
