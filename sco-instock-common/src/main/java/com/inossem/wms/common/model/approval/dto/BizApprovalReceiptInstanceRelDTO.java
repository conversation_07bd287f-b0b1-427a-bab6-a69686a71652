package com.inossem.wms.common.model.approval.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 审批-单据与审批实例关系传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "单据与审批实例关系传输对象", description = "单据与审批实例关系传输对象")
public class BizApprovalReceiptInstanceRelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/
    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人", example = "Admin", required = true)
    private String assignee;

    /**
     * 任务名
     */
    @ApiModelProperty(value = "任务名", example = "领料出库", required = true)
    private String name;

    /**
     * 单据类型名称
     */
    @ApiModelProperty(value = "单据类型名称", example  = "领料出库", required = true)
    private String receiptTypeI18n;

    /**
     * 发起人name
     */
    @ApiModelProperty(value = "发起人code", example = "Admin", required = true)
    private String userCode;

    /**
     * 发起人name
     */
    @ApiModelProperty(value = "发起人name", example  = "Admin", required = true)
    private String userName;

    /**
     * 部门id
     */
    @RlatAttr(rlatTableName = "sys_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    @ApiModelProperty(value = "部门id", example = "100", required = true)
    private String deptId;

    /**
     * 部门code
     */
    @ApiModelProperty(value = "部门id", example = "1100", required = true)
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", example = "采购部", required = true)
    private String deptName;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = true)
    private Long receiptHeadId;

    @ApiModelProperty(value = "单据code", example = "RK0001000634", required = true)
    private String receiptCode;

    @ApiModelProperty(value = "流程实例ID" , example = "282501" , required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "审批状态0 未审批 10审批通过 11审批未通过")
    private Integer approveStatus;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "userCode,userName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "发起人用户信息")
    private CurrentUser initiator;

    @ApiModelProperty(value = "是否废弃 1：废弃，0或空或其他：未废弃")
    private Integer isDiscard;

}
