package com.inossem.wms.common.enums.dataFill;

import lombok.Getter;

/**
 * 填充数据类型
 * 
 * <AUTHOR>
 */
public enum EnumDataFillType {

    /** 填充数据对象的关联属性和父子属性 */
    FILL_ATTR(0),
    /** 针对单个或多个数据对象的父子属性填充 */
    FILL_SON(1),
    /** 针对单个或多个数据对象的关联属性填充 */
    FILL_RLAT(2),
    /** 不做任何填充 */
    FILL_NOT(3);

    EnumDataFillType(Integer value) {
        this.intValue = value;
        this.strValue = value.toString();
    }

    @Getter
    private final Integer intValue;
    @Getter
    private final String strValue;
}
