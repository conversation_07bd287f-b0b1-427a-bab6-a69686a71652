package com.inossem.wms.common.model.masterdata.base.init;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 表-列 jiayl
 */
@Data
public class ColumnBase {

    /** 列名 */
    @ApiModelProperty(value = "列名" , example = "id")
    private String columnName;
    /** 排序位置 */
    @ApiModelProperty(value = "排序位置" , example = "1")
    private int position;
    /** 是否可为空 */
    @ApiModelProperty(value = "是否可为空" , example = "0")
    private Byte isNullAble;
    /** 类型 */
    @ApiModelProperty(value = "类型" , example = "input")
    private String dataType;
    /** 最大长度 */
    @ApiModelProperty(value = "最大长度" , example = "100")
    private Integer maximumLength;
    /** 列备注 */
    @ApiModelProperty(value = "列备注" , example = "列备注")
    private String columnComment;
    /** 是否主键 */
    @ApiModelProperty(value = "是否主键" , example = "1")
    private Byte isPRI;
    /** 所属表 */
    @ApiModelProperty(value = "所属表" , example = "biz_receipt_input_head")
    private String tableName;
    /** 所属实例 */
    @ApiModelProperty(value = "所属实例" , example = "BizReceiptInputHead")
    private String tableSchema;

}
