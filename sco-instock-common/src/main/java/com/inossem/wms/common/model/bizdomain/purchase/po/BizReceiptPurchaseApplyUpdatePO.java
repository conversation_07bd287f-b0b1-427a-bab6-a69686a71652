package com.inossem.wms.common.model.bizdomain.purchase.po;

import java.util.List;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采购申请更新PO
 */
@Data
@ApiModel(value="采购申请更新PO", description="采购申请更新PO")
public class BizReceiptPurchaseApplyUpdatePO {
    
    @ApiModelProperty(value = "行项目列表")
    private List<BizReceiptPurchaseApplyItemDTO> itemList;
} 