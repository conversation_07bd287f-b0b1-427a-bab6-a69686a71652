package com.inossem.wms.common.model.masterdata.car.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 车辆分页对象出参
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "车辆分页对象出参", description = "车辆分页对象出参")
public class DicCarPageVO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 车辆类型描述
     */
    private String carTypeName;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 车辆编码
     */
    private String carCode;

    /**
     * 车辆描述
     */
    private String carName;

    /**
     * 吊带标号
     */
    private String slingCode;

    /**
     * 是否禁用【1是，0否】
     */
    private int isDisabled;

    /**
     * 是否删除【1是，0否】
     */
    private int isDelete;

    /**
     * 备注
     */
    private Long remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 修改人id
     */
    private Long modifyUserId;

}
