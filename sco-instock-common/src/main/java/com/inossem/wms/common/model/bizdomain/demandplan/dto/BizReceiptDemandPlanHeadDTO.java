package com.inossem.wms.common.model.bizdomain.demandplan.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.common.enums.BaseEnumResultVO;
import com.inossem.wms.common.model.common.enums.EnumDemandPlanTypeMapVO;
import com.inossem.wms.common.model.common.enums.EnumDemandPlanUrgentFlagMapVO;
import com.inossem.wms.common.model.common.enums.EnumPurchaseTypeMapVO;
import com.inossem.wms.common.model.masterdata.asset.entity.DicAsset;
import com.inossem.wms.common.model.masterdata.costcenter.entity.DicCostCenter;
import com.inossem.wms.common.model.masterdata.mat.base.dto.DicMaterialGroupDTO;
import com.inossem.wms.common.model.masterdata.wbs.entity.DicWbs;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 需求计划头表DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "需求计划头表DTO", description = "需求计划头表DTO")
public class BizReceiptDemandPlanHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "需求人工号", example = "24001844")
    private String demandUserCode;

    @ApiModelProperty(value = "需求人姓名", example = "张三")
    private String demandUserName;

    @ApiModelProperty(value = "需求部门编码", example = "D001")
    private String demandDeptCode;

    @ApiModelProperty(value = "需求部门名称", example = "采购部")
    private String demandDeptName;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 - 单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属 - 单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_demand_plan_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 需求计划单行项目")
    private List<BizReceiptDemandPlanItemDTO> itemList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称", example = "需求计划")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称", example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "需求类型列表")
    private List<EnumPurchaseTypeMapVO> demandTypeList;

    @ApiModelProperty(value = "紧急标识列表")
    private List<EnumDemandPlanUrgentFlagMapVO> urgentFlagList;

    @ApiModelProperty(value = "预算类型列表")
    private List<BaseEnumResultVO> budgetTypeList;

    @ApiModelProperty(value = "提交人编码")
    private String submitUserCode;

    @ApiModelProperty(value = "提交人名称")
    private String submitUserName;

    @ApiModelProperty(value = "科目类别列表")
    private List<BaseEnumResultVO> subjectTypeList;

    @ApiModelProperty(value = "科目类别名称", example = "成本中心")
    private String subjectTypeI18n;

    @ApiModelProperty(value = "需求计划类型描述")
    private String demandPlanTypeI18n;

    @ApiModelProperty(value = "需求计划类型列表")
    private List<EnumDemandPlanTypeMapVO> demandPlanTypeList;

    @ApiModelProperty(value = "物料组列表")
    private List<DicMaterialGroupDTO> matGroupList;

    @ApiModelProperty(value = "工厂列表")
    private List<DicFactoryDTO> factoryList;

    @ApiModelProperty("WBS主数据列表")
    private List<DicWbs> wbsList;

    @ApiModelProperty("成本中心主数据列表")
    private List<DicCostCenter> costCenterList;

    @ApiModelProperty("资产卡片主数据列表")
    private List<DicAsset> assetList;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    private Long id;

    @ApiModelProperty(value = "需求计划单号", example = "XQ241024001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "400")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态(10:草稿,20:已提交,30:已审批,40:已驳回,50:已取消,60:已关闭)", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "需求计划类型(10:一次性计划,20:新增设备,30:PO计划,40:专项工具,50:年度框架计划)", example = "10")
    private Integer demandPlanType;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "demandUserCode,demandUserName")
    @ApiModelProperty(value = "需求人ID", example = "1")
    private Long demandUserId;

    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "demandDeptCode,demandDeptName")
    @ApiModelProperty(value = "需求部门ID", example = "1")
    private Long demandDeptId;

    @ApiModelProperty(value = "是否紧急(10:特急,20:正常)", example = "20")
    private Integer urgentFlag;

    @ApiModelProperty(value = "预算归属(10:制造成本类,20:辅助生产类,30:基干干部类,40:工业生产运行类,50:重油环保类,60:油品类,70:电气类,80:其他类)", example = "10")
    private Integer budgetType;

    @ApiModelProperty(value = "计划到货日期", example = "2024-10-24")
    private Date planArrivalDate;

    @ApiModelProperty(value = "需求计划名称", example = "2024年第一季度工具采购计划")
    private String demandPlanName;

    @ApiModelProperty(value = "采购原因", example = "生产需要")
    private String purchaseReason;

    @ApiModelProperty(value = "建议供应方名单", example = "供应商A,供应商B")
    private String suggestVendorList;

    @ApiModelProperty(value = "备注", example = "备注信息")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2024-10-24", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2024-10-24", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "需求计划类型名称")
    private String demandTypeI18n;

    @ApiModelProperty(value = "是否紧急名称")
    private String urgentFlagI18n;

    @ApiModelProperty(value = "预算归属名称")
    private String budgetTypeI18n;

    @ApiModelProperty(value = "提交人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "科目类别(10:成本中心,20:项目)", example = "10")
    private Integer subjectType;

    @ApiModelProperty(value = "需求类型(1:生产物资类,2:资产类,3:非生产类物资)", example = "1")
    private Integer demandType;

    @ApiModelProperty(value = "提交人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "handleUserName")
    private Long handleUserId;

    @ApiModelProperty(value = "提交人名称")
    private String handleUserName;
}
