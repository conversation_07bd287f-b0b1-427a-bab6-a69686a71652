package com.inossem.wms.common.model.sap.posting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SAP物料凭证过账行项目参数
 */
@Data
public class HXPostingItem {
    
    @ApiModelProperty(value = "通用必输字段-单据行项目号 ZEILE")
    private String receiptRid = "";
    
    @ApiModelProperty(value = "通用必输字段-物料编码 MATBF")
    private String matCode = "";
    
    @ApiModelProperty(value = "通用必输字段-工厂 WERKS")
    private String ftyCode = "";

    @ApiModelProperty(value = "通用必输字段-目标工厂 WERKS")
    private String ftyCode2 = "";
    
    @ApiModelProperty(value = "通用必输字段-库存地点 LGORT1")
    private String locationCode1 = "";

    @ApiModelProperty(value = "库存地点 LGORT2")
    private String locationCode2 = "";

    @ApiModelProperty(value = "移动类型 BWART")
    private String moveType = "";
    
    @ApiModelProperty(value = "通用必输字段-数量 ERFMG")
    private String qty = "";
    
    @ApiModelProperty(value = "通用必输字段-计量单位 ERFME")
    private String unitCode = "";
    
    @ApiModelProperty(value = "采购订单编号 EBELN")
    private String purchaseOrderCode = "";

    @ApiModelProperty(value = "采购凭证的项目编号  EBELP")
    private String purchaseOrderItemCode = "";

    @ApiModelProperty(value = "发票号 ZFPH")
    private String invoiceCode = "";

    @ApiModelProperty(value = "发票日期 ZFPRQ")
    private String invoiceDate = "";

    @ApiModelProperty(value = "成本中心 COSTCENTER")
    private String costCenter = "";

    @ApiModelProperty(value = "WBS编号 PS_POSID")
    private String wbsCode = "";

    @ApiModelProperty(value = "备注 SGTXT")
    private String remark = "";

    @ApiModelProperty(value = "移动类型标识 MV_IND")
    private String mvtInd = "";

    @ApiModelProperty(value = "物料凭证编号 MAT_DOC_CODE")
    private String matDocCode = "";

    @ApiModelProperty(value = "物料凭证年份 MAT_DOC_YEAR")
    private String matDocYear = "";

    @ApiModelProperty("资产编码")
    private String assetCode;

    @ApiModelProperty("资产子编号")
    private String assetSubCode;

} 
