package com.inossem.wms.common.enums.contract;

import com.inossem.wms.common.model.common.enums.EnumContractTaxRateMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同税率枚举
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Getter
@AllArgsConstructor
public enum EnumContractTaxRate {

    A0(10, "A0", "0%联邦可抵扣进项税"),
    A1(20, "A1", "18%联邦可抵扣进项税"),
    B0(30, "B0", "0%联邦不可抵扣进项税"),
    B1(40, "B1", "18%联邦不可抵扣进项税"),
    B2(50, "B2", "10%联邦不可抵扣进项税"),
    C0(60, "C0", "0%信德省可抵扣进项税"),
    C1(70, "C1", "19.5%信德省可抵扣进项税"),
    C2(80, "C2", "15%信德省可抵扣进项税"),
    D0(90, "D0", "0%信德省不可抵扣进项税"),
    D1(100, "D1", "15%信德省不可抵扣进项税"),
    D2(110, "D2", "10%信德省不可抵扣进项税"),
    D3(120, "D3", "8%信德省不可抵扣进项税"),
    E0(130, "E0", "0%联邦销项税"),
    E1(140, "E1", "18%联邦销项税"),
    J0(150, "J0", "0%进项税"),
    J2(160, "J2", "13%进项税"),
    J3(170, "J3", "6%进项税"),
    J4(180, "J4", "9%进项税");

    private final Integer key;
    private final String code;
    private final String desc;

    private static List<EnumContractTaxRateMapVO> list;

    public static List<EnumContractTaxRateMapVO> toList() {
        if (list == null) {
            List<EnumContractTaxRateMapVO> listInner = new ArrayList<>();
            EnumContractTaxRate[] ary = EnumContractTaxRate.values();
            for (EnumContractTaxRate e : ary) {
                EnumContractTaxRateMapVO vo = new EnumContractTaxRateMapVO();
                vo.setContractTaxRate(e.getKey());
                vo.setCode(e.getCode());
                listInner.add(vo);
            }   
            list = listInner;
        }
        return list;
    }

    public static EnumContractTaxRate getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (EnumContractTaxRate e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static EnumContractTaxRate getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (EnumContractTaxRate e : values()) {
            if (e.getKey().equals(key)) {
                return e;
            }
        }
        return null;
    }
} 
