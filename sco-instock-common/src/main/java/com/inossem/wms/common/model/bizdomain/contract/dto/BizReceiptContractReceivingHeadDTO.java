package com.inossem.wms.common.model.bizdomain.contract.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptContractReceivingHeadDTO implements Serializable {
    private static final long serialVersionUID = -4379492665574833955L;


    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型，402-其他合同，403-框架合同")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "收货类型，1非生产物资类&服务类&施工类合同收货，2 资产类 ")
    private Integer receiveType;

    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "receiptCode,deliveryNoticeDescribe", targetAttrName = "deliveryCode,deliveryNoticeDescribe")
    @ApiModelProperty(value = "托收PO批次id")
    private Long deliveryId;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "receiptCode,contractName,firstParty,supplierId,purchaseType", targetAttrName = "contractCode,contractName,firstParty,supplierId,purchaseType")
    @ApiModelProperty(value = "合同Id")
    private Long contractId;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "pmUserCode,pmUserName")
    @ApiModelProperty(value = "项目经理审批人id")
    private Long pmUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "assignUserCode,assignUserName")
    @ApiModelProperty(value = "专工审批人id")
    private Long assignUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    @ApiModelProperty(value = "提交人ID")
    private Long submitUserId;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;


    //  扩展属性
    @ApiModelProperty(value = "单据类型，402-其他合同，403-框架合同")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 创建人部门名称", example = "管理员")
    private String createUserDeptName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -提交人编码", example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称", example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "项目经理审批人Code")
    private String pmUserCode;

    @ApiModelProperty(value = "项目经理审批人name")
    private String pmUserName;

    @ApiModelProperty(value = "专工审批人Code")
    private String assignUserCode;

    @ApiModelProperty(value = "专工审批人name")
    private String assignUserName;

    @ApiModelProperty(value = "专工审批人列表")
    private List<SysUserDTO> assignUserList;


    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "送货单号")
    private String deliveryCode;

    @ApiModelProperty(value = "送货单描述")
    private String deliveryNoticeDescribe;


    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方国际化")
    private String firstPartyI18n;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierName", targetAttrName = "supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;


    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "冲销过帐日期", example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;


    @ApiModelProperty(value = "合同类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同类型国际化")
    private String purchaseTypeI18n;


    @ApiModelProperty(value = "是否存在考勤确认")
    private Integer isAttendance;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId", sonTbFkOtherAttrLimit = "receiptItemId=0")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @SonAttr(sonTbName = "biz_receipt_contract_receiving_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 行项目")
    private List<BizReceiptContractReceivingItemDTO> itemList;

    @ApiModelProperty(value = "填充属性 - 合同分项信息")
    @SonAttr(sonTbName = "biz_receipt_contract_sub_item", sonTbFkAttrName = "receiveId", sonTbFkOtherAttrLimit = {"paymentPlanId=0"})
    private List<BizReceiptContractSubItemDTO> subItemList;

    @SonAttr(sonTbName = "biz_receipt_contract_attendance_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 考勤行项目")
    private List<BizReceiptContractAttendanceItemDTO> attendanceItemList;

    @SonAttr(sonTbName = "biz_receipt_contract_examine_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 考核行项目")
    private List<BizReceiptContractExamineItemDTO> examineItemList;

    @SonAttr(sonTbName = "biz_receipt_contract_diesel_recheck_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 柴油复核行项目")
    private List<BizReceiptContractDieselRecheckItemDTO> dieselRecheckItemList;

    @ApiModelProperty(value = "过帐日期", example = "2021-05-11")
    private Date postingDate;
}
