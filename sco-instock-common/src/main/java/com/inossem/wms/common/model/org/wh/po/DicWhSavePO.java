package com.inossem.wms.common.model.org.wh.po;

import java.io.Serializable;

import com.inossem.wms.common.model.org.wh.dto.DicWhDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "仓库保存入参类", description = "仓库保存入参类")
@Data
public class DicWhSavePO implements Serializable {

    private static final long serialVersionUID = 9013246561469477022L;

    @ApiModelProperty(value = "仓库实体类")
    private DicWhDTO whInfo;
}
