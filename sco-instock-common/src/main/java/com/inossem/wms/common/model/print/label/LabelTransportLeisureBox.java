package com.inossem.wms.common.model.print.label;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wang
 * <p>
 *     闲置物资转库打印
 * </p>
 * @date 2022/6/21 19:34
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = " 闲置物资转库打印", description = "闲置物资转库打印")
public class LabelTransportLeisureBox implements Serializable {

    @ApiModelProperty(value = "公司描述" , example = "示例公司")
    private String corpName;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "采购订单")
    private String receiptCode;

    @ApiModelProperty(value = "物资类别")
    private String matCategory;

    @ApiModelProperty(value = "批次code")
    private String batchCode;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "入库日期")
    private Date inputDate;

    @ApiModelProperty(value = "所属工厂")
    private String ftyName;

    @ApiModelProperty(value = "所属仓库")
    private String whName;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    private Integer isSingle;


}
