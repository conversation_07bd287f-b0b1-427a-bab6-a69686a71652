package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物料编码申请单行项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptMatApplyItem对象", description = "物料编码申请单行项目表")
public class BizReceiptMatApplyItemDTO implements Serializable {

    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String itemStatusI18n;

    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;

    @ApiModelProperty(value = "单位描述" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "规格尺寸等描述信息")
    private String descInfo;

    @ApiModelProperty(value = "适用设备")
    private String applicableEquipment;

    @ApiModelProperty(value = "系统")
    private String systems;

    @ApiModelProperty(value = "物料描述英文（简称）")
    private String matNameEnShort;

    @ApiModelProperty(value = "Material name")
    private String materialNameEn;

    @ApiModelProperty(value = "Descriptive information such as specifications and dimensions")
    private String descInfoEn;

    @ApiModelProperty(value = "Applicable equipment")
    private String applicableEquipmentEn;

    @ApiModelProperty(value = "systems")
    private String systemsEn;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "计量单位id")
    private Long unitId;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组id")
    private Long matGroupId;

    @ApiModelProperty(value = "物料类型")
    private String matTypeCode;

    @ApiModelProperty(value = "评估类")
    private String extEvaluationClassification;

    @ApiModelProperty(value = "行业领用")
    private String industryCode;

    @ApiModelProperty(value = "价格控制")
    private String priceControlCode;

    @ApiModelProperty(value = "价格单位")
    private String priceUnit;

    @ApiModelProperty(value = "采购组")
    private String purchaseGroupCode;

    @ApiModelProperty(value = "利润中心")
    private String profitCenterCode;

    @TableLogic
    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

}
