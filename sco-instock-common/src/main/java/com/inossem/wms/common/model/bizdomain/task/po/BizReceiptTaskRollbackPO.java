package com.inossem.wms.common.model.bizdomain.task.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 作业单撤回PO
 */
@Data
@Accessors(chain = true)
public class BizReceiptTaskRollbackPO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long taskHeadId;
    private Integer taskReceiptType;
    private Long taskItemId;
    private Long taskReqHeadId;
//    private Long taskReqHeadVersion;
    private Long taskReqItemId;
    private BigDecimal qty;
}
