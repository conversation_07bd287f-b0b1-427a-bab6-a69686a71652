package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 退旧台账报表 PO
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ReturnOldSearchPO", description="ReturnOldSearchPO")
public class ReturnOldSearchPO extends PageCommon {

    @ApiModelProperty(value = "物料编码")
    private String matCode;
    private List<String> matCodeList;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "退旧物资入库申请单号")
    private String applyReceiptCode;
    private List<String> applyReceiptCodeList;

    @ApiModelProperty(value = "退旧物资入库单号")
    private String receiptCode;
    private List<String> receiptCodeList;

    @ApiModelProperty(value = "出库单号")
    private String outputReceiptCode;
    private List<String> outputReceiptCodeList;

    @ApiModelProperty(value = "预估退旧类型")
    private Integer estimateReturnOldType;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "退旧类型")
    private Integer returnOldType;

}
