package com.inossem.wms.common.util;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.nio.charset.StandardCharsets;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

//import de.hunsicker.jalopy.Jalopy;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.SneakyThrows;

/**
 * 
 * <AUTHOR>
 * @date 2021/1/21 17:17
 */
public class UtilMetadata {

    /**
     * 默认主键属性名
     */
    private static final String DEFAULT_PK_ATTR_NAME = "id";
    private static final Pattern underlinePattern = Pattern.compile("_(\\w)");
    private static final Pattern horizontalLinePattern = Pattern.compile("-(\\w)");
    private static final Pattern humpPattern = Pattern.compile("[A-Z]");

    /**
     * 下划线转驼峰
     * 
     * @param str
     * @return
     */
    public static String underlineToHump(String str) {
        Matcher matcher = underlinePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 中横线转驼峰
     * 
     * @param str
     * @return
     */
    public static String horizontalLineToHump(String str) {
        Matcher matcher = horizontalLinePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String tableToClass(String underlineStr) {
        if (underlineStr == null) {
            return null;
        }
        // 分成数组
        char[] charArray = underlineStr.toCharArray();
        // 判断上次循环的字符是否是"_"
        boolean underlineBefore = false;
        StringBuffer buffer = new StringBuffer();
        for (int i = 0, l = charArray.length; i < l; i++) {
            if (i == 0) {
                buffer.append(charArray[i] -= 32);
            } else {
                // 判断当前字符是否是"_",如果跳出本次循环
                if (charArray[i] == 95) {
                    underlineBefore = true;
                } else if (underlineBefore) {
                    // 如果为true，代表上次的字符是"_",当前字符需要转成大写
                    buffer.append(charArray[i] -= 32);
                    underlineBefore = false;
                } else {
                    // 不是"_"后的字符就直接追加
                    buffer.append(charArray[i]);
                }
            }
        }
        return buffer.toString();
    }

    public static String tableToProperty(String underlineStr) {
        if (underlineStr == null) {
            return null;
        }
        // 分成数组
        char[] charArray = underlineStr.toCharArray();
        // 判断上次循环的字符是否是"_"
        boolean underlineBefore = false;
        StringBuffer buffer = new StringBuffer();
        for (int i = 0, l = charArray.length; i < l; i++) {

            // 判断当前字符是否是"_",如果跳出本次循环
            if (charArray[i] == 95) {
                underlineBefore = true;
            } else if (underlineBefore) {
                // 如果为true，代表上次的字符是"_",当前字符需要转成大写
                buffer.append(charArray[i] -= 32);
                underlineBefore = false;
            } else {
                // 不是"_"后的字符就直接追加
                buffer.append(charArray[i]);
            }
        }
        return buffer.toString();
    }

    /**
     * 驼峰转下划线
     * 
     * @param str
     * @return
     */
    public static String humpToLine(String str) {
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 首字母转大写
     * 
     * @param str
     * @return
     */
    public static String firstCharToUpper(String str) {
        char[] c = str.toCharArray();
        if (c[0] < 'a' || c[0] > 'z') {
            return str;
        }
        c[0] -= 32;
        return String.valueOf(c);
    }

    /**
     * 首字母转小写
     * 
     * @param str
     * @return
     */
    public static String firstCharToLower(String str) {
        char[] c = str.toCharArray();
        if (c[0] < 'A' || c[0] > 'Z') {
            return str;
        }
        c[0] += 32;
        return String.valueOf(c);
    }

    /**
     * 字符串转包名
     * 
     * @param str
     * @return
     */
    public static String str2PackageName(String str) {
        if (UtilString.isNullOrEmpty(str)) {
            return null;
        }

        str = str.toLowerCase();

        return str.replace("-", "");
    }

    /**
     * 模板合并
     * 
     * @param parm 合并上下文参数
     * @param templateFile 模板文件
     * @param targetFilePath 生成文件绝对路径
     */
    @SneakyThrows
    public static void templateMerge(Map<String, Object> parm, File templateFile, String targetFilePath) {
        // 模板引擎
        VelocityEngine ve = new VelocityEngine();

        ve.setProperty(Velocity.FILE_RESOURCE_LOADER_PATH, templateFile.getParentFile().getAbsolutePath());

        // 获取模板文件
        Template template = ve.getTemplate(templateFile.getName(), StandardCharsets.UTF_8.name());

        if (targetFilePath.endsWith(".vm")) {
            targetFilePath = targetFilePath.substring(0, targetFilePath.length() - 3);
        }

        // 生成的文件
        File file = new File(targetFilePath);

        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }

        if (!file.exists()) {
            file.createNewFile();
        }

        // 目标文件写入
        try (FileOutputStream outStream = new FileOutputStream(file)) {
            try (OutputStreamWriter writer = new OutputStreamWriter(outStream, StandardCharsets.UTF_8)) {
                try (BufferedWriter sw = new BufferedWriter(writer)) {
                    template.merge(new VelocityContext(parm), sw);
                    sw.flush();
                }
            }
        }

         // 将生成的Java代码文件格式化
//        if (file.getName().endsWith(".java")) {
//            try {
//                File fileFormat = new File(targetFilePath);
//                Jalopy j = new Jalopy();
//                j.setEncoding(StandardCharsets.UTF_8.name());
//                j.setInput(fileFormat);
//                j.setOutput(fileFormat);
//                j.format();
//                j.cleanupBackupDirectory();
//            } catch (Exception e) {}
//        }

        System.out.println("成功生成文件:" + targetFilePath);
    }

    /**
     * 模板合并（使用Velocity模板引擎处理）
     * 
     * @param parm 合并上下文参数
     * @param templateStr 模板字符串
     * @return 合并后的字符串
     */
    public static String templateMerge(Map<String, Object> parm, String templateStr) {
        // 创建模版引擎上下文 并传入要替换的参数
        VelocityContext vc = new VelocityContext(parm);

        // 创建StringWriter对象 其内部是对StringBuffer进行的操作
        StringWriter writer = new StringWriter();

        // 模版引擎开始替换模版内容
        Velocity.evaluate(vc, writer, "", templateStr);

        String result = writer.getBuffer().toString();

        // 返回替换之后的字符串
        return result;
    }

    /**
     * 获取文件夹下所有文件，递归获取
     * 
     * @param file 文件夹
     * @return
     */
    public static List<File> getAllFiles(File file) {
        List<File> allFileList = new LinkedList<>();

        // 文件不存在直接返回
        if (!file.exists()) {
            return allFileList;
        }

        // 如果是文件，则只返回自己
        if (file.isFile()) {
            allFileList.add(file);

            return allFileList;
        }

        // 遍历文件夹
        for (File dirFile : file.listFiles()) {
            // 如果是文件夹则递归获取，如果是文件则直接搜集
            if (dirFile.isDirectory()) {
                // 递归获取
                allFileList.addAll(UtilMetadata.getAllFiles(dirFile));
            } else {
                allFileList.add(dirFile);
            }
        }

        return allFileList;
    }

    /**
     * 取得clazz类的泛型的class
     * 
     * @param clazz
     * @param index 要取的是第几个泛型
     * @return
     */
    public static Class getParameterizedTypeClass(Class clazz, int index) {
        if (clazz == null) {
            return null;
        }
        Class type = null;
        try {
            ParameterizedType superClass = (ParameterizedType)clazz.getGenericSuperclass();
            type = UtilMetadata.getParameterizedTypeClass(superClass, index);
        } catch (ClassCastException e) {
            e.printStackTrace();
        }

        return type;
    }

    /**
     * 取得泛型的class
     * 
     * @param pt 参数化类型
     * @param index 要取的是第几个泛型
     * @return
     */
    public static Class getParameterizedTypeClass(ParameterizedType pt, int index) {
        Class type = (Class)pt.getActualTypeArguments()[index];

        return type;
    }

    /**
     * 取得对应的数据库表名
     * 
     * @param clazz
     * @return
     */
    public static String getTableName(Class<?> clazz) {
        if (clazz == null) {
            return null;
        }

        String tableName = StringUtils.EMPTY;

        // 取得表名注解
        TableName annation = clazz.getAnnotation(TableName.class);

        // 如果使用了注解指定表名，直接使用注解指定的表名，否则使用类名
        if (annation == null) {
            String className = clazz.getName();

            // 驼峰转下划线 com.inossem.wms.common.model.auth.rel.entity.RelUserStockLocation
            Matcher matcher = humpPattern.matcher(className);
            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
            }
            matcher.appendTail(sb);
            String[] tableNames = sb.toString().split("[.]");
            tableName = tableNames[tableNames.length - 1];
            tableName = tableName.substring(1);
        } else {
            tableName = annation.value();
        }

        return tableName;
    }

    /**
     * 取得对应的数据库列名
     * 
     * @param clazz
     * @return
     */
    public static String getColName(String fieldName, Class<?> clazz) {
        String colName = StringUtils.EMPTY;

        // 反射获取属性
        Field field = UtilReflect.getField(fieldName, clazz);

        field.setAccessible(true);

        // 取得列名注解
        TableField annation = field.getAnnotation(TableField.class);

        // 如果使用了注解指定列名，直接使用注解指定的列名，否则使用属性名
        if (annation == null || UtilString.isNullOrEmpty(annation.value())) {
            // 驼峰转下划线
            colName = UtilMetadata.humpToLine(fieldName);
        } else {
            colName = annation.value();
        }

        return colName;
    }

    /**
     * 取得主键属性名
     * 
     * @param clazz
     * @return
     */
    public static String getPkAttrName(Class<?> clazz) {
        // 反射出类的所有字段
        List<Field> fieldList = UtilReflect.getAllFields(clazz);

        // 遍历所有字段
        for (Field field : fieldList) {

            field.setAccessible(true);

            // 取得主键注解
            TableId annation = field.getAnnotation(TableId.class);

            if (annation != null) {
                return field.getName();
            }
        }

        return UtilMetadata.DEFAULT_PK_ATTR_NAME;
    }
}
