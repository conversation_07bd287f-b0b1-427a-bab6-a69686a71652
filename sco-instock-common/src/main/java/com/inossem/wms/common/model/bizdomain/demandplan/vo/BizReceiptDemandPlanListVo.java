package com.inossem.wms.common.model.bizdomain.demandplan.vo;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 需求计划列表展示VO
 */
@Data
@ApiModel(value = "需求计划列表展示VO", description = "需求计划列表展示VO")
public class BizReceiptDemandPlanListVo {

    @ApiModelProperty(value = "主键id", example = "159843409264782")
    private Long id;

    @ApiModelProperty(value = "需求计划单号", example = "XQ241024001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "400")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "需求类型", example = "10")
    private Integer demandType;

    @ApiModelProperty(value = "需求计划类型", example = "10")
    private Integer demandPlanType;

    @ApiModelProperty(value = "需求人工号", example = "24001844")
    private String demandUserCode;

    @ApiModelProperty(value = "需求人姓名", example = "张三")
    private String demandUserName;

    @ApiModelProperty(value = "需求部门编码", example = "D001")
    private String demandDeptCode;

    @ApiModelProperty(value = "需求部门名称", example = "采购部")
    private String demandDeptName;

    @ApiModelProperty(value = "是否紧急", example = "20")
    private Integer urgentFlag;

    @ApiModelProperty(value = "预算归属", example = "10")
    private Integer budgetType;

    @ApiModelProperty(value = "科目类型", example = "10")
    private Integer subjectType;

    @ApiModelProperty(value = "计划到货日期", example = "2024-10-24")
    private Date planArrivalDate;

    @ApiModelProperty(value = "需求计划名称", example = "2024年第一季度工具采购计划")
    private String demandPlanName;

    @ApiModelProperty(value = "创建时间", example = "2024-10-24")
    private Date createTime;

    @ApiModelProperty(value = "创建人工号", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "单据类型名称", example = "需求计划")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态名称", example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "需求计划类型名称", example = "一次性计划")
    private String demandTypeI18n;

    @ApiModelProperty(value = "需求计划类型名称", example = "一次性计划")
    private String demandPlanTypeI18n;

    @ApiModelProperty(value = "是否紧急名称", example = "正常")
    private String urgentFlagI18n;

    @ApiModelProperty(value = "预算归属名称", example = "制造成本类")
    private String budgetTypeI18n;

    @ApiModelProperty(value = "科目类型名称", example = "制造成本类")
    private String subjectTypeI18n;

    @ApiModelProperty(value = "需求人工号", example = "24001844")
    private String handleUserCode;

    @ApiModelProperty(value = "需求人姓名", example = "张三")
    private String handleUserName;

    private String approveTime;
}
