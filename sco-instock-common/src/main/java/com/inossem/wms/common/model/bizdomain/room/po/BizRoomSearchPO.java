package com.inossem.wms.common.model.bizdomain.room.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 房间数据查询入参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomSearchPO", description="房间数据查询入参")
public class BizRoomSearchPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "房间编号【楼栋号-房间号】")
    private String roomCode;

    @ApiModelProperty(value = "楼栋号")
    private String buildingNo;

    @ApiModelProperty(value = "床位数")
    private Integer bedCount;

    @ApiModelProperty(value = "房间申请人ID")
    private Long applicantUserId;

    @ApiModelProperty(value = "当前房间使用信息抬头id")
    private Long currentRoomUsageHeadId;

    @ApiModelProperty(value = "是否空闲房间 1：只查询空闲房间，0或空或其他：查询所有房间")
    private Integer isIdleRoom;

    @ApiModelProperty(value = "楼栋号")
    private List<String> buildingNoList;

    @ApiModelProperty(value = "床位数")
    private List<Integer> bedCountList;

    @ApiModelProperty(value = "退房申请单行项目id")
    private Long checkOutReqItemId;

}
