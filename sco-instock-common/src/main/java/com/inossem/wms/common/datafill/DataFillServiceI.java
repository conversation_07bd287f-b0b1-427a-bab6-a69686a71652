package com.inossem.wms.common.datafill;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.metadata.MetadataContext;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilMetadata;
import com.inossem.wms.common.util.UtilReflect;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public interface DataFillServiceI {

    /**
     * 根据填充类型填充数据
     *
     * @param dataFillType 填充类型
     * @param dataList 要被填充的对象列表
     * @param <T>
     * <AUTHOR> <<EMAIL>>
     */
    <T extends Object> void fillType(EnumDataFillType dataFillType, List<T> dataList);

    /**
     * 根据填充类型填充数据
     *
     * @param dataFillType 填充类型
     * @param dataObj 要被填充的对象列表
     * @param <T>
     * <AUTHOR> <<EMAIL>>
     */
    <T extends Object> void fillType(EnumDataFillType dataFillType, T... dataObj);


    /**
     * 填充数据对象的关联属性和父子属性
     *
     * @param dataObj 要被填充的对象
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillAttr(T dataObj);

    /**
     * 填充数据对象的关联属性和父子属性（白名单-指定属性）
     *
     * @param dataObj 要被填充的对象
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillAttrWField(T dataObj, SFunction<T, ?>... functionArray);

    /**
     * 填充数据对象的关联属性和父子属性（黑名单-忽略属性）
     *
     * @param dataObj 要被填充的对象
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillAttrBField(T dataObj, SFunction<T, ?>... functionArray);

    /**
     * 填充数据对象的关联属性和父子属性
     *
     * @param dataList 要被填充的对象列表
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillAttr(List<T> dataList);

    /**
     * 填充数据对象的关联属性和父子属性（白名单-指定属性）
     *
     * @param dataList 要被填充的对象列表
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillAttrWField(List<T> dataList, SFunction<T, ?>... functionArray);

    /**
     * 填充数据对象的关联属性和父子属性（黑名单-忽略属性）
     *
     * @param dataList 要被填充的对象列表
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillAttrBField(List<T> dataList, SFunction<T, ?>... functionArray);

    /**
     * 针对单个或多个数据对象的父子属性填充方法的封装
     *
     * @param dataObj
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillSonAttrForDataObj(T dataObj);

    /**
     * 针对单个数据对象的父子属性填充方法的封装（白名单-指定属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillSonAttrForDataObjWField(T dataObj, SFunction<T, ?>... functionArray);

    /**
     * 针对单个数据对象的父子属性填充方法的封装（黑名单-忽略属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillSonAttrForDataObjBField(T dataObj, SFunction<T, ?>... functionArray);

    /**
     * 填充父子字段数据列表
     *
     * @param dataList
     * @param <T>
     */
    <T> void fillSonAttrDataList(List<T> dataList);

    /**
     * 填充父子字段数据列表（白名单-指定属性）
     *
     * @param dataList
     * @param fieldArray
     * @param <T>
     */
    <T> void fillSonAttrDataListWField(List<T> dataList, SFunction<T, ?>... fieldArray);

    /**
     * 填充父子字段数据列表（黑名单-忽略属性）
     *
     * @param dataList
     * @param functionArray
     * @param <T>
     */
    <T> void fillSonAttrDataListBField(List<T> dataList, SFunction<T, ?>... functionArray);

    /**
     * 针对单个数据对象的关联属性填充方法的封装
     *
     * @param dataObj
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillRlatAttrForDataObj(T dataObj);

    /**
     * 针对单个数据对象的关联属性填充方法的封装（白名单-指定属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillRlatAttrForDataObjWField(T dataObj, SFunction<T, ?>... functionArray);

    /**
     * 针对单个数据对象的关联属性填充方法的封装（黑名单-忽略属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    <T> void fillRlatAttrForDataObjBField(T dataObj, SFunction<T, ?>... functionArray);

    /**
     * 填充关联字段数据列表
     *
     * @param dataList
     * @param <T>
     */
    <T> void fillRlatAttrDataList(List<T> dataList);

    /**
     * 填充关联字段数据列表（白名单-指定属性）
     *
     * @param dataList
     * @param functionArray
     * @param <T>
     */
    <T> void fillRlatAttrDataListWField(List<T> dataList, SFunction<T, ?>... functionArray);

    /**
     * 填充关联字段数据列表（黑名单-忽略属性）
     *
     * @param dataList
     * @param functionArray
     * @param <T>
     */
    <T> void fillRlatAttrDataListBField(List<T> dataList, SFunction<T, ?>... functionArray);

}
