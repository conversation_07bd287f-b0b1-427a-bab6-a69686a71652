package com.inossem.wms.common.model.bizdomain.room.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 住房结算单行项目DTO（房费信息）
 * 主要记录结算的房间（房间使用记录）信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomReceiptSettlementItem对象", description="住房结算单行项目DTO（房费信息）")
public class BizRoomReceiptSettlementItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "房间类型")
    private String bedCountI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "房间id（冗余存储）")
    private Long roomId;

    @ApiModelProperty(value = "房间编号【楼栋号-房间号】（冗余存储）")
    private String roomCode;

    @ApiModelProperty(value = "床位数")
    private Integer bedCount;

    @ApiModelProperty(value = "房间使用记录抬头表id")
    private Long roomUsageHeadId;

    @ApiModelProperty(value = "房间使用开始时间（预定时间、申请交房时间、计费开始时间）")
    private Date startUsageTime;

    @ApiModelProperty(value = "上一结算时间（此时间之前的费用已结清）")
    private Date preSettlementTime;

    @ApiModelProperty(value = "房间使用结束时间（退房时间、计费结束时间）")
    private Date endUsageTime;

    @ApiModelProperty(value = "结算天数")
    private Integer settlementDays;

    @ApiModelProperty(value = "结算单价")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算总价（结算天数*结算单价）")
    private BigDecimal settlementTotalPrice;


}
