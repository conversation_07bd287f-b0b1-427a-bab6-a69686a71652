package com.inossem.wms.common.model.masterdata.car.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 车辆列表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "车辆列表出参", description = "车辆列表出参")
public class DicCarListVO {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/


    @ApiModelProperty(value = "采购订单")
    private BigDecimal purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单头" )
    private BigDecimal purchaseReceiptHeadId;

    @ApiModelProperty(value = "采购订单行项目" )
    private BigDecimal purchaseReceiptItemId;


    @ApiModelProperty(value = "前置单据id" , example = "149901631619073")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前置单据行项目号" , example = "1")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "已入库数量" , example = "10")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "出库数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "物料主键" , example = "159843409264782", required = false)
    private Long matId;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;

    @ApiModelProperty(value = "单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "物料组编码" , example = "g1")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称" , example = "物料组1")
    private String matGroupName;

    @ApiModelProperty(value = "物料类型编码" , example = "A1")
    private String matTypeCode;

    @ApiModelProperty(value = "物料类型名称" , example = "物料类型1")
    private String matTypeName;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "修改人名称" , example = "管理员")
    private String modifyUserName;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "工厂主键" , example = "145343907954689")
    private Long whId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "是否冻结描述" , example = "否")
    private String isFreezeI18n;

    @ApiModelProperty(value = "是否危险物料描述" , example = "否")
    private String isDangerousI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "计量单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "物料组id" , example = "1")
    private Long matGroupId;

    @ApiModelProperty(value = "物料类型id", example = "1")
    private Long matTypeId;

    @ApiModelProperty(value = "长度" , example = "100")
    private BigDecimal length;

    @ApiModelProperty(value = "宽度" , example = "100")
    private BigDecimal width;

    @ApiModelProperty(value = "高度" , example = "100")
    private BigDecimal height;

    @ApiModelProperty(value = "长度/宽度/高度的单位" , example = "M")
    private String unitLength;

    @ApiModelProperty(value = "毛重" , example = "100")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重" , example = "100")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "重量容差" , example = "正负5%")
    private BigDecimal weightTolerance;

    @ApiModelProperty(value = "重量的单位" , example = "KG")
    private String unitWeight;

    @ApiModelProperty(value = "体积" , example = "1000000")
    private BigDecimal volume;

    @ApiModelProperty(value = "体积的单位" , example = "M3")
    private String unitVolume;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLife;

    @ApiModelProperty(value = "保质期的单位 D-天 M-月" , example = "M")
    private String unitShelfLife;

    @ApiModelProperty(value = "是否启用保质期【1是，0否】" , example = "1")
    private Integer isShelfLife;

    @ApiModelProperty(value = "是否冻结【1是，0否】" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "危险物料标示【1是，0否】" , example = "0")
    private Integer isDangerous;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
}
