package com.inossem.wms.common.model.bizdomain.input.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 入库单抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "入库单抬头传输对象", description = "入库单抬头传输对象")
public class BizReceiptInputHeadDTO implements Serializable {

    private static final long serialVersionUID = 4627857038996145927L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "冲销过帐日期", example = "2021-05-11")
    private Date writeOffPostingDate;

    private String preReceiptCode;

    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销年度", example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "公司名称")
    private String corpName;

    @ApiModelProperty(value = "总计")
    private String sumPriceStr;
    @ApiModelProperty(value = "总计")
    private String sumPriceLocal;
    @ApiModelProperty(value = "质检提交人用户姓名")
    private String inspectSumbitterName;
    @ApiModelProperty(value = "参检人用户姓名")
    private String inspectUserName;
    @ApiModelProperty(value = "填充属性 - SAP采购订单创建人name")
    private String erpCreateUserName;

    @ApiModelProperty(value = "扩展属性- 总价-打印使用" , example = "20.1")
    private BigDecimal sumPrice;

    @ApiModelProperty(value = "扩展属性 - 物料凭证编号-打印使用", example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "扩展属性 - 工厂名称-打印使用", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "扩展属性 凭证时间-打印使用", example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "扩展属性 - 采购订单编码-打印使用", example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "废旧物资-原价" , example = "20.1")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "废旧物资-折旧价" , example = "1.1")
    private BigDecimal depreciationPrice;

    @ApiModelProperty(value = "废旧物资-处置评估价" , example = "3.1")
    private BigDecimal appraisalPrice;

    @ApiModelProperty(value = "废旧物资-是否危险【1是，0否】")
    private Integer isDanger;

    @RlatAttr(rlatTableName = "biz_receipt_inspect_head", sourceAttrName = "receiptCode", targetAttrName = "preReceiptCode")
    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键" , example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -采购员编码" , example = "Admin")
    private String purchaserUserCode;

    @ApiModelProperty(value = "填充属性 -采购员" , example = "管理员")
    private String purchaserUserName;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_input_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 入库行项目")
    private List<BizReceiptInputItemDTO> itemList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "inspectInputHeadId" , limitAttr = "receiptType=106")
    @ApiModelProperty(value = "填充属性 - 验收入库单运单")
    private List<BizReceiptWaybillDTO> waybillDTOList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "inspectInputWriteOffHeadId" , limitAttr = "receiptType=-106")
    @ApiModelProperty(value = "填充属性 - 验收入库冲销单运单")
    private List<BizReceiptWaybillDTO> waybillDTOWriteOffList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称" , example = "入库单")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "借用方式【1：长期借用单；2：短期借用单】")
    private Integer borrowType;

    @ApiModelProperty(value = "扩展属性 - 借用方式描述【1：长期借用单；2：短期借用单】")
    private String borrowTypeI18n;

    @ApiModelProperty(value = "填充属性 - 借用部门")
    private String deptCode;

    @ApiModelProperty(value = "填充属性 - 借用部门描述")
    private String deptName;

    @ApiModelProperty(value = "填充属性 - 借用科室")
    private String deptOfficeCode;

    @ApiModelProperty(value = "填充属性 - 借用科室描述")
    private String deptOfficeName;

    @ApiModelProperty(value = "预计借用天数（短期借用单必填）")
    private Integer estimateBorrowDay;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "维修厂商", example = "145343907954689")
    private String repairFty;

    @ApiModelProperty(value = "上架单提交人所属的部门")
    private String taskReqSubmitUserDeptName;

    @ApiModelProperty(value = "上架单提交人的电子签名")
    private String taskReqSubmitUserAutograph;

    @ApiModelProperty(value = "上架单提交人的电子签名")
    private String taskReqSubmitUserAutographData;

    @ApiModelProperty(value = "上架单完成的日期")
    private Date taskReqModifyTime;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    private Long id;

    @ApiModelProperty(value = "入库单号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "commonImgId", targetAttrName = "submitUserCommonImgId")
    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "入库类型")
    private String inputType;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "是否复检(1是0否)")
    private Integer isRecheck;

    @ApiModelProperty(value = "申请部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

    @ApiModelProperty(value = "申请科室id")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeCode,deptOfficeName", targetAttrName = "deptOfficeCode,deptOfficeName")
    private Long deptOfficeId;

    @ApiModelProperty(value = "暂存原因")
    private String tempStoreReason;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "是否虚拟出入库物项(1是0否)")
    private Integer isVirtual;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "来自物资返运冲销")
    private Integer writeOffFromMatReturn;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "supplierId", targetAttrName = "supplierId")
    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty(value = "供应商")
    private Long supplierId;

    @ApiModelProperty(value = "填充属性 - 供应商编码" , example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "填充属性 - 供应商名称" , example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "发送类型")
    private Integer sendType;

    @RlatAttr(rlatTableName = "biz_common_img", sourceAttrName = "imgBase64", targetAttrName = "submitUserSignImg")
    @ApiModelProperty(value = "填充属性 - 提交人用户签名id" , example = "管理员")
    private Long submitUserCommonImgId;

    @ApiModelProperty(value = "提交人签名")
    private String submitUserSignImg;


    @ApiModelProperty(value = "是否创建付款结算，1是0 否")
    private Integer settlementStatus;
}
