package com.inossem.wms.common.model.print.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签打印映射
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("label_print_mapping")
public class LabelPrintMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    @TableLogic
    private Integer isDelete;
    private Date createTime;
    private Date modifyTime;
    private Long createUserId;
    private Long modifyUserId;
    // wms二维码
    private String qrCodeWms;
    // 映射的二维码
    private String qrCodeMapping;

}
