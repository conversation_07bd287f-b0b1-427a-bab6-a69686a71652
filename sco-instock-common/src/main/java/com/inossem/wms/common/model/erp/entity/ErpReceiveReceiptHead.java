package com.inossem.wms.common.model.erp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 领料单head
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

@TableName("erp_receive_receipt_head")
@ApiModel(value = "ErpReceiveReceiptHead对象")
public class ErpReceiveReceiptHead implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "领料单head主键" , example = "111")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号" , example = "4500000001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "领料单描述" , example = "单据备注")
    private String remark;

    @ApiModelProperty(value = "领料单创建人code" , example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "领料单创建时间" , example = "2021-05-11")
    private Date erpCreateTime;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "工单号" )
    private String workOrder;

}
