package com.inossem.wms.common.model.bizdomain.demandplan.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 需求计划物料查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "需求计划物料查询条件")
public class BizReceiptDemandPlanMatSearchPO extends PageCommon {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物料组")
    private String matGroupName;

    @ApiModelProperty(value = "物料编码")
    private String matCode;
    
    @ApiModelProperty(value = "物料描述") 
    private String matName;

    @ApiModelProperty(value = "需求计划类型")
    private Integer demandPlanType;
} 