package com.inossem.wms.common.model.bizdomain.virtual.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/21 15:45
 * @desc BizReceiptVirtualOutputApplyHead 虚拟出入库申请单抬头
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "虚拟出入库申请单抬头", description = "虚拟出入库申请单抬头")
@TableName("biz_receipt_virtual_output_apply_head")
public class BizReceiptVirtualOutputApplyHead implements Serializable {
    private static final long serialVersionUID = -2590095607513263171L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "送货通知单号", example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型  送货通知：220", example = "220")
    private Integer receiptType;

    @ApiModelProperty(value = "草稿：10  送货中：120  已到货：121", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "申请单描述")
    private String applyDescribe;

}
