package com.inossem.wms.common.model.proxy.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.inossem.wms.common.annotation.RlatAttr;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* @Author: SCDM-Tools-constructor
* @Date:   2023-07-14
*/

@Data
@TableName("biz_proxy_user")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ProxyUserDTO对象", description = "DTO")
public class ProxyUserDTO implements Serializable{

	private String receiptStatusI18n;

	private String clientDeptCode;

	private String clientDeptName;

	private String delegateDeptCode;

	private String delegateDeptName;

	@RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "clientDeptCode,clientDeptName")
	@ApiModelProperty(value = "委托人部门")
	private Long clientDeptId;

	@RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "delegateDeptCode,delegateDeptName")
	@ApiModelProperty(value = "被委托人部门")
	private Long delegateDeptId;
	/***********************************************************/

	@ApiModelProperty(value = "id")
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	@ApiModelProperty(value = "编码")
	private String receiptCode;

	private Integer receiptStatus;

	@ApiModelProperty(value = "是否生效")
	private Integer isEnable;

	@ApiModelProperty(value = "被委托人被")
	private Long delegateUserId;

	private String delegateUserCode;

	private String delegateUserName;

	@ApiModelProperty(value = "委托人")
	private Long clientUserId;

	@ApiModelProperty(value = "委托人")
	private String clientUserCode;

	@ApiModelProperty(value = "委托人")
	private String clientUserName;

	@ApiModelProperty(value = "开始时间")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date startTime;

	@ApiModelProperty(value = "中止时间")
	private Date stopTime;

	@ApiModelProperty(value = "结束时间")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date endTime;

	@ApiModelProperty(value = "备注")
	private String remark;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "修改时间")
	private Date modifyTime;

	@TableLogic(delval = "id")
	@ApiModelProperty(value = "删除标识")
	private Long isDelete;

	@ApiModelProperty(value = "创建人id" , required = false)
	private Long createUserId;

	@ApiModelProperty(value = "修改人id" , required = false)
	private Long modifyUserId;

	@ApiModelProperty(value = "创建人", required = false)
	private String createUserName;

	@ApiModelProperty(value = "创建人编码", required = false)
	private String createUserCode;

	@ApiModelProperty(value = "修改人", required = false)
	private String modifyUserName;

	@ApiModelProperty(value = "修改人编码", required = false)
	private String modifyUserCode;

}
