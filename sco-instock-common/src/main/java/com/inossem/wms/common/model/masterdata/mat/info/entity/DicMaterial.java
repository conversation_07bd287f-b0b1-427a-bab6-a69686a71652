package com.inossem.wms.common.model.masterdata.mat.info.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 物料描述表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicMaterial对象", description = "物料描述表")
@TableName("dic_material")
public class DicMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "成套主物料id")
    private Long parentMatId;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "品名（英文）" , example = "物料描述001003")
    private String matNameEn;

    @ApiModelProperty(value = "计量单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "物料组id" , example = "1")
    private Long matGroupId;

    @ApiModelProperty(value = "物料类型id", example = "1")
    private Long matTypeId;

    @ApiModelProperty(value = "长度" , example = "100")
    private BigDecimal length;

    @ApiModelProperty(value = "宽度" , example = "100")
    private BigDecimal width;

    @ApiModelProperty(value = "高度" , example = "100")
    private BigDecimal height;

    @ApiModelProperty(value = "长度/宽度/高度的单位" , example = "M")
    private String unitLength;

    @ApiModelProperty(value = "毛重" , example = "100")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重" , example = "100")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "重量容差" , example = "正负5%")
    private BigDecimal weightTolerance;

    @ApiModelProperty(value = "重量的单位" , example = "KG")
    private String unitWeight;

    @ApiModelProperty(value = "体积" , example = "1000000")
    private BigDecimal volume;

    @ApiModelProperty(value = "体积的单位" , example = "M3")
    private String unitVolume;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLife;

    @ApiModelProperty(value = "保质期的单位 D-天 M-月" , example = "M")
    private String unitShelfLife;

    @ApiModelProperty(value = "是否启用保质期【1是，0否】" , example = "1")
    private Integer isShelfLife;

    @ApiModelProperty(value = "是否冻结【1是，0否】" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "是否危险物料【1是，0否】" , example = "0")
    private Integer isDangerous;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "最小货架寿命")
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "存储级别")
    private String eprio;
    private Integer mainFlag;

    @ApiModelProperty(value = "成套设备自编码物料置为1，其他物料为0")
    private Integer isCtCode;


    /* 2024-07-29 SDW二期新增字段 */
    @ApiModelProperty(value = "SAP响应字段 ZZJBWL 基本物料")
    private String extBasicMaterial;

    @ApiModelProperty(value = "SAP响应字段 ZZBDJ 质保等级")
    private String extWarrantyLevel;

    @ApiModelProperty(value = "SAP响应字段 ZZBDJT 质保等级描述")
    private String extWarrantyLevelDesc;

    @ApiModelProperty(value = "SAP响应字段 ZSFHJG 是否核安全局监管备件")
    private String extIsUnderNuclearSafetySupervision;

    @ApiModelProperty(value = "SAP响应字段 ZSFHJGT 是否核安全局监管备件描述")
    private String extIsUnderNuclearSafetySupervisionDesc;

    @ApiModelProperty(value = "SAP响应字段 ZZZTZ 制造厂图纸号")
    private String extManufacturerDrawingNumber;

    @ApiModelProperty(value = "SAP响应字段 ZZZTX 制造厂图项号")
    private String extManufacturerDrawingItemNumber;

    @ApiModelProperty(value = "SAP响应字段 ZZCCJ 制造厂家")
    private String extManufacturer;

    @ApiModelProperty(value = "SAP响应字段 ZZZSBH 制造商编号")
    private String extManufacturerCode;

    @ApiModelProperty(value = "SAP响应字段 ZZZSBHT 制造商名称")
    private String extManufacturerName;

    @ApiModelProperty(value = "SAP响应字段 MFRPN 制造商零件编号")
    private String extManufacturerPartNumber;

    @ApiModelProperty(value = "SAP响应字段 ZSFFSX 是否带放射性")
    private String extIsRadioactive;

    @ApiModelProperty(value = "SAP响应字段 ZSFFSXT 是否带放射性描述")
    private String extIsRadioactiveDesc;

    @ApiModelProperty(value = "SAP响应字段 ZWZFL 物资分类")
    private String extMaterialClassification;

    @ApiModelProperty(value = "SAP响应字段 ZWZFLT 物资分类描述")
    private String extMaterialClassificationDesc;

    @ApiModelProperty(value = "SAP响应字段 ZCGDDWB 采购订单文本")
    private String extPurchaseOrderText;

    @ApiModelProperty(value = "SAP响应字段 BKLAS 评估类")
    private String extEvaluationClassification;

    @ApiModelProperty(value = "SAP响应字段 BKBEZ 评估类的描述")
    private String extEvaluationClassificationDesc;

    @ApiModelProperty(value = "SAP响应字段 WRKST 基本物料")
    private String extMainMaterial;

    @ApiModelProperty(value = "SAP响应字段 NORMT 行业标准描述")
    private String extIndustryStandardDesc;

    @ApiModelProperty(value = "去年采购量", example = "1000.00")
    private BigDecimal lastYearPurchaseQty;

    @ApiModelProperty(value = "去年消耗量", example = "800.00")
    private BigDecimal lastYearConsumeQty;

}
