package com.inossem.wms.common.model.bizdomain.room.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 住房结算单维修费信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomReceiptSettlementRepair对象", description="住房结算单维修费信息表")
@TableName("biz_room_receipt_settlement_repair")
public class BizRoomReceiptSettlementRepair implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "房间id（冗余存储）")
    private Long roomId;

    @ApiModelProperty(value = "房间编号【楼栋号-房间号】（冗余存储）")
    private String roomCode;

    @ApiModelProperty(value = "房间使用记录抬头表id")
    private Long roomUsageHeadId;

    @ApiModelProperty(value = "住房报修单抬头表id")
    private Long receiptRepairHeadId;

    @ApiModelProperty(value = "维修总价")
    private BigDecimal repairTotalPrice;


}
