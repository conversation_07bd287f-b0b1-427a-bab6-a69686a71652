package com.inossem.wms.common.model.bizdomain.room.dto;

import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 住房申请单抬头DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomReceiptCheckInReqHeadDTO", description="住房申请单抬头DTO")
public class BizRoomReceiptCheckInReqHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /* ********************** 扩展字段开始 *************************/

    @SonAttr(sonTbName = "biz_room_receipt_check_in_req_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "住房申请单行项目列表")
    List<BizRoomReceiptCheckInReqItemDTO> itemList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "入住人员国籍（1：中国，2：巴基斯坦，3：其他国家）")
    private String checkInUserNationalityI18n;

    @ApiModelProperty(value = "申请类型（1：申请房间，2：现有房间）")
    private String reqTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 住房申请：9301")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "申请事由")
    private String reqReason;

    @ApiModelProperty(value = "入住人员国籍（1：中国，2：巴基斯坦，3：其他国家）")
    private Integer checkInUserNationality;

    @ApiModelProperty(value = "申请类型（1：申请房间，2：现有房间）")
    private Integer reqType;

    @ApiModelProperty(value = "房间使用开始时间（申请交房时间）")
    private Date startUsageTime;

    @ApiModelProperty(value = "申请房间id（申请类型为现有房间时此列有值）")
    private Long reqRoomId;

    @ApiModelProperty(value = "申请房间编号【楼栋号-房间号】")
    private String reqRoomCode;

    @ApiModelProperty(value = "是否内部使用（1：是内部使用的，0或其他：非内部使用）")
    private Integer isInternalUsage;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;


}
