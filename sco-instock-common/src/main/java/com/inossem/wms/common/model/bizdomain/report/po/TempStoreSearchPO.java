package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 暂存物资库存统计报表 PO
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "TempStoreSearchPO", description = "TempStoreSearchPO")
public class TempStoreSearchPO extends PageCommon {

    @ApiModelProperty(value = "当前日期时间")
    private Date now;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料编码" , example = "101232492")
    private String matCode;
    private List<String> matCodeList;

    @ApiModelProperty(value = "物料描述", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "批次号" , example = "301232492")
    private String batchCode;
    private List<String> batchCodeList;

    @ApiModelProperty(value = "暂存人")
    private String tempStoreUser;

    @ApiModelProperty(value = "暂存科室名称")
    private String tempStoreDeptOfficeName;

    @ApiModelProperty(value = "暂存部门名称")
    private String tempStoreDeptName;

    @ApiModelProperty(value = "暂存原因")
    private String tempStoreReason;

    @ApiModelProperty(value = "入库日期")
    private Date inputDateStart;

    @ApiModelProperty(value = "入库日期")
    private Date inputDateEnd;

    @ApiModelProperty(value = "到期日期")
    private Date tempStoreExpireDateStart;

    @ApiModelProperty(value = "到期日期")
    private Date tempStoreExpireDateEnd;

    @ApiModelProperty(value = "到期类型")
    private String expireType;

    @ApiModelProperty(value = "临期/到期天数")
    private Integer expireDays;

}
