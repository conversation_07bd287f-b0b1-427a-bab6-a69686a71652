package com.inossem.wms.common.model.bizdomain.report.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库存周转率 查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存周转率 查询入参传输对象", description = "库存周转率 查询入参")
public class StockTurnoverPO {


    @ApiModelProperty(value = "物料组id" , example = "1")
    private Long matGroupId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "开始月份" ,hidden = true , example = "2021-05")
    private String startMonth;

    @ApiModelProperty(value = "结束月份" ,hidden = true , example = "2021-06")
    private String endMonth;
}
