package com.inossem.wms.common.model.metadata.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/4 15:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "部门科室提交表单对象", description = "部门科室提交表单对象")
public class MetaDataDeptOfficePO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 4314459288508931222L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "公司id")
    private Long corpId;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @ApiModelProperty(value = "部门描述")
    private String deptName;

    @ApiModelProperty(value = "部门类别")
    private String deptType;

    @ApiModelProperty(value = "部门类别-国际化")
    private String deptTypeI18n;

    @ApiModelProperty(value = "科室id")
    private Long deptOfficeId;

    @ApiModelProperty(value = "科室编码")
    private String deptOfficeCode;

    @ApiModelProperty(value = "科室描述")
    private String deptOfficeName;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户所属部门科室标识")
    private String alreadySetting;

    @ApiModelProperty(value = "审批级别")
    private String jobLevel;

    @ApiModelProperty(value = "用户名")
    private List<String> userName;
}
