package com.inossem.wms.common.model.bizdomain.transport.vo;

import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportApplyItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 调拨出库单前续单据
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-24
 */
@ApiModel(value = "调拨出库单前续单据（调拨申请单）", description = "调拨出库单前续单据（调拨申请单）")
@Data
public class BizReceiptTransportOutPreHeadVo {

    @ApiModelProperty(value = "调拨申请单据号" , example = "SH01000005")
    private String receiptCode;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-11")
    private Date createTime;

    @ApiModelProperty(value = "创建人name" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "行项目-入库单")
    private List<BizReceiptTransportApplyItemDTO> children;

}
