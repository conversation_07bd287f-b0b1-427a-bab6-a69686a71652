package com.inossem.wms.common.constant.sap;

/**
 * Sap常量类
 *
 * <AUTHOR>
 * @date 2022/03/10 15:11
 **/
public class SapConst {

    /**
     * 调用SAP默认用户
     */
    public static final String DEFAULT_USER_CODE = "admin";

    /* ******************************** 调用SAP接口，接口类型常量 ************************************************/

    /**
     * 01接口类型
     */
    public static final String TYPE_ONE = "01";

    /**
     * 02接口类型
     */
    public static final String TYPE_TWO = "02";

    /**
     * 创建物料凭证接口类型
     */
    public static final String TYPE_CREATE_STOCK_BIN_INFO = "02";

    /**
     * 转储接口类型
     */
    public static final String TYPE_TRANS = "03";

    /**
     * XXXXX接口类型
     */
    public static final String TYPE_XXXXX = "04";
    /**
     * XXX接口类型
     */
    public static final String TYPE_XXXXXX = "05";
    /**
     * XXX接口类型
     */
    public static final String TYPE_XXXXXXX = "06";
    /**
     * XXX接口类型
     */
    public static final String TYPE_XXXXXXXX = "07";
    /**
     * STOCK接口类型
     */
    public static final String TYPE_STOCK = "08";
    /**
     * CANCEL_STOCK_BIN 取消物料凭证 接口类型
     */
    public static final String TYPE_CANCEL_STOCK_BIN = "09";
    /**
     * DIFF_POST 盘点差异过账 接口类型
     */
    public static final String TYPE_DIFF_POST = "10";

    /* ******************************** 调用SAP接口时，接口类型常量 ************************************************/

    /* ******************************** 调用SAP接口，接口方法名常量 ************************************************/

    /**
     * Erp采购订单接口
     */
    public static final String ERP_PURCHASE_RECEIPT_POST = "cgdd01";
    /**
     * 过账接口
     */
    public static final String NAME_POST = "cjpz08";

    /**
     * 物料转码-Y81/Y82过账接口
     */
    public static final String NAME_TRANSPORT_MAT_DIFF_UNIT_POST = "gdsmvy8";

    /**
     * 冲销接口
     */
    public static final String NAME_WRITEOFF = "qxpz09";
    /**
     * 创建预留单
     */
    public static final String NAME_CREATE_RESERVE = "cjyl12";
    /**
     * 创建物料凭证接口
     */
    public static final String NAME_CREATE_STOCK_BIN_INFO = "cjpz08";
    /**
     * 关闭预留、删除预留(整单)
     */
    public static final String NAME_WRITE_OFF = "cxyl13";
    /**
     * 同步物料接口
     */
    public static final String SYN_MATERIAL = "dqwl02";
    /**
     *
     */
    public static final String NAME_05 = "cgdd01";
    /**
     *
     */
    public static final String NAME_06 = "cgdd01";
    /**
     *
     */
    public static final String NAME_07 = "cgdd01";
    /**
     *
     */
    public static final String NAME_08 = "cgdd01";
    /**
     *
     */
    public static final String NAME_WBS = "cbzx03";

    /**
     * 收发存报表
     */
    public static final String NAME_JXCBB = "jxcbb";

    /**
     * 收发存报表
     */
    public static final String NAME_ATT_SYN = "att_syn";


    /**
     * Erp 领料单接口
     */
    public static final String ERP_RECEIVE_RECEIPT_POST = "dlld16";

    /* ******************************** 调用SAP接口时，接口方法名常量 ************************************************/

}
