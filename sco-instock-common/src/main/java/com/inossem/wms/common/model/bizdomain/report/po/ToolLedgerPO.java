package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 入库台账报表 PO
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ToolLedgerPO", description="ToolLedgerPO")
public class ToolLedgerPO extends PageCommon {

    @ApiModelProperty(value = "采购订单" , example = "4500000001")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
