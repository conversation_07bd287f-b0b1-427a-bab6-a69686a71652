package com.inossem.wms.common.model.stock.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: jhr
 * @Date: 2021/5/31 14:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StockBinConditionDTO {
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;
}
