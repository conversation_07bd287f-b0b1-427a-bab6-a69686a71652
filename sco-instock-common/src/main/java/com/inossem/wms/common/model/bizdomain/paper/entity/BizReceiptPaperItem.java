package com.inossem.wms.common.model.bizdomain.paper.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 图纸物料信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptPaperItem对象", description="图纸物料信息表")
@TableName("biz_receipt_paper_item")
public class BizReceiptPaperItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "设计院图纸物料信息表ID")
    private String npiotPaperMatInfoDesignId;

    @ApiModelProperty(value = "关联图纸ID")
    private String paperId;

    @ApiModelProperty(value = "部件号")
    private String partNo;

    @ApiModelProperty(value = "物资编码")
    private String matnr;

    @ApiModelProperty(value = "旧物料号（LRCM)")
    private String oldMatnr;

    @ApiModelProperty(value = "SAP物料号")
    private String sapMatnr;

    @ApiModelProperty(value = "其他代码")
    private String otherMatnr;

    @ApiModelProperty(value = "功能位置码")
    private String funcNo;

    @ApiModelProperty(value = "物资描述")
    private String descText;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "材质")
    private String material;

    @ApiModelProperty(value = "RCCM")
    private String rccm;

    @ApiModelProperty(value = "质保级别(01:A,02:A1,03:A2,04:B,05:C,06:NQR,07:Q1,08:Q2,09:Q3,10:Q4,11:QNC,12:QNCA,13:QNCB,14:QNCC,15:QR1,16:QR2,17:QR3,注:NA表示不适用,其他的字符本身无中文字义,中文也是说Q1级)")
    private String warrLev;

    @ApiModelProperty(value = "数量")
    private BigDecimal amount;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "加工尺寸")
    private String size;

    @ApiModelProperty(value = "物料类别")
    private String materType;

    @ApiModelProperty(value = "预制或安装")
    private String preFlg;

    @ApiModelProperty(value = "系统")
    private String paperSystem;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "厂房")
    private String factoryBuilding;

    @ApiModelProperty(value = "标高")
    private String level;

    @ApiModelProperty(value = "单重(kg)")
    private BigDecimal substance;

    @ApiModelProperty(value = "总重(kg)")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "组件或成品代码")
    private String comCode;

    @ApiModelProperty(value = "组件或成品描述")
    private String comDes;

    @ApiModelProperty(value = "组件或成品数量")
    private BigDecimal comAmount;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "项目编号")
    private String projCode;

    @ApiModelProperty(value = "工程文件编码")
    private String fileCode;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "卷标")
    private String fileMark;

    @ApiModelProperty(value = "状态")
    private String statusCode;

    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String island;

    @ApiModelProperty(value = "机组")
    private String term;

    @ApiModelProperty(value = "专业(EM1,EM2,EM3,EM4.1,EM4.2,EM5,EM6,EM7,EM8,EM9,EM10,CE:土建,ME:机械,HP:保温,PI:管道,EL:电气,IN:仪表,HVAC:暖通空调)")
    private String major;

    @ApiModelProperty(value = "房间")
    private String room;

    @ApiModelProperty(value = "图纸备注")
    private String paperNote;

    @ApiModelProperty(value = "是否是最新版")
    private String newVersion;

    @ApiModelProperty(value = "图纸清单更新者")
    private String updater;

    @ApiModelProperty(value = "图纸清单最后更新时间")
    private String lastUpdateTime;

    @ApiModelProperty(value = "物料裕量类别")
    private String plentCallType;

    @ApiModelProperty(value = "是否为组件：0是，1否")
    private String iscom;

    @ApiModelProperty(value = "拆分件ID")
    private String splitId;

    @ApiModelProperty(value = "组件ID")
    private String comId;

    @ApiModelProperty(value = "状态标识C：新建 U：修改 D：逻辑删除")
    private String status;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateOperator;

    @ApiModelProperty(value = "创建时间")
    private Date paperCreateTime;

    @ApiModelProperty(value = "创建人")
    private String createOperator;

    @ApiModelProperty(value = "预留字段1")
    private String extend1;

    @ApiModelProperty(value = "预留字段2")
    private String extend2;

    @ApiModelProperty(value = "预留字段3")
    private String extend3;

    @ApiModelProperty(value = "预留字段4")
    private String extend4;

    @ApiModelProperty(value = "预留字段5")
    private String extend5;

    @ApiModelProperty(value = "图纸输机净量")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "消耗系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "需求数量(图纸输机净量×消耗系数)")
    private BigDecimal qty;

    @ApiModelProperty(value = "累计需求提报数量")
    private BigDecimal requiredQty;

}
