package com.inossem.wms.common.model.bizdomain.electronicscale.dto;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *  电子秤移动记录DTO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ElectronicScaleRecord对象", description="")
public class LogElectronicScaleRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 净重" , example = "100")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "重量的单位" , example = "KG")
    private String unitWeight;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "电子秤ID")
    private String electronicScaleId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,netWeight,unitWeight", targetAttrName = "matCode,matName,netWeight,unitWeight")
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "当前重量")
    private BigDecimal currentWeight;

    @ApiModelProperty(value = "变化重量")
    private BigDecimal variableWeight;

    @ApiModelProperty(value = "当前数量")
    private BigDecimal currentQty;

    @ApiModelProperty(value = "变化数量")
    private BigDecimal variableQty;

    @ApiModelProperty(value = "重量单位")
    private String weightUnit;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
