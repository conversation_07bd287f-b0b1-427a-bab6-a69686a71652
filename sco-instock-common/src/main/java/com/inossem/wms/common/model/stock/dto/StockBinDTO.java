package com.inossem.wms.common.model.stock.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓位库存传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "仓位库存传输对象", description = "仓位库存传输对象")
public class StockBinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /************************* 自定义扩展 *****************************/

    @ApiModelProperty(value = "选择的agv", name = "agvBinDTO")
    private DicWhStorageBinDTO agvBinDTO;

    @ApiModelProperty(value = "选择的暂存区", name = "temporaryBinDTO")
    private DicWhStorageBinDTO temporaryBinDTO;
    /**
     * 操作数量
     */
    @ApiModelProperty(value = "操作数量", name = "10")
    private BigDecimal operationQty;
    /**
     * 标签集合
     */
    private List<BizLabelData> labelDataList;

    /**
     * 批次图片
     */
    @ApiModelProperty(value = "批次图片")
    private List<BizBatchImgDTO> batchImgList;
    /**
     * 特性值
     */
    @ApiModelProperty(value = "特性值" , example = "111111,1")
    private String specValue;

    @ApiModelProperty(value = "提交重量" , example = "10")
    private BigDecimal variableWeight;

    /************************* 自定义扩展 *****************************/

    /* ********************** 扩展字段 *************************/
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", name = "matCode", example = "80012345", required = true)
    private String matCode;

    @ApiModelProperty(value = "物料描述", name = "matName", example = "80012345", required = true)
    private String matName;

    /**
     * 单位编码id
     */
    @ApiModelProperty(value = "单位ID", name = "unitId", example = "7", required = true)
    private Long unitId;
    /**
     * 单位编码code
     */
    @ApiModelProperty(value = "单位编码", name = "unitCode", example = "EA", required = true)
    private String unitCode;

    @ApiModelProperty(value = "单位描述", name = "unitName", example = "EA", required = true)
    private String unitName;
    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码", name = "ftyCode", example = "1206", required = true)
    private String ftyCode;
    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂描述", name = "ftyName", example = "1206", required = true)
    private String ftyName;
    /**
     * 库存地点编码
     */
    @ApiModelProperty(value = "库存地点编码", name = "locationCode", example = "0001", required = true)
    private String locationCode;
    /**
     * 库存地点编码
     */
    @ApiModelProperty(value = "库存地点描述", name = "locationName", example = "0001", required = true)
    private String locationName;
    /**
     * 仓库号
     */
    @ApiModelProperty(value = "仓库号", name = "whCode", example = "S200", required = true)
    private String whCode;
    /**
     * 仓库号
     */
    @ApiModelProperty(value = "仓库描述", name = "whName", example = "S200", required = true)
    private String whName;
    /**
     * 存储类型
     */
    @ApiModelProperty(value = "存储类型", name = "typeCode", example = "Z01", required = true)
    private String typeCode;
    /**
     * 存储类型
     */
    @ApiModelProperty(value = "存储类型描述", name = "typeName", example = "Z01", required = true)
    private String typeName;
    /**
     * 仓位
     */
    @ApiModelProperty(value = "仓位", name = "binCode", example = "00", required = true)
    private String binCode;

    @ApiModelProperty(value = "存储单元", name = "cellCode", example = "P001", required = true)
    private String cellCode;

    /**
     * 批次信息传输
     */
    @ApiModelProperty(value = "批次信息传输")
    private BizBatchInfoDTO batchInfo;

    @ApiModelProperty(value = "非限制库存数量" , example = "100")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "在途库存数量" , example = "50")
    private BigDecimal stockQtyTransfer;

    @ApiModelProperty(value = "质检库存数量" , example = "50")
    private BigDecimal stockQtyInspection;

    @ApiModelProperty(value = "冻结库存数量" , example = "50")
    private BigDecimal stockQtyFreeze;

    @ApiModelProperty(value = "紧急库存数量" , example = "50")
    private BigDecimal stockQtyHaste;

    @ApiModelProperty(value = "临时库存数量" , example = "50")
    private BigDecimal stockQtyTemp;

    @ApiModelProperty(value = "批次库存id" , example = "152221184491522")
    private Long stockBatchId;

    @ApiModelProperty(value = "小数位" , example = "3")
    private Integer decimalPlace;
    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,grossWeight", targetAttrName = "matCode,matName,grossWeight")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "batchInfo")
    @ApiModelProperty(value = "批次信息id" , example = "152286145871874")
    private Long batchId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode", targetAttrName = "ftyCode")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode", targetAttrName = "locationCode")
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode", targetAttrName = "whCode")
    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,isDefault", targetAttrName = "typeCode,isDefault")
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @RlatAttr(rlatTableName = "dic_wh_storage_cell", sourceAttrName = "cellCode", targetAttrName = "cellCode")
    @ApiModelProperty(value = "存储单元ID" , example = "152758218981377")
    private Long cellId;

    @ApiModelProperty(value = "非限制库存数量" , example = "100")
    private BigDecimal qty;

    @ApiModelProperty(value = "在途库存数量" , example = "50")
    private BigDecimal qtyTransfer;

    @ApiModelProperty(value = "质检库存数量" , example = "50")
    private BigDecimal qtyInspection;

    @ApiModelProperty(value = "冻结库存数量" , example = "50")
    private BigDecimal qtyFreeze;

    @ApiModelProperty(value = "紧急库存数量" , example = "50")
    private BigDecimal qtyHaste;

    @ApiModelProperty(value = "临时库存数量" , example = "50")
    private BigDecimal qtyTemp;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "入库日期" , example = "2021-05-10")
    private Date inputDate;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productionDate;

    @ApiModelProperty(value = "1:默认  0：非默认" , example = "1")
    private Integer isDefault;

    @ApiModelProperty(value = "物料毛重（kg）" , example = "100")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "仓位承重（kg）" , example = "100")
    private BigDecimal binWeight;

}
