package com.inossem.wms.common.model.bizdomain.output.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 工器具报废申请工具查询PO:
 * <AUTHOR>
 * @date 2022/03/30 20:02
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "工器具报废申请工具查询PO", description = "工器具报废申请工具查询PO")
public class BizToolScrapApplySearchPO implements Serializable {

    private static final long serialVersionUID = 37394579234L;

    @ApiModelProperty(value = "工具编码" , example = "6516481")
    private Long toolCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "物料编码" , example = "物料编码001003")
    private String matCode;

    @ApiModelProperty(value = "出厂编码" , example = "出厂编码001")
    private String factoryCode;

    @ApiModelProperty(value = "仓位" , example = "仓位Code")
    private String binCode;


}
