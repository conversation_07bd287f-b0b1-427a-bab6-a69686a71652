package com.inossem.wms.common.model.account.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账期传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "账期传输对象", description = "账期传输对象")
public class DicAccountPeriodDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    /**
     * 公司编码
     */
    @ApiModelProperty(value = "公司编码" , example = "1000", required = false)
    private String corpCode;

    /**
     * 公司描述
     */
    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "账期id" , example = "***************", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司id" , example = "1", required = false)
    private Long corpId;

    @ApiModelProperty(value = "年度" , example = "2021", required = false)
    private Integer accountYear;

    @ApiModelProperty(value = "月份" , example = "5", required = false)
    private Integer accountMonth;

    @ApiModelProperty(value = "记账开始日期" , example = "2021-05-01", required = false)
    private Date accountBeginDate;

    @ApiModelProperty(value = "记账终止日期" , example = "2021-05-31", required = false)
    private Date accountEndDate;

    @ApiModelProperty(value = "记账实际日期" , example = "2021-05-15", required = false)
    private Date accountFactDate;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
