package com.inossem.wms.common.model.bizdomain.service.vo;

import com.inossem.wms.common.model.bizdomain.service.dto.BizReceiptServiceFacilityItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 服务工单供应商设施行项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptServiceSupplierFacilityItemVO对象", description = "服务工单供应商设施行项目表")
public class BizReceiptServiceSupplierFacilityItemVO implements Serializable {

    @ApiModelProperty(value = "供应商编码", example = "1000")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "英诺森")
    private String supplierName;

    @ApiModelProperty("服务工单设施行项目表")
    private List<BizReceiptServiceFacilityItemDTO> facilityItemDTOList;

}
