package com.inossem.wms.common.model.bizdomain.apply.po;

import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptToolBorrowApplyItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/9/2 10:10
 * @desc BizReceiptMatStockQueryPO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存特性查询PO", description = "库存特性查询PO")
public class BizReceiptMatStockQueryPO implements Serializable {
    private static final long serialVersionUID = 771138048318277792L;

    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;
    @ApiModelProperty(value = "物料id集合", example = "60000001")
    private Set<Long> matIdSet;
    @ApiModelProperty(value = "工厂id", example = "145343907954689")
    private Long ftyId;
    @ApiModelProperty(value = "库存地点id", example = "145725436526593")
    private Long locationId;
    @ApiModelProperty(value = "特殊库存类型E、K、O、Q", example = "Q")
    private String specStock;
    @ApiModelProperty(value = "库存类型集合", example = "1")
    protected Integer stockStatus;
    private Long batchId;
    @ApiModelProperty(value = "物料描述")
    private String matName;
    private String matCode;
    @ApiModelProperty(value = "仓位id")
    private Long binId;
    @ApiModelProperty(value = "特殊库存代码(WBS)")
    private String specStockCode;
    @ApiModelProperty(value = "批次号")
    private String batchCode;
    private String binCode;
    private Long whId;
    private String whCode;
    private Integer receiptType;
    private Long typeId;
    private List<BizReceiptToolBorrowApplyItemDTO> itemList;

    private List<String> matCodeList;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "UP码")
    private String extend29;

    @ApiModelProperty(value = "物资编码")
    private String extend20;

}
