package com.inossem.wms.common.model.bizdomain.room.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 住房结算单-其他费用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizRoomReceiptSettlementOther对象", description="住房结算单-其他费用信息表")
@TableName("biz_room_receipt_settlement_other")
public class BizRoomReceiptSettlementOther implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "费用来源")
    private String costSource;

    @ApiModelProperty(value = "备注说明")
    private String remarks;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;


}
