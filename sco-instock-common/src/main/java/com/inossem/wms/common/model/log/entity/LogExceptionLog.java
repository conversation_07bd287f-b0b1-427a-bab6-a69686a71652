package com.inossem.wms.common.model.log.entity;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 异常日志表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "LogExceptionLog对象", description = "异常日志表")
@TableName("log_exception_log")
public class LogExceptionLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户ID", name = "userId", example = "1")
    private Long userId;

    @ApiModelProperty(value = "用户编码" , example = "管理员")
    private String userName;

    @ApiModelProperty(value = "返回结果url" , example = "http:/*************:8087/error")
    private String requestUrl;

    @ApiModelProperty(value = "异常信息" , example = "未认证登录")
    private String exceptionMessage;

    @ApiModelProperty(value = "任务记录" , example = "String")
    private String stackTrace;

    @ApiModelProperty(value = "处理结果描述" , example = "String")
    private String execStatus;

    @ApiModelProperty(value = "处理结果编码" , example = "String")
    private String execCode;

    @ApiModelProperty(value = "处理说明" , example = "String")
    private String execDescription;

    @ApiModelProperty(value = "创建日期" , example = "2021-05-11")
    private Date createDate;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private LocalTime createTime;

}
