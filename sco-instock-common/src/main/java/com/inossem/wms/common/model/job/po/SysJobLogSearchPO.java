package com.inossem.wms.common.model.job.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/3/3 18:22
 *
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SysJobLog对象", description = "定時任務查詢日志")

public class SysJobLogSearchPO extends PageCommon implements Serializable {
    private static final long serialVersionUID = 2882431973495736307L;
    @ApiModelProperty(value = "任务名称" , example = "test")
    private String jobName;

    @ApiModelProperty(value = "任务组名" , example = "1")
    private String jobGroup;

    @ApiModelProperty(value = "状态（0正常 1暂停）" , example = "0")
    private String status;

    @ApiModelProperty(value = "调用目标字符串" , example = "com.inossem.wms.system.job.controller.JobController.test")
    private String invokeTarget;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;
}
