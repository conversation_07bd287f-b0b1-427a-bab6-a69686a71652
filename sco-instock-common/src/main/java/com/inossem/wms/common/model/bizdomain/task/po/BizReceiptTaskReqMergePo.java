package com.inossem.wms.common.model.bizdomain.task.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 合并下架作业
 */
@ApiModel(value = "合并下架作业", description = "合并下架作业")
@Data
@Accessors(chain = true)
public class BizReceiptTaskReqMergePo implements Serializable {

    private static final long serialVersionUID = 7154793125279306858L;

    @ApiModelProperty(value = "下架请求单id")
    private List<Long> idList;
}
