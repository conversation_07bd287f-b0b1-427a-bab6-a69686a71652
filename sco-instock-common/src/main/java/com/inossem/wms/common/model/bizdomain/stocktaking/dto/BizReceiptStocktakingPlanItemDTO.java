package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
@ApiModel(value = "库存盘点行项目传输对象", description = "库存盘点行项目传输对象")
public class BizReceiptStocktakingPlanItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BizReceiptStocktakingPlanItemDTO itemDTO = (BizReceiptStocktakingPlanItemDTO) o;
        return whId.equals(itemDTO.whId) &&
                typeId.equals(itemDTO.typeId) &&
                binId.equals(itemDTO.binId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(whId, typeId, binId);
    }

    /* ********************** 扩展字段开始 *************************/
    @ApiModelProperty(value = "盘点计数人名称" , example = "admin")
    private String countingUserName;

    @ApiModelProperty(value = "盘点物料")
    @SonAttr(sonTbName = "biz_receipt_stocktaking_bin", sonTbFkAttrName = "itemId")
    private List<BizReceiptStocktakingPlanBinDTO> binList;

    @ApiModelProperty(value = "仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "存储类型编码" , example = "801")
    private String typeCode;

    @ApiModelProperty(value = "存储类型描述" , example = "入库临时区")
    private String typeName;

    @ApiModelProperty(value = "仓位编码" , example = "00")
    private String binCode;

    @ApiModelProperty(value = "行项目状态描述", example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "仓位集合 PDA行项目层级显示专用", example = "00")
    private List<String> binCodeList;

    @ApiModelProperty(value = "盘点类型：0-首盘，1-复盘", example = "0")
    private Integer isReplay;

    @ApiModelProperty(value = "盘点方式：1-明盘，2-盲盘")
    private Integer stocktakingMode;

    @ApiModelProperty(value = "认领人")
    private String modifyUserName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "请求头id", example = "157329202937857")
    private Long headId;

    @ApiModelProperty(value = "盘点单行项目号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "是否指定物料【1是，0否】", example = "1")
    private Integer isAppointMat;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "复盘的原来仓位id", example = "157329202937857")
    private Long upIteamId;

    @ApiModelProperty(value = "盘点计数人id" , example = "157329202937857")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "countingUserName")
    private Long countingUserId;

}
