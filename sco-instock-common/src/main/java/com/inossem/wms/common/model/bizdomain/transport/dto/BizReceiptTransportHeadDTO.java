package com.inossem.wms.common.model.bizdomain.transport.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 转储单抬头传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "转储单抬头传输对象", description = "转储单抬头传输对象")
public class BizReceiptTransportHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/
    @SonAttr(sonTbName = "biz_receipt_transport_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptTransportItemDTO> itemDTOList;
    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据日志")
    List<BizCommonReceiptOperationLogDTO> logList;
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    List<BizCommonReceiptAttachment> fileList;
    @SonAttr(sonTbName = "biz_common_receipt_relation", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据流")
    List<BizCommonReceiptRelationDTO> relationList;
    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;
    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;
    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;
    @ApiModelProperty(value = "整理人编码", example = "Admin", required = true)
    private String arrangeUserCode;
    @ApiModelProperty(value = "整理人名称" , example = "管理员")
    private String arrangeUserName;
    @ApiModelProperty(value = "修改人编码" , example = "Admin")
    private String modifyUserCode;
    @ApiModelProperty(value = "修改人名称" , example = "管理员")
    private String modifyUserName;
    @ApiModelProperty(value = "移动类型编码" , example = "301")
    private String moveTypeCode;
    @ApiModelProperty(value = "移动类型名称" , example = "正常转正常(工厂)")
    private String moveTypeName;
    @ApiModelProperty(value = "特殊库存标识" , example = "Q")
    private String specStock;
    @ApiModelProperty(value = "单据状态名称" , example = "草稿")
    private String receiptStatusI18n;
    @ApiModelProperty(value = "单据类型名称")
    private String receiptTypeI18n;
    @ApiModelProperty(value = "移动类型id集合" , example = "3190")
    private List<Long> moveTypeIds;
    @ApiModelProperty(value = "寿期检定单单号")
    private String preReceiptCode;
    @ApiModelProperty(value = "寿期检定单描述")
    private String preRemark;
    /* ********************** 扩展字段结束 *************************/
    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "转储单号" , example = "ZC01000318")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据类型" , example = "211")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前置单据id" , example = "149901631619073")
    @RlatAttr(rlatTableName = "biz_receipt_lifetime_head", sourceAttrName = "receiptCode,remark", targetAttrName = "preReceiptCode,preRemark")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    @RlatAttr(rlatTableName = "dic_move_type", sourceAttrName = "moveTypeCode,moveTypeName,specStock", targetAttrName = "moveTypeCode,moveTypeName,specStock")
    private Long moveTypeId;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;

    @ApiModelProperty(value = "接收特殊库存标识" , example = "Q")
    private String inputSpecStock;

    @ApiModelProperty(value = "发出特殊库存标识" , example = "Q")
    private String outSpecStock;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "整理人id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "arrangeUserCode,arrangeUserName")
    private Long arrangeUserId;

    @ApiModelProperty(value = "批次")
    private String batchCode;

    @ApiModelProperty(value = "单据描述")
    private String des;
    // 原需求部门标识: 0--非原需求部门;1--原需求部门;
    private Integer requirementFlag;
    // 原需求部门
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "requirementDeptCode,requirementDeptName")
    private Long requirementDeptId;
    // 原需求科室
    @RlatAttr(rlatTableName = "dic_dept_office",sourceAttrName = "deptOfficeCode,deptOfficeName",targetAttrName = "requirementOfficeCode,requirementOfficeName")
    private Long requirementOfficeId;
    private String requirementDeptCode;
    private String requirementDeptName;
    private String requirementOfficeCode;
    private String requirementOfficeName;

    @ApiModelProperty(value = "是否快速模式  0 否  需要上下架  1 是 直接移动")
    private Integer quickModel;

    @ApiModelProperty(value = "是否已阅 0否 1是")
    private Integer isReview;

    @ApiModelProperty(value = "单据子类型 51601（相同物料），51602（不同物料）")
    private Integer receiptSubType;

    @ApiModelProperty(value = "单据子类型 51601（相同物料），51602（不同物料）")
    private String receiptSubTypeI18n;



    @ApiModelProperty(value = "NCR编号")
    private String ncr;
}
