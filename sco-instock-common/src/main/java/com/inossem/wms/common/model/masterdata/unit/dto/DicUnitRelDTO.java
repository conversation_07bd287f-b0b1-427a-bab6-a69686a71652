package com.inossem.wms.common.model.masterdata.unit.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 物料计量单位换算数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "物料计量单位换算数据传输对象", description = "物料计量单位换算数据传输对象")
public class DicUnitRelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/
    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称" , example = "物料描述001003")
    private String matName;

    /**
     * 源单位编码
     */
    @ApiModelProperty(value = "源单位编码" , example = "M3")
    private String sourceUnitCode;

    /**
     * 源单位名称
     */
    @ApiModelProperty(value = "源单位名称" , example = "立方米")
    private String sourceUnitName;

    /**
     * 目标单位编码
     */
    @ApiModelProperty(value = "目标单位编码" , example = "M3")
    private String targetUnitCode;

    /**
     * 目标单位名称
     */
    @ApiModelProperty(value = "目标单位名称" , example = "立方米")
    private String targetUnitName;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "sourceUnitCode,sourceUnitName")
    @ApiModelProperty(value = "原单位id，来源物料主数据基本单位" , example = "1")
    private Long sourceUnitId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "targetUnitCode,targetUnitName")
    @ApiModelProperty(value = "目标单位id" , example = "1")
    private Long targetUnitId;

    @ApiModelProperty(value = "原单位数量" , example = "1")
    private BigDecimal sourceQty;

    @ApiModelProperty(value = "目标单位数量" , example = "1")
    private BigDecimal targetQty;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
