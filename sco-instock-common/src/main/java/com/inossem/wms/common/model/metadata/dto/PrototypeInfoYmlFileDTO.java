package com.inossem.wms.common.model.metadata.dto;

/**
 * <AUTHOR>
 * @date 2021/3/16 9:30
 */

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * PrototypeInfo.yml文件信息
 * 
 * <AUTHOR>
 * @date 2021/03/16 09:30
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PrototypeInfoYmlFileDTO", description = "PrototypeInfo.yml文件信息")
public class PrototypeInfoYmlFileDTO {

    @ApiModelProperty(value = "工程描述" , example = "String")
    private String projectDesc;

    @ApiModelProperty(value = "模块列表")
    private List<PrototypeInfoYmlFileModuleDTO> moduleList;
}
