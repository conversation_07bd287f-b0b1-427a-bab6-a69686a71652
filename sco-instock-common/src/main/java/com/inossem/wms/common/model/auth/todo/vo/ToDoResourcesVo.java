package com.inossem.wms.common.model.auth.todo.vo;

import java.util.List;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description="待办事项或快捷方式树形对象")
public class ToDoResourcesVo {

	@ApiModelProperty(value = "子集待办")
	private List<ToDoResourcesVo> childrens;

	@ApiModelProperty(value = "父级代办" , example = "pname")
	private String resourcesName;

	@ApiModelProperty(value = "资源id", example = "148309173927937")
	private Long resourcesId;

	@ApiModelProperty(value = "资源code", example = "purchase_inbound")
	private String resourcesCode;

	@ApiModelProperty(value = "资源code", example = "purchase_inbound")
	private String resourcesCodeI18n;

	@ApiModelProperty(value = "资源父id", example = "148314154663937")
	private Long parentId;

	@ApiModelProperty(value = "显示顺序", example = "1")
	private String displayIndex;

	@ApiModelProperty(value = "是否有效", example = "1")
	private String enabled;

	@ApiModelProperty(value = "是否已选中过", example = "1")
	private String isChecked;

}
