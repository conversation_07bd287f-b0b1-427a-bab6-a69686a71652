package com.inossem.wms.common.model.erp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 采购订单 实体
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseReceiptHead对象", description = "ErpPurchaseReceiptHead对象")
@TableName("erp_purchase_receipt_head")
public class ErpPurchaseReceiptHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "采购订单head主键" , example = "111")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "SAP采购订单号" , example = "4500000001")
    private String receiptCode;

    @ApiModelProperty(value = "SAP单据备注" , example = "单据备注")
    private String remark;

    @ApiModelProperty(value = "SAP采购订单类型  BZ：标准  FJWZ：废旧物资" , example = "BZ")
    private String erpReceiptType;

    @ApiModelProperty(value = "SAP采购订单类型描述" , example = "采购订单类型描述")
    private String erpReceiptTypeName;

    @ApiModelProperty(value = "采购退货标识 0-采购订单, 1-采购退货订单" , example = "0")
    private Integer isReturnFlag;

    @ApiModelProperty(value = "SAP采购订单创建人code" , example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "SAP采购订单创建人name" , example = "管理员")
    private String erpCreateUserName;

    @ApiModelProperty(value = "SAP采购订单创建时间" , example = "2021-05-11")
    private Date erpCreateTime;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "供应商编码" , example = "111")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称" , example = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "付款方式" , example = "1")
    private Integer paymentMethod;

    @ApiModelProperty(value = "币种" , example = "1")
    private Integer currency;

    @ApiModelProperty(value = "合同编码" , example = "111")
    private String contractCode;

    @ApiModelProperty(value = "合同名称" , example = "合同名称")
    private String contractName;    

}
