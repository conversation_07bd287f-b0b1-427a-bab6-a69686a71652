package com.inossem.wms.common.model.masterdata.supplier.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 供应商主数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DicSupplierUserRel", description="DicSupplierUserRel")
@TableName("dic_supplier_user_rel")
public class DicSupplierUserRelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户实体(导入时使用)" , example = "123456")
    private SysUser sysUser;

    @ApiModelProperty(value = "原密码" , example = "123456")
    private String oldPassword;

    @ApiModelProperty(value = "密码" , example = "123456")
    private String password;

    @ApiModelProperty(value = "手机号" , example = "1300000000")
    private String phoneNumber;

    @ApiModelProperty(value = "邮箱" , example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "phoneNumber,email",targetAttrName = "phoneNumber,email")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "逻辑删除字段")
    @TableLogic
    private Long isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
