package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 申请单行项目明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptApplyBin对象", description="申请单行项目明细表")
@TableName("biz_receipt_apply_bin")
public class BizReceiptApplyBinDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "领用出库配货信息")
    @SonAttr(sonTbName = "biz_receipt_output_bin", sonTbFkAttrName = "applyBinId")
    private List<BizReceiptOutputBinDTO> binDTOList;

    @ApiModelProperty(value = "是否冲销【1是，0否】" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "批次code" , example = "100001")
    private String batchCode;

    @ApiModelProperty(value = "UP码")
    private String extend29;

    @ApiModelProperty(value = "物料类型")
    private String extend28;

    @ApiModelProperty(value = "WBS编号")
    private String specStock;

    @ApiModelProperty(value = "WBS编号")
    private String specStockCode;

    @ApiModelProperty(value = "主设备编码")
    private String parentMatCode;

    @ApiModelProperty(value = "物资编码")
    private String extend20;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    @ApiModelProperty(value = "入库日期")
    private Date inputDate;

    @ApiModelProperty(value = "保质期")
    private Integer shelfLine;

    @ApiModelProperty(value = "到期时间")
    private Date validityDate;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "小数位" , example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "库存地点编码", name = "locationCode", example = "0001", required = true)
    private String locationCode;

    @ApiModelProperty(value = "库存地点描述", name = "locationName", example = "0001", required = true)
    private String locationName;

    @ApiModelProperty(value = "工厂编码", name = "ftyCode", example = "1206", required = true)
    private String ftyCode;

    @ApiModelProperty(value = "工厂描述", name = "ftyName", example = "1206", required = true)
    private String ftyName;

    @ApiModelProperty(value = "单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "成套主物料id")
    private Long parentMatId;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "尾差")
    private BigDecimal remainder;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "item表id")
    private Long itemId;

    @ApiModelProperty(value = "配货行序号")
    private String bid;

    @ApiModelProperty(value = "工厂id")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    private Long locationId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,parentMatId", targetAttrName = "matCode,matName,parentMatId")
    private Long matId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitName,decimalPlace", targetAttrName = "unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "批次id")
    @RlatAttr(rlatTableName = "biz_batch_info",
            sourceAttrName = "batchCode,specStock,specStockCode,parentMatId,extend20,extend28,extend29,functionalLocationCode,productionDate,inputDate,shelfLine,validityDate,price,remainder",
            targetAttrName = "batchCode,specStock,specStockCode,parentMatId,extend20,extend28,extend29,functionalLocationCode,productionDate,inputDate,shelfLine,validityDate,price,remainder")
    private Long batchId;

    @ApiModelProperty(value = "批次库存id")
    private Long stockBatchId;

    @ApiModelProperty(value = "可用库存量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "可预留量")
    private BigDecimal reserveQty;

    @ApiModelProperty(value = "已预留量")
    private BigDecimal alreadyReserveQty;

    @ApiModelProperty(value = "可冲减数量")
    private BigDecimal canSubtractQty;

    @ApiModelProperty(value = "锁定冲减数量")
    private BigDecimal lockSubtractQty;

    @ApiModelProperty(value = "冻结数量")
    private BigDecimal qtyFreeze;

    @ApiModelProperty(value = "本次申请数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "冲减数量")
    private BigDecimal subtractQty;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "已作业数量" , example = "5")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "超发数量" , example = "5")
    private BigDecimal overQty;

    @ApiModelProperty(value = "预留单号")
    private Long reservedOrderCode;
    @ApiModelProperty(value = "预留单行项目号")
    private String reservedOrderRid;

    @ApiModelProperty(value = "本次申请数量")
    private BigDecimal preQty;

    @ApiModelProperty(value = "已拆分数量")
    private BigDecimal alreadySplitQty;

    @ApiModelProperty(value = "拆分前续单据bin表id")
    private Long preApplyBinId;

    @ApiModelProperty(value = "拆分前续单据head表id")
    private Long preApplyHeadId;

    @ApiModelProperty(value = "NCR编号")
    private String ncrbh;

}
