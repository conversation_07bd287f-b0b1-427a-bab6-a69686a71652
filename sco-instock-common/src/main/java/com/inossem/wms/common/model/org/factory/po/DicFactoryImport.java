package com.inossem.wms.common.model.org.factory.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 工厂传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "工厂传输对象", description = "工厂传输对象")
public class DicFactoryImport implements Serializable {

    private static final long serialVersionUID = -6543156272395787432L;

    /* ********************** 扩展字段 *************************/
    /**
     * 库存地点集合
     */
    @SonAttr(sonTbName = "dic_stock_location", sonTbFkAttrName = "ftyId")
    @ApiModelProperty(value = "库存地点集合", name = "locationDtoList", required = true)
    @ExcelIgnore
    private List<DicStockLocationDTO> locationDtoList;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    @ExcelIgnore
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    @ExcelIgnore
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    @ExcelIgnore
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    @ExcelIgnore
    private String modifyUserName;

    @ApiModelProperty(value = "公司code")
    @ExcelProperty(value = "公司", index =0)
    private String corpCode;

    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    @ExcelIgnore
    private String corpName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    @ExcelProperty(value = "工厂编码", index =1)
    private String ftyCode;

    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司id" , example = "1", required = false)
    @ExcelIgnore
    private Long corpId;

    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    @ExcelProperty(value = "工厂描述", index =2)
    private String ftyName;

    @ApiModelProperty(value = "地址" , example = "新地中心")
    @ExcelProperty(value = "地址", index =3)
    private String address;

    @ApiModelProperty(value = "是否为零价值工厂【1是，0否】" , example = "0")
    @ExcelProperty(value = "是否为零价值工厂", index =4)
    private Integer isWorthless;

    @ApiModelProperty(value = "是否为闲置物资工厂【1是，0否】" , example = "0")
    private Integer isLeisure;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    @ExcelIgnore
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    @ExcelIgnore
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    @ExcelIgnore
    private Long modifyUserId;

}
