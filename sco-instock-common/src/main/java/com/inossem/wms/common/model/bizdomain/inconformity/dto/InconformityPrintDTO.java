package com.inossem.wms.common.model.bizdomain.inconformity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/16 17:12
 * @desc InconformityPrintDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "标签传输对象", description = "标签传输对象")
public class InconformityPrintDTO implements Serializable {

    private static final long serialVersionUID = -2667163102263644458L;

    @ApiModelProperty(value = "printerIp打印机IP", example = "127.0.0.1")
    private String printerIp;

    @ApiModelProperty(value = "printerPort打印机端口", example = "6100")
    private Integer printerPort;

    @ApiModelProperty(value = "printerIp打印机IP", example = "127.0.0.1")
    private String printerIp1;

    @ApiModelProperty(value = "printerPort打印机端口", example = "6100")
    private Integer printerPort1;

    @ApiModelProperty(value = "printerIp打印机IP", example = "127.0.0.1")
    private String printerIp2;

    @ApiModelProperty(value = "printerPort打印机端口", example = "6100")
    private Integer printerPort2;

    @ApiModelProperty(value = "printerIp打印机IP", example = "127.0.0.1")
    private String printerIp3;

    @ApiModelProperty(value = "printerPort打印机端口", example = "6100")
    private Integer printerPort3;

    @ApiModelProperty(value = "printer打印机份数", example = "127")
    private Integer printNum;

    @ApiModelProperty(value = "printerIsPortable是否是便携式普通打印机 0：否  1：是", example = "0")
    private Integer printerIsPortable;

    @ApiModelProperty(value = "1：RFID抗金属  2：RFID非抗金属 3：普通标签", example = "3")
    private Integer labelType;

    @ApiModelProperty(value = "抬头对象")
    private BizReceiptInconformityHeadDTO bizReceiptInconformityHeadDTO;
}
