package com.inossem.wms.common.model.bizdomain.transport.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 调拨申请单明细表
 *
 * <AUTHOR> <<EMAIL>>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptTransportApplyItem对象", description="调拨申请单明细表")
public class BizReceiptTransportApplyItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    // @SonAttr(sonTbName = "biz_receipt_transport_bin", sonTbFkAttrName = "itemId")
    // @ApiModelProperty(value = "行项目列表")
    // List<BizReceiptTransportBinDTO> binDTOList;

    @SonAttr(sonTbName = "biz_receipt_assemble", sonTbFkAttrName = "receiptItemId")
    @ApiModelProperty(value = "特征列表")
    List<BizReceiptAssembleDTO> assembleDTOList;

    @ApiModelProperty(value = "小数位", name = "decimalPlace", example = "3", required = true)
    private Integer decimalPlace;

    @ApiModelProperty(value = "发出单位编码" , example = "M3")
    private String outputUnitCode;

    @ApiModelProperty(value = "发出单位名称" , example = "立方米")
    private String outputUnitName;

    @ApiModelProperty(value = "发出物料编码" , example = "M001005")
    private String outputMatCode;

    @ApiModelProperty(value = "发出物料名称" , example = "物料描述001003")
    private String outputMatName;

    @ApiModelProperty(value = "发出库存地点编码" , example = "2800")
    private String outputLocationCode;

    @ApiModelProperty(value = "发出库存地点名称" , example = "英诺森004")
    private String outputLocationName;

    @ApiModelProperty(value = "发出工厂编码" , example = "8000")
    private String outputFtyCode;

    @ApiModelProperty(value = "发出工厂名称" , example = "英诺森沈阳工厂")
    private String outputFtyName;

    @ApiModelProperty(value = "发出仓库编码" , example = "S800")
    private String outputWhCode;

    @ApiModelProperty(value = "发出仓库名称" , example = "英诺森帝国仓库")
    private String outputWhName;

    @ApiModelProperty(value = "接收单位编码" , example = "M3")
    private String inputUnitCode;

    @ApiModelProperty(value = "接收单位名称" , example = "立方米")
    private String inputUnitName;

    @ApiModelProperty(value = "接收物料编码" , example = "M001005")
    private String inputMatCode;

    @ApiModelProperty(value = "接收物料名称" , example = "物料描述001003")
    private String inputMatName;

    @ApiModelProperty(value = "接收库存地点编码" , example = "2800")
    private String inputLocationCode;

    @ApiModelProperty(value = "接收库存地点名称" , example = "英诺森004")
    private String inputLocationName;

    @ApiModelProperty(value = "接收工厂编码" , example = "8000")
    private String inputFtyCode;

    @ApiModelProperty(value = "接收工厂名称" , example = "英诺森沈阳工厂")
    private String inputFtyName;

    @ApiModelProperty(value = "接收仓库编码" , example = "S800")
    private String inputWhCode;

    @ApiModelProperty(value = "接收仓库名称" , example = "英诺森帝国仓库")
    private String inputWhName;

    @ApiModelProperty(value = "单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "单据code" , example = "草稿")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "非限制库存数量" , example = "100")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "填充属性 - 推荐仓位信息")
    private DicWhStorageBinDTO storageBin;

    @ApiModelProperty(value = "扩展属性 - 推荐仓位id" , example = "152218489651201")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "*", targetAttrName = "storageBin")
    private Long recommendBinId;
    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_transport_apply_head", sourceAttrName = "receiptCode,receiptType",
            targetAttrName = "receiptCode,receiptType")
    @ApiModelProperty(value = "调拨申请单id")
    private Long headId;

    @ApiModelProperty(value = "调拨申请单行项目序号")
    private String rid;

    @ApiModelProperty(value = "前置单据item表id")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "发出物料id" , example = "1")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "outputMatCode,outputMatName")
    private Long outputMatId;

    @ApiModelProperty(value = "发出物料单位id" , example = "7")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "outputUnitCode,outputUnitName,decimalPlace")
    private Long outputUnitId;

    @ApiModelProperty(value = "发出库存地点id" , example = "145729754562561")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "outputLocationCode,outputLocationName")
    private Long outputLocationId;

    @ApiModelProperty(value = "发出工厂id" , example = "145343907954689")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "outputFtyCode,outputFtyName")
    private Long outputFtyId;

    @ApiModelProperty(value = "发出仓库号id" , example = "1")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "outputWhCode,outputWhName")
    private Long outputWhId;

    @ApiModelProperty(value = "特殊库存代码")
    private String outputSpecStockCode;

    @ApiModelProperty(value = "特殊库存描述")
    private String outputSpecStockName;

    @ApiModelProperty(value = "调拨申请数量")
    private BigDecimal applyQty;

    @ApiModelProperty(value = "已下架数量")
    private BigDecimal unloadQty;

    @ApiModelProperty(value = "已上架数量")
    private BigDecimal loadQty;

    @ApiModelProperty(value = "已完成出库数量")
    private BigDecimal finishQty;

    @ApiModelProperty(value = "接收物料id" , example = "1")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "inputMatCode,inputMatName")
    private Long inputMatId;

    @ApiModelProperty(value = "接收物料单位id" , example = "145914045988865")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "inputUnitCode,inputUnitName")
    private Long inputUnitId;

    @ApiModelProperty(value = "接收库存地点id" , example = "145725436526593")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "inputLocationCode,inputLocationName")
    private Long inputLocationId;

    @ApiModelProperty(value = "接收工厂id" , example = "145343907954689")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "inputFtyCode,inputFtyName")
    private Long inputFtyId;

    @ApiModelProperty(value = "接收仓库号id" , example = "1")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "inputWhCode,inputWhName")
    private Long inputWhId;

    @ApiModelProperty(value = "特殊库存代码")
    private String inputSpecStockCode;

    @ApiModelProperty(value = "特殊库存描述")
    private String inputSpecStockName;

    @ApiModelProperty(value = "供应商代码")
    private String inputSupplierCode;

    @ApiModelProperty(value = "供应商描述")
    private String inputSupplierName;

    @ApiModelProperty(value = "凭证时间")
    private Date docDate;

    @ApiModelProperty(value = "过账时间")
    private Date postingDate;

    @ApiModelProperty(value = "行状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "物料凭证")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行号")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年份")
    private String matDocYear;

    @ApiModelProperty(value = "是否冲销【1是，0否】")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销凭证")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销行号")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销凭证时间")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过账时间")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销年份")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "行备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
