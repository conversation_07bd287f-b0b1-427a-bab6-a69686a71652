package com.inossem.wms.common.model.metadata.po;

import java.util.List;

import com.inossem.wms.common.model.metadata.dto.MetaDataDTO;
import com.inossem.wms.common.model.metadata.vo.MetadataFieldVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MetaDataAddPO {

    @ApiModelProperty(value = "Dto字段集合")
    List<MetadataFieldVO> metadataFieldVOList;
    @ApiModelProperty(value = "元数据")
    private MetaDataDTO metaDataDto;

}
