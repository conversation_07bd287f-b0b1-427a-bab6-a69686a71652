package com.inossem.wms.common.model.print.printer.dto;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 打印机主数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "打印机主数据传输对象", description = "打印机主数据传输对象")
public class DicPrinterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "打印机code" , example = "printer")
    private String printCode;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private String whId;

    @ApiModelProperty(value = "仓库code" , example = "S800")
    private String whCode;

    @ApiModelProperty(value = "仓库名称" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "打印机名称" , example = "测试打印机")
    private String printName;

    @ApiModelProperty(value = "打印机IP" , example = "127.0.0.1")
    private String printerIp;

    @ApiModelProperty(value = "打印机端口" , example = "6100")
    private String printerPort;

    @ApiModelProperty(value = "1：RFID抗金属  2：RFID非抗金属 3：普通标签" , example = "3")
    private Integer labelType;

    @ApiModelProperty(value = "1:默认  0：非默认" , example = "1")
    private Integer isDefault;

    @ApiModelProperty(value = "是否是便携式普通打印机 0：否  1：是" , example = "1")
    private Integer isPortable;

    @ApiModelProperty(value = "工厂ID" , example = "1")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    private String ftyCode;

    private String ftyName;

}
