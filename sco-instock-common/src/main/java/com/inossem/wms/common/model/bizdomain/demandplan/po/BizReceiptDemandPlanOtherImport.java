package com.inossem.wms.common.model.bizdomain.demandplan.po;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 需求计划其他类型导入PO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptDemandPlanOtherImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "*品名")
    private String productName;

    @ExcelProperty(value = "*物料组")
    private String matGroupCode;

    @ExcelProperty(value = "*需求数量")
    private BigDecimal qty;

    @ExcelProperty(value = "*计量单位")
    private String unitCode;

    @ExcelProperty(value = "*工厂")
    private String ftyCode;

    @ExcelProperty(value = "*成本中心\n" +
            "（科目分配类别为“K-成本中心时，必填）")
    private String costCenter;

    @ExcelProperty(value = "*WBS（项目编码）\n" +
            "（科目分配类别为“Q-项目时，必填）")
    private String wbsCode;

    @ExcelProperty(value = "备注")
    private String remark;
} 
