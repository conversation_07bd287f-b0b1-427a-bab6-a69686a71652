package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractSubItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentLackMat;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentSettlementHead;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentRegisterHeadDTO", description = "付款登记")
public class BizReceiptPaymentRegisterHeadDTO implements Serializable {
    private static final long serialVersionUID = -6092750215643556264L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "付款结算单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @RlatAttr(rlatTableName = "biz_receipt_payment_settlement_head",
            sourceAttrName = "receiptCode,paymentMonth,contractId,settlementType,*",
            targetAttrName = "settlementCode,paymentMonth,contractId,settlementType,paymentSettlementHead")
    @ApiModelProperty(value = "结算单id", example = "PD01000216")
    private Long settlementId;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "登记描述", example = "PD01000216")
    private String registerDesc;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_payment_register_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 -行项目")
    private List<BizReceiptPaymentRegisterItemDTO> itemList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;


    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -提交人编码", example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称", example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 结算单单据code")
    private String settlementCode;

    @ApiModelProperty(value = "扩展属性 - 付款月份")
    private String paymentMonth;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,purchaseType,supplierId,currency,firstParty",
            targetAttrName = "contractCode,purchaseType,supplierId,contractCurrency,firstParty")
    @ApiModelProperty(value = "扩展属性 - 合同id")
    private Long contractId;

    @ApiModelProperty(value = "扩展属性 - 合同号")
    private String contractCode;

    @ApiModelProperty(value = "扩展属性 - 合同币种")
    private Integer contractCurrency;

    @ApiModelProperty(value = "扩展属性 - 合同币种")
    private String contractCurrencyI18n;

    @ApiModelProperty(value = "已支付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "扩展属性 - 合同类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "扩展属性 - 合同类型国际化")
    private String purchaseTypeI18n;


    @RlatAttr(rlatTableName = "dic_supplier",
            sourceAttrName = "supplierName",
            targetAttrName = "supplierName")
    @ApiModelProperty(value = "扩展属性 - 供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "扩展属性 - 供应商名称")
    private String supplierName;


    @ApiModelProperty(value = "扩展属性 - 结算单对象")
    private BizReceiptPaymentSettlementHead paymentSettlementHead;


    @ApiModelProperty(value = "填充属性 -结算发票")
    private List<BizReceiptPaymentInvoiceDTO> invoiceList;

    @ApiModelProperty(value = "分项信息")
    private List<BizReceiptContractSubItemDTO> subItemList;

    @ApiModelProperty(value = "支付进度信息")
    private List<BizReceiptPaymentProgressDTO> progressList;

    @ApiModelProperty(value = "缺件信息")
    private List<BizReceiptPaymentLackMat> lackMatVOList;


    @ApiModelProperty(value = "已支付合同币种金额", example = "2500")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "未支付合同币种金额", example = "2500")
    private BigDecimal notPaidAmount;


    @ApiModelProperty(value = "本次支付卢比金额", example = "1")
    private BigDecimal qty;

    @ApiModelProperty(value = "本次支付日期", example = "1")
    private Date payDateTime;


    @ApiModelProperty(value = "结算类型", example = "1")
    private Integer settlementType;

    @ApiModelProperty(value = "甲方", example = "1")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方", example = "1")
    private String firstPartyI18n;
}
