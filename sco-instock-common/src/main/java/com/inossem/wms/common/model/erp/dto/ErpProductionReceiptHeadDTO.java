package com.inossem.wms.common.model.erp.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.SonAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 生产订单抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="生产订单抬头传输对象", description="生产订单抬头传输对象")
public class ErpProductionReceiptHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 生产订单行项目" , example = "1")
    @SonAttr(sonTbName = "erp_production_receipt_item", sonTbFkAttrName = "headId")
    private List<ErpProductionReceiptItemDTO> itemList;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "SAP生产订单号" , example = "1")
    private String receiptCode;

    @ApiModelProperty(value = "SAP生产订单描述" , example = "生产订单001")
    private String receiptDescription;

    @ApiModelProperty(value = "SAP单据备注" , example = "单据备注")
    private String remark;

    @ApiModelProperty(value = "SAP生成订单类型" , example = "1")
    private String erpReceiptType;

    @ApiModelProperty(value = "SAP生产订单类型描述" , example = "1")
    private String erpReceiptTypeName;

    @ApiModelProperty(value = "SAP生产订单创建人code" , example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "SAP生产订单创建人name" , example = "管理员")
    private String erpCreateUserName;

    @ApiModelProperty(value = "SAP生成订单创建时间" , example = "2021-05-12")
    private Date erpCreateTime;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;


}
