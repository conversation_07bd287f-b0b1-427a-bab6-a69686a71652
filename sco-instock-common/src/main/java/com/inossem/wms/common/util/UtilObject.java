package com.inossem.wms.common.util;

import com.inossem.wms.common.constant.Const;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class UtilObject {

    /**
     * Object 转String,空则返回""
     * 
     * @param obj 原Object
     * @return String
     */
    public static String getStringOrEmpty(Object obj) {
        if (obj == null) {
            return Const.STRING_EMPTY;
        } else if (obj instanceof String) {
            String s = (String)obj;
            if (UtilString.isNullOrEmpty(s)) {
                return Const.STRING_EMPTY;
            } else {
                return s;
            }
        } else if (obj instanceof BigDecimal) {
            BigDecimal bigDecimal = (BigDecimal)obj;
            return bigDecimal.stripTrailingZeros().toPlainString();
        } else if (obj instanceof Date) {
            Date dateTime = (Date)obj;
            return UtilLocalDateTime.getStringDateTimeForDate(dateTime);
        } else if (obj instanceof LocalDateTime) {
            LocalDateTime dateTime = (LocalDateTime)obj;
            return UtilLocalDateTime.getStringDateTimeForLocalDateTime(dateTime);
        } else if (obj instanceof LocalDate) {
            LocalDate date = (LocalDate)obj;
            return UtilLocalDateTime.getStringDateForLocalDate(date);
        } else if (obj instanceof LocalTime) {
            LocalTime time = (LocalTime)obj;
            return UtilLocalDateTime.getStringTimeForLocalTime(time);
        } else if (obj instanceof Integer) {
            Integer integer = (Integer)obj;
            return integer.toString();
        } else if (obj instanceof Long) {
            Long l = (Long)obj;
            return l.toString();
        } else if (obj instanceof Double) {
            Double d = (Double)obj;
            return d.toString();
        } else if (obj instanceof Byte) {
            Byte b = (Byte)obj;
            return b.toString();
        } else {
            return obj.toString();
        }
    }

    /**
     * Object转String,Object不存在则返回null
     * 
     * @param obj 原Object
     * @return String
     */
    public static String getStringOrNull(Object obj) {
        if (obj == null) {
            return null;
        } else if (obj instanceof String) {
            String s = (String)obj;
            if (UtilString.isNullOrEmpty(s)) {
                return null;
            } else {
                return s;
            }
        } else {
            return getStringOrEmpty(obj);
        }
    }

    /**
     * Object转Long,Object不存在则返回null
     * 
     * @param obj 原Object
     * @return Long
     */
    public static Long getLongOrNull(Object obj) {
        if (obj == null) {
            return null;
        } else {
            try {
                return Long.parseLong(obj.toString().trim());
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 
     * Object转Integer,Object不存在则返回null
     * 
     * @param obj 原Object
     * @return Integer
     */
    public static Integer getIntegerOrNull(Object obj) {
        try {
            if (obj == null) {
                return null;
            } else {
                String str = obj.toString().trim();
                if (str.length() == 0 || "null".equalsIgnoreCase(str)) {
                    return null;
                } else {
                    Double valueDouble = Double.parseDouble(obj.toString().trim());
                    return valueDouble.intValue();
                }
            }
        } catch (

        Exception ex) {
            return null;
        }
    }

    /**
     * 
     * Object转int,Object不存在则返回null
     * 
     * @param obj 原Object
     * @return int
     */
    public static int getIntOrZero(Object obj) {
        if (null == obj) {
            return 0;
        } else {
            try {
                String str = obj.toString().trim();
                if (str.length() == 0 || "null".equalsIgnoreCase(str)) {
                    return 0;
                } else {
                    return Integer.parseInt(str);
                }
            } catch (Exception e) {
                return 0;
            }
        }
    }

    /**
     * 
     * Object转BigDecimal,Object不存在则返回0
     * 
     * @param obj 原Object
     * @return BigDecimal
     */
    public static BigDecimal getBigDecimalOrZero(Object obj) {
        BigDecimal ret = BigDecimal.ZERO;
        if (obj != null) {
            if (obj instanceof BigDecimal) {
                ret = (BigDecimal)obj;
            } else if (obj instanceof String) {
                String str = obj.toString().trim();
                if (str.length() > 0) {
                    ret = new BigDecimal(str);
                }
            } else if (obj instanceof BigInteger) {
                ret = new BigDecimal((BigInteger)obj);
            } else if (obj instanceof Number) {
                // ret = new BigDecimal(((Number) obj).doubleValue());
                ret = new BigDecimal(obj.toString());
            } else {
                throw new ClassCastException("Not possible to coerce [" + obj + "] from class " + obj.getClass() + " into a BigDecimal.");
            }
        }
        ret = UtilBigDecimal.getBigDecimalStripTrailingZeros(ret);
        ret = new BigDecimal(ret.toPlainString());
        return ret;
    }

    /**
     * 
     * Object转Byte,Object不存在则返回null
     * 
     * @param obj 原Object
     * @return Byte
     */
    public static Byte getByteOrNull(Object obj) {
        if (obj == null) {
            return null;
        } else {
            try {
                return Byte.parseByte(obj.toString().trim());
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 
     * Object转BigDecimal,Object不存在则返回0
     * 
     * @param value 原Object
     * @return BigDecimal
     */
    public static BigDecimal getBigDecimal(Object value) {
        BigDecimal ret = null;
        if (value != null && !"".equals(value)) {
            if (value instanceof BigDecimal) {
                ret = (BigDecimal)value;
            } else if (value instanceof String) {
                ret = new BigDecimal((String)value);
            } else if (value instanceof BigInteger) {
                ret = new BigDecimal((BigInteger)value);
            } else if (value instanceof Number) {
                ret = new BigDecimal(((Number)value).doubleValue());
            } else {
                throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
            }
        } else {
            ret = BigDecimal.ZERO;
        }
        return ret;
    }

    /**
     * 过滤任意(script,html,style)标签符,返回纯文本
     * 
     * @param inputString 原Object
     * @return String
     */
    public static String htmlToText(Object inputString) {
        if (null == inputString) {
            return null;
        }
        // 含html标签的字符串
        String htmlStr = inputString.toString();
        String textStr = "";
        Pattern pScript;
        java.util.regex.Matcher mScript;
        Pattern pStyle;
        java.util.regex.Matcher mStyle;
        Pattern pHtml;
        java.util.regex.Matcher mHtml;
        try {
            // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
            String regExScript = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>";
            // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style>
            String regExStyle = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>";
            // 定义HTML标签的正则表达式
            String regExHtml = "<[^>]+>";
            pScript = Pattern.compile(regExScript, Pattern.CASE_INSENSITIVE);
            mScript = pScript.matcher(htmlStr);
            // 过滤script标签
            htmlStr = mScript.replaceAll("");
            pStyle = Pattern.compile(regExStyle, Pattern.CASE_INSENSITIVE);
            mStyle = pStyle.matcher(htmlStr);
            // 过滤style标签
            htmlStr = mStyle.replaceAll("");
            pHtml = Pattern.compile(regExHtml, Pattern.CASE_INSENSITIVE);
            mHtml = pHtml.matcher(htmlStr);
            // 过滤html标签
            htmlStr = mHtml.replaceAll("");
            textStr = htmlStr;
        } catch (Exception e) {
            System.err.println("Html2Text: " + e.getMessage());
        }
        // 返回文本字符串
        return textStr;

    }

    /**
     * 判断对象是否为null
     *
     * @param obj
     * @return
     */
    public static boolean isNull(Object obj) {
        return null == obj || obj.equals(null);
    }

    /**
     * 判断对象是否不为null
     *
     * @param obj
     * @return
     */
    public static boolean isNotNull(Object obj) {
        return null != obj && !obj.equals(null);
    }


    public static boolean isEmpty(Object aObj) {
        if (aObj instanceof String) {
            return isEmpty((String)aObj);
        } else if (aObj instanceof Long) {
            return isEmpty((Long)aObj);
        } else if (aObj instanceof Date) {
            return isEmpty((Date)aObj);
        } else if (aObj instanceof Collection) {
            return isEmpty((Collection)aObj);
        } else if (aObj instanceof Map) {
            return isEmpty((Map)aObj);
        } else if (aObj != null && aObj.getClass().isArray()) {
            return isEmptyArray(aObj);
        } else {
            return isNull(aObj);
        }
    }

    public static boolean isNotEmpty(Object aObj) {
        if (aObj instanceof String) {
            return !isEmpty((String)aObj);
        } else if (aObj instanceof Long) {
            return !isEmpty((Long)aObj);
        } else if (aObj instanceof Date) {
            return !isEmpty((Date)aObj);
        } else if (aObj instanceof Collection) {
            return !isEmpty((Collection)aObj);
        } else if (aObj instanceof Map) {
            return !isEmpty((Map)aObj);
        } else if (aObj != null && aObj.getClass().isArray()) {
            return !isEmptyArray(aObj);
        } else {
            return !isNull(aObj);
        }
    }

    private static boolean isEmptyArray(Object array) {
        int length = 0;
        if (array instanceof int[]) {
            length = ((int[])array).length;
        } else if (array instanceof byte[]) {
            length = ((byte[])array).length;
        } else if (array instanceof short[]) {
            length = ((short[])array).length;
        } else if (array instanceof char[]) {
            length = ((char[])array).length;
        } else if (array instanceof float[]) {
            length = ((float[])array).length;
        } else if (array instanceof double[]) {
            length = ((double[])array).length;
        } else if (array instanceof long[]) {
            length = ((long[])array).length;
        } else if (array instanceof boolean[]) {
            length = ((boolean[])array).length;
        } else {
            length = ((Object[])array).length;
        }
        return length == 0;
    }

    public static boolean isEmpty(Date aDate) {
        return aDate == null;
    }

    public static boolean isEmpty(Long aLong) {
        return aLong == null || aLong == 0;
    }

    public static boolean isEmpty(Map m) {
        return m == null || m.size() == 0;
    }

    public static boolean isEmpty(Collection c) {
        return c == null || c.size() == 0;
    }

    public static boolean isEmpty(String aStr) {
        return aStr == null || "null".equalsIgnoreCase(aStr.trim()) || Const.STRING_EMPTY.equals(aStr.trim());
    }

    public static String trim(String aStr) {
        if (aStr == null) {
            return "";
        } else {
            return aStr.trim();
        }
    }

    public static boolean equals(String str1, String str2) {
        return Objects.equals(str1, str2);
    }

    public static boolean equals(Long L1, Long L2) {
        return Objects.equals(L1, L2);
    }

    public static boolean equals(Object obj1, Object obj2) {
        boolean result;
        if (obj1 != null) {
            result = obj1.equals(obj2);
        } else {
            result = (obj2 == null);
        }
        return result;
    }

    public static boolean equalsIgnoreCase(String str1, String str2) {
        return str1 != null ? str1.equalsIgnoreCase(str2) : str2 == null;
    }

}
