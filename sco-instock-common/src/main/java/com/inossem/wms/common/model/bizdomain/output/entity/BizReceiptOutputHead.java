package com.inossem.wms.common.model.bizdomain.output.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 出库单抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptOutputHead对象", description = "出库单抬头表")
@TableName("biz_receipt_output_head")
public class BizReceiptOutputHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "出库单编码" , example = "CK01000400")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 销售出库411 预留出库412 采购退货413 领料出库414 报废出库415 其他出库416" , example = "411")
    private Integer receiptType;

    @ApiModelProperty(value = "10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成、70已退库" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "维修厂商")
    private String repairFty;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "签名" , example = "1", required = false)
    private String autograph;

    @ApiModelProperty(value = "接收人id" , example = "1", required = false)
    private Long receiverId;

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "实际领料人描述")
    private String actualReceiverName;

    @ApiModelProperty(value = "保管员用户id")
    private Long storeKeeperUserId;

    @ApiModelProperty(value = "发料人用户id")
    private Long sendMatUserId;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "工单号" )
    private String workOrder;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "是否超发【1是，0否】")
    private Integer isOver;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "退旧物资处置单号")
    private String returnOldMaintainReceiptCode;

    @ApiModelProperty(value = "成本中心id" , example = "1", required = false)
    private Long costCenterId;

    @ApiModelProperty(value = "成本中心编码" , example = "1", required = false)
    private String costCenterCode;

    @ApiModelProperty(value = "wbsID")
    private Long wbsId;

    @ApiModelProperty(value = "wbs编码")
    private String wbsCode;

    @ApiModelProperty(value = "出库日期")
    private Date outTime;

    @ApiModelProperty(value = "资产id")
    private Long assetId;

}
