package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 查询用户列表入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "查询用户列表入参", description = "查询用户列表入参")
public class BizReceiptStocktakingSearchUserListPO extends PageCommon {

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户编码")
    private List<String> userCodeList;

}
