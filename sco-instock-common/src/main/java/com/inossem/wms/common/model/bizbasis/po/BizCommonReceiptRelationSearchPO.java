package com.inossem.wms.common.model.bizbasis.po;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 查询单据流对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "查询单据流对象", description = "查询单据流对象")
public class BizCommonReceiptRelationSearchPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = true)
    private Long receiptHeadId;

    @ApiModelProperty(value = "行项目ID集合")
    private List<Long> receiptItemIdList;
}
