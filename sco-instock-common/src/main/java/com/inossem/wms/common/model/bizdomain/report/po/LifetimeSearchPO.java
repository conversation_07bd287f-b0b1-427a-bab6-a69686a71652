package com.inossem.wms.common.model.bizdomain.report.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 寿期台账 查询入参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "寿期台账报表 查询入参传输对象", description = "寿期台账报表 查询入参")
public class LifetimeSearchPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = -1;

    @ApiModelProperty(value = "到期日期开始时间")
    private Date startTime;

    @ApiModelProperty(value = "到期日期结束时间")
    private Date endTime;

    @ApiModelProperty(value = "延期日期开始时间")
    private Date docDateStartTime;

    @ApiModelProperty(value = "延期日期结束时间")
    private Date docDateEndTime;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料编码" , example = "4500000001")
    private String matCode;

    @ApiModelProperty(value = "批次号" , example = "2008310001")
    private String batchCode;

    @ApiModelProperty(value = "寿期检定单号")
    private String receiptCode;

    @ApiModelProperty(value = "寿期检定单描述")
    private String remark;

    @ApiModelProperty(value = "寿期检定结果单号")
    private String receiptCodeResult;

    @ApiModelProperty(value = "检定人")
    private String inspectUserName;

    @ApiModelProperty(value = "需求人")
    private String applyUserName;

    @ApiModelProperty(value = "需求部门")
    private String applyUserDeptCode;

    @ApiModelProperty(value = "到期日期")
    private Date expireDate;

    @ApiModelProperty(value = "检定结果")
    private String inspectResult;

    @ApiModelProperty(value = "延期日期")
    private Date delayDate;
    private List<Long> locationIdList;
    private List<Long> ftyIdList;
    private List<String> batchCodeList;
    private List<String> matCodeList;
    private Integer receiptType;
}
