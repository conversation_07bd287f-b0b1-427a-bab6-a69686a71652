package com.inossem.wms.common.model.metadata.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2021/03/02 19:58
 */
@Data
public class MetadataSonFieldDTO {

    /* **************** Dto属性 开始 ****************************/

    @ApiModelProperty(value = "字段是否可编辑（0：否 1：是）" , example = "1")
    private Integer canEdit;

    /* **************** Dto属性 结束 ****************************/
    @ApiModelProperty(value = "id）" , example = "1")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    @ApiModelProperty(value = "元数据ID" , example = "1")
    private Long metadataId;
    @ApiModelProperty(value = "属性名称" , example = "String")
    private String attrName;
    @ApiModelProperty(value = "属性类型名称" , example = "String")
    private String attrType;
    @ApiModelProperty(value = "子表名" , example = "String")
    private String sonTbName;
    @ApiModelProperty(value = "子表外键属性名" , example = "String")
    private String sonTbFkAttrName;
    @ApiModelProperty(value = "包名" , example = "String")
    private String attrPackage;
    @ApiModelProperty(value = "字段备注" , example = "String")
    private String colRemark;
    @ApiModelProperty(value = "字段名称" , example = "String")
    private String colName;
    @ApiModelProperty(value = "字段长度" , example = "225")
    private Integer colLength;
    @ApiModelProperty(value = "小数点" , example = "3")
    private String colDecimalPlace;
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private String createUserId;

}
