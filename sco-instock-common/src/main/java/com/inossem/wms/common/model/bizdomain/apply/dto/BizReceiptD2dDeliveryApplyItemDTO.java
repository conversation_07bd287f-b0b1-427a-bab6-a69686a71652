package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptD2dDeliveryApplyItemDTO", description = "门到门送货申请行项目")
public class BizReceiptD2dDeliveryApplyItemDTO implements Serializable {
    private static final long serialVersionUID = 3803142733527734491L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "receiptCode,contractName,createUserName", targetAttrName = "contractCode,contractName,purchaserName")
    @ApiModelProperty(value = "前续单据head主键", example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键", example = "111")
    @RlatAttr(rlatTableName = "biz_receipt_contract_item", sourceAttrName = "productName,rid", targetAttrName = "productName,contractRid")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型", example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据数量", example = "10")
    private BigDecimal preReceiptQty;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "matCode,matName,matNameEn")
    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "订单单位id", example = "7")
    private Long unitId;

    @ApiModelProperty(value = "数量", example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "未清数量（合同数量-已发货数量）", example = "5")
    private BigDecimal unclearQty;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求人")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值(单价*数量)")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "不含税货值(不含税单价*数量)")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "币种", example = "1")
    private Integer currency;

    @ApiModelProperty(value = "币种", example = "1")
    private String currencyI18n;

    @ApiModelProperty(value = "合同号", example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "合同行号", example = "20201101001")
    private String contractRid;

    @ApiModelProperty(value = "合同名称", example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "采购员编码")
    private String purchaserCode;

    @ApiModelProperty(value = "采购员名称")
    private String purchaserName;

    @ApiModelProperty(value = "物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "品名（英文）", example = "物料描述001003")
    private String matNameEn;

    @ApiModelProperty(value = "计量单位编码", example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "计量单位名称", example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "小数位", example = "2")
    private Integer decimalPlace;

    @ApiModelProperty(value = "单据行项目状态名称", example = "草稿")
    private String itemStatusI18n;


    @ApiModelProperty(value = "重量")
    private BigDecimal weight;



}
