package com.inossem.wms.common.model.masterdata.car.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 吊带管理分页列表查询出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="吊带管理分页列表查询出参", description="吊带管理分页列表查询出参")
public class DicSlingPageVO implements Serializable {

    private static final long serialVersionUID = -5756824198485682741L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "吊带编号")
    private String slingCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

}
