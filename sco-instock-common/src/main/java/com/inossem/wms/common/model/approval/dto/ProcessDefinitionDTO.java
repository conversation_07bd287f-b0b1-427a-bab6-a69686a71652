package com.inossem.wms.common.model.approval.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 流程定义传输对象
 * 
 * <AUTHOR>
 * @date 2020/8/12 11:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "流程定义传输对象", description = "流程定义传输对象")
public class ProcessDefinitionDTO {
    @ApiModelProperty(value = "id" , example = "1")
    private String id;

    @ApiModelProperty(value = "name" ,example = "名称")
    private String name;

    @ApiModelProperty(value = "key" ,example = "key")
    private String key;

    @ApiModelProperty(value = "版本" , example = "1")
    private Integer version;

    @ApiModelProperty(value = "类别", example = "category")
    private String category;

    @ApiModelProperty(value = "描述" , example = "描述")
    private String description;

    @ApiModelProperty(value = "部署id"  , example = "1")
    private String deploymentId;

    @ApiModelProperty(value = "部署时间", example = "2021-05-10")
    private Date deploymentTime;

    @ApiModelProperty(value = "图片资源名称", example = "图片资源名称")
    private String diagramResourceName;

    @ApiModelProperty(value = "资源名称", example = "资源名称")
    private String resourceName;

    @ApiModelProperty(value = "激活状态", example = "1")
    private String suspendState;

    @ApiModelProperty(value = "激活状态名称", example = "1")
    private String suspendStateName;
}
