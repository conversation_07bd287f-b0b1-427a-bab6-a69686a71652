package com.inossem.wms.common.model.erp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 成本中心
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ErpCostCenter", description = "")
@TableName("erp_cost_center")
public class ErpCostCenter implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "成本对象编码" , example = "ccc")
    private String costObjCode;

    @ApiModelProperty(value = "成本对象名称" , example = "成本中心1")
    private String costObjName;

    @ApiModelProperty(value = "成本中心编码" , example = "ccc")
    private String costCenterCode;

    @ApiModelProperty(value = "成本中心名称" , example = "成本中心1")
    private String costCenterName;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
