package com.inossem.wms.common.model.masterdata.base.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wang
 * @description 科室DTO
 * @date 2022/4/19 16:21
 */
@Data
@ApiModel(description = "科室")
public class DicDeptOfficeDTO implements Serializable {


    /* ========================================== 扩展字段开始 ==========================================*/


    @ApiModelProperty(value = "部门id", example = "1100")
    private String deptCode;


    @ApiModelProperty(value = "部门描述", example = "示例部门")
    private String deptName;

    /* ========================================== 扩展字段结束 ==========================================*/


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "科室编码")
    private String deptOfficeCode;

    @ApiModelProperty(value = "科室描述")
    private String deptOfficeName;

    @ApiModelProperty(value = "是否删除【1：是0：否】")
    @TableLogic
    private Integer isDelete;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人Code")
    private String createUserCode;

    @ApiModelProperty(value = "创建人Name")
    private String createUserName;

    @ApiModelProperty(value = "科室是否选中" , example = "1")
    private String officeIsChecked;

    @ApiModelProperty(value = "用户审批级别" , example = "1", required = false)
    private Integer jobLevel;
}
