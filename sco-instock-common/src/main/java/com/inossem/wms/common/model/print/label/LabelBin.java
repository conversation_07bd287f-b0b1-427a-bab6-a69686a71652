package com.inossem.wms.common.model.print.label;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓位标签打印实体类
 **/
@ApiModel(value = "仓位标签打印实体类")
@Data
public class LabelBin implements Serializable {

    private static final long serialVersionUID = 4579139521487272196L;

    @ApiModelProperty(value = "仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "存储类型编码" , example = "801")
    private String typeCode;

    @ApiModelProperty(value = "仓位编码" , example = "00")
    private String binCode;

    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "存储类型" , example = "T001")
    private String typeName;

    @ApiModelProperty(value = "仓位描述" , example = "00")
    private String binName;

    @ApiModelProperty(value = "二维码值" , example = "100012")
    private String qrCode;

    @ApiModelProperty(value = "rfid" , example = "100012")
    private String rfidCode;

    @ApiModelProperty(value = "是否是RFID标签  0普通标签 1RFID标签" , example = "1")
    private Integer labelIsRFID;

    @ApiModelProperty(value = "printerIp打印机IP" , example = "127.0.0.1")
    private String printerIp;

    @ApiModelProperty(value = "printerPort打印机端口" , example = "6100")
    private String printerPort;
}
