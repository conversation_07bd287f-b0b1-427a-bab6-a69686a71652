package com.inossem.wms.common.model.print.template.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DicPrintTemplateDTO {

    /* ********************** 扩展字段结束 *************************/

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private String receiptType;

    @ApiModelProperty(value = "模板名称" , example = "String")
    private String templateName;

    @ApiModelProperty(value = "模板Code" , example = "1")
    private String templateCode;

    @ApiModelProperty(value = "模板Code" , example = "String")
    private String templateJson;

    @ApiModelProperty(value = "逻辑删除" , example = "0")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人" , example = "1")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人" , example = "1")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

}
