projectDesc: 仓储管理原型工程
## 目标工程构建时生成到jenkins中的指定view名称
targetProJenkinsViewName: sco-instock3.0
## 原型工程的artifactId
artifactId: sco-instock
## 原型工程的artifactId简写
shortArtifactId: instock
## 原型工程的包名简写
shortPackage: wms
## 特定不会被拆分解析的文件列表
noSplitFileList:
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadRecommendService.java
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\unload\UnloadRecommendService.java
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InputCommonController.java
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonService.java
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\inspect\controller\InspectCommonController.java
  - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\inspect\service\biz\InspectCommonService.java
moduleList:
  -
    moduleName: 主数据模块
    moduleDesc: 处理主数据相关业务需求
    moduleKeyword: org
    subModuleList:
      -
        moduleName: 公司主数据
        moduleDesc: 处理公司主数据相关业务需求
        moduleKeyword: crop
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\controller\CorpController.java
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\service\biz\CorpService.java
      -
        moduleName: 工厂主数据
        moduleDesc: 处理工厂主数据相关业务需求
        moduleKeyword: factory
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\controller\FactoryController.java
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\service\biz\FactoryService.java
      -
        moduleName: 库存地点主数据
        moduleDesc: 处理库存地点主数据相关业务需求
        moduleKeyword: stockLocation
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\controller\StockLocationController.java
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\service\biz\StockLocationService.java
      -
        moduleName: 仓库主数据
        moduleDesc: 处理仓库主数据相关业务需求
        moduleKeyword: wh
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\controller\WhController.java
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\service\biz\WhService.java
      -
        moduleName: 存储类型主数据
        moduleDesc: 处理存储类型主数据相关业务需求
        moduleKeyword: whStorage
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\controller\WhStorageTypeController.java
          - sco-instock-java-v3.0\sco-instock-biz-basis\src\main\java\com\inossem\wms\bizbasis\masterdata\org\service\biz\WhStorageTypeService.java

  - moduleName: 采集模块
    moduleDesc: 处采集相关业务需求
    moduleKeyword: collection
    subModuleList:
      - moduleName: 采集任务
        moduleDesc: 处采集任务相关业务需求
        moduleKeyword: collectionTask
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\collection

  - moduleName: 送货模块
    moduleDesc: 处送货相关业务需求
    moduleKeyword: delivery
    subModuleList:
      - moduleName: 送货通知
        moduleDesc: 处送货通知相关业务需求
        moduleKeyword: deliveryNotice
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\delivery

  -
    moduleName: 验收模块
    moduleDesc: 处验收相关业务需求
    moduleKeyword: inspect
    subModuleList:
      - moduleName: 采购验收
        moduleDesc: 处采购验收相关业务需求
        moduleKeyword: purchaseInspect
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\inspect

  -
    moduleName: 入库模块
    moduleDesc: 处理入库相关业务需求
    moduleKeyword: input
    subModuleList:
      -
        moduleName: 验收入库
        moduleDesc: 处理验收入库相关业务需求
        moduleKeyword: inspectInput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InspectInputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InspectInputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\InspectInputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\InspectInputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\InspectInputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\InspectInputInspectMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\InspectInputInspectWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\ILoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\AbstractLoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadRecommendService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategyEmptyBin.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategySameMat.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
#        # 调用项声明文件列表，此列表下的文件中的public&protected方法会列在功能勾选调用项的【更多】列表中以供勾选
        callStatementFileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\InspectInputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
      -
        moduleName: 其他入库
        moduleDesc: 处理其他入库相关业务需求
        moduleKeyword: otherInput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\OtherInputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\OtherInputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\OtherInputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\OtherInputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\OtherInputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\prereceipt\InputPreReceiptComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\ILoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\AbstractLoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadRecommendService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategyEmptyBin.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategySameMat.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
      -
        moduleName: 生产入库
        moduleDesc: 处理生产入库相关业务需求
        moduleKeyword: productionInput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\ProductionInputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\ProductionInputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\ProductionInputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\ProductionInputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\ProductionInputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\prereceipt\InputPreReceiptComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\ILoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\AbstractLoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadRecommendService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategyEmptyBin.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategySameMat.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
      -
        moduleName: 采购入库
        moduleDesc: 处理采购入库相关业务需求
        moduleKeyword: purchaseInput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\PurchaseInputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\PurchaseInputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\PurchaseInputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\PurchaseInputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\PurchaseInputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\prereceipt\InputPreReceiptComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\ILoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\AbstractLoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadRecommendService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategyEmptyBin.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategySameMat.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
      -
        moduleName: 临时入库
        moduleDesc: 处理临时入库相关业务需求
        moduleKeyword: tempInput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\TempInputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\TempInputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\TempInputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\TempInputDeptoffsetMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\TempInputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\TempInputRevokeMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\TempInputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\ILoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\AbstractLoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadRecommendService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategyEmptyBin.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategySameMat.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
      -
        moduleName: 零价值入库
        moduleDesc: 处理零价值入库相关业务需求
        moduleKeyword: worthlessInput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\WorthlessInputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\WorthlessInputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\controller\InputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\biz\InputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\WorthlessInputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\WorthlessInputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\movetype\WorthlessInputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\component\inputbase\InputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\input\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\ILoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\AbstractLoadStrategy.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadRecommendService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategyEmptyBin.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load\LoadStrategySameMat.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
  -
    moduleName: 出库模块
    moduleDesc: 处理出库相关业务需求
    moduleKeyword: output
    subModuleList:
      -
        moduleName: 领料出库
        moduleDesc: 处理领料出库相关业务需求
        moduleKeyword: matReqOutput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\MatReqOutputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\MatReqOutputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\MatReqOutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsMatReqOutputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsMatReqOutputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\dao
      -
        moduleName: 其他出库
        moduleDesc: 处理其他出库相关业务需求
        moduleKeyword: otherOutput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OtherOutputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OtherOutputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OtherOutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsOtherOutputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsOtherOutputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\dao
      -
        moduleName: 采购退货
        moduleDesc: 处理采购退货相关业务需求
        moduleKeyword: purchaseReturn
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\PurchaseReturnController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\PurchaseReturnService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\PurchaseReturnComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsPurchaseReturnMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsPurchaseReturnWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\dao
      -
        moduleName: 销售出库
        moduleDesc: 处理销售出库相关业务需求
        moduleKeyword: saleOutput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\SaleOutputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\SaleOutputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\SaleOutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsSaleOutputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsSaleOutputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\dao
      -
        moduleName: 报废出库
        moduleDesc: 处理报废出库相关业务需求
        moduleKeyword: scrapOutput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\ScrapOutputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\ScrapOutputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\ScrapOutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsScrapOutputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsScrapOutputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\dao
      -
        moduleName: 零价值出库
        moduleDesc: 处理零价值出库相关业务需求
        moduleKeyword: worthlessOutput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\WorthlessOutputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\WorthlessOutputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\WorthlessOutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsWorthlessOutputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsWorthlessOutputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\dao
      -
        moduleName: 临时出库
        moduleDesc: 处理临时出库相关业务需求
        moduleKeyword: tempOutput
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\TempOutputController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\TempOutputService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\controller\OutputCommonController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\biz\OutputCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsTempOutputDeptOffsetMoceTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsTempOutputMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsTempOutputRevokeMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\movetype\InsTempOutputWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\OutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\TempOutputComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\dao

  -
    moduleName: 转储模块
    moduleDesc: 处理转储相关业务需求
    moduleKeyword: trans
    subModuleList:
      -
        moduleName: 实物转储
        moduleDesc: 处理实物转储相关业务需求
        moduleKeyword: transport
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\controller\TransportController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\biz\TransportService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportMessageQueueComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
      -
        moduleName: 账务转储
        moduleDesc: 处理账务转储(物料特性变更等)相关业务需求
        moduleKeyword: transfer
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\controller\TransferController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\biz\TransferService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransferComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\dao
  -
    moduleName: 调拨模块
    moduleDesc: 处理调拨相关业务需求
    moduleKeyword: allot
    subModuleList:
      -
        moduleName: 调拨申请
        moduleDesc: 处理调拨申请相关业务需求
        moduleKeyword: transportApply
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\controller\TransportApplyController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\biz\TransportApplyService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportApplyComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\dao
      -
        moduleName: 调拨出库
        moduleDesc: 处理调拨出库相关业务需求
        moduleKeyword: transportOut
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\controller\TransportOutController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\biz\TransportOutService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportOutComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportMessageQueueComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
      -
        moduleName: 调拨入库
        moduleDesc: 处理调拨入库相关业务需求
        moduleKeyword: transportIn
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\controller\TransportInController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\biz\TransportInService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportInComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportMessageQueueComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\component\TransportMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\transport\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
  -
    moduleName: 待完善模块
    moduleDesc: 不要勾选我，不然。。。你懂的。。。
    moduleKeyword: imperfectModule
    subModuleList:
      -
        moduleName: 待完善的模块
        moduleDesc: 不要勾选我，不然。。。你懂的。。。
        moduleKeyword: imperfectAll
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain

  -
    moduleName: 仓库管理模块
    moduleDesc: 处理上下架相关业务需求
    moduleKeyword: warehouse
    subModuleList:
      -
        moduleName: 上架管理
        moduleDesc: 处理上架相关业务需求
        moduleKeyword: load
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\controller\LoadTaskController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\biz\LoadTaskService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\LoadComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\load
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\wcs\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\wcs\service\datawrap\BizWcsInstructionHeadDataWrap.java
      - moduleName: 下架管理
        moduleDesc: 处理下架相关业务需求
        moduleKeyword: upload
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\controller\UnLoadTaskController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\biz\UnLoadTaskService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\UnLoadComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\component\TaskMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\service\strategy\unload
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\task\dao
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\wcs\service\datawrap\BizWcsInstructionHeadDataWrap.java
      - moduleName: 仓库整理管理
        moduleDesc: 处理仓库整理相关业务需求
        moduleKeyword: arrange
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\arrange\controller\BinArrangeController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\arrange\service\biz\BinArrangeService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\arrange\service\component\BinArrangeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\arrange\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\arrange\dao

  -
    moduleName: 退库模块
    moduleDesc: 处理退库相关业务需求
    moduleKeyword: returns
    subModuleList:
      -
        moduleName: 领料退库
        moduleDesc: 处理领料退库相关业务需求
        moduleKeyword: matReqReturn
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\controller\MatReqReturnController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\biz\MatReqReturnService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\biz\ReturnCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\MatReqReturnComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\ReturnComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\movetype\InsMatReqReturnMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\movetype\InsMatReqReturnWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\callback\ReturnTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputReturnCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\dao
      -
        moduleName: 销售退库
        moduleDesc: 处理销售退库相关业务需求
        moduleKeyword: saleReturn
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\controller\SaleReturnController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\biz\SaleReturnService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\biz\ReturnCommonApi.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\SaleReturnComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\ReturnComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\movetype\InsSaleReturnMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\movetype\InsSaleReturnWriteOffMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\component\callback\ReturnTaskCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\output\service\component\callback\OutputReturnCallbackComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\returns\dao
  -
    moduleName: 盘点模块
    moduleDesc: 处理盘点相关业务需求
    moduleKeyword: stocktaking
    subModuleList:
      -
        moduleName: 库存盘点
        moduleDesc: 处理库存盘点相关业务需求
        moduleKeyword: insStocktaking
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\controller\StocktakingController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\biz\StocktakingService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\component\StocktakingComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\component\movetype\StocktakingMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\dao
      -
        moduleName: 电子秤盘点
        moduleDesc: 处理电子秤盘点相关业务需求
        moduleKeyword: eleInsStocktaking
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\controller\EleStocktakingController.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\biz\EleStocktakingService.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\component\StocktakingComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\component\movetype\StocktakingMoveTypeComponent.java
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\service\datawrap
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\stocktaking\dao
  -
    moduleName: WCS模块
    moduleDesc: 处理WCS相关业务需求
    moduleKeyword: wcs
    isCanSplit: No
    subModuleList:
      -
        moduleName: WCS模块
        moduleDesc: 处理WCS相关业务需求
        moduleKeyword: subWcs
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\wcs
  -
    moduleName: 报表模块
    moduleDesc: 报表
    moduleKeyword: report
    subModuleList:
      -
        moduleName: 报表模块
        moduleDesc: 报表
        moduleKeyword: report
        isCanSplit: No
        fileList:
          - sco-instock-java-v3.0\sco-instock-biz-domain\src\main\java\com\inossem\wms\bizdomain\report