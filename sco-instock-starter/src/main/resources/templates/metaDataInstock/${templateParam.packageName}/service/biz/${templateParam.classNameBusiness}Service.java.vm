package ${templateParam.fullPackageName}.service.biz;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import ${templateParam.fullPackageName}.service.datawrap.${templateParam.classNameBusiness}Component;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.model.common.base.BizContext;

/**
 * ${templateParam.classNameBusinessDescription}
 *
 * <AUTHOR>
 * @date ${templateParam.createDate}
 */
@Service
public class ${templateParam.classNameBusiness}Service {
    @Autowired
    private ${templateParam.classNameBusiness}Component ${templateParam.beanBusinessName}Component;

    /**
     * 获取列表页
     *
     * @param ctx 上下文对象
     * @out vo 分页类型的集合
     */
    public void getPage(BizContext ctx) {
        ${templateParam.beanBusinessName}Component.getPage(ctx);
    }

    /**
     * 获取详情
     *
     * @param ctx 上下文对象
     * @out vo 结果
     */
    public void getInfo(BizContext ctx) {
        ${templateParam.beanBusinessName}Component.getInfo(ctx);
    }

    /**
     * 新增或修改方法
     *
     * @param ctx 上下文对象
     */
    public void addOrUpdate(BizContext ctx) {
        ${templateParam.beanBusinessName}Component.addOrUpdate(ctx);
    }

    /**
     * 删除方法
     *
     * @param ctx 上下文对象
     */
    public void remove(BizContext ctx) {
        ${templateParam.beanBusinessName}Component.remove(ctx);
    }

}
