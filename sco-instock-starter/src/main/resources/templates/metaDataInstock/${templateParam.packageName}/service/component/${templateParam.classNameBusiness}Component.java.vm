package ${templateParam.fullPackageName}.service.component;

import com.inossem.wms.common.util.UtilBean;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${templateParam.fullPackageNameCommon}.entity.${templateParam.className};
import ${templateParam.fullPackageNameCommon}.dto.${templateParam.className}DTO;
import ${templateParam.fullPackageNameCommon}.po.${templateParam.className}SavePO;
import ${templateParam.fullPackageNameCommon}.po.${templateParam.className}SearchPO;
import ${templateParam.fullPackageNameCommon}.vo.${templateParam.className}PageVO;
import ${templateParam.fullPackageName}.service.datawrap.${templateParam.className}DataWrap;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.bizbasis.common.service.biz.DataFillService;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.constant.Const;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.enums.EnumCheckType;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.enums.EnumReturnMsg;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.exception.${templateParam.projectArtifectCodeCamel}Exception;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.model.auth.user.vo.CurrentUser;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.model.common.DicDeleteCheckPO;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.model.common.base.BizContext;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.model.common.base.PageObjectVO;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.model.common.base.SingleResultVO;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.util.UtilBean;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.util.UtilCollection;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.util.UtilNumber;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.util.UtilString;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.system.config.service.biz.CheckDataService;
import ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.system.org.service.datawrap.${templateParam.className}DataWrap;


/**
 * ${templateParam.classNameBusinessDescription}
 *
 * <AUTHOR>
 * @date ${templateParam.createDate}
 */
@Service
@Slf4j
public class ${templateParam.classNameBusiness}Component {

    @Autowired
    private ${templateParam.className}DataWrap ${templateParam.beanName}DataWrap;
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 获取列表页
     *
     * @param ctx 上下文对象
     * @return 分页类型的集合
     */
    public void getPage(BizContext ctx) {
        ${templateParam.className}SearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询条件设置
        QueryWrapper<${templateParam.className}SearchPO> wrapper = new QueryWrapper<>();
        // 拼装参数
        IPage<${templateParam.className}PageVO> page = po.getPageObj(${templateParam.className}PageVO.class);
        ${templateParam.beanName}DataWrap.getPageVOList(page, wrapper);
        PageObjectVO<${templateParam.className}PageVO> vo = new PageObjectVO<>(page.getRecords(), page.getTotal());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 获取详情
     *
     * @param ctx 上下文对象
     * @return ${templateParam.classNameBusinessDescription}详情
     */
    public void getInfo(BizContext ctx) {
        Long ${templateParam.beanBusinessName}Id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmptyForLong(${templateParam.beanBusinessName}Id)) {
            throw new ${templateParam.projectArtifectCodeCamel}Exception(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        ${templateParam.className} ${templateParam.beanName} = ${templateParam.beanName}DataWrap.getById(${templateParam.beanBusinessName}Id);
        log.debug("${templateParam.classNameBusinessDescription}id：{}，详情：{}", ${templateParam.beanBusinessName}Id, JSONObject.toJSONString(${templateParam.beanName}));

        ${templateParam.className}DTO dto = UtilBean.newInstance(${templateParam.beanName}, ${templateParam.className}DTO.class);
        dataFillService.fillAttr(dto);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO<>(dto));
    }

    /**
     * 新增或修改方法
     *
     * @param ctx 上下文对象
     */
    public void addOrUpdate(BizContext ctx) {
        ${templateParam.className}SavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        log.debug("${templateParam.classNameBusinessDescription}新增/修改 po：{}", JSONObject.toJSONString(po));
        if (po == null) {
            throw new ${templateParam.projectArtifectCodeCamel}Exception(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        ${templateParam.className}DTO dto = po.get${templateParam.classNameBusiness}Info();
        ${templateParam.beanName}DataWrap.saveOrUpdateDto(dto, currentUser);
        log.info("${templateParam.classNameBusinessDescription}：{}，保存成功", dto.getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, dto.get${templateParam.classNameBusiness}Code());
    }

    /**
     * 删除方法
     *
     * @param ctx 上下文对象
     */
    public void remove(BizContext ctx) {
        Long ${templateParam.beanBusinessName}Id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.debug("${templateParam.classNameBusinessDescription}删除 ${templateParam.beanBusinessName}Id：{}", ${templateParam.beanBusinessName}Id);
        if (UtilNumber.isEmptyForLong(${templateParam.beanBusinessName}Id)) {
            throw new ${templateParam.projectArtifectCodeCamel}Exception(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        ${templateParam.className} entity = ${templateParam.beanName}DataWrap.getById(${templateParam.beanBusinessName}Id);
        // 逻辑删除
        ${templateParam.beanName}DataWrap.removeById(${templateParam.beanBusinessName}Id)
        log.info("${templateParam.classNameBusinessDescription}：{}，删除成功", ${templateParam.beanBusinessName}Id);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, entity.get${templateParam.classNameBusiness}Code());
    }

}
