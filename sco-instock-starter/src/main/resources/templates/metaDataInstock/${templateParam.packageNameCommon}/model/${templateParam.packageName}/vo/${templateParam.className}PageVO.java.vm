package ${templateParam.projectGroup}.${templateParam.projectArtifectCode}.common.model.${templateParam.packageName}.vo;
import lombok.Data;
import java.io.Serializable;

/**
 * ${templateParam.classNameBusinessDescription}分页结果集视图对象
 *
 * <AUTHOR>
 * @date ${templateParam.createDate}
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "${templateParam.classNameBusinessDescription}保存入参类", description = "${templateParam.classNameBusinessDescription}保存入参类")
public class ${templateParam.className}PageVO implements Serializable {

    /* ********************** 扩展字段开始 ************************ */

    /* ********************** 扩展字段结束 ************************ */
    #foreach( ${selfField} in ${templateParam.selfFieldList} )
    #if(${selfField.colRemark} && "${selfField.colRemark}" != "")
    /**
     * ${selfField.colRemark}
     */
    @ApiModelProperty(value = "${selfField.colRemark}")
    #end
    private ${selfField.attrType} ${selfField.attrName};
    #end

}
