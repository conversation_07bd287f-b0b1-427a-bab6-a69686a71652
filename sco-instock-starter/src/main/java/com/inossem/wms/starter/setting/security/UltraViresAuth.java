package com.inossem.wms.starter.setting.security;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 越权认证
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-08-18
 */
@Component
@Aspect
public class UltraViresAuth {

    private List<String> signListA = new ArrayList<>();
    private List<String> signListB = new ArrayList<>();
    private Boolean listActive = true;

    private final static String SIGN_EXPIRED_TIME = "7200";
    private final static String KEY = "0cd3a9c74c8a4ec38c9aa0aede38c59b";

    private final static String REQ_GET = "GET";
    private final static String REQ_POST = "POST";

    @Around("@annotation(com.inossem.wms.common.annotation.UltraVires)")
    public Object ultraViresAuth(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletRequest request = requestAttributes.getRequest();
        String requestMethod = request.getMethod();

        if (requestMethod.equals(REQ_GET)) {
            this.checkGetMethod(request);
        } else if (requestMethod.equals(REQ_POST)) {
            this.checkPostMethod(request, joinPoint);
        }

        Object[] args = joinPoint.getArgs();

        return joinPoint.proceed(args);
    }

    /**
     * 校验get请求
     *
     * @param request 请求对象
     */
    private void checkGetMethod(HttpServletRequest request) throws Exception {
        // 获取请求签名
        String sign = request.getHeader("sign");
        // 获取需要验证的签名字段
        String signKeys = request.getHeader("signKeys");
        if (null == signKeys || "".equals(signKeys)) {
            throw new Exception("请提供有效的签名字段");
        }
        String[] split = signKeys.split("&");
        Map<String, String> checkMap = new HashMap<>(split.length);
        for (String string : split) {
            String value = request.getParameter(string);
            checkMap.put(string, value);
        }
        checkTime(Long.valueOf(request.getParameter("timestamp")));
        checkSign(checkMap, sign);
    }

    /**
     * 校验post请求
     *
     * @param request   请求对象
     * @param joinPoint 切点
     */
    public void checkPostMethod(HttpServletRequest request, ProceedingJoinPoint joinPoint) throws Exception {
        // 获取请求签名
        String sign = request.getHeader("sign");
        // 获取需要验证的签名字段
        String signKeys = request.getHeader("signKeys");
        if (StringUtils.isEmpty(signKeys) || StringUtils.isEmpty(sign)) {
            throw new Exception("签名认证失败");
        }
        String[] split = signKeys.split("&");
        Map<String, String> checkMap = new HashMap<>(split.length);
        Object[] args = joinPoint.getArgs();
        JSONObject jsonData = new JSONObject();
        for (Object arg : args) {
            if (arg instanceof JSONObject) {
                jsonData = (JSONObject) arg;
            }
        }
        for (String string : split) {
            String value = jsonData.getString(string);
            checkMap.put(string, value);
        }
        checkTime(jsonData.getLong("timestamp"));
        checkSign(checkMap, sign);
    }

    /**
     * 校验过期时间
     *
     * @param timestamp 请求时间
     */
    private void checkTime(Long timestamp) throws Exception {
        long time = System.currentTimeMillis();
        // 超时判断
        if (time - timestamp > 1000 * Long.parseLong(SIGN_EXPIRED_TIME) || timestamp - time > 1000 * Long.parseLong(SIGN_EXPIRED_TIME)) {
            throw new Exception("签名超时");
        }
    }

    /**
     * 校验签名
     *
     * @param checkMap 签名
     * @param sign     签名
     */
    private void checkSign(Map<String, String> checkMap, String sign) throws Exception {
        int listSize = 5000;
        if (signListA.contains(sign) || signListB.contains(sign)) {
            throw new Exception("签名认证失败-已使用");
        } else {
            //添加
            if (listActive) {
                signListA.add(sign);
            } else {
                signListB.add(sign);
            }
            if (signListA.size() > listSize && signListB.size() > listSize) {
                if (listActive) {
                    signListB.clear();
                    listActive = false;
                } else {
                    signListA.clear();
                    listActive = true;
                }
            } else {
                if (signListA.size() > listSize) {
                    listActive = false;
                }
                if (signListB.size() > listSize) {
                    listActive = true;
                }
            }
        }
        if (checkMap.size() == 0) {
            throw new Exception("签名认证失败-header无参数");
        }
        String linkString = createLinkString(checkMap);

        String paySign = sign(linkString, KEY, "utf-8").toUpperCase();
        if (!sign.equals(paySign)) {
            throw new Exception("签名认证失败-签名无法匹配");
        }
    }

    /**
     * 签名字符串
     *
     * @param text         需要签名的字符串
     * @param key          密钥
     * @param inputCharset 编码格式
     * @return 签名结果
     */
    public static String sign(String text, String key, String inputCharset) {
        text = text + "&key=" + key;
        return DigestUtils.md5DigestAsHex(getContentBytes(text, inputCharset));
    }

    /**
     * 获取字节
     *
     * @param content 内容
     * @param charset 字符集
     * @return 字节
     */
    public static byte[] getContentBytes(String content, String charset) {
        if (charset == null || "".equals(charset)) {
            return content.getBytes();
        }
        try {
            return content.getBytes(charset);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("MD5签名过程中出现错误,指定的编码集不对,您目前指定的编码集是:" + charset);
        }
    }

    /**
     * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要排序并参与字符拼接的参数组
     * @return 拼接后字符串
     */
    public static String createLinkString(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder preStr = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            if (i == keys.size() - 1) {
                preStr.append(key).append("=").append(value);
            } else {
                preStr.append(key).append("=").append(value).append("&");
            }
        }
        return preStr.toString();
    }
}


