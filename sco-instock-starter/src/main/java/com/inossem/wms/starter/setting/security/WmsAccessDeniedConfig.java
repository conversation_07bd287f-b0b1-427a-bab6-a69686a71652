package com.inossem.wms.starter.setting.security;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;

/**
 * 菜单权限异常时
 *
 * <AUTHOR>
 * @date 2021/3/8 15:56
 */
public class WmsAccessDeniedConfig implements AccessDeniedHandler {
    @Override
    public void handle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AccessDeniedException e) {
        // 没有访问权限
        throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
    }
}
