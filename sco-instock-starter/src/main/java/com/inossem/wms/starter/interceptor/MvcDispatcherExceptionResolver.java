//package com.inossem.wms.starter.interceptor;
//
//import com.inossem.wms.common.enums.EnumReturnCode;
//import com.inossem.wms.common.enums.EnumReturnMessageLevel;
//import com.inossem.wms.common.model.common.base.BaseResult;
//import com.inossem.wms.common.util.UtilResponse;
//import com.inossem.wms.system.log.service.biz.ExceptionLogHandler;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.Ordered;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.HandlerExceptionResolver;
//import org.springframework.web.servlet.ModelAndView;
//
///**
// * MvcDispatcherExceptionResolver设计用于解决{@link org.springframework.web.servlet.DispatcherServlet}在处理控制器之前，
// * 调用getHandlerAdapter(mappedHandler.getHandler())方法之前出现异常时，
// * 获取处理器方法时抛出的mvc异常的统一处理缺失问题。
// * 参考org.springframework.web.servlet.DispatcherServlet#doDispatch(HttpServletRequest, HttpServletResponse)此方法的具体实现过程。
// *
// * <AUTHOR> <<EMAIL>>
// * @date 2021-10-19
// */
//@Component
//@Slf4j
//public class MvcDispatcherExceptionResolver implements HandlerExceptionResolver , Ordered {
//
//    @Autowired
//    protected ExceptionLogHandler exceptionLogHandler;
//
//    /**
//     * @param request  请求对象
//     * @param response 响应对象
//     * @param handler  处理器
//     * @param ex       待处理的异常对象
//     * @return ModelAndView 视图模型对象
//     * <AUTHOR> <<EMAIL>>
//     */
//    @Override
//    public ModelAndView
//    resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
//
//
//        BaseResult<Object> result = BaseResult.success();
//        result.setStatus(Boolean.FALSE);
//        result.setReturnCode(EnumReturnCode.RETURN_CODE_FAILURE.getValue());
//        result.setReturnMsg(ex.getMessage());
//        result.setMsgLevel(EnumReturnMessageLevel.ERROR.ordinal());
//        result.setData(ex.getMessage());
//        exceptionLogHandler.save(ex, ex.getMessage());
//
//        UtilResponse.returnBaseResult(request, response, result);
//        log.error("请求接口地址：" + request.getRequestURL() + "，DispatcherServlet处理请求分派前发生异常：" + ex.getMessage());
//        if (log.isDebugEnabled()) {
//            ex.printStackTrace();
//        }
//        return new ModelAndView();
//    }
//
//    /**
//     * 设置的异常处理器的处理顺序，这里为了解决{@link HandlerExceptionResolver}的处理顺序
//     * 此处理器的处理顺序应该小于{@link Integer#MAX_VALUE}
//     * @return 0
//     * <AUTHOR> <<EMAIL>>
//     */
//    @Override
//    public int getOrder() {
//        return 0;
//    }
//}
