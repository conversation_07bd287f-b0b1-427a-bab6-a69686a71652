package com.inossem.wms.starter.setting.mybatisplus;

import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilReflect;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MybatisPlus优化切面
 *
 * <AUTHOR>
 * @date 2021/09/06 14:51
 **/
@Aspect
@Component
@Slf4j
public class ScoMbpOptimizeAspect {

	/**
	 * MP提供的批量保存切点
	 */
	@Pointcut("execution(* com.baomidou.mybatisplus.extension.service.IService.saveBatch(..))")
	public void saveBatchPoint() {}

	/**
	 * MP提供的批量修改切点
	 */
	@Pointcut("execution(* com.baomidou.mybatisplus.extension.service.IService.updateBatchById(..))")
	public void updateBatchPoint() {}

	/**
	 * MP提供的批量保存或修改切点
	 */
	@Pointcut("execution(* com.baomidou.mybatisplus.extension.service.IService.saveOrUpdateBatch(..))")
	public void saveOrUpdateBatchPoint() {}

	/**
	 * MP提供的批量保存切点环绕增强
	 *
	 * @param proceedingJoinPoint
	 */
	@Around("saveBatchPoint()")
	public boolean aroundSaveBatchPoint(ProceedingJoinPoint proceedingJoinPoint) {

		if(proceedingJoinPoint.getArgs().length == 1){
			return ((BaseDataWrap)proceedingJoinPoint.getThis()).saveBatchOptimize((Collection)proceedingJoinPoint.getArgs()[0]);
		} else if(proceedingJoinPoint.getArgs().length == 2){
			return ((BaseDataWrap)proceedingJoinPoint.getThis()).saveBatchOptimize((Collection)proceedingJoinPoint.getArgs()[0], Integer.parseInt(proceedingJoinPoint.getArgs()[1].toString()));
		}

		return false;
	}

	/**
	 * MP提供的批量修改切点环绕增强
	 *
	 * @param proceedingJoinPoint
	 */
	@Around("updateBatchPoint()")
	public boolean aroundUpdateBatchPoint(ProceedingJoinPoint proceedingJoinPoint) {

		if(proceedingJoinPoint.getArgs().length == 1){
			return ((BaseDataWrap)proceedingJoinPoint.getThis()).updateBatchByIdOptimize((Collection)proceedingJoinPoint.getArgs()[0]);
		} else if(proceedingJoinPoint.getArgs().length == 2){
			return ((BaseDataWrap)proceedingJoinPoint.getThis()).updateBatchByIdOptimize((Collection)proceedingJoinPoint.getArgs()[0], Integer.parseInt(proceedingJoinPoint.getArgs()[1].toString()));
		}

		return false;
	}

	/**
	 * MP提供的批量保存或修改切点环绕增强
	 *
	 * @param proceedingJoinPoint
	 */
	@Around("saveOrUpdateBatchPoint()")
	public boolean aroundSaveOrUpdateBatchPoint(ProceedingJoinPoint proceedingJoinPoint) {

		if(proceedingJoinPoint.getArgs().length == 1){
			return ((BaseDataWrap)proceedingJoinPoint.getThis()).saveOrUpdateBatchOptimize((Collection)proceedingJoinPoint.getArgs()[0]);
		} else if(proceedingJoinPoint.getArgs().length == 2){
			return ((BaseDataWrap)proceedingJoinPoint.getThis()).saveOrUpdateBatchOptimize((Collection)proceedingJoinPoint.getArgs()[0], Integer.parseInt(proceedingJoinPoint.getArgs()[1].toString()));
		}

		return false;
	}


}