package com.inossem.wms.starter.interceptor;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * MethodRequestTraceHandler设计用于记录方法调用信息以及请求的日志打印
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-10-13
 */
@Aspect
@Component
@Slf4j
@Order(2)
public class MethodRequestTraceHandler {

    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public void postMapping() {}

    /**
     * 请求method前打印内容
     */
    @Before(value = "postMapping()")
    public void methodBefore(JoinPoint joinPoint) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletRequest request = requestAttributes.getRequest();

        // 打印请求内容
        if (log.isInfoEnabled()) {
            StringBuilder msg = new StringBuilder();
            msg.append("\n");
            msg.append("===============请求信息===============").append("\n");
            msg.append("请求地址: ").append(request.getRequestURL().toString()).append("\n");
            msg.append("请求方式: ").append(request.getMethod()).append("\n");
            msg.append("请求方法: ").append(joinPoint.getSignature()).append("\n");
//            msg.append("请求参数: ").append(Arrays.toString(joinPoint.getArgs())).append("\n");
            msg.append("===============请求信息===============");
            // 为了避免请求量较大时，多个打印语句导致输出乱序的问题，这里将要输出的内容拼接为一个长字符串一次性输出
            log.debug(msg.toString());
        }
    }

}
