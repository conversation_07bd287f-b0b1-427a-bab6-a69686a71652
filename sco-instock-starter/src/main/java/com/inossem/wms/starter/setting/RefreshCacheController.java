package com.inossem.wms.starter.setting;

import com.inossem.wms.common.model.common.base.BaseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RefreshCacheController {

    @Autowired
    private WmsSystemInitConfig systemInitConfig;

    @GetMapping(value = "/init-all-cache", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult initAllCache() {
        systemInitConfig.initAllCache();
        return BaseResult.success();
    }

}
