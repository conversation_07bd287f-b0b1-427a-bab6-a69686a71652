package com.inossem.wms.starter.setting;

import cn.hutool.core.util.StrUtil;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * WmsBannerConfig设计用于打印启动后的banner
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-08-16
 */
@Component
public class WmsBannerConfig {

    /** 应用程序版本，从maven pom文件读取 artifactId */
    private static String applicationVersion;
    /** 应用构建时间戳，从maven pom文件读取 sco.instock.build-timestamp */
    private static String applicationBuildTimestamp;
    /** 系统环境信息 */
    private static Environment environment;

    private static final String BANNER_GRID = "#";
    private static final String GREETING = "Inossem InStock system startup success.";
    private static final String VERSION = "App Version: %s";
    private static final String BUILD_TIME = "Build Time : %s";
    private static final int DEFAULT_GRID_WIDTH = 50;
    /** 缩进的空格数 */
    private static final int INDENT_SPACE = 3;
    private static final char BLANK_CHAR = ' ';

    /**
     * 生成启动完成后的banner字符串
     * @return banner字符串
     */
    public static String getBanner() {
        StringBuilder bannerBuilder = new StringBuilder();

        String greetingText = GREETING;
        String versionText = String.format(VERSION, applicationVersion);
        String buildTimeText = String.format(BUILD_TIME, getBuildTimeWithLocalTimezone(applicationBuildTimestamp));

        String ip;
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            ip = "127.0.0.1";
        }
        String port = environment.getProperty("server.port");
        String contextPath = environment.getProperty("server.servlet.context-path");
        String swaggerText = "Swagger doc: http://" + ip + ":" + port + contextPath + "/doc.html";

        String leftSidebar = BANNER_GRID + StrUtil.repeat(BLANK_CHAR, INDENT_SPACE);
        String rightSidebar = StrUtil.repeat(BLANK_CHAR, INDENT_SPACE) + BANNER_GRID;

        ArrayList<Integer> list = new ArrayList<>();
        list.add(greetingText.length());
        list.add(versionText.length());
        list.add(buildTimeText.length());
        list.add(swaggerText.length());

        // 根据显示信息的最大长度，动态计算所需的边框字符和空白字符
        int maxTextLength = list.stream().max(Comparable::compareTo).orElse(DEFAULT_GRID_WIDTH) + leftSidebar.length() + rightSidebar.length();
        maxTextLength = Math.max(maxTextLength, DEFAULT_GRID_WIDTH);
        int gridRepeatTimes = maxTextLength % 2 != 0 ? maxTextLength + 1 : maxTextLength;

        // 处理需要显示的字符串，得到每一行的显示内容
        String topString = StrUtil.repeat(BANNER_GRID, gridRepeatTimes);
        String bottomString = StrUtil.repeat(BANNER_GRID, gridRepeatTimes);
        String greetingString = StrUtil.fillAfter((leftSidebar + greetingText), BLANK_CHAR, maxTextLength - rightSidebar.length()) + rightSidebar;
        String versionString = StrUtil.fillAfter((leftSidebar + versionText), BLANK_CHAR, maxTextLength - rightSidebar.length()) + rightSidebar;
        String buildTimeString = StrUtil.fillAfter((leftSidebar + buildTimeText), BLANK_CHAR, maxTextLength - rightSidebar.length()) + rightSidebar;
        String swaggerDocString = StrUtil.fillAfter((leftSidebar + swaggerText), BLANK_CHAR, maxTextLength - rightSidebar.length()) + rightSidebar;

        // 对显示信息进行编组，并添加换行符
        bannerBuilder.append("\n");
        bannerBuilder.append(topString);
        bannerBuilder.append("\n");
        bannerBuilder.append(greetingString);
        bannerBuilder.append("\n");
        bannerBuilder.append(versionString);
        bannerBuilder.append("\n");
        bannerBuilder.append(buildTimeString);
        bannerBuilder.append("\n");
        bannerBuilder.append(swaggerDocString);
        bannerBuilder.append("\n");
        bannerBuilder.append(bottomString);
        return bannerBuilder.toString();
    }

    /**
     * 从Maven的pom文件读取的时间戳是UTC时间，通过此方法转换成本地时区时间
     * @param originalTimeString maven读入的时间戳字符串
     * @return 本地时区处理后的日期字符串
     */
    private static String getBuildTimeWithLocalTimezone(String originalTimeString) {
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        LocalDateTime dateTime = LocalDateTime.parse(originalTimeString, formatter);
//        Date date = Date.from(dateTime.toInstant(ZoneOffset.UTC));
        Date date = new Date();
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    @Value("${wms.application-version}")
    public void setApplicationVersion(String appVersion) {
        applicationVersion = appVersion;
    }
    @Value("${wms.application-build-timestamp}")
    public void setApplicationBuildTimestamp(String buildTimestamp) {
        applicationBuildTimestamp = buildTimestamp;
    }
    @Autowired
    public void setEnvironment(Environment env) {
        environment = env;
    }
}
