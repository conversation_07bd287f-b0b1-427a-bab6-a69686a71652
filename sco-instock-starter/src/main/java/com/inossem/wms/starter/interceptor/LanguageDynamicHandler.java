package com.inossem.wms.starter.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

/**
 * 动态数据国际化处理
 */
@Aspect
@Component
@Slf4j
@Order(3)
public class LanguageDynamicHandler {

    @Autowired
    protected DictionaryService dictionaryService;

    /**
     * 国际化处理
     */
    public void setReturnMsgByLangCode(BaseResult<?> returnObj) {
        String langCode = this.getLangCodeFromRequest();
        if (Const.DEFAULT_LANG_CODE.equals(langCode)) {
            return;
        }
        try {
            JSON jsonObject = (JSON) JSON.toJSON(returnObj.getData());
            this.doFilterByLanguage(returnObj.getData(), jsonObject, langCode);
        } catch (Exception ignored) {
            // 基础数据类型不再继续做递归处理
        }
    }

    /**
     * 从绑定到本线程的request对象中获取langCode语言码
     *
     * @return String 语言码
     * <AUTHOR>
     */
    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    /**
     * 递归处理国际化信息
     *
     * @param object   待转换信息
     * @param langCode 语言编码
     */
    private void doFilterByLanguage(Object object, Object jsonObject, String langCode) {
        Class<?> cls = object.getClass();
        // 如果是集合的话递归处理每一个对象
        if (jsonObject instanceof JSONArray) {
            for (Object obj : (List<?>) object) {
                try {
                    JSON json = (JSON) JSON.toJSON(obj);
                    this.doFilterByLanguage(obj, json, langCode);
                } catch (Exception ignored) {
                    // 基础数据类型不再继续做递归处理
                }
            }
        } else if (jsonObject instanceof JSONObject) {
            for (String currentKey : ((JSONObject) jsonObject).keySet()) {
                // 结尾为Name 或 Des 则尝试国际化处理，若未找到其他语言文本，数据保持不变
                if (currentKey.endsWith("List")) {
                    try {
                        Field currentField = cls.getDeclaredField(currentKey);
                        currentField.setAccessible(true);
                        JSON json = (JSON) JSON.toJSON(currentField.get(object));
                        this.doFilterByLanguage(currentField.get(object), json, langCode);
                    } catch (Exception ignored) {
                        // 基础数据类型不再继续做递归处理
                    }
                }
                if (!currentKey.endsWith(Const.NAME) && !currentKey.endsWith(Const.DES)) {
                    continue;
                }
                if (getNotI18nNameList().contains(currentKey)) {
                    continue;
                }
                try {
                    // 取出来源数据属性
                    Field sourceField = cls.getDeclaredField(currentKey);
                    boolean configField = this.getConfigField(sourceField.getName());
                    if (!configField) {
                        continue;
                    }
                    sourceField.setAccessible(true);
                    Object value = sourceField.get(object);
                    if (UtilObject.isNull(value)) {
                        continue;
                    }
                    Object o = sourceField.get(object);
                    if (UtilObject.isNotNull(o)) {
                        String sourceText = String.valueOf(o);
                        String i18nText = this.getI18nText(langCode, sourceField.getName(), sourceText);
                        sourceField.set(object, i18nText);
                    }
                } catch (NoSuchFieldException e) {
                    // 没有找到来源数据时, 不做国际化处理
                    System.out.println(e.getMessage());
                } catch (IllegalAccessException e) {
                    log.error(e.getMessage());
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_RESULT_TEXT);
                }

            }
        }
    }


    /**
     * 翻译国际化文本开关
     * <p>
     * 字段是否配置翻译国际化，返回 true 需要翻译文本
     * </p>
     */
    private boolean getConfigField(String fieldName) {
        Long i18nDynamicType = dictionaryService.getI18nDynamicType(fieldName);
        return UtilNumber.isNotEmpty(i18nDynamicType);
    }

    /**
     * 获取国际化文本
     *
     * @param langCode:语言码
     * @param fieldName:字段名
     * @param sourceText:源文本
     * @return 对应语言码的翻译文本，若未找到，则返回sourceText
     */
    private String getI18nText(String langCode, String fieldName, String sourceText) {
        String hashKey = String.format("%s_%s_%s", langCode, fieldName, sourceText);
        String i18nDynamicText = dictionaryService.getI18nDynamicText(hashKey);
        return UtilString.isNullOrEmpty(i18nDynamicText) ? sourceText : i18nDynamicText;
    }


    /**
     * 不需要国际化的Name列
     */
    public static List<String> getNotI18nNameList() {
        return Arrays.asList("createUserName", "modifyUserName");
    }

//    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
//    public void postMapping() {
//    }
//
//    @Pointcut("@annotation(org.springframework.web.bind.annotation.GetMapping)")
//    public void getMapping() {
//    }
//
//    @Pointcut("@annotation(org.springframework.web.bind.annotation.DeleteMapping)")
//    public void deleteMapping() {
//    }
//
//    @Pointcut("@annotation(org.springframework.web.bind.annotation.PutMapping)")
//    public void putMapping() {
//    }
//
//    @Pointcut("@annotation(org.springframework.web.bind.annotation.RequestMapping)")
//    public void requestMapping() {
//    }
//
//    @AfterReturning(value = "postMapping()", returning = "returnObj")
//    public void postMappingProcess(BaseResult<?> returnObj) {
//        setReturnMsgByLangCode(returnObj);
//    }
//
//    @AfterReturning(value = "getMapping()", returning = "returnObj")
//    public void getMappingProcess(BaseResult<?> returnObj) {
//        setReturnMsgByLangCode(returnObj);
//    }
//
//    @AfterReturning(value = "deleteMapping()", returning = "returnObj")
//    public void deleteMappingProcess(BaseResult<?> returnObj) {
//        setReturnMsgByLangCode(returnObj);
//    }
//
//    @AfterReturning(value = "putMapping()", returning = "returnObj")
//    public void putMappingProcess(BaseResult<?> returnObj) {
//        setReturnMsgByLangCode(returnObj);
//    }
//
//    @AfterReturning(value = "requestMapping()", returning = "returnObj")
//    public void requestMappingProcess(BaseResult<?> returnObj) {
//        setReturnMsgByLangCode(returnObj);
//    }

}
