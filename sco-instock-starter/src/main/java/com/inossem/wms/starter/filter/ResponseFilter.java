package com.inossem.wms.starter.filter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.zip.GZIPOutputStream;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.exception.WmsException;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ResponseFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        try {
            HttpServletRequest request = (HttpServletRequest)servletRequest;
            MyServletResponseWrapper response = new MyServletResponseWrapper((HttpServletResponse)servletResponse);

            if (request.getContentType() != null && request.getContentType().contains(MediaType.APPLICATION_OCTET_STREAM_VALUE)) {
                // 工作流查看图片
                chain.doFilter(servletRequest, servletResponse);
                return;
            }

            // 判断请求的ContentType是否为application/json
            if (request.getContentType() != null && ((request.getContentType().trim().toLowerCase().contains(MediaType.MULTIPART_FORM_DATA_VALUE.toLowerCase())) || request
                .getContentType().trim().toLowerCase().contains(MediaType.APPLICATION_JSON_VALUE.toLowerCase()))) {

                chain.doFilter(request, response);
                // 设置头信息
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                response.setHeader("Strict-Transport-Security", "value");
                String responseStr = new String(response.toByteArray());
                // 判断是否用GZIP压缩
                String compress = request.getHeader(Const.COMPRESS);
                if (EnumRealYn.TRUE.getStrValue().equals(compress)) {
                    // 创建一个新的 byte 数组输出流
                    ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
                    // 使用默认缓冲区大小创建新的输出流
                    GZIPOutputStream gzip = new GZIPOutputStream(byteOut);

                    // 将 b.length 个字节写入此输出流
                    gzip.write(responseStr.getBytes());
                    gzip.close();
                    servletResponse.setContentLength(Base64.getEncoder().encode(byteOut.toByteArray()).length);
                    servletResponse.getOutputStream().write(Base64.getEncoder().encode(byteOut.toByteArray()));

                } else {
                    servletResponse.setContentLength(responseStr.getBytes().length);
                    servletResponse.getOutputStream().write(responseStr.getBytes());
                }
            } else {
                chain.doFilter(servletRequest, servletResponse);
            }
        } catch (Exception e) {
            if (e.getMessage().contains(WmsException.class.getName())) {
                log.warn("过滤器链(FilterChain)终止执行");
                log.warn(e.getMessage());
                UtilHttpResult.handleAccessDenied(servletRequest, servletResponse, new WmsException(((WmsException)e.getCause()).getErrorCode()));
            } else {
                log.error("过滤器链执行失败");
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }
    }

    @Override
    public void destroy() {

    }
}

class MyServletResponseWrapper extends HttpServletResponseWrapper {

    private final ByteArrayOutputStream output;
    private ServletOutputStream filterOutput;

    public MyServletResponseWrapper(HttpServletResponse response) {
        super(response);
        output = new ByteArrayOutputStream();
    }

    /**
     * 将ServletOutputStream放到公共变量，解决不能多次读写问题
     * 
     * @return ServletOutputStream
     */
    @Override
    public ServletOutputStream getOutputStream() {
        if (filterOutput == null) {
            filterOutput = new ServletOutputStream() {
                @Override
                public void write(int b) {
                    output.write(b);
                }

                @Override
                public boolean isReady() {
                    return false;
                }

                @Override
                public void setWriteListener(WriteListener writeListener) {}
            };
        }
        return filterOutput;
    }

    public byte[] toByteArray() {
        return output.toByteArray();
    }

}
