package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizdomain.apply.service.component.ApplyCommonComponent;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.bizdomain.unitized.service.component.UnitizedLeisureApplyComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 成套设备 闲置物资申请 Service
 *
 * <AUTHOR>
 */

@Service
public class UnitizedLeisureApplyService {

    @Autowired
    private UnitizedLeisureApplyComponent leisureApplyComponent;

    @Autowired
    protected ApplyCommonComponent applyCommonComponent;

    @Autowired
    protected DicStockLocationDataWrap dicStockLocationDataWrap;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private OutputComponent outputComponent;

    /**
     * 初始化申请单
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"leisureApplyComponent#setInit", "leisureApplyComponent#setInfoExtendRelation",
            "leisureApplyComponent#setExtendAttachment", "leisureApplyComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 设置初始化信息
        leisureApplyComponent.setInit(ctx);
    }

    /**
     * 分页获取申请列表
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"leisureApplyComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 查询申请单列表-分页
        applyCommonComponent.setPage(ctx);
    }

    /**
     * 获取申请单详情
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"leisureApplyComponent#getInfo", "leisureApplyComponent#setBatchImg", "leisureApplyComponent#setInfoExtendRelation",
                    "leisureApplyComponent#setExtendAttachment", "leisureApplyComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 获取详情
        leisureApplyComponent.getInfo(ctx);

        // 设置单据流
        applyCommonComponent.setInfoExtendRelation(ctx);

        // 开启审批
        applyCommonComponent.setExtendWf(ctx);

        // 开启附件
        applyCommonComponent.setExtendAttachment(ctx);

        // 开启日志
        applyCommonComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存单据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"leisureApplyComponent#checkSave", "leisureApplyComponent#saveReceipt", "leisureApplyComponent#saveReceiptSameTime",
            "leisureApplyComponent#saveLabelReceiptRel", "leisureApplyComponent#saveBizReceiptOperationLog",
            "leisureApplyComponent#saveBizReceiptAttachment", "leisureApplyComponent#saveReceiptTree",
            "leisureApplyComponent#trimZeroQtyBinList"})
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存校验
        leisureApplyComponent.checkSave(ctx);

        // 保存单据【非同时模式】
        applyCommonComponent.saveApply(ctx);

        // 保存日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        applyCommonComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交单据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"leisureApplyComponent#checkSubmit", "leisureApplyComponent#submitReceipt",
            "leisureApplyComponent#submitReceiptSameTime", "leisureApplyComponent#occupyStock",
            "leisureApplyComponent#saveBizReceiptOperationLog", "leisureApplyComponent#saveBizReceiptAttachment",
            "leisureApplyComponent#saveReceiptTree", "leisureApplyComponent#generateInsMoveTypeAndCheck",
            "leisureApplyComponent#generateInsMoveTypeAndCheckSameTime", "leisureApplyComponent#postToSap",
            "leisureApplyComponent#postToInStock", "leisureApplyComponent#updateStatusCompleted", "leisureApplyComponent#addTaskRequest",
            "leisureApplyComponent#trimZeroQtyBinList", "leisureApplyComponent#checkSubmitSame",
            "leisureApplyComponent#updateStatusSubmitted"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 保存校验
        leisureApplyComponent.checkSave(ctx);

        // 提交单据【非同时模式】
        applyCommonComponent.saveApply(ctx);

        // 保存日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        applyCommonComponent.saveBizReceiptAttachment(ctx);

        //开启审批流
        leisureApplyComponent.startWorkFlow(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_CALLBACK_UNITIZED_IDLE_MATERIAL_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {

        // 审批回调
        leisureApplyComponent.approvalCallback(wfReceiptCo);
    }

    /**
     * 删除单据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"leisureApplyComponent#checkDelete", "leisureApplyComponent#checkTaskStatus",
            "leisureApplyComponent#deleteReceipt", "leisureApplyComponent#deleteReceiptSameTime", "leisureApplyComponent#deleteOccupyStock",
            "leisureApplyComponent#deleteReceiptTree", "leisureApplyComponent#deleteBizReceiptAttachment",
            "leisureApplyComponent#cancelTaskRequest"})
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 校验删除
        leisureApplyComponent.checkDelete(ctx);

        // 刪除借用申请单
        applyCommonComponent.deleteInfo(ctx);

        // 删除申请单附件
        applyCommonComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 查询物料库存
     *
     * @param ctx-po 申请单查询物料库存入参
     * @return 物料库存信息
     */
    public void getMatStock(BizContext ctx) {

        // 查询物料库存
        leisureApplyComponent.getMatFeatureStock(ctx);
    }

    /**
     * 获取配货信息
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputComponent#getItemInfoByFeatureStock", "outputComponent#getItemInfoByStockBin"})
    public void getItemInfo(BizContext ctx) {

        // 获取配货信息(特性库存)【非同时模式】
        outputComponent.getItemInfoByFeatureStock(ctx);
    }

    /**
     * 导入闲置物资申请
     *
     * @param ctx
     */
    public void importMatStock(BizContext ctx) {

        leisureApplyComponent.importMatStock(ctx);
    }

}
