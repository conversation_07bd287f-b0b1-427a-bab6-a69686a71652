package com.inossem.wms.bizdomain.register.service.biz;

import com.inossem.wms.bizdomain.register.service.component.ArrivalRegisterComponent;
import com.inossem.wms.bizdomain.register.service.component.RegisterCommonComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 到货登记 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-25
 */
@Service
public class ArrivalRegisterService {

    @Autowired
    protected RegisterCommonComponent registerCommonComponent;

    @Autowired
    protected ArrivalRegisterComponent arrivalRegisterComponent;

    /**
     * 查询车辆类型下拉
     *
     * @return 车辆类型下拉框
     */
    public void getCarTypeDown(BizContext ctx) {

        // 查询车辆类型下拉
        arrivalRegisterComponent.getCarTypeDown(ctx);

    }

    /**
     * 查询车辆下拉
     *
     * @return 车辆下拉框
     */
    public void getCarDown(BizContext ctx) {

        // 查询车辆下拉
        arrivalRegisterComponent.getCarDown(ctx);

    }

    /**
     * 查询吊带编号下拉
     *
     * @return 吊带编号下拉框
     */
    public void getSlingDown(BizContext ctx) {

        // 查询吊带编号下拉
        arrivalRegisterComponent.getSlingDown(ctx);

    }

    /**
     * 查询外观检查下拉
     *
     * @return 外观检查下拉框
     */
    public void getVisualCheckDown(BizContext ctx) {

        // 查询外观检查下拉
        arrivalRegisterComponent.getVisualCheckDown(ctx);

    }

    /**
     * 查询到货登记单列表-分页
     *
     * @param ctx-po 登记单分页查询入参
     * @return 到货登记单列表
     */
    public void getPage(BizContext ctx) {

        // 查询到货登记单列表-分页
        arrivalRegisterComponent.setPage(ctx);

    }

    /**
     * 查询到货登记单详情
     *
     * @param ctx-id 到货登记单抬头表主键
     * @return 到货登记单详情
     */
    public void getInfo(BizContext ctx) {

        // 到货登记单详情
        arrivalRegisterComponent.getInfo(ctx);

        // 设置详情页单据流
        registerCommonComponent.setInfoExtendRelation(ctx);

        // 开启附件
        registerCommonComponent.setExtendAttachment(ctx);

        // 开启操作日志
        registerCommonComponent.setExtendOperationLog(ctx);

    }

    public void export(BizContext ctx) {
        // 导出物项清单
        arrivalRegisterComponent.export(ctx);
    }

    /**
     * 初始化
     *
     * @return 到货登记单详情
     */
    public void init(BizContext ctx) {

        // 到货登记单详情
        arrivalRegisterComponent.init(ctx);

        // 设置详情页单据流
        registerCommonComponent.setInfoExtendRelation(ctx);

        // 开启附件
        registerCommonComponent.setExtendAttachment(ctx);

        // 开启操作日志
        registerCommonComponent.setExtendOperationLog(ctx);

    }

    /**
     * 到货登记-保存
     *
     * @param ctx-po 保存到货登记表单参数
     * @return ctx-receiptCode 到货登记单单号
     */
    @WmsMQListener(tags = TagConst.GEN_ARRIVAL_REGISTER_STOCK)
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 登记单保存校验
        registerCommonComponent.checkSaveRegister(ctx);

        // 保存登记单
        registerCommonComponent.saveApply(ctx);

        // 保存箱件图片
        arrivalRegisterComponent.saveBizCasesImg(ctx);

        // 保存附件
        registerCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        registerCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 到货登记-提交
     *
     * @param ctx-po 提交到货登记表单参数
     * @return ctx-receiptCode 到货登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 登记单保存校验
        registerCommonComponent.checkSaveRegister(ctx);

        // 校验采购订单是否存在 
        // registerCommonComponent.checkPurchaseOrder(ctx);    

        // 提交到货登记单
        arrivalRegisterComponent.submitLoseRegister(ctx);

        // 保存箱件图片
        arrivalRegisterComponent.saveBizCasesImg(ctx);

        // 保存附件
        registerCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        registerCommonComponent.saveBizReceiptOperationLog(ctx);

        // SAP过账
//        registerCommonComponent.postToSap(ctx);

        registerCommonComponent.writeBackRegisterQty(ctx);

        // 单据状态已完成
        registerCommonComponent.updateStatusCompleted(ctx);

        // 生成分配质检单
        arrivalRegisterComponent.genDistributeInspect(ctx);

    }

    /**
     * 到货登记-过账
     *
     * @param ctx-po 提交到货登记表单参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {

        // SAP过账
        registerCommonComponent.postToSap(ctx);

        // 单据状态已完成
        registerCommonComponent.updateStatusCompleted(ctx);

        // 生成分配质检单
        arrivalRegisterComponent.genDistributeInspect(ctx);

    }

    /**
     * 到货登记-删除
     *
     * @param ctx-id 到货登记单抬头表主键
     * @return ctx-receiptCode 到货登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 刪除登记单
        registerCommonComponent.deleteInfo(ctx);

        // 删除登记单单据流
        registerCommonComponent.deleteReceiptTree(ctx);

        // 删除登记单附件
        registerCommonComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        registerCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 到货登记-作废
     *
     * @param ctx-id 到货登记单抬头表主键
     * @return ctx-receiptCode 到货登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancel(BizContext ctx) {

        // 到货登记
        registerCommonComponent.cancelInfo(ctx);

        // 保存操作日志
        registerCommonComponent.saveBizReceiptOperationLog(ctx);

    }


    /**
     * 到货登记-冲销
     *
     * @param ctx-po 冲销入参
     */
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 到货登记单冲销校验
        arrivalRegisterComponent.checkWriteOffRegister(ctx);

        // SAP冲销
        registerCommonComponent.writeOffToSap(ctx);

        // 保存冲销日志
        registerCommonComponent.saveBizReceiptWriteOffOperationLog(ctx);

        // 单据状态已冲销
        //registerCommonComponent.updateStatusWriteOff(ctx);

    }


    /**
     * 到货登记-打印待检标识-PDA
     * @param ctx
     */
    public void boxApplyLabelPrint(BizContext ctx) {
        // 打印待检标识校验
        registerCommonComponent.checkPrint(ctx);
        // 填充打印数据
        registerCommonComponent.fillPrintData(ctx);
    }


    /**
     * 查询 油品送货通知
     * @param ctx
     */
    public void getDeliveryNotice(BizContext ctx){
        registerCommonComponent.getDeliveryNotice(ctx);
    }


    public void revoke(BizContext ctx) {

        // 撤销
        registerCommonComponent.revoke(ctx);

    }


}
