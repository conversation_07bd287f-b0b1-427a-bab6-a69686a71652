package com.inossem.wms.bizdomain.output.controller;

import com.inossem.wms.bizdomain.output.service.biz.ReturnOldOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 退旧出库 controller
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
@RestController
@Api(tags = "出库管理-退旧出库")
public class ReturnOldOutputController {

    @Autowired
    private ReturnOldOutputService returnOldOutputService;

    @ApiOperation(value = "退旧出库创建", tags = {"出库管理-退旧出库"})
    @GetMapping(value = "/outputs/return-old-output/inits")
    public BaseResult init(BizContext ctx) {
        returnOldOutputService.init(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "退旧出库查询列表（分页）", tags = {"出库管理-退旧出库"})
    @PostMapping(value = "/outputs/return-old-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptOutputQueryListPO po, BizContext ctx) {
        returnOldOutputService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "退旧出库查询物料库存", tags = {"出库管理-退旧出库"})
    @PostMapping(value = "/outputs/return-old-output/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        returnOldOutputService.getMatStock(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @ApiOperation(value = "退旧出库获取配货信息", tags = {"出库管理-退旧出库"})
    @PostMapping(value = "/outputs/return-old-output/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        returnOldOutputService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }

    @ApiOperation(value = "退旧出库详情", tags = {"出库管理-退旧出库"})
    @GetMapping(value = "/outputs/return-old-output/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        returnOldOutputService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "退旧出库保存", tags = {"出库管理-退旧出库"})
    @PostMapping(value = "/outputs/return-old-output/save")
    public BaseResult save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        returnOldOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "退旧出库提交", tags = {"出库管理-退旧出库"})
    @PostMapping(value = "/outputs/return-old-output/submit")
    public BaseResult submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        returnOldOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "退旧出库过账", tags = {"出库管理-退旧出库"})
    @PostMapping(value = "/outputs/return-old-output/post")
    public BaseResult post(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        returnOldOutputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_POST_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "退旧出库冲销", tags = {"出库管理-退旧出库"})
    @PostMapping(value = "/outputs/return-old-output/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        returnOldOutputService.writeOff(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "退旧出库删除", tags = {"出库管理-退旧出库"})
    @DeleteMapping(value = "/outputs/return-old-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        returnOldOutputService.delete(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_DELETE_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "退旧物资出库单-数据导入", tags = {"退旧物资出库单-数据导入"})
    @PostMapping(path = "/outputs/return-old-output/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptOutputHeadDTO> importMaterial(@RequestPart("file") MultipartFile file, BizContext ctx) {
        returnOldOutputService.importData(ctx);
        BizReceiptOutputHeadDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}
