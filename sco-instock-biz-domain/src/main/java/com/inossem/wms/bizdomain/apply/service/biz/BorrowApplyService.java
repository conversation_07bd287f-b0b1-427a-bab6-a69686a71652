package com.inossem.wms.bizdomain.apply.service.biz;

import com.inossem.wms.bizdomain.apply.service.component.ApplyCommonComponent;
import com.inossem.wms.bizdomain.apply.service.component.BorrowApplyComponent;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 借用申请 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Service
public class BorrowApplyService {

    @Autowired
    protected BorrowApplyComponent borrowApplyComponent;

    @Autowired
    protected ApplyCommonComponent applyCommonComponent;

    /**
     * 查询借用类型下拉
     *
     * @return 借用类型下拉框
     */
    public void getBorrowTypeDown(BizContext ctx) {

        // 查询借用类型下拉
        borrowApplyComponent.getBorrowTypeDown(ctx);

    }

    /**
     * 借用申请-初始化
     *
     * @return 借用申请单
     */
    public void init(BizContext ctx) {

        // 页面初始化
        borrowApplyComponent.setInit(ctx);

    }

    /**
     * 查询申请单列表-分页
     *
     * @param ctx-po 申请单分页查询入参
     * @return 申请单列表
     */
    public void getPage(BizContext ctx) {

        // 查询申请单列表-分页
        borrowApplyComponent.setPage(ctx);

    }

    /**
     * 查询借用申请单详情
     *
     * @param ctx-id 借用申请单抬头表主键
     * @return 借用申请单详情
     */
    public void getInfo(BizContext ctx) {

        // 借用申请单详情
        borrowApplyComponent.getInfo(ctx);

        // 设置详情页单据流
        applyCommonComponent.setInfoExtendRelation(ctx);

        // 开启附件
        applyCommonComponent.setExtendAttachment(ctx);

        // 开启操作日志
        applyCommonComponent.setExtendOperationLog(ctx);

    }

    /**
     * 借用申请-保存
     *
     * @param ctx-po 保存借用申请表单参数
     * @return ctx-receiptCode 借用申请单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 申请单保存校验
        applyCommonComponent.checkSaveApply(ctx);

        // 删除占用库存
        applyCommonComponent.removeOccupyStock(ctx);

        // 保存申请单
        applyCommonComponent.saveApply(ctx);

        // 占用库存
        applyCommonComponent.occupyStock(ctx);

        // 保存附件
        applyCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 借用申请-提交
     *
     * @param ctx-po 提交借用申请表单参数
     * @return ctx-receiptCode 借用申请单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 申请单保存校验
        applyCommonComponent.checkSaveApply(ctx);

        // 删除占用库存
        applyCommonComponent.removeOccupyStock(ctx);

        // 提交借用申请单
        borrowApplyComponent.submitBorrowApply(ctx);

        // 占用库存
        applyCommonComponent.occupyStock(ctx);

        // 保存附件
        applyCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

        // 更新申请单已完成
        applyCommonComponent.updateStatusCompleted(ctx);

        // 生成借用出库单
        borrowApplyComponent.genBorrowOutput(ctx);

    }

    /**
     * 借用申请-删除
     *
     * @param ctx-id 借用申请单抬头表主键
     * @return ctx-receiptCode 借用申请单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 刪除借用申请单
        applyCommonComponent.deleteInfo(ctx);

        // 删除申请单单据流
        applyCommonComponent.deleteReceiptTree(ctx);

        // 删除申请单附件
        applyCommonComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

        // 删除占用库存
        applyCommonComponent.removeOccupyStock(ctx);

    }

    /**
     * 查询物料库存
     *
     * @param ctx-po 申请单查询物料库存入参
     * @return 物料库存信息
     */
    public void getMatStock(BizContext ctx) {

        // 查询物料库存
        applyCommonComponent.getMatFeatureStock(ctx);

    }

}
