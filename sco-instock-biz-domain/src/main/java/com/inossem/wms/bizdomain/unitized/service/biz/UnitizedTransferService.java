package com.inossem.wms.bizdomain.unitized.service.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.inossem.wms.bizdomain.unitized.service.component.UnitizedTransferComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class UnitizedTransferService {

    @Autowired
    @Lazy
    private UnitizedTransferComponent unitizedTransferComponent;



    /**
     * 移动类型列表
     */
    @Entrance(call = {"transferComponent#getMoveTypeList"})
    public void getMoveTypeList(BizContext ctx) {

        // 移动类型列表
        unitizedTransferComponent.getMoveTypeList(ctx);
    }

    /**
     * 页面初始化
     */
    @Entrance(call = {"transferComponent#init", "transferComponent#setExtendWf",
        "transferComponent#setExtendAttachment", "transferComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 页面初始化
        unitizedTransferComponent.init(ctx);

        // 开启审批
        //transferComponent.setExtendWf(ctx);

        // 开启附件
        unitizedTransferComponent.setExtendAttachment(ctx);

        // 开启操作日志
        unitizedTransferComponent.setExtendOperationLog(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transferComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        unitizedTransferComponent.getStock(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transferComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        unitizedTransferComponent.getPage(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transferComponent#getInfo", "transferComponent#setExtendWf",
        "transferComponent#setExtendAttachment", "transferComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 查询单据详情,包含按钮组和扩展功能
        unitizedTransferComponent.getInfo(ctx);

        // 开启审批
        unitizedTransferComponent.setExtendWf(ctx);

        // 开启附件
        unitizedTransferComponent.setExtendAttachment(ctx);

        // 开启操作日志
        unitizedTransferComponent.setExtendOperationLog(ctx);

        // 开启审批
        unitizedTransferComponent.setExtendWf(ctx);

    }

    /**
     * 保存
     */
    @Entrance(call = {"transferComponent#checkSaveData", "transferComponent#save",
        "transferComponent#saveBizReceiptAttachment", "transferComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存时校验数据
        unitizedTransferComponent.checkSaveData(ctx);

        // 保存
        unitizedTransferComponent.save(ctx);

        // 保存附件
        unitizedTransferComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        unitizedTransferComponent.saveBizReceiptOperationLog(ctx);
    }

    @Entrance(call = {"transferComponent#checkSaveData", "transferComponent#save",
            "transferComponent#saveBizReceiptAttachment", "transferComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 保存时校验数据
        unitizedTransferComponent.checkSubmitData(ctx);

        // 保存
        unitizedTransferComponent.submit(ctx);

        // 保存附件
        unitizedTransferComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        unitizedTransferComponent.saveBizReceiptOperationLog(ctx);

        // 【先作业模式】状态变更已提交
        unitizedTransferComponent.updateStatusSubmitted(ctx);

        // 根据移动类型保存bin
        unitizedTransferComponent.saveOutputBinByMoveType(ctx);

        // 发起审批
        unitizedTransferComponent.startWorkFlow(ctx);

    }

    /**
     * 过账
     */
    @Entrance(call = {"transferComponent#checkSubmitData", "transferComponent#submit",
        "transferComponent#saveBizReceiptAttachment", "transferComponent#saveBizReceiptOperationLog",
        "transferComponent#saveOutputBinByMoveType", "transferComponent#generateInsDocToPost",
        "transferComponent#checkAndComputeForModifyStock", "transferComponent#updateStatusUnsync",
        "transferComponent#post", "transferComponent#modifyStock", "transferComponent#modifyLabel",
        "transferComponent#updateStatusCompleted"})
    public void post(BizContext ctx) {

//        // 提交时校验数据
//        transferComponent.checkSubmitData(ctx);
//
//        // 提交
//        transferComponent.submit(ctx);


        // 生成ins凭证 - 转储接收发出一次过账
        unitizedTransferComponent.generateInsDocToPost(ctx);

        // 过账前校验和数量计算
        unitizedTransferComponent.checkAndComputeForModifyStock(ctx);

        // 状态变更-未同步
        unitizedTransferComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        unitizedTransferComponent.post(ctx);

        unitizedTransferComponent.modifyStock(ctx);

        // 修改标签
        unitizedTransferComponent.modifyLabel(ctx);

        // 状态变更-已完成
        unitizedTransferComponent.updateStatusCompleted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transferComponent#delete", "transferComponent#deleteBizReceiptAttachment"})
    public void delete(BizContext ctx) {

        // 删除
        unitizedTransferComponent.delete(ctx);

        // 逻辑删除附件
        unitizedTransferComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 冲销
     */
    @Entrance(call = {"transferComponent#checkWriteOffData", "transferComponent#generateInsDocToPostWriteOff",
        "transferComponent#checkAndComputeForModifyStock", "transferComponent#writeOff",
        "transferComponent#modifyStock", "transferComponent#modifyLabel", "transferComponent#updateStatusWriteOff"})
    public void writeOff(BizContext ctx) {

        // 冲销时校验数据
        unitizedTransferComponent.checkWriteOffData(ctx);

        // 生成ins凭证 - 冲销
        unitizedTransferComponent.generateInsDocToPostWriteOff(ctx);

        // 过账前校验和数量计算
        unitizedTransferComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账-冲销
        unitizedTransferComponent.writeOff(ctx);

        // 修改库存
        unitizedTransferComponent.modifyStock(ctx);

        // 修改标签
        unitizedTransferComponent.modifyLabel(ctx);

        // 行项目状态变更-已冲销
        unitizedTransferComponent.updateStatusWriteOff(ctx);
    }

    /**
     * 获取WBS集合
     */
    @Entrance(call = {"transferComponent#getWbsList"})
    public void getWbsList(BizContext ctx) {

        // 获取WBS集合
        unitizedTransferComponent.getWbsList(ctx);
    }


    /**
     * 审批回调
     *
     * @param wfReceiptCo BizApprovalReceiptInstanceRelDTO
     */
    @WmsMQListener(tags = TagConst.APPROVAL_UNITIZED_TRANSFER_NATURE_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        if (EnumApprovalStatus.FINISH.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批通过
            unitizedTransferComponent.doTransferPost(wfReceiptCo);
        } else if (EnumApprovalStatus.REJECT.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批拒绝
            unitizedTransferComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }

    }



}
