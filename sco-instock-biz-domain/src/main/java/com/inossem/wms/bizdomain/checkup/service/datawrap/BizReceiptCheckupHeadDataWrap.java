package com.inossem.wms.bizdomain.checkup.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.checkup.dao.BizReceiptCheckupHeadMapper;
import com.inossem.wms.common.model.bizdomain.checkup.dto.BizReceiptCheckupHeadDTO;
import com.inossem.wms.common.model.bizdomain.checkup.entity.BizReceiptCheckupHead;
import com.inossem.wms.common.model.bizdomain.checkup.po.BizReceiptCheckupSearchPO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/7/30 14:10
 * @desc BizReceiptCheckupHeadDataWrap
 */
@Service
public class BizReceiptCheckupHeadDataWrap extends BaseDataWrap<BizReceiptCheckupHeadMapper, BizReceiptCheckupHead> {

    public List<BizReceiptCheckupHeadDTO> selectPageList(IPage<BizReceiptCheckupHeadDTO> page, BizReceiptCheckupSearchPO po) {
        return this.baseMapper.selectPageList(page, po);
    }
}
