package com.inossem.wms.bizdomain.room.controller;


import com.inossem.wms.bizdomain.room.service.biz.BizRoomReceiptCheckInReqService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckInReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomReceiptSearchPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(tags = "房间管理-住房申请")
public class BizRoomReceiptCheckInReqController {

    @Autowired
    private BizRoomReceiptCheckInReqService bizRoomReceiptCheckInReqService;


    @ApiOperation(value = "住房申请-初始化")
    @PostMapping(value = "/room/check-in-req/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizRoomReceiptCheckInReqHeadDTO>> init(@ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.init(ctx);
        return BaseResult.success(ctx.getVoContextData(BizResultVO.class));
    }

    @ApiOperation(value = "住房申请-分页")
    @PostMapping(value = "/room/check-in-req/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizRoomReceiptCheckInReqHeadDTO>> getPage(@RequestBody BizRoomReceiptSearchPO po, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.getPage(ctx);
        return BaseResult.success(ctx.getVoContextData(PageObjectVO.class));
    }

    @ApiOperation(value = "住房申请-详情")
    @GetMapping(value = "/room/check-in-req/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizRoomReceiptCheckInReqHeadDTO>> getInfo(@PathVariable("id") Long id, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.getInfo(ctx);
        return BaseResult.success(ctx.getVoContextData(BizResultVO.class));
    }

    @ApiOperation(value = "住房申请-详情(入参增加taskId)")
    @GetMapping(value = "/room/check-in-req/{id}/{taskId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizRoomReceiptCheckInReqHeadDTO>> getInfo(@PathVariable("id") Long id, @PathVariable("taskId") String taskId, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.getInfo(ctx);
        return BaseResult.success(ctx.getVoContextData(BizResultVO.class));
    }

    @ApiOperation(value = "住房申请-保存")
    @PostMapping(value = "/room/check-in-req/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizRoomReceiptCheckInReqHeadDTO po, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.saveReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "住房申请-提交")
    @PostMapping(value = "/room/check-in-req/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizRoomReceiptCheckInReqHeadDTO po, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.submitReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "住房申请-删除")
    @DeleteMapping(value = "/room/check-in-req/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> delete(@PathVariable("id") Long id, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.deleteReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "住房申请-撤销")
    @DeleteMapping(value = "/room/check-in-req/revoke/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> revoke(@PathVariable("id") Long id, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptCheckInReqService.revokeReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

}

