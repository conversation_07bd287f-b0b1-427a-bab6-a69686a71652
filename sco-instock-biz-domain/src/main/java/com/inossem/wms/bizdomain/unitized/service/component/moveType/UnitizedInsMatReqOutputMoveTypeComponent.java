package com.inossem.wms.bizdomain.unitized.service.component.moveType;

import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生成过账移动类型
 *
 * <AUTHOR>
 * @date 2021/3/31 11:11
 */

@Service
public class UnitizedInsMatReqOutputMoveTypeComponent {

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private OutputComponent outputComponent;

    /**
     * 生成记账ins凭证
     *
     * @param headDTO
     * @return
     */
    public StockInsMoveTypeDTO generatePostingInsDoc(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        itemDTOList = itemDTOList.stream().filter(item -> item.getTaskQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            if (!Collections.isEmpty(itemDTO.getAssembleDTOList()) && Collections.isEmpty(itemDTO.getBinDTOList())) {
                // 先过账模式，过账时bin表没数据，通过assemble表赋值
                insDocRid = this.getInsPostFirst(headDTO, itemDTO, insDocRid, insDocBatchList, insDocBinList);
            } else {
                // 非先过账模式，通过bin表赋值
                insDocRid = this.getInsNonPostFirst(headDTO, itemDTO, insDocRid, insDocBatchList, insDocBinList);
            }
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }
    public StockInsMoveTypeDTO generatePostingInsDocByApply(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        List<BizReceiptApplyBinDTO> applyBinDTOList = headDTO.getBinList().stream().filter(item -> item.getTaskQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptApplyBinDTO applyBinDTO : applyBinDTOList) {
            insDocRid = this.getInsByApply(headDTO, applyBinDTO, insDocRid, insDocBatchList, insDocBinList);
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }

    /**
     * 非先过账模式
     *
     * @param headDTO
     * @param itemDTO
     * @param insDocRid
     * @param insDocBatchList
     * @param insDocBinList
     */
    private Integer getInsNonPostFirst(BizReceiptOutputHeadDTO headDTO, BizReceiptOutputItemDTO itemDTO,
        Integer insDocRid, List<StockInsDocBatch> insDocBatchList, List<StockInsDocBin> insDocBinList) {
        for (BizReceiptOutputBinDTO binDTO : itemDTO.getBinDTOList()) {
            BizReceiptAssembleDTO assemble  =new BizReceiptAssembleDTO();
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                if(binDTO.getMatId().equals(assembleDTO.getMatId())){
                    assemble=assembleDTO;
                }
            }
            // 批次库存扣减
            StockInsDocBatch insDocBatch = new StockInsDocBatch();
            insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
            insDocBatch.setMatId(assemble.getMatId());
            insDocBatch.setBatchId(assemble.getUnitizedBatchId());
            insDocBatch.setFtyId(assemble.getFtyId());
            insDocBatch.setLocationId(assemble.getLocationId());
            insDocBatch.setUnitId(assemble.getUnitId());
            insDocBatch.setDecimalPlace(assemble.getDecimalPlace());
            insDocBatch.setDocDate(itemDTO.getDocDate());
            insDocBatch.setPostingDate(itemDTO.getPostingDate());
            insDocBatch.setMoveTypeId(itemDTO.getMoveTypeId());
            insDocBatch.setMoveQty(binDTO.getQty());
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBatch.setPreReceiptHeadId(headDTO.getId());
            insDocBatch.setPreReceiptItemId(itemDTO.getId());
            insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
            insDocBatch.setPreReceiptBinId(binDTO.getId());
            insDocBatch.setPreReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue());
            insDocBatch.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBatch.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBatch.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
            insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
            insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
            insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            insDocBatch.setCreateUserId(itemDTO.getCreateUserId());
            insDocBatchList.add(insDocBatch);
            // 出库临时区扣减
            StockInsDocBin insDocBin = new StockInsDocBin();
            insDocBin.setInsDocRid(insDocRid.toString());
            insDocBin.setMatId(assemble.getMatId());
            insDocBin.setBatchId(assemble.getUnitizedBatchId());
            insDocBin.setFtyId(assemble.getFtyId());
            insDocBin.setLocationId(assemble.getLocationId());
            insDocBin.setUnitId(assemble.getUnitId());
            insDocBin.setDecimalPlace(assemble.getDecimalPlace());
            insDocBin.setMoveQty(binDTO.getQty());
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBin.setPreReceiptHeadId(headDTO.getId());
            insDocBin.setPreReceiptItemId(itemDTO.getId());
            insDocBin.setPreReceiptBinId(binDTO.getId());
            insDocBin.setPreReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue());
            insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBin.setMatDocCode(itemDTO.getMatDocCode());
            insDocBin.setMatDocRid(itemDTO.getMatDocRid());
            insDocBin.setMatDocYear(itemDTO.getMatDocYear());
            insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            String typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
            insDocBin.setWhId(dictionaryService.getLocationCacheById(assemble.getLocationId()).getWhId());
            insDocBin.setCellId(0L);
            insDocBin.setCreateUserId(itemDTO.getCreateUserId());
            insDocBinList.add(insDocBin);
            insDocRid++;
        }
        return insDocRid;
    }
    private Integer getInsByApply(BizReceiptOutputHeadDTO headDTO, BizReceiptApplyBinDTO applyBinDTO,
        Integer insDocRid, List<StockInsDocBatch> insDocBatchList, List<StockInsDocBin> insDocBinList) {
        for (BizReceiptOutputBinDTO binDTO : applyBinDTO.getBinDTOList()) {
            // 批次库存扣减
            StockInsDocBatch insDocBatch = new StockInsDocBatch();
            insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
            insDocBatch.setMatId(binDTO.getMatId());
            insDocBatch.setBatchId(binDTO.getBatchId());
            insDocBatch.setFtyId(applyBinDTO.getFtyId());
            insDocBatch.setLocationId(applyBinDTO.getLocationId());
            DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(binDTO.getMatId());
            DicUnit dicUnit = dictionaryService.getUnitCacheById(dicMaterialDTO.getUnitId());
            insDocBatch.setUnitId(dicUnit.getId());
            insDocBatch.setDecimalPlace(dicUnit.getDecimalPlace());
            insDocBatch.setDocDate(headDTO.getDocDate());
            insDocBatch.setPostingDate(headDTO.getPostingDate());
//            insDocBatch.setMoveTypeId(itemDTO.getMoveTypeId());
            insDocBatch.setMoveQty(binDTO.getQty());
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBatch.setPreReceiptHeadId(headDTO.getId());
            insDocBatch.setPreReceiptItemId(applyBinDTO.getId());
            insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
            insDocBatch.setPreReceiptBinId(binDTO.getId());
            insDocBatch.setPreReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue());
            insDocBatch.setReferReceiptHeadId(applyBinDTO.getHeadId());
            insDocBatch.setReferReceiptItemId(applyBinDTO.getItemId());
            insDocBatch.setReferReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue());
//            insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
//            insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
//            insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
            insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            insDocBatch.setCreateUserId(headDTO.getCreateUserId());
            insDocBatchList.add(insDocBatch);
            // 出库临时区扣减
            StockInsDocBin insDocBin = new StockInsDocBin();
            insDocBin.setInsDocRid(insDocRid.toString());
            insDocBin.setMatId(binDTO.getMatId());
            insDocBin.setBatchId(binDTO.getBatchId());
            insDocBin.setFtyId(applyBinDTO.getFtyId());
            insDocBin.setLocationId(applyBinDTO.getLocationId());
            insDocBin.setUnitId(dicUnit.getId());
            insDocBin.setDecimalPlace(dicUnit.getDecimalPlace());
            insDocBin.setMoveQty(binDTO.getQty());
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBin.setPreReceiptHeadId(headDTO.getId());
            insDocBin.setPreReceiptItemId(applyBinDTO.getId());
            insDocBin.setPreReceiptBinId(binDTO.getId());
            insDocBin.setPreReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue());
            insDocBin.setReferReceiptHeadId(applyBinDTO.getHeadId());
            insDocBin.setReferReceiptItemId(applyBinDTO.getItemId());
            insDocBin.setReferReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue());
//            insDocBin.setMatDocCode(itemDTO.getMatDocCode());
//            insDocBin.setMatDocRid(itemDTO.getMatDocRid());
//            insDocBin.setMatDocYear(itemDTO.getMatDocYear());
            insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            String whCode = dictionaryService.getLocationCacheById(applyBinDTO.getLocationId()).getWhCode();
            String typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(whCode, typeCode);
            Long binId = dictionaryService.getBinIdCacheByCode(whCode, typeCode, binCode);
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
            insDocBin.setWhId(dictionaryService.getLocationCacheById(applyBinDTO.getLocationId()).getWhId());
            insDocBin.setCellId(0L);
            insDocBin.setCreateUserId(headDTO.getCreateUserId());
            insDocBinList.add(insDocBin);
            insDocRid++;
        }
        return insDocRid;
    }

    /**
     * 先过账模式
     *
     * @param headDTO
     * @param itemDTO
     * @param insDocRid
     * @param insDocBatchList
     * @param insDocBinList
     */
    private Integer getInsPostFirst(BizReceiptOutputHeadDTO headDTO, BizReceiptOutputItemDTO itemDTO, Integer insDocRid,
        List<StockInsDocBatch> insDocBatchList, List<StockInsDocBin> insDocBinList) {
        for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
            // 批次库存扣减
            StockInsDocBatch insDocBatch = new StockInsDocBatch();
            insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
            insDocBatch.setMatId(itemDTO.getMatId());
            Long batchId = outputComponent.getBatchId(assembleDTO);
            insDocBatch.setBatchId(batchId);
            insDocBatch.setFtyId(itemDTO.getFtyId());
            insDocBatch.setLocationId(itemDTO.getLocationId());
            insDocBatch.setUnitId(itemDTO.getUnitId());
            insDocBatch.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBatch.setDocDate(itemDTO.getDocDate());
            insDocBatch.setPostingDate(itemDTO.getPostingDate());
            insDocBatch.setMoveTypeId(itemDTO.getMoveTypeId());
            insDocBatch.setMoveQty(assembleDTO.getQty());
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBatch.setPreReceiptHeadId(headDTO.getId());
            insDocBatch.setPreReceiptItemId(itemDTO.getId());
            insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
            insDocBatch.setPreReceiptBinId(null);
            insDocBatch.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
            insDocBatch.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBatch.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBatch.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
            insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
            insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
            insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            insDocBatch.setCreateUserId(itemDTO.getCreateUserId());
            insDocBatchList.add(insDocBatch);
            // 出库临时区扣减
            StockInsDocBin insDocBin = new StockInsDocBin();
            insDocBin.setInsDocRid(insDocRid.toString());
            insDocBin.setMatId(itemDTO.getMatId());
            insDocBin.setBatchId(batchId);
            insDocBin.setFtyId(itemDTO.getFtyId());
            insDocBin.setLocationId(itemDTO.getLocationId());
            insDocBin.setUnitId(itemDTO.getUnitId());
            insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBin.setMoveQty(assembleDTO.getQty());
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBin.setPreReceiptHeadId(headDTO.getId());
            insDocBin.setPreReceiptItemId(itemDTO.getId());
            insDocBin.setPreReceiptBinId(null);
            insDocBin.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
            insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBin.setMatDocCode(itemDTO.getMatDocCode());
            insDocBin.setMatDocRid(itemDTO.getMatDocRid());
            insDocBin.setMatDocYear(itemDTO.getMatDocYear());
            insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            String typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
            insDocBin.setWhId(itemDTO.getWhId());
            insDocBin.setCellId(0L);
            insDocBin.setCreateUserId(itemDTO.getCreateUserId());
            insDocBinList.add(insDocBin);
            insDocRid++;
        }
        return insDocRid;
    }

}
