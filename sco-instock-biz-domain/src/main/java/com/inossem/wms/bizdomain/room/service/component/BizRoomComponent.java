package com.inossem.wms.bizdomain.room.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.language.ExportDataI18nComponent;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptRepairHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.room.EnumRoomStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomDTO;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoom;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptRepairHead;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomImport;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomSearchPO;
import com.inossem.wms.common.model.bizdomain.room.vo.BizRoomExportVO;
import com.inossem.wms.common.model.bizdomain.room.vo.BizRoomPageVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.file.service.biz.FileService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 房间管理组件库
 *
 **/
@Slf4j
@Component
public class BizRoomComponent {

    @Autowired
    private BizRoomDataWrap bizRoomDataWrap;

    @Autowired
    private BizRoomReceiptRepairHeadDataWrap bizRoomReceiptRepairHeadDataWrap;

    @Autowired
    private ExportDataI18nComponent exportDataI18nComponent;

    @Autowired
    private FileService fileService;


    /**
     * 导入数据
     */
    @SneakyThrows
    public void importData(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getFileContextData();

        //获取EXCEL数据
        List<BizRoomImport> importDataList = (List<BizRoomImport>) UtilExcel.readExcelData(file.getInputStream(), BizRoomImport.class);

        List<String> roomCodeList = new ArrayList<>();

        // 遍历导入数据进行校验
        for(int i = 0; i < importDataList.size(); i++){

            BizRoomImport importData = importDataList.get(i);

            if (UtilString.isNullOrEmpty(importData.getBuildingNo())
                    || UtilString.isNullOrEmpty(importData.getRoomNo())
                    || UtilNumber.isNull(importData.getBedCount())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }

            if (UtilNumber.isEmpty(importData.getWaterElectricityCost())) {
                importData.setWaterElectricityCost(BigDecimal.ZERO);
            }

            importData.setRoomCode(StringUtils.join(importData.getBuildingNo(), Const.HYPHEN, importData.getRoomNo()));

            roomCodeList.add(importData.getRoomCode());
        }

        List<BizRoom> dbRoomList = bizRoomDataWrap.list(new LambdaQueryWrapper<BizRoom>()
                .in(BizRoom::getRoomCode, roomCodeList)
        );

        // 校验数据是否重复
        if(UtilCollection.isNotEmpty(dbRoomList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_EXIST, dbRoomList.stream().map(BizRoom::getRoomCode).collect(Collectors.joining(Const.COMMA)));
        }

        // 保存数据
        bizRoomDataWrap.saveBatch(UtilCollection.toList(importDataList, BizRoom.class));
    }

    /**
     * 校验保存数据
     */
    public void cheeckSaveData(BizContext ctx) {
        BizRoomDTO po = ctx.getPoContextData();

        if (UtilString.isNullOrEmpty(po.getBuildingNo())
                || UtilString.isNullOrEmpty(po.getRoomNo())
                || UtilNumber.isEmpty(po.getBedCount())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 房间编号=楼栋号-房间号
        po.setRoomCode(StringUtils.join(po.getBuildingNo(), Const.HYPHEN, po.getRoomNo()));

        // 查看是否有重复房间编码的数据
        int dataCount = (int)bizRoomDataWrap.count(new LambdaQueryWrapper<BizRoom>()
                .eq(BizRoom::getRoomCode, po.getRoomCode())
                .ne(UtilNumber.isNotEmpty(po.getId()), BizRoom::getId, po.getId())
        );

        if(dataCount > Const.ZERO){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_EXIST, po.getRoomCode());
        }

        if (UtilNumber.isEmpty(po.getId())) {
            return;
        }

        BizRoom bizRoom = bizRoomDataWrap.getById(po.getId());

        if (bizRoom == null || UtilNumber.isEmpty(bizRoom.getCurrentRoomUsageHeadId())) {
            return;
        }

        // 只有空闲的房间才允许编辑“床位数量”、“水电费用”信息
        if (!bizRoom.getBedCount().equals(po.getBedCount()) || !bizRoom.getWaterElectricityCost().equals(po.getWaterElectricityCost())) {
            throw new WmsException(EnumReturnMsg.NO_MSG_1878);
        }
    }

    /**
     * 保存数据
     */
    public void saveData(BizContext ctx) {
        // 入参上下文
        BizRoomDTO po = ctx.getPoContextData();

        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();

        // 设置修改人信息
        po.setModifyUserId(user.getId());
        po.setModifyTime(new Date());

        // id为空则是新增数据，设置创建人信息
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
            po.setCreateTime(new Date());
        }

        // 保存数据
        bizRoomDataWrap.saveOrUpdateDto(po);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizRoomSearchPO po = ctx.getPoContextData();

        // 分页处理
        IPage<BizRoomPageVO> page = po.isPaging() ? po.getPageObj(BizRoomPageVO.class) : null;

        // 分页列表查询
        List<BizRoomPageVO> resultList = bizRoomDataWrap.getPageVo(page, new WmsLambdaQueryWrapper<BizRoomSearchPO>()
                .eq(true, BizRoomSearchPO::getIsDelete, BizRoom.class, EnumRealYn.FALSE.getIntValue())
                .eq(UtilString.isNotNullOrEmpty(po.getBuildingNo()), BizRoomSearchPO::getBuildingNo, BizRoom.class, po.getBuildingNo())
                .like(UtilString.isNotNullOrEmpty(po.getRoomCode()), BizRoomSearchPO::getRoomCode, BizRoom.class, po.getRoomCode())
                .eq(UtilNumber.isNotEmpty(po.getBedCount()), BizRoomSearchPO::getBedCount, BizRoom.class, po.getBedCount())
                .in(UtilCollection.isNotEmpty(po.getBuildingNoList()), BizRoomSearchPO::getBuildingNo, BizRoom.class, po.getBuildingNoList())
                .in(UtilCollection.isNotEmpty(po.getBedCountList()), BizRoomSearchPO::getBedCount, BizRoom.class, po.getBedCountList())
                .eq(EnumRealYn.TRUE.getIntValue().equals(po.getIsIdleRoom()), BizRoomSearchPO::getCurrentRoomUsageHeadId, BizRoom.class, Const.ZERO)
        );

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(resultList, po.isPaging() ? page.getTotal() : resultList.size()));

        // 各房间当前使用信息抬头id列表
        List<Long> currentRoomUsageHeadIdList = resultList.stream().filter(obj -> UtilNumber.isNotEmpty(obj.getCurrentRoomUsageHeadId())).map(BizRoomPageVO::getCurrentRoomUsageHeadId).collect(Collectors.toList());

        if(UtilCollection.isEmpty(currentRoomUsageHeadIdList)){
            return;
        }

        // 未完成报修记录列表
        List<BizRoomReceiptRepairHead> bizRoomReceiptRepairHeadList = bizRoomReceiptRepairHeadDataWrap.list(new LambdaQueryWrapper<BizRoomReceiptRepairHead>()
                .in(BizRoomReceiptRepairHead::getRoomUsageHeadId, currentRoomUsageHeadIdList)
                .ne(BizRoomReceiptRepairHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
        );

        // 存在报修的房间使用记录抬头id列表
        List<Long> existRepairRoomUsageHeadIdList = bizRoomReceiptRepairHeadList.stream().map(BizRoomReceiptRepairHead::getRoomUsageHeadId).collect(Collectors.toList());

        // 设置房间是否存在未完成的报修记录
        resultList.stream().forEach(obj -> obj.setIsExistRepair(existRepairRoomUsageHeadIdList.contains(obj.getCurrentRoomUsageHeadId()) ? EnumRealYn.TRUE.getIntValue() : EnumRealYn.FALSE.getIntValue()));
    }

    /**
     * 获取详情数据
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long roomId = ctx.getIdContextData();

        BizRoomDTO bizRoomDTO = bizRoomDataWrap.getDtoById(BizRoomDTO.class, roomId);

        ctx.setVoContextData(bizRoomDTO);
    }

    /**
     * 删除数据
     */
    public void delData(BizContext ctx) {
        // 入参上下文
        Long roomId = ctx.getIdContextData();

        if (UtilNumber.isEmpty(roomId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        BizRoom bizRoom = bizRoomDataWrap.getById(roomId);

        if (bizRoom == null) {
            return;
        }

        if (UtilNumber.isNotEmpty(bizRoom.getCurrentRoomUsageHeadId())) {
            throw new WmsException(EnumReturnMsg.NO_MSG_1878);
        }

        // 删除数据
        bizRoomDataWrap.removeById(roomId);
    }

    /**
     * 导出excel
     */
    public void exportExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(FileService.genFileCode(Const.XLSX));
        bizCommonFile.setFileName(FileService.genFileName("房间信息导出"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());

        // 生成前序数据时不要异步处理，否则后续流程可能无法按预期查询到前面异步插入的数据
        fileService.saveFileByUser(bizCommonFile);

        // 上下文入参
        BizRoomSearchPO po = ctx.getPoContextData();

        // 导出数据查询
        List<BizRoomExportVO> resultList = bizRoomDataWrap.selectExportData(new WmsLambdaQueryWrapper<BizRoomSearchPO>()
                .eq(true, BizRoomSearchPO::getIsDelete, BizRoom.class, EnumRealYn.FALSE.getIntValue())
                .eq(UtilString.isNotNullOrEmpty(po.getBuildingNo()), BizRoomSearchPO::getBuildingNo, BizRoom.class, po.getBuildingNo())
                .like(UtilString.isNotNullOrEmpty(po.getRoomCode()), BizRoomSearchPO::getRoomCode, BizRoom.class, po.getRoomCode())
                .eq(UtilNumber.isNotEmpty(po.getBedCount()), BizRoomSearchPO::getBedCount, BizRoom.class, po.getBedCount())
        );

        for(BizRoomExportVO bizRoomExportVO : resultList){

            // 入住人数
            bizRoomExportVO.setCheckInCount(UtilNumber.isEmpty(bizRoomExportVO.getCheckInCount()) ? Const.ZERO : bizRoomExportVO.getCheckInCount());

            // 入住人数展示值【入住人数/床位数】
            bizRoomExportVO.setCheckInCountStr(StringUtils.join(bizRoomExportVO.getCheckInCount(), Const.LEFT_SLASH, bizRoomExportVO.getBedCount()));

            // 房间状态
            if(UtilNumber.isEmpty(bizRoomExportVO.getCheckInCount())){
                // 空房
                bizRoomExportVO.setRoomStatus(EnumRoomStatus.VACANT_ROOM.getValue());
            } else if(bizRoomExportVO.getCheckInCount() < bizRoomExportVO.getBedCount()){
                // 部分入住
                bizRoomExportVO.setRoomStatus(EnumRoomStatus.PARTIAL_CHECK_IN.getValue());
            } else {
                // 满房
                bizRoomExportVO.setRoomStatus(EnumRoomStatus.FULL_HOUSE.getValue());
            }

            // 申请人
            bizRoomExportVO.setApplicantUser(Arrays.asList(bizRoomExportVO.getApplicantUserCode(),bizRoomExportVO.getApplicantUserName()).stream().filter(obj -> UtilString.isNotNullOrEmpty(obj)).collect(Collectors.joining(Const.HYPHEN)));
        }

        // 导出数据国际化
        exportDataI18nComponent.dataI18n(resultList);

        UtilExcel.writeExcel(BizRoomExportVO.class, resultList, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }
}