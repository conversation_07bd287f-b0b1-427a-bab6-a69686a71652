package com.inossem.wms.bizdomain.returns.dao;

import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnHeadDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnItem;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;

import java.util.List;

/**
 * <p>
 * 退库单行项目表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
public interface BizReceiptReturnItemMapper extends WmsBaseMapper<BizReceiptReturnItem> {
    /**
     * 成套设备查询预留单号
     */
    List<BizReceiptReturnItemDTO> getReserveInfoListUnitized(BizReceiptReturnHeadDTO headDTO);
}
