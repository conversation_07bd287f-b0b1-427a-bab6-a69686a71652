package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedDistributeInspectService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectSearchUserListPO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectHeadVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成套设备分配质检 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-26
 */
@RestController
public class UnitizedDistributeInspectController {

    @Autowired
    protected UnitizedDistributeInspectService unitizedDistributeInspectService;

    /**
     * 获取参检人列表
     *
     * @param po 查询用户列表入参
     * @return 用户列表
     */
    @ApiOperation(value = "获取参检人列表", tags = {"验收管理-分配质检"})
    @PostMapping(value = "/unitized/inspect/distribute-inspect/user-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<SysUser>> getUserList(@RequestBody BizReceiptInspectSearchUserListPO po, BizContext ctx) {
        unitizedDistributeInspectService.getUserList(ctx);
        PageObjectVO<SysUser> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 获取参检人反显name
     *
     * @param po 查询用户列表入参
     * @return 用户name描述
     */
    @ApiOperation(value = "获取参检人反显name", tags = {"验收管理-分配质检"})
    @PostMapping(value = "/unitized/inspect/distribute-inspect/user-name-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> getUserNameList(@RequestBody BizReceiptInspectSearchUserListPO po, BizContext ctx) {
        unitizedDistributeInspectService.getUserNameList(ctx);
        String vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询分配质检单列表-分页
     *
     * @param po 分配质检单分页查询入参
     * @return 分配质检单列表
     */
    @ApiOperation(value = "查询分配质检单列表-分页", tags = {"验收管理-分配质检"})
    @PostMapping(value = "/unitized/inspect/distribute-inspect/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInspectHeadVO>> getPage(@RequestBody BizReceiptInspectSearchPO po, BizContext ctx) {
        unitizedDistributeInspectService.getPage(ctx);
        PageObjectVO<BizReceiptInspectHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询分配质检单详情
     *
     * @param id 分配质检单抬头表主键
     * @return 分配质检单详情
     */
    @ApiOperation(value = "查询分配质检单详情", tags = {"验收管理-分配质检"})
    @GetMapping(value = "/unitized/inspect/distribute-inspect/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInspectHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedDistributeInspectService.getInfo(ctx);
        BizResultVO<BizReceiptInspectHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 分配质检-保存
     *
     * @param po 保存分配质检表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "分配质检-保存", tags = {"验收管理-分配质检"})
    @PostMapping(value = "/unitized/inspect/distribute-inspect/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInspectHeadDTO po, BizContext ctx) {
        unitizedDistributeInspectService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 分配质检-提交
     *
     * @param po 提交分配质检表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "分配质检-提交", tags = {"验收管理-分配质检"})
    @PostMapping(value = "/unitized/inspect/distribute-inspect/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInspectHeadDTO po, BizContext ctx) {
        unitizedDistributeInspectService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_SUBMIT_SUCCESS, receiptCode);
    }
    /**
     * 分配质检-同步DTS
     */
    @ApiOperation(value = "分配质检-保存", tags = {"验收管理-分配质检"})
    @PostMapping(value = "/unitized/inspect/distribute-inspect/syncDTS", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> syncDTS(@RequestBody BizReceiptInspectHeadDTO po, BizContext ctx) {
        unitizedDistributeInspectService.syncDTS(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 分配质检-阅知
     *
     * @param po 提交分配质检表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "分配质检-阅知", tags = {"验收管理-分配质检"})
    @PostMapping(value = "/unitized/inspect/distribute-inspect/review", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> review (@RequestBody BizReceiptInspectHeadDTO po, BizContext ctx) {
        unitizedDistributeInspectService.review(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_SUBMIT_SUCCESS, receiptCode);
    }
}
