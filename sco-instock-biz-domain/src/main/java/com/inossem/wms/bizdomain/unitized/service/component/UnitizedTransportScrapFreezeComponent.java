package com.inossem.wms.bizdomain.unitized.service.component;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.erp.service.biz.ErpWbsService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.transport.service.component.TransportComponent;
import com.inossem.wms.bizdomain.transport.service.component.TransportMessageQueueComponent;
import com.inossem.wms.bizdomain.transport.service.component.TransportMoveTypeComponent;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportRuleDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportRule;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizTransportScrapFreezeImportPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 报废冻结
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class UnitizedTransportScrapFreezeComponent {

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected ReceiptRelationService receiptRelationService;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    protected ReceiptAttachmentService bizReceiptAttachmentService;
    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    protected BizBatchInfoDataWrap bizBatchInfoDataWrap;
    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;
    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;
    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;
    @Autowired
    private BizReceiptTransportRuleDataWrap bizReceiptTransportRuleDataWrap;
    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private TransportComponent transportComponent;
    @Autowired
    private BatchInfoService batchInfoService;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private TransportMoveTypeComponent transportMoveTypeComponent;
    @Autowired
    private ErpWbsService erpWbsService;
    @Autowired
    private ErpPostingService erpPostingService;
    @Autowired
    private LabelReceiptRelService labelReceiptRelService;
    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private TransportMessageQueueComponent transportMessageQueueComponent;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private DicMaterialDataWrap dicMaterialDataWrap;
    @Autowired
    private BizLabelDataDataWrap bizLabelDataDataWrap;

    /**
     * 移动类型列表
     */
    public void getMoveTypeList(BizContext ctx) {
        // 移动类型下拉列表
        List<DicMoveType> moveTypeList = dictionaryService.getMoveTypeListCacheByReceiptType(EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue());
        // 返回对象
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(moveTypeList));
    }

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        /* ********* 页面初始化返回空的行项目、扩展项对象、按钮组【保存和提交】 ******** */
        BizResultVO<BizReceiptTransportHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptTransportHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSubmit(true).setButtonSave(true));
        /* ********* 页面初始化数据放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启审批
     */
    public void setExtendWf(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setWfRequired(false);
    }

    /**
     * 开启附件
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setAttachmentRequired(true);
    }

    /**
     * 开启操作日志
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setOperationLogRequired(true);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id            head主表id
     * @param receiptStatus 状态
     */
    private void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportItem::getItemStatus, receiptStatus)
                .eq(BizReceiptTransportItem::getHeadId, id);
        bizReceiptTransportItemDataWrap.update(wrapper);
    }

    /**
     * 界面创建导入
     *
     * @param ctx
     */
    public void getStockImport(BizContext ctx) {
        /**
         * 1、构造查询
         * 2、查询后构造批量返回
         */
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        String moveTypeId = ctx.getContextData("moveTypeId");
        List<BizReceiptAssembleRuleDTO> resultList = new ArrayList<>();
        //获取EXCEL数据
        List<BizTransportScrapFreezeImportPO> list = null;
        try {
            list = (List<BizTransportScrapFreezeImportPO>) UtilExcel.readExcelData(file.getInputStream(), BizTransportScrapFreezeImportPO.class);
            //判断EXCEL中主键重复的值
            Map<String, List<BizTransportScrapFreezeImportPO>> checkMap = list.stream().collect(Collectors.groupingBy(item -> item.getFtyCode() + "-" + item.getLocationCode() + "-" + item.getMatCode() + "-" + item.getBatchId()));
            for (String key : checkMap.keySet()) {
                List<BizTransportScrapFreezeImportPO> checkList = checkMap.get(key);
                if (checkList.size() > 1) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE, key);
                }
            }
            list.forEach(
                    importPo -> {
                        BizReceiptTransportHeadSearchPO searchPO = new  BizReceiptTransportHeadSearchPO();
                        searchPO.setMoveTypeId(Long.valueOf(moveTypeId));
                        Long ftyIdCacheByCode = dictionaryService.getFtyIdCacheByCode(importPo.getFtyCode());
                        Long locationIdCacheByCode = dictionaryService.getLocationIdCacheByCode(importPo.getFtyCode(), importPo.getLocationCode());
                        DicStockLocationDTO location = dictionaryService.getLocationCacheById(locationIdCacheByCode);
                        searchPO.setFtyId(ftyIdCacheByCode);
                        searchPO.setLocationId(location.getId());
                        searchPO.setBatchId(importPo.getBatchId());
                        searchPO.setBinId(dictionaryService.getBinIdCacheByCode(location.getWhCode(), importPo.getTypeCode(), importPo.getBinCode()));
                        searchPO.setMatCode(importPo.getMatCode());
                        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, searchPO);
                        this.getStock(ctx);
                        // 返回对象
                        SingleResultVO resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
                        BizReceiptAssembleRuleDTO assembleRuleDTO = (BizReceiptAssembleRuleDTO) resultVO.getResult();
                        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
                        if (UtilCollection.isEmpty(assembleDTOList)) {
                            throw new WmsException("物料:" + importPo.getMatCode() + ",工厂:" + importPo.getFtyCode() + ",库存地点:" + importPo.getLocationCode() + ",批次:" + importPo.getBatchId() + "没有库存");
                        } else if(assembleDTOList.size() > 1) {
                            throw new WmsException("物料:"  + importPo.getMatCode() + ",工厂:" + importPo.getFtyCode() + ",库存地点:" + importPo.getLocationCode() + ",批次:" + importPo.getBatchId() + "结果超过一条");
                        }
                        resultList.add(assembleRuleDTO);
                    }
            );

            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(resultList));

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    /**
     * 查询库存
     */
    public void getStock(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 根据单据类型获取特性
        BizReceiptAssembleRuleDTO assembleRuleDTO = new BizReceiptAssembleRuleDTO();
        po.setMatIdSet(new HashSet<>());
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
                return;
            }
            // 查询实际对应子物料ids
            Set<Long> childMatIdSet = this.getCtMatIdListByParentMatIdList(Collections.singleton(matId));
            if (UtilCollection.isEmpty(childMatIdSet)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
                return;
            }
            po.getMatIdSet().addAll(childMatIdSet);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getMatName())) {
            WmsLambdaQueryWrapper<DicMaterial> dicMaterialQueryWrapper = new WmsQueryWrapper<DicMaterial>().lambda()
                    .like(DicMaterial::getMatName, po.getMatName())
                    .select(DicMaterial::getId);
            List<DicMaterial> matList = dicMaterialDataWrap.list(dicMaterialQueryWrapper);
            if (UtilCollection.isEmpty(matList)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
                return;
            }
            Set<Long> matIdSet = matList.stream().map(DicMaterial::getId).collect(Collectors.toSet());
            // 查询实际对应子物料ids
            Set<Long> childMatIdSet = this.getCtMatIdListByParentMatIdList(matIdSet);
            if (UtilCollection.isEmpty(childMatIdSet)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
                return;
            }
            po.getMatIdSet().addAll(childMatIdSet);
            po.setMatName(null);
        }
        if (UtilCollection.isNotEmpty(po.getChildMatCodeList())) {
            // 物料编码不是空时, 根据编码查询id
            Collection<Long> matIdList = dictionaryService.getMatIdListByMatCodeList(po.getChildMatCodeList());
            if (UtilCollection.isEmpty(matIdList)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
                return;
            }
            po.getMatIdSet().addAll(matIdList);
            po.setChildMatCodeList(null);
        }
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        Integer receiptType = EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue();
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        BizReceiptAssembleRuleSearchPO rulePo = UtilBean.deepCopyNewInstance(po, BizReceiptAssembleRuleSearchPO.class);
        rulePo.setSpecStock(dicMoveType.getSpecStock());
        rulePo.setSpecStockCode(StringUtils.isEmpty(po.getSpecStockCode()) ? null : po.getSpecStockCode());
        rulePo.setStockStatus(stockStatus);
        rulePo.setFtyId(UtilNumber.isNotEmpty(po.getFtyId()) ? po.getFtyId() : dictionaryService.getFtyIdCacheByCode(EnumFactory.J047.getFtyCode()));
        rulePo.setMMatName(po.getChildMatName());
        rulePo.setIsApplyFlag(EnumRealYn.TRUE.getIntValue());
        rulePo.setIsUnitized(EnumRealYn.TRUE.getBoolValue());
        rulePo.setNow(EnumRealYn.TRUE.getIntValue().equals(po.getIsExpired()) ? UtilDate.getNow() : null);
        assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValueBySdw4Unitized(null, null, receiptType, rulePo);

        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            for (BizReceiptAssembleDTO dto : assembleDTOList) {
                dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            }
            if (UtilCollection.isNotEmpty(po.getItemDTOList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptTransportItemDTO itemDTO : po.getItemDTOList()) {
                    for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                        for (BizReceiptAssembleDTO dto : assembleDTOList) {
                            if (dto.getSpecCode().equals(assembleDTO.getSpecCode())
                                    && dto.getSpecValue().equals(assembleDTO.getSpecValue())) {
                                dto.setStockQty(dto.getStockQty().subtract(assembleDTO.getQty()));
                            }
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            String tableFieldNameCellId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                    && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                    && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(po.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    stockBinDTO.setCellId(0L);
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                            // 存储单元
                            stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
//                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
//                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
//                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
//                            && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue())) ) {
                        stockBinDTOList.add(stockBinDTO);
//                    }
                }
                // 批量查询标签列表
                if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                    List<BizLabelData> labelDataVOList = labelDataService.getList(stockBinDTOList);
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                        for (int i = 0; i < codeList.size(); i++) {
                            if (codeList.get(i).equals(tableFieldNameBatchId)) {
                                // 批次
                                batchInfoId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                                // 存储类型
                                typeId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                                // 存储单元
                                cellId = 0L;
                            } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                                // 仓位
                                binId = Long.parseLong(valueList.get(i));
                            }
                        }
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        for (BizLabelData labelData : labelDataVOList) {
                            if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                    && labelData.getMatId().equals(assembleDTO.getMatId())
                                    && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                    && labelData.getBatchId().equals(batchInfoId)
                                    && labelData.getTypeId().equals(typeId)
                                    && labelData.getCellId().equals(cellId)
                                    && labelData.getBinId().equals(binId)
                                    && EnumRealYn.FALSE.getIntValue().equals(labelData.getIsFreeze())) {
                                // 唯一键相同时,匹配
                                BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                                labelReceiptRelDTO.setLabelId(labelData.getId());
                                labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                                labelReceiptRelDTO.setQty(labelData.getQty());
                                labelReceiptRelDTO.setStockQty(labelData.getQty());
                                labelDataList.add(labelReceiptRelDTO);
                            }
                        }
                        assembleDTO.setLabelDataList(labelDataList);
                    }
                }
            }
        }
        // 返回对象
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
    }

    /**
     * 根据头部物料id，查询实际对应子物料ids
     *
     * @param matIdList 物料id集合
     * @return 对应子物料ids
     */
    private Set<Long> getCtMatIdListByParentMatIdList(Collection<Long> matIdList) {
        // 根据头部物料id，查询实际对应子物料ids
        QueryWrapper<DicMaterial> dicMaterialQueryWrapper = new QueryWrapper<>();
        dicMaterialQueryWrapper.lambda()
                .in(DicMaterial::getParentMatId, matIdList)
                .select(DicMaterial::getId);
        List<DicMaterial> sonMatList = dicMaterialDataWrap.list(dicMaterialQueryWrapper);
        return sonMatList.stream().map(DicMaterial::getId).collect(Collectors.toSet());
    }

    /**
     * 列表 - 分页
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        List<Long> locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        po.setLocationIdList(locationIds);

        // 分页处理
        IPage<BizReceiptTransportHeadDTO> page = new Page<>(po.getPageIndex(), po.getPageSize());
        bizReceiptTransportHeadDataWrap.selectTransportPageVoListByPo(page, po);
        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 查询单据详情,包含按钮组和扩展功能
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO bizReceiptTransportHeadDTO =
                UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(bizReceiptTransportHeadDTO);

        transportComponent.setBatchInfo(bizReceiptTransportHeadDTO.getItemDTOList());

        for (BizReceiptTransportItemDTO itemDTO : bizReceiptTransportHeadDTO.getItemDTOList()) {
            // 保存的时候按照 labelId 拆分 assembleDTO, 这里按照 specValue 合并 assembleDTO
            List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
            Map<String, List<BizReceiptAssembleDTO>> specValueAssembleMap = itemDTO.getAssembleDTOList().stream().collect(Collectors.groupingBy(BizReceiptAssembleDTO::getSpecValue));
            for (String specValue : specValueAssembleMap.keySet()) {
                List<BizReceiptAssembleDTO> value = specValueAssembleMap.get(specValue);
                BizReceiptAssembleDTO assembleDTO = UtilBean.deepCopy(value.get(0));
                // 重新合并 labelDataList
                assembleDTO.setLabelDataList(new ArrayList<>());
                for (BizReceiptAssembleDTO dto : value) {
                    // 这里的 dto 里 有且只有一个 labelReceiptRel
                    BizLabelReceiptRelDTO labelReceiptRel = dto.getLabelDataList().get(0);
                    labelReceiptRel.setStockQty(labelReceiptRel.getQty());
                    labelReceiptRel.setQty(dto.getQty());
                    labelReceiptRel.setIsChecked(dto.getIsChecked());
                    assembleDTO.getLabelDataList().add(labelReceiptRel);
                }
                // 配货数量反显
                assembleDTO.setQty(BigDecimal.ZERO);
                for (BizReceiptAssembleDTO dto : value) {
                    if (UtilNumber.isNotEmpty(dto.getIsChecked())) { // 只计算已选中的数量
                        assembleDTO.setQty(assembleDTO.getQty().add(dto.getQty()));
                    }
                }
                assembleDTOList.add(assembleDTO);
            }
            itemDTO.setAssembleDTOList(assembleDTOList);

            // 库存数量反显
            itemDTO.setStockQty(BigDecimal.ZERO);
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                itemDTO.setStockQty(itemDTO.getStockQty().add(assembleDTO.getStockQty()));
            }
        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(bizReceiptTransportHeadDTO);
        // 设置单据流
        // ExtendVO extendVO = this.setInfoExtendRelation(bizReceiptTransportHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(bizReceiptTransportHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 根据状态设置按钮组
     */
    private ButtonVO setButton(BizReceiptTransportHeadDTO headDTO) {
        Integer receiptStatus = headDTO.getReceiptStatus();
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonSave(true);
            buttonVO.setButtonDelete(true);
            buttonVO.setButtonSubmit(true);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue())) {
            // 未同步状态,按钮过账
            buttonVO.setButtonPost(true);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            // 已完成状态,按钮打印
            buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 保存时校验数据
     */
    public void checkSaveData(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (headDTO == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 根据移动类型查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        if (rule == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        headDTO.setOutputStockStatus(rule.getOutputStockStatus());
        headDTO.setInputStockStatus(rule.getInputStockStatus());
        headDTO.setInputSpecStock(rule.getInputSpecStock());
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            headDTO.setSpecStock(itemDTO.getSpecStock());
            itemDTO.setOutputSpecStockCode(itemDTO.getSpecStockCode());
            // 根据规则对目标信息校验/赋值
            itemDTO.setInputFtyId(this.checkRule(rule.getInputFtyId(), itemDTO.getOutputFtyId(), itemDTO.getInputFtyId()));
            itemDTO.setInputLocationId(
                    this.checkRule(rule.getInputLocationId(), itemDTO.getOutputLocationId(), itemDTO.getInputLocationId()));
            itemDTO.setInputMatId(this.checkRule(rule.getInputMatId(), itemDTO.getOutputMatId(), itemDTO.getInputMatId()));
            itemDTO.setInputUnitId(
                    this.checkRule(rule.getInputUnitId(), itemDTO.getOutputUnitId(), itemDTO.getInputUnitId()));
            itemDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(), itemDTO.getOutputSpecStockCode(),
                    itemDTO.getInputSpecStockCode()));
        }
    }

    /**
     * 提交时校验数据
     */
    public void checkSubmitData(BizContext ctx) {
        // 保存时校验数据
        this.checkSaveData(ctx);
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue   规则值
     * @param outputValue 发出值
     * @param inputValue  接收值
     * @return 接收值
     */
    private Long checkRule(Long ruleValue, Long outputValue, Long inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if (0 == ruleValue) {
            // 空值
            inputValue = null;
        } else if (1 == ruleValue) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if (2 == ruleValue) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue   规则值
     * @param outputValue 发出值
     * @param inputValue  接收值
     * @return 接收值
     */
    private String checkRule(String ruleValue, String outputValue, String inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if ("0".equals(ruleValue)) {
            // 空值
            inputValue = null;
        } else if ("1".equals(ruleValue)) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if ("2".equals(ruleValue)) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        this.save(ctx);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // head处理
        headDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue());
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        String code = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            headDTO.setModifyUserId(ctx.getCurrentUser().getId());
            // 根据id更新
            bizReceiptTransportHeadDataWrap.updateDtoById(headDTO);
            // 特征物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(headDTO.getId());
            // item物理删除
            bizReceiptTransportItemDataWrap.deleteByHeadId(headDTO.getId());
            // 设置上下文单据日志 - 修改
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_TRANSPORT_FREEZE.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptTransportHeadDataWrap.saveDto(headDTO);
            // 设置上下文单据日志 - 创建
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
        // item处理
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        }
        bizReceiptTransportItemDataWrap.saveBatchDto(itemDTOList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    // 这里按照 labelId 拆分 assembleDTO, 查询详情页的时候会按照 specValue 合并 assembleDTO
                    for (BizLabelReceiptRelDTO bizLabelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizReceiptAssembleDTO assembleDTO = UtilBean.deepCopy(bizReceiptAssembleDTO);
                        assembleDTO.setReceiptType(headDTO.getReceiptType());
                        assembleDTO.setReceiptHeadId(headDTO.getId());
                        assembleDTO.setReceiptItemId(itemDto.getId());
                        assembleDTO.setId(null);
                        assembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                                ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                                : bizReceiptAssembleDTO.getSpecType());
                        assembleDTO.setLabelDataList(Collections.singletonList(bizLabelReceiptRelDTO));
                        assembleDTO.setLabelId(bizLabelReceiptRelDTO.getLabelId());
                        Integer isChecked = bizLabelReceiptRelDTO.getIsChecked();
                        assembleDTO.setIsChecked(UtilNumber.isEmpty(isChecked) ? 0 : 1);
                        assembleDTO.setQty(bizLabelReceiptRelDTO.getQty());
                        assembleDTOList.add(assembleDTO);
                    }
                }
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            for (BizReceiptAssembleDTO assembleDTO : assembleDTOList) {
                for (BizLabelReceiptRelDTO labelReceiptRelDTO : assembleDTO.getLabelDataList()) {
                    BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                    labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                    labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                    labelReceiptRel.setReceiptHeadId(headDTO.getId());
                    labelReceiptRel.setReceiptItemId(assembleDTO.getReceiptItemId());
                    labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                    bizLabelReceiptRelList.add(labelReceiptRel);
                    labelRelIdList.add(labelReceiptRelDTO.getId());
                }
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptTransportItemDTO item : itemDTOList) {
            if (UtilNumber.isNotEmpty(headDTO.getPreReceiptType()) && UtilNumber.isNotEmpty(headDTO.getPreReceiptHeadId())) {
                BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
                bizCommonReceiptRelation.setReceiptType(EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue());
                bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
                bizCommonReceiptRelation.setReceiptItemId(item.getId());
                bizCommonReceiptRelation.setPreReceiptType(headDTO.getPreReceiptType());
                bizCommonReceiptRelation.setPreReceiptHeadId(headDTO.getPreReceiptHeadId());
                bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
                list.add(bizCommonReceiptRelation);
            }
        }
        if (UtilCollection.isNotEmpty(list)) {
            receiptRelationService.multiSaveReceiptTree(list);
        }
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizReceiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 逻辑删除附件
        bizReceiptAttachmentService.deleteBizReceiptAttachment(headId, EnumReceiptType.STOCK_FREEZE_SCRAP.getValue());
    }

    /**
     * 状态变更-未同步
     */
    public void updateStatusUnsync(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
    }

    /**
     * 状态变更-已完成
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        if (headDTO.getMoveTypeCode().equals(Const.MOVE_TYPE_315)) {
            // 315 在途转非限制, 状态变更-已过账, 生成上架
            this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            BizContext ctx_headDTO = new BizContext();
            ctx_headDTO.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
            ctx_headDTO.setCurrentUser(ctx.getCurrentUser());
            transportMessageQueueComponent.generateInputTaskReq(ctx_headDTO);
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 行项目状态变更-已驳回
     */
    public void updateStatusRejected(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        List<Long> itemIds = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            if (UtilCollection.isNotEmpty(itemDto.getBinDTOList())) {
                List<Long> binIds = new ArrayList<>();
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                    binIds.add(bizReceiptAssembleDTO.getId());
                }
                bizReceiptAssembleDataWrap.removeByIds(binIds);
            }
            itemIds.add(itemDto.getId());
        }
        bizReceiptTransportItemDataWrap.removeByIds(itemIds);
        bizReceiptTransportHeadDataWrap.removeById(headId);
    }

    /**
     * 生成ins凭证 - 转储接收发出一次过账
     */
    public void generateInsDocToPost(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO =
                transportMoveTypeComponent.generateInsDocToPost(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }


    /**
     * 过账前校验和数量计算
     */
    public void checkAndComputeForModifyStock(BizContext ctx) {
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
    }

    /**
     * 调用sap接口过账
     */
    public void post(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        CurrentUser currentUser = ctx.getCurrentUser();
        // 过账前修改单据状态未同步
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        List<ErpPostingObject> list = new ArrayList<>(headDTO.getItemDTOList().size());
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            // 已有物料凭证的行项目不在交互sap
            if (UtilString.isNullOrEmpty(itemDTO.getMatDocCode())) {
                ErpPostingObject erpPosting = new ErpPostingObject();
                erpPosting.setFtyCode(itemDTO.getOutputFtyCode());
                erpPosting.setLocationCode(itemDTO.getOutputLocationCode());
                erpPosting.setMatCode(itemDTO.getOutputMatCode());
                erpPosting.setInputFtyCode(itemDTO.getInputFtyCode());
                erpPosting.setInputLocationCode(itemDTO.getInputLocationCode());
                erpPosting.setInputMatCode(itemDTO.getInputMatCode());
                erpPosting.setQty(itemDTO.getQty());
                erpPosting.setMoveTypeCode(headDTO.getMoveTypeCode());
                erpPosting.setWmsHeadId(headDTO.getId());
                erpPosting.setWmsItemId(itemDTO.getId());
                list.add(erpPosting);
            }
        }
        if (UtilCollection.isNotEmpty(list)) {
            /* ******** 设置入库单账期 ******** */
            this.setInPostDate(list, currentUser);
            // 交互sap过账
            List<ErpPostingObject> listVO = erpPostingService.posting1(list);
            List<BizReceiptTransportItem> itemList = new ArrayList<>(listVO.size());
            for (ErpPostingObject erpPostingObject : listVO) {
                BizReceiptTransportItem item = new BizReceiptTransportItem();
                item.setId(erpPostingObject.getWmsItemId());
                String matDocCode = erpPostingObject.getMatDocCode();
                String matDocRid = erpPostingObject.getMatDocRid();
                String matDocYear = erpPostingObject.getMatDocYear();
                Date docDate = erpPostingObject.getDocDate();
                Date postingDate = erpPostingObject.getPostingDate();
                item.setMatDocCode(matDocCode);
                item.setMatDocRid(matDocRid);
                item.setMatDocYear(matDocYear);
                item.setDocDate(docDate);
                item.setPostingDate(postingDate);
                itemList.add(item);
                // 回写物料凭证
                for (BizReceiptTransportItemDTO dto : headDTO.getItemDTOList()) {
                    if (dto.getId().equals(erpPostingObject.getWmsItemId())) {
                        dto.setMatDocCode(matDocCode);
                        dto.setMatDocRid(matDocRid);
                        dto.setMatDocYear(matDocYear);
                    }
                }
                for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                    if (insDocBatch.getPreReceiptItemId().equals(erpPostingObject.getWmsItemId())) {
                        insDocBatch.setMatDocCode(matDocCode);
                        insDocBatch.setMatDocRid(matDocRid);
                        insDocBatch.setMatDocYear(matDocYear);
                        insDocBatch.setDocDate(docDate);
                        insDocBatch.setPostingDate(postingDate);
                    }
                }
                for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                    if (insDocBin.getPreReceiptItemId().equals(erpPostingObject.getWmsItemId())) {
                        insDocBin.setMatDocCode(matDocCode);
                        insDocBin.setMatDocRid(matDocRid);
                        insDocBin.setMatDocYear(matDocYear);
                    }
                }
            }
            // 保存物料凭证相关信息
            bizReceiptTransportItemDataWrap.updateBatchById(itemList);
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemList 未同步sap入库单行项目
     * @param user     当前用户
     */
    private void setInPostDate(List<ErpPostingObject> itemList, CurrentUser user) {
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        for (ErpPostingObject item : itemList) {
            item.setDocDate(UtilDate.getNow());
            item.setPostingDate(postingDate);
        }
    }

    /**
     * 修改库存
     */
    public void modifyStock(BizContext ctx) {
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.modifyStock(insMoveTypeDTO);
    }

    /**
     * 修改标签
     */
    public void modifyLabel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 同时模式,在页面选择标签
        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
        labelReceiptRel.setReceiptHeadId(headDTO.getId());
        List<BizLabelReceiptRel> relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
        if (UtilCollection.isEmpty(relList)) {
            // 未查询到对应的标签信息则不修改
            return;
        }
        List<BizLabelData> labelDataList = new ArrayList<>();
        for (BizLabelReceiptRel receiptRel : relList) {
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                for (BizReceiptTransportBinDTO binDTO : itemDTO.getBinDTOList()) {
                    if (receiptRel.getReceiptBinId().equals(binDTO.getId())
                            || receiptRel.getPreReceiptBinId().equals(binDTO.getId())) {
                        // id一致
                        BizLabelData labelData = new BizLabelData();
                        labelData.setId(receiptRel.getLabelId());
                        if (itemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
                            // 冲销回发出
                            labelData.setFtyId(itemDTO.getOutputFtyId());
                            labelData.setLocationId(itemDTO.getOutputLocationId());
                            labelData.setWhId(itemDTO.getOutputWhId());
                            labelData.setMatId(itemDTO.getOutputMatId());
                            labelData.setBatchId(binDTO.getOutputBatchId());
                            labelData.setTypeId(binDTO.getOutputTypeId());
                            labelData.setBinId(binDTO.getOutputBinId());
                            labelData.setCellId(binDTO.getOutputCellId());
                        } else {
                            // 批次信息更新为接收
                            labelData.setFtyId(itemDTO.getInputFtyId());
                            labelData.setLocationId(itemDTO.getInputLocationId());
                            labelData.setWhId(itemDTO.getInputWhId());
                            labelData.setMatId(itemDTO.getInputMatId());
                            labelData.setBatchId(binDTO.getInputBatchId());
                            if (!UtilNumber.isEmpty(binDTO.getInputTypeId())) {
                                labelData.setTypeId(binDTO.getInputTypeId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputBinId())) {
                                labelData.setBinId(binDTO.getInputBinId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputCellId())) {
                                labelData.setCellId(binDTO.getInputCellId());
                            }
                        }
                        labelDataList.add(labelData);
                    }
                }
            }
        }
        labelDataService.multiUpdateLabelData(labelDataList);
    }

    /**
     * 保存操作日志
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 单据
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 根据移动类型保存bin
     */
    public void saveOutputBinByMoveType(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 取表名,字段名
        String tableName = StockBin.class.getAnnotation(TableName.class).value();
        String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
        String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
        String tableFieldNameBinId = tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
        List<BizLabelReceiptRel> labelReceiptRelList = new ArrayList<>();
        // bin处理
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        int bid = 1;
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                if (UtilNumber.isEmpty(assembleDTO.getIsChecked())) {
                    continue;
                }
                BizReceiptTransportBinDTO transportBinDTO = new BizReceiptTransportBinDTO();
                if (UtilString.isNotNullOrEmpty(itemDTO.getInputSpecStockCode())) {
                    transportBinDTO.setInputSpecStockCode(itemDTO.getInputSpecStockCode());
                }
                transportBinDTO.setId(null);
                transportBinDTO.setHeadId(headDTO.getId());
                transportBinDTO.setItemId(itemDTO.getId());
                transportBinDTO.setBid(Integer.toString(bid++));
                List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                for (int i = 0; i < codeList.size(); i++) {
                    if (codeList.get(i).equals(tableFieldNameBatchId)) {
                        // 批次
                        Long batchId = Long.parseLong(valueList.get(i));
                        transportBinDTO.setOutputBatchId(batchId);
                    } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                        // 存储类型
                        transportBinDTO.setOutputTypeId(Long.parseLong(valueList.get(i)));
                        transportBinDTO.setInputTypeId(itemDTO.getInputTypeId());
                    } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                        // 存储单元
                        transportBinDTO.setOutputCellId(Long.parseLong(valueList.get(i)));
                        transportBinDTO.setInputCellId(0L);

                    } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                        // 仓位
                        transportBinDTO.setOutputBinId(Long.parseLong(valueList.get(i)));
                        transportBinDTO.setInputBinId(itemDTO.getInputBinId());
                    }
                }
                transportBinDTO.setQty(assembleDTO.getQty());
                transportBinDTOList.add(transportBinDTO);
                if (UtilCollection.isNotEmpty(assembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelData : assembleDTO.getLabelDataList()) {
                        // 拼装标签关联信息
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelData.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(transportBinDTO.getHeadId());
                        labelReceiptRel.setReceiptItemId(transportBinDTO.getItemId());
                        labelReceiptRel.setReceiptBinId(Long.parseLong(transportBinDTO.getBid()));
                        labelReceiptRelList.add(labelReceiptRel);
                    }
                }
            }
        }
        dataFillService.fillAttr(transportBinDTOList);
        // 根据移动类型查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        // 特殊库存设置
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 特殊库存设置
            binDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(),
                    binDTO.getOutputBatchInfoDTO().getSpecStockCode(), binDTO.getInputSpecStockCode()));
        }
        // 生成接收方批次信息
        this.multiInsertBatchInfo(transportBinDTOList);
        // bin表保存
        bizReceiptTransportBinDataWrap.saveBatchDto(transportBinDTOList);
        // 回填 binId
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            for (BizLabelReceiptRel receiptRel : labelReceiptRelList) {
                if (receiptRel.getReceiptHeadId().equals(binDTO.getHeadId())
                        && receiptRel.getReceiptItemId().equals(binDTO.getItemId())
                        && receiptRel.getReceiptBinId().toString().equals(binDTO.getBid())) {
                    receiptRel.setReceiptBinId(binDTO.getId());
                    BizLabelReceiptRelDTO relDTO = UtilBean.newInstance(receiptRel,BizLabelReceiptRelDTO.class);

                    if(binDTO.getLabelDataList()==null){
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        labelDataList.add(relDTO);
                        binDTO.setLabelDataList(labelDataList);
                    }else{
                        binDTO.getLabelDataList().add(relDTO);
                    }
                }
            }
        }
        // 标签关联信息保存
        labelReceiptRelService.saveBatch(labelReceiptRelList);
        // 重新填充headDto
        dataFillService.fillAttr(headDTO);
    }

    /**
     * 处理标签关联关系 - 拆分标签
     *
     * @param ctx 转储单抬头传输对象
     */
    public void handleLabel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        // 将所有标签操作数量大于0的按标签ID进行聚合，聚合时判断如果操作数量总和大于标签数量则异常
        // 逐个遍历行项目，如果某个行项目的存在标签，并判断这个标签的所有操作数量小于标签数量则拆分标签，拆分标签包含生成新标签数据和更新已有标签数量(扣除新标签数量)
        // 生成新的标签关联单据数据，并复制源标签关联单据到新标签上
        AtomicInteger rfidRid = new AtomicInteger(1);
        List<BizLabelData> labelNewList = new ArrayList<>();
        List<BizLabelData> labelUpdateList = new ArrayList<>();
        List<BizLabelReceiptRel> relNewList = new ArrayList<>();
        List<Long> lableIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                for (BizLabelReceiptRelDTO relDTO : assembleDTO.getLabelDataList()) {
                    lableIdList.add(relDTO.getLabelId());
                }
            }
        }
        Integer receiptType = headDTO.getReceiptType();
        Long headId = headDTO.getId();
        Long userId = headDTO.getCreateUserId();
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                List<BizLabelReceiptRelDTO> relDTOList = assembleDTO.getLabelDataList();
                List<BizLabelReceiptRelDTO> opLabelList = relDTOList.stream().filter(e -> e.getLabelId() != null).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(opLabelList)) {
                    continue;
                }
                List<BizLabelData> lableList = bizLabelDataDataWrap.listByIds(lableIdList);
                for (BizLabelReceiptRelDTO bizLabelDataDTO : opLabelList) {
                    if (UtilNumber.isEmpty(bizLabelDataDTO.getIsChecked())) {
                        continue;
                    }
                    BigDecimal operationQty = bizLabelDataDTO.getQty(); // 操作数量 从标签上取
                    if (operationQty.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    BigDecimal labelQty = bizLabelDataDTO.getStockQty(); // 标签数量 从标签上取
                    BizLabelData rel = null;
                    BizLabelData sourceLabelData = lableList.stream().filter(e -> e.getId().compareTo(bizLabelDataDTO.getLabelId()) == 0).findFirst().orElse(null);
                    if (UtilObject.isNull(sourceLabelData)) {
                        continue;
                    }
                    if (operationQty.compareTo(labelQty) < 0) { // 操作数量小于标签数量
                        rel = UtilBean.newInstance(sourceLabelData, BizLabelData.class);
                        rel.setQty(operationQty);
                        rel.setId(null);
                        rel.setLabelCode(null);
                        rel.setSourceLabelId(sourceLabelData.getId());
                        rel.setRid(rfidRid.getAndIncrement());
                        rel.setSnCode(null);
                        rel.setWhId(itemDTO.getInputWhId());
                        rel.setTypeId(itemDTO.getInputTypeId());
                        rel.setBinId(itemDTO.getInputBinId());
                        rel.setCellId(0L);
                        rel.setMatId(itemDTO.getInputMatId());
                        rel.setBatchId(assembleDTO.getBatchInfo().getId());
                        rel.setIsFreeze(EnumRealYn.TRUE.getIntValue());
                        labelNewList.add(rel);
                        sourceLabelData.setQty(sourceLabelData.getQty().subtract(operationQty));
                        labelUpdateList.add(sourceLabelData);
                    } else if (operationQty.compareTo(labelQty) == 0) { // 操作数量等于标签数量
                        rel = sourceLabelData;
                        sourceLabelData.setWhId(itemDTO.getInputWhId());
                        sourceLabelData.setTypeId(itemDTO.getInputTypeId());
                        sourceLabelData.setBinId(itemDTO.getInputBinId());
                        sourceLabelData.setMatId(itemDTO.getInputMatId());
                        sourceLabelData.setBatchId(assembleDTO.getBatchInfo().getId());
                        sourceLabelData.setIsFreeze(EnumRealYn.TRUE.getIntValue());
                        labelUpdateList.add(sourceLabelData);
                    } else {
                        throw new WmsException(EnumReturnMsg.LABEL_QTY_NOT_ENOUGH);
                    }
                    BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                    labelReceiptRel.setLabelId(rel.getId());
                    labelReceiptRel.setReceiptType(receiptType);
                    labelReceiptRel.setReceiptHeadId(headId);
                    labelReceiptRel.setReceiptItemId(itemDTO.getId());
                    labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                    labelReceiptRel.setPreReceiptHeadId(null);
                    labelReceiptRel.setPreReceiptItemId(null);
                    labelReceiptRel.setPreReceiptBinId(null);
                    labelReceiptRel.setRid(rel.getRid());
                    labelReceiptRel.setStatus(null);
                    labelReceiptRel.setCreateUserId(userId);
                    relNewList.add(labelReceiptRel);
                }
            }
        }
        Map<Integer, BizLabelDataDTO> labelDataDTOMap = new HashMap<>();
        if (UtilCollection.isNotEmpty(labelNewList)) {
            // 插入批次标签
            List<String> labelCodeList = bizCommonService.getNextNValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue(), labelNewList.size());
            int idx = 0;
            for (BizLabelData labelData : labelNewList) {
                String code = labelCodeList.get(idx);
                labelData.setLabelCode(code);
                labelData.setSnCode(code);
                idx++;
            }
            List<BizLabelDataDTO> labelDataDTOList = UtilCollection.toList(labelNewList, BizLabelDataDTO.class);
            labelDataService.saveBatchDto(labelDataDTOList);
            // 复制关联关系
            labelReceiptRelService.copyRel(labelNewList);
            labelDataDTOList.forEach(e -> {
                labelDataDTOMap.put(e.getRid(), e);
            });
        }
        if (!CollectionUtils.isEmpty(labelUpdateList)) {
            labelDataService.multiUpdateLabelData(labelUpdateList);
        }
        if (UtilCollection.isNotEmpty(relNewList)) {
            // 设置新标签id
            for (BizLabelReceiptRel rel : relNewList) {
                BizLabelDataDTO labelDataDTO = labelDataDTOMap.get(rel.getRid());
                if (labelDataDTO != null) {
                    rel.setLabelId(labelDataDTO.getId());
                }
            }
            labelReceiptRelService.saveBatch(relNewList);
        }
        // 重新填充headDto
        dataFillService.fillAttr(headDTO);
    }

    /**
     * 生成接收方批次信息
     */
    public void multiInsertBatchInfo(List<BizReceiptTransportBinDTO> transportBinDTOList) {
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        List<BizBatchInfoDTO> batchInfoDtoList = new ArrayList<>();
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 唯一键处理
            String uk = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-" + binDTO.getInputSpecStock() + "-"
                    + binDTO.getInputSpecStockCode() + "-" + binDTO.getOutputBatchInfoDTO().getBatchCode();
            if (batchMap.containsKey(uk)) {
                // 已有批次
                BizBatchInfoDTO batchInfoDTO = batchMap.get(uk);
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
            } else {
                BizBatchInfoDTO batchInfoDTO =
                        UtilBean.newInstance(binDTO.getOutputBatchInfoDTO(), BizBatchInfoDTO.class);
                // 判断批次唯一索引是否变更:工厂/物料/特殊库存类型/代码变更
                String outputKey = binDTO.getOutputFtyId() + "-" + binDTO.getOutputMatId() + "-"
                        + binDTO.getOutputSpecStock() + "-" + binDTO.getOutputSpecStockCode();
                String inputKey = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-"
                        + binDTO.getInputSpecStock() + "-" + binDTO.getInputSpecStockCode();
                if (!outputKey.equals(inputKey)) {
                    batchInfoDTO.setPreBatchId(binDTO.getOutputBatchId());
                    batchInfoDTO.setPreFtyId(binDTO.getOutputFtyId());
                    batchInfoDTO.setPreMatId(binDTO.getOutputMatId());
                    batchInfoDTO.setFtyId(binDTO.getInputFtyId());
                    batchInfoDTO.setMatId(binDTO.getInputMatId());
                    // 特殊库存类型变更
                    batchInfoDTO.setSpecStock(binDTO.getOutputBatchInfoDTO().getSpecStock());
                    // 特殊库存代码变更
                    batchInfoDTO.setCreateTime(new Date());
                    batchInfoDTO.setModifyTime(new Date());
                    batchInfoDTO.setSpecStockCode(binDTO.getOutputBatchInfoDTO().getSpecStockCode());
                    batchInfoDTO.setSpecStockName(binDTO.getOutputBatchInfoDTO().getSpecStockName());
                    batchInfoDTO.setId(null);
                    batchInfoDtoList.add(batchInfoDTO);
                }
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
                batchMap.put(uk, batchInfoDTO);
            }
        }
        if (UtilCollection.isNotEmpty(batchInfoDtoList)) {
            // 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDtoList);
        }
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 回填接收批次id
            binDTO.setInputBatchId(binDTO.getInputBatchInfoDTO().getId());
        }
        // TODO: 2021/4/28 批次特性转移
    }

    /**
     * 根据headId查询出库单列表
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptTransportHeadDTO getItemListById(Long headId) {
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 获取过账移动类型并校验
     *
     * @param ctx 上下文
     */
    public void generateInsMoveTypeAndCheck(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypeDTO postingInsMoveTypeDTO;
        try {
            postingInsMoveTypeDTO = transportMoveTypeComponent.generateFreezeInsDocToPostByBin(headDTO, ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeDTO);
        // 校验
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, postingInsMoveTypeDTO);
        transportComponent.checkAndComputeForModifyStock(ctx);

    }

    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHeadDTO headDTO = this.getItemListById(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }

    /**
     * 填充打印数据
     * @param ctx
     */
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptTransportHeadDTO headDTO = (BizReceiptTransportHeadDTO) po.getHeadDTO();
        // 创建打印实体对象
        List<LabelReceiptInputBox> labelReceiptInputBoxes = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptTransportBinDTO binDTO : itemDTO.getBinDTOList()) {
                BizBatchInfoDTO batchInfoDTO = binDTO.getInputBatchInfoDTO();
                // 查询标签表
                List<BizLabelData> labelDataList = labelDataService.findById(binDTO.getLabelDataList().stream().map(BizLabelReceiptRelDTO::getLabelId).collect(Collectors.toList()));
                Map<Long, BizLabelData> labelDataMap = labelDataList.stream().collect(Collectors.toMap(BizLabelData::getId, label -> label));
                for (BizLabelReceiptRelDTO relDTO : binDTO.getLabelDataList()) {
                    BizLabelData labelData = labelDataMap.get(relDTO.getLabelId());
                    if (UtilObject.isNotNull(labelData)) {
                        LabelReceiptInputBox labelReceiptInputBox = new LabelReceiptInputBox();
                        labelReceiptInputBox.setLabelId(labelData.getId());
                        labelReceiptInputBox.setTagType(labelData.getLabelType());
                        labelReceiptInputBox.setIsSingle(batchInfoDTO.getIsSingle());
                        labelReceiptInputBox.setRfidCode(labelData.getLabelCode());
                        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
                        labelReceiptInputBox.setBatchId(batchInfoDTO.getId());
                        labelReceiptInputBox.setBatchCode(batchInfoDTO.getBatchCode());
                        labelReceiptInputBox.setCorpName(batchInfoDTO.getCorpName());
                        labelReceiptInputBox.setCreateTime(labelData.getCreateTime());
                        labelReceiptInputBox.setFtyId(labelData.getFtyId());
                        labelReceiptInputBox.setMatId(itemDTO.getInputMatId());
                        labelReceiptInputBox.setMatCode(itemDTO.getInputMatCode());
                        labelReceiptInputBox.setMatName(itemDTO.getInputMatName());
                        labelReceiptInputBox.setMaintenanceDate(batchInfoDTO.getMaintenanceDate());
                        labelReceiptInputBox.setProductDate(batchInfoDTO.getProductionDate());
                        labelReceiptInputBox.setLifetimeDate(batchInfoDTO.getLifetimeDate());
                        labelReceiptInputBox.setPackageTypeI18n(EnumPackageType.getDescByValue(batchInfoDTO.getPackageType()));
                        labelReceiptInputBoxes.add(labelReceiptInputBox);
                    }
                }
            }
        }
        // 填充打印信息
        printInfo.setLabelBoxList(labelReceiptInputBoxes);
        if (UtilCollection.isNotEmpty(labelReceiptInputBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, labelReceiptInputBoxes);
            // 发送MQ打印请求 - 这里共用入库标签打印
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 更新批次信息NCR字段
     *
     * @param ctx 入参上下文
     */
    public void updateBatchInfoNcr(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 批次信息处理
        if (UtilString.isNotNullOrEmpty(headDTO.getNcr())) {
            List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                assembleDTOList.addAll(itemDTO.getAssembleDTOList());
            }
            List<Long> batchIdList = assembleDTOList.stream()
                    .map(BizReceiptAssembleDTO::getBatchInfo)
                    .map(BizBatchInfoDTO::getId)
                    .distinct().collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(batchIdList)) {
                bizBatchInfoDataWrap.update(new UpdateWrapper<BizBatchInfo>().lambda()
                        .set(BizBatchInfo::getNcr, headDTO.getNcr())
                        .in(BizBatchInfo::getId, batchIdList));
            }
        }
    }

}
