package com.inossem.wms.bizdomain.input.controller;

import com.inossem.wms.bizdomain.input.service.biz.TempStoreInputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.tag.LabelMapVo;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 暂存入库单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */

@RestController
@Api(tags = "入库管理-暂存物项入库")
public class TempStoreInputController {

    @Autowired
    private TempStoreInputService tempStoreInputService;

    /**
     * 暂存物项入库-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "暂存物项入库-初始化", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        tempStoreInputService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 暂存物项入库-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 暂存物项入库单分页
     */
    @ApiOperation(value = "暂存物项入库-分页", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po,
        BizContext ctx) {
        tempStoreInputService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 暂存物项入库-详情
     *
     * @param id 暂存物项入库主键
     * @param ctx 入参上下文 {"id":"暂存物项入库主键"}
     */
    @ApiOperation(value = "暂存物项入库-详情", tags = {"入库管理-暂存物项入库"})
    @GetMapping(value = "/input/temp-store-inputs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        tempStoreInputService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 暂存物项入库-前续单据
     *
     * @param po 查询条件
     * @return 借用出库单
     */
    @ApiOperation(value = "暂存物项入库-前续单据", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/pre-receipt", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getPreReceipt(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        tempStoreInputService.getPreReceipt(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 暂存物项入库-保存
     *
     * @param po 保存暂存物项入库表单参数
     * @param ctx 入参上下文 {"po":"保存暂存物项入库表单参数"}
     */
    @ApiOperation(value = "暂存物项入库-保存", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        tempStoreInputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 暂存物项入库-提交
     *
     * @param po 提交暂存物项入库表单参数
     * @param ctx 入参上下文 {"po":"提交暂存物项入库表单参数"}
     */
    @ApiOperation(value = "暂存物项入库-提交", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        tempStoreInputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TEMP_STORE_INPUT_SUBMIT_SUCCESS, code);
    }
    /**
     * 暂存物项入库-处理
     *
     * @param po 提交暂存物项入库表单参数
     * @param ctx 入参上下文 {"po":"提交暂存物项入库表单参数"}
     */
    @ApiOperation(value = "暂存物项入库-处理", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/deal", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> deal(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        tempStoreInputService.deal(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TEMP_STORE_INPUT_SUBMIT_SUCCESS, code);
    }

    /**
     * 验收入库-过账
     *
     * @param po 保存验收入库表单参数
     * @param ctx 入参上下文 {"po":"保存验收入库表单参数"}
     */
    @ApiOperation(value = "暂存物项入库-过账", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> post(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        tempStoreInputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 暂存物项入库-冲销
     *
     * @param po 暂存物项入库冲销表单参数
     * @param ctx 入参上下文 {"po":"暂存物项入库冲销表单参数"}
     */
    @ApiOperation(value = "暂存物项入库-冲销", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInputWriteOffPO po, BizContext ctx) {
        tempStoreInputService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 暂存物项入库单-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "暂存物项入库单-删除", tags = {"入库管理-暂存物项入库"})
    @DeleteMapping("/input/temp-store-inputs/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        tempStoreInputService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_DELETE_SUCCESS, po.getReceiptCode());
    }


    /**
     * 查询标签类型下拉
     *
     * @return 标签类型下拉框
     *
     */
    @ApiOperation(value = "查询标签类型下拉", tags = {"入库管理-暂存物项入库"})
    @GetMapping(path = "/input/temp-store-inputs/tag-type/down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<LabelMapVo>> getDown() {
        return BaseResult.success(tempStoreInputService.getDown());
    }



    /**
     * 暂存物项入库单-物料标签打印
     * @auth zhibo
     * @param po 打印入参
     * @param ctx 请求上下文
     * @return 暂存物项入库单
     */
    @ApiOperation(value = "暂存物项入库-物料标签打印", tags = {"入库管理-暂存物项入库"})
    @PostMapping(value = "/input/temp-store-inputs/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptInputHeadDTO> po, BizContext ctx) {
        tempStoreInputService.boxApplyLabelPrint(ctx);
        List<LabelReceiptInputBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

}