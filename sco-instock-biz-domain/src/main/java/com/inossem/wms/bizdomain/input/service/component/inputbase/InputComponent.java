package com.inossem.wms.bizdomain.input.service.component.inputbase;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.http.HttpRequest;
import com.alibaba.excel.util.DateUtils;
import com.inossem.wms.common.constant.log.SysLogConst;
import com.inossem.wms.common.model.log.entity.LogSapLog;
import com.inossem.wms.common.util.UtilGzip;
import com.inossem.wms.system.log.service.datawrap.LogSapLogDataWrap;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.StockLocationService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.component.ContractComponent;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputBinDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.biz.LoadTaskService;
import com.inossem.wms.bizdomain.task.service.component.TaskComponent;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskItemDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumImageBizType;
import com.inossem.wms.common.enums.EnumMoveType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSendType;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.rel.entity.SysUserRoleRel;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemQtyDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputBin;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadCallbackVO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskItem;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqHead;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.dto.PrintItemDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.sap.posting.HXPostingHeader;
import com.inossem.wms.common.model.sap.posting.HXPostingItem;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypePostTaskDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilBigDecimal;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 入库基础组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class InputComponent {

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private BizReceiptInputBinDataWrap bizReceiptInputBinDataWrap;

    @Autowired
    protected BatchInfoService bizBatchInfoService;

    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected LoadTaskService loadTaskService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private StockLocationService stockLocationService;

    @Autowired
    private MaterialService materialService;

    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    private TaskComponent taskComponent;

    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private BizBatchInfoDataWrap bizBatchInfoDataWrap;


    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    protected BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;


    @Autowired
    private HXSapIntegerfaceService hxInterfaceService;

    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap receiptDeliveryNoticeItemDataWrap;
    @Autowired
    protected LogSapLogDataWrap logSapLogDataWrap;

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"入库","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"入库及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // // 判断业务流程是否需要审批
        // boolean wfByReceiptType =
        // UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.STOCK_INPUT_INSPECT.getValue());
        // if (UtilObject.isNull(resultVO.getHead())) {
        // // 初始化 - 设置审批开启/关闭
        // resultVO.getExtend().setWfRequired(wfByReceiptType);
        // } else {
        // // 详情页 - 设置审批开启/关闭
        // resultVO.getExtend().setWfRequired(wfByReceiptType);
        // if (wfByReceiptType) {
        // // TODO: 2021/4/8 设置单据详情审批数据
        // }
        // }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 设置批次图片信息
     *
     * @in ctx 入参 {@link BizResultVO ("head":"入库单详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"入库单详情及批次图片")}
     */
    public void setBatchImg(BizContext ctx) {
        // 入参上下文
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead()) && UtilCollection.isEmpty(resultVO.getHead().getItemList())) {
            return;
        }
        Set<Long> batchIdSet = resultVO.getHead().getItemList().stream().map(BizReceiptInputItemDTO::getBatchId)
            .collect(Collectors.toSet());
        // 获取批次图片
        Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
        if (imgMap.isEmpty()) {
            return;
        }
        // 赋值批次图片
        resultVO.getHead().getItemList().forEach(itemDTO -> {
            if (UtilNumber.isNotEmpty(itemDTO.getBatchId())
                && UtilCollection.isNotEmpty(imgMap.get(itemDTO.getBatchId()))) {
                itemDTO.setBizBatchImgDTOList(imgMap.get(itemDTO.getBatchId()));
            }
        });
        // 设置入库单详情批次图片到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 保存入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的入库单}
     * @out ctx 出参 {"stockInputCode" : "入库单号"},{@link BizReceiptInputHeadDTO : "已保存的入库单}
     */
    public void saveInput(BizContext ctx) {
        // 入参上下文 - 要保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String stockInputCode = po.getReceiptCode();
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        po.setCreateUserId(UtilNumber.isNotEmpty(po.getCreateUserId()) ? po.getCreateUserId() : user.getId());
        po.setModifyUserId(user.getId());
        po.setCreateTime(null);
        po.setModifyTime(UtilDate.getNow());
        // 暂存入库设置申请人部门和前序单据
        String tempStorePreReceiptCode = null; 
        if (UtilNumber.isNotEmpty(po.getId()) && po.getReceiptType().equals(EnumReceiptType.STOCK_TEMP_MAT_INPUT.getValue())){
            List<MetaDataDeptOfficePO> deptList = sysUserDeptOfficeRelDataWrap.getUserDept(po.getCreateUserId());
            if (!CollectionUtils.isEmpty(deptList)) {
                MetaDataDeptOfficePO defaultDept = deptList.get(0);
                po.setDeptCode(defaultDept.getDeptCode());
                po.setDeptName(defaultDept.getDeptName());
                po.setDeptOfficeCode(defaultDept.getDeptOfficeCode());
                po.setDeptOfficeName( defaultDept.getDeptOfficeName());
            }
            BizReceiptInputItemDTO inputItemDTO = po.getItemList().get(0);
            BizReceiptApplyHead applyHead  = bizReceiptApplyHeadDataWrap.getOne(new LambdaQueryWrapper<BizReceiptApplyHead>().eq(BizReceiptApplyHead::getId, inputItemDTO.getPreReceiptHeadId()));
            if (applyHead != null){
                tempStorePreReceiptCode = applyHead.getReceiptCode();
            }
        }

        
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新入库单
            bizReceiptInputHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteInputItem(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            // ChangBaoLong根据业务修改：由于工器具各种业务的单号生成规则不一致，code会由MQ发送过来，不去改变
            if (UtilString.isNullOrEmpty(stockInputCode)) {
                //根据类型生成单据code
                if (EnumReceiptType.TOOL_MAINTAIN_INPUT.getValue().equals(po.getReceiptType())) {
                    stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TOOL_MAINTAIN_INPUT.getValue());
                }
                else if (EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue().equals(po.getReceiptType())) {
                    // 成套设备暂存入库 SKX-CWH5YYYY-APS-ZCXX
                    // 由于保存批次信息时会提前生成单号, 此处直接保存即可
                    // stockInputCode = "SK" + po.getUnit() + "-CWH5" + UtilDate.getYearMonth(UtilDate.getNow()) + "-APS-ZC" + bizCommonService.getNextSequence("unitized_temp_input_req");
                    stockInputCode = "SK" + po.getUnit() + "-CWH5" + DateUtil.thisYear() + "-APS-ZC" + bizCommonService.getNextSequence(EnumSequenceCode.SEQUENCE_UNITIZED_TEMP_INPUT_REQ.getValue());
                    // stockInputCode = po.getReceiptCode();
                } else if (EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(po.getReceiptType())) {
                    // 入库冲销生成单号
                    stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.INPUT_WRITE_OFF.getValue());
                } else if (EnumReceiptType.STOCK_RETURN_OLD_INPUT.getValue().equals(po.getReceiptType())) {
                    // 退旧入库生成单号
                    stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_MAT_REQ_RETURN_OLD_INPUT.getValue());
                } else if (EnumReceiptType.LEISURE_INPUT.getValue().equals(po.getReceiptType())) {
                    // 闲置入库生成单号
                    stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LEISURE_INPUT.getValue());
                } else if (EnumReceiptType.STOCK_INPUT_OTHER.getValue().equals(po.getReceiptType())) {
                    // 油品入库
                    stockInputCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_OTHER_INPUT.getValue());
                } else {
                    stockInputCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_INPUT.getValue());
                }
            }
            po.setReceiptCode(stockInputCode);
            bizReceiptInputHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存入库单head成功!单号{},主键{}", stockInputCode, po.getId());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        List<BizBatchInfoDTO> saveBatchInfoDtoList = new ArrayList<>();
        List<BizBatchInfoDTO> updateBatchInfoDtoList = new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(po.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
            itemDTO.setReceiptCode(po.getReceiptCode());
            itemDTO.setReceiptType(po.getReceiptType());
            BizBatchInfoDTO batchInfoDto = itemDTO.getBizBatchInfoDTO();
            if (UtilObject.isNotNull(batchInfoDto)) {
                batchInfoDto.setMatId(itemDTO.getMatId());
                batchInfoDto.setFtyId(itemDTO.getFtyId());
                batchInfoDto.setCreateUserId(user.getId());
                saveBatchInfoDtoList.add(batchInfoDto);
                // 验证是否为编辑
                if (UtilNumber.isNotEmpty(po.getId())) {
                    BizBatchInfoDTO updateBatchInfoDto = new BizBatchInfoDTO();
                    updateBatchInfoDto.setId(batchInfoDto.getId());
                    // 暂存入库 需要修改 单品/批次 0批次 1单品 暂存人、暂存部门、暂存科室 增加前序单据
                    if (itemDTO.getReceiptType().equals(EnumReceiptType.STOCK_TEMP_MAT_INPUT.getValue())
                            || itemDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue())){
                        if (UtilNumber.isNotEmpty(itemDTO.getTempStorePeriod())) {
                            updateBatchInfoDto.setTempStoreExpireDate(DateUtil.offsetMonth(UtilDate.getNow(), itemDTO.getTempStorePeriod()));
                        }
                        updateBatchInfoDto.setIsSingle(batchInfoDto.getIsSingle());
                        updateBatchInfoDto.setTagType(batchInfoDto.getTagType());
                        updateBatchInfoDto.setTempStoreUser(po.getCreateUserName());
                        updateBatchInfoDto.setTempStoreDeptCode(po.getDeptCode());
                        updateBatchInfoDto.setTempStoreDeptName(po.getDeptName());
                        updateBatchInfoDto.setTempStoreDeptOfficeCode(po.getDeptOfficeCode());
                        updateBatchInfoDto.setTempStoreDeptOfficeName(po.getDeptOfficeName());
                        updateBatchInfoDto.setTempStorePreReceiptCode(tempStorePreReceiptCode);
                    }
                    // 其它入库 则更新供应商和需求人
                    if (EnumReceiptType.STOCK_INPUT_OTHER.getValue().equals(itemDTO.getReceiptType())){
                        updateBatchInfoDto.setIsSingle(batchInfoDto.getIsSingle());
                        updateBatchInfoDto.setTagType(batchInfoDto.getTagType());
                        updateBatchInfoDto.setSupplierName(batchInfoDto.getSupplierName());
                        updateBatchInfoDto.setApplyUserName(batchInfoDto.getApplyUserName());
                    }
                    if (EnumReceiptType.STOCK_WASTE_MATERIALS_INPUT.getValue().equals(itemDTO.getReceiptType())){
                        updateBatchInfoDto.setIsSingle(batchInfoDto.getIsSingle());
                        updateBatchInfoDto.setTagType(batchInfoDto.getTagType());
                        updateBatchInfoDto.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_WASTER.getValue());
                        updateBatchInfoDto.setSpecStockCode(batchInfoDto.getSpecStockCode());
                    }
                    // 工器具维保结果维护 更新批次信息工具状态
                    if (EnumReceiptType.TOOL_MAINTAIN_INPUT.getValue().equals(po.getReceiptType())) {
                        updateBatchInfoDto.setToolStatus(itemDTO.getToolStatus());
                    }
                    // 工器具归还 清空批次信息工具使用人、使用地点、使用事由
                    if (EnumReceiptType.STOCK_INPUT_BORROW.getValue().equals(po.getReceiptType())) {
                        updateBatchInfoDto.setToolUserName(Const.STRING_EMPTY);
                        updateBatchInfoDto.setToolUsePlace(Const.STRING_EMPTY);
                        updateBatchInfoDto.setToolUseReason(Const.STRING_EMPTY);
                    }
                    updateBatchInfoDtoList.add(updateBatchInfoDto);
                }
            }
        }
        // 批量保存或更新批次信息中的供应商和需求人,单品/批次、暂存人、暂存部门、暂存科室 等信息
        if (UtilNumber.isNotEmpty(po.getId())) {
            bizBatchInfoService.multiUpdateBatchInfo(updateBatchInfoDtoList);
        }
        bizBatchInfoService.multiCheckUKSaveBatchInfo(saveBatchInfoDtoList);

        if(UtilCollection.isNotEmpty(saveBatchInfoDtoList)) {
            log.debug("批量保存批次信息成功!批次号{},操作人{}", saveBatchInfoDtoList.get(0).getBatchCode(), user.getUserName());
        }
        // 批量保存item
        po.getItemList().forEach(item -> {
            item.setId(null);
            if(UtilObject.isNotNull(item.getBizBatchInfoDTO())) {
                item.setBatchId(item.getBizBatchInfoDTO().getId());
            }
        });
        bizReceiptInputItemDataWrap.saveBatchDto(po.getItemList());
        log.debug("保存入库单item成功!单号{},headId{}", stockInputCode, po.getId());
        /* ********************** item处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(po);
        //更新到货通知 到货登记单据
        this.updateDeliveryNoticeReceipt(po);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, stockInputCode);
        // 返回保存的入库单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, po);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, po.getId());
    }

    /**
     * 保存批次图片
     *
     * @in ctx 入参 {@link BizReceiptInputItemDTO : "入库单批次图片"}
     */
    public void saveBizBatchImg(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        for (BizReceiptInputItemDTO itemDto : headDTO.getItemList()) {
            if (UtilCollection.isNotEmpty(itemDto.getBizBatchImgDTOList())) {
                itemDto.getBizBatchImgDTOList().forEach(imgDTO -> {
                    imgDTO.setId(null);
                    imgDTO.setMatId(itemDto.getMatId());
                    imgDTO.setFtyId(itemDto.getFtyId());
                    imgDTO.setBatchId(itemDto.getBizBatchInfoDTO().getId());
                    imgDTO.setImgBizType(EnumImageBizType.BATCH.getValue());
                    imgDTO.setReceiptType(0);
                    imgDTO.setReceiptHeadId(0L);
                    imgDTO.setReceiptItemId(0L);
                    imgDTO.setCreateUserId(ctx.getCurrentUser().getId());
                });
            } else {
                itemDto.setBizBatchImgDTOList(new ArrayList<>());
            }
        }
        // 批量保存入库单批次图片
        bizBatchImgService.multiSaveBizBatchImg(headDTO.getItemList().stream()
            .flatMap(item -> item.getBizBatchImgDTOList().stream()).collect(Collectors.toList()));
        log.debug("批量保存验收单批次图片成功!");
    }


    /**
     * 调用sap传输附件
     * @param ctx
     */
    public void attSynToSap(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(!headDTO.getReceiptType().equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())){
            return;
        }
        List<BizCommonReceiptRelationDTO>  relationList=receiptRelationService.queryRelationByHeadId(headDTO.getReceiptType(),headDTO.getId());
        BizCommonReceiptRelationDTO signInspect = relationList.stream()
                .filter(item -> item.getRootHeadId().equals(headDTO.getId())  && item.getReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue()) )
                .findFirst().orElse(null);
        JSONObject params = new JSONObject();
        params.put("callerReceiptType", headDTO.getReceiptType());
        params.put("callReceiptCode", headDTO.getReceiptCode());
        JSONArray itemArray=new JSONArray();
        params.put("T_ITEM",itemArray);
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String url = "%s%s/file/download/";
        String contextPath = request.getContextPath();
        url=String.format(url, UtilConst.getInstance().getBaseUrl(), contextPath);
        if(headDTO.getFileList()!=null){
            BizReceiptInputItemDTO firstItem=  headDTO.getItemList().get(0);
            AtomicInteger rid = new AtomicInteger(1);
            for(BizCommonReceiptAttachment file: headDTO.getFileList()){
                JSONObject fileObj =new JSONObject();
                String fileReallyPath=url+file.getFileId();
                if(fileReallyPath.length()>255){

                }
                fileObj.put("MBLNR",firstItem.getMatDocCode());//物料凭证编号
                fileObj.put("WMSTYP","02");//智能存储系统单据类型 01 质检会签单   02 入库单
                fileObj.put("WMSDOC",headDTO.getReceiptCode());//智能存储系统单号
                fileObj.put("ZHXM",rid.getAndIncrement());//附件流水号  同一个WMS单据下的附件，从1开始的流水号
                fileObj.put("EBELN",firstItem.getReferReceiptCode());//采购凭证号
                fileObj.put("FLNAME",file.getFileName());//文件名称
                fileObj.put("URLAD1",fileReallyPath);//部分值  附件URL地址 .如果255长度不够，请拆分后按照12345顺序填充
                fileObj.put("FLPATH","");//文件路径
                fileObj.put("NOTDEL","");//操作时不允许删除附件
                fileObj.put("ZPROFG","");//审核通过之前上载标示
                itemArray.add(fileObj);
            }
        }
        if (UtilObject.isNotNull (signInspect)) {
            Long signInspectId=signInspect.getReceiptHeadId(); //最近的一次质检会签单
            // 获取质检会签单详情
            BizReceiptInspectHeadDTO signInspectheadDTO = UtilBean.newInstance(bizReceiptInspectHeadDataWrap.getById(signInspectId), BizReceiptInspectHeadDTO.class);
            // 填充关联属性和父子属性
            dataFillService.fillAttr(signInspectheadDTO);
            BizReceiptInspectItemDTO firstItem=  signInspectheadDTO.getItemList().get(0);
            AtomicInteger rid = new AtomicInteger(1);
            for(BizCommonReceiptAttachment file: signInspectheadDTO.getFileList()){
                JSONObject fileObj =new JSONObject();
                String fileReallyPath=url+file.getFileId();
                if(fileReallyPath.length()>255){

                }
                fileObj.put("MBLNR",firstItem.getMatDocCode());//物料凭证编号
                fileObj.put("WMSTYP","01");//智能存储系统单据类型 01 质检会签单   02 入库单
                fileObj.put("WMSDOC",signInspectheadDTO.getReceiptCode());//智能存储系统单号
                fileObj.put("ZHXM",rid.getAndIncrement());//附件流水号  同一个WMS单据下的附件，从1开始的流水号
                fileObj.put("EBELN",firstItem.getReferReceiptCode());//采购凭证号
                fileObj.put("FLNAME",file.getFileName());//文件名称
                fileObj.put("URLAD1",fileReallyPath);//部分值  附件URL地址 .如果255长度不够，请拆分后按照12345顺序填充
                fileObj.put("FLPATH","");//文件路径
                fileObj.put("NOTDEL","");//操作时不允许删除附件
                fileObj.put("ZPROFG","");//审核通过之前上载标示
                itemArray.add(fileObj);
            }
        }
        if(itemArray!=null && itemArray.size()>0){
            CurrentUser user = ctx.getCurrentUser();
            /* ******** 调用sap ******** */
            ErpReturnObject returnObj = sapInterfaceService.attSyn( user,JSONArray.toJSONStringWithDateFormat(params, "yyyyMM",
                    SerializerFeature.WriteDateUseDateFormat));
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {

            }
            if (!Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                log.warn("同步附件{}调用SAP失败", headDTO.getReceiptCode());
            }
        }
    }

    /**
     * 保存单据附件
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存附件的入库单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存入库单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            headDTO.getReceiptType(), user.getId());
        log.debug("保存入库单附件成功!");
    }

    /**
     * 保存批次附件
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存附件的入库单"}
     */
    public void saveBatchAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();

        if (headDTO.getItemList() != null && headDTO.getItemList().get(0) != null) {
            // 前序单据是质检会签单据时，保存行项目附件作为每行物料的批次附件
            for (BizReceiptInputItemDTO itemDTO : headDTO.getItemList()) {
                receiptAttachmentService.saveBatchInfoAttachment(itemDTO.getFileList(), itemDTO.getMatId(), itemDTO.getBatchId(), user.getId());
            }
        }
        log.debug("保存入库单附件成功!");
    }


    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存操作日志的入库单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", user.getId());
    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的入库单
     */
    public void saveReceiptTree(BizReceiptInputHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptInputItemDTO item : headDTO.getItemList()) {
            if (UtilNumber.isNotEmpty(item.getPreReceiptType()) && UtilNumber.isNotEmpty(item.getPreReceiptHeadId())
                && UtilNumber.isNotEmpty(item.getPreReceiptItemId())) {
                BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
                dtoList.add(dto);
            }
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 更新到货通知 到货登记单据
     * @param headDTO
     */
    public void updateDeliveryNoticeReceipt(BizReceiptInputHeadDTO headDTO) {
        if (headDTO.getReceiptType().equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue())) {
            List<BizCommonReceiptRelationDTO>  relationList=receiptRelationService.queryRelationByHeadId(headDTO.getReceiptType(),headDTO.getId());
            for (BizReceiptInputItemDTO itemDto : headDTO.getItemList()) {
                BizCommonReceiptRelationDTO deliveryNotice = relationList.stream()
                        .filter(item -> item.getRootItemId().equals(itemDto.getId())  && item.getReceiptType().equals(EnumReceiptType.DELIVERY_NOTICE.getValue()) )
                        .findFirst().orElse(null);
                if (UtilObject.isNotNull (deliveryNotice)) {
                    itemDto.setDeliveryNoticeHeadId(deliveryNotice.getReceiptHeadId());
                    itemDto.setDeliveryNoticeItemId(deliveryNotice.getReceiptItemId());
                }
                BizCommonReceiptRelationDTO arrivalRegister = relationList.stream()
                        .filter(item -> item.getRootItemId().equals(itemDto.getId()) && item.getReceiptType().equals(EnumReceiptType.ARRIVAL_REGISTER.getValue()) )
                        .findFirst().orElse(null);
                if (UtilObject.isNotNull (arrivalRegister)) {
                    itemDto.setArrivalRegisterHeadId(arrivalRegister.getReceiptHeadId());
                    itemDto.setArrivalRegisterItemId(arrivalRegister.getReceiptItemId());
                }
            }
            if (UtilObject.isNotNull (relationList)) {
                bizReceiptInputItemDataWrap.saveOrUpdateBatchDto(headDTO.getItemList());
            }

        }
    }
    /**
     * 保存单据流
     *
     */
    public void saveReceiptTreeByCtx(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.saveReceiptTree(headDTO);
    }

    /**
     * 普通标签生成上架请求
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void generateLoadReq(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 生成上架请求前过滤非普通标签
        List<BizReceiptInputItemDTO> canTaskItemList = po.getItemList();
        if (UtilCollection.isNotEmpty(canTaskItemList)) {
            /* ******** 设置作业请求head ******** */
            BizReceiptTaskReqHeadDTO reqHeadDTO = UtilBean.newInstance(po, BizReceiptTaskReqHeadDTO.class);
            reqHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPLETED.getValue());
            reqHeadDTO.setReceiptType(EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue());
            reqHeadDTO.setId(null);
            if(!UtilString.hasText(reqHeadDTO.getDes())){
                reqHeadDTO.setDes(po.getDeliveryNoticeDescribe());
            }
            if(!UtilString.hasText(reqHeadDTO.getDes())){
                reqHeadDTO.setDes(po.getRemark());
            }
            reqHeadDTO.setCreateTime(UtilDate.getNow());
            reqHeadDTO.setModifyTime(UtilDate.getNow());
            /* ******** 设置作业请求item ******** */
            List<BizReceiptTaskReqItemDTO> repItemListDTO =
                UtilCollection.toList(canTaskItemList, BizReceiptTaskReqItemDTO.class);
            for (BizReceiptTaskReqItemDTO reqItemDTO : repItemListDTO) {
                reqItemDTO.setPreReceiptType(po.getReceiptType());
                reqItemDTO.setPreReceiptCode(po.getReceiptCode());
                reqItemDTO.setPreReceiptHeadId(po.getId());
                reqItemDTO.setPreReceiptItemId(reqItemDTO.getId());
                reqItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPLETED.getValue());
                reqItemDTO.setId(null);
                reqItemDTO.setHeadId(null);
                reqItemDTO.setCreateTime(UtilDate.getNow());
                reqItemDTO.setModifyTime(UtilDate.getNow());
            }
            reqHeadDTO.setItemList(repItemListDTO);
            // 设置上架请求数据到上下文
            BizContext ctxNew = new BizContext();
            ctxNew.setCurrentUser(ctx.getCurrentUser());
            ctxNew.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, new BizReceiptTaskReqSavePo().setStockTaskReqHeadInfo(reqHeadDTO));
            // 推送MQ异步生成上架请求
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GENERATE_LOAD_REQ_TAGS, ctxNew);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
            //loadTaskService.generateLoadReq(ctx);
        }
    }

    /**
     * 过账前补全数据(不交互sap时使用)
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void setPostData(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();

            for (BizReceiptInputItemDTO inputItemDTO : headDTO.getItemList()) {

                inputItemDTO.setPostingDate(headDTO.getPostingDate());
            };


        // 上下文返回参数- ins凭证
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * sap入库过账
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"},{@link StockInsMoveTypeDTO : "ins凭证"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "补全凭证【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间】"}
     */
    public void postInputToSap(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        // 未同步sap行项目
        List<BizReceiptInputItemDTO> itemListNotSync = headDTO.getItemList().stream()
            .filter(e -> UtilObject.isEmpty(e.getIsPost()) || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
            .collect(Collectors.toList());
        /* ******** 设置入库单账期 ******** */
        itemListNotSync.forEach(p -> p.setPostingDate(headDTO.getPostingDate()));
        this.setInPostDate(itemListNotSync, user);
        HXPostingReturn returnObj = new HXPostingReturn();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            // 过账SAP批次号处理
            itemListNotSync.forEach(p -> p.setBatchCode(p.getBizBatchInfoDTO().getBatchCode()));
            HXPostingHeader header = this.transferPosting(headDTO,itemListNotSync,false);
            returnObj = hxInterfaceService.posting(header,false);
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {

                    for (BizReceiptInputItemDTO inputItemDTO : itemListNotSync) {

                        String matDocCode = returnObj.getMatDocCode();
                        inputItemDTO.setMatDocCode(matDocCode);
                        String matDocRid = String.format("%04d", UtilObject.getIntOrZero(inputItemDTO.getRid()));
                        inputItemDTO.setMatDocRid(matDocRid);
                        inputItemDTO.setMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
                        inputItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());

                        Date postingDate = inputItemDTO.getPostingDate();
                        Long batchId = inputItemDTO.getBatchId();
                        if ((postingDate != null) && UtilNumber.isNotEmpty(batchId)) {
                            bizBatchInfoDataWrap.updateMatDoc(batchId, postingDate, matDocCode, matDocRid);
                        }
                        // 过账成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                                if (insDocBatch.getPreReceiptItemId().equals(inputItemDTO.getId())) {
                                    insDocBatch.setMatDocCode(inputItemDTO.getMatDocCode());
                                    insDocBatch.setMatDocRid(inputItemDTO.getMatDocRid());
                                    insDocBatch.setPostingDate(inputItemDTO.getPostingDate());
                                    insDocBatch.setDocDate(inputItemDTO.getDocDate());
                                    insDocBatch.setMatDocYear(inputItemDTO.getMatDocYear());
                                }
                            }
                            for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                                if (insDocBin.getPreReceiptItemId().equals(inputItemDTO.getId())) {
                                    insDocBin.setMatDocCode(inputItemDTO.getMatDocCode());
                                    insDocBin.setMatDocRid(inputItemDTO.getMatDocRid());
                                    insDocBin.setMatDocYear(inputItemDTO.getMatDocYear());
                                }
                            }
                        }
                    }
                    // 更新入库单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                    this.updateItem(itemListNotSync);

                // 更新入库单状态 - 已记账
                this.updateStatus(headDTO, itemListNotSync, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());

                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("入库单{}SAP过账失败", headDTO.getReceiptCode());
                // 防止已完成状态被覆盖
                if (!checkCompletedStatus(headDTO)) {
                    // 更新入库单head、item状态-未同步
                    this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                }
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync = headDTO.getItemList().stream().map(BizReceiptInputItemDTO::getMatDocCode)
                .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    public boolean checkCompletedStatus(BizReceiptInputHeadDTO headDTO) {
        return bizReceiptInputHeadDataWrap.getById(headDTO.getId()).getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    @Autowired
    private DicMaterialDataWrap dicMaterialDataWrap;

    /**
     * 刷新 物料主数据中的存储级别
     */
    public void updateMatInfo(List<BizReceiptInputItemDTO> itemList){

        if(UtilCollection.isNotEmpty(itemList)){
            List<DicMaterial> materialList = new ArrayList<>();
            Set<Long> matIdSet = new HashSet<>();
            for(BizReceiptInputItemDTO inputItemDTO:itemList){
                if(matIdSet.contains(inputItemDTO.getMatId())){
                    continue;
                }
                DicMaterial material = new DicMaterial();
                DicStockLocationDTO locationDTO = dictionaryService.getLocationCacheById(inputItemDTO.getLocationId());
                material.setId(inputItemDTO.getMatId());
                if(locationDTO!=null&&UtilString.hasText(locationDTO.getEprio())){
                    DicMaterialDTO materialDTO = dictionaryService.getMatCacheById(inputItemDTO.getMatId());
                    if(materialDTO!=null&&!UtilString.hasText(materialDTO.getEprio())){
                        // 物料主数据中为空时才更新
                        material.setEprio(locationDTO.getEprio());
                        matIdSet.add(inputItemDTO.getMatId());
                        materialList.add(material);
                    }

                }

            }
            if(UtilCollection.isNotEmpty(materialList)){
                dicMaterialDataWrap.updateBatchByIdOptimize(materialList, 100);
            }
        }

    }

    /**
     * ins入库过账
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"},{@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void postInputToIns(BizContext ctx) {
        // 入参上下文-ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 入参上下文-入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        try {
            // 修改库存
            stockCommonService.modifyStock(insMoveTypeDTO);
            // 更新入库单状态 - 已记账
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            // 单据日志 - 过账
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("入库单{}ins过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 失败时更新入库单及行项目为未同步
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 冲销前补全数据(不交互sap时使用)
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void setWriteOffData(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();

            for (BizReceiptInputItemDTO inputItemDTO : headDTO.getItemList()) {
                inputItemDTO.setWriteOffPostingDate(headDTO.getWriteOffPostingDate());
            };


        // 上下文返回参数- ins凭证
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * sap入库冲销
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"},{@link StockInsMoveTypeDTO : "ins凭证"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "补全凭证【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】"}
     */
    public void writeOffInputToSap(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();

        List<BizReceiptInputItemDTO> itemListNotSync = headDTO.getItemList();

        /* ******** 设置冲销账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        HXPostingReturn returnObj = new HXPostingReturn();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */

            HXPostingHeader header = this.transferPosting(headDTO,itemListNotSync,true);
            returnObj = hxInterfaceService.writeOff(header,true);
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号

                    for (BizReceiptInputItemDTO inputItemDTO : itemListNotSync) {

                        inputItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                        inputItemDTO.setWriteOffMatDocCode(returnObj.getMatDocCode());
                        inputItemDTO.setWriteOffMatDocRid(inputItemDTO.getRid());
                    
                        inputItemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));

                        // 冲销成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch dto : insMoveTypeDTO.getInsDocBatchList()) {
                                if (dto.getPreReceiptItemId().equals(inputItemDTO.getId())) {
                                    dto.setMatDocCode(inputItemDTO.getWriteOffMatDocCode());
                                    dto.setMatDocRid(inputItemDTO.getWriteOffMatDocRid());
                                    dto.setPostingDate(inputItemDTO.getWriteOffPostingDate());
                                    dto.setDocDate(inputItemDTO.getWriteOffDocDate());
                                    dto.setMatDocYear(inputItemDTO.getWriteOffMatDocYear());
                                }
                            }
                            for (StockInsDocBin dto : insMoveTypeDTO.getInsDocBinList()) {
                                if (dto.getPreReceiptItemId().equals(inputItemDTO.getId())) {
                                    dto.setMatDocCode(inputItemDTO.getWriteOffMatDocCode());
                                    dto.setMatDocRid(inputItemDTO.getWriteOffMatDocRid());
                                    dto.setMatDocYear(inputItemDTO.getWriteOffMatDocYear());
                                }
                            }
                        }


                    }

                    // 更新入库单行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】
                    ((InputComponent) AopContext.currentProxy()).updateItemCommitImmediately(itemListNotSync);

            } else {
                log.error("入库单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync =
                headDTO.getItemList().stream().map(BizReceiptInputItemDTO::getWriteOffMatDocCode)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }


    public HXPostingHeader transferPosting(BizReceiptInputHeadDTO headDTO,List<BizReceiptInputItemDTO> itemList,boolean isWriteOff){

        HXPostingHeader header = new HXPostingHeader();
        header.setReceiptCode(headDTO.getReceiptCode());
        header.setReceiptType(headDTO.getReceiptType());
        header.setReceiptId(headDTO.getId());
        if(isWriteOff){
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffDocDate()));
        }else{
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getDocDate()));
        }
        List<HXPostingItem> items = itemList.stream().map(input -> {
            HXPostingItem item = new HXPostingItem();
            item.setReceiptRid(input.getRid());
            item.setFtyCode(input.getFtyCode());
            item.setLocationCode1(input.getLocationCode());
            item.setMatCode(input.getMatCode());
            item.setQty(UtilBigDecimal.getString(input.getQty()));
            item.setUnitCode(input.getUnitCode());
            if(isWriteOff){
                item.setMoveType(EnumMoveType.MOVE_TYPE_102.getValue());
            }else{
                item.setMoveType(EnumMoveType.MOVE_TYPE_101.getValue());
            }
            item.setPurchaseOrderCode(input.getPurchaseCode());
            item.setPurchaseOrderItemCode(input.getPurchaseRid());
            if(EnumSendType.OIL_PROCUREMENT.getValue().equals(headDTO.getSendType())){
                //油品收货时需输入发票
                item.setInvoiceCode(input.getInvoiceNo());
                item.setInvoiceDate(UtilDate.convertDateToDateStr(input.getInvoiceDate()));
            }
            if(isWriteOff){
                // 冲销传入 物料凭证信息
                item.setMatDocCode(input.getMatDocCode());
                item.setMatDocYear(input.getMatDocYear());

            }

            return item;
        }).collect(Collectors.toList());
        header.setItems(items);
        return header;
    }

    /**
     * ins入库冲销
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库冲销入参"},{@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void writeOffInputToIns(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - 凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        try {
            /* ***** 修改库存 ***** */
            stockCommonService.modifyStock(insMoveTypeDTO);
            /* ***** 更新item状态-已冲销 ***** */
            this.updateStatus(null, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            /* ***** 更新head状态-已完成 ***** */
            List<Integer> itemStatusList = this.getItemStatusList(headDTO.getId());
            if (bizCommonService.getInputCompletedItemStatusSet().containsAll(itemStatusList)) {
                // 所有行项目状态都是【已完成】或【冲销中】或【已冲销】时，修改单据状态为已完成
                this.updateStatus(headDTO, null, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            }
            /* ***** 单据日志 - 冲销 ***** */
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("入库单{}ins冲销过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 冲销同步更新上架请求
     *
     * @in ctx 入参 {@link BizReceiptInputWriteOffPO : "入库冲销入参对象"}
     */
    public void writeOffUpdateReq(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, po.getItemIds());
        // 推送异步MQ
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.RECEIPT_WRITE_OFF_MODIFY_REQ_ITEM, ctx));
    }

    /**
     * 更新批次入库时间
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void updateInputDate(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO) && UtilCollection.isNotEmpty(headDTO.getItemList())) {
            /* ******** 更新批次信息的入库时间 ******** */
            List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
            for (BizReceiptInputItemDTO inputItemDTO : headDTO.getItemList()) {
                if (EnumRealYn.FALSE.getIntValue().equals(inputItemDTO.getIsWriteOff())) {
                    BizBatchInfoDTO bizBatchInfo = inputItemDTO.getBizBatchInfoDTO();
                    bizBatchInfo.setId(inputItemDTO.getBatchId());
                    bizBatchInfo.setInputDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
                    bizBatchInfo.setMaintenanceDate(inputItemDTO.getDocDate());
                    bizBatchInfoDTOList.add(bizBatchInfo);
                }
            }
            if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
                bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            }
        }
    }

    /**
     * 更新入库单 - 已完成
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新入库单 - 已完成
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 加油站平台-推送推送入库相关信息-接口1
     */
    public void syncOilSys(BizContext ctx) {
        if(!UtilConst.getInstance().getOilEnabled()){
            return;
        }
        String headCode = "", result = "";
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        JSONArray jsonParams = new JSONArray();
        try {
            // 入参上下文
            List<BizReceiptTaskReqItem> taskReqItemList = bizReceiptTaskReqItemDataWrap.list(new QueryWrapper<BizReceiptTaskReqItem>().lambda()
                    .eq(BizReceiptTaskReqItem::getPreReceiptHeadId, headId).eq(BizReceiptTaskReqItem::getIsDelete, 0));
            if(UtilCollection.isNotEmpty(taskReqItemList)){
                List<Long> reqHeadIdList = taskReqItemList.stream().map(e->e.getHeadId()).collect(Collectors.toList());
                List<BizReceiptTaskReqHead> headList = bizReceiptTaskReqHeadDataWrap.list(new QueryWrapper<BizReceiptTaskReqHead>().lambda()
                        .in(BizReceiptTaskReqHead::getId, reqHeadIdList));
                Map<Long, String> headCodeMap = headList.stream().collect(Collectors.toMap(o->o.getId(), o->o.getReceiptCode(),(k1,k2)->k2));
                List<BizReceiptTaskItem> taskItemList = bizReceiptTaskItemDataWrap.list(new QueryWrapper<BizReceiptTaskItem>().lambda()
                        .in(BizReceiptTaskItem::getTaskReqHeadId, reqHeadIdList).eq(BizReceiptTaskItem::getIsDelete, 0));
                List<BizReceiptTaskItemDTO> taskItemDTOList = UtilCollection.toList(taskItemList, BizReceiptTaskItemDTO.class);
                dataFillService.fillAttr(taskItemDTOList);
                for(BizReceiptTaskItemDTO taskItem : taskItemDTOList){
                    JSONObject jsonParam = new JSONObject();
                    headCode = headCodeMap.get(taskItem.getTaskReqHeadId());
                    DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(taskItem.getMatId());
                    jsonParam.put("taskReqReceiptCode", headCode);
                    jsonParam.put("matCode", dicMaterialDTO.getMatCode());
                    jsonParam.put("matName", dicMaterialDTO.getMatName());
                    jsonParam.put("qty", taskItem.getQty());
                    jsonParam.put("binCode", taskItem.getTargetBinCode());
                    jsonParam.put("createTime", UtilDate.convertDateToDateStr(taskItem.getCreateTime()));
                    jsonParams.add(jsonParam);
                }
            }
            log.info("调用post接口:" + UtilConst.getInstance().getOilUrl() + jsonParams.toString());
            // post调用
            String timestamp = DateUtils.format(new Date(), "yyyyMMddHHmm");
            String sign = getSign(null, jsonParams.toString(), UtilConst.getInstance().getOilAppId(), UtilConst.getInstance().getOilAppSecret(), timestamp);
            result = HttpRequest.post(UtilConst.getInstance().getOilUrl()).disableCookie()
                    .header("appId", UtilConst.getInstance().getOilAppId())
                    .header("timestamp", timestamp)
                    .header("sign", sign)
                    .body(jsonParams.toString()).execute().body();
            this.saveLog(ctx.getCurrentUser(), "成功", headCode, UtilConst.getInstance().getOilUrl(), jsonParams.toString(), result, headId, 1);
        }  catch ( Exception e) {
            this.saveLog(ctx.getCurrentUser(), "失败", headCode, UtilConst.getInstance().getOilUrl(), jsonParams.toString(), result + ",异常信息," + e.getMessage(), headId, 0);
            log.info("推送加油站平台-接口1异常" + e.getStackTrace());
        }
    }

    private void saveLog(CurrentUser currentUser, String sapResult, String receiptCode, String url, String inParam, String outParam, Long receiptId, Integer interfaceStatus) {
        LogSapLog model = new LogSapLog();
        model.setSapResult(sapResult);
        model.setReturnMsg(sapResult);
        model.setInterfaceType("40"); // EAM
        model.setReceiptCode(receiptCode);
        model.setInterfaceDescribe("入库");
        model.setUrl(url);
        model.setInParam(UtilGzip.compressStringToString(inParam));
        model.setOutParam(UtilGzip.compressStringToString(outParam));
        model.setUserName(currentUser.getUserName());
        model.setRequestType("POST");
        model.setCreateDate(new Date());
        model.setCreateTime(LocalTime.now());
        model.setRequestSource(SysLogConst.URL_SUFFIX_WEB);
        model.setReceiptId(receiptId.toString());
        model.setReceiptType(EnumReceiptType.STOCK_INPUT_OTHER.getValue().toString());
        model.setInterfaceStatus(interfaceStatus);
        logSapLogDataWrap.save(model);
        log.info("HTTP-REST接口调用日志保存成功");
    }

    public static String getSign(Map<String, Object> paramMap, String requestBody, String appId, String appSecret, String timestamp) {
        //将参数按字典顺序排序
        final Map<String, String> sortedMap = new TreeMap<>();
        if (paramMap != null && !paramMap.keySet().isEmpty()) {
            for (Map.Entry<String, Object> e : paramMap.entrySet()) {
                sortedMap.put(e.getKey(), e.getValue().toString());
            }
        }
        if (UtilString.isNotNullOrEmpty(requestBody)) {
            sortedMap.put("body", Base64.getEncoder().encodeToString(requestBody.getBytes()));
        }
        sortedMap.put("appId", appId);
        sortedMap.put("appSecret", appSecret);
        sortedMap.put("timestamp", timestamp);

        String sign = sortedMap.keySet().stream().map(key -> key + "=" + sortedMap.get(key)).collect(Collectors.joining("&"));
        return DigestUtils.md5Hex(sign);
    }

    /**
     * 更新入库单 - 已驳回
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void updateStatusRejected(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新入库单 - 已驳回
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
    }

    /**
     * 更新入库单【实际入库数量、单据状态】
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO : "入库上架回调VO"}
     */
    public void updateInputByCallback(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 装载已作业入库单
        List<BizReceiptInputItemDTO> taskStockInputList = Lists.newArrayList();
        vo.getInputItemCallbackVoList().forEach(taskItem -> {
            BizReceiptInputItemDTO input = new BizReceiptInputItemDTO();
            input.setId(taskItem.getTaskItemId());
            input.setTaskQty(taskItem.getTaskQty());
            taskStockInputList.add(input);
        });
        // 根据作业情况修改实际入库数量
        if (UtilCollection.isNotEmpty(taskStockInputList)) {
            List<Long> itemIds =
                taskStockInputList.stream().map(BizReceiptInputItemDTO::getId).collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(itemIds)) {
                // 获取验收入库单
                List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap.listByIds(itemIds);
                // 转DTO
                List<BizReceiptInputItemDTO> inputItemDTOList = UtilCollection.toList(inputItemList, BizReceiptInputItemDTO.class);
                for (BizReceiptInputItemDTO taskInputItem : taskStockInputList) {
                    BizReceiptInputItemDTO currentDbStockInputDTO = inputItemDTOList.stream().filter(item -> taskInputItem.getId().equals(item.getId())).findFirst().orElse(null);
                    if (UtilObject.isNull(currentDbStockInputDTO)) {
                        return;
                    }
                    // 设置当前行项目状态 【全部上架-已完成/部分上架-入库中】
                    if (currentDbStockInputDTO.getQty().compareTo(taskInputItem.getTaskQty().add(currentDbStockInputDTO.getTaskQty())) == 0) {
                        taskInputItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
                        taskInputItem.setTaskQty(currentDbStockInputDTO.getQty());
                    } else {
                        taskInputItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
                        taskInputItem.setTaskQty(taskInputItem.getTaskQty().add(currentDbStockInputDTO.getTaskQty()));
                    }
                }
                /* ***** 更新验收入库单item【实际入库数量、单据状态】***** */
                bizReceiptInputItemDataWrap.updateBatchDtoById(taskStockInputList);
                /* ***** 更新验收入库单head【单据状态】***** */
                List<Integer> itemStatusList = this.getItemStatusList(vo.getTaskHeadId());
                Set<Integer> itemStatusSet = new HashSet<>(itemStatusList);
                // 判断所有行项目是否都是【已作业】，是更新单据为【已作业】，否则更新单据为【作业中】
                if (itemStatusSet.size() == 1
                    && itemStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
                    this.updateStatus(new BizReceiptInputHeadDTO().setId(vo.getTaskHeadId()), null,
                        EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
                } else {
                    this.updateStatus(new BizReceiptInputHeadDTO().setId(vo.getTaskHeadId()), null,
                        EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
                }
            }
        }
    }

    /**
     * 校验行项目是否全部完作业
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO : "验收入库上架回调VO"}
     */
    public boolean checkAllItemStatusTask(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验行项目是否全已作业
        if (this.checkAllItemStatusSame(vo.getTaskHeadId(), EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
            BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(vo.getTaskHeadId());
            // 转DTO
            BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
            // 填充关联属性和父子属性
            dataFillService.fillAttr(headDTO);
            // 设置入库单信息到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            return true;
        }
        return false;
    }

    /**
     * 删除入库单单据流
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "入库单删除入参"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据流
            receiptRelationService.deleteReceiptTree(po.getReceiptType(), po.getHeadId());
        }
    }

    /**
     * 删除入库单单据流
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "入库单删除入参"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据附件
            receiptAttachmentService.deleteBizReceiptAttachment(po.getHeadId(), po.getReceiptType());
        }
    }

    /**
     * 删除作业请求
     *
     * @param ctx 上下文
     */
    public void cancelTaskRequest(BizContext ctx) {
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, po.getItemIds());
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.DEL_RECEIPT_REQ_ITEM, ctx));
    }

    /**
     * 准备更新入库单状态
     *
     * @param headDTO 入库单head
     * @param itemDTOList 入库单item
     * @param status 要修改的单据状态
     */
    public void updateStatus(BizReceiptInputHeadDTO headDTO, List<BizReceiptInputItemDTO> itemDTOList, Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新入库单head
     *
     * @param headDto 入库单head
     */
    private void updateHead(BizReceiptInputHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptInputHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新入库单item
     *
     * @param itemDtoList 入库单item
     */
    public void updateItem(List<BizReceiptInputItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptInputItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 更新入库单item
     *
     * @param itemDtoList 入库单item
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateItemCommitImmediately(List<BizReceiptInputItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptInputItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 删除入库单行项目
     *
     * @param po 要删除的入库信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInputItem(BizReceiptInputHeadDTO po) {
        UpdateWrapper<BizReceiptInputItem> wrapperItem = new UpdateWrapper<>();
        wrapperItem.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputItem::getHeadId, po.getId());
        bizReceiptInputItemDataWrap.physicalDelete(wrapperItem);
        UpdateWrapper<BizReceiptInputBin> wrapperBin = new UpdateWrapper<>();
        wrapperBin.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputBin::getHeadId, po.getId());
        bizReceiptInputBinDataWrap.physicalDelete(wrapperBin);
    }

    /**
     * 过账前设置行项目账期
     *
     * @param inputItemDTOList 未同步sap入库单行项目
     * @param user 当前用户
     */
    public void setInPostDate(List<BizReceiptInputItemDTO> inputItemDTOList, CurrentUser user) {
        if (UtilCollection.isNotEmpty(inputItemDTOList)) {
            Date postingDate = inputItemDTOList.get(0).getPostingDate();
            Date writeOffPostingDate = inputItemDTOList.get(0).getWriteOffPostingDate();
            Date deliveryWriteOffPostingDate = inputItemDTOList.get(0).getDeliveryWriteOffPostingDate();
            if (UtilObject.isNull(postingDate)) {
                postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            if (UtilObject.isNull(writeOffPostingDate)) {
                writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            if (UtilObject.isNull(deliveryWriteOffPostingDate)) {
                deliveryWriteOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            // 判断过账日期是否在帐期内
            // postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
            // writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
            // 2024-11-19 过账日期、冲销日期、发货冲销日期为当前日期
            for (BizReceiptInputItemDTO inputItemDTO : inputItemDTOList) {
                if (EnumRealYn.FALSE.getIntValue().equals(inputItemDTO.getIsWriteOff())) {
                    inputItemDTO.setDocDate(UtilDate.getNow());
                    inputItemDTO.setPostingDate(postingDate);
                } else {
                    inputItemDTO.setWriteOffDocDate(UtilDate.getNow());
                    inputItemDTO.setWriteOffPostingDate(writeOffPostingDate);

                    inputItemDTO.setDeliveryWriteOffDocDate(UtilDate.getNow());
                    inputItemDTO.setDeliveryWriteOffPostingDate(deliveryWriteOffPostingDate);
                }
            }
        }
    }

    /**
     * 判断行项目是否为空
     *
     * @param headDTO 入库单
     */
    public void checkEmptyItem(BizReceiptInputHeadDTO headDTO) {
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, "单据备注超过200字符限制");
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 校验验收入库单行项目相关数量
     *
     * @param headDTO 入库单
     */
    public void checkEmptyItemQty(BizReceiptInputHeadDTO headDTO) {
        List<String> errorRidList = new ArrayList<>(headDTO.getItemList().size());
        headDTO.getItemList().forEach(itemDTO -> {
            if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                errorRidList.add(itemDTO.getRid());
            }
        });
        if (UtilCollection.isNotEmpty(errorRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_QTR_ZERO, errorRidList.toString());
        }
    }

    /**
     * 入库回调失败更新单据状态 - 未同步
     *
     * @param vo 验收入库上架回调VO
     */
    public void inputHeadCallbackToFail(BizReceiptInputHeadCallbackVO vo) {
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(vo.getTaskHeadId());
        // 转DTO
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 更新单据-未同步
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
    }

    /**
     * 按钮组
     *
     * @param headDTO 入库单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInputHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        // 单据类型
        Integer receiptType = headDTO.getReceiptType();

        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
            if (EnumReceiptType.STOCK_TOOLS_INPUT.getValue().equals(receiptType)) {
                // 2023-06-19 工器具信息中保存的信息在批次信息中，草稿状态反复保存应重新生成工器具编码，此处暂不处理，隐藏保存按钮规避此问题
                buttonVO.setButtonSave(false);
            }
            return buttonVO;
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 已提交 -【删除】
            return buttonVO.setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {

//            if (EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType)) {
//                // 2023-05-11 根据邓健要求，移除验收入库已完成状态下105冲销
//                return buttonVO.setButtonPrint(true);
//            }

            // 已记账 -【冲销、打印】
            return buttonVO.setButtonWriteOff(true).setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {

            if (EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType)) {
                // 2023-05-11 根据邓健要求，移除验收入库已完成状态下105冲销
                return buttonVO.setButtonPrint(true);
            }

            // 作业中 -【冲销】
            List<BizReceiptInputItemDTO> itemList = this.getItemListById(headDTO.getId()).getItemList();
            boolean canDisplayWriteOff = false;
            for (BizReceiptInputItemDTO inputItemDTO : itemList) {
                if (EnumRealYn.TRUE.getIntValue().equals(inputItemDTO.getIsPost())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            buttonVO.setButtonPrint(true);
            return buttonVO.setButtonWriteOff(canDisplayWriteOff);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 -【冲销、打印】
            List<BizReceiptInputItemDTO> itemList = this.getItemListById(headDTO.getId()).getItemList();
            boolean canDisplayWriteOff = false;
            for (BizReceiptInputItemDTO inputItemDTO : itemList) {
                if (EnumRealYn.FALSE.getIntValue().equals(inputItemDTO.getIsWriteOff())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            if (EnumReceiptType.STOCK_TOOLS_INPUT.getValue().equals(receiptType)){
                return buttonVO;
            }
            if (EnumReceiptType.STOCK_INPUT_REPAIR.getValue().equals(receiptType)) {
                return buttonVO.setButtonWriteOff(canDisplayWriteOff);
            }
            if (EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType)) {
                // 2023-05-11 根据邓健要求，移除验收入库已完成状态下105冲销
               // return buttonVO.setButtonPrint(true).setButtonDeal(true);
               return buttonVO.setButtonPrint(true).setButtonWriteOff(canDisplayWriteOff);
            }
            return buttonVO.setButtonWriteOff(canDisplayWriteOff).setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus)) {
            // 已作业 -【过账】
            return buttonVO.setButtonPost(true);
        }
        return buttonVO;
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 获取行项目状态集合
     *
     * @param headId 单据id
     * @return 状态列表
     */
    public List<Integer> getItemStatusList(Long headId) {
        // 获取入库单行项目
        List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap
            .list(new QueryWrapper<BizReceiptInputItem>().lambda().eq(BizReceiptInputItem::getHeadId, headId));
        // 行项目状态集合
        return inputItemList.stream().map(BizReceiptInputItem::getItemStatus).collect(Collectors.toList());
    }

    /**
     * 根据headId查询出库单列表
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptInputHeadDTO getItemListById(Long headId) {
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(headId);
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 校验行行目状态是否全部相同
     *
     * @param headId 入库单抬头主键
     * @param itemStatus 行项目状态
     * @return true/false
     */
    public boolean checkAllItemStatusSame(Long headId, Integer itemStatus) {
        UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptInputItem::getHeadId, headId);
        // 获取全部上架作业的入库单
        List<BizReceiptInputItem> inputItem = bizReceiptInputItemDataWrap.list(wrapper);
        // 转DTO
        List<BizReceiptInputItemDTO> allStockInputDTOList =
            UtilCollection.toList(inputItem, BizReceiptInputItemDTO.class);
        if (UtilCollection.isEmpty(allStockInputDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 过滤掉已冲销行项目
        allStockInputDTOList = allStockInputDTOList.stream().filter(e->!e.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue())).collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(allStockInputDTOList)){
            // 过滤行项目
            List<BizReceiptInputItemDTO> stayInputList = allStockInputDTOList.stream()
                    .filter(e -> itemStatus.equals(e.getItemStatus())).collect(Collectors.toList());
            return stayInputList.size() == allStockInputDTOList.size();
        }else{
            return true;
        }

    }

    /**
     * 提交前校验物料、库存地点是否冻结
     */
    public void checkFreeze(BizReceiptInputHeadDTO headDTO) {
        // 提交前校验物料、库存地点是否冻结
        materialService.checkFreezeMaterial(headDTO.getItemList().stream().map(BizReceiptInputItemDTO::getMatId)
            .distinct().collect(Collectors.toList()));
        stockLocationService.checkFreezeInputLocation(headDTO.getItemList());
    }

    /**
     * 根据物料凭证计算库存是否可以正常修改
     *
     * @param stockInsMoveTypePostTaskDTO 移动类型
     */
    public void checkAndComputeForModifyStock(StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO) {
        if (Objects.nonNull(stockInsMoveTypePostTaskDTO)) {
            if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getPostDTO())) {
                stockCommonService.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO.getPostDTO());
            }
            if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getTaskDTO())) {
                stockCommonService.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO.getTaskDTO());
            }
        }
    }

    /**
     * InStock过账
     *
     * @param ctx 上下文
     */
    public void postToInStock(BizContext ctx) {
        log.info("入库instock过账 ctx：{}", JSONObject.toJSONString(ctx));
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        try {
            // 修改库存
            this.modifyStock(stockInsMoveTypePostTaskDTO);
            // 更新单据行项目状态已记账
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            // 添加单据过账日志
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                    EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", user.getId());
        } catch (WmsException e) {
            // 失败时更新入库单及行项目为未同步
            log.warn("入库单{}过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 根据移动类型对象修改库存
     *
     * @param stockInsMoveTypePostTaskDTO 移动类型
     */
    public void modifyStock(StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO) {
        if (Objects.nonNull(stockInsMoveTypePostTaskDTO)) {
            if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getPostDTO())) {
                stockCommonService.modifyStock(stockInsMoveTypePostTaskDTO.getPostDTO());
            }
            if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getTaskDTO())) {
                stockCommonService.modifyStock(stockInsMoveTypePostTaskDTO.getTaskDTO());
            }
        }
    }


    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();

        /*if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }*/
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }


        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptInputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);


        // 过滤行项目
        if(UtilCollection.isEmpty(po.getBizLabelPrintDTO().getPrintItemDTOList())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        Set<Long> itemIdSet = po.getBizLabelPrintDTO().getPrintItemDTOList().stream().map(PrintItemDTO::getItemId).collect(Collectors.toSet());

        Map<Long,PrintItemDTO> printMap = po.getBizLabelPrintDTO().getPrintItemDTOList().stream().collect(Collectors.toMap(e->e.getItemId(), e->e));


        List<BizReceiptInputItemDTO> itemList = headDTO.getItemList().stream().filter(e->itemIdSet.contains(e.getId())).collect(Collectors.toList());

        if(UtilCollection.isEmpty(itemList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        for(BizReceiptInputItemDTO itemDTO:itemList){
            PrintItemDTO print = printMap.get(itemDTO.getId());

            itemDTO.setIsSingle(print.getIsSingle());
            itemDTO.setTagType(print.getTagType());
            itemDTO.setPrintNum(print.getPrintNum());
            itemDTO.setIsLabelPrint(1);
        }

        headDTO.setItemList(itemList);

        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }



    @Autowired
    private DicMaterialFactoryDataWrap dicMaterialFactoryDataWrap;


    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptInputHeadDTO headDTO = (BizReceiptInputHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<BizReceiptInputItemDTO> itemDTOList = headDTO.getItemList();

        int printCount = 0;
        for(BizReceiptInputItemDTO itemDTO:itemDTOList){
            if(itemDTO.getTagType()==0&&itemDTO.getIsSingle()==0){
                // 普通批次标签不生成标签code
                continue;
            }
            printCount = printCount + itemDTO.getPrintNum();
        }

        List<String> labelCodeList = bizCommonService.getNextNValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue(),printCount);
        int la = 0;

        for(BizReceiptInputItemDTO itemDTO:itemDTOList ){

            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), EnumDefaultStorageType.INPUT.getTypeCode());
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(),EnumDefaultStorageType.INPUT.getTypeCode(),EnumDefaultStorageType.INPUT.getBinCode());
            if(itemDTO.getIsSingle() == 1){
                // 单品标签
                for (int i = 0; i < itemDTO.getPrintNum(); i++) {
                    String labelCode = labelCodeList.get(la);
                    la++;
                    printCount(receiptInputBoxes, itemDTO, labelCode);
                    // 设置需要保存的标签数据
                    BizLabelDataDTO label = BizLabelDataDTO.builder()
                        .id(null)
                        .matId(itemDTO.getMatId())
                        .ftyId(itemDTO.getFtyId())
                        .locationId(itemDTO.getLocationId())
                        .batchId(itemDTO.getBizBatchInfoDTO().getId())
                        .binId(binId)
                        .whId(itemDTO.getWhId())
                        .typeId(typeId)
                        .labelCode(labelCode)
                        .snCode(labelCode)
                        .qty(BigDecimal.ZERO)
                        .labelType(itemDTO.getTagType())
                        .receiptHeadId(itemDTO.getHeadId())
                        .receiptItemId(itemDTO.getId())
                        .receiptType(itemDTO.getReceiptType())
                        .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                        .preReceiptItemId(itemDTO.getPreReceiptItemId())
                        .preReceiptType(itemDTO.getPreReceiptType())
                        .build();
                    labelDataList.add(label);

                }
            }else{
                String labelCode = "";
                if(itemDTO.getTagType()!=0){
                    // RFID批次 生成标签数据
                    for (int i = 0; i < itemDTO.getPrintNum(); i++) {
                        labelCode = labelCodeList.get(la);
                        la++;
                        // 设置需要保存的标签数据
                        BizLabelDataDTO label = BizLabelDataDTO.builder()
                            .id(null)
                            .matId(itemDTO.getMatId())
                            .ftyId(itemDTO.getFtyId())
                            .locationId(itemDTO.getLocationId())
                            .batchId(itemDTO.getBizBatchInfoDTO().getId())
                            .binId(binId)
                            .whId(itemDTO.getWhId())
                            .typeId(typeId)
                            .labelCode(labelCode)
                            .snCode(labelCode)
                            .qty(BigDecimal.ZERO)
                            .labelType(itemDTO.getBizBatchInfoDTO().getTagType())
                            .receiptHeadId(itemDTO.getHeadId())
                            .receiptItemId(itemDTO.getId())
                            .receiptType(itemDTO.getReceiptType())
                            .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                            .preReceiptItemId(itemDTO.getPreReceiptItemId())
                            .preReceiptType(itemDTO.getPreReceiptType())
                            .build();
                        labelDataList.add(label);
                        printCount(receiptInputBoxes, itemDTO, labelCode);
                    }

                }else{
                    // 普通批次标签
                    for (int i = 0; i < itemDTO.getPrintNum(); i++) {
                        printCount(receiptInputBoxes, itemDTO, labelCode);
                    }

                }



            }

            // 设置要更新的批次信息数据
            itemDTO.getBizBatchInfoDTO().setInspectHeadId(itemDTO.getHeadId());
            itemDTO.getBizBatchInfoDTO().setInspectItemId(itemDTO.getId());
            itemDTO.getBizBatchInfoDTO()
                .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getBizBatchInfoDTO().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
            itemDTO.getBizBatchInfoDTO().setTagType(itemDTO.getTagType());
            itemDTO.getBizBatchInfoDTO().setIsSingle(itemDTO.getIsSingle());
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());


        };

        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        // 更新入库单
        bizReceiptInputItemDataWrap.updateBatchByIdOptimize(itemDTOList, 100);

        // 更新物料工厂
        this.updateMatFty(itemDTOList);
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));

            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }

            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        /* *** 插入标签数与上架请求关联关系*** */
        List<BizReceiptTaskReqItem> reqItemList = this.getReqByPreHeadId(headDTO.getId());


        if(UtilCollection.isNotEmpty(reqItemList)){

            Map<Long, BizReceiptTaskReqItem> reqItemMap = reqItemList.stream().collect(Collectors.toMap(e->e.getPreReceiptItemId(), e->e,(k1,k2)->k2));

            for (BizLabelDataDTO label : labelDataList) {

                BizReceiptTaskReqItem reqItem = reqItemMap.get(label.getReceiptItemId());
                if(reqItem!=null){
                    BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                    bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                    bizLabelReceiptRel.setId(null);
                    bizLabelReceiptRel.setLabelId(label.getId());
                    bizLabelReceiptRel.setReceiptType(EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue());
                    bizLabelReceiptRel.setReceiptHeadId(reqItem.getHeadId());
                    bizLabelReceiptRel.setReceiptItemId(reqItem.getId());
                    bizLabelReceiptRel.setPreReceiptType(reqItem.getPreReceiptType());
                    bizLabelReceiptRel.setPreReceiptHeadId(reqItem.getPreReceiptHeadId());
                    bizLabelReceiptRel.setPreReceiptItemId(reqItem.getPreReceiptItemId());
                    bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                    bizLabelReceiptRelList.add(bizLabelReceiptRel);
                }

            }

        } else {
            log.error("入库单单据{},因缺少作业请求信息无法添加上架请求与标签对应的关联关系，标签打印失败", headDTO.getReceiptCode());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_REQUEST_ALREADY_IN_PROGRESS_EXCEPTION);
        }

        labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            Map<String, Long> labelDataMap = labelDataList.stream().collect(Collectors.toMap(e->e.getLabelCode(), e->e.getId()));
            for(LabelReceiptInputBox box:receiptInputBoxes){
                Long labelId = labelDataMap.get(box.getRfidCode());
                if(labelId!=null){
                    box.setLabelId(labelId);
                }
            }

            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }


    private void updateMatFty(List<BizReceiptInputItemDTO> itemDTOList){
        List<DicMaterialFactoryDTO> materialFactoryDTOList = new ArrayList<>();
        for(BizReceiptInputItemDTO itemDTO:itemDTOList){
            DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(itemDTO.getMatId(),itemDTO.getFtyId());
            if(materialFactoryDTO!=null){
                materialFactoryDTO.setIsSingle(itemDTO.getIsSingle());
                materialFactoryDTO.setTagType(itemDTO.getTagType());
                materialFactoryDTOList.add(materialFactoryDTO);
            }

        }
        if(UtilCollection.isNotEmpty(materialFactoryDTOList)){
            dicMaterialFactoryDataWrap.updateBatchByIdOptimize(materialFactoryDTOList, 100);
            // 刷新缓存
            dictionaryService.refreshDicMaterialFactoryByUniqueKey(materialFactoryDTOList);
        }


    }

    @Autowired
    private BizReceiptTaskReqItemDataWrap bizReceiptTaskReqItemDataWrap;
    @Autowired
    private BizReceiptTaskItemDataWrap bizReceiptTaskItemDataWrap;
    @Autowired
    private BizReceiptTaskReqHeadDataWrap bizReceiptTaskReqHeadDataWrap;

    /**
     * 获取上架请求
     * @param headId
     * @return
     */
    private List<BizReceiptTaskReqItem> getReqByPreHeadId(Long headId){
        List<BizReceiptTaskReqItem> reqItems = new ArrayList<>();

        QueryWrapper<BizReceiptTaskReqItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTaskReqItem::getPreReceiptHeadId, headId);

        reqItems = bizReceiptTaskReqItemDataWrap.list(queryWrapper);
        if(UtilCollection.isNotEmpty(reqItems)){
            List<Long> reqHeadIdList = reqItems.stream().map(e->e.getHeadId()).collect(Collectors.toList());

            QueryWrapper<BizReceiptTaskReqHead> headQueryWrapper = new QueryWrapper<>();
            headQueryWrapper.lambda().in(BizReceiptTaskReqHead::getId, reqHeadIdList);
            headQueryWrapper.lambda().eq(BizReceiptTaskReqHead::getReceiptType,EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue());
            List<BizReceiptTaskReqHead> headList = bizReceiptTaskReqHeadDataWrap.list(headQueryWrapper);
            if(UtilCollection.isNotEmpty(headList)){
                List<Long> findHeadIdList = headList.stream().map(e->e.getId()).collect(Collectors.toList());
                reqItems = reqItems.stream().filter(e->findHeadIdList.contains(e.getHeadId())).collect(Collectors.toList());
            }

        }

        return reqItems;
    }



    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCount(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(itemDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(itemDTO.getTagType());
        labelReceiptInputBox.setIsSingle(itemDTO.getIsSingle());
        labelReceiptInputBox.setBatchCode(itemDTO.getBizBatchInfoDTO().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setMatDocCode(itemDTO.getMatDocCode());
        labelReceiptInputBox.setMatDocRid(itemDTO.getMatDocRid());
        labelReceiptInputBox.setItemId(itemDTO.getId());
        labelReceiptInputBox.setLifetimeDate(itemDTO.getBizBatchInfoDTO().getLifetimeDate());
        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (itemDTO.getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }
        // 批次 + 普通标签不需要生成rfid,其他生成rfid
        //        if (!(EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsSingle()) && EnumTagType.GENERAL.getValue().equals(itemDTO.getTagType()))) {
        //            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        //        } else {
        //            labelReceiptInputBox.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
        //        }
        if(itemDTO.getTagType()==0){
            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.FALSE.getIntValue());
        }else{
            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        }

        receiptInputBoxes.add(labelReceiptInputBox);
    }




    public void setPrintInfo(BizReceiptInputHeadDTO headDTO){

        if(UtilCollection.isNotEmpty(headDTO.getItemList())){

            for(BizReceiptInputItemDTO inputItemDTO:headDTO.getItemList()){
                if(inputItemDTO.getIsLabelPrint()==0){
                    // 查询物料工厂
                    DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(inputItemDTO.getMatId(), inputItemDTO.getFtyId());

                    if(materialFactoryDTO!=null){
                        inputItemDTO.setIsSingle(materialFactoryDTO.getIsSingle());
                        inputItemDTO.setTagType(materialFactoryDTO.getTagType());
                    }

                }
                // [27990]【高温堆-WEB】验收入库，验收入库标签打印时，不能出现“普通标签”的类型，所有标签类型均为“RFID非抗金属标签”
                if(UtilNumber.isEmpty(inputItemDTO.getTagType())){
                    inputItemDTO.setTagType(EnumTagType.METAL_UNRESISTANT.getValue());
                }

                List<BizReceiptTaskItemDTO> taskItemDTOList = taskComponent.getTaskItemByPreReceiptItemIds(new ArrayList<>(Collections.singletonList(inputItemDTO.getId())));
                if (UtilCollection.isNotEmpty(taskItemDTOList)) {
                    String binCodeStr = taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getTargetBinCode).collect(Collectors.joining(","));
                    inputItemDTO.setBinCodeStr(binCodeStr);
                    // 只设置一次
                    if (headDTO.getItemList().stream().allMatch(c -> UtilString.isNullOrEmpty(c.getSubmitUserSignImg()))) {
                        String submitUserSignImg = taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getSubmitUserSignImg).collect(Collectors.joining(","));
                        inputItemDTO.setSubmitUserSignImg(submitUserSignImg);
                    }
                }
            }

        }

    }


    /**
     * 保存仓位【同时模式】
     *
     * @in ctx  入参 {@link BizReceiptInputHeadDTO : "已保存的入库单}
     */
    public void saveInputBin(BizContext ctx) {
        // 入参上下文 - 已保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - 已保存的入库单code
        String stockInputCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);

        /* ********************** bin处理开始 *************************/
        AtomicInteger bid = new AtomicInteger(1);
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            if (UtilCollection.isNotEmpty(itemDTO.getBinList())) {
                itemDTO.getBinList().forEach(bin -> {
                    bin.setId(null);
                    bin.setHeadId(itemDTO.getHeadId());
                    bin.setItemId(itemDTO.getId());
                    bin.setBid(Integer.toString(bid.getAndIncrement()));
                    bin.setQty(itemDTO.getQty());
                });
            } else {
                itemDTO.setBinList(new ArrayList<>());
            }
        }

        bizReceiptInputBinDataWrap.saveBatchDto(po.getItemList().stream().flatMap(item -> item.getBinList().stream()).collect(Collectors.toList()));
        log.debug("保存入库单bin成功!单号{},headId{}", stockInputCode, po.getId());
        /* ********************** bin处理结束 *************************/
    }

    /**
     * 保存单品标签【同时模式】
     *
     * @in ctx  入参 {@link BizReceiptInputHeadDTO : "已保存的入库单}
     */
    public void saveLabelData(BizContext ctx) {
        // 入参上下文 - 已保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 装载批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();

        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            // 单品/批次 0批次 1单品
            Integer isSingle = itemDTO.getBizBatchInfoDTO().getIsSingle();
            if (UtilCollection.isNotEmpty(itemDTO.getBinList()) && EnumRealYn.TRUE.getIntValue().equals(isSingle)) {
                itemDTO.getBinList().forEach(binDTO ->{
                    for (int qty = 0; qty < binDTO.getQty().intValue(); qty++) {
                        BizLabelDataDTO label = new BizLabelDataDTO();
                        String labelCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());
                        label = UtilBean.newInstance(itemDTO, label.getClass());
                        label.setId(null);
                        label.setLabelCode(labelCode);
                        label.setSnCode(labelCode);
                        label.setQty(new BigDecimal(1));
                        label.setTypeId(binDTO.getTypeId());
                        label.setBinId(binDTO.getBinId());
                        label.setCellId(binDTO.getCellId());
                        label.setReceiptType(po.getReceiptType());
                        label.setReceiptHeadId(itemDTO.getHeadId());
                        label.setReceiptItemId(itemDTO.getId());
                        label.setReceiptBinId(binDTO.getId());
                        label.setReceiptType(itemDTO.getReceiptType());
                        labelDataList.add(label);
                    }
                });
            }
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());
        }

        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelReceiptRelService.multiRemoveLabel(po.getId());
            log.info("入库单删除标签及关联数据成功,headId{}", po.getId());

            labelDataService.saveBatchDto(labelDataList);
            log.info("入库单插入标签数据成功 " + JSONObject.toJSONString(labelDataList));

            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(po.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_CODE.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("入库单插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        /* *** 更新批次新信息标签类型 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)){
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("入库单更新批次标签类型成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
    }

    public void setButtonHidden(BizContext ctx) {
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead())) {
            return;
        }

        CurrentUser currentUser = ctx.getCurrentUser();
        // 当角色有且只有"JS006"时，才需要隐藏所有按钮只保留打印
        if (ObjectUtils.isNotEmpty(currentUser.getSysUserRoleRelList()) && currentUser.getSysUserRoleRelList().size() == 1){
            Set<String> tempRole = currentUser.getSysUserRoleRelList().stream().map(SysUserRoleRel::getRoleCode).collect(Collectors.toSet());
            if(tempRole.contains("JS006")) {
                // 进行权限特殊处理，需求组角色开放质检会签与验收入库打印 
                resultVO.setButton(new ButtonVO());
                resultVO.getHead().setRelationList(null);
            }
        }
    }


    @Autowired
    private ContractComponent contractComponent;

    /**
     * 过账及冲销时 回写合同 已入库数量
     */
    public void writeBackQty(BizContext ctx){

        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        BizReceiptInputHeadDTO headDTO ;
        
        if(EnumReceiptOperationType.RECEIPT_OPERATION_POSTING.equals(operationLogType)){
            headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        }else{
            headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        }
        
        

    
        Map<Long, BizReceiptContractItemQtyDTO> contractItemMap = new HashMap<>();

        if(UtilCollection.isNotEmpty(headDTO.getItemList())){
            for(BizReceiptInputItemDTO itemDTO:headDTO.getItemList()){
                Long key = itemDTO.getReferReceiptItemId();
                
                    if(contractItemMap.containsKey(key)){
                        BizReceiptContractItemQtyDTO contractItem = contractItemMap.get(key);
                        contractItem.setInputQty(contractItem.getInputQty().add(itemDTO.getQty()));
                        contractItem.setSendQty(contractItem.getSendQty().add(itemDTO.getQty()));
                    }else {
                        BizReceiptContractItemQtyDTO contractItem = new BizReceiptContractItemQtyDTO();
                        contractItem.setId(itemDTO.getReferReceiptItemId());
                        contractItem.setInputQty(itemDTO.getQty());
                        contractItem.setSendQty(itemDTO.getQty());
                        contractItemMap.put(key,contractItem);
                    }
                
            }
        }

       

        

        // 过账时增加 冲销时回写扣减    
        if(EnumReceiptOperationType.RECEIPT_OPERATION_POSTING.equals(operationLogType)){
            contractItemMap.values().forEach(item -> {
                // 正向过账不回写合同已入库数量
                item.setSendQty(BigDecimal.ZERO);
            });
        }else{
            // 冲销时回写扣减 数量取相反数
            contractItemMap.values().forEach(item -> {
                item.setInputQty(item.getInputQty().negate());
                item.setSendQty(item.getSendQty().negate());
            });    
        }
        

        contractItemMap.values().forEach(item -> {
            if(headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())){
                item.setFrameworkContract(true);
            }
        });  

        if(UtilCollection.isNotEmpty(contractItemMap.values())){
            BizContext newCtx = new BizContext();  
            newCtx.setContextData(Const.BIZ_CONTEXT_KEY_LIST,new ArrayList<>(contractItemMap.values()));
            newCtx.setCurrentUser(ctx.getCurrentUser());
            contractComponent.batchUpdateItemQty(newCtx);
        }


        // 更新送货通知单已入库数量
        // 查询送货通知单明细

        List<Long> receiptItemIdList = headDTO.getItemList().stream().map(BizReceiptInputItemDTO::getDeliveryItemId).collect(Collectors.toList());

        List<BizReceiptDeliveryNoticeItem> deliveryItemList = receiptDeliveryNoticeItemDataWrap.list(new QueryWrapper<BizReceiptDeliveryNoticeItem>().lambda().in(BizReceiptDeliveryNoticeItem::getId, receiptItemIdList));
        Map<Long,BigDecimal> updateMap = new HashMap<>();
        Map<Long,BizReceiptDeliveryNoticeItem> deliveryItemMap = new HashMap<>();
        List<BizReceiptDeliveryNoticeItem> updateList = new ArrayList<>();
        if(UtilCollection.isNotEmpty(deliveryItemList)){
            deliveryItemMap = deliveryItemList.stream().collect(Collectors.toMap(BizReceiptDeliveryNoticeItem::getId, Function.identity(),(oldValue,newValue)->newValue));
        }

        if(UtilCollection.isNotEmpty(headDTO.getItemList())){
            for(BizReceiptInputItemDTO item:headDTO.getItemList()){
                if(updateMap.containsKey(item.getDeliveryItemId())){
                    BigDecimal inputQty = updateMap.get(item.getDeliveryItemId());
                    inputQty = inputQty.add(item.getQty());
                    updateMap.put(item.getDeliveryItemId(), inputQty);
                }else{
                    updateMap.put(item.getDeliveryItemId(), item.getQty());
                }
            }
            final Map<Long, BizReceiptDeliveryNoticeItem> finalDeliveryItemMap = deliveryItemMap;
            updateMap.forEach((key,value)->{
                BizReceiptDeliveryNoticeItem item = finalDeliveryItemMap.get(key);
                if(item!=null){
                    BizReceiptDeliveryNoticeItem updateItem = new BizReceiptDeliveryNoticeItem();
                    updateItem.setId(item.getId());
                    if(EnumReceiptOperationType.RECEIPT_OPERATION_POSTING.equals(operationLogType)){    
                        updateItem.setInputQty(item.getInputQty().add(value));
                        updateList.add(updateItem);
                    }else{
                        // 冲销时不回写 防止 先关闭后冲销时 合同已发货数量回写失败
                    }
                    
                }
            }); 
        }

        if(UtilCollection.isNotEmpty(updateList)){
            receiptDeliveryNoticeItemDataWrap.updateBatchById(updateList);   
        }

    }   

}
