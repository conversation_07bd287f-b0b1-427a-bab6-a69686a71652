package com.inossem.wms.bizdomain.apply.service.biz;

import com.inossem.wms.bizdomain.apply.service.component.MatApplyComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 物料编码申请 service
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class MatApplyService {

    @Autowired
    private MatApplyComponent matApplyComponent;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {

        // 初始化
        matApplyComponent.setInit(ctx);

        // 开启附件
        matApplyComponent.setExtendAttachment(ctx);

        // 开启操作日志
        matApplyComponent.setExtendOperationLog(ctx);
    }

    /**
     * 物料编码申请-分页
     *
     * @param ctx context
     */
    public void getPage(BizContext ctx) {

        // 分页
        matApplyComponent.getPage(ctx);
    }

    /**
     * 物料编码申请-详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {

        // 获取详情
        matApplyComponent.getInfo(ctx);

        // 开启附件
        matApplyComponent.setExtendAttachment(ctx);

        // 开启操作日志
        matApplyComponent.setExtendOperationLog(ctx);

        // 开启审批
        matApplyComponent.setExtendWf(ctx);
    }

    /**
     * 保存单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存-校验物料编码申请入参
        matApplyComponent.checkSaveData(ctx);

        // 保存-物料编码申请单
        matApplyComponent.saveApply(ctx);

        // 保存操作日志
        matApplyComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        matApplyComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交入参校验
        matApplyComponent.checkSubmitData(ctx);

        // 提交物料编码申请
        matApplyComponent.submitApply(ctx);

        // 保存操作日志
        matApplyComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        matApplyComponent.saveBizReceiptAttachment(ctx);

        // 开启审批
        matApplyComponent.startWorkFlow(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_MAT_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {

        // 审批回调
        matApplyComponent.approvalCallback(wfReceiptCo);
    }

    /**
     * 物料组下拉
     *
     * @param ctx 入参上下文
     */
    public void getMatGroup(BizContext ctx) {

        // 获取物料组下拉
        matApplyComponent.getMatGroup(ctx);
    }

    /**
     * 导出
     *
     * @param ctx 入参上下文
     */
    public void export(BizContext ctx) {

        // 导出
        matApplyComponent.export(ctx);
    }

    /**
     * 删除
     *
     * @param ctx 入参上下文
     */
    public void remove(BizContext ctx) {

        // 删除
        matApplyComponent.remove(ctx);
    }

    /**
     * 撤销
     *
     * @param ctx 入参上下文
     */
    public void revoke(BizContext ctx) {

        // 撤销
        matApplyComponent.revoke(ctx);
    }

    /**
     * 导入
     *
     * @param ctx 入参上下文
     */
    public void importMatApply(BizContext ctx) {

        // 导入
        matApplyComponent.importMatApply(ctx);
    }

    /**
     * 导入物料编码
     *
     * @param ctx 入参上下文
     */
    public void importMatCode(BizContext ctx) {

        // 导入
        matApplyComponent.importMatCode(ctx);
    }

}
