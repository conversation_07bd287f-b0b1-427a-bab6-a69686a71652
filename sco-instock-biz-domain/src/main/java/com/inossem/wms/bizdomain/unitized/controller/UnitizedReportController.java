package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedReportService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.report.dto.InspectCaseDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.InspectMatDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.OccupyDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeDirectDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeInputDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeOutputDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.OverDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.OverDetailDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.SubtractDetailDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeUnloadDTO;
import com.inossem.wms.common.model.bizdomain.report.po.*;
import com.inossem.wms.common.model.bizdomain.report.vo.*;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.MoveTypeMapVO;
import com.inossem.wms.common.model.common.enums.SpecStockMapVO;
import com.inossem.wms.common.model.common.enums.StockStatusMapVO;
import com.inossem.wms.common.model.common.enums.inventory.StocktakingTypeMapVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;


/**
 * <p>
 * 报表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-10
 */
@RestController
@Api(tags = "仓储功能-报表管理")
public class UnitizedReportController {

    @Autowired
    protected UnitizedReportService reportService;
    private InputLedgerPO po;
    private BizContext ctx;


    /**
     * 特殊库存标识
     * @param ctx ctx
     * @return 特殊库存标识 vo
     */
    @ApiOperation(value = "报表-特殊库存下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/sepc-stock/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SpecStockMapVO>> getSepcStockList(BizContext ctx) {
        reportService.getSpecStockList(ctx);
        MultiResultVO<SpecStockMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存类型
     * @param ctx ctx
     * @return 库存类型vo
     */
    @ApiOperation(value = "报表-库存类型下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/stock-status/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockStatusMapVO>> getStockStatusList(BizContext ctx) {
        reportService.getStockStatusList(ctx);
        MultiResultVO<StockStatusMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 批次库存查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<UnitizedStockBatchVO>> selectStockBatchDetail(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchDetail(ctx);
        PageObjectVO<UnitizedStockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 批次库存导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-批次库存导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockBatchDetail(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockBatchDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 批次库存查询 物料组分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存-物料组分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/mat-group-code/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBatchVO>> selectStockBatchGroupByMatGroup(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchGroupByMatGroup(ctx);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 批次库存查询 库存地点分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存-库存地点分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/location/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBatchVO>> selectStockBatchGroupByLocation(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchGroupByLocation(ctx);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 批次库存查询 仓库号分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存-仓库分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/wh/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBatchVO>> selectStockBatchGroupByWh(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchGroupByWh(ctx);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 仓位库存查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-bin/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<UnitizedStockBinVO>> selectStockBinDetail(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinDetail(ctx);
        PageObjectVO<UnitizedStockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 仓位库存导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-bin/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockBinExcel(@RequestBody StockBinSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockBinDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 仓位库存查询 仓库分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存仓库-分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-bin/wh/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinVO>> selectStockBinGroupByWh(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinGroupByWh(ctx);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 仓位库存查询 存储类型分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存存储类型分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-bin/type/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinVO>> selectStockBinGroupByType(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinGroupByType(ctx);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 仓位库存查询 详情 存储区分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存-存储区分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-bin/section/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinVO>> selectStockBinGroupBySection(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinGroupBySection(ctx);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存积压分析
     * @param po 入参
     * @param ctx ctx
     * @return 返回库存积压vo
     */
    @ApiOperation(value = "报表-库存积压", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-age-analyse/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockAgeAnalyseVO>> selectStockAnalyse(@RequestBody StockAgeAnalysePO po, BizContext ctx) {
        reportService.selectStockAnalyse(ctx);
        MultiResultVO<StockAgeAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存积压分析 仓库分组 查询一年以上
     * @param po 入参
     * @param ctx ctx
     * @return 返回库存积压vo
     */
    @ApiOperation(value = "报表-库存积压-仓库号分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-age-analyse/wh-stock-analyse/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockAgeAnalyseVO>> selectStockAnalyseGroupByWh(@RequestBody StockAgeAnalysePO po, BizContext ctx) {
        reportService.selectStockAnalyseGroupByWh(ctx);
        MultiResultVO<StockAgeAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }




    /**
     * 业务凭证查询
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-业务凭证", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-ins-doc-batch/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockInsDocBatchVO>> selectStockInsDocBatch(@RequestBody StockInsDocBatchSearchPO po, BizContext ctx) {
        reportService.selectStockInsDocBatch(ctx);
        PageObjectVO<StockInsDocBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 业务凭证查询导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-业务凭证导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-ins-doc-batch/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockInsDocBatch(@RequestBody StockInsDocBatchSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockInsDocBatch(ctx);
        return BaseResult.success();
    }

    /**
     * 业务类型
     * @param ctx ctx
     * @return 业务类型 vo
     */
    @ApiOperation(value = "报表-业务类型下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-batch/receipt-type-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SpecStockMapVO>> getReceiptTypeList(BizContext ctx) {
        reportService.getReceiptTypeList(ctx);
        MultiResultVO<SpecStockMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 作业查询
     * @param po 入参
     * @param ctx ctx
     * @return 作业vo
     */
    @ApiOperation(value = "报表-作业查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/task/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TaskVO>> selectTask(@RequestBody TaskSearchPO po, BizContext ctx) {
        reportService.selectTask(ctx);
        PageObjectVO<TaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 作业查询导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 作业vo
     */
    @ApiOperation(value = "报表-作业查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/task/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportTask(@RequestBody TaskSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportTask(ctx);
        return BaseResult.success();
    }


    /**
     * 库存对账 全库
     * @param ctx ctx
     * @return 标准返回对象
     */
    @ApiOperation(value = "报表-库存对账提交", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-diff", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> stockDiff(BizContext ctx) {
        reportService.stockDiff();
        return BaseResult.success();
    }

    /**
     * 库存对账查询
     * @param po 入参
     * @param ctx ctx
     * @return 库存对账返回参数
     */
    @ApiOperation(value = "报表-库存对账查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-diff/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockDiffVO>> selectStockDiff(@RequestBody StockDiffSearchPO po, BizContext ctx) {
        reportService.selectStockDiff(ctx);
        PageObjectVO<StockDiffVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存对账查询导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 库存对账返回参数
     */
    @ApiOperation(value = "报表-库存对账查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-diff/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockDiff(@RequestBody StockDiffSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockDiff(ctx);
        return BaseResult.success();
    }

    /**
     * 库存积压分析 库存地点分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回库存积压vo
     */
    @ApiOperation(value = "报表-库存积压-库存地点分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-age-analyse", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockAgeAnalyseLocationVO>> selectStockAnalyseGroupByLocation(@RequestBody StockAgeAnalysePO po, BizContext ctx) {
        reportService.selectStockAnalyseGroupByLocation(ctx);
        MultiResultVO<StockAgeAnalyseLocationVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * erp库存金额分析
     * @param ctx ctx
     * @return 库存金额返回参数
     */
    @ApiOperation(value = "报表-erp库存金额分析", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/erp-stock-money-analyse", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockMoneyAnalyseVO>> erpStockMoneyAnalyse(BizContext ctx) {
        reportService.erpStockMoneyAnalyse(ctx);
        MultiResultVO<StockMoneyAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 出入库统计 7天内
     * @param ctx ctx
     * @return 出入库统计返回参数
     */
    @ApiOperation(value = "报表-出入库统计", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/in-out-analyse", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<InAndOutAnalyseVO>> selectInAndOut(BizContext ctx) {
        reportService.selectInAndOut(ctx);
        MultiResultVO<InAndOutAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * erp库存周转率 最近半年
     * @param ctx ctx
     * @return 出入库统计返回参数
     */
    @ApiOperation(value = "报表-库存周转率", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-turnover", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockTurnoverVO>> selectStockTurnover(@RequestBody StockTurnoverPO po, BizContext ctx) {
        reportService.selectStockTurnover(ctx);
        MultiResultVO<StockTurnoverVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 月末 计算库存周转率
     * @param ctx ctx
     * @return 基本返回对象
     */
    @ApiOperation(value = "报表-计算库存周转率", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/collect-stock-turnover", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult collectStockTurnover(BizContext ctx) {
        reportService.collectStockTurnover();

        return BaseResult.success();
    }

    /**
     * pda仓位库存查询
     * @param ctx ctx
     * @return 仓位库存
     */
    @ApiOperation(value = "报表-pda仓位库存查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/pda-stock-bin/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockBinDTO>> getPdaStockBin(@RequestBody StockBinPdaSearchPO po, BizContext ctx) {
        reportService.getPdaStockBin(ctx);
        MultiResultVO<StockBinDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 重量库存查询
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-重量库存查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-bin-weight/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinWeightVo>> selectStockBinWeightDetail(@RequestBody StockBinWeightSearchPO po, BizContext ctx) {
        reportService.selectStockBinWeightDetail(ctx);
        PageObjectVO<StockBinWeightVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 重量库存查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-重量库存查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/stock-bin-weight/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockBinWeightDetail(@RequestBody StockBinWeightSearchPO po, BizContext ctx) {
        reportService.exportStockBinWeightDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 电子秤报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-电子秤报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/electronic-scale-record/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ElectronicScaleVO>> selectElectronicScaleRecord(@RequestBody ElectronicScalePO po, BizContext ctx) {
        reportService.selectElectronicScaleRecord(ctx);
        PageObjectVO<ElectronicScaleVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 入库台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-入库台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/input-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<UnitizedInputLedgerVo>> selecInputLedgerRecord(@RequestBody UnitizedInputLedgerPO po, BizContext ctx) {
        reportService.selectInputLedgerRecord(ctx);
        PageObjectVO<UnitizedInputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 入库台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-入库台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/input-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportInputLedgerRecord(@RequestBody UnitizedInputLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportInputLedgerRecord(ctx);
        return BaseResult.success();
    }

    /**
     * 入库台账报表同步
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-入库台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/input-ledger/sync", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> syncWarehousingAccount(@RequestBody UnitizedInputLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.syncWarehousingAccount(ctx);
        return BaseResult.success();
    }

    /**
     * 移动类型
     * @param ctx ctx
     * @return 移动类型 vo
     */
    @ApiOperation(value = "报表-移动类型下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/input-ledger/move-type/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<MoveTypeMapVO>> getMoveTypeList(BizContext ctx) {
        reportService.getMoveTypeList(ctx);
        MultiResultVO<MoveTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 直抵现场台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-直抵现场台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/direct-scene/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DirectSceneVo>> selecInputLedgerRecord(@RequestBody DirectScenePO po, BizContext ctx) {
        reportService.selectDirectSceneRecord(ctx);
        PageObjectVO<DirectSceneVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 直抵现场台账查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-直抵现场台账报表查询导出Excel selectDeliveryTrackRecord", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/direct-scene/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportInputLedgerRecord(@RequestBody DirectScenePO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportDirectSceneRecord(ctx);
        return BaseResult.success();
    }

    /**
     * 入库物资跟踪报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-入库物资跟踪报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/delivery-track/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DeliveryTrackVo>> selectDeliveryTrackRecord(@RequestBody DeliveryTrackPO po, BizContext ctx) {
        reportService.selectDeliveryTrackRecord(ctx);
        PageObjectVO<DeliveryTrackVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 入库物资跟踪报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-入库物资跟踪报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/delivery-track/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportDeliveryTrackRecord(@RequestBody DeliveryTrackPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportDeliveryTrackRecord(ctx);
        return BaseResult.success();
    }


    /**
     * 出库台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-出库台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/output-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<UnitizedOutputLedgerVo>> selecOutputLedgerRecord(@RequestBody UnitizedOutputLedgerPO po, BizContext ctx) {
        reportService.selectOutputLedgerRecord(ctx);
        PageObjectVO<UnitizedOutputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 出库台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-出库台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/output-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportOutputLedgerRecord(@RequestBody UnitizedOutputLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportOutputLedgerRecord(ctx);
        return BaseResult.success();
    }

    /**
     * 退旧换新台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-退旧换新台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/return-new-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ReturnNewLedgerVo>> selectReturnNewLedgerRecord(@RequestBody ReturnNewLedgerPO po, BizContext ctx) {
        reportService.selectReturnNewLedgerRecord(ctx);
        PageObjectVO<ReturnNewLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 退旧换新台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-退旧换新台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/return-new-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportReturnNewLedgerRecord(@RequestBody ReturnNewLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportReturnNewLedgerRecord(ctx);
        return BaseResult.success();
    }



    /**
     * 工器具台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-退工器具台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/tool-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ToolLedgerVo>> selectToolLedgerRecord(@RequestBody ToolLedgerPO po, BizContext ctx) {
        reportService.selectToolLedgerRecord(ctx);
        PageObjectVO<ToolLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 工器具台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-工器具台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/tool-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportToolLedgerRecord(@RequestBody ToolLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportToolLedgerRecord(ctx);
        return BaseResult.success();
    }
    /**
     * 寿期台账查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回寿期台账vo
     */
    @ApiOperation(value = "报表-寿期台账详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/lifetime/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<LifetimeVO>> selectLifetimeDetail(@RequestBody LifetimeSearchPO po, BizContext ctx) {
        reportService.selectLifetimeDetail(ctx);
        PageObjectVO<LifetimeVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 寿期台账查询 详情
     *
     * @param po  入参
     * @param ctx ctx
     * @return 返回寿期台账vo
     */
    @ApiOperation(value = "报表-寿期台账详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/lifetime-new/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<LifetimeNewVO>> selectLifetimeDetailNew(@RequestBody LifetimeSearchPO po, BizContext ctx) {
        reportService.selectLifetimeDetailNew(ctx);
        PageObjectVO<LifetimeNewVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 寿期台账导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-寿期台账导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/lifetime/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportLifetimeDetail(@RequestBody LifetimeSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportLifetimeDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 寿期台账导出Excel
     *
     * @param po  入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-寿期台账导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/lifetime-new/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportLifetimeDetailNew(@RequestBody LifetimeSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportLifetimeDetailNew(ctx);
        return BaseResult.success();
    }

    /**
     * 维保台账查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回维保台账vo
     */
    @ApiOperation(value = "报表-维保台账详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/maintain/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MaintainVO>> selectMaintainDetail(@RequestBody MaintainSearchPO po, BizContext ctx) {
        reportService.selectMaintainDetail(ctx);
        PageObjectVO<MaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 维保台账导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-维保台账导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/maintain/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportMaintainDetail(@RequestBody MaintainSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportMaintainDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 上传pdf
     */
    @ApiOperation(value = "上传pdf", notes = "上传pdf", tags = {"上传pdf"})
    @PostMapping(path = "/unitized/report/uploadPdf", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizCommonFile> uploadPdf(@RequestPart("file") MultipartFile file, @RequestPart("po") String po, BizContext ctx) {
        return BaseResult.success(reportService.uploadPdf(ctx));
    }

    /**
     * 定时任务同步DTS
     */
    @ApiOperation(value = "同步DTS", tags = {"同步DTS"})
    @PostMapping(value = "/unitized/report/syncDTS", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> syncDTS(@RequestBody Object po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,po);
        reportService.syncDTS(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "报表-入库办理时效", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/input/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeInputVO>> getTimeInput(@RequestBody TimeInputDTO po, BizContext ctx) {
        reportService.getTimeInput(ctx);
        PageObjectVO<TimeInputVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-入库办理时效-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/input/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeInputVO>> getTimeInputExcel(@RequestBody TimeInputDTO po, BizContext ctx) {
        reportService.getTimeInputExcel(ctx);
        PageObjectVO<TimeInputVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-出库办理时效", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/output/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeOutputVO>> getTimeOutput(@RequestBody TimeOutputDTO po, BizContext ctx) {
        reportService.getTimeOutput(ctx);
        PageObjectVO<TimeOutputVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-出库办理时效-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/output/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeOutputVO>> getTimeOutputExcel(@RequestBody TimeOutputDTO po, BizContext ctx) {
        reportService.getTimeOutputExcel(ctx);
        PageObjectVO<TimeOutputVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-备料下架时效", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/unload/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeUnloadVO>> getTimeUnload(@RequestBody TimeUnloadDTO po, BizContext ctx) {
        reportService.getTimeUnload(ctx);
        PageObjectVO<TimeUnloadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-备料下架时效-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/unload/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeUnloadVO>> getTimeUnloadExcel(@RequestBody TimeUnloadDTO po, BizContext ctx) {
        reportService.getTimeUnloadExcel(ctx);
        PageObjectVO<TimeUnloadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-直抵现场时效", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/direct/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeDirectVO>> getTimeDirect(@RequestBody TimeDirectDTO po, BizContext ctx) {
        reportService.getTimeDirect(ctx);
        PageObjectVO<TimeDirectVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-直抵现场时效-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/time/direct/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TimeDirectVO>> getTimeDirectExcel(@RequestBody TimeDirectDTO po, BizContext ctx) {
        reportService.getTimeDirectExcel(ctx);
        PageObjectVO<TimeDirectVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物资领用质量有效期监控报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/occupy/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OccupyVO>> getOccupy(@RequestBody OccupyDTO po, BizContext ctx) {
        reportService.getOccupy(ctx);
        PageObjectVO<OccupyVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物资领用质量有效期监控报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/occupy/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OccupyVO>> getOccupyDetail(@RequestBody OccupyDTO po, BizContext ctx) {
        reportService.getOccupyDetail(ctx);
        PageObjectVO<OccupyVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物资领用质量有效期监控报表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/occupy/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OccupyVO>> getOccupyExcel(@RequestBody OccupyDTO po, BizContext ctx) {
        reportService.getOccupyExcel(ctx);
        PageObjectVO<OccupyVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-领用多发量统计报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/over/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OverVO>> getOver(@RequestBody OverDTO po, BizContext ctx) {
        reportService.getOver(ctx);
        PageObjectVO<OverVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-领用多发量统计报表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/over/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OverVO>> getOverExcel(@RequestBody OverDTO po, BizContext ctx) {
        reportService.getOverExcel(ctx);
        PageObjectVO<OverVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-领用多发量统计报表-超发", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/over/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OverDetailVO>> getOverDetail(@RequestBody OverDetailDTO po, BizContext ctx) {
        reportService.getOverDetail(ctx);
        PageObjectVO<OverDetailVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-领用多发量统计报表-超发-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/over/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OverDetailVO>> getOverDetailExcel(@RequestBody OverDetailDTO po, BizContext ctx) {
        reportService.getOverDetailExcel(ctx);
        PageObjectVO<OverDetailVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-领用多发量统计报表-冲减", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/subtract/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<SubtractDetailVO>> getSubtractDetail(@RequestBody SubtractDetailDTO po, BizContext ctx) {
        reportService.getSubtractDetail(ctx);
        PageObjectVO<SubtractDetailVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-领用多发量统计报表-冲减-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/subtract/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<SubtractDetailVO>> getSubtractDetailExcel(@RequestBody SubtractDetailDTO po, BizContext ctx) {
        reportService.getSubtractDetailExcel(ctx);
        PageObjectVO<SubtractDetailVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-直抵现场台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/input/direct/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DirectSceneVo>> getInputDirect(@RequestBody DirectScenePO po, BizContext ctx) {
        reportService.getInputDirect(ctx);
        PageObjectVO<DirectSceneVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-直抵现场台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/input/direct/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DirectSceneVo>> getInputDirectExcel(@RequestBody DirectScenePO po, BizContext ctx) {
        reportService.getInputDirectExcel(ctx);
        PageObjectVO<DirectSceneVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-开箱验收跟踪表-物项开箱率查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/inspect/case/rate", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<Map>> getInspectCaseRate(@RequestBody InspectCaseDTO po, BizContext ctx) {
        reportService.getInspectCaseRate(ctx);
        SingleResultVO<Map> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-开箱验收跟踪表-箱件维度", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/inspect/case/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<InspectCaseVO>> getInspectCase(@RequestBody InspectCaseDTO po, BizContext ctx) {
        reportService.getInspectCase(ctx);
        PageObjectVO<InspectCaseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-开箱验收跟踪表-箱件维度导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/inspect/case/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<InspectCaseVO>> getInspectCaseExcel(@RequestBody InspectCaseDTO po, BizContext ctx) {
        reportService.getInspectCaseExcel(ctx);
        PageObjectVO<InspectCaseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-开箱验收跟踪表-物料维度", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/inspect/mat/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<InspectMatVO>> getInspectMat(@RequestBody InspectMatDTO po, BizContext ctx) {
        reportService.getInspectMat(ctx);
        PageObjectVO<InspectMatVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-开箱验收跟踪表-物料维度导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/inspect/mat/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<InspectMatVO>> getInspectMatExcel(@RequestBody InspectMatDTO po, BizContext ctx) {
        reportService.getInspectMatExcel(ctx);
        PageObjectVO<InspectMatVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-NCR统计报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/ncr/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<NcrVO>> getNcr(@RequestBody NcrPO po, BizContext ctx) {
        reportService.getNcr(ctx);
        PageObjectVO<NcrVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-NCR统计报表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/ncr/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<NcrVO>> getNcrExcel(@RequestBody NcrPO po, BizContext ctx) {
        reportService.getNcrExcel(ctx);
        PageObjectVO<NcrVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物料明细查询报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/mat-doc/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MatDocVO>> getMatDocDetail(@RequestBody MatDocSearchPO po, BizContext ctx) {
        reportService.getMatDocDetail(ctx);
        PageObjectVO<MatDocVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-直抵现场台账报表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/mat-doc/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MatDocVO>> getMatDocDetailExcel(@RequestBody MatDocSearchPO po, BizContext ctx) {
        reportService.getMatDocDetailExcel(ctx);
        PageObjectVO<MatDocVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-到货接收跟踪报表-箱件维度", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/arrival-track/case-detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ArrivalTrackVO>> getArrivalTrackCaseDetail(@RequestBody ArrivalTrackSearchPO po, BizContext ctx) {
        reportService.getArrivalTrackCaseDetail(ctx);
        PageObjectVO<ArrivalTrackVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-到货接收跟踪报表-箱件维度导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/arrival-track/case-excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ArrivalTrackVO>> getArrivalTrackDetailCaseExcel(@RequestBody ArrivalTrackSearchPO po, BizContext ctx) {
        reportService.getArrivalTrackDetailCaseExcel(ctx);
        PageObjectVO<ArrivalTrackVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-到货接收跟踪报表-批次维度", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/arrival-track/receipt-detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ArrivalTrackReceiptVO>> getArrivalTrackReceiptDetail(@RequestBody ArrivalTrackSearchPO po, BizContext ctx) {
        reportService.getArrivalTrackReceiptDetail(ctx);
        PageObjectVO<ArrivalTrackReceiptVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-到货接收跟踪报表-批次维度导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/arrival-track/receipt-excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ArrivalTrackReceiptVO>> getArrivalTrackReceiptDetailExcel(@RequestBody ArrivalTrackSearchPO po, BizContext ctx) {
        reportService.getArrivalTrackReceiptDetailExcel(ctx);
        PageObjectVO<ArrivalTrackReceiptVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-GVN统计报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/gvn-inconformity/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<GvnInconformityVO>> getGvnInconformityDetail(@RequestBody GvnInconformitySearchPO po, BizContext ctx) {
        reportService.getGvnInconformityDetail(ctx);
        PageObjectVO<GvnInconformityVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-GVN统计报表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/gvn-inconformity/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<GvnInconformityVO>> getGvnInconformityDetailExcel(@RequestBody GvnInconformitySearchPO po, BizContext ctx) {
        reportService.getGvnInconformityDetailExcel(ctx);
        PageObjectVO<GvnInconformityVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物资返运统计报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/material-return/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MaterialReturnVO>> getMaterialReturnDetail(@RequestBody MaterialReturnSearchPO po, BizContext ctx) {
        reportService.getMaterialReturnDetail(ctx);
        PageObjectVO<MaterialReturnVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物资返运统计报表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/material-return/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MaterialReturnVO>> getMaterialReturnDetailExcel(@RequestBody MaterialReturnSearchPO po, BizContext ctx) {
        reportService.getMaterialReturnDetailExcel(ctx);
        PageObjectVO<MaterialReturnVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-查询物资类型下拉", tags = {"仓储管理-报表"})
    @GetMapping(path = "/unitized/report/unitized-maintain/extend28-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StocktakingTypeMapVO>> getExtend28Down(BizContext ctx) {
        reportService.getExtend28Down(ctx);
        MultiResultVO<StocktakingTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-维护保养统计报表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/unitized-maintain/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<UnitizedMaintainVO>> getUnitizedMaintainDetail(@RequestBody MaintainSearchPO po, BizContext ctx) {
        reportService.getUnitizedMaintainDetail(ctx);
        PageObjectVO<UnitizedMaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-维护保养统计报表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/unitized-maintain/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<UnitizedMaintainVO>> getUnitizedMaintainDetailExcel(@RequestBody MaintainSearchPO po, BizContext ctx) {
        reportService.getUnitizedMaintainDetailExcel(ctx);
        PageObjectVO<UnitizedMaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-暂存物资库存统计表", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/temp-store/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TempStoreVO>> getUnitizedTempStoreDetail(@RequestBody TempStoreSearchPO po, BizContext ctx) {
        reportService.getUnitizedTempStoreDetail(ctx);
        PageObjectVO<TempStoreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-暂存物资库存统计表-导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/unitized/report/temp-store/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TempStoreVO>> getUnitizedTempStoreExcel(@RequestBody TempStoreSearchPO po, BizContext ctx) {
        reportService.getUnitizedTempStoreExcel(ctx);
        PageObjectVO<TempStoreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}
