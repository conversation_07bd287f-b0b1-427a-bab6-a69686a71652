package com.inossem.wms.bizdomain.arrange.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.arrange.dao.BizReceiptArrangeHeadMapper;
import com.inossem.wms.common.model.bizdomain.arrange.entity.BizReceiptArrangeHead;
import com.inossem.wms.common.model.bizdomain.arrange.po.BizReceiptArrangeSearchPo;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 仓库整理单抬头表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Service
public class BizReceiptArrangeHeadDataWrap extends BaseDataWrap<BizReceiptArrangeHeadMapper, BizReceiptArrangeHead> {

    @Autowired
    private BizReceiptArrangeHeadMapper bizReceiptArrangeHeadMapper;
    
    public IPage<BizReceiptArrangeHead> getArrangeHeadList(IPage<BizReceiptArrangeHead> page, BizReceiptArrangeSearchPo po) {
        List<BizReceiptArrangeHead>  pageList = bizReceiptArrangeHeadMapper.getArrangeHeadList(page, po);
        return page.setRecords(pageList);
    }
    public IPage<BizReceiptArrangeHead> getArrangeHeadListUnitized(IPage<BizReceiptArrangeHead> page, BizReceiptArrangeSearchPo po) {
        List<BizReceiptArrangeHead>  pageList = bizReceiptArrangeHeadMapper.getArrangeHeadListUnitized(page, po);
        return page.setRecords(pageList);
    }
}
