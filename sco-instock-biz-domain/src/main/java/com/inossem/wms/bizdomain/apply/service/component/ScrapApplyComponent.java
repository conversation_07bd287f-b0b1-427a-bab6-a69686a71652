package com.inossem.wms.bizdomain.apply.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputBinDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemExportDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 闲置物资申请组件类
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class ScrapApplyComponent {

    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;
    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;
    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;
    @Autowired
    private BizReceiptOutputBinDataWrap bizReceiptOutputBinDataWrap;
    @Autowired
    private OutputComponent outputComponent;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private ApplyCommonComponent applyCommonComponent;

    @Autowired
    private BizReceiptApplyItemDataWrap bizReceiptApplyItemDataWrap;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;

    @Autowired
    private SysUserDataWrap sysUserDataWrap;

    /**
     * 提交校验
     *
     * @param ctx 上下文
     */
    public void checkSubmit(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 参数基本校验
        this.check(ctx);
        // 申请数量是否为0校验
        this.checkItemQtyIsZero(ctx);
        // 申请数是否小于可申请数
        this.checkOperatedQty(headDTO);
        // 校验单据状态
        this.checkReceiptStatus(ctx);
    }

    /**
     * 校验单据状态
     *
     * @param ctx
     */
    public void checkReceiptStatus(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        boolean canSubmit = false;
        if (Objects.isNull(headDTO.getId())) {
            canSubmit = true;
        } else {
            BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(headDTO.getId());
            if (head == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
            }
            if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())
                    || EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(head.getReceiptStatus())) {
                canSubmit = true;
            }
        }
        if (!canSubmit) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_SUBMIT);
        }
    }

    /**
     * 校验行项目qty是否为0
     *
     * @param ctx 上下文
     */
    public void checkItemQtyIsZero(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<String> errorRidList = new ArrayList<>();
        headDTO.getItemList().forEach(itemDTO -> {
            if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                errorRidList.add(itemDTO.getRid());
            }
        });
        if (UtilCollection.isNotEmpty(errorRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_OUTPUT_QTY_ZERO, errorRidList.toString());
        }
    }

    /**
     * 参数基本校验
     *
     * @param ctx 上下文
     */
    public void check(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (Objects.isNull(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 提交前校验物料、库存地点是否冻结
        materialService.checkFreezeMaterial(headDTO.getItemList().stream().map(BizReceiptApplyItemDTO::getMatId)
                .distinct().collect(Collectors.toList()));
        this.checkFreezeOutputLocation(headDTO.getItemList());
    }


    /**
     * 申请时，判断库存地点是否申请冻结
     */
    public void checkFreezeOutputLocation(List<BizReceiptApplyItemDTO> itemDTOList) {
        Set<String> freezeLocationSet = new HashSet<>();
        Set<String> ridSet = new HashSet<>();
        int rid = 1;
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            DicStockLocationDTO locationDto = dictionaryService.getLocationCacheById(itemDTO.getLocationId());
            if (null == locationDto) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_SUCH_STOCK_LOCATION);
            }
            if (locationDto.getFreezeOutput() > 0) {
                String freezeLocation = locationDto.getFtyCode() + "-" + locationDto.getLocationCode();
                freezeLocationSet.add(freezeLocation);
                ridSet.add(String.valueOf(rid++));
            }
        }
        if (!freezeLocationSet.isEmpty()) {
            // 冻结库存地点
            throw new WmsException(EnumReturnMsg.RETURN_CODE_LOCATION_FREEZING_OUTPUT_ITEM, ridSet.toString(), freezeLocationSet.toString());
        }
    }


    /**
     * 申请数是否小于可申请数
     *
     * @param headDTO headDTO
     */
    public void checkOperatedQty(BizReceiptApplyHeadDTO headDTO) {
        // 申请数量大于可申请数量时抛出异常
        for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
            BigDecimal qty = itemDTO.getStockQty();
            if (qty.compareTo(itemDTO.getQty()) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QUANTITY_DIFFERENCES);
            }
        }
    }

    /**
     * 保存单据【非同时模式】
     *
     * @param ctx 上下文
     */
    public void saveReceipt(BizContext ctx) {
        boolean isNew = true;
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        // head处理
        if (UtilNumber.isNotEmpty(id)) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(id);
            // 根据id更新
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
            // item物理删除
            QueryWrapper<BizReceiptApplyItem> queryWrapperItem = new QueryWrapper<>();
            queryWrapperItem.lambda().eq(BizReceiptApplyItem::getHeadId, id);
            bizReceiptApplyItemDataWrap.physicalDelete(queryWrapperItem);
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(createUserId);
            headDTO.setModifyUserId(createUserId);
            headDTO.setReceiptType(headDTO.getReceiptType());
            headDTO.setReceiptStatus(status);
            bizReceiptApplyHeadDataWrap.saveDto(headDTO);
            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }
        // item处理
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        int rid = 1;
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(status);
            itemDto.setCreateTime(createTime);
            itemDto.setCreateUserId(createUserId);
            itemDto.setModifyUserId(user.getId());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 状态变更-已完成
     */
    public void updateStatusSubmitted(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 状态变更-已驳回
     */
    public void updateStatusRejected(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
    }

    /**
     * 状态变更-草稿
     */
    public void updateStatusDraft(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
    }

    /**
     * 删除附件
     *
     * @param ctx 上下文
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        receiptAttachmentService.deleteBizReceiptAttachment(headDTO.getId(), headDTO.getReceiptType());
    }

    /**
     * 更新单据,行项目状态 如不更新单据状态，headDTO参数传null
     *
     * @param headDTO headDTO
     * @param itemDTOList 行项目列表
     * @param status 状态
     */
    public void updateStatus(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList,
                             Integer status) {
        if (Objects.isNull(headDTO)) {
            this.updateItemStatus(itemDTOList, status);
        } else if (CollectionUtils.isEmpty(itemDTOList)) {
            this.updateReceiptStatus(headDTO, status);
        } else if (!CollectionUtils.isEmpty(itemDTOList)) {
            this.updateItemStatus(itemDTOList, status);
            this.updateReceiptStatus(headDTO, status);
        }
    }

    /**
     * 更新行项目状态
     *
     * @param itemDTOList 行项目列表
     * @param status 状态
     */
    public void updateItemStatus(List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach(item -> item.setItemStatus(status));
            bizReceiptApplyItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }

    /**
     * 删除单据【非同时模式】
     *
     * @param ctx 上下文
     */
    public void deleteReceipt(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(id);
        BizReceiptApplyHeadDTO headDTO = UtilBean.deepCopyNewInstance(head, BizReceiptApplyHeadDTO.class);
        CurrentUser user = ctx.getCurrentUser();
        // 删除head
        bizReceiptApplyHeadDataWrap.removeById(id);
        // 删除item
        bizReceiptApplyItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptApplyItem>() {

            {
                eq(BizReceiptApplyItem::getHeadId, id);
            }
        });
        receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "", user.getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 更新单据状态
     *
     * @param headDTO headDTO
     * @param receiptStatus 单据状态
     */
    public void updateReceiptStatus(BizReceiptApplyHeadDTO headDTO, Integer receiptStatus) {
        if (Objects.nonNull(headDTO)) {
            // 单据状态
            headDTO.setReceiptStatus(receiptStatus);
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 提交单据【非同时模式】
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitReceipt(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        applyCommonComponent.saveApply(ctx);
    }


    /**
     * 校验删除
     *
     * @param ctx 上下文
     */
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    /**
     * 获取详情
     *
     * @param ctx 上下文
     */
    public void getInfo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptApplyHeadDTO headDTO = this.getItemListById(headId);
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        headDTO.setItemList(itemDTOList);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 设置按钮
        ButtonVO button = this.setButton(headId);
        // 设置审批按钮权限
        workflowService.setApproveButton(button, ctx.getContextData("taskId"));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), button));
    }


    /**
     * 获取按钮权限
     *
     * @param headId 单据id
     * @return 按钮设置
     */
    public ButtonVO setButton(Long headId) {
        ButtonVO button = new ButtonVO();
        QueryWrapper<BizReceiptApplyHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptApplyHead::getId, headId).eq(BizReceiptApplyHead::getIsDelete, 0);
        BizReceiptApplyHead one = bizReceiptApplyHeadDataWrap.getOne(queryWrapper);
        button.setButtonSave(this.setButtonSave(headId, one));
        button.setButtonDelete(this.setButtonDelete(headId, one));
        button.setButtonSubmit(this.setButtonSubmit(headId, one));
        button.setButtonPost(this.setButtonPost(headId, one));
        button.setButtonRevoke(this.setButtonRevoke(headId, one));
        return button;
    }

    private BizReceiptApplyHeadDTO getItemListById(Long headId) {
        BizReceiptApplyHead BizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(BizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptOutputItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(headDTO.getReceiptType());
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptType(item.getPreReceiptType());
            bizCommonReceiptRelation.setPreReceiptHeadId(item.getPreReceiptHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }



    /**
     * 逻辑删除单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        receiptRelationService.deleteReceiptTree(EnumReceiptType.STOCK_APPLY_SCRAP.getValue(), headId);
    }

    /**
     * 移动类型列表
     */
    public void getMoveTypeList(BizContext ctx) {
        // 移动类型下拉列表
        List<DicMoveType> moveTypeList =
                dictionaryService.getMoveTypeListCacheByReceiptType(EnumReceiptType.STOCK_APPLY_SCRAP.getValue());
        // 返回对象
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(moveTypeList));
    }


    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptApplySearchMatPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setSpecStockCode(null);
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
//        Long matId = null;
//        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
//            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
//            if (Objects.isNull(matId)) {
//                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
//                return;
//            }
//        }

        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        po.setReceiptType(EnumReceiptType.STOCK_APPLY_SCRAP.getValue());
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po,BizReceiptAssembleRuleSearchPO.class);
        // 配置matId
        pos.setSpecStock(dicMoveType.getSpecStock());
//        pos.setMatId(matId);
        pos.setPreMatCode(po.getMatCode());
        pos.setStockStatus(stockStatus);
        pos.setSpecStockCode(StringUtils.isEmpty(po.getSpecStockCode()) ? null : po.getSpecStockCode());
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(),pos);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
        }
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        matStockDTO.setItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * 校验行项目的参数
     * @param ctx
     */
    public void checkSave(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(headDTO)||UtilCollection.isEmpty(headDTO.getItemList())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    /**
     * 撤销流程
     *
     * @param ctx 上下文
     */
    public void revokeProcessInstance(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptApplyHeadDTO headDTO = vo.getHead();
        // 删除待办:必须在审批撤销前
        String id = workflowService.deleteTodo(headDTO.getId());
        // 审批撤销
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(id);
        workflowService.revoke(revokeDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<
            BizReceiptApplyHeadDTO> resultVO =
                new BizResultVO<>(
                    new BizReceiptApplyHeadDTO().setReceiptType(EnumReceiptType.STOCK_APPLY_SCRAP.getValue())
                        .setMoveTypeId(dictionaryService.getMoveTypeListCacheByReceiptType(EnumReceiptType.STOCK_APPLY_SCRAP.getValue()).get(0).getId())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()),
                        new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(false), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    /**
     * 开启附件
     *
     * @param ctx 上下文
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启日志
     *
     * @param ctx 上下文
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 获取申请单列表(分页)
     *
     * @param ctx 上下文
     */
    public void getPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptApplyPageVO> page = po.getPageObj(BizReceiptApplyPageVO.class);
        bizReceiptApplyHeadDataWrap.getOutputPageVoList(page, po);
        long totalCount = page.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), totalCount));
    }


    /**
     * 申请单能否保存
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonSave(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        return false;
    }

    /**
     * 申请单能否删除
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonDelete(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 驳回状态 可以删除
            return true;
        }
        return false;
    }

    /**
     * 申请单能否提交 0否、1是
     *
     * @param headId 单据id
     * @return 申请单能否提交 0否、1是
     */
    public Boolean setButtonSubmit(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }


    /**
     * 申请单能否未同步再次过账 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonPost(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步状态
            return true;
        }
        // 已作业状态
        return EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus);
    }

    /**
     * 申请单能否撤销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonRevoke(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中状态
            return true;
        }
        return false;
    }




    /**
     * 申请单能否核销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonDebtOffset(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus)) {
            // 待核销状态
            return true;
        }
        return false;
    }

    /**
     * 设置批次图片信息
     *
     * @param ctx 上下文
     */
    public void setBatchImg(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        this.setBatchImg(resultVO.getHead().getItemList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    /**
     * 设置批次图片信息
     *
     * @param itemDTOList 行项目
     */
    public void setBatchImg(List<BizReceiptApplyItemDTO> itemDTOList) {
//        Set<Long> matIdSet = itemDTOList.stream().map(BizReceiptApplyItemDTO::getMatId).collect(Collectors.toSet());
//        if (UtilCollection.isNotEmpty(matIdSet)) {
//            Map<Long, List<BizBatchImgDTO>> batchImgMap = batchImgService.getBatchImgListByMatIdList(matIdSet, 4);
//            for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
//                List<BizBatchImgDTO> bizBatchImgDTOS = batchImgMap.get(itemDTO.getMatId());
//                itemDTO.setBatchImgList(bizBatchImgDTOS);
//            }
//        }
    }

    /**
     * 保存日志
     *
     * @param ctx 上下文
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }


    /**
     * 保存单据附件
     *
     * @param ctx 上下文
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    @Async
    public void createScrapOutput(BizContext ctx) {
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptOutputHeadDTO outputHead = new BizReceiptOutputHeadDTO();
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        Date createTime = new Date();
        Long createUserId = applyHeadDTO.getCreateUserId();
        // 新增
        String code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_SCRAP_OUTPUT.getValue());
        outputHead.setId(null);
        outputHead.setReceiptCode(code);
        outputHead.setCreateUserId(createUserId);
        outputHead.setModifyUserId(createUserId);
        outputHead.setReceiptType(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue());
        outputHead.setReceiptStatus(status);
        outputHead.setRepairFty(applyHeadDTO.getRepairFty());
        outputHead.setCreateTime(UtilDate.getNow());
        outputHead.setModifyTime(UtilDate.getNow());
        outputHead.setRemark(applyHeadDTO.getRemark());
        bizReceiptOutputHeadDataWrap.saveDto(outputHead);
        // 单据日志 - 新增
        if (Objects.isNull(operationLogType)) {
            // 设置上下文单据日志 - 创建
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        }
        // item处理
        List<BizReceiptApplyItemDTO> applyItemDTOList = applyHeadDTO.getItemList();
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
        int rid = 1;
        for (BizReceiptApplyItemDTO applyItemDto : applyItemDTOList) {
            BizReceiptOutputItemDTO itemDto = UtilBean.deepCopyNewInstance(applyItemDto, BizReceiptOutputItemDTO.class);
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(outputHead.getId());
            itemDto.setMoveTypeId(applyHeadDTO.getMoveTypeId());
            itemDto.setItemStatus(status);
            itemDto.setCreateTime(createTime);
            itemDto.setCreateUserId(createUserId);
            itemDto.setModifyUserId(createUserId);
            itemDto.setPreReceiptHeadId(applyHeadDTO.getId());
            itemDto.setQty(BigDecimal.ZERO);
            itemDto.setPreReceiptItemId(applyItemDto.getId());
            itemDto.setPreReceiptType(applyHeadDTO.getReceiptType());
            itemDto.setPreReceiptQty(applyItemDto.getStockQty());
            itemDto.setStockQty(applyItemDto.getStockQty());
            itemDto.setPreReceiptQty(applyItemDto.getQty());
            itemDTOList.add(itemDto);
        }
        if(!CollectionUtils.isEmpty(itemDTOList)){
            bizReceiptOutputItemDataWrap.saveBatchDto(itemDTOList);
        }
        outputHead.setItemDTOList(itemDTOList);

        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        BizResultVO<
                BizReceiptOutputHeadDTO> resultVO =
                new BizResultVO<>(
                        outputHead.setCreateTime(UtilDate.getNow()).setCreateUserId(createUserId),
                        new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);


        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,outputHead);
        // 设置单据流
        this.saveReceiptTree(ctx);

        // 开启附件
        outputComponent.setExtendAttachment(ctx);

        // 开启日志
        outputComponent.setExtendOperationLog(ctx);
    }

    /**
     * 根据单据号获取id
     *
     * @param receiptCode
     * @return
     */
    public Long getIdByReceiptCode(String receiptCode) {
        QueryWrapper<BizReceiptTransportHead> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptTransportHead::getReceiptCode, receiptCode);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getOne(wrapper);
        if (Objects.isNull(head)) {
            return Long.valueOf(0);
        } else {
            return head.getId();
        }
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "不符合项处置单"}
     */
    public void startWorkFlow(BizContext ctx) {
        // 发起流程审批
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();

        // 审批校验
        this.approveCheck(ctx.getCurrentUser(), variables);

        // 报废申请：“请审批”[公司+部门]用户姓名+“提交的流程”+报废单描述（取报废申请抬头报废单描述）
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getRemark());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 更新报废申请单 - 审批中
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());
    }

    /**
     * 审批校验
     */
    private void approveCheck(CurrentUser currentUser, Map<String, Object> variables) {
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        List<String> deptCodeList = userDepartment.stream().map(MetaDataDeptOfficePO::getDeptCode).distinct().collect(Collectors.toList());
        if (UtilCollection.isEmpty(deptCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        variables.put("deptCodeList", deptCodeList);

        List<String> userList1 = new ArrayList<>();
        List<String> userList2 = new ArrayList<>();
        List<SysUser> userList3 = new ArrayList<>();
        for (String deptCode : deptCodeList) {
            // 一级审批节点 发起人部门领导
            userList1.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2));
            // 二级审批节点 发起人部门分管领导
            userList2.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3));
        }
        // 三级审批节点 公司领导 李吉根
        userList3 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "10100212"));
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
        if (UtilCollection.isEmpty(userList3)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
        }
        variables.put("userCode", "10100212");
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        ctx.setCurrentUser(wfReceiptCo.getInitiator());
        BizReceiptApplyHeadDTO headDTO = this.getItemListById(wfReceiptCo.getReceiptHeadId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            // 生成报废出库单
            this.createScrapOutputNew(ctx);
        } else {
            // 如果驳回时携带了废弃标记，则直接关闭单据
            if(EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())){
                // 更新状态已关闭
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());
                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", headDTO.getReceiptCode(), "报废申请的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(headDTO.getCreateUserId()).getUserCode()), headDTO.getReceiptCode());
            } else {
                // 更新状态已驳回
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", headDTO.getReceiptCode(), "报废申请的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(headDTO.getCreateUserId()).getUserCode()), headDTO.getReceiptCode());
            }
        }
    }

    public void exportMat(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(UtilExcel.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(UtilExcel.getFileName("报废申请-物料导出"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());

        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<BizReceiptApplyItemExportDTO> exportDTOList = UtilCollection.toList(headDTO.getItemList(),BizReceiptApplyItemExportDTO.class);
        UtilExcel.writeExcel(BizReceiptApplyItemExportDTO.class, exportDTOList, bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void createScrapOutputNew(BizContext ctx) {
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        BizReceiptApplyHeadDTO headDTO = this.getItemListById(po.getId());

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);

        this.createScrapOutput(ctx);
    }

    public void checkFile(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(headDTO.getFileList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FILE_NOT_NULL);
        }
    }
}
