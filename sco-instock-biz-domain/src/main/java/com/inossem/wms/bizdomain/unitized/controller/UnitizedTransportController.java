package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.transport.service.biz.TransportService;
import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedTransportService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.erp.dto.ErpWbs;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 转储
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "转储管理")
public class UnitizedTransportController {

    @Autowired
    private UnitizedTransportService transportService;

    /**
     * 移动类型列表
     *
     * @out vo 移动类型列表
     */
    @ApiOperation(value = "移动类型列表", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/getMoveTypeList")
    public BaseResult getMoveTypeList(BizContext ctx) {
        transportService.getMoveTypeList(ctx);
        MultiResultVO<DicMoveType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 页面初始化
     *
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/init")
    public BaseResult init(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询库存 - 只选到物料
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询库存", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/getStock")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表 - 分页", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/results")
    public BaseResult getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 获取盘点人列表
     *
     * @return 用户列表
     */
    @ApiOperation(value = "获取整理人列表", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/user-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SysUserDTO>> getUserList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getUserList(ctx);
        MultiResultVO<SysUserDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"转储管理"})
    @GetMapping(value = "/unitized/transport/{id}")
    public BaseResult getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 提交
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "提交", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 过账
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "过账", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/post")
    public BaseResult post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"转储管理"})
    @DeleteMapping("/unitized/transport/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 冲销
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "冲销", tags = {"转储管理"})
    @PostMapping("/unitized/transport/write-off")
    public BaseResult writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transportService.writeOff(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 获取WBS集合
     */
    @ApiOperation(value = "获取WBS集合", tags = {"转储管理"})
    @PostMapping(value = "/unitized/transport/wbs")
    public BaseResult getWbsList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getWbsList(ctx);
        MultiResultVO<ErpWbs> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}