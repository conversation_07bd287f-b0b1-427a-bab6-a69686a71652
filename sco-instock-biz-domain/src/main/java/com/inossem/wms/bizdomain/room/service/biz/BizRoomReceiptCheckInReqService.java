package com.inossem.wms.bizdomain.room.service.biz;

import com.inossem.wms.bizdomain.room.service.component.BizRoomReceiptCheckInReqComponent;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckInReqHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 住房申请服务类
 *
 **/
@Slf4j
@Service
public class BizRoomReceiptCheckInReqService {

    @Autowired
    private BizRoomReceiptCheckInReqComponent bizRoomReceiptCheckInReqComponent;

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        bizRoomReceiptCheckInReqComponent.init(ctx);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 分页查询
        bizRoomReceiptCheckInReqComponent.getPage(ctx);
    }

    /**
     * 获取详情数据
     */
    public void getInfo(BizContext ctx) {
        bizRoomReceiptCheckInReqComponent.getInfo(ctx);

        bizRoomReceiptCheckInReqComponent.setExtendWf(ctx);
    }

    /**
     * 保存单据
     */
    @Transactional
    public void saveReceipt(BizContext ctx) {
        // 保存校验
        bizRoomReceiptCheckInReqComponent.checkSaveData(ctx);

        // 保存单据
        bizRoomReceiptCheckInReqComponent.saveReceipt(ctx);

        // 保存单据流
        bizRoomReceiptCheckInReqComponent.saveReceiptTree(ctx);
    }

    /**
     * 提交单据
     */
    @Transactional
    public void submitReceipt(BizContext ctx) {

        // 入参上下文
        BizRoomReceiptCheckInReqHeadDTO po = ctx.getPoContextData();

        // 设置提交信息
        po.setSubmitUserId(ctx.getCurrentUserId());
        po.setSubmitTime(new Date());

        // 保存校验
        bizRoomReceiptCheckInReqComponent.checkSaveData(ctx);

        // 提交校验
        bizRoomReceiptCheckInReqComponent.checkSubmitData(ctx);

        // 保存单据
        bizRoomReceiptCheckInReqComponent.saveReceipt(ctx);

        // 保存单据流
        bizRoomReceiptCheckInReqComponent.saveReceiptTree(ctx);

        // 发起审批流
        bizRoomReceiptCheckInReqComponent.startWorkFlow(ctx);
    }

    /**
     * 删除单据
     */
    @Transactional
    public void deleteReceipt(BizContext ctx) {
        // 删除单据
        bizRoomReceiptCheckInReqComponent.deleteReceipt(ctx);
    }
    /**
     * 审批回调处理
     * 处理工作流审批结果,更新状态
     *
     * @param wfReceiptCo 审批结果信息
     */
    @WmsMQListener(tags = TagConst.APPROVAL_ROOM_CHECK_IN_REQ)
    @In(parameter = "BizApprovalReceiptInstanceRelDTO", required = {"receiptHeadId", "approveStatus"})
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        bizRoomReceiptCheckInReqComponent.approvalCallback(wfReceiptCo);
    }

    /**
     * 撤销单据
     */
    public void revokeReceipt(BizContext ctx) {

        // 撤销单据
        bizRoomReceiptCheckInReqComponent.revokeReceipt(ctx);
    }

}
