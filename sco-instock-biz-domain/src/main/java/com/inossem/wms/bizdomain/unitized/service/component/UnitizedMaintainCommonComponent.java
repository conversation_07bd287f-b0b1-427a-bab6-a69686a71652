package com.inossem.wms.bizdomain.unitized.service.component;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizdomain.maintain.service.datawrap.BizReceiptMaintainHeadDataWrap;
import com.inossem.wms.bizdomain.maintain.service.datawrap.BizReceiptMaintainItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.maintain.EnumMaintenanceType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainHeadDTO;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainItemDTO;
import com.inossem.wms.common.model.bizdomain.maintain.entity.BizReceiptMaintainHead;
import com.inossem.wms.common.model.bizdomain.maintain.entity.BizReceiptMaintainItem;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 维保公共方法 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@Service
public class UnitizedMaintainCommonComponent {

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected BizReceiptMaintainHeadDataWrap bizReceiptMaintainHeadDataWrap;

    @Autowired
    protected BizReceiptMaintainItemDataWrap bizReceiptMaintainItemDataWrap;

    @Autowired
    protected ApprovalService approvalService;

    /**
     * 维保单保存校验
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保单"}
     */
    public void checkSaveMaintain(BizContext ctx) {
        // 获取上下文
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            List<String> errorRidList = new ArrayList<>(headDTO.getItemList().size());
            headDTO.getItemList().forEach(itemDTO -> {
                if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    errorRidList.add(itemDTO.getRid());
                }
            });
            if (UtilCollection.isNotEmpty(errorRidList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_OPERATION_QTY_ZERO, errorRidList.toString());
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 保存维保单
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保单"}
     * @out ctx 出参 {"receiptCode" : "维保单单号"},{@link BizReceiptMaintainHeadDTO : "已保存的维保单}
     */
    public void saveMaintain(BizContext ctx) {
        // 入参上下文 - 要保存的维保单
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        headDTO.setReceiptStatus(UtilNumber.isEmpty(headDTO.getReceiptStatus()) ? receiptStatus : headDTO.getReceiptStatus());
        if(UtilNumber.isEmpty(headDTO.getId())){
            headDTO.setCreateUserId(user.getId());
        }
        headDTO.setModifyUserId(user.getId());
        headDTO.setCreateTime(null);
        headDTO.setModifyTime(UtilDate.getNow());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新维保单
            bizReceiptMaintainHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptMaintainItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptMaintainItem::getHeadId, headDTO.getId());
            bizReceiptMaintainItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            Integer receiptType = headDTO.getReceiptType();
            if (EnumReceiptType.UNITIZED_STOCK_MAINTAIN_PLAN.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SWQUENCE_MAINTAIN_PLAN.getValue());
            } else if (EnumReceiptType.UNITIZED_STOCK_MAINTAIN_RESULT.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_MAINTAIN_RESULT.getValue());
                // 特殊维保
                if (EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(headDTO.getMaintenanceType())) {
                    receiptCode = "SK" + headDTO.getUnit() + "-CWH5" + DateUtil.thisYear() + "-RC-" + DateUtil.format(new Date(), "MM") + bizCommonService.getNextSequenceCodeMonth(EnumSequenceCode.SEQUENCE_UNITIZED_MAINTAIN_REPORT_PLAN.getValue());
                }
            }
            headDTO.setReceiptCode(receiptCode);
            bizReceiptMaintainHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        int rid = 1;
        for (BizReceiptMaintainItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(Integer.toString(rid++));
            itemDTO.setItemStatus(UtilNumber.isEmpty(itemDTO.getItemStatus()) ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue() : itemDTO.getItemStatus());
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        bizReceiptMaintainItemDataWrap.saveBatchDto(headDTO.getItemList());
        /* ********************** item处理结束 *************************/
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的维保单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 更新维保单已完成
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        // 入参上下文 - 要保存的维保单
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头已完成
        UpdateWrapper<BizReceiptMaintainHead> headUpdateWrapper = new UpdateWrapper<BizReceiptMaintainHead>();
        headUpdateWrapper.lambda().set(BizReceiptMaintainHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()).eq(BizReceiptMaintainHead::getId, headDTO.getId());
        bizReceiptMaintainHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目已完成
        UpdateWrapper<BizReceiptMaintainItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptMaintainItem>();
        itemUpdateWrapper.lambda().set(BizReceiptMaintainItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()).eq(BizReceiptMaintainItem::getHeadId, headDTO.getId());
        bizReceiptMaintainItemDataWrap.update(itemUpdateWrapper);
    }

    public void updateStatus(Long receiptId, Integer receiptStatus) {
        // 单据状态
        BizReceiptMaintainHead head = new BizReceiptMaintainHead();
        head.setId(receiptId);
        head.setReceiptStatus(receiptStatus);
        bizReceiptMaintainHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptMaintainItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptMaintainItem::getItemStatus, receiptStatus)
                .eq(BizReceiptMaintainItem::getHeadId, receiptId);
        bizReceiptMaintainItemDataWrap.update(wrapper);
    }

    /**
     * 刪除维保单
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "维保单单号"}
     */
    public void deleteInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置维保单
        BizReceiptMaintainHeadDTO headDTO = UtilBean.newInstance(bizReceiptMaintainHeadDataWrap.getById(headId), BizReceiptMaintainHeadDTO.class);
        // 逻辑删除抬头表
        bizReceiptMaintainHeadDataWrap.removeById(headId);
        // 逻辑删除行项目表
        QueryWrapper<BizReceiptMaintainItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptMaintainItem::getHeadId, headId);
        bizReceiptMaintainItemDataWrap.remove(itemWrapper);
        // 维保单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 维保单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptMaintainHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptMaintainHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptMaintainHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "要保持单据流的维保单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文 - 要保持单据流的维保单
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptMaintainItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation()
                    .setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId())
                    .setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType())
                    .setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "要保持附件的维保单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的维保单
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存维保单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "要保存操作日志的维保单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的维保单
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 删除维保单单据流
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保单"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除维保单单据流
        receiptRelationService.deleteReceiptTree(po.getReceiptType(), po.getId());
    }

    /**
     * 删除维保单附件
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保单"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(po.getId(), po.getReceiptType());
    }

    /**
     * 维保单保存校验
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保单"}
     */
    public void checkSaveMaintainResult(BizContext ctx) {
        // 获取上下文
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO)) {
            Integer maintainType = headDTO.getMaintenanceType();
            Integer maintainTypeNormal = EnumMaintenanceType.DAILY_MAINTAIN.getValue();
            Integer maintainTypePro = EnumMaintenanceType.SPECIAL_MAINTAIN.getValue();
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            List<String> errorRidList = new ArrayList<>(headDTO.getItemList().size());
            headDTO.getItemList().forEach(itemDTO -> {
                // 只校验前端已勾选行项目
                if (UtilNumber.isNotEmpty(itemDTO.getIsChecked())) {
                    if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                        errorRidList.add(itemDTO.getRid());
                    }
                    Date maintainDateNormal = itemDTO.getMaintenanceDateNormal();
                    if (maintainType.equals(maintainTypeNormal) && maintainDateNormal == null) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                    }
                    Date maintainDatePro = itemDTO.getMaintenanceDatePro();
                    if (maintainType.equals(maintainTypePro) && maintainDatePro == null) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                    }
                }
            });
            if (UtilCollection.isNotEmpty(errorRidList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_OPERATION_QTY_ZERO, errorRidList.toString());
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptMaintainHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(true);
        } else {
            // 详情页 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(true);
            if (UtilObject.isNotNull(resultVO.getHead())) {
                List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                resultVO.getHead().setApproveList(approveList);
                if (UtilCollection.isNotEmpty(approveList)) {
                    resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 删除时校验单据状态
     *
     * @param ctx 入参上下文
     */
    public void deleteCheck(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 查询维保单
        BizReceiptMaintainHead head = bizReceiptMaintainHeadDataWrap.getById(headId);
        // 单据校验
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
    }

}
