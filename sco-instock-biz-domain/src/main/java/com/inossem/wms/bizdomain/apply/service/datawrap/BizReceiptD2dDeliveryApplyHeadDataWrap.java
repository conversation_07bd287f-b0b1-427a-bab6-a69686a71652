package com.inossem.wms.bizdomain.apply.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.apply.dao.BizReceiptD2dDeliveryApplyHeadMapper;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptD2dDeliveryApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptD2dDeliveryApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptD2dDeliveryApplySearchPO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/24
 */
@Service
public class BizReceiptD2dDeliveryApplyHeadDataWrap extends BaseDataWrap<BizReceiptD2dDeliveryApplyHeadMapper, BizReceiptD2dDeliveryApplyHead> {

    public IPage<BizReceiptD2dDeliveryApplyHeadDTO> selectPage(IPage<BizReceiptD2dDeliveryApplyHeadDTO> pageData,
                                                               WmsQueryWrapper<BizReceiptD2dDeliveryApplySearchPO> pageWrapper) {
        return pageData.setRecords(this.baseMapper.selectPage(pageData, pageWrapper));
    }

}
