package com.inossem.wms.bizdomain.deliverywaybill.service.biz;

import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.deliverywaybill.service.component.BizReceiptIncomeTaxExemptionComponent;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.dto.BizReceiptIncomeTaxExemptionHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.util.UtilObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 所得税免税服务类
 *
 **/
@Slf4j
@Service
public class BizReceiptIncomeTaxExemptionService {

    @Autowired
    private BizReceiptIncomeTaxExemptionComponent bizReceiptIncomeTaxExemptionComponent;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        bizReceiptIncomeTaxExemptionComponent.init(ctx);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 分页查询
        bizReceiptIncomeTaxExemptionComponent.getPage(ctx);
    }

    /**
     * 获取详情数据
     */
    public void getInfo(BizContext ctx) {
        bizReceiptIncomeTaxExemptionComponent.getInfo(ctx);
    }

    /**
     * 保存单据
     */
    @Transactional
    public void saveReceipt(BizContext ctx) {
        // 保存校验
        bizReceiptIncomeTaxExemptionComponent.checkSaveData(ctx);

        // 保存单据
        bizReceiptIncomeTaxExemptionComponent.saveReceipt(ctx);
    }

    /**
     * 提交单据
     */
    @Transactional
    public void submitReceipt(BizContext ctx) {

        // 入参上下文
        BizReceiptIncomeTaxExemptionHeadDTO po = ctx.getPoContextData();

        // 设置提交信息
        po.setSubmitUserId(ctx.getCurrentUserId());
        po.setSubmitTime(new Date());

        // 保存校验
        bizReceiptIncomeTaxExemptionComponent.checkSaveData(ctx);

        // 提交校验
        bizReceiptIncomeTaxExemptionComponent.checkSubmitData(ctx);

        // 待处理状态的提交,记录完成时间
        if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(po.getReceiptStatus())){
            po.setCompleteTime(new Date());
        }

        // 保存单据
        bizReceiptIncomeTaxExemptionComponent.saveReceipt(ctx);

        // 草稿状态的提交，更新状态为待赎单
        if(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())){
            // 更新状态为待处理
            bizReceiptIncomeTaxExemptionComponent.updateStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE);

            // 发送OA待办
            hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", po.getReceiptCode(), "所得税免税的待处理"), StringUtils.EMPTY, po.getId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(po.getHandleUserId()).getUserCode()), po.getReceiptCode());

        } else if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(po.getReceiptStatus())){

            // 待处理状态的提交，更新状态为已完成
            bizReceiptIncomeTaxExemptionComponent.updateStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED);

            // 办结OA待办
            SysUser handleUser = dictionaryService.getSysUserCacheById(po.getHandleUserId());
            if (UtilObject.isNotNull(handleUser)) {
                hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, po.getId().toString(), Arrays.asList(handleUser.getUserCode()), po.getReceiptCode());
            }
        }
    }

    /**
     * 删除单据
     */
    @Transactional
    public void deleteReceipt(BizContext ctx) {
        // 删除单据
        bizReceiptIncomeTaxExemptionComponent.deleteReceipt(ctx);
    }

    /**
     * 撤销单据
     */
    public void revoke(BizContext ctx) {
        // 撤销单据
        bizReceiptIncomeTaxExemptionComponent.revokeReceipt(ctx);

        BizReceiptIncomeTaxExemptionHeadDTO po = ctx.getPoContextData();

        // 办结OA待办
        SysUser handleUser = dictionaryService.getSysUserCacheById(po.getHandleUserId());
        if (UtilObject.isNotNull(handleUser)) {
            hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, po.getId().toString(), Arrays.asList(handleUser.getUserCode()), po.getReceiptCode());
        }
    }

    /**
     * 获取可以创建所得税免税的送货通知单
     */
    public void selectDeliveryNoticeList(BizContext ctx) {
        bizReceiptIncomeTaxExemptionComponent.selectDeliveryNoticeList(ctx);
    }
}
