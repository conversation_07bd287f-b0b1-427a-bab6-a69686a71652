package com.inossem.wms.bizdomain.planeTicket.service.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizdomain.planeTicket.service.datawrap.BizReceiptPlaneTicketHeadDataWrap;
import com.inossem.wms.bizdomain.planeTicket.service.datawrap.BizReceiptPlaneTicketItemDataWrap;
import com.inossem.wms.bizdomain.planeTicket.service.datawrap.BizReceiptPlaneTicketOtherDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.planeTicket.dto.BizReceiptPlaneTicketHeadDTO;
import com.inossem.wms.common.model.bizdomain.planeTicket.dto.BizReceiptPlaneTicketItemDTO;
import com.inossem.wms.common.model.bizdomain.planeTicket.dto.BizReceiptPlaneTicketItemExcelDTO;
import com.inossem.wms.common.model.bizdomain.planeTicket.entity.BizReceiptPlaneTicketHead;
import com.inossem.wms.common.model.bizdomain.planeTicket.entity.BizReceiptPlaneTicketItem;
import com.inossem.wms.common.model.bizdomain.planeTicket.entity.BizReceiptPlaneTicketOther;
import com.inossem.wms.common.model.bizdomain.planeTicket.po.BizReceiptPlaneTicketHeadPO;
import com.inossem.wms.common.model.bizdomain.planeTicket.vo.BizReceiptPlaneTicketHeadVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Service
@Slf4j
public class PlaneTicketComponent {

    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    protected BizCommonService bizCommonService;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    protected BizReceiptPlaneTicketHeadDataWrap bizReceiptPlaneTicketHeadDataWrap;
    @Autowired
    protected BizReceiptPlaneTicketItemDataWrap bizReceiptPlaneTicketItemDataWrap;
    @Autowired
    protected BizReceiptPlaneTicketOtherDataWrap bizReceiptPlaneTicketOtherDataWrap;
    @Autowired
    protected DicSupplierDataWrap dicSupplierDataWrap;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptPlaneTicketHeadDTO().setReceiptType(EnumReceiptType.PLANE_TICKET.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName())
                        .setSupplierList(dicSupplierDataWrap.list().stream()
                                .filter(supplier -> EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue()
                                        .equals(supplier.getReceiptStatus()))
                                .collect(Collectors.toList())),
                        new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 初始化-结算
     */
    public void initSettlement(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptPlaneTicketHeadDTO().setReceiptType(EnumReceiptType.PLANE_TICKET_SETTLEMENT.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName())
                        .setSupplierList(dicSupplierDataWrap.list().stream()
                                .filter(supplier -> EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue()
                                        .equals(supplier.getReceiptStatus()))
                                .collect(Collectors.toList())),
                        new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 分页
     */
    public void getPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptPlaneTicketHeadPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(po.getReceiptStatusList())) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(new ArrayList<>(), 0L));
            return;
        }
        // 排序特殊处理
        if(po.getDescSortColumn().contains("supplier_code")){
            po.setDescSortColumn(po.getDescSortColumn().replace("supplier_code", "supplierId"));
        }
        if(po.getDescSortColumn().contains("supplier_name")){
            po.setDescSortColumn(po.getDescSortColumn().replace("supplier_name", "supplierId"));
        }
        if(po.getAscSortColumn().contains("supplier_code")){
            po.setAscSortColumn(po.getAscSortColumn().replace("supplier_code", "supplierId"));
        }
        if(po.getAscSortColumn().contains("supplier_name")){
            po.setAscSortColumn(po.getAscSortColumn().replace("supplier_name", "supplierId"));
        }
        // 分页查询处理
        IPage<BizReceiptPlaneTicketHead> page = po.getPageObj(BizReceiptPlaneTicketHead.class);
        QueryWrapper<BizReceiptPlaneTicketHead> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .eq(BizReceiptPlaneTicketHead::getReceiptType, po.getReceiptType())
                .in(BizReceiptPlaneTicketHead::getReceiptStatus, po.getReceiptStatusList())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptPlaneTicketHead::getReceiptCode, po.getReceiptCode())
                .orderByDesc(BizReceiptPlaneTicketHead::getReceiptCode);
        bizReceiptPlaneTicketHeadDataWrap.page(page, queryWrapper);
        List<BizReceiptPlaneTicketHeadVO> list = UtilCollection.toList(page.getRecords(), BizReceiptPlaneTicketHeadVO.class);
        dataFillService.fillAttr(list);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(list, page.getTotal()));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptPlaneTicketHead bizReceiptPlaneTicketHead = bizReceiptPlaneTicketHeadDataWrap.getById(headId);
        BizReceiptPlaneTicketHeadDTO dto = UtilBean.newInstance(bizReceiptPlaneTicketHead, BizReceiptPlaneTicketHeadDTO.class);
        dataFillService.fillAttr(dto);
        dto.setSupplierList(dicSupplierDataWrap.list().stream()
                .filter(supplier -> EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue()
                        .equals(supplier.getReceiptStatus()))
                .collect(Collectors.toList()));
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(dto.getReceiptStatus())) {
            // 草稿 -【保存、提交、删除】
            buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);

        } else if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue().equals(dto.getReceiptStatus())) {
            // 待结算 -【结算 】
            buttonVO.setButtonConfirmSettlement(true);
        }
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(dto, new ExtendVO(), buttonVO));
    }

    /**
     * 未结算的人员查询
     */
    public void getItemList(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptPlaneTicketHeadPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取itemList
        List<BizReceiptPlaneTicketItem> itemList = bizReceiptPlaneTicketItemDataWrap.list(new QueryWrapper<BizReceiptPlaneTicketItem>().lambda()
                .eq(BizReceiptPlaneTicketItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue())
                .eq(BizReceiptPlaneTicketItem::getSupplierId, po.getSupplierId())
        );
        List<BizReceiptPlaneTicketItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptPlaneTicketItemDTO.class);
        itemDTOList.forEach(o -> o.setPreHeadId(o.getHeadId()).setPreItemId(o.getId()).setId(null).setHeadId(null));
        dataFillService.fillAttr(itemDTOList);
        itemDTOList.stream().filter(o->o.getReceiptType().equals(EnumReceiptType.PLANE_TICKET.getValue()));
        BizReceiptPlaneTicketHeadDTO dto = new BizReceiptPlaneTicketHeadDTO();
        dto.setItemDTOList(itemDTOList);
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(dto, new ExtendVO(), new ButtonVO()));
    }

    /**
     * 保存
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptPlaneTicketHeadDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        dto.setCreateUserId(user.getId());
        dto.setModifyUserId(user.getId());
        dto.setCreateTime(null);
        dto.setModifyTime(UtilDate.getNow());
        dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        if (UtilNumber.isNotEmpty(dto.getId())) {
            // 更新
            bizReceiptPlaneTicketHeadDataWrap.updateDtoById(dto);

        } else {
            String receiptCode = "";
            if (dto.getReceiptType().equals(EnumReceiptType.PLANE_TICKET.getValue())) {
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PLANE_TICKET.getValue());
            } else if (dto.getReceiptType().equals(EnumReceiptType.PLANE_TICKET_SETTLEMENT.getValue())) {
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PLANE_TICKET.getValue());
            }
            dto.setReceiptCode(receiptCode);
            // 新增
            bizReceiptPlaneTicketHeadDataWrap.saveDto(dto);
        }
        if (UtilCollection.isNotEmpty(dto.getItemDTOList())) {
            AtomicInteger rid = new AtomicInteger(1);
            dto.getItemDTOList().forEach(o -> {
                if(UtilNumber.isEmpty(o.getId())){
                    o.setId(null);
                }
            });
            dto.getItemDTOList().forEach(o -> o.setHeadId(dto.getId())
                    .setRid(Integer.toString(rid.getAndIncrement()))
                    .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()));
            bizReceiptPlaneTicketItemDataWrap.saveOrUpdateBatchDto(dto.getItemDTOList());
        }
        if (UtilCollection.isNotEmpty(dto.getOtherDTOList())) {
            AtomicInteger rid = new AtomicInteger(1);
            dto.getOtherDTOList().forEach(o -> o.setHeadId(dto.getId()).setRid(Integer.toString(rid.getAndIncrement())));
            bizReceiptPlaneTicketOtherDataWrap.saveOrUpdateBatchDto(dto.getOtherDTOList());
        }
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, dto.getId());
    }

    /**
     * 提交待结算
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptPlaneTicketHeadDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 行项目待结算
        bizReceiptPlaneTicketItemDataWrap.update(new UpdateWrapper<BizReceiptPlaneTicketItem>().lambda()
                .set(BizReceiptPlaneTicketItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue())
                .eq(BizReceiptPlaneTicketItem::getHeadId, dto.getId()));
        // 单据已完成
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue();
        if (dto.getReceiptType().equals(EnumReceiptType.PLANE_TICKET_SETTLEMENT.getValue())) {
            receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue();
        }
        bizReceiptPlaneTicketHeadDataWrap.update(new UpdateWrapper<BizReceiptPlaneTicketHead>().lambda()
                .set(BizReceiptPlaneTicketHead::getReceiptStatus, receiptStatus)
                .eq(BizReceiptPlaneTicketHead::getId, dto.getId()));
    }

    /**
     * 结算
     */
    public void settlement(BizContext ctx) {
        // 入参上下文
        BizReceiptPlaneTicketHeadDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 已结算
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_SETTLEMENT_COMPLETED.getValue();
        bizReceiptPlaneTicketItemDataWrap.update(new UpdateWrapper<BizReceiptPlaneTicketItem>().lambda()
                .set(BizReceiptPlaneTicketItem::getItemStatus, status)
                .eq(BizReceiptPlaneTicketItem::getHeadId, dto.getId()));
        bizReceiptPlaneTicketHeadDataWrap.update(new UpdateWrapper<BizReceiptPlaneTicketHead>().lambda()
                .set(BizReceiptPlaneTicketHead::getReceiptStatus, status)
                .eq(BizReceiptPlaneTicketHead::getId, dto.getId()));
        // 结算表前序订单表
        List<Long> preItemIdList = dto.getItemDTOList().stream().map(o -> o.getPreItemId()).collect(Collectors.toList());
        bizReceiptPlaneTicketItemDataWrap.update(new UpdateWrapper<BizReceiptPlaneTicketItem>().lambda()
                .set(BizReceiptPlaneTicketItem::getItemStatus, status)
                .in(BizReceiptPlaneTicketItem::getId, preItemIdList));
    }

    /**
     * 删除
     */
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // other删除
        bizReceiptPlaneTicketOtherDataWrap.physicalDelete(new QueryWrapper<BizReceiptPlaneTicketOther>().lambda().eq(BizReceiptPlaneTicketOther::getHeadId, headId));
        // item删除
        bizReceiptPlaneTicketItemDataWrap.physicalDelete(new QueryWrapper<BizReceiptPlaneTicketItem>().lambda().eq(BizReceiptPlaneTicketItem::getHeadId, headId));
        // head删除
        bizReceiptPlaneTicketHeadDataWrap.physicalDeleteById(headId);
    }

    /**
     * 导入
     */
    public void importExcel(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        List<BizReceiptPlaneTicketItemExcelDTO> excelList;
        try {
            //获取EXCEL数据
            excelList = (List<BizReceiptPlaneTicketItemExcelDTO>) UtilExcel.readExcelData(file.getInputStream(), BizReceiptPlaneTicketItemExcelDTO.class);
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
        if (UtilCollection.isEmpty(excelList)) {
            return;
        }

        List<BizReceiptPlaneTicketItemDTO> itemDTOList = new ArrayList<>();
        for (BizReceiptPlaneTicketItemExcelDTO excelDTO : excelList) {
            BizReceiptPlaneTicketItemDTO itemDto = UtilBean.newInstance(excelDTO, BizReceiptPlaneTicketItemDTO.class);
            DicSupplier dicSupplier = dicSupplierDataWrap.getOne(new QueryWrapper<DicSupplier>().lambda().eq(DicSupplier::getSupplierCode, itemDto.getSupplierCode()));
            if (null != dicSupplier) {
                itemDto.setSupplierCode(dicSupplier.getSupplierCode());
                itemDto.setSupplierName(dicSupplier.getSupplierName());
                itemDto.setSupplierId(dicSupplier.getId());
            } else {
                throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "结算单位不存在");
            }
            itemDTOList.add(itemDto);
        }

        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptPlaneTicketHeadDTO().setReceiptType(EnumReceiptType.PLANE_TICKET.getValue()).setItemDTOList(itemDTOList)
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()),
                        new ExtendVO(), buttonVO);
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 导出
     */
    public void exportExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("机票订单导出"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        // 入参上下文
        BizReceiptPlaneTicketHeadDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        UtilExcel.writeExcel(BizReceiptPlaneTicketItemExcelDTO.class, dto.getItemDTOList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }
}
