package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedLeisureApplyService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 成套设备 闲置物资申请 Controller
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "成套设备-闲置申请")
public class UnitizedLeisureApplyController {

    @Autowired
    private UnitizedLeisureApplyService leisureApplyService;

    @ApiOperation(value = "闲置申请创建", tags = {"成套设备-闲置申请"})
    @PostMapping(value = "/unitized/leisure/apply/init")
    public BaseResult<?> init(BizContext ctx) {
        leisureApplyService.init(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "闲置申请查询列表（分页）", tags = {"成套设备-闲置申请"})
    @PostMapping(value = "/unitized/leisure/apply/results")
    public BaseResult<PageObjectVO<BizReceiptApplyPageVO>> getPage(@RequestBody BizReceiptApplySearchPO po, BizContext ctx) {
        leisureApplyService.getPage(ctx);
        PageObjectVO<BizReceiptApplyPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "闲置申请详情", tags = {"成套设备-闲置申请"})
    @GetMapping(value = "/unitized/leisure/apply/{id}")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        leisureApplyService.getInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "闲置申请保存", tags = {"成套设备-闲置申请"})
    @PostMapping(value = "/unitized/leisure/apply/save")
    public BaseResult save(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        leisureApplyService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "闲置申请单提交", tags = {"成套设备-闲置申请"})
    @PostMapping(value = "/unitized/leisure/apply/submit")
    public BaseResult submit(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        leisureApplyService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "闲置申请删除", tags = {"成套设备-闲置申请"})
    @DeleteMapping(value = "/unitized/leisure/apply/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        leisureApplyService.delete(ctx);
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_DELETE_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "闲置申请查询", tags = {"成套设备-闲置申请"})
    @PostMapping(value = "/unitized/leisure/apply/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        leisureApplyService.getMatStock(ctx);
        MatStockDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "闲置申请导入", tags = {"闲置管理-闲置申请导入"})
    @PostMapping(value = "/unitized/leisure/apply/import")
    public BaseResult<MatStockDTO> importMatStock(@RequestPart("file") MultipartFile file, BizContext ctx) {
        leisureApplyService.importMatStock(ctx);
        MatStockDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}
