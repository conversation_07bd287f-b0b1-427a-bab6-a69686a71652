package com.inossem.wms.bizdomain.room.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptAllocationHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptAllocationItemDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptCheckInReqHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptCheckInReqItemDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomUsageHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomUsageItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.room.EnumRoomReqType;
import com.inossem.wms.common.enums.room.EnumRoomUserNationality;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.approval.entity.BizApprovalReceiptInstanceRel;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckInReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckInReqItemDTO;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoom;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptAllocationHead;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptAllocationItem;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptCheckInReqHead;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptCheckInReqItem;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomUsageHead;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomUsageItem;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomReceiptSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptCapitalPlanHeadDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilReflect;
import com.inossem.wms.common.util.UtilSequence;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import com.inossem.wms.system.workflow.service.business.datawrap.BizApprovalReceiptInstanceRelDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 住房申请组件库
 *
 **/
@Slf4j
@Component
public class BizRoomReceiptCheckInReqComponent {

    @Autowired
    private BizRoomDataWrap bizRoomDataWrap;

    @Autowired
    private BizRoomUsageHeadDataWrap bizRoomUsageHeadDataWrap;

    @Autowired
    private BizRoomUsageItemDataWrap bizRoomUsageItemDataWrap;

    @Autowired
    private BizRoomReceiptCheckInReqHeadDataWrap bizRoomReceiptCheckInReqHeadDataWrap;

    @Autowired
    private BizRoomReceiptCheckInReqItemDataWrap bizRoomReceiptCheckInReqItemDataWrap;

    @Autowired
    private BizRoomReceiptAllocationHeadDataWrap bizRoomReceiptAllocationHeadDataWrap;

    @Autowired
    private BizRoomReceiptAllocationItemDataWrap bizRoomReceiptAllocationItemDataWrap;

    @Autowired
    private BizApprovalReceiptInstanceRelDataWrap bizApprovalReceiptInstanceRelDataWrap;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;

    @Autowired
    private ApprovalService approvalService;

    /**
     * 页面初始化
     *
     */
    public void init(BizContext ctx) {
        BizRoomReceiptCheckInReqHeadDTO headDTO = new BizRoomReceiptCheckInReqHeadDTO()
                .setReceiptType(EnumReceiptType.ROOM_CHECK_IN_REP.getValue())
                .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName());

        BizResultVO<BizRoomReceiptCheckInReqHeadDTO> resultVO = new BizResultVO<>(
                headDTO
                , ExtendVO.getDefaultInstance()
                , this.setButton(headDTO));

        // 设置页面初始化数据到上下文
        ctx.setVoContextData(resultVO);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizRoomReceiptSearchPO po = ctx.getPoContextData();

        if (UtilCollection.isEmpty(po.getReceiptStatusList())) {
            ctx.setVoContextData(new PageObjectVO<BizRoomReceiptCheckInReqHeadDTO>(new ArrayList(), 0L));
            return;
        }

        // 分页处理
        IPage<BizRoomReceiptCheckInReqHead> page = po.getPageObj(BizRoomReceiptCheckInReqHead.class);

        // 分页查询
        bizRoomReceiptCheckInReqHeadDataWrap.page(page, new LambdaQueryWrapper<BizRoomReceiptCheckInReqHead>()
                .in(BizRoomReceiptCheckInReqHead::getReceiptStatus, po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizRoomReceiptCheckInReqHead::getReceiptCode, po.getReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUser()), BizRoomReceiptCheckInReqHead::getCreateUserName, po.getCreateUser())
                .ge(po.getCreateTimeStart() != null, BizRoomReceiptCheckInReqHead::getCreateTime, UtilDate.getStartOfDay(po.getCreateTimeStart()))
                .le(po.getCreateTimeEnd() != null, BizRoomReceiptCheckInReqHead::getCreateTime, UtilDate.getEndOfDay(po.getCreateTimeEnd()))
                .orderByDesc(BizRoomReceiptCheckInReqHead::getCreateTime)
        );

        List<BizRoomReceiptCheckInReqHeadDTO> dtoList = UtilCollection.toList(page.getRecords(), BizRoomReceiptCheckInReqHeadDTO.class);

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getIdContextData();

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取抬头DTO
        BizRoomReceiptCheckInReqHeadDTO headDto = bizRoomReceiptCheckInReqHeadDataWrap.getDtoById(BizRoomReceiptCheckInReqHeadDTO.class, headId);

        // 添加单据流关联关系
        headDto.setRelationList(receiptRelationService.getReceiptTree(headDto.getReceiptType(), headDto.getId(), null));

        // 添加审批记录
        headDto.setApproveList(bizApprovalReceiptInstanceRelDataWrap.getApproveRecord(headDto.getReceiptCode()));

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDto);

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        // 设置详情到上下文
        ctx.setVoContextData(new BizResultVO<>(headDto, ExtendVO.getDefaultInstance(), buttonVO));
    }

    /**
     * <AUTHOR>
     *
     * 设置审批流
     *
     * @param ctx
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizRoomReceiptCheckInReqHeadDTO> resultVO = ctx.getVoContextData();

        // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.CAPITAL_PLAN.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
    }

    /**
     * 保存校验
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckInReqHeadDTO po = ctx.getPoContextData();
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

        // 入住人员国籍、申请类型、房间使用开始时间
        if(UtilNumber.isEmpty(po.getCheckInUserNationality())
                || UtilNumber.isEmpty(po.getReqType())
                || (po.getStartUsageTime() == null && EnumRoomReqType.REQ_ROOM.getValue().equals(po.getReqType()))){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 只记录日期，清空时间
        po.setStartUsageTime(UtilDate.getStartOfDay(po.getStartUsageTime()));

        // 现有房间情况下申请房间id不能为空
        if(EnumRoomReqType.CURRENT_ROOM.getValue().equals(po.getReqType()) && UtilNumber.isEmpty(po.getReqRoomId())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 当“外部供应商用户”，申请类型为“申请房间”时，必须选择合同
        if((!EnumRealYn.TRUE.getIntValue().equals(po.getIsInternalUsage())) && EnumRoomReqType.REQ_ROOM.getValue().equals(po.getReqType()) && UtilNumber.isEmpty(po.getContractId())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 行项目数据校验
        if(UtilCollection.isEmpty(po.getItemList())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

        for (BizRoomReceiptCheckInReqItemDTO item : po.getItemList()) {

            // 对身份证/护照图片值进行收集再设置
            this.resetImgFieldValue(item, "identificationImgId");

            // 对身份证/护照图片值进行收集再设置
            this.resetImgFieldValue(item, "visaImgId");

            // 姓名、性别必填
            if(UtilString.isNullOrEmpty(item.getCheckInUserName())
                    || UtilNumber.isEmpty(item.getCheckInUserSex())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }

            // 只记录日期，清空时间
            item.setCheckInTime(item.getCheckInTime() == null ? null : UtilDate.getStartOfDay(item.getCheckInTime()));

            // 内部使用的无需进行后续校验
            if(EnumRealYn.TRUE.getIntValue().equals(po.getIsInternalUsage())){
                continue;
            }

            // 身份证、入住时间必填
            if(UtilString.isNullOrEmpty(item.getCheckInUserIdNumber()) || item.getCheckInTime() == null){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }

            // 中国国籍时校验
            if(EnumRoomUserNationality.CHINA.getValue().equals(po.getCheckInUserNationality())){
                // 护照、护照图片、签证图片必填
                if(UtilString.isNullOrEmpty(item.getCheckInUserPassportNumber())
                        || UtilNumber.isEmpty(item.getIdentificationImgId1())
                        || UtilNumber.isEmpty(item.getVisaImgId1())){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
            } else {
                // 其他国籍身份证图片必填
                if(UtilNumber.isEmpty(item.getIdentificationImgId1())){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
            }
        }
    }

    /**
     * 提交校验
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckInReqHeadDTO po = ctx.getPoContextData();

        // 申请房间的情况下，清空申请房间id和申请房间编号
        if(EnumRoomReqType.REQ_ROOM.getValue().equals(po.getReqType())){

            po.setReqRoomId(0L);
            po.setReqRoomCode(Const.STRING_EMPTY);

            return;
        }

        // 现有房间情况下才需要校验房间人数
        // 获取房间信息
        BizRoom bizRoom = bizRoomDataWrap.getById(po.getReqRoomId());

        // 冗余存储房间编号
        po.setReqRoomCode(bizRoom.getRoomCode());

        // 获取房间当前使用信息
        BizRoomUsageHead bizRoomUsageHead = bizRoomUsageHeadDataWrap.getById(bizRoom.getCurrentRoomUsageHeadId());

        // 已超出房间入住人数，请重新选择
        if(bizRoom.getBedCount() < bizRoomUsageHead.getCheckInCount() + po.getItemList().size()){
            throw new WmsException(EnumReturnMsg.NO_MSG_1879);
        }
    }

    /**
     * 保存单据
     */
    public void saveReceipt(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckInReqHeadDTO headDto = ctx.getPoContextData();

        // 单据类型
        headDto.setReceiptType(EnumReceiptType.ROOM_CHECK_IN_REP.getValue());

        // 单据状态
        headDto.setReceiptStatus(UtilNumber.isEmpty(headDto.getReceiptStatus()) || EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(headDto.getReceiptStatus()) ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue() : headDto.getReceiptStatus());

        // id为空则是新增数据，id有值则是修改数据
        if(UtilNumber.isEmpty(headDto.getId())){
            headDto.setId(null);
            headDto.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.ROOM_CHECK_IN_REQ.getValue()));
        }

        // 保存抬头信息
        bizRoomReceiptCheckInReqHeadDataWrap.saveOrUpdateDto(headDto);

        AtomicInteger rid = new AtomicInteger(1);

        for (BizRoomReceiptCheckInReqItemDTO itemDto : headDto.getItemList()) {
            itemDto.setId(UtilNumber.isEmpty(itemDto.getId()) ? UtilSequence.nextId() : itemDto.getId());
            itemDto.setHeadId(headDto.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(headDto.getReceiptStatus());
        }

        // 删除原有数据库中的行项目数据，删除后重新保存
        bizRoomReceiptCheckInReqItemDataWrap.physicalDelete(new LambdaQueryWrapper<BizRoomReceiptCheckInReqItem>()
                .eq(BizRoomReceiptCheckInReqItem::getHeadId, headDto.getId())
        );

        // 保存行项目列表
        bizRoomReceiptCheckInReqItemDataWrap.saveBatchDto(headDto.getItemList());

        // 保存单据附件
        receiptAttachmentService.saveBizReceiptAttachment(headDto.getFileList(), headDto.getId(), headDto.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * <AUTHOR>
     *
     * 保存单据流
     *
     * @param ctx
     */
    public void saveReceiptTree(BizContext ctx) {
        BizRoomReceiptCheckInReqHeadDTO headDto = ctx.getPoContextData();

        List<BizCommonReceiptRelation> relationList = new ArrayList<>();
        for (BizRoomReceiptCheckInReqItemDTO itemDto : headDto.getItemList()) {
            BizCommonReceiptRelation relation = new BizCommonReceiptRelation();
            relation.setReceiptType(headDto.getReceiptType());
            relation.setReceiptHeadId(itemDto.getHeadId());
            relation.setReceiptItemId(itemDto.getId());
            relation.setPreReceiptType(null);
            relation.setPreReceiptHeadId(0L);
            relation.setPreReceiptItemId(0L);
            relationList.add(relation);
        }

        receiptRelationService.multiSaveReceiptTree(relationList);
    }

    /**
     * 删除单据
     */
    public void deleteReceipt(BizContext ctx) {
        Long receiptHeadId = ctx.getIdContextData();

        // 获取单据信息
        BizRoomReceiptCheckInReqHead bizRoomReceiptCheckInReqHead = bizRoomReceiptCheckInReqHeadDataWrap.getById(receiptHeadId);

        // 只有草稿状态的单据才可删除
        if(!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(bizRoomReceiptCheckInReqHead.getReceiptStatus())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_CAN_NOT_DELETE);
        }

        // 删除单据流
        receiptRelationService.deleteReceiptTree(bizRoomReceiptCheckInReqHead.getReceiptType(), receiptHeadId);

        // 删除附件
        receiptAttachmentService.deleteBizReceiptAttachment(receiptHeadId, bizRoomReceiptCheckInReqHead.getReceiptType());

        // 删除行项目
        bizRoomReceiptCheckInReqItemDataWrap.remove(new LambdaQueryWrapper<BizRoomReceiptCheckInReqItem>().eq(BizRoomReceiptCheckInReqItem::getHeadId, receiptHeadId));

        // 删除抬头
        bizRoomReceiptCheckInReqHeadDataWrap.removeById(receiptHeadId);

    }

    /**
     * <AUTHOR>
     *
     * 修改单据状态
     *
     * @param receiptHeadId 单据抬头id
     */
    public void updateStatus(Long receiptHeadId, EnumReceiptStatus receiptStatus) {

        // 修改单据抬头状态
        bizRoomReceiptCheckInReqHeadDataWrap.update(new LambdaUpdateWrapper<BizRoomReceiptCheckInReqHead>()
                .eq(BizRoomReceiptCheckInReqHead::getId, receiptHeadId)
                .set(BizRoomReceiptCheckInReqHead::getReceiptStatus, receiptStatus.getValue())
                .set(BizRoomReceiptCheckInReqHead::getModifyUserId, UtilCurrentContext.getCurrentUser().getId())
                .set(BizRoomReceiptCheckInReqHead::getModifyTime, new Date())
        );

        // 修改单据行项目状态
        bizRoomReceiptCheckInReqItemDataWrap.update(new LambdaUpdateWrapper<BizRoomReceiptCheckInReqItem>()
                .eq(BizRoomReceiptCheckInReqItem::getHeadId, receiptHeadId)
                .set(BizRoomReceiptCheckInReqItem::getItemStatus, receiptStatus.getValue())
                .set(BizRoomReceiptCheckInReqItem::getModifyUserId, UtilCurrentContext.getCurrentUser().getId())
                .set(BizRoomReceiptCheckInReqItem::getModifyTime, new Date())
        );
    }

    /**
     * <AUTHOR>
     *
     * 发起审批流
     *
     * @param ctx
     */
    public void startWorkFlow(BizContext ctx) {
        BizRoomReceiptCheckInReqHeadDTO headDTO = ctx.getPoContextData();

        // 现有房间的情况下，不需要发起审批
        if(EnumRoomReqType.CURRENT_ROOM.getValue().equals(headDTO.getReqType())){
            // 更新状态为已完成
            this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED);

            // 更新申请房间的使用情况
            this.updateRoomUsage(headDTO);

            return;
        }

        // 内部使用情况下，不需要审批，直接生成住房分配单
        if(EnumRealYn.TRUE.getIntValue().equals(headDTO.getIsInternalUsage())){
            // 更新状态为已完成
            this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED);

            // 生成住房分配单
            this.genAllocationReceipty(headDTO.getId());

            return;
        }

        // 审批前验证逻辑，各节点是否存在对应审批人
        List<MetaDataDeptOfficePO> userDeptList = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());

        if (UtilCollection.isEmpty(userDeptList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 暂不支持一个人有多个部门
        MetaDataDeptOfficePO userDept = userDeptList.get(0);

        List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.AD.getCode(), null, EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }

        List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.AD.getCode(), null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }

        // 更新状态为审批中
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING);

        Map<String, Object> variables = new HashMap<>();
        variables.put("userDept", userDept);
        // 【OA待办】: 住房申请待办展示：“请审批[XXXX公司+XX部门]创建人名称提交的住房申请流程：申请事由”； XXX公司 取单据创建人的所属公司，XXX部门取创建人的部门 ，申请事由取住房申请单抬头的申请事由
        // 如测试环境住房申请单ZS2506180004：请审批[华信资源有限责任公司经营管理部]陈飞提交的流程：申请事由
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + userDept.getDeptName() + "]" + headDTO.getCreateUserName() + "提交的流程：" + headDTO.getReqReason());

        // 设置审批变量
        workflowService.setBizReceiptVariables(variables, headDTO.getId(), headDTO.getReceiptCode(), headDTO.getReceiptType(), ctx, headDTO.getReqReason());
        workflowService.startWorkFlow(headDTO.getId(), headDTO.getReceiptCode(), headDTO.getReceiptType(), variables);

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());

//        // 模拟审批通过
//        this.approvalCallback(new BizApprovalReceiptInstanceRelDTO().setReceiptHeadId(headDTO.getId()).setApproveStatus(EnumApprovalStatus.FINISH.getValue()));

//        // 模拟审批驳回
//        this.approvalCallback(new BizApprovalReceiptInstanceRelDTO().setReceiptHeadId(headDTO.getId()).setApproveStatus(EnumApprovalStatus.REJECT.getValue()));
    }

    /**
     * <AUTHOR>
     *
     * 审批回调处理
     *
     * @param wfReceiptCo
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {

        // 审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 生成住房分配单
            this.genAllocationReceipty(wfReceiptCo.getReceiptHeadId());

            // 更新状态为已完成
            this.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED);
        } else {

            BizRoomReceiptCheckInReqHead head = bizRoomReceiptCheckInReqHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());

            // 如果驳回时携带了废弃标记，则直接关闭单据
            if(EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())){

                // 更新状态为已关闭
                this.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED);

                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "住房申请的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            } else {

                // 更新状态为已驳回
                this.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED);

                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "住房申请的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            }
        }
    }

    /**
     * <AUTHOR>
     *
     * 更新申请房间的使用情况
     * 当住房请求类型为现有房间时调用
     * 向该房屋的明细表中添加入住人员信息
     * 更新该房屋的抬头表中的入住人数
     *
     * @param headDTO
     */
    public void updateRoomUsage(BizRoomReceiptCheckInReqHeadDTO headDTO) {
        // 获取房间信息
        BizRoom bizRoom = bizRoomDataWrap.getById(headDTO.getReqRoomId());

        // 要添加到房屋使用信息明细表中的数据
        List<BizRoomUsageItem> bizRoomUsageItemList = new ArrayList<>();

        for(BizRoomReceiptCheckInReqItemDTO bizRoomReceiptCheckInReqItemDTO : headDTO.getItemList()){
            BizRoomUsageItem bizRoomUsageItem = new BizRoomUsageItem();
            bizRoomUsageItemList.add(bizRoomUsageItem);

            bizRoomUsageItem.setHeadId(bizRoom.getCurrentRoomUsageHeadId());
            bizRoomUsageItem.setRoomId(bizRoom.getId());
            bizRoomUsageItem.setCheckInReqItemId(bizRoomReceiptCheckInReqItemDTO.getId());
            bizRoomUsageItem.setCheckInUserName(bizRoomReceiptCheckInReqItemDTO.getCheckInUserName());
            bizRoomUsageItem.setCheckInUserSex(bizRoomReceiptCheckInReqItemDTO.getCheckInUserSex());
            bizRoomUsageItem.setCheckInUserIdNumber(bizRoomReceiptCheckInReqItemDTO.getCheckInUserIdNumber());
            bizRoomUsageItem.setCheckInUserPassportNumber(bizRoomReceiptCheckInReqItemDTO.getCheckInUserPassportNumber());
            bizRoomUsageItem.setCheckInTime(bizRoomReceiptCheckInReqItemDTO.getCheckInTime());
            bizRoomUsageItem.setIdentificationImgId1(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId1());
            bizRoomUsageItem.setIdentificationImgId2(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId2());
            bizRoomUsageItem.setIdentificationImgId3(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId3());
            bizRoomUsageItem.setIdentificationImgId4(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId4());
            bizRoomUsageItem.setIdentificationImgId5(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId5());
            bizRoomUsageItem.setVisaImgId1(bizRoomReceiptCheckInReqItemDTO.getVisaImgId1());
            bizRoomUsageItem.setVisaImgId2(bizRoomReceiptCheckInReqItemDTO.getVisaImgId2());
            bizRoomUsageItem.setVisaImgId3(bizRoomReceiptCheckInReqItemDTO.getVisaImgId3());
            bizRoomUsageItem.setVisaImgId4(bizRoomReceiptCheckInReqItemDTO.getVisaImgId4());
            bizRoomUsageItem.setVisaImgId5(bizRoomReceiptCheckInReqItemDTO.getVisaImgId5());
        }

        // 保存房间使用信息明细
        bizRoomUsageItemDataWrap.saveBatch(bizRoomUsageItemList);

        // 查询房间使用信息明细数量
        int itemCount = (int)bizRoomUsageItemDataWrap.count(new LambdaQueryWrapper<BizRoomUsageItem>().eq(BizRoomUsageItem::getHeadId, bizRoom.getCurrentRoomUsageHeadId()));

        // 更新房屋使用抬头表中的入住人数
        bizRoomUsageHeadDataWrap.update(new LambdaUpdateWrapper<BizRoomUsageHead>()
                .eq(BizRoomUsageHead::getId, bizRoom.getCurrentRoomUsageHeadId())
                .set(BizRoomUsageHead::getCheckInCount, itemCount)
                .set(BizRoomUsageHead::getModifyUserId, UtilCurrentContext.getCurrentUser().getId())
                .set(BizRoomUsageHead::getModifyTime, new Date())
        );
    }

    /**
     * 生成住房分配单
     *
     * @param bizRoomReceiptCheckInReqHeadId 住房申请单抬头id
     */
    public void genAllocationReceipty(Long bizRoomReceiptCheckInReqHeadId) {
        // 获取住房申请单抬头DTO
        BizRoomReceiptCheckInReqHeadDTO bizRoomReceiptCheckInReqHeadDTO = bizRoomReceiptCheckInReqHeadDataWrap.getDtoById(BizRoomReceiptCheckInReqHeadDTO.class, bizRoomReceiptCheckInReqHeadId);

        BizRoomReceiptAllocationHead bizRoomReceiptAllocationHead = new BizRoomReceiptAllocationHead();

        bizRoomReceiptAllocationHead.setId(null);
        bizRoomReceiptAllocationHead.setCreateUserId(bizRoomReceiptCheckInReqHeadDTO.getSubmitUserId());
        bizRoomReceiptAllocationHead.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.ROOM_ALLOCATION.getValue()));
        bizRoomReceiptAllocationHead.setReceiptType(EnumReceiptType.ROOM_ALLOCATION.getValue());
        bizRoomReceiptAllocationHead.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_WAIT_ALLOCATION.getValue());
        bizRoomReceiptAllocationHead.setReqReason(bizRoomReceiptCheckInReqHeadDTO.getReqReason());
        bizRoomReceiptAllocationHead.setCheckInUserNationality(bizRoomReceiptCheckInReqHeadDTO.getCheckInUserNationality());
        bizRoomReceiptAllocationHead.setReqType(bizRoomReceiptCheckInReqHeadDTO.getReqType());
        bizRoomReceiptAllocationHead.setStartUsageTime(bizRoomReceiptCheckInReqHeadDTO.getStartUsageTime());
        bizRoomReceiptAllocationHead.setContractId(bizRoomReceiptCheckInReqHeadDTO.getContractId());
        bizRoomReceiptAllocationHead.setContractCode(bizRoomReceiptCheckInReqHeadDTO.getContractCode());
        bizRoomReceiptAllocationHead.setContractName(bizRoomReceiptCheckInReqHeadDTO.getContractName());

        // 保存住房分配单抬头
        bizRoomReceiptAllocationHeadDataWrap.save(bizRoomReceiptAllocationHead);

        // 行项目列表
        List<BizRoomReceiptAllocationItem> bizRoomReceiptAllocationItemList = new ArrayList<>();

        // 单据流
        List<BizCommonReceiptRelation> relationList = new ArrayList<>();

        AtomicInteger rid = new AtomicInteger(1);

        for(BizRoomReceiptCheckInReqItemDTO bizRoomReceiptCheckInReqItemDTO : bizRoomReceiptCheckInReqHeadDTO.getItemList()){

            BizRoomReceiptAllocationItem bizRoomReceiptAllocationItem = new BizRoomReceiptAllocationItem();
            bizRoomReceiptAllocationItemList.add(bizRoomReceiptAllocationItem);

            bizRoomReceiptAllocationItem.setId(UtilSequence.nextId());
            bizRoomReceiptAllocationItem.setCreateUserId(bizRoomReceiptAllocationHead.getCreateUserId());
            bizRoomReceiptAllocationItem.setHeadId(bizRoomReceiptAllocationHead.getId());
            bizRoomReceiptAllocationItem.setRid(Integer.toString(rid.getAndIncrement()));
            bizRoomReceiptAllocationItem.setPreReceiptHeadId(bizRoomReceiptCheckInReqHeadDTO.getId());
            bizRoomReceiptAllocationItem.setPreReceiptItemId(bizRoomReceiptCheckInReqItemDTO.getId());
            bizRoomReceiptAllocationItem.setPreReceiptType(bizRoomReceiptCheckInReqHeadDTO.getReceiptType());
            bizRoomReceiptAllocationItem.setItemStatus(bizRoomReceiptAllocationHead.getReceiptStatus());
            bizRoomReceiptAllocationItem.setItemRemark(bizRoomReceiptCheckInReqItemDTO.getItemRemark());
            bizRoomReceiptAllocationItem.setCheckInUserName(bizRoomReceiptCheckInReqItemDTO.getCheckInUserName());
            bizRoomReceiptAllocationItem.setCheckInUserSex(bizRoomReceiptCheckInReqItemDTO.getCheckInUserSex());
            bizRoomReceiptAllocationItem.setCheckInUserIdNumber(bizRoomReceiptCheckInReqItemDTO.getCheckInUserIdNumber());
            bizRoomReceiptAllocationItem.setCheckInUserPassportNumber(bizRoomReceiptCheckInReqItemDTO.getCheckInUserPassportNumber());
            bizRoomReceiptAllocationItem.setCheckInTime(bizRoomReceiptCheckInReqItemDTO.getCheckInTime());
            bizRoomReceiptAllocationItem.setIdentificationImgId1(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId1());
            bizRoomReceiptAllocationItem.setIdentificationImgId2(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId2());
            bizRoomReceiptAllocationItem.setIdentificationImgId3(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId3());
            bizRoomReceiptAllocationItem.setIdentificationImgId4(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId4());
            bizRoomReceiptAllocationItem.setIdentificationImgId5(bizRoomReceiptCheckInReqItemDTO.getIdentificationImgId5());
            bizRoomReceiptAllocationItem.setVisaImgId1(bizRoomReceiptCheckInReqItemDTO.getVisaImgId1());
            bizRoomReceiptAllocationItem.setVisaImgId2(bizRoomReceiptCheckInReqItemDTO.getVisaImgId2());
            bizRoomReceiptAllocationItem.setVisaImgId3(bizRoomReceiptCheckInReqItemDTO.getVisaImgId3());
            bizRoomReceiptAllocationItem.setVisaImgId4(bizRoomReceiptCheckInReqItemDTO.getVisaImgId4());
            bizRoomReceiptAllocationItem.setVisaImgId5(bizRoomReceiptCheckInReqItemDTO.getVisaImgId5());

            // 单据流
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            relationList.add(bizCommonReceiptRelation);

            bizCommonReceiptRelation.setReceiptType(bizRoomReceiptAllocationHead.getReceiptType());
            bizCommonReceiptRelation.setReceiptHeadId(bizRoomReceiptAllocationItem.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(bizRoomReceiptAllocationItem.getId());
            bizCommonReceiptRelation.setPreReceiptType(bizRoomReceiptCheckInReqHeadDTO.getReceiptType());
            bizCommonReceiptRelation.setPreReceiptHeadId(bizRoomReceiptCheckInReqItemDTO.getHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(bizRoomReceiptCheckInReqItemDTO.getId());
        }

        // 保存住房分配单行项目列表
        bizRoomReceiptAllocationItemDataWrap.saveBatch(bizRoomReceiptAllocationItemList);

        // 保存单据流
        receiptRelationService.multiSaveReceiptTree(relationList);
    }

    /**
     * <AUTHOR>
     *
     * 对图片属性值进行收集再设置
     * 防止xxxImgId1为空，xxxImgId2有值的情况
     *
     * @param item 行项目
     * @param fieldNamePrefix 图片id属性值前缀
     */
    private void resetImgFieldValue(BizRoomReceiptCheckInReqItemDTO item, String fieldNamePrefix){

        List<Field> fieldList = UtilReflect.getAllFields(BizRoomReceiptCheckInReqItemDTO.class);

        fieldList = fieldList.stream().filter(field -> field.getName().contains(fieldNamePrefix)).sorted(Comparator.comparing((Field::getName))).collect(Collectors.toList());

        if(UtilCollection.isEmpty(fieldList)){
            return;
        }

        List<Long> imgIdList = new ArrayList<>();

        // 遍历属性，收集图片id
        for (Field field : fieldList) {
            Long imgId = UtilReflect.getValueByField(field, item);

            if(UtilNumber.isEmpty(imgId)){
                continue;
            }

            imgIdList.add(imgId);
        }

        // 遍历属性，设置图片id
        for (int i = 0; i < fieldList.size(); i++) {
            Field field = fieldList.get(i);

            UtilReflect.setValueByField(item, field, imgIdList.size() >= i + 1 ? imgIdList.get(i) : 0L);
        }
    }

    /**
     * 按钮组
     *
     * @param headDTO 单据抬头信息
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizRoomReceiptCheckInReqHeadDTO headDTO) {

        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();

        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(receiptStatus)) {
            // 创建 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)){
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)){
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        return buttonVO;
    }

    /**
     * 撤销单据
     *
     * @param ctx 入参上下文
     */
    public void revokeReceipt(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getIdContextData();

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取抬头DTO
        BizRoomReceiptCheckInReqHeadDTO headDTO = bizRoomReceiptCheckInReqHeadDataWrap.getDtoById(BizRoomReceiptCheckInReqHeadDTO.class, headId);
        
        if (UtilObject.isNull(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        if (!EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(headDTO.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }

        // 删除待办
        workflowService.deleteTodo(headId);

        // 获取审批实例关系
        BizApprovalReceiptInstanceRel approvalRel = bizApprovalReceiptInstanceRelDataWrap.getOne(
                new QueryWrapper<BizApprovalReceiptInstanceRel>()
                        .lambda()
                        .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, headId)
                        .eq(BizApprovalReceiptInstanceRel::getApproveStatus, EnumApprovalStatus.APPROVING.getValue())
                        .orderByDesc(BizApprovalReceiptInstanceRel::getCreateTime).last("limit 1")
        );

        if (UtilObject.isNull(approvalRel)) {
            log.warn("住房申请[{}]未找到有效的审批实例", headDTO.getReceiptCode());
            return;
        }

        // 撤销审批流程
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(approvalRel.getProcessInstanceId());
        workflowService.revoke(revokeDTO);
        log.debug("住房申请[{}]审批流程撤销完成", headDTO.getReceiptCode());

        // 更新领料申请单 - 草稿
        updateStatus(headId, EnumReceiptStatus.RECEIPT_STATUS_DRAFT);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

}