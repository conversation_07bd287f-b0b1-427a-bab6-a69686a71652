package com.inossem.wms.bizdomain.maintain.service.component;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.maintain.service.datawrap.BizReceiptMaintainHeadDataWrap;
import com.inossem.wms.bizdomain.maintain.service.datawrap.BizReceiptMaintainItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.maintain.EnumMaintenanceType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleMaintainDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainHeadDTO;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainItemDTO;
import com.inossem.wms.common.model.bizdomain.maintain.entity.BizReceiptMaintainHead;
import com.inossem.wms.common.model.bizdomain.maintain.entity.BizReceiptMaintainItem;
import com.inossem.wms.common.model.bizdomain.maintain.po.BizReceiptMaintainSearchPO;
import com.inossem.wms.common.model.bizdomain.maintain.po.BizReceiptMaintainSearchStockPO;
import com.inossem.wms.common.model.bizdomain.maintain.vo.BizReceiptMaintainPageVO;
import com.inossem.wms.common.model.bizdomain.maintain.vo.BizReceiptMaintainPlanVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 维保计划 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@Service
public class MaintainPlanComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BatchInfoService batchInfoService;

    @Autowired
    protected StockCommonService stockCommonService;

    @Autowired
    protected MaintainCommonComponent maintainCommonComponent;

    @Autowired
    protected BizReceiptMaintainHeadDataWrap bizReceiptMaintainHeadDataWrap;
    @Autowired
    protected BizReceiptMaintainItemDataWrap bizReceiptMaintainItemDataWrap;
    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    protected SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    protected WorkflowService workflowService;

    /**
     * 查询维保类型下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("EnumMaintenanceType.toList()":"维保类型下拉框")}
     */
    public void getMaintenanceTypeDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumMaintenanceType.toCreateList()));
    }

    /**
     * 页面初始化
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"维保计划单明细","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化返回空的行项目、扩展项对象、按钮组【保存和提交】
        BizResultVO<BizReceiptMaintainHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptMaintainHeadDTO().setReceiptType(EnumReceiptType.STOCK_MAINTAIN_PLAN.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        if (UtilCollection.isNotEmpty(ctx.getCurrentUser().getUserDeptList())) {
            resultVO.getHead().setDeptId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptId());
        }
        // 页面初始化数据放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询维保计划列表-分页
     *
     * @in ctx 入参 {@link BizReceiptMaintainSearchPO :"维保分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptMaintainSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (CollectionUtils.isEmpty(currentUser.getLocationList())) {
            return;
        }
        List locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        po.setLocationIdList(locationIds);

        // 分页查询处理
        IPage<BizReceiptMaintainPageVO> page = po.getPageObj(BizReceiptMaintainPageVO.class);
        bizReceiptMaintainHeadDataWrap.getPageVOList(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 维保计划单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"维保计划单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取维保集合单
        BizReceiptMaintainHeadDTO headDTO = UtilBean.newInstance(bizReceiptMaintainHeadDataWrap.getById(headId), BizReceiptMaintainHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 查询工厂物料维保列表
        if (EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(headDTO.getMaintenanceType()) && !EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
            for (BizReceiptMaintainItemDTO itemDTO : headDTO.getItemList()) {
                DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(itemDTO.getMatId(), itemDTO.getFtyId());
                itemDTO.setMatFtyMaintainList(new ArrayList<>());
                if (UtilObject.isNotNull(materialFactoryDTO) && UtilCollection.isNotEmpty(materialFactoryDTO.getMaintainList())) {
                    materialFactoryDTO.getMaintainList().forEach(matFtyMaintain -> {
                        if (EnumRealYn.TRUE.getIntValue().equals(matFtyMaintain.getIsMaintenanceEnable())) {
                            itemDTO.getMatFtyMaintainList().add(matFtyMaintain);
                        }
                    });
                }
            }
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置维保计划单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 维保单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptMaintainHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        return buttonVO;
    }

    /**
     * 提交维保计划单
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "要提交的维保计划单"}
     * @out ctx 出参 {"receiptCode" : "维保计划单单号"}
     */
    public void submitMaintainPlan(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交寿维保单时赋值提交时间提交人
        headDTO.setSubmitTime(UtilDate.getNow());
        headDTO.setSubmitUserId(user.getId());
        // 主办人所属部门
        if (UtilNumber.isNotEmpty(headDTO.getAssignUserId())) {
            List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(headDTO.getAssignUserId());
            if (UtilCollection.isNotEmpty(userDept)) {
                headDTO.setAssignDeptId(userDept.get(0).getDeptId());
            }
        }

        // 保存维保单
        maintainCommonComponent.saveMaintain(ctx);
    }

    /**
     * 更新批次保养大纲
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "要提交的维保计划单"}
     */
    public void updateBatchMaintenanceProgram(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(po.getMaintenanceType().equals(EnumMaintenanceType.SPECIAL_MAINTAIN.getValue())) {
            List<BizReceiptMaintainItemDTO> itemDTOList = po.getItemList();
            int size = itemDTOList.size();
            Map<Long, BizReceiptMaintainItemDTO> itemDTOMap = new HashMap<>(size);
            Set<Long> batchIdSet = new HashSet<>(size);
            List<Long> batchIdList = new ArrayList<>(size);
            for (BizReceiptMaintainItemDTO itemDTO : itemDTOList) {
                Long batchId = itemDTO.getBatchId();
                if (batchIdSet.add(batchId)) {
                    batchIdList.add(batchId);
                    String program = itemDTO.getMaintenanceProgram();
                    if (StringUtils.isBlank(program))
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                    itemDTOMap.put(batchId, itemDTO);
                }
            }
            // 更新批次保养大纲
            List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getBatchInfoList(batchIdList);
            batchInfoDTOList.forEach(p -> {
                Long id = p.getId();
                BizReceiptMaintainItemDTO itemDTO = itemDTOMap.get(id);
                p.setMaintenanceProgram(itemDTO.getMaintenanceProgram());
            });
            batchInfoService.multiUpdateBatchInfo(batchInfoDTOList);
        }
    }

    /**
     * 生成维保结果维护单
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保计划单"}
     */
    public void genMaintainResult(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        BizReceiptMaintainHeadDTO headDTO = new BizReceiptMaintainHeadDTO();
        List<BizReceiptMaintainItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.STOCK_MAINTAIN_RESULT.getValue())
                .setMaintenanceType(po.getMaintenanceType())
                .setPlanCompleteDate(po.getPlanCompleteDate())
                .setAssignDeptId(po.getAssignDeptId())
                .setDeptId(po.getDeptId())
                .setRemark(po.getRemark());
        for (BizReceiptMaintainItemDTO itemDTO : po.getItemList()) {
            BizReceiptMaintainItemDTO resultItemDTO = UtilBean.newInstance(itemDTO, BizReceiptMaintainItemDTO.class);
            resultItemDTO.setPreReceiptHeadId(po.getId());
            resultItemDTO.setPreReceiptItemId(itemDTO.getId());
            resultItemDTO.setPreReceiptType(EnumReceiptType.STOCK_MAINTAIN_PLAN.getValue());
            resultItemDTO.setPreReceiptQty(itemDTO.getQty());
            itemDTOList.add(resultItemDTO);
        }
        headDTO.setItemList(itemDTOList);
        // 设置入参上下文
        BizContext ctxResult = new BizContext();
        ctxResult.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxResult.setCurrentUser(ctx.getCurrentUser());
        // 推送MQ生成维保结果维护单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_MAINTAIN_RESULT_STOCK, ctxResult);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 查询物料批次库存
     *
     * @in ctx 入参 {@link BizReceiptMaintainSearchStockPO : "维保查询物料批次库存入参"}
     * @out ctx 出参 {@link SingleResultVO ("matStockDTO":"物料批次库存")}
     */
    public void getMatFeatureStock(BizContext ctx) {
        // 获取上下文
        BizReceiptMaintainSearchStockPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptMaintainItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        Set<Long> matIdSet = null;
        String matCode = po.getMatCode();
        if (UtilString.isNotNullOrEmpty(matCode)) {
            if (matCode.contains(",")) {
                matIdSet = new HashSet<>();
                String[] matCodes = matCode.split(",");
                for (String code : matCodes) {
                    if (StringUtils.isBlank(code))
                        continue;
                    Long codeId = dictionaryService.getMatIdByMatCode(code);
                    if (Objects.isNull(codeId))
                        continue;
                    matIdSet.add(codeId);
                }
                if (CollectionUtils.isEmpty(matIdSet)) {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                    return;
                }
            } else {
                matId = dictionaryService.getMatIdByMatCode(matCode);
                if (Objects.isNull(matId)) {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                    return;
                }
            }
        }
        Long binId = null;
        if (UtilString.isNotNullOrEmpty(po.getBinCode())) {
            binId = dictionaryService.getBinCacheByCode(po.getBinCode()).getId();
            if (Objects.isNull(binId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        // 入参封装
        BizReceiptAssembleRuleSearchPO searchPO = new BizReceiptAssembleRuleSearchPO();
        searchPO.setIsUnitized(false);
        searchPO.setFtyId(po.getFtyId());
        searchPO.setLocationId(po.getLocationId());
        searchPO.setMatId(matId);
        searchPO.setMatIdSet(matIdSet);
        searchPO.setBinId(binId);
        searchPO.setMaintenanceType(po.getMaintenanceType());
        Integer maintainType = po.getMaintenanceType();
        if (EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(maintainType)) {
            searchPO.setMaintenanceValidDateStart(po.getMaintenanceValidDateStart());
            searchPO.setMaintenanceValidDateEnd(po.getMaintenanceValidDateEnd());
        } else if (EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(maintainType)) {
            searchPO.setMaintenanceValidDateStart(po.getMaintenanceValidDateStart());
            searchPO.setMaintenanceValidDateEnd(po.getMaintenanceValidDateEnd());
            searchPO.setMaintainProFlag(EnumRealYn.TRUE.getIntValue());
        }
        searchPO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 根据特性code查询特性库存
        List<BizReceiptAssembleMaintainDTO> assembleDTOList = stockCommonService.getMaintainStockBySdw(po.getReceiptType(), searchPO);
        MatStockDTO matStockDTO = new MatStockDTO();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            // 特性库存转行项目
            for (BizReceiptAssembleMaintainDTO assembleDTO : assembleDTOList) {
                BizReceiptMaintainItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptMaintainItemDTO.class);
                itemDTO.setQty(assembleDTO.getStockQty());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTO.setBinId(assembleDTO.getBinIdTemp());
                itemDTO.setBinCode(assembleDTO.getBinCodeTemp());
                if (EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(maintainType)) {
                    DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(assembleDTO.getMatId(), assembleDTO.getFtyId());
                    itemDTO.setMatFtyMaintainList(new ArrayList<>());
                    if (UtilObject.isNotNull(materialFactoryDTO) && UtilCollection.isNotEmpty(materialFactoryDTO.getMaintainList())) {
                        materialFactoryDTO.getMaintainList().forEach(matFtyMaintain -> {
                            if (EnumRealYn.TRUE.getIntValue().equals(matFtyMaintain.getIsMaintenanceEnable())) {
                                itemDTO.getMatFtyMaintainList().add(matFtyMaintain);
                            }
                        });
                    }
                }
                itemDTOList.add(itemDTO);
            }
            BizReceiptAssembleMaintainDTO firstItem = assembleDTOList.get(0);
            matStockDTO.setFeatureCode(firstItem.getRuleFeatureCode());
            matStockDTO.setFeatureName(firstItem.getRuleFeatureName());
        }
        matStockDTO.setMaintainItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }


    /**
     * 维保计划导出
     *
     * @param ctx
     */
    public void exportPlan(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("维保计划"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        BizReceiptMaintainHead head = bizReceiptMaintainHeadDataWrap.getById(headId);
        if (head == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        String receiptCode = head.getReceiptCode();
        // 获取维保集合单
        List<BizReceiptMaintainPlanVO> planVOList = bizReceiptMaintainItemDataWrap.exportByPlan(headId);
        dataFillService.fillAttr(planVOList);
        if (!CollectionUtils.isEmpty(planVOList)) {
            Map<Integer, String> depositMap = new HashMap<>();
            Map<Integer, String> pkgMap = new HashMap<>();
            String lang = Const.DEFAULT_LANG_CODE;
            planVOList.forEach(item -> {
                item.setReceiptCode(receiptCode);
                Integer depositType = item.getDepositType();
                if (depositType != null) {
                    String depositTypeI18n = depositMap.get(depositType);
                    if (depositTypeI18n == null) {
                        depositTypeI18n = i18nTextCommonService.getNameMessage(lang, "depositType", depositType.toString());
                        depositMap.put(depositType, depositTypeI18n);
                    }
                    item.setDepositTypeI18n(depositTypeI18n);
                }
                Integer pkgType = item.getPackageType();
                if (pkgType != null) {
                    String pkgTypeI18n = pkgMap.get(pkgType);
                    if (pkgTypeI18n == null) {
                        pkgTypeI18n = i18nTextCommonService.getNameMessage(lang, "packageType", pkgType.toString());
                        pkgMap.put(pkgType, pkgTypeI18n);
                    }
                    item.setPackageTypeI18n(pkgTypeI18n);
                }
            });
        }
        UtilExcel.writeExcel(BizReceiptMaintainPlanVO.class, planVOList, bizCommonFile);
        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

    public void startWorkFlow(BizContext ctx) {
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        // 2023-08-02 再次修改,仅保留包装更换(原日常维保)业务的审批 2024-09-13 增加维护保养类型审批流
//        Integer receiptType = po.getMaintenanceType().equals(EnumMaintenanceType.SPECIAL_MAINTAIN.getValue()) ? EnumReceiptType.STOCK_MAINTAIN_PLAN_PROFESSIONAL.getValue() : po.getReceiptType();
        Integer receiptType = EnumReceiptType.STOCK_MAINTAIN_PLAN_PROFESSIONAL.getValue();
        // 发起流程审批
        String procId = null;
//        if (EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(po.getMaintenanceType())) {
//            // 缺陷维保移除审批，仅对非缺陷维保的进行
//            // 2023-08-02 再次修改,仅保留包装更换业务的审批
//            // 2024-08-26 再次修改,包装更换不进行审批
//            this.approverCheck(userDept);
//            // 2023-07-24 根据业务方要求，缺陷维保取消审批，为避免后期缺陷维保再独立启用审批，缺陷维保单据单据单独传递单据类型
//            // 2023-08-02 再次修改,仅保留包装更换(原日常维保)业务的审批
//            Map<String, Object> variables = new HashMap<>();
//            Long ftyId=po.getItemList().get(0).getFtyId();
//            variables.put("ftyId", ftyId);
//            // 用户所属部门、对口部门、对口科室
//            variables.put("userDept", userDept);
//            workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
//            procId = workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
//        } else
        if (EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(po.getMaintenanceType())) {
            // 2024-09-13 再次修改,维护保养也添加审批
            List<String> userCodeList = this.approverCheckSpecialMaintain(po);
            Map<String, Object> variables = new HashMap<>();
            Long ftyId=po.getItemList().get(0).getFtyId();
            variables.put("ftyId", ftyId);
            // 主办人
            variables.put("userCodeList", userCodeList);
            workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
            procId = workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        }

        if (UtilString.isNotNullOrEmpty(procId)) {
            // 更新 - 审批中
            UpdateWrapper<BizReceiptMaintainHead> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(BizReceiptMaintainHead::getId,po.getId()).set(BizReceiptMaintainHead::getReceiptStatus,EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
            bizReceiptMaintainHeadDataWrap.update(updateWrapper);
            List<Long> itemIdList = po.getItemList().stream().map(obj -> obj.getId()).collect(Collectors.toList());
            UpdateWrapper<BizReceiptMaintainItem> itemListUpdateWrapper = new UpdateWrapper<>();
            itemListUpdateWrapper.lambda().in(BizReceiptMaintainItem::getId,itemIdList).set(BizReceiptMaintainItem::getItemStatus,EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
            bizReceiptMaintainItemDataWrap.update(itemListUpdateWrapper);
        } else {
            // 找不到审批按照无审批处理，直接完成
            BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
            wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
            wfReceiptCo.setInitiator(ctx.getCurrentUser());
            wfReceiptCo.setReceiptHeadId(receiptId);
            this.approvalCallback(wfReceiptCo);
        }
    }

    private void approverCheck(List<MetaDataDeptOfficePO> userDept) {
        if (UtilCollection.isEmpty(userDept)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        List<String> level1UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDept.get(0).getDeptCode(),userDept.get(0).getDeptOfficeCode(), EnumApprovalLevel.LEVEL_3);
        List<String> level2UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDept.get(0).getDeptCode(), null, EnumApprovalLevel.LEVEL_4);
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "1", userDept.get(0).getDeptName(), userDept.get(0).getDeptOfficeName(), EnumApprovalLevel.LEVEL_3.getValue());
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
    }

    private List<String> approverCheckSpecialMaintain(BizReceiptMaintainHeadDTO po) {
        // 抬头添加主办人字段存入流程变量，作为一级审核节点审批人
        if (UtilNumber.isEmpty(po.getAssignUserId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<String> userCodeList = new ArrayList<>();
        SysUser assignUser = dictionaryService.getSysUserCacheById(po.getAssignUserId());
        if (UtilObject.isNull(assignUser)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        userCodeList.add(assignUser.getUserCode());
        return userCodeList;
    }

    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            BizContext ctx = new BizContext();
            CurrentUser currentUser = wfReceiptCo.getInitiator();
            ctx.setCurrentUser(currentUser);
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            BizReceiptMaintainHead maintainHead = bizReceiptMaintainHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
            BizReceiptMaintainHeadDTO headDTO = UtilBean.newInstance(maintainHead, BizReceiptMaintainHeadDTO.class);
            dataFillService.fillAttr(headDTO);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
             // 更新单据已完成
             maintainCommonComponent.updateStatusCompleted(ctx);
             // 生成维保结果维护单
             this.genMaintainResult(ctx);
             //修改状态
             maintainCommonComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        } else {
            maintainCommonComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }
}
