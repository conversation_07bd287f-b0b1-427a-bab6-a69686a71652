package com.inossem.wms.bizdomain.output.controller;

import com.inossem.wms.bizdomain.output.service.biz.WasteOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 废旧出库controller
 *
 * <AUTHOR>
 * @date 2021/4/7 9:28
 */

@RestController
@Api(tags = "出库管理-废旧出库")
public class WasteOutputController {

    @Autowired
    private WasteOutputService wasteOutputService;

    @ApiOperation(value = "废旧出库创建", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/init")
    public BaseResult init(BizContext ctx) {
        wasteOutputService.init(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "废旧出库查询列表（分页）", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptOutputQueryListPO po,
        BizContext ctx) {
        wasteOutputService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "废旧出库查询物料库存", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        wasteOutputService.getMatStock(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @ApiOperation(value = "废旧出库获取配货信息", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        wasteOutputService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }

    @ApiOperation(value = "废旧出库详情", tags = {"出库管理-废旧出库"})
    @GetMapping(value = "/outputs/waste-output/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        wasteOutputService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "废旧出库保存", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/save")
    public BaseResult save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        wasteOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "废旧出库提交", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/submit")
    public BaseResult submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        wasteOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "废旧出库过账", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/post")
    public BaseResult post(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        wasteOutputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_POST_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "废旧出库冲销", tags = {"出库管理-废旧出库"})
    @PostMapping(value = "/outputs/waste-output/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        wasteOutputService.writeOff(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "废旧出库删除", tags = {"出库管理-废旧出库"})
    @DeleteMapping(value = "/outputs/waste-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        wasteOutputService.delete(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_DELETE_SUCCESS, headDTO.getReceiptCode());
    }


    /**
     * 废旧物资出库单-数据导入
     * @param file
     * @param ctx
     * @return
     */
    @ApiOperation(value = "废旧物资出库单-数据导入", notes = "废旧物资出库单-数据导入", tags = {"入库管理-废旧物资出库单"})
    @PostMapping(path = "/outputs/waste-output/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptOutputHeadDTO> importMaterial(@RequestPart("file") MultipartFile file, BizContext ctx) {
        wasteOutputService.importData(ctx);
        BizReceiptOutputHeadDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}