package com.inossem.wms.bizdomain.stocktaking.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingHead;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingHeadPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 库存盘点抬头表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-25
 */
public interface BizReceiptStocktakingHeadMapper extends WmsBaseMapper<BizReceiptStocktakingHead> {

    /**
     * 查询库存盘点抬头表分页列表
     *
     * @param page 分页参数
     * @param po 条件
     * @return
     */
    List<BizReceiptStocktakingHeadPageVO> selectBizReceiptStocktakingHeadPageVOList(IPage<BizReceiptStocktakingHeadPageVO> page, @Param("po") BizReceiptStocktakingHeadSearchPO po);
    /**
     * 查询库存盘点抬头表分页列表
     *
     * @param page 分页参数
     * @param po 条件
     * @return
     */
    List<BizReceiptStocktakingHeadPageVO> selectBizReceiptStocktakingHeadPageVOListUnitized(IPage<BizReceiptStocktakingHeadPageVO> page, @Param("po") BizReceiptStocktakingHeadSearchPO po);

    /**
     * 查询库存盘点抬头表分页列表
     *
     * @param page 分页参数
     * @param po 条件
     * @return
     */
    List<BizReceiptStocktakingHeadPageVO> getBizReceiptStocktakingPlanHeadPageVOList(IPage<BizReceiptStocktakingHeadPageVO> page, @Param("po") BizReceiptStocktakingHeadSearchPO po);

    /**
     * 首盘更新库存盘点物料明细表 首盘盘点单bin_id 最后一次复盘盘点单bin_id
     * @param head
     */
    public void updateStocktakingBinIdByFirst(@Param("po") BizReceiptStocktakingHead head);
    /**
     * 复盘更新库存盘点物料明细表  最后一次复盘盘点单bin_id
     * @param head
     */
    public void updateStocktakingBinIdByLast(@Param("po") BizReceiptStocktakingHead head);
    /**
     * 复盘更新库存盘点抬头表  最后一次复盘盘点单head_id
     * @param head
     */
    public void updateStocktakingHeadIdByLast(@Param("po") BizReceiptStocktakingHead head);
    /**
     * 首盘更新库存盘点物料明细表 是否计数
     * @param head
     */
    public void updateStocktakingBinCountByFirst(@Param("po") BizReceiptStocktakingHead head);
    /**
     * 复盘更新库存盘点物料明细表 是否计数
     * @param head
     */
    public void updateStocktakingBinCountByLast(@Param("po") BizReceiptStocktakingHead head);


}
