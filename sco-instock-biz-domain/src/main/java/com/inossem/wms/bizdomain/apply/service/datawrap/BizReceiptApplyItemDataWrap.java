package com.inossem.wms.bizdomain.apply.service.datawrap;

import com.inossem.wms.bizdomain.apply.dao.BizReceiptApplyItemMapper;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptToolVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptSearchPrePO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 借用申请单行项目明细表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Service
public class BizReceiptApplyItemDataWrap extends BaseDataWrap<BizReceiptApplyItemMapper, BizReceiptApplyItem> {

    public List<BizReceiptToolVO> getToolList(BizReceiptApplySearchMatPO po) {
        return this.baseMapper.getToolList(po);
    }

    /**
     * 查询借用申请单行项目信息
     *
     * @param po 查新条件
     * @return List<BizReceiptApplyItemDTO>
     */
    public List<BizReceiptApplyItemDTO> getApplyReceiptItemList(BizReceiptSearchPrePO po) {
        return this.baseMapper.getApplyReceiptItemList(po);
    }

    public void updateStatusById(BizReceiptApplyItemDTO dto) {
        this.getBaseMapper().updateStatusById(dto);
    }

    public void updateByIds(int itemStatus, List<Long> ids) {
        this.getBaseMapper().updateByIds(itemStatus, ids);
    }
}
