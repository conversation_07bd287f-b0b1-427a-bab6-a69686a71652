package com.inossem.wms.bizdomain.paper.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.paper.service.datawrap.BizReceiptPaperSyncFileDataWrap;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.model.bizdomain.paper.dto.BizReceiptPaperSyncFileDTO;
import com.inossem.wms.common.model.bizdomain.paper.entity.BizReceiptPaperSyncFile;
import com.inossem.wms.common.model.bizdomain.paper.po.BizReceiptPaperSyncFileSearchPO;
import com.inossem.wms.common.model.bizdomain.paper.vo.BizReceiptPaperSyncFilePageVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设计原图纸同步文件记录 服务接口
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Slf4j
@Service
public class BizReceiptPaperSyncFileService {

    @Autowired
    private BizReceiptPaperSyncFileDataWrap bizReceiptPaperSyncFileDataWrap;
    @Autowired
    private DtsDesignPaperService dtsDesignPaperService;

    /**
     * 已同步的文件
     */
    public List<String> syncFileList(){
        QueryWrapper<BizReceiptPaperSyncFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptPaperSyncFile::getSync,1);
        List<BizReceiptPaperSyncFile> list = bizReceiptPaperSyncFileDataWrap.list(queryWrapper);
        return list.stream().map(BizReceiptPaperSyncFile::getFileName).collect(Collectors.toList());
    }

    /**
     * 保存同步文件
     */
    public void saveBatch(List<BizReceiptPaperSyncFile> syncFiles) {
        bizReceiptPaperSyncFileDataWrap.saveBatch(syncFiles);
        int syncSize = syncFiles.size();
        List<BizReceiptPaperSyncFile> failList = syncFiles.stream().filter(e -> e.getSync().equals(0)).collect(Collectors.toList());
        int failSize = failList.size();
        int successSize = syncSize - failSize;
        log.info(String.format("同步文件总数：%s；同步成功：%s：同步失败：%s",syncSize,successSize,failSize));
        if (UtilCollection.isNotEmpty(failList)){
            List<String> failFileNameList = failList.stream().map(BizReceiptPaperSyncFile::getFileName).collect(Collectors.toList());
            log.info(String.format("同步失败：%s",failFileNameList));
        }
    }


    /**
     * 获取设计原图纸同步文件记录 - 分页列表
     */
    public PageObjectVO<BizReceiptPaperSyncFilePageVO> getPage(BizReceiptPaperSyncFileSearchPO po) {
        // 分页查询
        IPage<BizReceiptPaperSyncFile> page = po.getPageObj(BizReceiptPaperSyncFile.class);
        QueryWrapper<BizReceiptPaperSyncFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UtilNumber.isNotNull(po.getSync()),BizReceiptPaperSyncFile::getSync,po.getSync());
        queryWrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getFileName()),BizReceiptPaperSyncFile::getFileName,po.getFileName());
        queryWrapper.lambda().eq(UtilObject.isNotNull(po.getSyncDate()),BizReceiptPaperSyncFile::getSyncDate,po.getSyncDate());
        queryWrapper.lambda().orderByDesc(BizReceiptPaperSyncFile::getCreateTime);
        bizReceiptPaperSyncFileDataWrap.page(page, queryWrapper);
        List<BizReceiptPaperSyncFilePageVO> list = UtilCollection.toList(page.getRecords(), BizReceiptPaperSyncFilePageVO.class);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(list, totalCount);
    }

    /**
     * 同步
     */
    @Transactional(rollbackFor = Exception.class)
    public void sync(BizReceiptPaperSyncFileDTO po) {
        dtsDesignPaperService.sync(po);
    }

    /**
     * 同步
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncJob() {
        dtsDesignPaperService.syncJob();
    }

    /**
     * 更新为已同步
     * @param po
     */
    public void updateSyncYes(BizReceiptPaperSyncFileDTO po) {
        po.setSync(EnumRealYn.TRUE.getIntValue());
        bizReceiptPaperSyncFileDataWrap.updateDtoById(po);
    }
}
