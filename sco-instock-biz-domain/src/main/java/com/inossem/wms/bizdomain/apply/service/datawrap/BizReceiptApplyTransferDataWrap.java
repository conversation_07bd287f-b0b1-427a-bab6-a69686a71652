package com.inossem.wms.bizdomain.apply.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizdomain.apply.dao.BizReceiptApplyTransferMapper;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyTransferDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyTransfer;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class BizReceiptApplyTransferDataWrap extends BaseDataWrap<BizReceiptApplyTransferMapper, BizReceiptApplyTransfer> {

    /**
     * 物理删除
     * @param receiptHeadId 根据前置单据
     */
    public void physicalDeleteByHeadId(Long receiptHeadId) {
        QueryWrapper<BizReceiptApplyTransfer> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptApplyTransfer::getReceiptHeadId ,receiptHeadId);
        this.physicalDelete(wrapper);
    }

    /**
     * 查询行项目出库数
     *
     * @param itemId
     * @return
     */
    public BigDecimal getItemAssembleOperatedQty(Long itemId) {
        return this.getBaseMapper().getItemAssembleOperatedQty(itemId);
    }

    public void updateById(BizReceiptApplyTransferDTO dto) {
        this.getBaseMapper().updateById(dto);
    }
}
