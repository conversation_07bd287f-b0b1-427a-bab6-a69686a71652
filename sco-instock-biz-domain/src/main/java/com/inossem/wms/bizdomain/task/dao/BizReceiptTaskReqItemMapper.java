package com.inossem.wms.bizdomain.task.dao;

import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-25
 */
public interface BizReceiptTaskReqItemMapper extends WmsBaseMapper<BizReceiptTaskReqItem> {

    /**
     * 批量修改请求作业数量
     * @param taskReqItemList 请求行项目
     */
    void updateBatchReqTaskQty(List<BizReceiptTaskReqItem> taskReqItemList);

    /**
     * 根据前续单据行项目id获取请求行项目
     * @param preReceiptItemIdList 前续单据行项目id集合
     * @return 请求行项目集合
     */
    List<BizReceiptTaskReqItem> getReqItemListByPreReceiptItemIdList(List<Long> preReceiptItemIdList);

    /**
     * 根据前续单据行项目id获取未背删除的请求行项目
     * @param preReceiptItemIdList 前续单据行项目id集合
     * @return 请求行项目集合
     */
    List<BizReceiptTaskReqItem> getReqItemListByPreReceiptItemIdUseList(List<Long> preReceiptItemIdList);

    void updateQtyAndStatusById(@Param("id") Long id, @Param("itemStatus") int itemStatus, @Param("taskQty") BigDecimal taskQty);

    void updateStatusByIds(@Param("idList") List<Long> idList, @Param("status") int status);
}
