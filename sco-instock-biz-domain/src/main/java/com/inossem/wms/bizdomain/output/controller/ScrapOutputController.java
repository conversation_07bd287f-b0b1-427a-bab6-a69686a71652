package com.inossem.wms.bizdomain.output.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.bizdomain.output.service.biz.ScrapOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.dto.ErpCostCenterDTO;
import com.inossem.wms.common.model.erp.po.ErpCostCenterQueryPO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 报废出库
 *
 * @Author: jhr
 * @Date: 2021/5/6 14:42
 */

@RestController
@Api(tags = "出库管理-报废出库")
public class ScrapOutputController {

    @Autowired
    private ScrapOutputService scrapOutputService;

    @ApiOperation(value = "报废出库创建", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/init")
    public BaseResult init(BizContext ctx) {
        scrapOutputService.init(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报废出库查询列表（分页）", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptOutputQueryListPO po,
        BizContext ctx) {
        scrapOutputService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报废出库查询物料库存列表", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        scrapOutputService.getMatStock(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @ApiOperation(value = "报废出库获取配货信息", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        scrapOutputService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }

    @ApiOperation(value = "报废出库详情", tags = {"出库管理-报废出库"})
    @GetMapping(value = "/outputs/scrap-output/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        scrapOutputService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报废出库保存", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/save")
    public BaseResult save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        scrapOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "报废出库提交", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/submit")
    public BaseResult submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        scrapOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "报废出库过账", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/post")
    public BaseResult post(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        scrapOutputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_POST_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "报废出库冲销", tags = {"出库管理-报废出库"})
    @PostMapping(value = "/outputs/scrap-output/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        scrapOutputService.writeOff(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "报废出库删除", tags = {"出库管理-报废出库"})
    @DeleteMapping(value = "/outputs/scrap-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        scrapOutputService.delete(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_DELETE_SUCCESS, headDTO.getReceiptCode());
    }


}