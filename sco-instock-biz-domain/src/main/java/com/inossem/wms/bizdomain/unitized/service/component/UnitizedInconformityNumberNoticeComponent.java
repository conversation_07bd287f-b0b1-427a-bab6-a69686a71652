package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizdomain.inconformity.service.component.InconformityCommonComponent;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectUserDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.inconformity.EnumDifferentType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityItem;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectUser;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilPrint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 成套设备数量差异通知 组件库
 * </p>
 */
@Slf4j
@Service
public class UnitizedInconformityNumberNoticeComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected InconformityCommonComponent inconformityCommonComponent;

    @Autowired
    protected BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    protected BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    protected BizReceiptInconformityHeadDataWrap bizReceiptInconformityHeadDataWrap;

    @Autowired
    protected BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;
    @Autowired
    protected SapInterfaceService sapInterfaceService;
    @Autowired
    protected BizReceiptRegisterHeadDataWrap bizReceiptRegisterHeadDataWrap;
    @Autowired
    protected UnitizedDeliveryNoticeComponent unitizedDeliveryNoticeComponent;
    @Autowired
    protected BizReceiptInspectUserDataWrap bizReceiptInspectUserDataWrap;


    /**
     * 查询差异类型下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("EnumLostType.toList()":"差异类型下拉框")}
     */
    public void getDifferentTypeDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumDifferentType.toList()));
    }

    /**
     * 查询成套设备数量差异通知列表-分页
     *
     * @in ctx 入参 {@link BizReceiptInconformitySearchPO :"不符合项分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptInconformityPageVO> page = po.getPageObj(BizReceiptInconformityPageVO.class);
        CurrentUser user = ctx.getCurrentUser();
        bizReceiptInconformityHeadDataWrap.getPageVOListUnitized(page, po,user);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 成套设备数量差异通知单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"成套设备数量差异通知单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取成套设备数量差异通知单详情
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(bizReceiptInconformityHeadDataWrap.getById(headId), BizReceiptInconformityHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        SysUser purchaseUser = dictionaryService.getSysUserCacheByuserCode(headDTO.getItemList().get(0).getPurchaseUserCode());
        String purchaseUserName = headDTO.getItemList().get(0).getPurchaseUserCode();
        if (!(purchaseUser == null || purchaseUser.getUserName() == null || purchaseUser.getUserName() == "")){
            purchaseUserName = purchaseUser.getUserName();
        }
        // 属性填充
        headDTO.setPurchaseUserCode(headDTO.getItemList().get(0).getPurchaseUserCode())
                .setPurchaseUserName(purchaseUserName);
        // 差异通知填充固定资产物料描述属性
        List<Long> referReceiptItemIdList = headDTO.getItemList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).map(BizReceiptInconformityItemDTO::getReferReceiptItemId).collect(Collectors.toList());

        if (referReceiptItemIdList.size() > 0){
            QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, referReceiptItemIdList);
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);

            Map<Long, ErpPurchaseReceiptItem> erpPurchaseMap = erpPurchaseReceiptItemList.stream()
                    .collect(Collectors.toMap(ErpPurchaseReceiptItem::getId, obj -> obj));

            headDTO.getItemList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(itemDTO -> {
                ErpPurchaseReceiptItem erpPurchaseList = erpPurchaseMap.get(itemDTO.getReferReceiptItemId());
                if (null != erpPurchaseList && "1".equals(erpPurchaseList.getSubjectType())){
                    itemDTO.setMatName(erpPurchaseList.getMatNameBack());
                }
            });

        }
        if(UtilCollection.isEmpty(headDTO.getNumberNoticeWaybillDTOList())){
            // 一个质检会签有多供数量和不合格数量时, 同时生成两张数量差异单据时, 根据之前会签单号查询
            List<BizReceiptWaybill> numberNoticeWaybillList = bizReceiptWaybillDataWrap.list(new QueryWrapper<BizReceiptWaybill>().lambda().in(BizReceiptWaybill::getMoreNumberInconformityNoticeHeadId, headDTO.getId()));
            List<BizReceiptWaybillDTO> numberNoticeWaybillDTOList = UtilCollection.toList(numberNoticeWaybillList, BizReceiptWaybillDTO.class);
            dataFillService.fillAttr(numberNoticeWaybillDTOList);
            headDTO.setNumberNoticeWaybillDTOList(numberNoticeWaybillDTOList);
            if(headDTO.getNumberNoticeWaybillDTOList()!=null){
                headDTO.getNumberNoticeWaybillDTOList().forEach(p -> {
                    p.setTotalPrice(p.getMoreQty().multiply(p.getPrice()));
                });
                fillPrintInfo(headDTO);
            }

        } else {
            if(headDTO.getNumberNoticeWaybillDTOList()!=null){
                headDTO.getNumberNoticeWaybillDTOList().forEach(p -> {
                    p.setTotalPrice(p.getUnarrivalQty().multiply(p.getPrice()));
                });
                fillPrintInfo(headDTO);
            }
        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置成套设备数量差异通知单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }



    public void fillPrintInfo( BizReceiptInconformityHeadDTO headDTO){
        headDTO.setWareContractDept(Const.wareContractDept); //仓储承包商
        BizReceiptWaybillDTO waybill=headDTO.getNumberNoticeWaybillDTOList().get(0);
        // 获取送货通知单
        BizReceiptRegisterHead registerHead = bizReceiptRegisterHeadDataWrap.getById(waybill.getArrivalRegisterHeadId()) ;
        headDTO.setReceiveDate(registerHead.getReceiveDate());

        QueryWrapper<BizReceiptInspectUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptInspectUser::getHeadId, waybill.getSignInspectHeadId());
        List<BizReceiptInspectUser> userDTOList = bizReceiptInspectUserDataWrap.list(queryWrapper);

        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        if (!CollectionUtils.isEmpty(userDTOList)) {
            for (int i = 0; i < userDTOList.size(); i++) {
                BizReceiptInspectUser userDTO = userDTOList.get(i);
                String inspectUserName = userDTO.getInspectUserName();
                if (StringUtils.isNoneBlank(inspectUserName) && inspectUserName.startsWith("data:image/svg")) {
                    if (i == 0) {
                        headDTO.setSign1(inspectUserName);
                        headDTO.setSignDate1(headDTO.getSubmitTime());
                        continue;
                    }
                    if (i == 1) {
                        headDTO.setSign2(inspectUserName);
                        headDTO.setSignDate2(headDTO.getSubmitTime());
                        continue;
                    }
                    if (i == 4) {
                        headDTO.setSign3(inspectUserName);
                        headDTO.setSignDate3(headDTO.getSubmitTime());
                        continue;
                    }
                    if (i == 5) {
                        headDTO.setSign4(inspectUserName);
                        headDTO.setSignDate4(headDTO.getSubmitTime());
                    }
                }
            }
        }

    }

    /**
     * 保存成套设备数量差异通知单
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "不符合项单"}
     * @out ctx 出参 {"receiptCode" : "不符合项单单号"},{@link BizReceiptInspectHeadDTO : "已保存的不符合项单}
     */
    public void saveInconformity(BizContext ctx) {
        // 入参上下文 - 要保存的不符合项单
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        headDTO.setReceiptStatus(receiptStatus);
        headDTO.setCreateUserId(user.getId());
        headDTO.setModifyUserId(user.getId());
        headDTO.setCreateTime(null);
        headDTO.setModifyTime(UtilDate.getNow());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新不符合项单
            bizReceiptInconformityHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptInconformityItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptInconformityItem::getHeadId, headDTO.getId());
            bizReceiptInconformityItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
//            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_INCONFORMITY_NOTICE.getValue());
            // SKX-EPH4YYXX-DII-ZZZZ
            receiptCode = "SK" + unitizedDeliveryNoticeComponent.queryDeliveryNoticeHead(headDTO.getItemList().get(0).getPreReceiptType(), headDTO.getItemList().get(0).getPreReceiptHeadId()).getUnit()
                    + "-EPH4" + UtilDate.getYearMonth(new Date()) + "-DII-" + bizCommonService.getNextSequence("inconformity");
            headDTO.setReceiptCode(receiptCode);
            bizReceiptInconformityHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptInconformityItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            itemDTO.setItemStatus(receiptStatus);
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        bizReceiptInconformityItemDataWrap.saveBatchDto(headDTO.getItemList());
        /* ********************** item处理结束 *************************/
        /* ********************** waybill处理开始 *************************/
        // 行项目按行号分组
        Map<Long, List<BizReceiptInconformityItemDTO>> itemMap = headDTO.getItemList().stream().collect(Collectors.groupingBy(BizReceiptInconformityItemDTO::getPreReceiptItemId));
        for (BizReceiptWaybillDTO waybillDTO : headDTO.getNumberNoticeWaybillDTOList()) {
            if(headDTO.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF_MORE.getValue())){
                // 多供判断
                waybillDTO.setMoreNumberInconformityNoticeHeadId(headDTO.getId());
                waybillDTO.setMoreNumberInconformityNoticeItemId(itemMap.get(waybillDTO.getSignInspectItemId()).get(0).getId());
                waybillDTO.setMoreNumberInconformityNoticeItemRid(itemMap.get(waybillDTO.getSignInspectItemId()).get(0).getRid());

            } else {
                waybillDTO.setNumberInconformityNoticeHeadId(headDTO.getId());
                waybillDTO.setNumberInconformityNoticeItemId(itemMap.get(waybillDTO.getSignInspectItemId()).get(0).getId());
                waybillDTO.setNumberInconformityNoticeItemRid(itemMap.get(waybillDTO.getSignInspectItemId()).get(0).getRid());
            }
            waybillDTO.setGvnbh(headDTO.getReceiptCode());
        }
        if(headDTO.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF_MORE.getValue())) {
            bizReceiptWaybillDataWrap.updateWaybillByMoreNumberInconformityNotice(headDTO.getNumberNoticeWaybillDTOList());
        } else {
            bizReceiptWaybillDataWrap.updateWaybillByNumberInconformityNotice(headDTO.getNumberNoticeWaybillDTOList());
        }

        /* ********************** waybill处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(headDTO);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的不符合项单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }
    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的不符合项单
     */
    public void saveReceiptTree(BizReceiptInconformityHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptInconformityItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }
    /**
     * 按钮组
     *
     * @param headDTO 成套设备数量差异通知单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInconformityHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        }else if(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 提交成套设备数量差异通知单
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要提交的成套设备数量差异通知单"}
     * @out ctx 出参 {"receiptCode" : "成套设备数量差异通知单单号"}
     */
    public void submitInconformityNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交寿维保单时赋值提交时间提交人
        po.setSubmitTime(UtilDate.getNow());
        po.setSubmitUserId(user.getId());
        if(po.getWriteOffPostingDate()==null){
            po.setWriteOffPostingDate(po.getPostingDate());
        }
        po.getNumberNoticeWaybillDTOList().forEach(p -> p.setNWriteOffPostingDate(po.getWriteOffPostingDate()).setNWriteOffReason(po.getWriteOffReason()).setNWriteOffQty(p.getUnarrivalQty()));
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存成套设备数量差异通知单
        this.saveInconformity(ctx);
    }

    /**
     * 生成不符合项处置单
     *
     * @param ctx - 成套设备数量差异通知单提交表单内容
     */
    public void genInconformityMaintain(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();
        List<BizReceiptInconformityItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NAINTAIN.getValue())
                .setIsSafe(po.getIsSafe())
                .setDifferentType(po.getDifferentType())
                .setDepositPoint(po.getDepositPoint())
                .setReceiveDate(po.getReceiveDate())
                .setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe())
                .setPurchaserManagerName(po.getPurchaserManagerName())
                .setSupplierSolveReason(po.getSupplierSolveReason());
        headDTO.setNumberWaybillDTOList(po.getNumberNoticeWaybillDTOList());
        for (BizReceiptInconformityItemDTO itemDTO : po.getItemList()) {
            BizReceiptInconformityItemDTO inconformityDTO = UtilBean.newInstance(itemDTO, BizReceiptInconformityItemDTO.class);
            inconformityDTO.setPreReceiptHeadId(po.getId());
            inconformityDTO.setPreReceiptItemId(itemDTO.getId());
            inconformityDTO.setPreReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NOTICE.getValue());
            inconformityDTO.setPreReceiptQty(itemDTO.getQty());
            itemDTOList.add(inconformityDTO);
        }
        headDTO.setItemList(itemDTOList);
        // 设置入参上下文
        BizContext ctxInconformity = new BizContext();
        ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxInconformity.setCurrentUser(ctx.getCurrentUser());
        // 推送MQ生成不符合项处置单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_NUMBER_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 基于质检会签单创建
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO < BizReceiptInspectHeadDTO > :"质检会签单结果集"}
     */
    public void getPreReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();
        // 查询已完成的质检会签单信息
        List<BizReceiptInspectItemDTO> inspectItemDTOList = bizReceiptInspectItemDataWrap.geInspectItemListByInconformityNotice(po);
        if(UtilCollection.isNotEmpty(inspectItemDTOList)) {
            // 查询质检会签单已创建成套设备数量差异通知的信息
            QueryWrapper<BizReceiptInconformityItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().in(BizReceiptInconformityItem::getPreReceiptItemId, inspectItemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()));
            List<BizReceiptInconformityItemDTO> inconformityItemList = UtilCollection.toList(bizReceiptInconformityItemDataWrap.list(itemQueryWrapper), BizReceiptInconformityItemDTO.class);
            if(UtilCollection.isNotEmpty(inconformityItemList)) {
                // 查询不符合项单对应抬头信息
                QueryWrapper<BizReceiptInconformityHead> headQueryWrapper = new QueryWrapper<>();
                headQueryWrapper.lambda().in(BizReceiptInconformityHead::getId, inconformityItemList.stream().map(p -> p.getHeadId()).collect(Collectors.toSet()));
                List<BizReceiptInconformityHead> headList = bizReceiptInconformityHeadDataWrap.list(headQueryWrapper);
                inconformityItemList.forEach(p -> {
                    headList.forEach(q -> {
                        if(p.getHeadId().equals(q.getId())) {
                            p.setDifferentType(q.getDifferentType());
                        }
                    });
                });
            }
            // 数据封装
            List<BizReceiptInconformityItemDTO> itemDTOList = UtilCollection.toList(inspectItemDTOList, BizReceiptInconformityItemDTO.class);
            itemDTOList.forEach(p -> {
                p.setPreReceiptHeadId(p.getHeadId()).setPreReceiptItemId(p.getId()).setPreReceiptType(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue())
                        .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                inspectItemDTOList.forEach(q -> {
                    if(p.getId().equals(q.getId())) {
                        if(po.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF.getValue())) {
                            p.setPreReceiptQty(q.getUnarrivalQty()).setQty(q.getUnarrivalQty());
                        }else if(po.getDifferentType().equals(EnumDifferentType.QUALITY_DIFF.getValue())) {
                            p.setPreReceiptQty(q.getUnqualifiedQty()).setQty(q.getUnqualifiedQty());
                        }
                    }
                });
                // 标注无法创建的行项目
                inconformityItemList.forEach(q-> {
                    if(p.getId().equals(q.getPreReceiptItemId()) && q.getDifferentType().equals(po.getDifferentType())) {
                        p.setIsCanCreate(false);
                    }
                });
                if(p.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    p.setIsCanCreate(false);
                }
                // 分物资返运填充固定资产物料描述属性
                if (p.getMatId() == 0L){
                    ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(new QueryWrapper<ErpPurchaseReceiptItem>().eq(null != p.getReferReceiptItemId(), "id", p.getReferReceiptItemId()));
                    if (erpPurchaseReceiptItem != null && erpPurchaseReceiptItem.getSubjectType().equals("1")){
                        p.setMatName(erpPurchaseReceiptItem.getMatNameBack());
                    }
                }
                
            });
            // 过滤无法创建的行项目
            itemDTOList = itemDTOList.stream().filter(p -> p.getIsCanCreate().equals(true)).collect(Collectors.toList());
            dataFillService.fillRlatAttrDataList(itemDTOList);
            if (UtilCollection.isNotEmpty(itemDTOList)) {
                BizReceiptInspectHead bizReceiptInspectHead =bizReceiptInspectHeadDataWrap.getById(itemDTOList.get(0).getHeadId());
                // 哥给你改bug了
                headDTO.setPurchaseUserCode(itemDTOList.get(0).getPurchaseUserCode())
                        .setPurchaseUserName(itemDTOList.get(0).getPurchaseUserName())
                        .setPurchaserManagerName(bizReceiptInspectHead.getPurchaserManagerName())
                        .setReceiveDate(inspectItemDTOList.get(0).getReceiveDate())
                        .setDepositPoint(inspectItemDTOList.get(0).getDepositPoint())
                        .setIsSafe(inspectItemDTOList.get(0).getIsSafe())
                        .setDifferentType(po.getDifferentType())
                        .setReceiptType(EnumReceiptType.INCONFORMITY_NOTICE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                        .setDeliveryNoticeDescribe(itemDTOList.get(0).getDeliveryNoticeDescribe())
                        .setItemList(itemDTOList);
            }
        }
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO,
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true)));
    }


    /**
     * 过账前设置行项目账期
     *
     * @param headDTO 单据抬头信息
     * @param user 当前用户
     */
    private void setInPostDate(BizReceiptInconformityHeadDTO headDTO, CurrentUser user) {
        if (UtilCollection.isEmpty(headDTO.getNumberNoticeWaybillDTOList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date writeOffPostingDate = headDTO.getNumberNoticeWaybillDTOList().get(0).getNWriteOffPostingDate();
        if (UtilObject.isNull(writeOffPostingDate)) {
            writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
        for (BizReceiptWaybillDTO waybillDTO : headDTO.getNumberNoticeWaybillDTOList()) {
            waybillDTO.setNWriteOffDocDate(UtilDate.getNow());
            waybillDTO.setNWriteOffPostingDate(writeOffPostingDate);
        }
    }
    /**
     * 更新单据状态
     *
     * @param headDTO 不符合项单抬头
     * @param itemDTOList 不符合项单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptInconformityHeadDTO headDTO, List<BizReceiptInconformityItemDTO> itemDTOList, Integer receiptStatus) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptInconformityHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptInconformityHead::getId, headDTO.getId())
                    .set(BizReceiptInconformityHead::getReceiptStatus, receiptStatus);
            bizReceiptInconformityHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptInconformityItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptInconformityItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptInconformityItem::getItemStatus, receiptStatus);
            bizReceiptInconformityItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 根据解决方案处理单据
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要提交的不符合项处置单"}
     */
    public boolean handleReceiptBySolveReasonNew(BizContext ctx) {
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptWaybillDTO> itemListNotSync = new ArrayList<>();
        List<BizReceiptWaybillDTO> itemListUpdate = new ArrayList<>();
        Map<Long, BigDecimal> recoverMap = new HashMap<>(itemListNotSync.size());
        // 主要配件需要按照子部件的单价与数量计算出头部件的金额，并对该金额进行104冲销
        List<BizReceiptWaybillDTO> waybillDTOList = po.getNumberNoticeWaybillDTOList();
        for (BizReceiptWaybillDTO waybillDTO : waybillDTOList) {
            if (waybillDTO.getNWriteOffQty().compareTo(BigDecimal.ZERO) == 0
                    || waybillDTO.getPrice().compareTo(BigDecimal.ZERO) == 0
                    // 多供不走sap接口
                    || po.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF_MORE.getValue())) {
                waybillDTO.setQIsWriteOff(EnumRealYn.TRUE.getIntValue());
                itemListUpdate.add(waybillDTO);
                continue;
            }
            BizReceiptInconformityItemDTO inconformityItemDTO =
                    UtilBean.newInstance(bizReceiptInconformityItemDataWrap.getById(waybillDTO.getNumberInconformityNoticeItemId()), BizReceiptInconformityItemDTO.class);
            dataFillService.fillAttr(inconformityItemDTO);
            waybillDTO.setNumberInconformityItem(inconformityItemDTO);
            itemListNotSync.add(waybillDTO);
        }
        if (!CollectionUtils.isEmpty(itemListUpdate))
            bizReceiptWaybillDataWrap.updateBatchDtoById(itemListUpdate);
        boolean result = writeOffToSap(ctx, itemListNotSync, recoverMap);
        return result;
    }
    /**
     * SAP冲销
     *
     */
    private boolean writeOffToSap(BizContext ctx, List<BizReceiptWaybillDTO> itemListNotSync, Map<Long, BigDecimal> recoverMap) {
        if (CollectionUtils.isEmpty(itemListNotSync)) {
            return true;
        }
        Integer trueFlag = EnumRealYn.TRUE.getIntValue();
        itemListNotSync.forEach(item -> {
            BigDecimal remainder = item.getRemainder();
            Integer useSignRemainder = item.getUseSignRemainder();
            if (useSignRemainder == trueFlag && remainder.compareTo(BigDecimal.ZERO) > 0) {
                recoverMap.put(item.getId(), item.getRemainder());
                item.setRemainder(BigDecimal.ZERO);
            }
        });
        //入参上下文
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser cUser = ctx.getCurrentUser();
        /* ******** 设置冲销账期 ******** */
        this.setInPostDate(headDTO, cUser);
        //  运单行单价等于0的 更新运单过账信息【过帐日期、凭证时间、sap过账标识】
        List<BizReceiptWaybillDTO> unToSapItemDTOList = itemListNotSync.stream().filter(p ->UtilNumber.isEmpty(p.getPrice())).collect(Collectors.toList());

        if(UtilCollection.isNotEmpty(unToSapItemDTOList)) {
            unToSapItemDTOList.forEach(p -> p.setNIsWriteOff(EnumRealYn.TRUE.getIntValue()));
            bizReceiptWaybillDataWrap.updateBatchDtoById(unToSapItemDTOList);
        }
        // 过滤存在单价的行项目，存在单价的行项目需要与SAP交互
        itemListNotSync = itemListNotSync.stream()
                .filter(item -> !UtilNumber.isEmpty(item.getPrice())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(itemListNotSync)) {
            return true;
        }
        itemListNotSync.get(0).setReceiptCode(headDTO.getReceiptCode());
        ErpReturnObject returnObj = sapInterfaceService.postingNew(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
        /* ******** 调用sap后处理开始 ******** */
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            // 更新冲销物料凭证号
            List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
            if (UtilCollection.isNotEmpty(returnObjectItems)) {
                for (BizReceiptWaybillDTO wayBillDTO : itemListNotSync) {
                    ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                            .filter(item -> item.getReceiptCode().equals(wayBillDTO.getNumberInconformityItem().getReceiptCode())
                                    && item.getReceiptRid().equals(wayBillDTO.getNumberInconformityNoticeItemRid()))
                            .findFirst().orElse(null);
                    if (null == currentReturnObject) {
                        continue;
                    }
                    wayBillDTO.setNWriteOffMatDocCode(currentReturnObject.getMatDocCode());
                    wayBillDTO.setNWriteOffMatDocRid(currentReturnObject.getMatDocRid());
                    wayBillDTO.setNWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                    wayBillDTO.setNIsWriteOff(EnumRealYn.TRUE.getIntValue());
                    wayBillDTO.setNDmbtr(currentReturnObject.getDmbtr());
                    Long id = wayBillDTO.getId();
                    BigDecimal remainderMapping = recoverMap.get(id);
                    BigDecimal remainder = wayBillDTO.getRemainder();
                    if (remainder.compareTo(BigDecimal.ZERO) > 0) {
                        wayBillDTO.setUseSignRemainder(trueFlag);
                        wayBillDTO.setRemainderIncon(remainder);
                        wayBillDTO.setRemainder(BigDecimal.ZERO);
                    } else if (remainderMapping != null) {
                        wayBillDTO.setRemainder(remainderMapping);
                    }
                };
                // 更新运单冲销信息【物料凭证编号、物料凭证的行序号、物料凭证年度、过帐日期、凭证时间、sap冲销标识、冲销原因、本位币金额】
                bizReceiptWaybillDataWrap.updateBatchDtoById(itemListNotSync);
            }
            /* ******** 调用sap后处理结束 ******** */
        } else {
            log.error("不符合项处置单{}SAP冲销过账失败", headDTO.getReceiptCode());
            // 更新不符合项处置单head、item状态-未同步
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            return false;
        }
        return true;
    }

}
