package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.unitized.service.component.BizUnitizedUPSplitHeadComponent;
import com.inossem.wms.bizdomain.unitized.service.component.BizUnitizedUPSplitItemComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizUnitizedUPSplitItemDTO;
import com.inossem.wms.common.model.bizdomain.unitized.vo.BizUnitizedUPSplitHeadPageVO;
import com.inossem.wms.common.model.bizdomain.unitized.vo.BizUnitizedUPSplitPreItemVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 成套设备UP码拆分抬头 服务接口
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Service
public class BizUnitizedUPSplitHeadService {

    @Autowired
    private BizUnitizedUPSplitHeadComponent bizUnitizedUPSplitHeadComponent;

    @Autowired
    private BizUnitizedUPSplitItemComponent bizUnitizedUPSplitItemComponent;

    /**
      * 获取成套设备UP码拆分抬头 - 分页列表
     */
    public PageObjectVO<BizUnitizedUPSplitHeadPageVO> getPage(BizContext ctx) {
       return bizUnitizedUPSplitHeadComponent.getPage(ctx);
    }

    /**
     * 获取成套设备UP码拆分抬头详情
     */
    public void getInfo(BizContext ctx) {
        bizUnitizedUPSplitHeadComponent.getInfo(ctx);
        // 开启审批
        bizUnitizedUPSplitHeadComponent.setExtendWf(ctx);
        // 开启操作日志
        bizUnitizedUPSplitHeadComponent.setExtendOperationLog(ctx);
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        bizUnitizedUPSplitHeadComponent.check(ctx);
        //创建批次编码与标签编码
        bizUnitizedUPSplitItemComponent.fillSplitItemCode(ctx);
        //保存
        bizUnitizedUPSplitHeadComponent.addOrUpdate(ctx);
        // 保存操作日志
        bizUnitizedUPSplitHeadComponent.saveBizReceiptOperationLog(ctx);
        // todo 当前审批模型未确认，以单人审批代替，在模型确认后 修改监听器 - ListenerUnitizedUPSplit 与模型 unitizedUPSplit-1
        // 发起审批
        bizUnitizedUPSplitHeadComponent.startWorkFlow(ctx);
    }

    /**
     * 查询物资类型下拉列表
     */
    public MultiResultVO<String> getMatTypeDropDown() {
        return bizUnitizedUPSplitHeadComponent.getMatTypeDropDown();
    }

    /**
     * 查询可拆分的物料
     */
    public MultiResultVO<BizUnitizedUPSplitPreItemVO> getSplitPreItemList(BizContext ctx) {
        return bizUnitizedUPSplitItemComponent.getSplitPreItemList(ctx);
    }

    public void init(BizContext ctx) {
        // 设置初始化信息
        bizUnitizedUPSplitHeadComponent.setInit(ctx);
        // 开启操作日志
        bizUnitizedUPSplitHeadComponent.setExtendOperationLog(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_CALLBACK_UNITIZED_UP_SPLIT)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 审批回调
        bizUnitizedUPSplitHeadComponent.approvalCallback(wfReceiptCo);

    }
}
