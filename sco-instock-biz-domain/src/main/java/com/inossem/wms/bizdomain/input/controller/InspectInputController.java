package com.inossem.wms.bizdomain.input.controller;

import com.inossem.wms.bizdomain.input.service.biz.InspectInputService;
import com.inossem.wms.common.annotation.RepeatSubmit;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.po.SignatureImgInfoPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
//import sun.misc.BASE64Decoder;
import org.apache.commons.codec.binary.Base64;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <p>
 * 验收入库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */

@RestController
@Api(tags = "入库管理-验收入库")
public class InspectInputController {

    @Autowired
    private InspectInputService inspectInputService;

    /**
     * 验收入库-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "验收入库-初始化", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/input/inspect-inputs/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        inspectInputService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 验收入库-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 验收入库单分页
     */
    @ApiOperation(value = "验收入库-分页", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/input/inspect-inputs/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po, BizContext ctx) {
        inspectInputService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 验收入库-详情
     *
     * @param id 验收入库主键
     * @param ctx 入参上下文 {"id":"验收入库主键"}
     */
    @ApiOperation(value = "验收入库-详情", tags = {"入库管理-验收入库"})
    @GetMapping(value = "/input/inspect-inputs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        inspectInputService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 油品入库-推送加油站平台-接口1
     */
    @ApiOperation(value = "油品入库-推送加油站平台-接口1", tags = {"入库管理-油品入库"})
    @GetMapping(value = "/input/inspect-inputs/sync/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> sync(@PathVariable("id") Long id, BizContext ctx) {
        inspectInputService.sync(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 验收入库-保存
     *
     * @param po 保存验收入库表单参数
     * @param ctx 入参上下文 {"po":"保存验收入库表单参数"}
     */
    @ApiOperation(value = "验收入库-保存", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/input/inspect-inputs/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        inspectInputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 验收入库-提交
     *
     * @param po 提交验收入库表单参数
     * @param ctx 入参上下文 {"po":"提交验收入库表单参数"}
     */
    @ApiOperation(value = "验收入库-提交", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/input/inspect-inputs/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        inspectInputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_SUBMIT_SUCCESS, code);
    }

    /**
     * 验收入库-传输附件
     *
     * @param po 提交验收入库表单参数
     * @param ctx 入参上下文 {"po":"提交验收入库表单参数"}
     */
    @ApiOperation(value = "验收入库-传输附件", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/input/inspect-inputs/attsyn", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> attSynToSap(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        inspectInputService.attSynToSap(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_SUBMIT_SUCCESS, po.getReceiptCode());
    }

    /**
     * 验收入库-过账
     *
     * @param po 保存验收入库表单参数
     * @param ctx 入参上下文 {"po":"保存验收入库表单参数"}
     */
    @ApiOperation(value = "验收入库-过账", tags = {"入库管理-验收入库"})
    @RepeatSubmit
    @PostMapping(value = "/input/inspect-inputs/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> post(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        inspectInputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 验收入库-冲销
     *
     * @param po 验收入库冲销表单参数
     * @param ctx 入参上下文 {"po":"验收入库冲销表单参数"}
     */
    @ApiOperation(value = "验收入库-冲销", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/input/inspect-inputs/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInputWriteOffPO po, BizContext ctx) {
        inspectInputService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 验收入库单-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "验收入库单-删除", tags = {"入库管理-验收入库"})
    @DeleteMapping("/input/inspect-inputs/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        inspectInputService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_DELETE_SUCCESS, po.getReceiptCode());
    }


    /**
     * 验收入库单-物料标签打印-PDA
     * @auth zhibo
     * @param po 打印入参
     * @param ctx 请求上下文
     * @return 领料申请单数据
     */
    @ApiOperation(value = "验收入库单-物料标签打印-PDA", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/input/inspect-inputs/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptInputHeadDTO> po, BizContext ctx) {
        inspectInputService.boxApplyLabelPrint(ctx);
        List<LabelReceiptInputBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }



    /**
     * 签名信息-保存
     *
     * @param po 保存签名信息
     * @param
     */
    @ApiOperation(value = "签名信息-保存", tags = {"签名信息-保存"})
    @PostMapping(value = "/input/inspect-inputs/save/signature", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> signatureImgUpload(@RequestBody List<SignatureImgInfoPO> po, BizContext ctx) {
        List<SignatureImgInfoPO> poData = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        System.out.println("=="+poData.toString()+"==");
        String imgFilePath = "D:\\work\\img";
        // 获取图片的base64
        List<String> base64List = po.stream().map(SignatureImgInfoPO::getImg).collect(Collectors.toList());
        base64List.forEach(item -> getImg(item, imgFilePath));
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_DELETE_SUCCESS, null);
    }

    private static boolean getImg(String imgBase, String imgFilePath) {
        if (imgBase == null) // 图像数据为空
            return false;
//        BASE64Decoder decoder = new BASE64Decoder();
        // 前台在用Ajax传base64值的时候会把base64中的+换成空格，所以需要替换回来。
        String baseValue = imgBase.replaceAll(" ", "+");
        try {
            // Base64解码
            //去除base64中无用的部分
//            byte[] b = decoder.decodeBuffer(baseValue.replace("data:image/jpeg;base64,", ""));
            byte[] b = Base64.decodeBase64(baseValue.replace("data:image/jpeg;base64,", ""));
            imgBase = imgBase.replace("base64,", "");
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            // 生成jpg图片
            Random random = new Random();
            OutputStream out = new FileOutputStream(new File(imgFilePath.toString()+"\\"+ random.nextInt(1000000) +".jpg"));
            out.write(b);
            out.flush();
            out.close();
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    /**
     * 验收入库-详情
     *
     * @param id 验收入库主键
     * @param ctx 入参上下文 {"id":"验收入库主键"}
     */
    @ApiOperation(value = "验收入库-打印详情", tags = {"入库管理-验收入库"})
    @GetMapping(value = "/input/inspect-inputs/print/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getPrintInfo(@PathVariable("id") Long id, BizContext ctx) {
        inspectInputService.getPrintInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}
