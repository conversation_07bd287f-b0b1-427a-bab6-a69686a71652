package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedTransportLeisureService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.print.label.LabelTransportLeisureBox;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 成套设备 闲置物资标记
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "成套设备-闲置管理")
public class UnitizedTransportLeisureController {

    @Autowired
    private UnitizedTransportLeisureService transportLeisureService;

    @ApiOperation(value = "列表-分页", tags = {"成套设备-闲置物资标记"})
    @PostMapping(value = "/unitized/leisure/results")
    public BaseResult getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportLeisureService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "列表", tags = {"成套设备-闲置物资标记"})
    @PostMapping(value = "/unitized/leisure/list")
    public BaseResult getList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportLeisureService.getList(ctx);
        MultiResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "详情", tags = {"成套设备-闲置物资标记"})
    @GetMapping(value = "/unitized/leisure/{id}")
    public BaseResult getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportLeisureService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "保存", tags = {"成套设备-闲置物资标记"})
    @PostMapping(value = "/unitized/leisure/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportLeisureService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "提交", tags = {"成套设备-闲置物资标记"})
    @PostMapping(value = "/unitized/leisure/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportLeisureService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "删除", tags = {"成套设备-闲置物资标记"})
    @DeleteMapping("/unitized/leisure/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportLeisureService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "验收入库单-物料标签打印-PDA", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/unitized/leisure/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptTransportHeadDTO> po, BizContext ctx) {
        transportLeisureService.boxApplyLabelPrint(ctx);
        List<LabelTransportLeisureBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

}
