package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.apply.service.component.MaterialOrderOutApplyComponent;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.bizdomain.output.service.component.callback.OutputTaskCallbackComponent;
import com.inossem.wms.bizdomain.task.service.component.UnLoadComponent;
import com.inossem.wms.bizdomain.unitized.service.component.UnitizedMaterialOutputComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/24 13:32
 */
@Service
public class UnitizedMaterialOutputService {
    @Autowired
    private UnitizedMaterialOutputComponent unitizedMaterialOutputComponent;
    @Autowired
    private UnLoadComponent unLoadComponent;
    @Autowired
    private OutputComponent outputComponent;


    @Autowired
    private MaterialOrderOutApplyComponent materialOrderOutApplyComponent;

    @Autowired
    private OutputTaskCallbackComponent outputTaskCallbackComponent;


    /**
     *初始化出库单
     * @param ctx - 上下文
     */
    public void init(BizContext ctx) {
        // 设置按钮
        unitizedMaterialOutputComponent.setInit(ctx);
        // 开启单据流
        outputComponent.setExtendRelation(ctx);
        // 开启审批
         outputComponent.setExtendWf(ctx);
        // 开启附件
        outputComponent.setExtendAttachment(ctx);
        // 开启操作日志
        outputComponent.setExtendOperationLog(ctx);

    }

    /**
     * 分页获取出库列表
     * @param ctx
     */
    public void getPage(BizContext ctx) {
//        outputComponent.getPage(ctx);
        unitizedMaterialOutputComponent.getPage(ctx);
    }

    /**
     * 获取物料库存
     */
    public void getMatStockInfo(BizContext ctx) {
        this.getMatStockInfoNoSameTime(ctx);
    }


    /**
     * 获取成套领料出库详情
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        // 获取详情
        unitizedMaterialOutputComponent.getInfo(ctx);
        // 设置批次图片
        outputComponent.setBatchImg(ctx);
        // 开启单据流
        outputComponent.setExtendRelation(ctx);
        // 开启审批
        // outputComponent.setExtendWf(ctx);
        // 开启附件
        outputComponent.setExtendAttachment(ctx);
        // 开启操作日志
        outputComponent.setExtendOperationLog(ctx);
    }

    /**
     * 成套领料出库详情-打印
     * @param ctx
     */
    public void printInfo(BizContext ctx) {
        // 获取打印数据
        unitizedMaterialOutputComponent.getPrintInfo(ctx);
    }


    /**
     * 保存单据
     * @param ctx
     */
    public void save(BizContext ctx) {
        this.saveNoSameTime(ctx);
    }

    /**
     * 先作业 提交
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 校验
        unitizedMaterialOutputComponent.checkSubmit(ctx);
        // 提交单据
        unitizedMaterialOutputComponent.submitReceipt(ctx);
        // 占用库存
       // outputComponent.occupyStock(ctx);
        // 保存操作日志
        outputComponent.saveBizReceiptOperationLog(ctx);
        // 保存附件
        outputComponent.saveBizReceiptAttachment(ctx);
        // 保存单据流
        unitizedMaterialOutputComponent.saveReceiptTree(ctx);
        // 【先作业模式】状态变更已提交
        outputComponent.updateStatusSubmitted(ctx);
        // 发送下架请求
        outputComponent.addTaskRequest(ctx);
        // 若无可出库数量,单据完成,释放锁定的冲减数量
        outputComponent.checkCompleted(ctx);
    }

    /**
     * 获取配货信息
     * @param ctx
     */
    public void getItemInfo(BizContext ctx) {
        this.getItemInfoNoSameTime(ctx);
    }


    /**
     * 手动点击过账
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {
        this.postTaskFirst(ctx);
    }

    /**
     * 冲销
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {
        // 校验
        unitizedMaterialOutputComponent.checkWriteOff(ctx);
        // 生成冲销移动类型【非先过账模式】
        unitizedMaterialOutputComponent.generateWriteOffInsMoveTypeAndCheckNonPostFirst(ctx);
        // sap冲销
        unitizedMaterialOutputComponent.writeOffToSap(ctx);
        // InStock冲销
        unitizedMaterialOutputComponent.writeOffToInStock(ctx);
        //单品冲销【先过账模式】
        outputComponent.writeOffToInStockLable(ctx);
        // 推送冲销修改请求
        outputComponent.addWriteOffRequest(ctx);
        // 更新批次维保日期
        unitizedMaterialOutputComponent.updateBatchMaintenanceDate(ctx);
    }
    @Transactional(rollbackFor = Exception.class)
    public void writeOffByByRequire(BizContext ctx) {
        // 校验
        unitizedMaterialOutputComponent.checkWriteOffByRequire(ctx);
        // 生成冲销移动类型【非先过账模式】
        unitizedMaterialOutputComponent.generateWriteOffInsMoveTypeByRequire(ctx);
        // sap冲销
        unitizedMaterialOutputComponent.writeOffToSapByRequire(ctx);
        // InStock冲销
        unitizedMaterialOutputComponent.writeOffToInStock(ctx);
        //单品冲销【先过账模式】
        outputComponent.writeOffToInStockLableByRequire(ctx);
        // 更新批次维保日期
        unitizedMaterialOutputComponent.updateBatchMaintenanceDateByRequire(ctx);
    }

    /*============================================*/
    /**
     * 非同时模式 获取物料库存
     * @param ctx
     */
    private void getMatStockInfoNoSameTime(BizContext ctx) {
        // 获取物料特性库存【非同时模式】
        outputComponent.getMatFeatureStock(ctx);
    }


    /**
     * 非同时模式 保存
     * @param ctx
     */
    private void saveNoSameTime(BizContext ctx) {
        // 校验数据
        outputComponent.checkSave(ctx);
        // 保存单据抬头及下级信息
        unitizedMaterialOutputComponent.saveReceipt(ctx);
        // 保存操作日志
        outputComponent.saveBizReceiptOperationLog(ctx);
        // 保存附件
        outputComponent.saveBizReceiptAttachment(ctx);
        // 保存单据流
        unitizedMaterialOutputComponent.saveReceiptTree(ctx);
    }

    /**
     * 非同时模式 获取配货信息
     * @param ctx
     */
    private void getItemInfoNoSameTime(BizContext ctx) {
        // 配置单据类型
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
        // 获取配货信息(特性库存)【非同时模式】
        materialOrderOutApplyComponent.getItemInfoByFeatureStock(ctx);
    }


    /**
     * 先作业模式 手动点击过账
     * @param ctx
     */
    private void postTaskFirst(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer receiptStatus = headDTO.getReceiptStatus();
        // 校验
        outputComponent.checkPost(ctx);
        // 保存签名
        unitizedMaterialOutputComponent.saveAutograph(ctx);
        // 生成移动类型
        unitizedMaterialOutputComponent.generateInsMoveTypeAndCheck(ctx);
        // sap过账
        unitizedMaterialOutputComponent.postToSapNew(ctx);

        //出库之后不关闭预留
//        // 调用sap关闭预留信息，整单关闭、删除
        unitizedMaterialOutputComponent.closeReservationListNew(ctx);
        // InStock过账
        outputComponent.postToInStock(ctx);
        // 过账后删除库存占用
        outputComponent.deleteOccupyByPre(ctx);
        // 过账后记录超发
        outputComponent.saveOver(ctx);
        // 过账后释放锁定的冲减数量
        outputComponent.updateSubtract(ctx);
        // 单据状态已完成
        outputComponent.updateStatusCompleted(ctx);
        // 作业中过账时处理下架作业请求
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue())) {
            unLoadComponent.closeUnLoadReq(headDTO.getId());
        }
    }

    /**
     * 关闭预留
     * @param ctx
     */
    public void close(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer receiptStatus = headDTO.getReceiptStatus();

        // 调用sap关闭预留信息，整单关闭、删除
        unitizedMaterialOutputComponent.closeReservationListNew(ctx);
        // 单据状态已完成
        outputComponent.updateStatusCompleted(ctx);
        // 删除库存占用
        outputComponent.deleteOccupyByPre(ctx);
        // 删除锁定冲减
        outputComponent.deleteSubtractByPre(ctx);
        // 已提交状态关联预留时处理下架作业请求
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue())) {
            unLoadComponent.closeUnLoadReq(headDTO.getId());
        }
    }

    /**
     * 删除单据
     * @param ctx
     */
    public void delete(BizContext ctx) {
        this.deleteTaskFirst(ctx);
    }


    /**
     * 先作业模式 删除单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskFirst(BizContext ctx) {
        // 校验
        unitizedMaterialOutputComponent.checkDelete(ctx);
        outputComponent.checkTaskStatus(ctx);

        // 删除单据
        outputComponent.deleteReceipt(ctx);
        // 删除库存占用
       // outputComponent.deleteOccupyStock(ctx);
        // 删除单据流
        unitizedMaterialOutputComponent.deleteReceiptTree(ctx);
        // 删除附件
        outputComponent.deleteBizReceiptAttachment(ctx);
        // 删除作业请求
        outputComponent.cancelTaskRequest(ctx);
    }


    /**
     * 下架回调
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputTaskCallbackComponent#updateQtyAndStatus", "outputComponent#isTask",
            "outputComponent#checkPost", "matReqOutputComponent#generateInsMoveTypeAndCheck", "outputComponent#postToSap",
            "outputComponent#postToInStock", "outputComponent#deleteOccupyStock", "outputComponent#updateStatusCompleted"})
    @WmsMQListener(tags = TagConst.UNITIZED_TASK_MAT_REQ_OUTPUT_CALLBACK)
    public void callbackByTask(BizContext ctx) {

        // TODO-BO: 2022/5/23 这里注释掉，需手动过账
        // 更新数量和状态
        outputTaskCallbackComponent.updateQtyAndStatus(ctx);

        /*
        // 先作业模式
        if (outputComponent.isTask(ctx)) {
            // 校验
            outputComponent.checkPost(ctx);
            // 生成移动类型
            matReqOutputComponent.generateInsMoveTypeAndCheck(ctx);
            // sap过账
            outputComponent.postToSap(ctx);
            // InStock过账
            outputComponent.postToInStock(ctx);
            // 删除占用库存
            outputComponent.deleteOccupyStock(ctx);
            // 单据状态已完成
            outputComponent.updateStatusCompleted(ctx);
        }

         */
    }

}
