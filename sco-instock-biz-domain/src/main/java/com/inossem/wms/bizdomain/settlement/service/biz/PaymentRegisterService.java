package com.inossem.wms.bizdomain.settlement.service.biz;

import com.inossem.wms.bizdomain.settlement.service.component.PaymentRegisterComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentRegisterHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Service
public class PaymentRegisterService {

    @Autowired
    private PaymentRegisterComponent paymentRegisterComponent;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        // 设置按钮
        paymentRegisterComponent.setInit(ctx);

        // 开启附件
        paymentRegisterComponent.setExtendAttachment(ctx);

        // 开启操作日志
        paymentRegisterComponent.setExtendOperationLog(ctx);

    }

    /**
     * 分页查询
     */
    public void getPageVo(BizContext ctx) {
        paymentRegisterComponent.getPageVo(ctx);
    }

    /**
     * 付款结算查询
     */
    public void selectPaymentSettlement(BizContext ctx) {
        paymentRegisterComponent.selectPaymentSettlement(ctx);
    }


    /**
     * 付款结算-详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {

        // 获取详情
        paymentRegisterComponent.getInfo(ctx);

        // 开启单据流
        paymentRegisterComponent.setInfoExtendRelation(ctx);

        // 开启附件
        paymentRegisterComponent.setExtendAttachment(ctx);

        // 开启审批
        paymentRegisterComponent.setExtendWf(ctx);

    }

    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 校验
        paymentRegisterComponent.checkSaveData(ctx);

        // 保存-付款结算
        paymentRegisterComponent.save(ctx);

        // 保存操作日志
        paymentRegisterComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        paymentRegisterComponent.saveBizReceiptAttachment(ctx);

    }

    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交-校验付款计划入参
        paymentRegisterComponent.checkSaveData(ctx);

        // 提交付款计划
        paymentRegisterComponent.submit(ctx);

        // 保存操作日志
        paymentRegisterComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        paymentRegisterComponent.saveBizReceiptAttachment(ctx);

        // 入参上下文
        BizReceiptPaymentRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(po.getReceiptStatus())) {
            // 开启审批
            paymentRegisterComponent.startWorkFlow(ctx);
        } else {
            // 推送srm
            paymentRegisterComponent.pullSrm(ctx);
        }
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_PAYMENT_REGISTER)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        paymentRegisterComponent.approvalCallback(wfReceiptCo);
    }

    /**
     * 撤销
     *
     * @param ctx 入参上下文
     */
    public void revoke(BizContext ctx) {
        // 获取单据详情
        paymentRegisterComponent.getInfo(ctx);
        // 撤销审批流
        paymentRegisterComponent.revokeProcessInstance(ctx);
        // 更新单据草稿
        paymentRegisterComponent.updateStatusDraft(ctx);
    }

    public void getRegisterInfo(BizContext ctx) {
        paymentRegisterComponent.getRegisterInfo(ctx);
    }

}
