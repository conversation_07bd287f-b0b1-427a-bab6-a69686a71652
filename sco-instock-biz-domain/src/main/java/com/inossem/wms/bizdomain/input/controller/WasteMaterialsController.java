package com.inossem.wms.bizdomain.input.controller;


import com.inossem.wms.bizdomain.input.service.biz.WasterMaterialsService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * <AUTHOR>
 * <p>
 *     废旧物资入库-前端控制器
 * </p>
 * @date 2022/4/24 13:56
 */
@RestController
public class WasteMaterialsController {

    @Autowired
    private WasterMaterialsService wasterMaterialsService;


    /**
     * 废旧物资入库-初始化
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "废旧物资入库-初始化", tags = {"入库管理-废旧物资入库"})
    @PostMapping(value = "/input/waster-materials/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        wasterMaterialsService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 废旧物资入库-分页
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 废旧物资入库单分页
     */
    @ApiOperation(value = "废旧物资入库-分页", tags = {"入库管理-废旧物资入库"})
    @PostMapping(value = "/input/waster-materials/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po,
                                                                   BizContext ctx) {
        wasterMaterialsService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }



    /**
     * 废旧物资入库单-详情
     * @param id 废旧物资入库单主键
     * @param ctx 入参上下文 {"id":"废旧物资入库单主键"}
     */
    @ApiOperation(value = "废旧物资入库单-详情", tags = {"入库管理-废旧物资入库"})
    @GetMapping(value = "/input/waster-materials/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        wasterMaterialsService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 废旧物资入库单-回填出库物料【批次、批次特性】
     * @param po 入库物料信息
     * @param ctx 入参上下文 {"po":"入库物料信息"}
     */
    @ApiOperation(value = "废旧物资入库单-回填出库物料属性值", tags = {"入库管理-废旧物资入库"})
    @PostMapping(value = "/input/waster-materials/set-mat-info", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptInputItemDTO>> setMatInfo(@RequestBody BizReceiptInputHeadDTO po,
                                                                        BizContext ctx) {
        wasterMaterialsService.setMatInfo(ctx);
        MultiResultVO<BizReceiptInputItemDTO> inputItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(inputItemDTOList);
    }


    /**
     * 废旧物资入库单-保存
     *
     * @param po 保存废旧物资入库单参数
     * @param ctx 入参上下文 {"po":"保存其废旧物资入库单参数"}
     */
    @ApiOperation(value = "废旧物资入库单-保存", tags = {"入库管理-废旧物资入库"})
    @PostMapping(value = "/input/waster-materials/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        wasterMaterialsService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }


    /**
     * 废旧物资入库单-提交
     *
     * @param po 提交废旧物资入库单参数
     * @param ctx 入参上下文 {"po":"提交废旧物资入库单参数"}
     */
    @ApiOperation(value = "废旧物资入库单-提交", tags = {"入库管理-废旧物资入库"})
    @PostMapping(value = "/input/waster-materials/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        wasterMaterialsService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }


    /**
     * 验收入库-过账
     *
     * @param po 保存验收入库表单参数
     * @param ctx 入参上下文 {"po":"保存验收入库表单参数"}
     */
    @ApiOperation(value = "废旧物资入库单-过账", tags = {"入库管理-废旧物资入库"})
    @PostMapping(value = "/input/waster-materials/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> post(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        wasterMaterialsService.post(ctx);
        return BaseResult.success(po.getReceiptCode());
    }


    /**
     * 废旧物资入库单-删除
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "废旧物资入库单-删除", tags = {"入库管理-废旧物资入库"})
    @DeleteMapping("/input/waster-materials/ids")
    public BaseResult delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        wasterMaterialsService.delete(ctx);
        return BaseResult.success();
    }


    /**
     * 废旧物资入库单-数据导入
     * @param file
     * @param ctx
     * @return
     */
    @ApiOperation(value = "废旧物资入库单-数据导入", notes = "废旧物资入库单-数据导入", tags = {"入库管理-废旧物资入库"})
    @PostMapping(path = "/input/waster-materials/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInputHeadDTO> importMaterial(@RequestPart("file") MultipartFile file, BizContext ctx) {
        wasterMaterialsService.importWasterData(ctx);
        BizReceiptInputHeadDTO po  = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(po);
    }


    /**
     * 查询报废原因下拉
     *
     * @return 检定结果下拉框
     */
    @ApiOperation(value = "查询是否危险下拉", tags = {"入库管理-废旧物资入库"})
    @GetMapping(path = "/input/waster-materials/is-danger", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<?>> getIsDanger(BizContext ctx) {
        wasterMaterialsService.getIsDanger(ctx);
        MultiResultVO<?> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 废旧物资入库单-物料标签打印
     * @auth zhibo
     * @param po 打印入参
     * @param ctx 请求上下文
     * @return 废旧物资入库单
     */
    @ApiOperation(value = "废旧物资入库单-物料标签打印", tags = {"入库管理-废旧物资入库单"})
    @PostMapping(value = "/input/waster-materials/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptInputHeadDTO> po, BizContext ctx) {
        wasterMaterialsService.boxApplyLabelPrint(ctx);
        List<LabelReceiptInputBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }


}
