package com.inossem.wms.bizdomain.apply.service.biz;

import com.inossem.wms.bizdomain.apply.service.component.EmergencyMatOrderOutApplyComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/23 11:22
 * @desc EmergencyMatOrderOutApplyService
 */
@Service
public class EmergencyMatOrderOutApplyService {

    @Autowired
    private EmergencyMatOrderOutApplyComponent emergencyMatOrderOutApplyComponent;


    /**
     * 获取领用依据枚举
     */
    public void getReceiveBasis(BizContext ctx) {

        // 获取领用依据枚举
        emergencyMatOrderOutApplyComponent.getReceiveBasis(ctx);
    }

    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        // 设置按钮
        emergencyMatOrderOutApplyComponent.setInit(ctx);

        // 开启附件
        emergencyMatOrderOutApplyComponent.setExtendAttachment(ctx);

        // 开启操作日志
        emergencyMatOrderOutApplyComponent.setExtendOperationLog(ctx);
    }

    /**
     * 紧急领用出库-分页
     *
     * @param ctx context
     */
    public void getPage(BizContext ctx) {

        emergencyMatOrderOutApplyComponent.gePage(ctx);
    }

    /**
     * 紧急领用出库-详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {

        // 获取详情
        emergencyMatOrderOutApplyComponent.getInfo(ctx);

        // 开启附件
        emergencyMatOrderOutApplyComponent.setExtendAttachment(ctx);

        // 开启操作日志
        emergencyMatOrderOutApplyComponent.setExtendOperationLog(ctx);

        // 开启审批流
        emergencyMatOrderOutApplyComponent.setExtendWf(ctx);

    }

    /**
     * 保存单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存-校验紧急领用出库入参
        emergencyMatOrderOutApplyComponent.checkSaveData(ctx);

        // 保存-紧急领用出库单
        emergencyMatOrderOutApplyComponent.saveApply(ctx);

        // 保存操作日志
        emergencyMatOrderOutApplyComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        emergencyMatOrderOutApplyComponent.saveBizReceiptAttachment(ctx);

    }

    /**
     * 提交单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交-校验紧急领用出库入参
        emergencyMatOrderOutApplyComponent.checkSaveData(ctx);

        // 提交紧急领用出库
        emergencyMatOrderOutApplyComponent.submitApply(ctx);

        // 保存操作日志
        emergencyMatOrderOutApplyComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        emergencyMatOrderOutApplyComponent.saveBizReceiptAttachment(ctx);

        // 开启审批
        emergencyMatOrderOutApplyComponent.startWorkFlow(ctx);

    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_EMERGENCY_MAT_ORDER_OUT_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        emergencyMatOrderOutApplyComponent.approvalCallback(wfReceiptCo);

    }


    /**
     * 删除单据
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 刪除申请单
        emergencyMatOrderOutApplyComponent.deleteInfo(ctx);

        // 删除申请单附件
        emergencyMatOrderOutApplyComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        emergencyMatOrderOutApplyComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 关闭申请单
     */
    public void close(BizContext ctx) {
        // 关闭申请单
        emergencyMatOrderOutApplyComponent.closeReceipt(ctx);
    }

    /**
     * 配货
     *
     * @param ctx
     */
    public void distribution(BizContext ctx) {
        // 配货
        emergencyMatOrderOutApplyComponent.distribution(ctx);
    }

    /**
     * 非同时模式 获取物料库存
     *
     * @param ctx 上下文
     */
    public void getMatStockNoSameTime(BizContext ctx) {
        // 配置时候需要模糊查询物料描述
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取物料特性库存【非同时模式】
        emergencyMatOrderOutApplyComponent.getMatFeatureStock(ctx);
    }
}
