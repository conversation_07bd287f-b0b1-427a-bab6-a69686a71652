package com.inossem.wms.bizdomain.output.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 出库单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
public interface BizReceiptOutputHeadMapper extends WmsBaseMapper<BizReceiptOutputHead> {

    /**
     * 领料出库单列表分页结果集查询
     *
     * @param page 分页参数
     * @param wrapper 条件
     * @return
     */
    List<BizReceiptOutputPageVO> selectOutputPageVoList(IPage page, @Param(Constants.WRAPPER) Wrapper wrapper);


    /**
     *维修出列表查询
     * @param page 分页参数
     * @return
     */
    List<BizReceiptOutputPageVO> selectRepairOutputPageVoListByPo(IPage page, @Param("po") BizReceiptOutputQueryListPO po);


    /**
     * 工器具维保出库单列表分页结果集查询
     *
     * @param page 分页参数
     * @param wrapper 条件
     * @return
     */
    List<BizReceiptOutputPageVO> getToolOutputPageVoList(IPage page, @Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * (批量)查询行项目在wms中其他出库单据的已创建未同步数
     *
     * @param id
     * @param itemList
     * @return
     */
    List<BizReceiptOutputItemDTO> getWmsCreatedQtyByList(@Param("id") Long id, @Param("list") List<BizReceiptOutputItemDTO> itemList);

    /**
     * 借用出库单列表分页结果集查询
     *
     * @param page 分页参数
     * @param po 出库单分页查询入参
     * @return 出库单列表
     */
    List<BizReceiptOutputPageVO> selectBorrowOutputPageVoList(IPage page, @Param("po") BizReceiptOutputQueryListPO po);

    List<BizReceiptOutputPageVO> selectOutputInfoPageVoList(IPage page, @Param(Constants.WRAPPER) Wrapper wrapper);

    List<BizReceiptOutputPageVO> selectOutputInfoPageVoListUnitized(IPage page, @Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * 根据出库单行项目找到领料申请备注
     * @param id
     * @return
     */
    String selectRemarkByOutPutId(@Param("id") Long id);

    void updateStatusById(@Param("id") long id, @Param("status") int status);

    List<BizReceiptOutputItemDTO> getOutputItemDTOList(@Param("po") BizReceiptOutputSearchPO po);
}
