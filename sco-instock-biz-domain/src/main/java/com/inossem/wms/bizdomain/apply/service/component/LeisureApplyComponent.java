package com.inossem.wms.bizdomain.apply.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.entity.BizApprovalReceiptInstanceRel;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.output.po.LeisureApplyMatImportPO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 闲置物资申请组件类
 *
 * <AUTHOR>
 * @date 2021/3/19 10:41
 */

@Service
@Slf4j
public class LeisureApplyComponent {

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    protected StockCommonService stockCommonService;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;
    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;
    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private ApplyCommonComponent applyCommonComponent;
    @Autowired
    private BizReceiptApplyItemDataWrap bizReceiptApplyItemDataWrap;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private BizBatchInfoDataWrap bizBatchInfoDataWrap;

    /**
     * 提交校验
     *
     * @param ctx 上下文
     */
    public void checkSubmit(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 参数基本校验
        this.check(ctx);
        // 申请数量是否为0校验
        this.checkItemQtyIsZero(ctx);
        // 申请数是否小于可申请数
        this.checkOperatedQty(headDTO);
        // 校验单据状态
        this.checkReceiptStatus(ctx);
    }

    /**
     * 校验行项目的参数
     *
     * @param ctx
     */
    public void checkSave(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(headDTO) || UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }


    /**
     * 校验单据状态
     *
     * @param ctx
     */
    public void checkReceiptStatus(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        boolean canSubmit = false;
        if (Objects.isNull(headDTO.getId())) {
            canSubmit = true;
        } else {
            BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(headDTO.getId());
            if (head == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
            }
            if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())
                    || EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(head.getReceiptStatus())) {
                canSubmit = true;
            }
        }
        if (!canSubmit) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_SUBMIT);
        }
    }

    /**
     * 校验行项目qty是否为0
     *
     * @param ctx 上下文
     */
    public void checkItemQtyIsZero(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<String> errorRidList = new ArrayList<>();
        headDTO.getItemList().forEach(itemDTO -> {
            if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                errorRidList.add(itemDTO.getRid());
            }
        });
        if (UtilCollection.isNotEmpty(errorRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_OUTPUT_QTY_ZERO, errorRidList.toString());
        }
    }

    /**
     * 参数基本校验
     *
     * @param ctx 上下文
     */
    public void check(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (Objects.isNull(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 提交前校验物料、库存地点是否冻结
        materialService.checkFreezeMaterial(headDTO.getItemList().stream().map(BizReceiptApplyItemDTO::getMatId)
                .distinct().collect(Collectors.toList()));
        this.checkFreezeOutputLocation(headDTO.getItemList());
    }


    /**
     * 申请时，判断库存地点是否申请冻结
     */
    public void checkFreezeOutputLocation(List<BizReceiptApplyItemDTO> itemDTOList) {
        Set<String> freezeLocationSet = new HashSet<>();
        Set<String> ridSet = new HashSet<>();
        int rid = 1;
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            DicStockLocationDTO locationDto = dictionaryService.getLocationCacheById(itemDTO.getLocationId());
            if (null == locationDto) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_SUCH_STOCK_LOCATION);
            }
            if (locationDto.getFreezeOutput() > 0) {
                String freezeLocation = locationDto.getFtyCode() + "-" + locationDto.getLocationCode();
                freezeLocationSet.add(freezeLocation);
                ridSet.add(String.valueOf(rid++));
            }
        }
        if (!freezeLocationSet.isEmpty()) {
            // 冻结库存地点
            throw new WmsException(EnumReturnMsg.RETURN_CODE_LOCATION_FREEZING_OUTPUT_ITEM, ridSet.toString(), freezeLocationSet.toString());
        }
    }


    /**
     * 申请数是否小于可申请数
     *
     * @param headDTO headDTO
     */
    public void checkOperatedQty(BizReceiptApplyHeadDTO headDTO) {
        // 申请数量大于可申请数量时抛出异常
        for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
            BigDecimal qty = itemDTO.getStockQty();
            if (qty.compareTo(itemDTO.getQty()) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QUANTITY_DIFFERENCES);
            }
        }
    }

    /**
     * 保存单据【非同时模式】
     *
     * @param ctx 上下文
     */
    public void saveReceipt(BizContext ctx) {
        boolean isNew = true;
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        // head处理
        if (UtilNumber.isNotEmpty(id)) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(id);
            // 根据id更新
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
            // item物理删除
            QueryWrapper<BizReceiptApplyItem> queryWrapperItem = new QueryWrapper<>();
            queryWrapperItem.lambda().eq(BizReceiptApplyItem::getHeadId, id);
            bizReceiptApplyItemDataWrap.physicalDelete(queryWrapperItem);
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(createUserId);
            headDTO.setModifyUserId(createUserId);
            headDTO.setReceiptType(headDTO.getReceiptType());
            headDTO.setReceiptStatus(status);
            bizReceiptApplyHeadDataWrap.saveDto(headDTO);
            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }
        // item处理
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        int rid = 1;
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(status);
            itemDto.setCreateTime(createTime);
            itemDto.setCreateUserId(createUserId);
            itemDto.setModifyUserId(user.getId());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 状态变更-已完成
     */
    public void updateStatusSubmitted(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 状态变更-已完成
     */
    public void updateStatusRejected(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
    }

    /**
     * 删除附件
     *
     * @param ctx 上下文
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        receiptAttachmentService.deleteBizReceiptAttachment(headDTO.getId(), headDTO.getReceiptType());
    }

    /**
     * 更新单据,行项目状态 如不更新单据状态，headDTO参数传null
     *
     * @param headDTO     headDTO
     * @param itemDTOList 行项目列表
     * @param status      状态
     */
    public void updateStatus(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList,
                             Integer status) {
        if (Objects.isNull(headDTO)) {
            this.updateItemStatus(itemDTOList, status);
        } else if (CollectionUtils.isEmpty(itemDTOList)) {
            this.updateReceiptStatus(headDTO, status);
        } else if (!CollectionUtils.isEmpty(itemDTOList)) {
            this.updateItemStatus(itemDTOList, status);
            this.updateReceiptStatus(headDTO, status);
        }
    }

    /**
     * 更新行项目状态
     *
     * @param itemDTOList 行项目列表
     * @param status      状态
     */
    public void updateItemStatus(List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach(item -> item.setItemStatus(status));
            bizReceiptApplyItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }

    /**
     * 删除单据【非同时模式】
     *
     * @param ctx 上下文
     */
    public void deleteReceipt(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(id);
        BizReceiptApplyHeadDTO headDTO = UtilBean.deepCopyNewInstance(head, BizReceiptApplyHeadDTO.class);
        CurrentUser user = ctx.getCurrentUser();
        // 删除head
        bizReceiptApplyHeadDataWrap.removeById(id);
        // 删除item
        bizReceiptApplyItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptApplyItem>() {

            {
                eq(BizReceiptApplyItem::getHeadId, id);
            }
        });
        receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "", user.getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 更新单据状态
     *
     * @param headDTO       headDTO
     * @param receiptStatus 单据状态
     */
    public void updateReceiptStatus(BizReceiptApplyHeadDTO headDTO, Integer receiptStatus) {
        if (Objects.nonNull(headDTO)) {
            // 单据状态
            headDTO.setReceiptStatus(receiptStatus);
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 提交单据【非同时模式】
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitReceipt(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        applyCommonComponent.saveApply(ctx);
    }


    /**
     * 校验删除
     *
     * @param ctx 上下文
     */
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    /**
     * 获取详情
     *
     * @param ctx 上下文
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptApplyHeadDTO headDTO = this.getItemListById(headId);
        // 设置按钮
        ButtonVO button = this.setButton(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), button));
    }


    /**
     * 获取按钮权限
     *
     * @param headId 单据id
     * @return 按钮设置
     */
    public ButtonVO setButton(Long headId) {
        ButtonVO button = new ButtonVO();
        QueryWrapper<BizReceiptApplyHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptApplyHead::getId, headId).eq(BizReceiptApplyHead::getIsDelete, 0);
        BizReceiptApplyHead one = bizReceiptApplyHeadDataWrap.getOne(queryWrapper);
        button.setButtonSave(this.setButtonSave(headId, one));
        button.setButtonDelete(this.setButtonDelete(headId, one));
        button.setButtonSubmit(this.setButtonSubmit(headId, one));
        button.setButtonPost(this.setButtonPost(headId, one));
        button.setButtonDebtOffset(this.setButtonDebtOffset(headId, one));
        return button;
    }

    private BizReceiptApplyHeadDTO getItemListById(Long headId) {
        BizReceiptApplyHead BizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(BizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        // 为避免前端传参导致批次id错误,这里通过specValue解析
        headDTO.getItemList().forEach(item -> {
            item.getAssembleDTOList().forEach(assembleDTO -> {
                Long batchId = applyCommonComponent.getBatchId(assembleDTO);
                assembleDTO.setBatchId(batchId);
            });
        });
        return headDTO;
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptTransportItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(EnumReceiptType.LEISURE_APPLY.getValue());
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }


    /**
     * 逻辑删除单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        receiptRelationService.deleteReceiptTree(EnumReceiptType.LEISURE_APPLY.getValue(), headId);
    }


    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<
                BizReceiptApplyHeadDTO> resultVO =
                new BizResultVO<>(
                        new BizReceiptApplyHeadDTO().setReceiptType(EnumReceiptType.LEISURE_APPLY.getValue())
                                .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()),
                        new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(false).setWfRequired(true), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    /**
     * 开启附件
     *
     * @param ctx 上下文
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启日志
     *
     * @param ctx 上下文
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 获取申请单列表(分页)
     *
     * @param ctx 上下文
     */
    public void getPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptApplyPageVO> page = po.getPageObj(BizReceiptApplyPageVO.class);
        bizReceiptApplyHeadDataWrap.getOutputPageVoList(page, po);
        long totalCount = page.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), totalCount));
    }


    /**
     * 申请单能否保存
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonSave(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 申请单能否删除
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonDelete(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 驳回状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 单据已提交状态，且没有开始作业的，允许删除
            return true;
        }
        return false;
    }

    /**
     * 申请单能否提交 0否、1是
     *
     * @param headId 单据id
     * @return 申请单能否提交 0否、1是
     */
    public Boolean setButtonSubmit(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }


    /**
     * 申请单能否未同步再次过账 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonPost(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步状态
            return true;
        }
        // 已作业状态
        return EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus);
    }


    /**
     * 申请单能否核销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonDebtOffset(Long headId, BizReceiptApplyHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus)) {
            // 待核销状态
            return true;
        }
        return false;
    }

    /**
     * 设置批次图片信息
     *
     * @param ctx 上下文
     */
    public void setBatchImg(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        this.setBatchImg(resultVO.getHead().getItemList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    /**
     * 设置批次图片信息
     *
     * @param itemDTOList 行项目
     */
    public void setBatchImg(List<BizReceiptApplyItemDTO> itemDTOList) {
//        Set<Long> matIdSet = itemDTOList.stream().map(BizReceiptApplyItemDTO::getMatId).collect(Collectors.toSet());
//        if (UtilCollection.isNotEmpty(matIdSet)) {
//            Map<Long, List<BizBatchImgDTO>> batchImgMap = batchImgService.getBatchImgListByMatIdList(matIdSet, 4);
//            for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
//                List<BizBatchImgDTO> bizBatchImgDTOS = batchImgMap.get(itemDTO.getMatId());
//                itemDTO.setBatchImgList(bizBatchImgDTOS);
//            }
//        }
    }

    /**
     * 保存日志
     *
     * @param ctx 上下文
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }


    /**
     * 保存单据附件
     *
     * @param ctx 上下文
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }


    public void getApplyInfo(BizContext ctx) {
        BizApprovalReceiptInstanceRel instance = ctx.getContextData(Const.BIZ_CONTEXT_KEY_WF_PO);

        if (UtilNumber.isEmpty(instance.getReceiptHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptApplyHeadDTO headDTO = this.getItemListById(instance.getReceiptHeadId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

//    @Async
//    public void createLeisureTransport(BizContext ctx) {
//        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
//        BizReceiptTransportHeadDTO transportHead = new BizReceiptTransportHeadDTO();
//        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
//        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
//        Date createTime = new Date();
//        Long createUserId = applyHeadDTO.getCreateUserId();
//        // 新增
//        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TRANSPORT.getValue());
//        transportHead.setId(null);
//        transportHead.setReceiptCode(code);
//        transportHead.setCreateUserId(createUserId);
//        transportHead.setModifyUserId(createUserId);
//        transportHead.setReceiptType(EnumReceiptType.LEISURE_CHANGE.getValue());
//        transportHead.setReceiptStatus(status);
//        transportHead.setCreateTime(UtilDate.getNow());
//        transportHead.setModifyTime(UtilDate.getNow());
//        transportHead.setMoveTypeId(3110l);
//        transportHead.setRemark(applyHeadDTO.getRemark());
//        transportHead.setPreReceiptType(applyHeadDTO.getReceiptType());
//        transportHead.setPreReceiptHeadId(applyHeadDTO.getId());
//        bizReceiptTransportHeadDataWrap.saveDto(transportHead);
//        // 单据日志 - 新增
//        if (Objects.isNull(operationLogType)) {
//            // 设置上下文单据日志 - 创建
//            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
//                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
//        }
//        // item处理
//        List<BizReceiptApplyItemDTO> applyItemDTOList = applyHeadDTO.getItemList();
//        List<BizReceiptTransportItemDTO> itemDTOList = new ArrayList<>();
//        int rid = 1;
//        for (BizReceiptApplyItemDTO applyItemDto : applyItemDTOList) {
//            BizReceiptTransportItemDTO itemDto = UtilBean.deepCopyNewInstance(applyItemDto, BizReceiptTransportItemDTO.class);
//            itemDto.setRid(Integer.toString(rid++));
//            itemDto.setId(null);
//            itemDto.setHeadId(transportHead.getId());
//            itemDto.setItemStatus(status);
//            itemDto.setCreateTime(createTime);
//            itemDto.setCreateUserId(createUserId);
//            itemDto.setInputFtyId(applyItemDto.getFtyId());
//            itemDto.setInputLocationId(applyItemDto.getLocationId());
//            itemDto.setInputWhId(applyItemDto.getWhId());
//            itemDto.setInputMatId(applyItemDto.getMatId());
//            itemDto.setInputUnitId(applyItemDto.getUnitId());
//            itemDto.setOutputFtyId(applyItemDto.getFtyId());
//            itemDto.setOutputLocationId(applyItemDto.getLocationId());
//            itemDto.setOutputWhId(applyItemDto.getWhId());
//            itemDto.setOutputMatId(applyItemDto.getMatId());
//            itemDto.setOutputUnitId(applyItemDto.getUnitId());
//            itemDto.setModifyUserId(createUserId);
//            itemDto.setStockQty(applyItemDto.getQty());
//            itemDto.setPreReceiptItemId(applyItemDto.getId());
//            itemDto.setStockQty(applyItemDto.getStockQty());
//            itemDto.setQty(applyItemDto.getQty());
//            itemDTOList.add(itemDto);
//        }
//        if (!CollectionUtils.isEmpty(itemDTOList)) {
//            bizReceiptTransportItemDataWrap.saveBatchDto(itemDTOList);
//        }
//        transportHead.setItemDTOList(itemDTOList);
//        List<BizReceiptTransportBinDTO> binDTOList = new ArrayList<>();
//        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
//            int bid = 1;
//            for (BizReceiptAssembleDTO assemble : itemDto.getAssembleDTOList()) {
//                BizReceiptTransportBinDTO binDTO = UtilBean.deepCopyNewInstance(itemDto, BizReceiptTransportBinDTO.class);
//                binDTO.setBid(Integer.toString(bid++));
//                binDTO.setId(null);
//                Long batchId = applyCommonComponent.getBatchId(assemble);
//                binDTO.setHeadId(transportHead.getId());
//                binDTO.setItemId(itemDto.getId());
//                binDTO.setInputMatId(itemDto.getInputMatId());
//                binDTO.setOutputMatId(itemDto.getOutputMatId());
//                binDTO.setOutputFtyId(itemDto.getOutputFtyId());
//                binDTO.setInputFtyId(itemDto.getInputFtyId());
//                binDTO.setInputBatchId(batchId);
//                binDTO.setOutputBatchId(batchId);
//                binDTO.setCreateTime(createTime);
//                binDTO.setCreateUserId(createUserId);
//                binDTO.setModifyUserId(createUserId);
//                binDTO.setQty(assemble.getStockQty());
//                binDTOList.add(binDTO);
//            }
//        }
//
//        if (!CollectionUtils.isEmpty(itemDTOList)) {
//            bizReceiptTransportBinDataWrap.saveBatchDto(binDTOList);
//        }
//
//        transportHead.setItemDTOList(itemDTOList);
//
//        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
//        BizResultVO<
//                BizReceiptTransportHeadDTO> resultVO =
//                new BizResultVO<>(
//                        transportHead.setReceiptType(EnumReceiptType.LEISURE_CHANGE.getValue())
//                                .setCreateTime(UtilDate.getNow()).setCreateUserId(createUserId),
//                        new ExtendVO(), buttonVO);
//        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
//
//
//        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, transportHead);
//        // 设置单据流
//        this.saveReceiptTree(ctx);
//
//        // 开启附件
//        this.setExtendAttachment(ctx);
//
//        // 开启日志
//        this.setExtendOperationLog(ctx);
//    }

    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
        // 配置matId
        pos.setSpecStock(null);
        pos.setMatId(matId);
        pos.setStockStatus(stockStatus);
        pos.setSpecStockCode(StringUtils.isEmpty(po.getSpecStockCode()) ? null : po.getSpecStockCode());
        pos.setIsLeisure(0);
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(), pos);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
        }
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        matStockDTO.setItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * 根据单据号获取id
     *
     * @param receiptCode
     * @return
     */
    public Long getIdByReceiptCode(String receiptCode) {
        QueryWrapper<BizReceiptTransportHead> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptTransportHead::getReceiptCode, receiptCode);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getOne(wrapper);
        if (Objects.isNull(head)) {
            return Long.valueOf(0);
        } else {
            return head.getId();
        }
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "不符合项处置单"}
     */
    public void startWorkFlow(BizContext ctx) {
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        // 发起流程审批
        this.approverCheck(userDept);
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Integer receiptType = po.getReceiptType();
        Map<String, Object> variables = new HashMap<>();
        Long ftyId=po.getItemList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        // 用户所属部门、对口部门、对口科室
        variables.put("userDept", userDept);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新闲置物资申请单 - 审批中
        applyCommonComponent.updateStatusApprove(ctx);
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            BizContext ctx = new BizContext();
            CurrentUser currentUser = wfReceiptCo.getInitiator();
            ctx.setCurrentUser(currentUser);
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            BizReceiptApplyHeadDTO headDTO = this.getItemListById(wfReceiptCo.getReceiptHeadId());
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
//             生成闲置物资转库
//            createLeisureTransport(ctx);
            // 添加闲置标记
            this.addLeisureMark(ctx);
            applyCommonComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        } else {
            applyCommonComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }


    private void approverCheck(List<MetaDataDeptOfficePO> userDept) {
        if (UtilCollection.isEmpty(userDept)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        List<String> level1UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDept.get(0).getDeptCode(),userDept.get(0).getDeptOfficeCode(), EnumApprovalLevel.LEVEL_2);
        List<String> level2UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDept.get(0).getDeptCode(), null, EnumApprovalLevel.LEVEL_4);
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "1", userDept.get(0).getDeptName(), userDept.get(0).getDeptOfficeName(), EnumApprovalLevel.LEVEL_2.getValue());
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
    }

    /**
     * 添加闲置标记
     */
    public void addLeisureMark(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizBatchInfo> list = new ArrayList<>();
        CurrentUser cUser = ctx.getCurrentUser();
        // 更新状态
        for (BizReceiptApplyItemDTO item : headDTO.getItemList()) {
            for (BizReceiptAssembleDTO assembleDTO : item.getAssembleDTOList()) {
                BizBatchInfo batchInfo = new BizBatchInfo();
                batchInfo.setId(assembleDTO.getBatchId());
                batchInfo.setMatId(item.getMatId());
                batchInfo.setFtyId(item.getFtyId());
                list.add(batchInfo);
            }
        }
        bizBatchInfoDataWrap.updateBatchSpecStockToLeisure(list, Const.IS_LEISURE, cUser.getId());
    }

    /**
     * 导入闲置物资申请
     *
     * @param ctx
     */
    public void importMatStock(BizContext ctx) {

        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);

        try {
            //获取EXCEL数据
            List<LeisureApplyMatImportPO> list = (List<LeisureApplyMatImportPO>) UtilExcel.readExcelData(file.getInputStream(), LeisureApplyMatImportPO.class);
            //判断EXCEL中主键重复的值
            Map<String, List<LeisureApplyMatImportPO>> checkMap = list.stream().collect(Collectors.groupingBy(item -> item.getFtyCode() + "-" + item.getLocationCode() + "-" + item.getMatCode() + "-" + item.getBatchCode() + "-" + item.getWbsCode()));
            for (String key : checkMap.keySet()) {
                List<LeisureApplyMatImportPO> checkList = checkMap.get(key);
                if (checkList.size() > 1) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE, key);
                }
            }

            /**
             *
             * 查询库存信息
             */
            List<BizReceiptOutputSearchPO> bizReceiptOutputSearchPOS = new ArrayList<>();
            list.forEach(p -> {
                BizReceiptOutputSearchPO bizReceiptOutputSearchPO = new BizReceiptOutputSearchPO();
                bizReceiptOutputSearchPO.setFtyCode(p.getFtyCode());
                bizReceiptOutputSearchPO.setLocationCode(p.getLocationCode());
                bizReceiptOutputSearchPO.setMatCode(p.getMatCode());
//                bizReceiptOutputSearchPO.setLeisureBatchId(p.getBatchId());
                bizReceiptOutputSearchPO.setBatchCode(p.getBatchCode());
                bizReceiptOutputSearchPO.setSpecStockCode(p.getWbsCode());
                bizReceiptOutputSearchPO.setSpecStock(p.getSpecStock());
                bizReceiptOutputSearchPOS.add(bizReceiptOutputSearchPO);
            });

            bizReceiptOutputSearchPOS.forEach(searchPO -> {


                List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
                Long matId = null;
                if (UtilString.isNotNullOrEmpty(searchPO.getMatCode())) {
                    matId = dictionaryService.getMatIdByMatCode(searchPO.getMatCode());
                    if (Objects.isNull(matId)) {
                        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                        return;
                    }
                }
                Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
                // 根据特性code查询特性库存
                BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(searchPO, BizReceiptAssembleRuleSearchPO.class);
                // 配置matId
                pos.setSpecStock(searchPO.getSpecStock());
                pos.setMatId(matId);
                pos.setStockStatus(stockStatus);
                pos.setSpecStockCode(StringUtils.isEmpty(searchPO.getSpecStockCode()) ? null : searchPO.getSpecStockCode());
                pos.setIsLeisure(0);
//                pos.setBatchId(searchPO.getLeisureBatchId());
                pos.setBatchCode(searchPO.getBatchCode());
                // 根据特性，批次号等查询库存
                BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdwByBatchCode(searchPO.getReceiptType(), pos);
                if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
                    // 特性库存转行项目
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptOutputItemDTO.class);
                        itemDTO.setReceiptType(searchPO.getReceiptType());
                        itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                        itemDTO.setQty(itemDTO.getStockQty());
                        itemDTO.setAssembleDTOList(assembleRuleDTO.getAssembleDTOList());
                        itemDTOList.add(itemDTO);
                    }
                }
                if (UtilCollection.isEmpty(itemDTOList)) {
                    throw new WmsException("物料:" + searchPO.getMatCode() + ",工厂:" + searchPO.getFtyCode() + ",库存地点:" + searchPO.getLocationCode() + ",批次:" + searchPO.getBatchCode() + "没有库存");
                }

                MatStockDTO matStockDTO1 = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
                matStockDTO1.setItemDTOList(itemDTOList);
                this.concat(ctx, matStockDTO1);
            });


        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    /**
     * 合并
     */
    private void concat(BizContext ctx, MatStockDTO matStockDTO1) {
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isEmpty(matStockDTO)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,  UtilBean.newInstance(matStockDTO1,MatStockDTO.class));
        } else {
            matStockDTO.getItemDTOList().addAll(matStockDTO1.getItemDTOList());
        }
    }
}
