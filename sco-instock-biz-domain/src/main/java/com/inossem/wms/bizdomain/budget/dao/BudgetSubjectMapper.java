package com.inossem.wms.bizdomain.budget.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplyHead;
import com.inossem.wms.common.model.masterdata.budget.entity.DicBudgetSubject;
import com.inossem.wms.common.model.masterdata.budget.po.DicBudgetSubjectSearchPO;
import com.inossem.wms.common.model.masterdata.budget.vo.DicBudgetSubjectPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BudgetSubjectMapper extends WmsBaseMapper<DicBudgetSubject> {

    List<DicBudgetSubjectPageVO> selectPageVOList(IPage<DicBudgetSubjectPageVO> page, @Param("po") DicBudgetSubjectSearchPO po);

    List<BizReceiptPurchaseApplyHead> checkPurchaseApplyReceiptExist(@Param("id") Long id);
}
