package com.inossem.wms.bizdomain.unitized.service.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizdomain.lifetime.service.datawrap.BizReceiptLifetimeHeadDataWrap;
import com.inossem.wms.bizdomain.lifetime.service.datawrap.BizReceiptLifetimeItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.lifetime.dto.BizReceiptLifetimeHeadDTO;
import com.inossem.wms.common.model.bizdomain.lifetime.dto.BizReceiptLifetimeItemDTO;
import com.inossem.wms.common.model.bizdomain.lifetime.entity.BizReceiptLifetimeHead;
import com.inossem.wms.common.model.bizdomain.lifetime.entity.BizReceiptLifetimeItem;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 寿期公共方法 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Service
public class UnitizedLifetimeCommonComponent {

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected BizReceiptLifetimeHeadDataWrap bizReceiptLifetimeHeadDataWrap;

    @Autowired
    protected BizReceiptLifetimeItemDataWrap bizReceiptLifetimeItemDataWrap;

    /**
     * 寿期单保存校验
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期单"}
     */
    public void checkSaveLifetime(BizContext ctx) {
        // 获取上下文
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            List<String> errorRidList = new ArrayList<>(headDTO.getItemList().size());
            headDTO.getItemList().forEach(itemDTO -> {
                if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    errorRidList.add(itemDTO.getRid());
                }
            });
            if (UtilCollection.isNotEmpty(errorRidList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_OPERATION_QTY_ZERO, errorRidList.toString());
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 保存寿期单
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期单"}
     * @out ctx 出参 {"receiptCode" : "寿期单单号"},{@link BizReceiptLifetimeHeadDTO : "已保存的寿期单}
     */
    public void saveLifetime(BizContext ctx) {
        // 入参上下文 - 要保存的寿期单
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        if(EnumReceiptType.UNITIZED_STOCK_LIFETIME_MAINTAIN.getValue().equals(headDTO.getReceiptType())) {
            receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_UN_MAINTAIN.getValue();
        }
        headDTO.setReceiptStatus(receiptStatus);
        if(UtilNumber.isEmpty(headDTO.getId())){
            headDTO.setCreateUserId(user.getId());
        }
        headDTO.setModifyUserId(user.getId());
        headDTO.setCreateTime(null);
        headDTO.setModifyTime(UtilDate.getNow());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新寿期单
            bizReceiptLifetimeHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptLifetimeItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptLifetimeItem::getHeadId, headDTO.getId());
            bizReceiptLifetimeItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            Integer receiptType = headDTO.getReceiptType();
            if (EnumReceiptType.UNITIZED_STOCK_LIFETIME_APPRIASAL.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SWQUENCE_LIFETIME_APPRAISAL.getValue());
            } else if (EnumReceiptType.UNITIZED_STOCK_LIFETIME_MAINTAIN.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LIFETIME_MAINTAIN.getValue());
            }
            headDTO.setReceiptCode(receiptCode);
            bizReceiptLifetimeHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        int rid = 1;
        for (BizReceiptLifetimeItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(Integer.toString(rid++));
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        bizReceiptLifetimeItemDataWrap.saveBatchDto(headDTO.getItemList());
        /* ********************** item处理结束 *************************/
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的寿期单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 更新寿期单已完成
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        // 入参上下文 - 要保存的寿期单
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头已完成
        UpdateWrapper<BizReceiptLifetimeHead> headUpdateWrapper = new UpdateWrapper<BizReceiptLifetimeHead>();
        headUpdateWrapper.lambda().set(BizReceiptLifetimeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()).eq(BizReceiptLifetimeHead::getId, headDTO.getId());
        bizReceiptLifetimeHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目已完成
        UpdateWrapper<BizReceiptLifetimeItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptLifetimeItem>();
        itemUpdateWrapper.lambda().set(BizReceiptLifetimeItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()).eq(BizReceiptLifetimeItem::getHeadId, headDTO.getId());
        bizReceiptLifetimeItemDataWrap.update(itemUpdateWrapper);
    }

    /**
     * 更新寿期单审批中
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期单"}
     */
    public void updateStatusApproving(BizContext ctx) {
        // 入参上下文 - 要保存的寿期单
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头审批中
        UpdateWrapper<BizReceiptLifetimeHead> headUpdateWrapper = new UpdateWrapper<BizReceiptLifetimeHead>();
        headUpdateWrapper.lambda().set(BizReceiptLifetimeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue()).eq(BizReceiptLifetimeHead::getId, headDTO.getId());
        bizReceiptLifetimeHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目审批中
        UpdateWrapper<BizReceiptLifetimeItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptLifetimeItem>();
        itemUpdateWrapper.lambda().set(BizReceiptLifetimeItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue()).eq(BizReceiptLifetimeItem::getHeadId, headDTO.getId());
        bizReceiptLifetimeItemDataWrap.update(itemUpdateWrapper);
    }

    /**
     * 更新寿期单已驳回
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期单"}
     */
    public void updateStatusRejected(BizContext ctx) {
        // 入参上下文 - 要保存的寿期单
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头审批中
        UpdateWrapper<BizReceiptLifetimeHead> headUpdateWrapper = new UpdateWrapper<BizReceiptLifetimeHead>();
        headUpdateWrapper.lambda().set(BizReceiptLifetimeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue()).eq(BizReceiptLifetimeHead::getId, headDTO.getId());
        bizReceiptLifetimeHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目审批中
        UpdateWrapper<BizReceiptLifetimeItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptLifetimeItem>();
        itemUpdateWrapper.lambda().set(BizReceiptLifetimeItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue()).eq(BizReceiptLifetimeItem::getHeadId, headDTO.getId());
        bizReceiptLifetimeItemDataWrap.update(itemUpdateWrapper);
    }

    /**
     * 刪除寿期单
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "寿期单单号"}
     */
    public void deleteInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置寿期单
        BizReceiptLifetimeHeadDTO headDTO = UtilBean.newInstance(bizReceiptLifetimeHeadDataWrap.getById(headId), BizReceiptLifetimeHeadDTO.class);
        // 逻辑删除抬头表
        bizReceiptLifetimeHeadDataWrap.removeById(headId);
        // 逻辑删除行项目表
        QueryWrapper<BizReceiptLifetimeItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptLifetimeItem::getHeadId, headId);
        bizReceiptLifetimeItemDataWrap.remove(itemWrapper);
        // 寿期单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 寿期单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptLifetimeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptLifetimeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptLifetimeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "要保持单据流的寿期单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文 - 要保持单据流的寿期单
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptLifetimeItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "要保持附件的寿期单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的寿期单
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存寿期单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "要保存操作日志的寿期单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的寿期单
        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 删除寿期单单据流
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期单"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptLifetimeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除寿期单单据流
        receiptRelationService.deleteReceiptTree(po.getReceiptType(), po.getId());
    }

    /**
     * 删除寿期单附件
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期单"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptLifetimeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(po.getId(), po.getReceiptType());
    }

}
