package com.inossem.wms.bizdomain.input.controller;

import com.inossem.wms.bizdomain.input.service.biz.ToolsInputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.po.*;
import com.inossem.wms.common.model.bizdomain.input.vo.ToolsMaterialInfoVO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectPdaLabelVo;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialListConfirmSplitPO;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.DicMaterialListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> wang
 * @description 工器具入库
 * @date 2022/3/29 16:34
 */
@RestController
@Api(tags = "入库管理-工器具入库")
public class ToolsInputController {

    @Autowired
    private ToolsInputService toolsInputService;

    /**
     * 工器具入库-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "工器具入库-初始化", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/input/tools-inputs/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        toolsInputService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 工器具入库-分页
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 其他入库单分页
     */
    @ApiOperation(value = "工器具入库-分页", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/input/tools-inputs/results/page", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po,
                                                                   BizContext ctx) {
        toolsInputService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 工器具入库-详情
     *
     * @param id 其他入库主键
     * @param ctx 入参上下文 {"id":"工器具入库主键"}
     */
    @ApiOperation(value = "其他入库-详情", tags = {"入库管理-工器具入库"})
    @GetMapping(value = "/input/tools-inputs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        toolsInputService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 工器具入库单-前续单据
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件"}
     * @return 物料信息
     */
    @ApiOperation(value = "工器具入库单-前续单据", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/input/tools-inputs/mat-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicMaterialListVO>> getMatList(@RequestBody DicMaterialSearchPO po, BizContext ctx) {
        MultiResultVO<DicMaterialListVO> dicMaterialList = toolsInputService.getList(ctx);
        return BaseResult.success(dicMaterialList);
    }

    /**
     * 工器具入库单-确认前续单据并拆分
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件"}
     * @return 物料信息
     */
    @ApiOperation(value = "工器具入库单-确认前续单据并拆分", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/input/tools-inputs/mat-list/confirm", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BizBatchInfoDTO>> confirmMatList(@RequestBody DicMaterialListConfirmSplitPO po, BizContext ctx) {
        return BaseResult.success(toolsInputService.confirmSplitMat(ctx,true));
    }


    /**
     * 工器具入库单-保存
     *
     * @param po 保存工器具入库表单参数
     * @param ctx 入参上下文 {"po":"保存工器具入库表单参数"}
     */
    @ApiOperation(value = "工器具入库-保存", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/input/tools-inputs/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        toolsInputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TOOLS_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 工器具入库单-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "工器具入库单-删除", tags = {"入库管理-工器具入库"})
    @DeleteMapping("/input/tools-inputs/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        toolsInputService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_DELETE_SUCCESS, po.getReceiptCode());
    }

    /**
     * 工器具入库单-提交
     *
     * @param po 保存工器具入库表单参数
     * @param ctx 入参上下文 {"po":"保存工器具入库表单参数"}
     */
    @ApiOperation(value = "工器具入库-提交", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/input/tools-inputs/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        toolsInputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TOOLS_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 工器具入库单数据导入
     * @param file
     * @param ctx
     * @return
     */
    @ApiOperation(value = "工器具入库单数据导入", notes = "工器具入库单主数据导入", tags = {"入库管理-工器具入库"})
    @PostMapping(path = "/input/tools-inputs/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<DicMaterialDTO>> importMaterial(@RequestPart("file") MultipartFile file, BizContext ctx) {

        toolsInputService.importToolsData(ctx);
        return BaseResult.success(ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO),EnumReturnMsg.RETURN_CODE_TOOLS_INPUT_SAVE_SUCCESS);
    }

    /**
     * 工器具入库单-冲销
     * @param po 工器具入库单冲销表单参数
     * @param ctx 入参上下文 {"po":"其他入库冲销表单参数"}
     */
    @ApiOperation(value = "工器具入库单-冲销", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/input/tools-inputs/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInputWriteOffPO po, BizContext ctx) {
        toolsInputService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TOOLS_INPUT_SAVE_SUCCESS, po.getReceiptCode());
    }

    /**
     * 工器具主数据查询
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "工器具主数据查询", notes = "工器具主数据查询", tags = {"入库管理-工器具入库"})
    @PostMapping("/input/tools-inputs/tools-material")
    public BaseResult<PageObjectVO<ToolsMaterialInfoVO>> getToolMaterialInfo(@RequestBody ToolsMaterialInfoSearchPO po,
                                                                             BizContext ctx){
        PageObjectVO<ToolsMaterialInfoVO> toolMaterialInfo = toolsInputService.getToolMaterialInfo(ctx);
        return BaseResult.success(toolMaterialInfo);
    }

    /**
     * 工器具主数据批量修改
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "工器具主数据批量修改", notes = "工器具主数据批量修改", tags = {"入库管理-工器具入库"})
    @PostMapping("/input/tools-inputs/tools-update")
    public BaseResult<String> updateToolMaterialInfo(@RequestBody ToolsMaterialInfoSearchPO po,
                                                                             BizContext ctx){
        toolsInputService.multiUpdateByToolTypeId(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TOOLS_INPUT_SAVE_SUCCESS, code);
    }
    /**
     * 工器具主数据-冻结
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "工器具主数据-冻结", notes = "工器具主数据-冻结", tags = {"入库管理-工器具入库"})
    @PostMapping("/input/tools-inputs/tools-freeze")
    public BaseResult<String> freezeOperation(@RequestBody ToolsMaterialInfoSearchPO po,
                                                                             BizContext ctx){
        toolsInputService.freezeOperation(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TOOLS_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 工器具库存报表 导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-工器具库存报表导出Excel", tags = {"入库管理-工器具入库"})
    @PostMapping(path = "/report/tools-batch/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportToolsBatchDetail(@RequestBody ToolsMaterialInfoSearchPO po, BizContext ctx) {
        toolsInputService.exportToolsBatchDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 工器具提交后信息-打印
     *
     * @param po 验收表单
     * @param ctx 入参上下文 {"po":"验收表单"}
     * @return 验收管理Pda端标签相关数据
     */
    @ApiOperation(value = "工器具提交后信息-打印", tags = {"入库管理-工器具入库"})
    @PostMapping(value = "/tools/input/web-print", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInspectPdaLabelVo> webPrint(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        toolsInputService.webPrint(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_PRINT_SUCCESS);
    }

}
