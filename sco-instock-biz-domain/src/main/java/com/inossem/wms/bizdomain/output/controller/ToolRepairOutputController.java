package com.inossem.wms.bizdomain.output.controller;

import com.inossem.wms.bizdomain.output.service.biz.ToolRepairOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 维修出库
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "维修管理-维修出库")
public class ToolRepairOutputController {

    @Autowired
    private ToolRepairOutputService toolRepairOutputService;

    @ApiOperation(value = "维修出库创建", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/init")
    public BaseResult init(BizContext ctx) {
        toolRepairOutputService.init(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "维修出库查询列表（分页）", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptOutputQueryListPO po,
        BizContext ctx) {
        toolRepairOutputService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "维修出库查询物料库存", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        toolRepairOutputService.getMatStock(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @ApiOperation(value = "维修出库获取配货信息", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        toolRepairOutputService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }

    @ApiOperation(value = "维修出库详情", tags = {"维修管理-维修出库"})
    @GetMapping(value = "/repair/output/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        toolRepairOutputService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "维修出库保存", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/save")
    public BaseResult save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        toolRepairOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "维修出库提交", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/submit")
    public BaseResult submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        toolRepairOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "维修出库过账", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/post")
    public BaseResult post(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        toolRepairOutputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_POST_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "维修出库冲销", tags = {"维修管理-维修出库"})
    @PostMapping(value = "/repair/output/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        toolRepairOutputService.writeOff(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "维修出库删除", tags = {"维修管理-维修出库"})
    @DeleteMapping(value = "/repair/output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        toolRepairOutputService.delete(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_DELETE_SUCCESS, headDTO.getReceiptCode());
    }

}