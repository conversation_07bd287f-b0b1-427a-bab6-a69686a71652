package com.inossem.wms.bizdomain.contract.dao;

import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.PurchaseDTO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractLedgerListExportVO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractReportListVO;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractListVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;

import java.util.List;

/**
 * <p>
 * 合同头 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface BizReceiptContractHeadMapper extends WmsBaseMapper<BizReceiptContractHead> {

    IPage<BizReceiptContractListVO> getContractPageVo(@Param("page") IPage<BizReceiptContractListVO> page,
            @Param("ew") WmsQueryWrapper<BizReceiptContractSearchPO> wrapper);

    IPage<BizReceiptContractReportListVO> getContractReportPageVo(@Param("page") IPage<BizReceiptContractReportListVO> page, @Param("ew") WmsQueryWrapper<BizReceiptContractSearchPO> wrapper);

    IPage<BizReceiptContractLedgerListExportVO> getContractLedgerReportPageVo(@Param("page") IPage<BizReceiptContractLedgerListExportVO> page, @Param("ew") WmsQueryWrapper<BizReceiptContractSearchPO> wrapper);

    List<PurchaseDTO> getPurchaseList(@Param("po") BizReceiptContractHeadDTO headDTO);
}
