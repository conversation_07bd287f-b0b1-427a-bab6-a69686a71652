package com.inossem.wms.bizdomain.facility.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.service.dto.BizReceiptServiceFacilityItemDTO;
import com.inossem.wms.common.model.bizdomain.service.po.BizReceiptServiceSearchPO;
import com.inossem.wms.common.model.masterdata.facility.dto.DicFacilityDTO;
import com.inossem.wms.common.model.masterdata.facility.entity.DicFacility;
import com.inossem.wms.common.model.masterdata.facility.po.DicFacilitySearchPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设施管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Mapper
public interface DicFacilityMapper extends WmsBaseMapper<DicFacility> {


    List<DicFacilityDTO> selectPage(IPage<DicFacilityDTO> page, @Param(Constants.WRAPPER) WmsQueryWrapper<DicFacilitySearchPO> queryWrapper);

    void updateDto(@Param("po") DicFacilityDTO po);

    List<BizReceiptServiceFacilityItemDTO> selectServiceItemDTOListBySupplier(@Param("po") BizReceiptServiceSearchPO po);

    List<BizReceiptContractHeadDTO> selectFacilityContractList(@Param(Constants.WRAPPER) WmsLambdaQueryWrapper<BizReceiptContractSearchPO> queryWrapper);
}
