package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.component.MaterialOutputComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.task.service.biz.UnLoadTaskService;
import com.inossem.wms.bizdomain.task.service.component.TaskComponent;
import com.inossem.wms.bizdomain.task.service.component.UnLoadComponent;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskHead;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqHead;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqMergePo;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskSearchPo;
import com.inossem.wms.common.model.bizdomain.task.vo.*;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 成套设备下架组件
 */

@Component
@Slf4j
public class UnitizedUnLoadComponent {

    @Autowired
    private BizReceiptTaskReqHeadDataWrap bizReceiptTaskReqHeadDataWrap;
    @Autowired
    private BizReceiptTaskReqItemDataWrap bizReceiptTaskReqItemDataWrap;
    @Autowired
    private BizReceiptTaskHeadDataWrap bizReceiptTaskHeadDataWrap;
    @Autowired
    private UnLoadComponent unLoadComponent;
    @Autowired
    private UnLoadTaskService unLoadTaskService;
    @Autowired
    private TaskComponent taskComponent;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    private LabelReceiptRelService labelReceiptRelService;
    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;

    /**
     * 获取作业请求列表页-分页
     *
     * @param ctx 系统上下文
     * @return 分页类型的集合
     */
    public void getTaskReqPageList(BizContext ctx) {
        log.info("作业请求列表查询 ctx：{}", JSONObject.toJSONString(ctx, SerializerFeature.IgnoreNonFieldGetter));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptTaskSearchPo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList = null;
        if (user != null && user.getLocationList() != null) {
            List<DicStockLocationDTO> locationList = user.getLocationList();
            locationIdList = locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);

        //成套设备下架参数
        if (UtilNumber.isEmpty(po.getReReceiptType())) {
            po.setReReceiptType(null);
        }
        try {
            List<Long> outPutReceiptId = this.convertApplyReceiptParam(po.getReqMatApplyReceiptCode(), po.getReqMatApplyCreateUserName());
            List<Long> outPutReceiptId2 = this.convertOutPutReceiptId(po.getReqMatOutReceiptCode());
            if (UtilCollection.isNotEmpty(outPutReceiptId)) {
                po.setReqMatOutReceiptIdList(outPutReceiptId);
            }
            if (UtilCollection.isNotEmpty(outPutReceiptId2)) {
                po.setReqMatOutReceiptIdList(outPutReceiptId2);
            }
            if (UtilCollection.isNotEmpty(outPutReceiptId) && UtilCollection.isNotEmpty(outPutReceiptId2)) {
                List<Long> outputReceiptList = outPutReceiptId.stream().filter(e -> outPutReceiptId2.contains(e)).distinct().collect(Collectors.toList());
                po.setReqMatOutReceiptIdList(outputReceiptList);
            }
        } catch (Exception e) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(null, 0L));
        }

        // 分页处理
        IPage<BizReceiptTaskVO> page = new Page<>(po.getPageIndex(), po.getPageSize());
        List<BizReceiptTaskVO> taskVoList = bizReceiptTaskReqHeadDataWrap.selectUnitizedStockTaskReqList(po, page);
        log.info("获取作业请求列表 taskVoList：{}", JSONObject.toJSONString(taskVoList));
        // 数据填充
        dataFillService.fillAttr(taskVoList);
        for (BizReceiptTaskVO taskVO : taskVoList) {
            if (!taskVO.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())) {
                // 不是领用出库, 没有是否准将预留
                taskVO.setIsExact(-1);
            }
        }
        log.info("获取作业请求列表-填充 taskVoList：{}", JSONObject.toJSONString(taskVoList));
        // 回填数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(taskVoList, page.getTotal()));
    }

    /**
     * 合并成套下架作业单校验
     */
    public void mergeCheck(BizContext ctx) {
        BizReceiptTaskReqMergePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(po.getIdList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilCollection.isEmpty(po.getIdList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        QueryWrapper<BizReceiptTaskReqItem> reqItemQueryWrapper = new QueryWrapper<>();
        reqItemQueryWrapper.lambda().in(BizReceiptTaskReqItem::getHeadId, po.getIdList());
        List<BizReceiptTaskReqItem> taskReqItemList = bizReceiptTaskReqItemDataWrap.list(reqItemQueryWrapper);
        //合并下架单据的前值单据必须是 成套设备领料出库单
        boolean notMerge = taskReqItemList.stream().anyMatch(e -> !EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue().equals(e.getPreReceiptType()));
        if (notMerge) {
            //合并下架单据的前值单据 不是 成套设备领料出库单
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
        }
        List<BizReceiptTaskReqItemDTO> taskReqItemDTOList = UtilCollection.toList(taskReqItemList, BizReceiptTaskReqItemDTO.class);
        dataFillService.fillAttr(taskReqItemDTOList);
        String usedDeptName = taskReqItemDTOList.get(0).getUsedDeptName();
        for (BizReceiptTaskReqItemDTO reqItemDTO : taskReqItemDTOList) {
            if (!usedDeptName.equals(reqItemDTO.getUsedDeptName())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
            }
        }
        QueryWrapper<BizReceiptTaskHead> taskQueryWrapper = new QueryWrapper<>();
        taskQueryWrapper.lambda().in(BizReceiptTaskHead::getTaskReqHeadId, po.getIdList());
        List<BizReceiptTaskHead> taskHeadList = bizReceiptTaskHeadDataWrap.list(taskQueryWrapper);
        if (UtilCollection.isNotEmpty(taskHeadList)) {
            List<Long> uploadReqIdList = taskHeadList.stream().map(BizReceiptTaskHead::getTaskReqHeadId).collect(Collectors.toList());
            List<BizReceiptTaskReqHead> reqHeadList = bizReceiptTaskReqHeadDataWrap.listByIds(uploadReqIdList);
            List<String> uploadReqList = reqHeadList.stream().map(BizReceiptTaskReqHead::getReceiptCode).collect(Collectors.toList());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
        //相同预留类型、相同仓库号前端校验
    }

    /**
     * 查询领料出库单id
     */
    public List<Long> convertOutPutReceiptId(String reqMatOutReceiptCode) {
        if (UtilString.isNullOrEmpty(reqMatOutReceiptCode)) {
            return null;
        }
        String[] split = reqMatOutReceiptCode.split(";");
        List<String> receiptCodeList = Arrays.stream(split).collect(Collectors.toList());
        QueryWrapper<BizReceiptOutputHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BizReceiptOutputHead::getReceiptCode, receiptCodeList);
        List<BizReceiptOutputHead> list = bizReceiptOutputHeadDataWrap.list(queryWrapper);
        if (UtilCollection.isEmpty(list)) {
            throw new WmsException("返回空列表");
        }
        return list.stream().map(BizReceiptOutputHead::getId).collect(Collectors.toList());
    }

    /**
     * 查询领料出库单id
     */
    public List<Long> convertApplyReceiptParam(String reqMatApplyReceiptCode, String reqMatApplyCreateUserName) {
        if (UtilString.isNullOrEmpty(reqMatApplyReceiptCode) && UtilString.isNullOrEmpty(reqMatApplyCreateUserName)) {
            return null;
        }
        QueryWrapper<BizReceiptApplyHead> queryWrapper = new QueryWrapper<>();
        if (UtilString.isNotNullOrEmpty(reqMatApplyReceiptCode)) {
            String[] split = reqMatApplyReceiptCode.split(";");
            List<String> receiptCodeList = Arrays.stream(split).collect(Collectors.toList());
            queryWrapper.lambda().in(BizReceiptApplyHead::getReceiptCode, receiptCodeList);
        }
        if (UtilString.isNotNullOrEmpty(reqMatApplyCreateUserName)) {
            QueryWrapper<SysUser> userQueryWrapper = new QueryWrapper<>();
            userQueryWrapper.lambda().eq(SysUser::getUserName, reqMatApplyCreateUserName);
            List<SysUser> list = sysUserDataWrap.list(userQueryWrapper);
            if (UtilCollection.isEmpty(list)) {
                throw new WmsException("返回空列表");
            }
            queryWrapper.lambda().eq(BizReceiptApplyHead::getCreateUserId, list.get(0).getId());
        }
        List<BizReceiptApplyHead> list = bizReceiptApplyHeadDataWrap.list(queryWrapper);
        if (UtilCollection.isEmpty(list)) {
            throw new WmsException("返回空列表");
        }
        List<Long> applyIdList = list.stream().map(BizReceiptApplyHead::getId).collect(Collectors.toList());
        QueryWrapper<BizReceiptOutputItem> outputHeadQueryWrapper = new QueryWrapper<>();
        outputHeadQueryWrapper.lambda().in(BizReceiptOutputItem::getPreReceiptHeadId, applyIdList);
        List<BizReceiptOutputItem> outputItemList = bizReceiptOutputItemDataWrap.list(outputHeadQueryWrapper);
        if (UtilCollection.isEmpty(outputItemList)) {
            throw new WmsException("返回空列表");
        }
        return outputItemList.stream().map(BizReceiptOutputItem::getHeadId).distinct().collect(Collectors.toList());
    }

    /**
     * 合并成套设备下架单据
     */
    public void merge(BizContext ctx) {
        BizReceiptTaskReqMergePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
//        List<BizReceiptTaskReqHead> reqHeadList = bizReceiptTaskReqHeadDataWrap.listByIds(po.getIdList());
        QueryWrapper<BizReceiptTaskReqItem> reqItemQueryWrapper = new QueryWrapper<>();
        reqItemQueryWrapper.lambda().in(BizReceiptTaskReqItem::getHeadId, po.getIdList());
        List<BizReceiptTaskReqItem> taskReqItemList = bizReceiptTaskReqItemDataWrap.list(reqItemQueryWrapper);
        BizReceiptTaskReqHeadDTO headDTO = new BizReceiptTaskReqHeadDTO();
        List<BizReceiptTaskReqItemDTO> reqItemDTOList = UtilCollection.toList(taskReqItemList, BizReceiptTaskReqItemDTO.class);
        for (BizReceiptTaskReqItemDTO bizReceiptTaskReqItemDTO : reqItemDTOList) {
            bizReceiptTaskReqItemDTO.setMergeReqHeadId(bizReceiptTaskReqItemDTO.getHeadId());
            bizReceiptTaskReqItemDTO.setMergeReqItemId(bizReceiptTaskReqItemDTO.getId());
            bizReceiptTaskReqItemDTO.setId(null);
            bizReceiptTaskReqItemDTO.setCreateUserId(currentUser.getId());
            bizReceiptTaskReqItemDTO.setModifyUserId(currentUser.getId());
            bizReceiptTaskReqItemDTO.setCreateTime(new Date());
            bizReceiptTaskReqItemDTO.setModifyTime(new Date());
            bizReceiptTaskReqItemDTO.setModifyUserCode(currentUser.getUserCode());
            bizReceiptTaskReqItemDTO.setCreateUserCode(currentUser.getUserCode());
        }
        headDTO.setItemList(reqItemDTOList);
        headDTO.setMerge(EnumRealYn.TRUE.getIntValue());
        headDTO.setId(null);
        headDTO.setCreateUserId(currentUser.getId());
        headDTO.setModifyUserId(currentUser.getId());
        headDTO.setCreateTime(new Date());
        headDTO.setModifyTime(new Date());
        headDTO.setModifyUserCode(currentUser.getUserCode());
        headDTO.setCreateUserCode(currentUser.getUserCode());
        //生成下架请求
        unLoadComponent.addUnLoadReqBySameWhId(headDTO, reqItemDTOList);
        //删除原单据
        bizReceiptTaskReqHeadDataWrap.removeByIds(po.getIdList());
        QueryWrapper<BizReceiptTaskReqItem> deleteReqItemQueryWrapper = new QueryWrapper<>();
        deleteReqItemQueryWrapper.lambda().in(BizReceiptTaskReqItem::getHeadId, po.getIdList());
        bizReceiptTaskReqItemDataWrap.remove(deleteReqItemQueryWrapper);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

    /**
     * 查询下架作业单详情
     */
    public void getTaskReq(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ***************************请求抬头信息 **********************************/
        // 请求头信息
        BizReceiptTaskReqHead taskReqHead = bizReceiptTaskReqHeadDataWrap.getById(id);
        unLoadTaskService.getTaskReq(ctx);
        if (UtilNumber.isEmpty(taskReqHead.getMerge())) {
            // 非合并下架请求，保持原逻辑
            return;
        }
        // 根据请求id获取作业请求详情
        taskComponent.getTaskReq(ctx);
        // 设置行项目批次图片信息
        taskComponent.setBatchImg(ctx);
        // 设置批次特性
        taskComponent.setSpecFeature(ctx);
        // 开启操作日志
        taskComponent.setExtendOperationLog(ctx);
        //按照物料分组汇总行项目
        this.groupReqItem(ctx);
        //自定义返回结构
        taskComponent.customizeResult(ctx);
        this.addChildReqItem(ctx);
    }

    /**
     * 按照物料分组汇总，得到父子结构的行项目
     */
    public void groupReqItem(BizContext ctx) {
        BizResultVO<BizReceiptTaskReqHeadDTO> contextData = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskReqHeadDTO dataHead = contextData.getHead();
        List<BizReceiptTaskReqItemDTO> reqItemList = dataHead.getItemList();
        List<BizReceiptTaskReqItemDTO> unCompletedReqItemList = reqItemList.stream().filter(itemVo -> !EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemVo.getItemStatus())).collect(Collectors.toList());
        //按物料合并行
        List<BizReceiptTaskReqItemDTO> itemVOList = new ArrayList<>();
        Map<Long, List<BizReceiptTaskReqItemDTO>> itemGroup = unCompletedReqItemList.stream().collect(Collectors.groupingBy(BizReceiptTaskReqItemDTO::getMatId));
        for (Map.Entry<Long, List<BizReceiptTaskReqItemDTO>> entry : itemGroup.entrySet()) {
            List<BizReceiptTaskReqItemDTO> valueList = entry.getValue().stream().filter(itemVo -> !EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemVo.getItemStatus())).collect(Collectors.toList());
            BizReceiptTaskReqItemDTO old = valueList.get(0);
            BizReceiptTaskReqItemDTO bizReceiptTaskReqItemVO = UtilBean.newInstance(old, BizReceiptTaskReqItemDTO.class);
            bizReceiptTaskReqItemVO.setId(null);
            //汇总数量
            BigDecimal qty = valueList.stream().map(BizReceiptTaskReqItemDTO::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal taskQty = valueList.stream().map(BizReceiptTaskReqItemDTO::getTaskQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            bizReceiptTaskReqItemVO.setQty(qty);
            bizReceiptTaskReqItemVO.setTaskQty(taskQty);
            bizReceiptTaskReqItemVO.setChildItemList(valueList);
            bizReceiptTaskReqItemVO.setIsOver(EnumRealYn.FALSE.getIntValue());
            boolean over = valueList.stream().anyMatch(e -> e.getIsOver().equals(EnumRealYn.TRUE.getIntValue()));
            if (over) {
                bizReceiptTaskReqItemVO.setIsOver(EnumRealYn.TRUE.getIntValue());
            }
            itemVOList.add(bizReceiptTaskReqItemVO);
        }
        dataHead.setItemList(itemVOList);
        contextData.setHead(dataHead);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, contextData);
        //未下架的行项目，用做自定义结构后，匹配子行项目数据
        ctx.setContextData("unCompletedReqItemList", unCompletedReqItemList);
        ctx.setContextData("reqItemList", reqItemList);
    }

    /**
     * 匹配子行项目
     */
    public void addChildReqItem(BizContext ctx) {
        //行项目
        List<BizReceiptTaskReqItemDTO> unCompletedReqItemList = ctx.getContextData("unCompletedReqItemList");
        Map<Long, List<BizReceiptTaskReqItemDTO>> unCompletedReqItemGroup = unCompletedReqItemList.stream().collect(Collectors.groupingBy(BizReceiptTaskReqItemDTO::getMatId));
        BizResultVO<BizReceiptTaskVO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskVO head = resultVO.getHead();
        for (BizReceiptTaskReqItemVO bizReceiptTaskReqItemVO : head.getReqItemList()) {
            List<BizReceiptTaskReqItemDTO> reqItemDTOList = unCompletedReqItemGroup.get(bizReceiptTaskReqItemVO.getMatId());
            List<BizReceiptTaskReqItemVO> childItemList = UtilCollection.toList(reqItemDTOList, BizReceiptTaskReqItemVO.class);
            for (BizReceiptTaskReqItemVO reqItemVO : childItemList) {
                // 物料编码
                reqItemVO.setMatCode(bizReceiptTaskReqItemVO.getMatCode());
                // 物料描述
                reqItemVO.setMatName(bizReceiptTaskReqItemVO.getMatName());
                reqItemVO.setNetWeight(bizReceiptTaskReqItemVO.getNetWeight());
                reqItemVO.setWeightTolerance(bizReceiptTaskReqItemVO.getWeightTolerance());
                reqItemVO.setUnTaskQty(reqItemVO.getQty().subtract(reqItemVO.getTaskQty()));
            }
            bizReceiptTaskReqItemVO.setChildItemList(childItemList);
        }
        //由于在customizeResult是对汇总数据操作，汇总后的行项目id为空，在customizeResult中查询作业单是匹配数据失败，在此重新匹配
        List<BizReceiptTaskReqItemDTO> reqItemList = ctx.getContextData("reqItemList");
        List<BizReceiptTaskItemDTO> taskItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        Map<Long, BizReceiptTaskReqItemDTO> taskReqItemDTOMap = reqItemList.stream().collect(Collectors.toMap(BizReceiptTaskReqItemDTO::getId, Function.identity()));
        List<BizReceiptTaskItemVO> taskItemVoList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(taskItemDTOList)) {
            // 作业head id
            List<Long> taskHeadIdList =
                    taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getHeadId).collect(Collectors.toList());
            // 作业单head集合
            List<BizReceiptTaskHeadVO> bizReceiptTaskHeadVOList =
                    UtilCollection.toList(bizReceiptTaskHeadDataWrap.listByIds(taskHeadIdList), BizReceiptTaskHeadVO.class);
            // 数据填充
            dataFillService.fillRlatAttrDataList(bizReceiptTaskHeadVOList);
            Map<Long, BizReceiptTaskHeadVO> receiptTaskHeadVOMap = bizReceiptTaskHeadVOList.stream().collect(Collectors
                    .groupingBy(BizReceiptTaskHeadVO::getId, Collectors.collectingAndThen(Collectors.toList(), value -> {
                        value.get(0).setBizReceiptTaskItemVOList(new ArrayList<>());
                        return value.get(0);
                    })));
            // 作业单标签集合
            List<BizLabelReceiptRelDTO> bizTaskLabelReceiptRelDTOList =
                    labelReceiptRelService.getDTOList(taskHeadIdList, null, null, null);
            Map<Long, List<BizLabelReceiptRelDTO>> relTaskItemMap = bizTaskLabelReceiptRelDTOList.stream()
                    .collect(Collectors.groupingBy(BizLabelReceiptRelDTO::getReceiptItemId));
            // 请求行项目信息
            BizReceiptTaskReqItemDTO reqItemDTO;
            for (BizReceiptTaskItemDTO itemDTO : taskItemDTOList) {
                BizReceiptTaskItemVO taskItem = UtilBean.newInstance(itemDTO, BizReceiptTaskItemVO.class);
                reqItemDTO = taskReqItemDTOMap.get(itemDTO.getTaskReqItemId());
                if (null == reqItemDTO) {
                    continue;
                }
                // 根据请求行项目属性赋值作业单行项目
                taskItem.setMatCode(reqItemDTO.getMatInfo().getMatCode());
                taskItem.setMatName(reqItemDTO.getMatInfo().getMatName());
                taskItem.setUnitCode(reqItemDTO.getUnitCode());
                taskItem.setUnitName(reqItemDTO.getUnitName());
                taskItem.setDecimalPlace(reqItemDTO.getDecimalPlace());
                taskItem.setLabelReceiptRelDTOList(relTaskItemMap.get(itemDTO.getId()));
                receiptTaskHeadVOMap.get(itemDTO.getHeadId()).getBizReceiptTaskItemVOList().add(taskItem);
                taskItemVoList.add(taskItem);
            }
            head.setTaskHeadList(new ArrayList<>(receiptTaskHeadVOMap.values()));
            head.setTaskItemList(JSONObject.parseArray(
                    JSON.toJSONString(taskItemVoList, SerializerFeature.DisableCircularReferenceDetect),
                    BizReceiptTaskItemVO.class));
        }
    }

    public void saveTaskReqUnLoad(BizContext ctx) {
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTaskReqHeadDTO stockTaskReqHeadInfo = po.getStockTaskReqHeadInfo();
        if (UtilNumber.isEmpty(stockTaskReqHeadInfo.getMerge())) {
            //非合并下架单
            unLoadTaskService.saveTaskReqUnLoad(ctx);
            return;
        }
        //匹配真实行
        this.matchChildItem(ctx);
        List<BizReceiptTaskReqHeadDTO> headDTOList = this.splitTaskReq(ctx);
        for (BizReceiptTaskReqHeadDTO headDTO : headDTOList) {
            po.setStockTaskReqHeadInfo(headDTO);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
            unLoadTaskService.saveTaskReqUnLoad(ctx);
        }
    }

    /**
     * 将汇总行项目提交的下架单据 拆分到子行项目
     */
    public void matchChildItem(BizContext ctx) {
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTaskReqHeadDTO stockTaskReqHeadInfo = po.getStockTaskReqHeadInfo();
        List<BizReceiptTaskReqItemDTO> itemList = stockTaskReqHeadInfo.getItemList();
        List<BizReceiptTaskReqItemDTO> realItemList = new ArrayList<>();
        for (BizReceiptTaskReqItemDTO reqItemDTO : itemList) {
            //待下架数量
            BigDecimal remainderQty = reqItemDTO.getQty().subtract(reqItemDTO.getTaskQty());
            //超发数量
            BigDecimal overQty = reqItemDTO.getSubmitQty().subtract(remainderQty);
            List<BizReceiptTaskReqItemDTO> childItemList = reqItemDTO.getChildItemList();
            for (BizReceiptTaskReqItemDTO childItem : childItemList) {
                //子行项目下架数量
                BigDecimal qty = childItem.getQty();
                //从汇总行下架仓位匹配数量得到的子行项目下架仓位
                List<StockBinDTO> childStockBinList = new ArrayList<>();
                for (StockBinDTO stockBinDTO : reqItemDTO.getStockBinList()) {
                    if (UtilNumber.isEmpty(stockBinDTO.getOperationQty())) {
                        continue;
                    }
                    StockBinDTO childStockBinDTO = UtilBean.newInstance(stockBinDTO, StockBinDTO.class);
                    if (qty.compareTo(stockBinDTO.getOperationQty()) >= 0) {
                        //如果子行需要下架数量 >= 仓位操作数量，则匹配成功复制该仓位到子行项目
                        childStockBinDTO.setOperationQty(stockBinDTO.getOperationQty());
                        childStockBinList.add(childStockBinDTO);
                        //原仓位操作数量0
                        stockBinDTO.setOperationQty(BigDecimal.ZERO);
                        continue;
                    }
                    childStockBinList.add(childStockBinDTO);
                    //原仓位操作数量
                    stockBinDTO.setOperationQty(stockBinDTO.getOperationQty().subtract(qty));
                    qty = stockBinDTO.getOperationQty().subtract(qty);
                }
                if (UtilCollection.isNotEmpty(childStockBinList)) {
                    if (overQty.compareTo(BigDecimal.ZERO) > 0 && childItem.getIsOver().equals(EnumRealYn.TRUE.getIntValue())) {
                        //超发：匹配第一条允许超发的子行项目
                        StockBinDTO stockBinDTO = childStockBinList.get(0);
                        BigDecimal overOperationQty = stockBinDTO.getOperationQty().add(overQty);
                        stockBinDTO.setOperationQty(overOperationQty);
                    }
                    BigDecimal submitQty = childStockBinList.stream().map(StockBinDTO::getOperationQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    childItem.setSubmitQty(submitQty);
                    childItem.setStockBinList(childStockBinList);
                    realItemList.add(childItem);
                }
            }
        }
        stockTaskReqHeadInfo.setItemList(realItemList);
        po.setStockTaskReqHeadInfo(stockTaskReqHeadInfo);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
    }

    /**
     * 按照领料出库单拆分作业单
     */
    public List<BizReceiptTaskReqHeadDTO> splitTaskReq(BizContext ctx) {
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTaskReqHeadDTO stockTaskReqHeadInfo = po.getStockTaskReqHeadInfo();
        List<BizReceiptTaskReqItemDTO> itemList = stockTaskReqHeadInfo.getItemList();
        Map<Long, List<BizReceiptTaskReqItemDTO>> collect = itemList.stream().collect(Collectors.groupingBy(BizReceiptTaskReqItemDTO::getPreReceiptHeadId));
        List<BizReceiptTaskReqHeadDTO> headDTOList = new ArrayList<>();
        for (Map.Entry<Long, List<BizReceiptTaskReqItemDTO>> entry : collect.entrySet()) {
            BizReceiptTaskReqHeadDTO headDTO = UtilBean.newInstance(stockTaskReqHeadInfo, BizReceiptTaskReqHeadDTO.class);
            headDTO.setItemList(entry.getValue());
            headDTOList.add(headDTO);
        }
        return headDTOList;
    }
}
