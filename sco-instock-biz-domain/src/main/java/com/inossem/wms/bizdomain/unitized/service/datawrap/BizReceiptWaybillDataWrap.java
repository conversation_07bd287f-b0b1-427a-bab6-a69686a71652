package com.inossem.wms.bizdomain.unitized.service.datawrap;

import com.inossem.wms.bizdomain.unitized.dao.BizReceiptWaybillMapper;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 成套运单信息表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-05
 */
@Service
public class BizReceiptWaybillDataWrap extends BaseDataWrap<BizReceiptWaybillMapper, BizReceiptWaybill> {

    /**
     * (成套设备验收入库调用)更新运单验收入库相关及批次信息
     *
     * @param List<BizReceiptWaybillDTO> 运单信息
     */
    public void updateWaybillByInspectInput(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByInspectInput(waybillList);
    }
    public void updateWaybillByInspectInputWriteOff(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByInspectInputWriteOff(waybillList);
    }
    /**
     * (成套设备质量差异不符合项处置调用)更新运单质量差异不符合项处置相关信息
     *
     * @param List<BizReceiptWaybillDTO> 运单信息
     */
    public void updateWaybillByQualityInconformityMaintain(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByQualityInconformityMaintain(waybillList);
    }
    public void updateWaybillByQualityInconformityNotice(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByQualityInconformityNotice(waybillList);
    }
    public void updateWaybillByConditionalRelease(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByConditionalRelease(waybillList);
    }
    /**
     * (成套设备数量差异不符合项处置调用)更新运单数量差异不符合项处置相关信息
     *
     * @param List<BizReceiptWaybillDTO> 运单信息
     */
    public void updateWaybillByNumberInconformityMaintain(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByNumberInconformityMaintain(waybillList);
    }
    public void updateWaybillByMoreNumberInconformityMaintain(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByMoreNumberInconformityMaintain(waybillList);
    }
    public void updateWaybillByNumberInconformityNotice(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByNumberInconformityNotice(waybillList);
    }
    public void updateWaybillByMoreNumberInconformityNotice(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateWaybillByMoreNumberInconformityNotice(waybillList);
    }
    public void updateUseRemainder(Integer useSignRemainder, List<Long> idList) {
        this.baseMapper.updateUseRemainder(useSignRemainder, idList);
    }
    public void updateMatIdById(List<BizReceiptWaybillDTO> waybillList) {
        this.baseMapper.updateMatIdById(waybillList);
    }
}
