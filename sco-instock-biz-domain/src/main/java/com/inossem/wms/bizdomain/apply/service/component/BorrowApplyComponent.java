package com.inossem.wms.bizdomain.apply.service.component;

//import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.apply.EnumBorrowType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 借用申请 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Service
public class BorrowApplyComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ApplyCommonComponent applyCommonComponent;

    @Autowired
    protected BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;

    /**
     * 查询借用类型下拉
     *
     * @out ctx 出参 {@link MultiResultVO<> ("EnumBorrowType.toList()":"借用类型下拉框")}
     */
    public void getBorrowTypeDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumBorrowType.toList()));
    }

    /**
     * 页面初始化
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"借用申请单明细","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化返回空的行项目、扩展项对象、按钮组【保存和提交】
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptApplyHeadDTO().setReceiptType(EnumReceiptType.BORROW_APPLY.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                        .setDeptId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptId())
                        .setDeptOfficeId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptOfficeId())
                        .setDeptOfficeName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptOfficeName()),
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 页面初始化数据放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询申请单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptApplySearchPO :"申请单分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (UtilCollection.isEmpty(currentUser.getLocationList())) {
            return;
        }
        List<Long> locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        po.setLocationIdList(locationIds);
        // 分页查询处理
        IPage<BizReceiptApplyPageVO> page = po.getPageObj(BizReceiptApplyPageVO.class);
        bizReceiptApplyHeadDataWrap.getPageVOList(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 借用申请单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"借用申请单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取借用申请单
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHeadDataWrap.getById(headId), BizReceiptApplyHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        headDTO.getItemList().forEach(p -> p.setFormatCode(p.getBatchInfoDto().getFormatCode()) );
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置借用申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 申请单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        return buttonVO;
    }

    /**
     * 提交借用申请单
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "要提交的借用申请单"}
     * @out ctx 出参 {"receiptCode" : "借用申请单单号"}
     */
    public void submitBorrowApply(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存申请单
        applyCommonComponent.saveApply(ctx);
    }

    /**
     * 生成借用出库单
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "借用申请单"}
     */
    public void genBorrowOutput(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        BizReceiptOutputHeadDTO headDTO = new BizReceiptOutputHeadDTO();
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_BORROW.getValue());
        for (BizReceiptApplyItemDTO itemDTO : po.getItemList()) {
            List<BizReceiptOutputBinDTO> binDTOList = new ArrayList<>();
            BizReceiptOutputBinDTO borrowBinDTO = UtilBean.newInstance(itemDTO, BizReceiptOutputBinDTO.class);
            binDTOList.add(borrowBinDTO);
            BizReceiptOutputItemDTO borrowItemDTO = UtilBean.newInstance(itemDTO, BizReceiptOutputItemDTO.class);
            borrowItemDTO.setPreReceiptHeadId(po.getId());
            borrowItemDTO.setPreReceiptItemId(itemDTO.getId());
            borrowItemDTO.setPreReceiptType(EnumReceiptType.BORROW_APPLY.getValue());
            borrowItemDTO.setPreReceiptQty(itemDTO.getQty());
            borrowItemDTO.setReferReceiptHeadId(po.getId());
            borrowItemDTO.setReferReceiptItemId(itemDTO.getId());
            borrowItemDTO.setReferReceiptType(EnumReceiptType.BORROW_APPLY.getValue());
            borrowItemDTO.setBinDTOList(binDTOList);
            itemDTOList.add(borrowItemDTO);
        }
        headDTO.setItemDTOList(itemDTOList);
        // 设置入参上下文
        BizContext ctxOutput = new BizContext();
        ctxOutput.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxOutput.setCurrentUser(ctx.getCurrentUser());
        // 推送MQ生成借用出库单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_BORROW_OUTPUT_STOCK, ctxOutput);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

}
