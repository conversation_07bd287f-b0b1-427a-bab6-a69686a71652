package com.inossem.wms.bizdomain.room.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptRepairHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptRepairItemDataWrap;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptRepairHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptRepairItemDTO;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoom;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptRepairHead;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptRepairItem;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomReceiptSearchPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilSequence;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 住房报修组件库
 *
 **/
@Slf4j
@Component
public class BizRoomReceiptRepairComponent {

    @Autowired
    private BizRoomDataWrap bizRoomDataWrap;

    @Autowired
    private BizRoomReceiptRepairHeadDataWrap bizRoomReceiptRepairHeadDataWrap;

    @Autowired
    private BizRoomReceiptRepairItemDataWrap bizRoomReceiptRepairItemDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    /**
     * 页面初始化
     *
     */
    public void init(BizContext ctx) {
        BizRoomReceiptRepairHeadDTO headDTO = new BizRoomReceiptRepairHeadDTO()
                .setReceiptType(EnumReceiptType.ROOM_REPAIR.getValue())
                .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName());

        BizResultVO<BizRoomReceiptRepairHeadDTO> resultVO = new BizResultVO<>(
                headDTO
                , new ExtendVO().setAttachmentRequired(true)
                , this.setButton(headDTO));

        // 设置页面初始化数据到上下文
        ctx.setVoContextData(resultVO);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizRoomReceiptSearchPO po = ctx.getPoContextData();

        if (UtilCollection.isEmpty(po.getReceiptStatusList())) {
            ctx.setVoContextData(new PageObjectVO<BizRoomReceiptRepairHeadDTO>(new ArrayList(), 0L));
            return;
        }

        // 分页处理
        IPage<BizRoomReceiptRepairHead> page = po.getPageObj(BizRoomReceiptRepairHead.class);

        // 分页查询
        bizRoomReceiptRepairHeadDataWrap.page(page, new LambdaQueryWrapper<BizRoomReceiptRepairHead>()
                .in(BizRoomReceiptRepairHead::getReceiptStatus, po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizRoomReceiptRepairHead::getReceiptCode, po.getReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUser()), BizRoomReceiptRepairHead::getCreateUserName, po.getCreateUser())
                .ge(po.getCreateTimeStart() != null, BizRoomReceiptRepairHead::getCreateTime, UtilDate.getStartOfDay(po.getCreateTimeStart()))
                .le(po.getCreateTimeEnd() != null, BizRoomReceiptRepairHead::getCreateTime, UtilDate.getEndOfDay(po.getCreateTimeEnd()))
                .orderByDesc(BizRoomReceiptRepairHead::getCreateTime)
        );

        List<BizRoomReceiptRepairHeadDTO> dtoList = UtilCollection.toList(page.getRecords(), BizRoomReceiptRepairHeadDTO.class);

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getIdContextData();

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取抬头DTO
        BizRoomReceiptRepairHeadDTO headDto = bizRoomReceiptRepairHeadDataWrap.getDtoById(BizRoomReceiptRepairHeadDTO.class, headId);

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDto);

        // 设置详情到上下文
        ctx.setVoContextData(new BizResultVO<>(headDto, new ExtendVO().setAttachmentRequired(true).setWfRequired(true), buttonVO));
    }

    /**
     * 保存校验
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptRepairHeadDTO po = ctx.getPoContextData();
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 报修房间、联系人、联系电话、故障描述不能为空
        if(UtilNumber.isEmpty(po.getRoomId())
                || UtilString.isNullOrEmpty(po.getContactsUser())
                || UtilString.isNullOrEmpty(po.getContactsTel())
                || UtilString.isNullOrEmpty(po.getFaultDesc())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取房间信息
        BizRoom bizRoom = bizRoomDataWrap.getById(po.getRoomId());

        // 房间当前使用信息抬头id
        po.setRoomUsageHeadId(bizRoom.getCurrentRoomUsageHeadId());

        // 冗余存储房间编号
        po.setRoomCode(bizRoom.getRoomCode());
    }

    /**
     * 提交校验
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptRepairHeadDTO po = ctx.getPoContextData();

        if(UtilNumber.isNotEmpty(po.getId()) && UtilNumber.isEmpty(po.getReceiptStatus())) {
            // 从数据库查询最新的状态
            po.setReceiptStatus(bizRoomReceiptRepairHeadDataWrap.getById(po.getId()).getReceiptStatus());
        }

        // 待处理单据提交 需校验“维修信息”是否有值，有值才允许提交
        if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(po.getReceiptStatus()) && UtilCollection.isEmpty(po.getItemList())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        for(BizRoomReceiptRepairItemDTO bizRoomReceiptRepairItemDTO : po.getItemList()){
            // 具体故障描述、维修结果描述
            if(UtilString.isNullOrEmpty(bizRoomReceiptRepairItemDTO.getFaultDesc()) || UtilString.isNullOrEmpty(bizRoomReceiptRepairItemDTO.getRepairResultsDesc())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        }

        // 创建、草稿状态的提交，设置报修时间
        if(UtilNumber.isEmpty(po.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(po.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())){
            po.setRepairTime(new Date());
        } else if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(po.getReceiptStatus())){
            // 待处理状态的提交，设置维修完成时间
            po.setCompleteTime(new Date());
        }
    }

    /**
     * 保存单据
     */
    public void saveReceipt(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptRepairHeadDTO headDto = ctx.getPoContextData();

        // 单据类型
        headDto.setReceiptType(EnumReceiptType.ROOM_REPAIR.getValue());

        // 单据状态
        headDto.setReceiptStatus(UtilNumber.isEmpty(headDto.getReceiptStatus()) || EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(headDto.getReceiptStatus()) ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue() : headDto.getReceiptStatus());

        // id为空则是新增数据，id有值则是修改数据
        if(UtilNumber.isEmpty(headDto.getId())){
            headDto.setId(UtilSequence.nextId());
            headDto.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.ROOM_REPAIR.getValue()));
        }

        // 保存抬头信息
        bizRoomReceiptRepairHeadDataWrap.saveOrUpdateDto(headDto);

        // 保存单据附件
        receiptAttachmentService.saveBizReceiptAttachment(headDto.getFileList(), headDto.getId(), headDto.getReceiptType(), ctx.getCurrentUser().getId());

        // 删除原有数据库中的行项目数据，删除后重新保存
        bizRoomReceiptRepairItemDataWrap.physicalDelete(new LambdaQueryWrapper<BizRoomReceiptRepairItem>()
                .eq(BizRoomReceiptRepairItem::getHeadId, headDto.getId())
        );

        if(UtilCollection.isEmpty(headDto.getItemList())){
            return;
        }

        AtomicInteger rid = new AtomicInteger(1);

        for (BizRoomReceiptRepairItemDTO itemDto : headDto.getItemList()) {
            itemDto.setId(UtilNumber.isEmpty(itemDto.getId()) ? UtilSequence.nextId() : itemDto.getId());
            itemDto.setHeadId(headDto.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(headDto.getReceiptStatus());
        }

        // 保存行项目列表
        bizRoomReceiptRepairItemDataWrap.saveBatchDto(headDto.getItemList());
    }

    /**
     * 删除单据
     */
    public void deleteReceipt(BizContext ctx) {
        Long receiptHeadId = ctx.getIdContextData();

        // 获取单据信息
        BizRoomReceiptRepairHead headEntity = bizRoomReceiptRepairHeadDataWrap.getById(receiptHeadId);

        // 只有草稿状态的单据才可删除
        if(!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(headEntity.getReceiptStatus())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_CAN_NOT_DELETE);
        }

        // 删除附件
        receiptAttachmentService.deleteBizReceiptAttachment(receiptHeadId, headEntity.getReceiptType());

        // 删除行项目
        bizRoomReceiptRepairItemDataWrap.remove(new LambdaQueryWrapper<BizRoomReceiptRepairItem>().eq(BizRoomReceiptRepairItem::getHeadId, receiptHeadId));

        // 删除抬头
        bizRoomReceiptRepairHeadDataWrap.removeById(receiptHeadId);

    }

    /**
     * <AUTHOR>
     *
     * 修改单据状态
     *
     * @param receiptHeadId 单据抬头id
     */
    public void updateStatus(Long receiptHeadId, EnumReceiptStatus receiptStatus) {

        // 修改单据抬头状态
        bizRoomReceiptRepairHeadDataWrap.update(new LambdaUpdateWrapper<BizRoomReceiptRepairHead>()
                .eq(BizRoomReceiptRepairHead::getId, receiptHeadId)
                .set(BizRoomReceiptRepairHead::getReceiptStatus, receiptStatus.getValue())
                .set(BizRoomReceiptRepairHead::getModifyUserId, UtilCurrentContext.getCurrentUser().getId())
                .set(BizRoomReceiptRepairHead::getModifyTime, new Date())
        );

        // 修改单据行项目状态
        bizRoomReceiptRepairItemDataWrap.update(new LambdaUpdateWrapper<BizRoomReceiptRepairItem>()
                .eq(BizRoomReceiptRepairItem::getHeadId, receiptHeadId)
                .set(BizRoomReceiptRepairItem::getItemStatus, receiptStatus.getValue())
                .set(BizRoomReceiptRepairItem::getModifyUserId, UtilCurrentContext.getCurrentUser().getId())
                .set(BizRoomReceiptRepairItem::getModifyTime, new Date())
        );
    }

    /**
     * 按钮组
     *
     * @param headDTO 单据抬头信息
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizRoomReceiptRepairHeadDTO headDTO) {

        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();

        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(receiptStatus)) {
            // 创建 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(receiptStatus)){
            // 待处理 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        return buttonVO;
    }
}