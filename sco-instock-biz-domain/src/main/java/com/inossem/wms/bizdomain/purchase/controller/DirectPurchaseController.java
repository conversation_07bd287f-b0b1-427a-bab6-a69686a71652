package com.inossem.wms.bizdomain.purchase.controller;

import com.inossem.wms.bizdomain.purchase.service.biz.ContractChangeService;
import com.inossem.wms.bizdomain.purchase.service.biz.DirectPurchaseService;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplySearchPO;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplyUpdatePO;
import com.inossem.wms.common.model.bizdomain.purchase.vo.BizReceiptPurchaseApplyListVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.apply.BudgetClassMapVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@Api(tags = "直接采购")
public class DirectPurchaseController {


    @Autowired
    private DirectPurchaseService directPurchaseService;

    /**
     * 直接采购-获取预算分类预算科目列表
     */
    @ApiOperation(value = "获取预算分类预算科目列表", tags = {"直接采购管理"})
    @PostMapping("/direct-purchase/budgetList")
    public BaseResult<MultiResultVO<BudgetClassMapVO>> getEnumBudgetList(BizContext ctx) {
        directPurchaseService.getEnumBudgetList(ctx);
        MultiResultVO<BudgetClassMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 直接采购分页查询
     */
    @ApiOperation(value = "直接采购分页查询", tags = {"直接采购管理"})
    @PostMapping("/direct-purchase/results")
    @In(parameter = "BizReceiptPurchaseApplySearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyListVO>")
    public BaseResult<PageObjectVO<BizReceiptPurchaseApplyListVO>> getPurchaseApplyPageVo(
            @RequestBody BizReceiptPurchaseApplySearchPO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        directPurchaseService.getPurchaseApplyPageVo(ctx);
        PageObjectVO<BizReceiptPurchaseApplyListVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 直接采购初始化
     */
    @ApiOperation(value = "直接采购初始化", tags = {"直接采购管理"})
    @PostMapping("/direct-purchase/init")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> init(BizContext ctx) {
        directPurchaseService.init(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 直接采购详情
     */
    @ApiOperation(value = "直接采购详情", tags = {"直接采购管理"})
    @GetMapping("/direct-purchase/{id}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> getPurchaseApplyDetail(
            @PathVariable("id") Long id, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        directPurchaseService.getPurchaseApplyDetail(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 直接采购详情
     */
    @ApiOperation(value = "直接采购详情", tags = {"直接采购管理"})
    @GetMapping("/direct-purchase/{id}/{taskId}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> getPurchaseApplyDetail(
            @PathVariable("id") Long id, @PathVariable("taskId") String taskId, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        directPurchaseService.getPurchaseApplyDetail(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 直接采购保存
     */
    @ApiOperation(value = "直接采购保存", tags = {"直接采购管理"})
    @PostMapping("/direct-purchase/save")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"receiptType", "applyUserId", "applyDeptId", "planArrivalDate"})
    @Out(parameter = "String")
    public BaseResult<String> save(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        directPurchaseService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 直接采购提交
     */
    @ApiOperation(value = "直接采购提交", tags = {"直接采购管理"})
    @PostMapping("/direct-purchase/submit")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> submit(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        directPurchaseService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 直接采购过账srm
     */
    @ApiOperation(value = "直接采购过账", tags = {"直接采购管理"})
    @PostMapping("/direct-purchase/post")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> post(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        directPurchaseService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 直接采购删除
     */
    @ApiOperation(value = "直接采购删除", tags = {"直接采购管理"})
    @DeleteMapping("/direct-purchase/{id}")
    @In(parameter = "id", required = "id")
    public BaseResult<String> delete(@PathVariable("id") Long id, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        directPurchaseService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 获取需求计划行项目列表
     */
    @ApiOperation(value = "获取需求计划行项目列表", tags = {"直接采购管理"})
    @PostMapping("/direct-purchase/demand-plan-items")
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyItemDTO>")
    public BaseResult<PageObjectVO<BizReceiptPurchaseApplyItemDTO>> getDemandPlanItems(
            @RequestBody BizReceiptDemandPlanSearchPO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        directPurchaseService.getDemandPlanItems(ctx);
        PageObjectVO<BizReceiptPurchaseApplyItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 取消直接采购行项目
     */
    @ApiOperation(value = "取消直接采购行项目")
    @PostMapping("/direct-purchase/cancelItems")
    public BaseResult<String> cancelItems(@RequestBody BizReceiptPurchaseApplyUpdatePO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        directPurchaseService.cancel(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }


    /**
     * 文件上传功能
     *
     * @param fileInClient 文件
     * @return 上传后文件信息
     */
    @ApiOperation(value = "文件上传", tags = {"附件管理"})
    @PostMapping(path = "/direct-purchase/file/upload", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizCommonFile> upload(@RequestPart("file") MultipartFile fileInClient, CurrentUser user) {
        return BaseResult.success(directPurchaseService.upload(fileInClient, user));
    }

    @ApiOperation(value = "撤销")
    @PostMapping(value = "/direct-purchase/revoke")
    public BaseResult<?> revoke(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        directPurchaseService.revoke(ctx);
        return BaseResult.success();
    }



}
