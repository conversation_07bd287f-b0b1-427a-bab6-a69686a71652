package com.inossem.wms.bizdomain.returns.service.component.movetype;

import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnBinDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnHeadDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSpring;

/**
 * 生成过帐移动类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-05-11
 */

@Service
public class InsSaleReturnMoveTypeComponent {

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 生成记账ins凭证
     *
     * @param headDTO
     * @return
     */
    public StockInsMoveTypeDTO generatePostingInsDoc(BizReceiptReturnHeadDTO headDTO) {
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        dictionaryService = this.getDictionaryService();
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            for (BizReceiptReturnBinDTO bin : itemDTO.getItemInfoList()) {
                StockInsDocBatch insDocBatch = new StockInsDocBatch();
                insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
                insDocBatch.setMatId(itemDTO.getMatId());
                insDocBatch.setBatchId(bin.getBatchId());
                insDocBatch.setFtyId(itemDTO.getFtyId());
                insDocBatch.setLocationId(itemDTO.getLocationId());
                insDocBatch.setUnitId(itemDTO.getUnitId());
                insDocBatch.setDecimalPlace(itemDTO.getDecimalPlace());
                insDocBatch.setDocDate(itemDTO.getDocDate());
                insDocBatch.setPostingDate(itemDTO.getPostingDate());
                insDocBatch.setMoveTypeId(itemDTO.getMoveTypeId());
                insDocBatch.setMoveQty(bin.getQty());
                // 批次库存增加
                insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
                insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBatch.setPreReceiptHeadId(headDTO.getId());
                insDocBatch.setPreReceiptItemId(itemDTO.getId());
                insDocBatch.setPreReceiptBinId(bin.getId());
                insDocBatch.setPreReceiptType(EnumReceiptType.STOCK_RETURN_SALE.getValue());
                insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
                insDocBatch.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
                insDocBatch.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
                insDocBatch.setReferReceiptType(itemDTO.getReferReceiptType());
                insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
                insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
                insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
                insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
                insDocBatchList.add(insDocBatch);
                StockInsDocBin insDocBin = new StockInsDocBin();
                insDocBin.setInsDocRid(insDocRid.toString());
                insDocBin.setMatId(itemDTO.getMatId());
                insDocBin.setBatchId(bin.getBatchId());
                insDocBin.setFtyId(itemDTO.getFtyId());
                insDocBin.setLocationId(itemDTO.getLocationId());
                insDocBin.setUnitId(itemDTO.getUnitId());
                insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
                insDocBin.setMoveQty(bin.getQty());
                // 退库临时区增加
                insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
                insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBin.setPreReceiptHeadId(headDTO.getId());
                insDocBin.setPreReceiptItemId(itemDTO.getId());
                insDocBin.setPreReceiptBinId(bin.getId());
                insDocBin.setPreReceiptType(EnumReceiptType.STOCK_RETURN_SALE.getValue());
                insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
                insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
                insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
                insDocBin.setMatDocCode(itemDTO.getMatDocCode());
                insDocBin.setMatDocRid(itemDTO.getMatDocRid());
                insDocBin.setMatDocYear(itemDTO.getMatDocYear());
                insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
                String typeCode = EnumDefaultStorageType.OUTPUT_RETURN.getTypeCode();
                String binCode = EnumDefaultStorageType.OUTPUT_RETURN.getBinCode();
                Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
                Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
                insDocBin.setTypeId(typeId);
                insDocBin.setBinId(binId);
                insDocBin.setWhId(itemDTO.getWhId());
                insDocBin.setCellId(bin.getCellId());
                insDocBinList.add(insDocBin);
                insDocRid++;
            }
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }

    private DictionaryService getDictionaryService() {
        return UtilSpring.getBean("dictionaryService");
    }

}