package com.inossem.wms.bizdomain.input.service.component.movetype;

import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputBinDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 暂存入库过账移动类型组件库
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */

@Service
@Slf4j
public class ReturnOldInputMoveTypeComponent {

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 其他入库过账凭证
     *
     * @param headDTO 其他入库信息
     * @return ins凭证
     */
    public StockInsMoveTypeDTO generateInsDocToPost(BizReceiptInputHeadDTO headDTO) {
        if (UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 装载批次库存凭证、仓位库存凭证
        StockInsMoveTypeDTO stockInsMoveTypeDTO = new StockInsMoveTypeDTO();
        // 装载批次库存凭证集合
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        // 装载仓位库存凭证集合
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        // 凭证编码
        String insDocCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        // 凭证序号
        AtomicInteger insDocRid = new AtomicInteger(1);

        for (BizReceiptInputItemDTO inputItemDTO : headDTO.getItemList()) {
            if (UtilCollection.isEmpty(inputItemDTO.getBinList())) {
                /* ***** 先过账后作业模式 or 先作业后过账模式 ***** */
                // 批次库存凭证
                insDocBatchList.add(this.getInputInsBatch(headDTO, inputItemDTO, insDocCode, insDocRid.get()));
                // 仓位库存凭证
                insDocBinList
                    .add(this.getInputInsBin(headDTO, inputItemDTO, null, insDocCode, insDocRid.getAndIncrement()));
            } else {
                /* ***** 同时过账作业模式 ***** */
                inputItemDTO.getBinList().forEach(binDTO -> {
                    // 批次库存凭证
                    insDocBatchList.add(this.getInputInsBatch(headDTO, inputItemDTO, insDocCode, insDocRid.get()));
                    // 仓位库存凭证
                    insDocBinList.add(
                        this.getInputInsBin(headDTO, inputItemDTO, binDTO, insDocCode, insDocRid.getAndIncrement()));
                });
            }
        }
        // 设置批次库存凭证
        stockInsMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        // 设置仓位库存凭证
        stockInsMoveTypeDTO.setInsDocBinList(insDocBinList);
        return stockInsMoveTypeDTO;
    }

    /**
     * 批次库存凭证
     *
     * @param headDTO 其他入库单
     * @param inputItemDTO 其他入库单行项目
     * @param insDocCode 其他入库单号
     * @param insDocRid 其他入库单序号
     * @return 批次库存凭证
     */
    private StockInsDocBatch getInputInsBatch(BizReceiptInputHeadDTO headDTO, BizReceiptInputItemDTO inputItemDTO,
        String insDocCode, int insDocRid) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        // 凭证编码
        insDocBatch.setInsDocCode(insDocCode);
        // 凭证序号
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
        // 物料
        insDocBatch.setMatId(inputItemDTO.getMatId());
        // 批次
        insDocBatch.setBatchId(inputItemDTO.getBatchId());
        // 工厂
        insDocBatch.setFtyId(inputItemDTO.getFtyId());
        // 库存地点
        insDocBatch.setLocationId(inputItemDTO.getLocationId());
        // 单位
        insDocBatch.setUnitId(inputItemDTO.getUnitId());
        insDocBatch.setDecimalPlace(inputItemDTO.getDecimalPlace());
        // 移动数量
        insDocBatch.setMoveQty(inputItemDTO.getQty());
        // 库存状态
        insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 前续单据
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptItemId(inputItemDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据
        insDocBatch.setReferReceiptHeadId(inputItemDTO.getReferReceiptHeadId());
        insDocBatch.setReferReceiptItemId(inputItemDTO.getReferReceiptItemId());
        insDocBatch.setReferReceiptType(inputItemDTO.getReferReceiptType());
        // 冲销标识
        insDocBatch.setIsWriteOff(inputItemDTO.getIsWriteOff());
        // 入库 - 新增
        insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        // 创建人
        insDocBatch.setCreateUserId(inputItemDTO.getCreateUserId());
        return insDocBatch;
    }

    /**
     * 入库仓位库存凭证
     *
     * @param headDTO 其他入库单
     * @param inputItemDTO 其他入库单行项目
     * @param binDTO 其他入库仓位
     * @param insDocCode 其他入库单号
     * @param insDocRid 其他入库单序号
     * @return 入库仓位库存凭证
     */
    private StockInsDocBin getInputInsBin(BizReceiptInputHeadDTO headDTO, BizReceiptInputItemDTO inputItemDTO,
        BizReceiptInputBinDTO binDTO, String insDocCode, int insDocRid) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        // 凭证编码
        insDocBin.setInsDocCode(insDocCode);
        // 凭证序号
        insDocBin.setInsDocRid(String.valueOf(insDocRid));
        // 物料
        insDocBin.setMatId(inputItemDTO.getMatId());
        // 批次
        insDocBin.setBatchId(inputItemDTO.getBatchId());
        // 工厂
        insDocBin.setFtyId(inputItemDTO.getFtyId());
        // 库存地点
        insDocBin.setLocationId(inputItemDTO.getLocationId());
        // 仓库
        insDocBin.setWhId(inputItemDTO.getWhId());
        // 单位
        insDocBin.setUnitId(inputItemDTO.getUnitId());
        insDocBin.setDecimalPlace(inputItemDTO.getDecimalPlace());
        // 库存状态
        insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 前续单据
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(inputItemDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据
        insDocBin.setReferReceiptHeadId(inputItemDTO.getReferReceiptHeadId());
        insDocBin.setReferReceiptItemId(inputItemDTO.getReferReceiptItemId());
        insDocBin.setReferReceiptType(inputItemDTO.getReferReceiptType());
        // 冲销标识
        insDocBin.setIsWriteOff(inputItemDTO.getIsWriteOff());
        // 入库 - 新增
        insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        // 创建人
        insDocBin.setCreateUserId(inputItemDTO.getCreateUserId());
        if (UtilObject.isNull(binDTO)) {
            String typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.INPUT.getBinCode();
            // 入库临时存储类型
            insDocBin.setTypeId(dictionaryService.getStorageTypeIdCacheByCode(inputItemDTO.getWhCode(),
                EnumDefaultStorageType.INPUT.getTypeCode()));
            // 入库临时仓位
            insDocBin.setBinId(dictionaryService.getBinIdCacheByCode(inputItemDTO.getWhCode(), typeCode, binCode));
            // 入库临时托盘
            insDocBin.setCellId(0L);
            // 移动数量
            insDocBin.setMoveQty(inputItemDTO.getQty());
        } else {
            // 入库目标存储类型
            insDocBin.setTypeId(binDTO.getTypeId());
            // 入库目标仓位
            insDocBin.setBinId(binDTO.getBinId());
            // 入库目标托盘
            insDocBin.setCellId(binDTO.getCellId() == null ? 0 : binDTO.getCellId());
            // 移动数量
            insDocBin.setMoveQty(binDTO.getQty());
        }
        return insDocBin;
    }

}