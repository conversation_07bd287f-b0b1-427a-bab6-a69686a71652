package com.inossem.wms.bizdomain.paper.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.paper.dto.BizReceiptPaperItemDTO;
import com.inossem.wms.common.model.bizdomain.paper.entity.BizReceiptPaperItem;
import com.inossem.wms.common.model.bizdomain.paper.po.BizReceiptPaperSearchPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 图纸物料信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface BizReceiptPaperItemMapper extends WmsBaseMapper<BizReceiptPaperItem> {

    List<BizReceiptPaperItemDTO> getPage(IPage<BizReceiptPaperItemDTO> page, @Param(Constants.WRAPPER) WmsQueryWrapper<BizReceiptPaperSearchPO> queryWrapper);
}
