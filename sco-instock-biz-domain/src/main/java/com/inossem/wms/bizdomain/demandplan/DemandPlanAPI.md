# 需求计划接口文档

---
## 1. 需求计划分页查询

### 基本信息

**Path:** /demandplans/results

**Method:** POST

**Description:** 根据查询条件分页获取需求计划列表

### 请求参数

#### Body

```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "receiptCode": "XQ241024001",
  "receiptStatusList": [10,20,30],
  "demandType": 1,
  "urgentFlag": 10,
  "budgetType": 10,
  "demandPlanName": "2024计划",
  "planArrivalDateStart": "2024-01-01",
  "planArrivalDateEnd": "2024-12-31",
  "createTimeStart": "2024-01-01",
  "createTimeEnd": "2024-12-31",
  "demandUserCode": "24001844",
  "demandUserName": "张三",
  "createUserCode": "Admin",
  "createUserName": "管理员",
  "demandDeptCode": "D001",
  "demandDeptName": "采购部",
  "matTypeCode": "MT001",
  "matTypeName": "原材料"
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|pageIndex|number|是|页码|
|pageSize|number|是|每页大小|
|receiptCode|string|否|单据编号|
|receiptStatusList|array|否|单据状态列表|
|demandType|number|否|需求类型(1:生产物资,2:资产,3:非生产)|
|urgentFlag|number|否|紧急标识(10:特急,20:正常)|
|budgetType|number|否|预算类型|
|demandPlanName|string|否|需求计划名称|
|planArrivalDateStart|string|否|计划到货开始日期|
|planArrivalDateEnd|string|否|计划到货结束日期|
|createTimeStart|string|否|创建开始时间|
|createTimeEnd|string|否|创建结束时间|
|demandUserCode|string|否|需求人编码|
|demandUserName|string|否|需求人名称|
|createUserCode|string|否|创建人编码|
|createUserName|string|否|创建人名称|
|demandDeptCode|string|否|需求部门编码|
|demandDeptName|string|否|需求部门名称|
|matTypeCode|string|否|物料类型编码|
|matTypeName|string|否|物料类型名称|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "pageIndex": 1,
    "pageSize": 10,
    "total": 100,
    "rows": [{
      "id": 159843409264782,
      "receiptCode": "XQ241024001",
      "receiptType": 400,
      "receiptStatus": 10,
      "receiptStatusI18n": "草稿",
      "demandType": 1,
      "demandTypeI18n": "生产物资类",
      "demandPlanType": 10,
      "demandPlanTypeI18n": "一次性计划",
      "demandUserCode": "24001844",
      "demandUserName": "张三",
      "demandDeptCode": "D001",
      "demandDeptName": "采购部",
      "urgentFlag": 10,
      "urgentFlagI18n": "特急",
      "budgetType": 10,
      "budgetTypeI18n": "制造成本类",
      "planArrivalDate": "2024-10-24",
      "demandPlanName": "2024年第一季度工具采购计划",
      "createTime": "2024-01-01 10:00:00",
      "createUserName": "管理员"
    }]
  }
}
```

---
## 2. 需求计划初始化

### 基本信息

**Path:** /demandplans/init

**Method:** POST

**Description:** 初始化新的需求计划单，设置默认值

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "data": {
      "receiptType": 400,
      "receiptStatus": 10,
      "createTime": "2024-01-01 10:00:00",
      "createUserId": 1,
      "createUserName": "管理员",
      "demandTypeList": [{
        "code": 1,
        "name": "生产物资类"
      }],
      "demandPlanTypeList": [{
        "code": 10,
        "name": "一次性计划"
      }],
      "urgentFlagList": [{
        "code": 10,
        "name": "特急"
      }],
      "budgetTypeList": [{
        "code": 10,
        "name": "制造成本类"
      }],
      "subjectTypeList": [{
        "code": 10,
        "name": "成本中心"
      }]
    },
    "extendVO": {
      "attachmentRequired": true,
      "operationLogRequired": true,
      "relationRequired": true
    },
    "buttonVO": {
      "buttonSave": true,
      "buttonSubmit": true,
      "buttonDelete": false
    }
  }
}
```

---
## 3. 需求计划保存

### 基本信息

**Path:** /demandplans/save

**Method:** POST

**Description:** 保存需求计划（新增或修改）

### 请求参数

#### Body

```json
{
  "id": 159843409264782,
  "receiptCode": "XQ241024001",
  "receiptType": 400,
  "demandType": 1,
  "demandPlanType": 10,
  "demandUserId": 1,
  "demandDeptId": 1,
  "urgentFlag": 10,
  "budgetType": 10,
  "planArrivalDate": "2024-10-24",
  "demandPlanName": "2024年第一季度工具采购计划",
  "purchaseReason": "生产需要",
  "suggestVendorList": "供应商A,供应商B",
  "remark": "备注信息",
  "subjectType": 10,
  "itemList": [{
    "id": 159843409264783,
    "headId": 159843409264782,
    "rid": "0010",
    "matId": 60000001,
    "unitId": 7,
    "ftyId": 1,
    "demandQty": 10.00,
    "itemRemark": "备注",
    "assetCardNo": "ASSET001",
    "assetCardDesc": "办公设备",
    "productName": "激光打印机",
    "matGroupId": 1,
    "wbsNo": "WBS2024001",
    "costCenter": "COST001"
  }],
  "fileList": [{
    "fileName": "附件1.pdf",
    "fileUrl": "http://xxx"
  }]
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|否|主键ID(修改时必填)|
|receiptCode|string|否|单据编号(修改时必填)|
|receiptType|number|是|单据类型(400:需求计划)|
|demandType|number|是|需求类型|
|demandPlanType|number|是|需求计划类型|
|demandUserId|number|是|需求人ID|
|demandDeptId|number|是|需求部门ID|
|urgentFlag|number|是|紧急标识|
|budgetType|number|是|预算类型|
|planArrivalDate|string|是|计划到货日期|
|demandPlanName|string|是|需求计划名称|
|purchaseReason|string|否|采购原因|
|suggestVendorList|string|否|建议供应方名单|
|remark|string|否|备注|
|subjectType|number|否|科目类别(非生产类必填)|
|itemList|array|是|行项目列表|
|fileList|array|否|附件列表|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": "XQ241024001"
}
```

---
## 4. 需求计划提交

### 基本信息

**Path:** /demandplans/submit

**Method:** POST

**Description:** 提交需求计划进入审批流程

### 请求参数

#### Body

```json
{
  "id": 159843409264782,
  "receiptCode": "XQ241024001",
  "remark": "提交备注"
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|是|主键ID|
|receiptCode|string|是|单据编号|
|remark|string|否|提交备注|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": "XQ241024001"
}
```

---
## 5. 需求计划删除

### 基本信息

**Path:** /demandplans/{id}

**Method:** DELETE

**Description:** 删除草稿状态的需求计划

### 请求参数

#### Path Parameters

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|是|需求计划ID|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

---
## 6. 需求计划取消

### 基本信息

**Path:** /demandplans/cancel

**Method:** POST

**Description:** 取消已提交或已驳回状态的需求计划

### 请求参数

#### Body

```json
{
  "id": 159843409264782,
  "receiptCode": "XQ241024001",
  "remark": "取消原因"
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|是|主键ID|
|receiptCode|string|是|单据编号|
|remark|string|否|取消原因|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": "XQ241024001"
}
```

---
## 7. 发起审批

### 基本信息

**Path:** /demandplans/start-workflow

**Method:** POST

**Description:** 发起需求计划审批流程

### 请求参数

#### Body

```json
{
  "id": 159843409264782,
  "receiptCode": "XQ241024001",
  "remark": "提交审批"
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|是|主键ID|
|receiptCode|string|是|单据编号|
|remark|string|否|审批说明|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": "XQ241024001"
}
```

---
## 8. 审批回调

### 基本信息

**Path:** /demandplans/approval-callback

**Method:** POST

**Description:** 处理审批结果回调

### 请求参数

#### Body

```json
{
  "receiptHeadId": 159843409264782,
  "receiptCode": "XQ241024001",
  "approveStatus": 1,
  "approveResult": "同意",
  "approveRemark": "审批通过",
  "approveUser": {
    "id": 1,
    "userCode": "admin",
    "userName": "管理员"
  }
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|receiptHeadId|number|是|单据头ID|
|receiptCode|string|是|单据编号|
|approveStatus|number|是|审批状态(1:通过,2:驳回)|
|approveResult|string|是|审批结果|
|approveRemark|string|否|审批意见|
|approveUser|object|是|审批人信息|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

---
## 9. 需求计划物料查询

### 基本信息

**Path:** /demandplans/materials

**Method:** POST

**Description:** 分页查询可用于需求计划的物料

### 请求参数

#### Body

```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "matGroupId": 1,
  "matCode": "M001005",
  "matName": "物料描述"
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|pageIndex|number|是|页码|
|pageSize|number|是|每页大小|
|matGroupId|number|否|物料组ID|
|matCode|string|否|物料编码|
|matName|string|否|物料名称|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "pageIndex": 1,
    "pageSize": 10,
    "total": 100,
    "rows": [{
      "id": 60000001,
      "matCode": "M001005",
      "matName": "物料描述001003",
      "matTypeId": 1,
      "matTypeCode": "MT001",
      "matTypeName": "原材料",
      "matGroupId": 1,
      "matGroupCode": "MG001",
      "matGroupName": "工具类",
      "unitId": 7,
      "unitCode": "M3",
      "unitName": "立方米",
      "stockQty": 100.00,
      "safetyStock": 50.00,
      "purchasePrice": 99.99
    }]
  }
}
```

---
## 10. 需求计划行项目导入

### 基本信息

**Path:** /demandplans/import

**Method:** POST

**Description:** 通过Excel导入需求计划行项目

### 请求参数

#### Body

```form-data
file: [Excel文件]
head: {
  "id": 159843409264782,
  "receiptCode": "XQ241024001",
  "demandType": 1,
  "subjectType": 10
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|file|file|是|Excel文件|
|head|object|是|需求计划头信息|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "data": {
      "id": 159843409264782,
      "receiptCode": "XQ241024001",
      "receiptType": 400,
      "receiptStatus": 10,
      "itemList": [{
        "id": 159843409264783,
        "headId": 159843409264782,
        "rid": "0010",
        "matId": 60000001,
        "matCode": "M001005",
        "matName": "物料描述",
        "unitId": 7,
        "demandQty": 10.00,
        "itemRemark": "备注"
      }]
    },
    "extendVO": {
      "attachmentRequired": true,
      "operationLogRequired": true,
      "relationRequired": true
    },
    "buttonVO": {
      "buttonSave": true,
      "buttonSubmit": true,
      "buttonDelete": true
    }
  }
}
```