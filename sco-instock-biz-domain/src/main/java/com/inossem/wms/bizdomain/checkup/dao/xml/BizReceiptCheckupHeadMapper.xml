<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.checkup.dao.BizReceiptCheckupHeadMapper">
    <select id="selectPageList" resultType="com.inossem.wms.common.model.bizdomain.checkup.dto.BizReceiptCheckupHeadDTO">
        SELECT
        brch.*
        FROM biz_receipt_checkup_head brch
        inner join biz_receipt_checkup_item brci on brch.id = brci.head_id
        where brch.is_delete = 0
        <if test="po.description != null and po.description != '' ">
            and
            brch.description like CONCAT('%',#{po.description},'%' )
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size > 0 ">
            and brch.receipt_status IN
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP by brch.id
        order by brch.create_time desc
    </select>

</mapper>
