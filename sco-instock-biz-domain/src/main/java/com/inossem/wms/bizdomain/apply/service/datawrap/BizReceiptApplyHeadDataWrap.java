package com.inossem.wms.bizdomain.apply.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.apply.dao.BizReceiptApplyHeadMapper;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplyQueryListPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 借用申请单抬头表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Service
public class BizReceiptApplyHeadDataWrap extends BaseDataWrap<BizReceiptApplyHeadMapper, BizReceiptApplyHead> {

    /**
     * 查询申请单列表-分页
     *
     * @param page 分页参数
     * @param po 条件
     * @return 申请单列表
     *
     */
    public IPage<BizReceiptApplyPageVO> getPageVOList(IPage<BizReceiptApplyPageVO> page, BizReceiptApplySearchPO po) {
        List<BizReceiptApplyPageVO> pageVOList = this.baseMapper.selectPageVOList(page, po);
        return page.setRecords(pageVOList);
    }
    /**
     * 查询申请单列表-分页
     *
     * @param page 分页参数
     * @param po 条件
     * @return 申请单列表
     *
     */
    public IPage<BizReceiptApplyPageVO> selectPageVOListUnitized(IPage<BizReceiptApplyPageVO> page, BizReceiptApplySearchPO po) {
        List<BizReceiptApplyPageVO> pageVOList = this.baseMapper.selectPageVOListUnitized(page, po);
        return page.setRecords(pageVOList);
    }
    /**
     * 查询维修申请单列表-分页
     *
     * @param page 分页参数
     * @param po 条件
     * @return 申请单列表
     *
     */
    public IPage<BizReceiptApplyPageVO> getOutputPageVoList(IPage<BizReceiptApplyPageVO> page, BizReceiptApplySearchPO po) {
        List<BizReceiptApplyPageVO> pageVOList = this.baseMapper.selectOutputPageVoList(page, po);
        return page.setRecords(pageVOList);
    }

    /**
     * 领料出库单申请分页
     * @param page
     * @param pageWrapper
     * @return
     */
    public IPage<BizReceiptApplyPageVO> getApplyInfoPageVOList(IPage<BizReceiptApplyPageVO> page, WmsQueryWrapper<BizReceiptApplyQueryListPO> pageWrapper) {
        return page.setRecords(this.baseMapper.selectApplyInfoPageVOList(page, pageWrapper));
    }
    /**
     * 领料出库单申请分页
     * @param page
     * @param pageWrapper
     * @return
     */
    public IPage<BizReceiptApplyPageVO> getApplyInfoPageVOListUnitized(IPage<BizReceiptApplyPageVO> page, WmsQueryWrapper<BizReceiptApplyQueryListPO> pageWrapper) {
        return page.setRecords(this.baseMapper.selectApplyInfoPageVOListUnitized(page, pageWrapper));
    }
    /**
     * 查询单据
     *
     * @param ids ID集合
     * @return List<BizReceiptInspectItemDTO>
     */
    public List<BizReceiptApplyPageVO> findByIds(List<Long> ids) {
        return this.baseMapper.findByIds(ids);
    }

    public void updateStatusById(int receiptStatus, Long id) {
        this.getBaseMapper().updateStatusById(receiptStatus, id);
    }
}
