<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.matview.dao.BizMaterialViewAuditNuclearMapper">
    <!-- biz_material_view_audit_nuclear 物料主数据视图审批-核电级别-->

    <!--温馨提示varchar2长度默认为50, 时间类型 datetime(3)/datetime 请自行修改
    create table biz_material_view_audit_nuclear(
         	id bigint not null   comment 'id'primary key ,
         	head_id bigint null   comment '单据id' ,
         	item_status VARCHAR null   comment '状态【草稿、审批中、已驳回、已完成】' ,
         	mat_id bigint null   comment '物料id' ,
         	old_mat_code VARCHAR(50) null   comment '物料id' ,
         	assets VARCHAR(50) null   comment '是否资产 X标识是，空标识否' ,
         	collect VARCHAR(50) null   comment '核电集采 X标识是，空标识否' ,
         	share VARCHAR(50) null   comment '是否共享备件 X标识是，空标识否' ,
         	life_time VARCHAR(50) null   comment '总货架寿命' ,
         	level VARCHAR(50) null   comment '质保等级' ,
         	nuclear_safe_admin VARCHAR(50) null   comment '是否核安全局监管备件' ,
         	purchase_receipt_content VARCHAR(50) null   comment '采购订单文本' ,
         	eomm VARCHAR(50) null   comment 'EOMM手册' ,
         	basic_mat VARCHAR(50) null   comment '基本物料' ,
         	min_leave_life_time VARCHAR(50) null   comment '最小剩余货架寿命' ,
         	sled VARCHAR(50) null   comment 'SLED的期间标识' ,
         	order_unit VARCHAR(50) null   comment '订单单位' ,
         	drawing_sheet VARCHAR(50) null   comment '电厂图纸' ,
         	file VARCHAR(50) null   comment '附件/照片' ,
         	annotation VARCHAR(50) null   comment '内部批注' ,
         	inspect VARCHAR(50) null   comment '检验文本' ,
         	convert VARCHAR(50) null   comment '其它单位与基本计量单位的转换关系' ,
         	other_unit VARCHAR(50) null   comment '其它单位' ,
         	is_delete VARCHAR null   comment '删除标识' ,
         	create_time  datetime null   comment '创建时间' ,
         	modify_time  datetime null   comment '修改时间' ,
         	create_user_id bigint null   comment '创建人id' ,
         	modify_user_id bigint null   comment '修改人id' 
    )
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_general_ci
     comment '物料主数据视图审批-核电级别';
    -->

</mapper>
