package com.inossem.wms.bizdomain.register.dao;

import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 登记单行项目明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
public interface BizReceiptRegisterItemMapper extends WmsBaseMapper<BizReceiptRegisterItem> {

    /**
     * (到货登记调用)更新行项目冲销数量
     *
     * @param itemIds 不符合项处置单行项目id
     */
    void updateArrivalRegisterItemWriteOffQty(@Param("itemIds") List<Long> itemIds);

    /**
     * (成套设备到货登记调用)更新行项目冲销数量
     *
     * @param itemIds 不符合项处置单行项目id
     */
    void updateUnitizedArrivalRegisterItemWriteOffQty(@Param("itemIds") List<Long> itemIds);

}
