package com.inossem.wms.bizdomain.apply.service.biz;

import com.inossem.wms.bizdomain.apply.service.component.ApplyCommonComponent;
import com.inossem.wms.bizdomain.apply.service.component.MatReqReturnApplyComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 退库申请 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-07
 */
@Service
public class MatReqReturnApplyService {

    @Autowired
    protected ApplyCommonComponent applyCommonComponent;

    @Autowired
    protected MatReqReturnApplyComponent matReqReturnApplyComponent;

    /**
     * 退库申请-初始化
     *
     * @return 退库申请单
     */
    public void init(BizContext ctx) {

        // 页面初始化
        matReqReturnApplyComponent.setInit(ctx);

    }

    /**
     * 查询申请单列表-分页
     *
     * @param ctx-po 申请单分页查询入参
     * @return 申请单列表
     */
    public void getPage(BizContext ctx) {

        // 查询申请单列表-分页
        matReqReturnApplyComponent.setPage(ctx);

    }

    /**
     * 查询退库申请单详情
     *
     * @param ctx-id 退库申请单抬头表主键
     * @return 退库申请单详情
     */
    public void getInfo(BizContext ctx) {

        // 退库申请单详情
        matReqReturnApplyComponent.getInfo(ctx);

        // 设置详情页单据流
        applyCommonComponent.setInfoExtendRelation(ctx);

        // 开启附件
        applyCommonComponent.setExtendAttachment(ctx);

        // 开启操作日志
        applyCommonComponent.setExtendOperationLog(ctx);

        // 开启审批
        applyCommonComponent.setExtendWf(ctx);

    }

    /**
     * 退库申请-保存
     *
     * @param ctx-po 保存退库申请表单参数
     * @return ctx-receiptCode 退库申请单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 申请单保存校验
        applyCommonComponent.checkSaveApply(ctx);

        // 保存申请单
        applyCommonComponent.saveApply(ctx);

        // 保存单据流
        applyCommonComponent.saveReceiptTree(ctx);

        // 保存附件
        applyCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 退库申请-提交
     *
     * @param ctx-po 提交退库申请表单参数
     * @return ctx-receiptCode 退库申请单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 申请单保存校验
        applyCommonComponent.checkSaveApply(ctx);

        // 提交退库申请单
        matReqReturnApplyComponent.submitMatReqReturnApply(ctx);

        // 保存单据流
        applyCommonComponent.saveReceiptTree(ctx);

        // 保存附件
        applyCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

        // 发起审批
//        matReqReturnApplyComponent.startWorkFlow(ctx);
        // 更新单据已完成
        applyCommonComponent.updateStatusCompleted(ctx);
        // 生成退库质检分配单
        matReqReturnApplyComponent.genMatReqReturnDistributeInspect(ctx);
    }

    /**
     * 退库申请-删除
     *
     * @param ctx-id 退库申请单抬头表主键
     * @return ctx-receiptCode 退库申请单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 刪除退库申请单
        applyCommonComponent.deleteInfo(ctx);

        // 删除申请单单据流
        applyCommonComponent.deleteReceiptTree(ctx);

        // 删除申请单附件
        applyCommonComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        applyCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 查询出库单行项目列表
     *
     * @param ctx-po 查询前序单据入参
     */
    public void getOutputReceiptItemList(BizContext ctx) {

        // 查询出库单行项目列表
        matReqReturnApplyComponent.getOutputReceiptItemList(ctx);

    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_CALLBACK_MAT_REQ_RETURN_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {

        // 审批回调
        matReqReturnApplyComponent.approvalCallback(wfReceiptCo);

    }

    public void revoke(BizContext ctx) {

        // 撤销
        matReqReturnApplyComponent.revoke(ctx);

    }


}
