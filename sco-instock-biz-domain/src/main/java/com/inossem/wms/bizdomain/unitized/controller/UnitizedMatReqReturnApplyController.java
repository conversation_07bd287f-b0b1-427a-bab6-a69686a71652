package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedMatReqReturnApplyService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.returns.vo.BizReceiptReturnPreVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.PreReceiptQueryPO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 退库申请
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-07
 */
@RestController
public class UnitizedMatReqReturnApplyController {

    @Autowired
    protected UnitizedMatReqReturnApplyService matReqReturnApplyService;

    /**
     * 退库申请-初始化
     *
     * @return 退库申请单
     */
    @ApiOperation(value = "退库申请-初始化", tags = {"退库管理-退库申请"})
    @GetMapping(value = "/unitized/apply/mat-req-return-apply/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> init(BizContext ctx) {
        matReqReturnApplyService.init(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询退库申请单列表-分页
     *
     * @param po 申请单分页查询入参
     * @return 退库申请单列表
     */
    @ApiOperation(value = "查询退库申请单列表-分页", tags = {"退库管理-退库申请"})
    @PostMapping(value = "/unitized/apply/mat-req-return-apply/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptApplyPageVO>> getPage(@RequestBody BizReceiptApplySearchPO po, BizContext ctx) {
        matReqReturnApplyService.getPage(ctx);
        PageObjectVO<BizReceiptApplyPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询退库申请单详情
     *
     * @param id 退库申请单抬头表主键
     * @return 退库申请单详情
     */
    @ApiOperation(value = "查询退库申请单详情", tags = {"退库管理-退库申请"})
    @GetMapping(value = "/unitized/apply/mat-req-return-apply/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        matReqReturnApplyService.getInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 退库申请-保存
     *
     * @param po 保存退库申请表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "退库申请-保存", tags = {"退库管理-退库申请"})
    @PostMapping(value = "/unitized/apply/mat-req-return-apply/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        matReqReturnApplyService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 退库申请-提交
     *
     * @param po 提交退库申请表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "退库申请-提交", tags = {"退库管理-退库申请"})
    @PostMapping(value = "/unitized/apply/mat-req-return-apply/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        matReqReturnApplyService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 退库申请-删除
     *
     * @param id 退库申请单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "退库申请-删除", tags = {"退库管理-退库申请"})
    @DeleteMapping(value = "/unitized/apply/mat-req-return-apply/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        matReqReturnApplyService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_DELETE_SUCCESS, receiptCode);
    }

    /**
     * 查询领料出库单行项目列表
     *
     * @param po 查询前序单据入参
     * @return 领料出库单行项目
     */
    @ApiOperation(value = "查询前序单据", tags = {"退库管理-退库申请"})
    @PostMapping(value = "/unitized/apply/mat-req-return-apply/output-item/results")
    public BaseResult<MultiResultVO<BizReceiptReturnPreVO>> getOutputReceiptItemList(@RequestBody PreReceiptQueryPO po, BizContext ctx) {
        matReqReturnApplyService.getOutputReceiptItemList(ctx);
        MultiResultVO<BizReceiptReturnPreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}
