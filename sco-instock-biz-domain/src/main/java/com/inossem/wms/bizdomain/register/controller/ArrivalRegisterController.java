package com.inossem.wms.bizdomain.register.controller;

import com.inossem.wms.bizdomain.register.service.biz.ArrivalRegisterService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptRegisterSearchPO;
import com.inossem.wms.common.model.bizdomain.register.vo.BizRecieptRegisterPageVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.register.VisualCheckMapVO;
import com.inossem.wms.common.model.masterdata.car.entity.DicCar;
import com.inossem.wms.common.model.masterdata.car.entity.DicCarType;
import com.inossem.wms.common.model.masterdata.car.entity.DicSling;
import com.inossem.wms.common.model.print.label.LabelReceiptRegisterBox;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 到货登记 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-25
 */
@RestController
public class ArrivalRegisterController {

    @Autowired
    protected ArrivalRegisterService arrivalRegisterService;

    /**
     * 查询车辆类型下拉
     *
     * @return 车辆类型下拉框
     */
    @ApiOperation(value = "查询车辆类型下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/register/arrival-register/car-type-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicCarType>> getCarTypeDown(BizContext ctx) {
        arrivalRegisterService.getCarTypeDown(ctx);
        MultiResultVO<DicCarType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询车辆下拉
     *
     * @return 车辆下拉框
     */
    @ApiOperation(value = "查询车辆下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/register/arrival-register/car-down/{code}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicCar>> getCarDown(@PathVariable("code") String code, BizContext ctx) {
        arrivalRegisterService.getCarDown(ctx);
        MultiResultVO<DicCar> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询吊带编号下拉
     *
     * @return 吊带编号下拉框
     */
    @ApiOperation(value = "查询吊带编号下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/register/arrival-register/sling-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicSling>> getSlingDown(BizContext ctx) {
        arrivalRegisterService.getSlingDown(ctx);
        MultiResultVO<DicSling> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询外观检查下拉
     *
     * @return 外观检查下拉框
     */
    @ApiOperation(value = "查询外观检查下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/register/arrival-register/visual-check-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<VisualCheckMapVO>> getVisualCheckDown(BizContext ctx) {
        arrivalRegisterService.getVisualCheckDown(ctx);
        MultiResultVO<VisualCheckMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询到货登记单列表-分页
     *
     * @param po 登记单分页查询入参
     * @return 到货登记单列表
     */
    @ApiOperation(value = "查询到货登记单列表-分页", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizRecieptRegisterPageVO>> getPage(@RequestBody BizReceiptRegisterSearchPO po, BizContext ctx) {
        arrivalRegisterService.getPage(ctx);
        PageObjectVO<BizRecieptRegisterPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询到货登记单详情
     *
     * @param id 到货登记单抬头表主键
     * @return 到货登记单详情
     */
    @ApiOperation(value = "查询到货登记单详情", tags = {"送货管理-到货登记"})
    @GetMapping(value = "/register/arrival-register/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRegisterHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        arrivalRegisterService.getInfo(ctx);
        BizResultVO<BizReceiptRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 导出物项清单
     */
    @ApiOperation(value = "导出物项清单", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/register/export/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@PathVariable("id") Long id, BizContext ctx) {
        arrivalRegisterService.export(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 查询到货登记单详情
     *
     * @return 到货登记单详情
     */
    @ApiOperation(value = "初始化", tags = {"送货管理-到货登记"})
    @GetMapping(value = "/register/arrival-register/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRegisterHeadDTO>> init(BizContext ctx) {
        arrivalRegisterService.init(ctx);
        BizResultVO<BizReceiptRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 到货登记-保存
     *
     * @param po 保存到货登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-保存", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        arrivalRegisterService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 到货登记-提交
     *
     * @param po 提交到货登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-提交", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        arrivalRegisterService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 到货登记-过账
     *
     * @param po 提交到货登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-过账", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult post(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        arrivalRegisterService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 到货登记-删除
     *
     * @param id 到货登记单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-删除", tags = {"送货管理-到货登记"})
    @DeleteMapping(value = "/register/arrival-register/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        arrivalRegisterService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_DELETE_SUCCESS, receiptCode);
    }

    /**
     * 到货登记-冲销
     *
     * @param po 查询条件
     * @return 到货登记行项目
     */
    @ApiOperation(value = "到货登记-冲销", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        arrivalRegisterService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 到货登记-打印待检标识-PDA
     * @auth zhibo
     * @param po 打印入参
     * @param ctx 请求上下文
     * @return 到货登记单数据
     */
    @ApiOperation(value = "到货登记-打印待检标识", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptRegisterHeadDTO> po, BizContext ctx) {
        arrivalRegisterService.boxApplyLabelPrint(ctx);
        List<LabelReceiptRegisterBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    /**
     * 到货登记单作废
     *
     * @param id 到货登记单抬头表主键
     * @return 到货登记单详情
     */
    @ApiOperation(value = "到货登记-撤回", tags = {"送货管理-到货登记"})
    @GetMapping(value = "/register/arrival-register/cancel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> cancel(@PathVariable("id") Long id, BizContext ctx) {
        arrivalRegisterService.cancel(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_DELETE_SUCCESS, receiptCode);
    }


    @ApiOperation(value = "到货登记-查询 油品送货通知", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/get-delivery-notice")
    public BaseResult<?> getDeliveryNotice(@RequestBody BizReceiptDeliveryNoticeSearchPO po, BizContext ctx) {
        arrivalRegisterService.getDeliveryNotice(ctx);
        List<BizReceiptRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }


    /**
     * 到货登记单撤销
     */
    @ApiOperation(value = "到货登记-撤销", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/register/arrival-register/revoke", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> revoke(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        arrivalRegisterService.revoke(ctx);
        return BaseResult.success();
    }
}
