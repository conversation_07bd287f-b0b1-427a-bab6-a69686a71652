package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedUnLoadTaskService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqMergePo;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskSearchPo;
import com.inossem.wms.common.model.bizdomain.task.vo.BizReceiptTaskVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 成套设备下架
 */

@RestController
@Api(tags = "成套设备仓库管理-下架管理")
public class UnitizedUnLoadTaskController {

    @Autowired
    private UnitizedUnLoadTaskService unitizedUnLoadTaskService;

    /**
     * 获取下架请求列表
     *
     * @param po 查询条件参数
     * @return 下架请求列表集合
     */
    @ApiOperation(value = "获取成套设备下架请求列表-分页", tags = {"仓库管理-下架管理"})
    @PostMapping(path = "/unitized/warehouse/unload/task-req/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptTaskVO>> getTaskReqPageList(@RequestBody BizReceiptTaskSearchPo po,
                                                                         BizContext ctx) {
        unitizedUnLoadTaskService.getTaskReqPageList(ctx);
        PageObjectVO<BizReceiptTaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "按照作业请求id查找作业请求", tags = {"仓库管理-下架管理"})
    @GetMapping(path = "/unitized/warehouse/unload/task-req/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptTaskVO>> getTaskReq(@PathVariable("id") Long id, BizContext ctx) {
        unitizedUnLoadTaskService.getTaskReq(ctx);
        BizResultVO<BizReceiptTaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "创建备料单", notes = "创建备料单", tags = {"仓库管理-下架管理"})
    @PostMapping(path = "/unitized/warehouse/unload/merge", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> merge(@RequestBody BizReceiptTaskReqMergePo po, BizContext ctx) {
        // 保存上架作业请求
        unitizedUnLoadTaskService.merge(ctx);
        return BaseResult.success((String)ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE));
    }

    @ApiOperation(value = "保存下架作业请求", notes = "保存下架作业请求", tags = {"仓库管理-下架管理"})
    @PostMapping(path = "/unitized/warehouse/unload/task-req", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> saveTaskReqUnLoad(@RequestBody BizReceiptTaskReqSavePo po, BizContext ctx) {
        // 保存上架作业请求
        unitizedUnLoadTaskService.saveTaskReqUnLoad(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TASK_SUMBIT_SUCCESS,
                (String)ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE));
    }

}