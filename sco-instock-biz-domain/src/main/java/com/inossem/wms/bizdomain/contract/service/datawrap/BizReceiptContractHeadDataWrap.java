package com.inossem.wms.bizdomain.contract.service.datawrap;

import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.PurchaseDTO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractLedgerListExportVO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractReportListVO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.contract.dao.BizReceiptContractHeadMapper;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractListVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;

import java.util.List;

/**
 * <p>
 * 合同头 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class BizReceiptContractHeadDataWrap extends BaseDataWrap<BizReceiptContractHeadMapper, BizReceiptContractHead> {

    public IPage<BizReceiptContractListVO> getContractPageVo(IPage<BizReceiptContractListVO> page, 
            WmsQueryWrapper<BizReceiptContractSearchPO> wrapper) {
        return baseMapper.getContractPageVo(page, wrapper);
    }

    public IPage<BizReceiptContractReportListVO> getContractReportPageVo(IPage<BizReceiptContractReportListVO> page, WmsQueryWrapper<BizReceiptContractSearchPO> wrapper) {
        return baseMapper.getContractReportPageVo(page, wrapper);
    }

    public IPage<BizReceiptContractLedgerListExportVO> getContractLedgerReportPageVo(IPage<BizReceiptContractLedgerListExportVO> page, WmsQueryWrapper<BizReceiptContractSearchPO> wrapper) {
        return baseMapper.getContractLedgerReportPageVo(page, wrapper);
    }

    public List<PurchaseDTO> getPurchaseList(BizReceiptContractHeadDTO headDTO) {
        return baseMapper.getPurchaseList(headDTO);
    }
}
