package com.inossem.wms.bizdomain.apply.service.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonComponent;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizbasis.stock.service.biz.StockOccupyService;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockOccupyDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.EnumToolDefaultStorageType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBatchDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockBatch;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockOccupy;
import com.inossem.wms.common.model.stock.key.StockBatchKey;
import com.inossem.wms.common.model.tool.entity.DicToolType;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;

import cn.hutool.core.date.DateUtil;

/**
 * <p>
 * 申请公共 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-06
 */
@Service
public class ApplyCommonComponent {

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected BatchInfoService batchInfoService;

    @Autowired
    protected StockCommonService stockCommonService;

    @Autowired
    protected StockOccupyService stockOccupyService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected StockCommonComponent stockCommonComponent;

    @Autowired
    protected StockOccupyDataWrap stockOccupyDataWrap;

    @Autowired
    protected BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    protected BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;

    @Autowired
    protected BizReceiptApplyItemDataWrap bizReceiptApplyItemDataWrap;

    @Autowired
    protected ApprovalService approvalService;

    /**
     * 查询申请单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptApplySearchPO :"申请单分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (CollectionUtils.isEmpty(currentUser.getLocationList())) {
            return;
        }
        List<Long> locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        po.setLocationIdList(locationIds);
        // 分页查询处理
        IPage<BizReceiptApplyPageVO> page = po.getPageObj(BizReceiptApplyPageVO.class);
        bizReceiptApplyHeadDataWrap.getPageVOList(page, po);

        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 获取物料特性库存
     *
     * @in ctx 入参 {@link BizReceiptApplySearchMatPO : "申请单查询物料库存入参"}
     * @out ctx 出参 {@link SingleResultVO ("matStockDTO":"物料库存信息")}
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptApplySearchMatPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptApplyItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO<>(new MatStockDTO()));
                return;
            }
        }
        Long batchId = null;
        if (UtilString.isNotNullOrEmpty(po.getToolCode())) {
            BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDtoByCode(po.getToolCode());
            if (UtilObject.isNotNull(batchInfoDTO)) {
                batchId = batchInfoDTO.getId();
            }
            if (Objects.isNull(batchId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO<>(new MatStockDTO()));
                return;
            }
        }
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO searchPO = new BizReceiptAssembleRuleSearchPO();
        searchPO.setReceiptType(po.getReceiptType());
        searchPO.setFtyId(dictionaryService.getFtyIdCacheByCode(Const.TOOL_FTY_CODE));
        searchPO.setLocationId(dictionaryService.getLocationIdCacheByCode(Const.TOOL_FTY_CODE, Const.TOOL_LOCATION_CODE));
        searchPO.setMatId(matId);
        searchPO.setBatchId(batchId);
        searchPO.setBinId(po.getBinId());
        searchPO.setOutFtyCode(po.getOutFtyCode());
        searchPO.setMatName(po.getMatName());
        searchPO.setIsNuclearIslandToolRoom(po.getIsNuclearIslandToolRoom());
        searchPO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        searchPO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TOOL.getValue());
        searchPO.setFormatCode(po.getFormatCode());
        if (po.getMaintainInDate() != null && po.getMaintainOutDate() != null) {
            searchPO.setMaintainInDate(po.getMaintainInDate());
            searchPO.setMaintainOutDate(po.getMaintainOutDate());
        }
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getToolStockByFeatureCode(po.getReceiptType(), searchPO);
        List<BizReceiptAssembleDTO> assembleList = assembleRuleDTO.getAssembleDTOList();
        if (UtilCollection.isNotEmpty(assembleList)) {
            // 过滤出库存大于0的配货信息
            assembleList = assembleList.stream().filter(obj -> (obj.getStockQty().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList());
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleList) {
                BizReceiptApplyItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptApplyItemDTO.class);
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                List<String> specValueList = Arrays.asList(assembleDTO.getSpecValue().split(","));
                // 批次信息
                BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(Long.valueOf(specValueList.get(0)));
                itemDTO.setBatchId(batchInfoDTO.getId());
                itemDTO.setToolCode(batchInfoDTO.getBatchCode());
                itemDTO.setToolStatus(batchInfoDTO.getToolStatus());
                itemDTO.setOutFtyCode(batchInfoDTO.getOutFtyCode());
                itemDTO.setToolManageStatusRemark(batchInfoDTO.getToolManageStatusRemark());
                // 工器具类型信息
                DicToolType toolType = dictionaryService.getToolTypeCacheById(batchInfoDTO.getToolTypeId());
                itemDTO.setToolTypeId(toolType.getId());
                itemDTO.setToolTypeName(toolType.getToolTypeName());
                // 存储类型信息
                itemDTO.setTypeId(Long.valueOf(specValueList.get(1)));
                // 仓位信息
                itemDTO.setBinId(Long.valueOf(specValueList.get(2)));
                itemDTO.setBinCode(dictionaryService.getBinCacheById(Long.valueOf(specValueList.get(2))).getBinCode());
                // 特殊库存标识
                itemDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TOOL.getValue());
                itemDTO.setFormatCode(batchInfoDTO.getFormatCode());
                itemDTOList.add(itemDTO);
            }
        }
        // 根据工具类型过滤
        if (UtilNumber.isNotEmpty(po.getToolTypeId())) {
            itemDTOList = itemDTOList.stream().filter(p -> po.getToolTypeId().equals(p.getToolTypeId())).collect(Collectors.toList());
        }
        // 根据业务类型过滤库存
        List<Long> typeIdList = new ArrayList<>();
        if (EnumReceiptType.BORROW_APPLY.getValue().equals(po.getReceiptType())) {
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_NORMAL.getTypeCode()));
        } else if (EnumReceiptType.TOOL_MAINTAIN_OUTPUT.getValue().equals(po.getReceiptType())) {
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_NORMAL.getTypeCode()));
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_OUTPUT.getTypeCode()));
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_DISABLED.getTypeCode()));
        } else if (EnumReceiptType.REPAIR_APPLY.getValue().equals(po.getReceiptType())) {
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_NORMAL.getTypeCode()));
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_REPAIR.getTypeCode()));
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_DISABLED.getTypeCode()));
        } else if (EnumReceiptType.TOOL_SCRAP_APPLY.getValue().equals(po.getReceiptType())) {
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_NORMAL.getTypeCode()));
            typeIdList.add(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_DISABLED.getTypeCode()));
        }
        itemDTOList = itemDTOList.stream().filter(p -> typeIdList.contains(p.getTypeId())).collect(Collectors.toList());
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        matStockDTO.setApplyItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO<>(matStockDTO));
    }

    /**
     * 获取配货信息
     *
     * @in ctx 入参 {@link BizReceiptApplySearchMatPO : "申请单查询物料库存入参"}
     * @out ctx 出参 {@link SingleResultVO ("assembleRuleDTO":"物料库存信息")}
     */
    public void getItemInfoByFeatureStock(BizContext ctx) {
        BizReceiptApplySearchMatPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询特性库存
        List<StockBinDTO> stockBinDTOS = null;
        if (UtilCollection.isNotEmpty(po.getAssembleDTOList())) {
            stockBinDTOS = stockCommonService.fillSpecCodeAndValue(po.getAssembleDTOList());
        }
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO searchPO = new BizReceiptAssembleRuleSearchPO();
        searchPO.setFtyId(po.getFtyId());
        searchPO.setLocationId(po.getLocationId());
        searchPO.setMatId(po.getMatId());
        searchPO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        searchPO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TOOL.getValue());
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getToolStockByFeatureCodeAndValue(stockBinDTOS, po.getReceiptType(), searchPO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO<>(assembleRuleDTO));
    }

    /**
     * 申请单保存校验
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void checkSaveApply(BizContext ctx) {
        // 获取上下文
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            List<String> errorRidList = new ArrayList<>(headDTO.getItemList().size());
            headDTO.getItemList().forEach(itemDTO -> {
                if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    errorRidList.add(itemDTO.getRid());
                }
            });
            if (UtilCollection.isNotEmpty(errorRidList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_OPERATION_QTY_ZERO, errorRidList.toString());
            }
            // 采购退货申请效验
            if(headDTO.getReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN_APPLY.getValue())) {
                // 装载qty已超过可出库数量的行项目
                List<String> errorExceedQtyList = new ArrayList<>();
                headDTO.getItemList().forEach(itemDTO -> {
                    if (itemDTO.getQty().compareTo(itemDTO.getAvailableQty()) > 0) {
                        errorExceedQtyList.add(itemDTO.getRid());
                    }
                });
                if (UtilCollection.isNotEmpty(errorExceedQtyList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEED_OUTPUT_QTY, errorExceedQtyList.toString());
                }

                // 校验行项目采购订单是否相同
                Set<Long> errorPurchaseIdSet = headDTO.getItemList().stream().map(itemDTO -> itemDTO.getPreReceiptHeadId()).collect(Collectors.toSet());
                if(UtilCollection.isNotEmpty(errorPurchaseIdSet)&&errorPurchaseIdSet.size()>1) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_RETURN_PURCHASE_CODE_DIFFERENT);
                }
            }
            if(!headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_MAT_REQ_APPLY.getValue())
                    && !headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_APPLY.getValue())
                    && !headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER_APPLY.getValue())
                    && !headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_TRANSFER_APPLY.getValue())
                    && !headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_OLD_APPLY.getValue())
                    && !headDTO.getReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN_APPLY.getValue())) {
                List<String> occupyToolCodeList = new ArrayList<>(headDTO.getItemList().size());
                List<StockBatchKey> stockBatchKeyList = new ArrayList<>();
                headDTO.getItemList().forEach(p -> {
                    StockBatchKey stockBatchKey = new StockBatchKey(p.getMatId(), p.getBatchId(), p.getFtyId(), p.getLocationId());
                    stockBatchKeyList.add(stockBatchKey);
                });
                List<StockBatchDTO> stockBatchDTOList = stockCommonComponent.getToolStockBatchByInsMoveTypeVo(stockBatchKeyList);
                if(UtilCollection.isNotEmpty(stockBatchDTOList)) {
                    headDTO.getItemList().forEach(p -> {
                        stockBatchDTOList.forEach(q -> {
                            if (p.getFtyId().equals(q.getFtyId()) && p.getLocationId().equals(q.getLocationId())
                                    && p.getMatId().equals(q.getMatId()) && p.getBatchId().equals(q.getBatchId())) {
                                p.setStockBatchId(q.getId());
                            }
                        });
                    });
                    QueryWrapper<StockOccupy> stockOccupyQueryWrapper = new QueryWrapper<>();
                    stockOccupyQueryWrapper.lambda().in(StockOccupy::getStockBatchId, stockBatchDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                            .ne(UtilNumber.isNotEmpty(headDTO.getId()), StockOccupy::getReceiptHeadId, headDTO.getId());
                    List<StockOccupy> stockOccupyList = stockOccupyDataWrap.list(stockOccupyQueryWrapper);
                    headDTO.getItemList().forEach(p -> {
                        stockOccupyList.forEach(q -> {
                            if (p.getStockBatchId().equals(q.getStockBatchId())) {
                                occupyToolCodeList.add(p.getToolCode());
                            }
                        });
                    });
                    if (UtilCollection.isNotEmpty(occupyToolCodeList)) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_OCCUPY_QTY, occupyToolCodeList.toString());
                    }
                }else  {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_QUANTITY_DIFFERENCES);
                }
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 保存申请单
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     * @out ctx 出参 {"receiptCode" : "申请单单号"},{@link BizReceiptApplyHeadDTO : "已保存的申请单}
     */
    public void saveApply(BizContext ctx) {
        // 入参上下文 - 要保存的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        if(UtilNumber.isEmpty(headDTO.getId())){
            headDTO.setCreateUserId(user.getId());
        }
        headDTO.setModifyUserId(user.getId());
        headDTO.setCreateTime(null);
        headDTO.setModifyTime(UtilDate.getNow());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新申请单
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptApplyItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptApplyItem::getHeadId, headDTO.getId());
            bizReceiptApplyItemDataWrap.physicalDelete(wrapperItem);
            // assemble物理删除
            QueryWrapper<BizReceiptAssemble> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizReceiptAssemble::getReceiptHeadId, headDTO.getId());
            bizReceiptAssembleDataWrap.physicalDelete(queryWrapperAssemble);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            Integer receiptType = headDTO.getReceiptType();
            if (EnumReceiptType.TOOL_SCRAP_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TOOL_SCRAP_APPLY.getValue());
            } else if (EnumReceiptType.BORROW_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TOOL_BORROW_APPLY.getValue());
            } else if (EnumReceiptType.REPAIR_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TOOL_REPAIR_APPLY.getValue());
            }else if (EnumReceiptType.LEISURE_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LEISURE_APPLY.getValue());
            }else if (EnumReceiptType.UNITIZED_LEISURE_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_LEISURE_APPLY.getValue());
            } else if (EnumReceiptType.STOCK_RETURN_MAT_REQ_APPLY.getValue().equals(receiptType)) {// 退库申请)
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SWQUENCE_MAT_REQ_RETURN_APPLY.getValue());
            }else if(EnumReceiptType.STOCK_RETURN_TRANSFER_APPLY.getValue().equals(receiptType) ) { //退转库入库申请
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_MAT_REQ_TRANSFER_RETURN_APPLY.getValue());
            }else if(EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER_APPLY.getValue().equals(receiptType)) { //退转库入库申请
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_TRANSFER_RETURN_APPLY.getValue());
            }else if(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SWQUENCE_PURCHASE_RETURN_APPLY.getValue());
            }else if(EnumReceiptType.STOCK_APPLY_SCRAP.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_SCRAP_APPLY.getValue());
            }else if(EnumReceiptType.STOCK_TEMP_MAT_INPUT_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.ITEM_STORAGE_APPLY.getValue());
            }else if(EnumReceiptType.STOCK_RETURN_OLD_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_MAT_REQ_RETURN_OLD_APPLY.getValue());
            }else if(EnumReceiptType.LEISURE_INPUT_APPLY.getValue().equals(receiptType)) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LEISURE_INPUT_APPLY.getValue());
            } else if (EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_APPLY.getValue().equals(receiptType)) {
                receiptCode = "SK" + headDTO.getUnit() + "-CWH5" + DateUtil.thisYear() + "-APS-T" + DateUtil.format(new Date(), "MM") + bizCommonService.getNextSequenceCodeMonth(EnumSequenceCode.SEQUENCE_UNITIZED_MAT_REQ_RETURN_APPLY.getValue());
            }
            headDTO.setId(null);
            headDTO.setReceiptCode(receiptCode);
            bizReceiptApplyHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(String.format("%05d",rid.getAndIncrement()*10));
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(headDTO.getItemList());
        /* ********************** item处理结束 *************************/
        if(!headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_MAT_REQ_APPLY.getValue())
            && !headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_APPLY.getValue())
            && !headDTO.getReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN_APPLY.getValue())
            && !headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_TRANSFER_APPLY.getValue())
            && !headDTO.getReceiptType().equals(EnumReceiptType.STOCK_TEMP_MAT_INPUT_APPLY.getValue())) {
            /* ********************** assemble处理开始 *************************/
            List<BizReceiptAssembleDTO> assembleList = new ArrayList<>();
            for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
                for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                    assembleDTO.setId(null);
                    assembleDTO.setReceiptHeadId(headDTO.getId());
                    assembleDTO.setReceiptItemId(itemDTO.getId());
                    assembleDTO.setReceiptType(headDTO.getReceiptType());
                    assembleDTO.setCreateUserId(user.getId());
                    assembleDTO.setModifyUserId(user.getId());
                    assembleDTO.setSpecType(assembleDTO.getSpecType() == null
                            ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue() : assembleDTO.getSpecType());
                    assembleList.add(assembleDTO);
                }
            }
            bizReceiptAssembleDataWrap.saveBatchDto(assembleList);
            /* ********************** assemble处理开始结束 *************************/
        }
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的申请单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 删除占用库存
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "要删除占用库存的申请单"}
     */
    public void removeOccupyStock(BizContext ctx) {
        // 入参上下文 - 要删除占用库存的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除库存占用
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            stockOccupyService.deleteByReceipt(headDTO.getReceiptType(), headDTO.getId(), null, null);
        }
    }

    /**
     * 占用库存
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "要占用库存的申请单"}
     */
    public void occupyStock(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptAssembleRuleSearchPO assembleRuleSearchPO = new BizReceiptAssembleRuleSearchPO();
        // 筛选库存
        assembleRuleSearchPO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
            assembleDTOList.addAll(itemDTO.getAssembleDTOList());
        }
        assembleRuleSearchPO.setAssembleList(UtilCollection.toList(assembleDTOList, BizReceiptAssemble.class));
        assembleRuleSearchPO.setFeatureCode(assembleDTOList.get(0).getSpecCode());
        assembleRuleSearchPO.setSpecStock(headDTO.getItemList().get(0).getSpecStock());
        stockOccupyService.saveByAssembleDTO(assembleRuleSearchPO);
    }

    /**
     * 更新申请单已完成
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        // 入参上下文 - 要保存的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头已完成
        UpdateWrapper<BizReceiptApplyHead> headUpdateWrapper = new UpdateWrapper<BizReceiptApplyHead>();
        headUpdateWrapper.lambda().set(BizReceiptApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()).eq(BizReceiptApplyHead::getId, headDTO.getId());
        bizReceiptApplyHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目已完成
        UpdateWrapper<BizReceiptApplyItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptApplyItem>();
        itemUpdateWrapper.lambda().set(BizReceiptApplyItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()).eq(BizReceiptApplyItem::getHeadId, headDTO.getId());
        bizReceiptApplyItemDataWrap.update(itemUpdateWrapper);
    }

    /**
     * 更新申请单审批中
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void updateStatusApprove(BizContext ctx) {
        // 入参上下文 - 要保存的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头审批中
        UpdateWrapper<BizReceiptApplyHead> headUpdateWrapper = new UpdateWrapper<BizReceiptApplyHead>();
        headUpdateWrapper.lambda().set(BizReceiptApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue()).eq(BizReceiptApplyHead::getId, headDTO.getId());
        bizReceiptApplyHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目审批中
        UpdateWrapper<BizReceiptApplyItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptApplyItem>();
        itemUpdateWrapper.lambda().set(BizReceiptApplyItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue()).eq(BizReceiptApplyItem::getHeadId, headDTO.getId());
        bizReceiptApplyItemDataWrap.update(itemUpdateWrapper);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    public void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptApplyHead head = new BizReceiptApplyHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptApplyHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptApplyItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptApplyItem::getItemStatus, receiptStatus)
                .eq(BizReceiptApplyItem::getHeadId, id);
        bizReceiptApplyItemDataWrap.update(wrapper);
    }

    /**
     * 更新申请单已驳回
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void updateStatusRejected(BizContext ctx) {
        // 入参上下文 - 要保存的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头已驳回
        UpdateWrapper<BizReceiptApplyHead> headUpdateWrapper = new UpdateWrapper<BizReceiptApplyHead>();
        headUpdateWrapper.lambda().set(BizReceiptApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue()).eq(BizReceiptApplyHead::getId, headDTO.getId());
        bizReceiptApplyHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目已驳回
        UpdateWrapper<BizReceiptApplyItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptApplyItem>();
        itemUpdateWrapper.lambda().set(BizReceiptApplyItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue()).eq(BizReceiptApplyItem::getHeadId, headDTO.getId());
        bizReceiptApplyItemDataWrap.update(itemUpdateWrapper);
    }

    /**
     * 更新申请单已撤销
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void updateStatusRevoke(BizContext ctx) {
        // 入参上下文 - 要保存的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新单据抬头已撤销
        UpdateWrapper<BizReceiptApplyHead> headUpdateWrapper = new UpdateWrapper<BizReceiptApplyHead>();
        headUpdateWrapper.lambda().set(BizReceiptApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_REVOKE.getValue()).eq(BizReceiptApplyHead::getId, headDTO.getId());
        bizReceiptApplyHeadDataWrap.update(headUpdateWrapper);
        // 更新单据行项目已撤销
        UpdateWrapper<BizReceiptApplyItem> itemUpdateWrapper = new UpdateWrapper<BizReceiptApplyItem>();
        itemUpdateWrapper.lambda().set(BizReceiptApplyItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_REVOKE.getValue()).eq(BizReceiptApplyItem::getHeadId, headDTO.getId());
        bizReceiptApplyItemDataWrap.update(itemUpdateWrapper);
    }

    /**
     * 刪除申请单
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "申请单单号"}
     */
    public void deleteInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置申请单
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHeadDataWrap.getById(headId), BizReceiptApplyHeadDTO.class);
        // 逻辑删除抬头表
        bizReceiptApplyHeadDataWrap.removeById(headId);
        // 逻辑删除行项目表
        QueryWrapper<BizReceiptApplyItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptApplyItem::getHeadId, headId);
        bizReceiptApplyItemDataWrap.remove(itemWrapper);
        // 逻辑删除assemble表
        QueryWrapper<BizReceiptAssemble> assembleWrapper = new QueryWrapper<>();
        assembleWrapper.lambda().eq(BizReceiptAssemble::getReceiptHeadId, headId);
        bizReceiptAssembleDataWrap.remove(assembleWrapper);
        // 申请单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 申请单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }


    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);

        boolean wfByReceiptType = enableWfByReceiptType(bizReceiptApplyHead.getReceiptType());

        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 设置单据流
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                    .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "要保持单据流的申请单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文 - 要保持单据流的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptApplyItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "要保持附件的申请单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存申请单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "要保存操作日志的申请单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的申请单
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 删除申请单单据流
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除申请单单据流
        receiptRelationService.deleteReceiptTree(po.getReceiptType(), po.getId());
    }

    /**
     * 删除申请单附件
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(po.getId(), po.getReceiptType());
    }

    /**
     * 获取批次id(用于先过账模式生成凭证)
     *
     * @param assembleDTO 配货特征传输对象
     * @return 批次id
     */
    public Long getBatchId(BizReceiptAssembleDTO assembleDTO) {
        Long batchId = null;
        String stockBatchBatchId = StockBatch.class.getAnnotation(TableName.class).value() + Const.POINT
                + UtilMybatisPlus.getColumnByFunction(StockBatch::getBatchId);
        String stockBinBatchId = StockBin.class.getAnnotation(TableName.class).value() + Const.POINT
                + UtilMybatisPlus.getColumnByFunction(StockBatch::getBatchId);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(stockBatchBatchId) || codeList.get(i).equals(stockBinBatchId)) {
                batchId = Long.valueOf(valueList.get(i));
            }
        }
        return batchId;
    }

    /**
     * 根据单据类型检查是否开启审批
     *
     * @return boolean boolean
     */
    private boolean enableWfByReceiptType(Integer receiptType) {
        //判断单据是否开启审批
        boolean wfByReceiptType = false;
        if (EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue().equals(receiptType)) {
            // 领料申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.STOCK_RETURN_MAT_REQ_APPLY.getValue().equals(receiptType)) {
            // 退库申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.STOCK_APPLY_SCRAP.getValue().equals(receiptType)) {
            // 报废申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.TOOL_SCRAP_APPLY.getValue().equals(receiptType)) {
            // 工器具报废申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.LEISURE_APPLY.getValue().equals(receiptType)) {
            // 闲置物资申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.STOCK_RETURN_TRANSFER_APPLY.getValue().equals(receiptType)) {
            // 退转库申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.STOCK_TEMP_MAT_INPUT_APPLY.getValue().equals(receiptType)) {
            // 暂存物项申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.STOCK_TEMP_MAT_OUTPUT_APPLY.getValue().equals(receiptType)) {
            // 暂存领用申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue().equals(receiptType)) {
            // 成套设备领料申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue().equals(receiptType)) {
            // 成套设备领料申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ_APPLY.getValue().equals(receiptType)) {
            // 退转库领料申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_APPLY.getValue().equals(receiptType)) {
            // 成套设备退库申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT_APPLY.getValue().equals(receiptType)) {
            // 成套设备暂存物项申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_DELAY.getValue().equals(receiptType)) {
            // 成套设备暂存物项延期
            wfByReceiptType = true;
        } else if (EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_OUTPUT_APPLY.getValue().equals(receiptType)) {
            // 成套设备暂存物项领用
            wfByReceiptType = true;
        } else if (EnumReceiptType.UNITIZED_LEISURE_APPLY.getValue().equals(receiptType)) {
            // 成套设备闲置物资申请
            wfByReceiptType = true;
        } else if (EnumReceiptType.STOCK_RETURN_OLD_APPLY.getValue().equals(receiptType)) {
            // 成套设备退旧物资申请
            wfByReceiptType = true;
        }
        return wfByReceiptType;
    }

}
