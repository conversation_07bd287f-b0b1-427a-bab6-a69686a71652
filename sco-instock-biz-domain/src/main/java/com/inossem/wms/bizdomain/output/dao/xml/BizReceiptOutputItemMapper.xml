<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.output.dao.BizReceiptOutputItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem">
        <id column="id" property="id"/>
        <result column="head_id" property="headId"/>
        <result column="rid" property="rid"/>
        <result column="fty_id" property="ftyId"/>
        <result column="mat_id" property="matId"/>
        <result column="location_id" property="locationId"/>
        <result column="wh_id" property="whId"/>
        <result column="qty" property="qty"/>
        <result column="task_qty" property="taskQty"/>
        <result column="return_qty" property="returnQty"/>
        <result column="doc_date" property="docDate"/>
        <result column="posting_date" property="postingDate"/>
        <result column="unit_id" property="unitId"/>
        <result column="pre_receipt_head_id" property="preReceiptHeadId"/>
        <result column="pre_receipt_item_id" property="preReceiptItemId"/>
        <result column="pre_receipt_type" property="preReceiptType"/>
        <result column="refer_receipt_head_id" property="referReceiptHeadId"/>
        <result column="refer_receipt_item_id" property="referReceiptItemId"/>
        <result column="refer_receipt_type" property="referReceiptType"/>
        <result column="move_type_id" property="moveTypeId"/>
        <result column="mat_doc_code" property="matDocCode"/>
        <result column="mat_doc_rid" property="matDocRid"/>
        <result column="mat_doc_year" property="matDocYear"/>
        <result column="item_status" property="itemStatus"/>
        <result column="is_write_off" property="isWriteOff"/>
        <result column="write_off_mat_doc_code" property="writeOffMatDocCode"/>
        <result column="write_off_doc_date" property="writeOffDocDate"/>
        <result column="write_off_posting_date" property="writeOffPostingDate"/>
        <result column="write_off_mat_doc_rid" property="writeOffMatDocRid"/>
        <result column="write_off_mat_doc_year" property="writeOffMatDocYear"/>
        <result column="item_remark" property="itemRemark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="modify_user_id" property="modifyUserId"/>
    </resultMap>

    <select id="selectOutputReceiptItemList"
            resultType="com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem">
        select
            roi.id, roi.head_id, roi.rid, roi.fty_id, roi.mat_id, roi.location_id, roi.wh_id, roi.qty, roi.return_qty,
            roi.return_old_qty, roi.task_qty,roi.input_other_qty,
            roi.doc_date, roi.posting_date, roi.unit_id, roi.pre_receipt_head_id, roi.pre_receipt_item_id,
            roi.pre_receipt_type, roi.pre_receipt_qty, roi.refer_receipt_head_id, roi.refer_receipt_item_id,
            roi.refer_receipt_type, roi.move_type_id, roi.mat_doc_code, roi.mat_doc_rid, roi.mat_doc_year,
            roi.item_status, roi.is_write_off, roi.write_off_mat_doc_code, roi.write_off_doc_date,
            roi.write_off_posting_date, roi.write_off_mat_doc_rid, roi.write_off_mat_doc_year, roi.item_remark,
            roi.is_delete, roi.create_time, roi.modify_time, roi.create_user_id, roi.modify_user_id, roi.spec_stock, roi.spec_stock_code
        from biz_receipt_output_item roi
        inner join biz_receipt_output_head roh on roi.head_id = roh.id
        <where>
            roi.is_delete = 0 and roi.is_write_off = 0 and roh.receipt_status = 90 and roi.qty > (roi.return_qty + roi.return_old_qty)
            <if test="preReceiptCode != null and preReceiptCode != '' ">
                AND roh.receipt_code = #{preReceiptCode}
            </if>
            <if test="startTime != null and endTime != null">
                AND DATE(roh.create_time)
                BETWEEN #{startTime, jdbcType=TIMESTAMP} AND DATE_ADD(#{endTime, jdbcType=TIMESTAMP},INTERVAL
                1 DAY)
            </if>
            <if test="outputReceiptTypeList != null and outputReceiptTypeList.size()>0">
                AND (roh.receipt_type IN
                <foreach collection="outputReceiptTypeList" open="(" separator="," close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="locationIdList != null and locationIdList.size()>0">
                AND (roi.location_id IN
                <foreach collection="locationIdList" open="(" separator="," close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="matId != null and matId != 0">
                AND roi.mat_id = #{matId}
            </if>
        </where>
    </select>

    <select id="getOutputItemListByUnitized"
            resultType="com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO">
        select rob.id,
               rob.head_id,
               roh.receipt_code,
               roh.receipt_type,
               rob.bid rid,
               rab.fty_id,
               rab.location_id,
               dsl.wh_id,
               rob.qty,
               rob.return_qty,
               rob.return_old_qty,
               rob.task_qty,
               roi.doc_date,
               roi.posting_date,
               dm.unit_id,
               roi.pre_receipt_head_id,
               roi.pre_receipt_item_id,
               roi.pre_receipt_type,
               roi.pre_receipt_qty,
               roi.refer_receipt_head_id,
               roi.refer_receipt_item_id,
               roi.refer_receipt_type,
               roi.move_type_id,
               rob.mat_doc_code,
               rob.mat_doc_rid,
               rob.mat_doc_year,
               roi.item_status,
               rob.is_write_off,
               rob.write_off_mat_doc_code,
               rob.write_off_doc_date,
               roi.write_off_posting_date,
               rob.write_off_mat_doc_rid,
               rob.write_off_mat_doc_year,
               roi.item_remark,
               roh.des head_remark,
               rob.is_delete,
               rob.create_time,
               rob.modify_time,
               rob.create_user_id,
               rob.modify_user_id,
               bbi.spec_stock,
               bbi.spec_stock_code,
               rob.batch_id,
               rob.mat_id
        from biz_receipt_output_head roh
            join biz_receipt_output_item roi on roi.head_id = roh.id
            join biz_receipt_apply_bin rab on rab.head_id = roi.pre_receipt_head_id
            join biz_receipt_output_bin rob on rob.apply_bin_id = rab.id
            join biz_batch_info bbi on bbi.id = rob.batch_id
            left join dic_stock_location dsl on dsl.id = rab.location_id
            left join dic_material dm on dm.id = rob.mat_id
            left join dic_material parent_dm on parent_dm.id = dm.parent_mat_id
        where roh.is_delete = 0
            and rob.is_delete = 0
            and rob.is_write_off = 0
            and roh.receipt_status = 90
            and rob.qty > (rob.return_qty + rob.return_old_qty)
            and roi.pre_receipt_type = 1077
        <if test="preReceiptCode != null and preReceiptCode != '' ">
            AND roh.receipt_code = #{preReceiptCode}
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE(roh.create_time)
            BETWEEN #{startTime, jdbcType=TIMESTAMP} AND DATE_ADD(#{endTime, jdbcType=TIMESTAMP},INTERVAL
            1 DAY)
        </if>
        <if test="outputReceiptTypeList != null and outputReceiptTypeList.size() > 0">
            AND (roh.receipt_type IN
            <foreach collection="outputReceiptTypeList" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="locationIdList != null and locationIdList.size() > 0">
            AND (rab.location_id IN
            <foreach collection="locationIdList" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="parentMatCode != null and parentMatCode != '' ">
            AND parent_dm.mat_code = #{parentMatCode}
        </if>
        <if test="parentMatName != null and parentMatName != ''">
            AND parent_dm.mat_name LIKE concat( '%',#{parentMatName}, '%')
        </if>
        <if test="matCode != null and matCode != '' ">
            AND dm.mat_code = #{matCode}
        </if>
        <if test="matName != null and matName != ''">
            AND dm.mat_name LIKE concat( '%',#{matName}, '%')
        </if>
        <if test="functionalLocationCode != null and functionalLocationCode != '' ">
            AND bbi.functional_location_code = #{functionalLocationCode}
        </if>
        <if test="extend29 != null and extend29 != '' ">
            AND bbi.extend29 = #{extend29}
        </if>
        <if test="extend20 != null and extend20 != '' ">
            AND bbi.extend20 = #{extend20}
        </if>
        <if test="extend24 != null and extend24 != '' ">
            AND bbi.extend24 LIKE concat( '%',#{extend24}, '%')
        </if>
        group by rob.id

        union all

        select roi.id,
               roi.head_id,
               roh.receipt_code,
               roh.receipt_type,
               roi.rid,
               roi.fty_id,
               roi.location_id,
               roi.wh_id,
               SUM(rob.qty) qty,
               SUM(rob.return_qty) return_qty,
               SUM(rob.return_old_qty) return_old_qty,
               SUM(rob.task_qty) task_qty,
               roi.doc_date,
               roi.posting_date,
               dm.unit_id,
               roi.pre_receipt_head_id,
               roi.pre_receipt_item_id,
               roi.pre_receipt_type,
               roi.pre_receipt_qty,
               roi.refer_receipt_head_id,
               roi.refer_receipt_item_id,
               roi.refer_receipt_type,
               roi.move_type_id,
               roi.mat_doc_code,
               roi.mat_doc_rid,
               roi.mat_doc_year,
               roi.item_status,
               roi.is_write_off,
               roi.write_off_mat_doc_code,
               roi.write_off_doc_date,
               roi.write_off_posting_date,
               roi.write_off_mat_doc_rid,
               roi.write_off_mat_doc_year,
               roi.item_remark,
               roh.des head_remark,
               roi.is_delete,
               roi.create_time,
               roi.modify_time,
               roi.create_user_id,
               roi.modify_user_id,
               roi.spec_stock,
               roi.spec_stock_code,
               rob.batch_id,
               rob.mat_id
        from biz_receipt_output_head roh
            join biz_receipt_output_item roi on roi.head_id = roh.id
            join biz_receipt_output_bin rob on rob.item_id = roi.id
            left join biz_batch_info bbi on bbi.id = rob.batch_id
            left join dic_material dm on dm.id = rob.mat_id
            left join dic_material parent_dm on parent_dm.id = dm.parent_mat_id
        where roi.is_delete = 0
            and roh.is_delete = 0
            and roi.is_write_off = 0
            and roh.receipt_status = 90
            and roi.location_id != 0
        <if test="preReceiptCode != null and preReceiptCode != '' ">
            AND roh.receipt_code = #{preReceiptCode}
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE(roh.create_time)
            BETWEEN #{startTime, jdbcType=TIMESTAMP} AND DATE_ADD(#{endTime, jdbcType=TIMESTAMP},INTERVAL
            1 DAY)
        </if>
        <if test="outputReceiptTypeList != null and outputReceiptTypeList.size() > 0">
            AND (roh.receipt_type IN
            <foreach collection="outputReceiptTypeList" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="locationIdList != null and locationIdList.size() > 0">
            AND (roi.location_id IN
            <foreach collection="locationIdList" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="parentMatCode != null and parentMatCode != '' ">
            AND parent_dm.mat_code = #{parentMatCode}
        </if>
        <if test="parentMatName != null and parentMatName != ''">
            AND parent_dm.mat_name LIKE concat( '%',#{parentMatName}, '%')
        </if>
        <if test="matCode != null and matCode != '' ">
            AND dm.mat_code = #{matCode}
        </if>
        <if test="matName != null and matName != ''">
            AND dm.mat_name LIKE concat( '%',#{matName}, '%')
        </if>
        <if test="functionalLocationCode != null and functionalLocationCode != '' ">
            AND bbi.functional_location_code = #{functionalLocationCode}
        </if>
        <if test="extend29 != null and extend29 != '' ">
            AND bbi.extend29 = #{extend29}
        </if>
        <if test="extend20 != null and extend20 != '' ">
            AND bbi.extend20 = #{extend20}
        </if>
        <if test="extend24 != null and extend24 != '' ">
            AND bbi.extend24 LIKE concat( '%',#{extend24}, '%')
        </if>
        group by roi.id, rob.mat_id
        having SUM(rob.qty) > (SUM(rob.return_qty) + SUM(rob.return_old_qty))
    </select>

    <select id="getOutputItemListByBorrowInput" resultType="com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO">
        SELECT
        broi.*,
        broh.receipt_code AS output_receipt_code,
        brob.batch_id,
        info.borrow_user_id
        FROM biz_receipt_output_item broi
        LEFT JOIN biz_receipt_output_head broh ON broi.head_id = broh.id AND broh.is_delete = 0
        LEFT JOIN biz_receipt_output_bin brob ON broi.id = brob.item_id AND brob.is_delete = 0
        LEFT JOIN biz_receipt_apply_head brah ON broi.refer_receipt_head_id = brah.id AND brah.is_delete = 0
        LEFT JOIN biz_batch_info info ON brob.batch_id = info.id
        WHERE broi.is_delete = 0
        AND broi.item_status = 90
        AND brah.borrow_type = 1
        <if test="po.outputReceiptCode != null and po.outputReceiptCode != ''">
            AND broh.receipt_code = #{po.outputReceiptCode}
        </if>
        <if test="po.borrowUserId != null and po.borrowUserId != ''">
            AND info.borrow_user_id = #{po.borrowUserId}
        </if>
        GROUP BY broi.id
    </select>


    <select id="getOutputItemListByRepairInput" resultType="com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO">
        SELECT
        broi.*,brai.bin_id,
        brob.batch_id, case when broi.id = inputItem.pre_receipt_item_id then 1 else 0 end as repairStatus
        FROM biz_receipt_output_item broi
        LEFT JOIN biz_receipt_output_head broh ON broi.head_id = broh.id AND broh.is_delete = 0
        LEFT JOIN biz_receipt_output_bin brob ON broi.id = brob.item_id AND brob.is_delete = 0
        LEFT JOIN biz_receipt_apply_item brai ON brai.id = broi.pre_receipt_item_id
        LEFT JOIN biz_receipt_input_item inputItem ON inputItem.pre_receipt_item_id = broi.id  AND inputItem.is_delete = 0
        WHERE broi.is_delete = 0
        AND broi.item_status = 90 and broh.receipt_type = #{po.preReceiptType}
        <if test="po.outputReceiptCode != null and po.outputReceiptCode != ''">
            AND broh.receipt_code = #{po.outputReceiptCode}
        </if>
        GROUP BY broi.id
        order by broi.create_time desc
    </select>

    <select id="getMatreqOutputItemListByInput" resultType="com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO">
        SELECT
        broi.*,
        brob.batch_id, case when broi.id = inputItem.pre_receipt_item_id then 1 else 0 end as repairStatus
        FROM biz_receipt_output_item broi
        LEFT JOIN biz_receipt_output_head broh ON broi.head_id = broh.id AND broh.is_delete = 0
        LEFT JOIN biz_receipt_output_bin brob ON broi.id = brob.item_id AND brob.is_delete = 0
        LEFT JOIN biz_receipt_input_item inputItem ON inputItem.pre_receipt_item_id = broi.id  AND inputItem.is_delete = 0
        WHERE broi.is_delete = 0 and broi.is_return = 1
        AND broi.item_status = 90 and broh.receipt_type = #{po.preReceiptType}
        <if test="po.outputReceiptCode != null and po.outputReceiptCode != ''">
            AND broh.receipt_code = #{po.outputReceiptCode}
        </if>
        GROUP BY broi.id
        order by broi.create_time desc
    </select>
    <select id="selectAllOutBatchList" resultType="com.inossem.wms.common.model.batch.entity.BizBatchInfo">
        select *  from stock_batch sb left join biz_batch_info bbi on sb.batch_id = bbi.id
        where
        <foreach collection="syncList" item ="item" separator=" or ">
            (sb.batch_id = #{item.binDTOList[0].batchId} and sb.qty = #{item.qty})
        </foreach>
    </select>
    <select id="getHeadMaterial" resultType="java.lang.String">
        select mat_code from dic_material dm where
        id in (
        select parent_mat_id from dic_material dm where dm.id in
        <foreach collection="syncList" open="(" separator="," close=")" item="item">
            #{item.matId}
        </foreach>
            )
        limit 1
    </select>
    <select id="getHeadMaterialByMatId" resultType="java.lang.String">
        select mat_code from dic_material where id in (select parent_mat_id from dic_material dm where id = #{matId})
    </select>
    <select id="selectExportItemList"
            resultType="com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputOilsExportVO">
        select
            biz_receipt_output_head.receipt_code,
            sys_user.user_name                   as create_user_name,
            biz_receipt_output_head.create_time,
            biz_receipt_output_head.out_time,
            biz_receipt_output_head.remark,
            biz_receipt_output_item.mat_id,
            dic_material.unit_id,
            biz_receipt_output_item.qty,
            biz_receipt_output_item.stock_qty,
            biz_receipt_output_item.remain_stock_qty,
            biz_receipt_output_item.fty_id,
            biz_receipt_output_item.location_id,
            biz_receipt_output_item.posting_date as finishInputTime,
            biz_receipt_output_head.receipt_status
        from biz_receipt_output_head
        inner join biz_receipt_output_item ON biz_receipt_output_head.id = biz_receipt_output_item.head_id
            AND biz_receipt_output_head.is_delete = 0
            AND biz_receipt_output_item.is_delete = 0
        inner join sys_user ON biz_receipt_output_head.create_user_id = sys_user.id
        LEFT JOIN biz_receipt_apply_head ah ON ah.id = biz_receipt_output_item.pre_receipt_head_id
        LEFT JOIN dic_material ON biz_receipt_output_item.mat_id = dic_material.id
            AND sys_user.is_delete = 0
        ${ew.customSqlSegment}
        order by biz_receipt_output_head.create_time
        desc
    </select>

    <update id="updateQtyByAutoDistribution" parameterType="java.util.List">
        update biz_receipt_output_item t
        inner join (
        <foreach collection="list" item="item" index="index" separator="UNION ALL" >
            select #{item.id} id,
            #{item.qty} qty
        </foreach>
        ) t1 on t.id = t1.id
        set t.qty = t1.qty
    </update>

    <update id="updateStatusAndQtyById">
        update biz_receipt_output_item set item_status=#{status},task_qty=#{taskQty},finish_qty=#{finishQty} where id=#{id}
    </update>
</mapper>
