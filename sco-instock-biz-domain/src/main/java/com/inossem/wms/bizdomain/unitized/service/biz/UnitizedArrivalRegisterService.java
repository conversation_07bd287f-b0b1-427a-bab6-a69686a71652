package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.unitized.service.component.UnitizedArrivalRegisterComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 成套设备到货登记 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-25
 */
@Service
public class UnitizedArrivalRegisterService {

    @Autowired
    protected UnitizedArrivalRegisterComponent unitizedArrivalRegisterComponent;

    /**
     * 查询车辆类型下拉
     *
     * @return 车辆类型下拉框
     */
    public void getCarTypeDown(BizContext ctx) {

        // 查询车辆类型下拉
        unitizedArrivalRegisterComponent.getCarTypeDown(ctx);

    }

    /**
     * 查询车辆下拉
     *
     * @return 车辆下拉框
     */
    public void getCarDown(BizContext ctx) {

        // 查询车辆下拉
        unitizedArrivalRegisterComponent.getCarDown(ctx);

    }

    /**
     * 查询箱件状态下拉
     *
     * @return 箱件状态下拉框
     */
    public void getVisualCheckDown(BizContext ctx) {

        // 查询箱件状态下拉
        unitizedArrivalRegisterComponent.getVisualCheckDown(ctx);

    }
    /**
     * 查询检查结果下拉
     *
     * @return 检查结果下拉框
     */
    public void getCheckResultDown(BizContext ctx) {

        // 查询检查结果下拉
        unitizedArrivalRegisterComponent.getCheckResultDown(ctx);

    }

    /**
     * 查询检查结果下拉
     *
     * @return 检查结果下拉框
     */
    public void getCheckRejectResultDown(BizContext ctx) {

        // 查询检查结果下拉
        unitizedArrivalRegisterComponent.getCheckRejectResultDown(ctx);

    }


    /**
     * 查询到货登记单列表-分页
     *
     * @param ctx-po 登记单分页查询入参
     * @return 到货登记单列表
     */
    public void getPage(BizContext ctx) {

        // 查询到货登记单列表-分页
        unitizedArrivalRegisterComponent.setPage(ctx);

    }

    /**
     * 查询到货登记单详情
     *
     * @param ctx-id 到货登记单抬头表主键
     * @return 到货登记单详情
     */
    public void getInfo(BizContext ctx) {

        // 到货登记单详情
        unitizedArrivalRegisterComponent.getInfo(ctx);

        // 设置详情页单据流
        unitizedArrivalRegisterComponent.setInfoExtendRelation(ctx);

        // 开启附件
        unitizedArrivalRegisterComponent.setExtendAttachment(ctx);

        // 开启操作日志
        unitizedArrivalRegisterComponent.setExtendOperationLog(ctx);

    }

    /**
     * 到货登记-保存
     *
     * @param ctx-po 保存到货登记表单参数
     * @return ctx-receiptCode 到货登记单单号
     */
    @WmsMQListener(tags = TagConst.GEN_UNITIZED_ARRIVAL_REGISTER_STOCK)
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 登记单保存校验
        unitizedArrivalRegisterComponent.checkSaveRegister(ctx);

        // 保存登记单
        unitizedArrivalRegisterComponent.saveApply(ctx);

        // 保存箱件图片
        unitizedArrivalRegisterComponent.saveBizCasesImg(ctx);

        // 保存附件
        unitizedArrivalRegisterComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        unitizedArrivalRegisterComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 到货登记-同步DTS
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncDTS(BizContext ctx) {
        // 同步DTS
        unitizedArrivalRegisterComponent.syncDTS(ctx);
    }


    /**
     * 到货登记-提交
     *
     * @param ctx-po 提交到货登记表单参数
     * @return ctx-receiptCode 到货登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 登记单保存校验
        unitizedArrivalRegisterComponent.checkSaveRegister(ctx);

        // 提交到货登记单
        unitizedArrivalRegisterComponent.submitLoseRegister(ctx);

        // 保存箱件图片
        unitizedArrivalRegisterComponent.saveBizCasesImg(ctx);

        // 保存附件
        unitizedArrivalRegisterComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        unitizedArrivalRegisterComponent.saveBizReceiptOperationLog(ctx);

        // SAP过账
        unitizedArrivalRegisterComponent.postToSap(ctx);

        // 单据状态已完成
        unitizedArrivalRegisterComponent.updateStatusCompleted(ctx);

        // 生成分配质检单
        unitizedArrivalRegisterComponent.genDistributeInspect(ctx);

        // 同步DTS
        unitizedArrivalRegisterComponent.syncDTS(ctx);

    }

    /**
     * 到货登记-过账
     *
     * @param ctx-po 提交到货登记表单参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {

        // SAP过账
        unitizedArrivalRegisterComponent.postToSap(ctx);

        // 单据状态已完成
        unitizedArrivalRegisterComponent.updateStatusCompleted(ctx);

        // 生成分配质检单
        unitizedArrivalRegisterComponent.genDistributeInspect(ctx);

    }

    /**
     * 到货登记-删除
     *
     * @param ctx-id 到货登记单抬头表主键
     * @return ctx-receiptCode 到货登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 刪除登记单
        unitizedArrivalRegisterComponent.deleteInfo(ctx);

        // 删除登记单单据流
        unitizedArrivalRegisterComponent.deleteReceiptTree(ctx);

        // 删除登记单附件
        unitizedArrivalRegisterComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        unitizedArrivalRegisterComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 到货登记-冲销
     *
     * @param ctx-po 冲销入参
     */
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 到货登记单冲销校验
        unitizedArrivalRegisterComponent.checkWriteOffRegister(ctx);

        // SAP冲销
        unitizedArrivalRegisterComponent.writeOffToSap(ctx);

        // 保存冲销日志
        unitizedArrivalRegisterComponent.saveBizReceiptWriteOffOperationLog(ctx);

    }

}
