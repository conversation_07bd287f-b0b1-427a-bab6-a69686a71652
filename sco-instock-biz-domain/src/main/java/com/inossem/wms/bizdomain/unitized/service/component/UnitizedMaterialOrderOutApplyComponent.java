package com.inossem.wms.bizdomain.unitized.service.component;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpWbsService;
import com.inossem.wms.bizbasis.erp.service.biz.ReserveReceiptService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockOccupyDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockOverDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyBinDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyTransferDataWrap;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputInfoDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.component.TransferComponent;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportRuleDataWrap;
import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedMaterialOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumDeptTypes;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyBinDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyTransferDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyBin;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyTransfer;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplyQueryListPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputInfoDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputInfo;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.output.po.BizMatApplyLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.require.entity.BizReceiptRequireHead;
import com.inossem.wms.common.model.bizdomain.require.entity.BizReceiptRequireItem;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportRule;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.entity.SapWbs;
import com.inossem.wms.common.model.erp.po.ReserveReceiptCreatePO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySearchPO;
import com.inossem.wms.common.model.masterdata.mat.fty.vo.DicMaterialFactoryPageVO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.corp.entity.DicCorp;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptRegisterBox;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.entity.StockOccupy;
import com.inossem.wms.common.model.stock.entity.StockOver;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPo;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wang
 * <p>领料申请单组件库</p>
 * @date 2022/5/7 14:28
 */
@Service
@Slf4j
public class UnitizedMaterialOrderOutApplyComponent {

    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private BizReceiptApplyItemDataWrap bizReceiptApplyItemDataWrap;
    @Autowired
    private BizReceiptApplyBinDataWrap bizReceiptApplyBinDataWrap;
    @Autowired
    private BizReceiptOutputInfoDataWrap bizReceiptOutputInfoDataWrap;
    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;
    @Autowired
    private OutputComponent outputComponent;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private BatchImgService batchImgService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private ReserveReceiptService reserveReceiptService;
    @Autowired
    private UnitizedMaterialOutputService unitizedMaterialOutputService;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    private DicMaterialDataWrap dicMaterialDataWrap;
    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private LabelReceiptRelService labelReceiptRelService;
    @Autowired
    protected SapInterfaceService sapInterfaceService;
    @Autowired
    private BizReceiptTransportRuleDataWrap bizReceiptTransportRuleDataWrap;
    @Autowired
    private BizReceiptApplyTransferDataWrap bizReceiptApplyTransferDataWrap;
    @Autowired
    private TransferComponent transferComponent;
    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;
    @Autowired
    private ErpWbsService erpWbsService;
    @Autowired
    private DicMaterialFactoryDataWrap dicMaterialFactoryDataWrap;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;
    @Autowired
    private StockOccupyDataWrap stockOccupyDataWrap;
    @Autowired
    private StockOverDataWrap stockOverDataWrap;

    //仓储承包商：取用户“李欣宇”所属部门
    private static final String CONTRACTOR_USER_NAME = "李欣宇";

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSubmit(true).setButtonSave(Boolean.TRUE);
        BizResultVO<BizReceiptApplyHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptApplyHeadDTO()
                        .setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue())
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserName(user.getUserName()),
                new ExtendVO(), buttonVO);
        if (UtilCollection.isNotEmpty(user.getUserDeptList())) {
            resultVO.getHead().setCreateDeptId(user.getUserDeptList().get(0).getDeptId());
            resultVO.getHead().setCreateOfficeId(user.getUserDeptList().get(0).getDeptOfficeId());
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 获取领料单出库详情
     *
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        BizReceiptApplyHeadDTO applyHeadDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        // 属性填充
        dataFillService.fillAttr(applyHeadDTO);
        Long ftyId = applyHeadDTO.getItemList().get(0).getFtyId();
        if (UtilNumber.isEmpty(ftyId)) {
            ftyId = applyHeadDTO.getBinList().get(0).getFtyId();
        }
        DicFactoryDTO ftyCache = dictionaryService.getFtyCacheById(ftyId);
        DicCorp corpCache = dictionaryService.getCorpCacheById(ftyCache.getCorpId());
        applyHeadDTO.setCorpName(corpCache==null? null : corpCache.getCorpName());
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        itemDTOList.stream().forEach(bizReceiptApplyItemDTO -> {
            if (bizReceiptApplyItemDTO.getIsReturnFlag()==0){
                bizReceiptApplyItemDTO.setIsReturnFlagStr("否");
            }else {
                bizReceiptApplyItemDTO.setIsReturnFlagStr("是");
            }
            if(UtilNumber.isNotEmpty(bizReceiptApplyItemDTO.getPreCreateUserId())){
                bizReceiptApplyItemDTO.setPreCreateUserName(sysUserDataWrap.getById(bizReceiptApplyItemDTO.getPreCreateUserId()).getUserName());
            }
        });
        if (UtilCollection.isNotEmpty(applyHeadDTO.getBinList())) {
            for (BizReceiptApplyBinDTO binDTO : applyHeadDTO.getBinList()) {
                // 主设备编码
                if (UtilString.isNullOrEmpty(binDTO.getParentMatCode())) {
                    binDTO.setParentMatCode(dictionaryService.getMatCacheById(binDTO.getParentMatId()).getMatCode());
                }
            }
        }
        // 设置运单总价
        applyHeadDTO.getItemList().forEach(p -> {
            List<BizReceiptAssembleDTO> assembleDTOList = p.getAssembleDTOList();
            if (!CollectionUtils.isEmpty(assembleDTOList)) {
                assembleDTOList.forEach(q -> q.setAmount(q.getPrice().multiply(q.getQty()).add(q.getRemainder())));
            }
            List<BizReceiptApplyTransferDTO> transferDTOList = p.getTransferDTOList();
            if (!CollectionUtils.isEmpty(transferDTOList)) {
                transferDTOList.forEach(q -> q.setAmount(q.getPrice().multiply(q.getQty()).add(q.getRemainder())));
            }
        });
        //仓储承包商：取用户“李欣宇”所属部门
        String contractorName = this.getContractorName();
        applyHeadDTO.setContractorName(contractorName);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(applyHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(applyHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 仓储承包商：取用户“李欣宇”所属部门
     * @return 取用户“李欣宇”所属部门
     */
    private String getContractorName(){
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUser::getUserName,CONTRACTOR_USER_NAME);
        List<SysUser> userList = sysUserDataWrap.list(queryWrapper);
        if (UtilCollection.isEmpty(userList)){
            return null;
        }
        List<MetaDataDeptOfficePO> userDeptList = sysUserDeptOfficeRelDataWrap.getUserDept(userList.get(0).getId());
        if (UtilCollection.isEmpty(userDeptList)){
            return null;
        }
        return userDeptList.get(0).getDeptName();
    }


    /**
     * 获取成套领料申请单申请分页
     *
     * @param ctx
     */
    public void gePage(BizContext ctx) {
        BizReceiptApplyQueryListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(StringUtils.isNotEmpty(po.getChildMatCode())){
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            po.setMatId(matId);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            if (UtilNumber.isNotEmpty(po.getReceiptType()) && po.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue())) {
                // 根据需求创建的领用申请 没有库存地点
                locationIdList = null;
            }
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptApplyQueryListPO> wmsQueryWrapper = new WmsQueryWrapper<>();
        wmsQueryWrapper.lambda()
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getReceiptType, BizReceiptApplyHead.class, po.getReceiptType())
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getIsDelete, BizReceiptApplyHead.class, 0)
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptApplyQueryListPO::getReceiptStatus, BizReceiptApplyHead.class, po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptApplyQueryListPO::getLocationId, BizReceiptApplyItem.class,locationIdList)
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptApplyQueryListPO::getReceiptCode, BizReceiptApplyHead.class, po.getReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getMatReceiver()), BizReceiptApplyQueryListPO::getMatReceiver, BizReceiptOutputInfo.class, po.getMatReceiver())
                .eq(UtilString.isNotNullOrEmpty(po.getMatDept()), BizReceiptApplyQueryListPO::getMatDept, BizReceiptOutputInfo.class, po.getMatDept())
                .eq(UtilString.isNotNullOrEmpty(po.getChildMatCode()), BizReceiptApplyQueryListPO::getMatCode, DicMaterial.class, po.getChildMatCode())
                .eq(UtilNumber.isNotNull(po.getMatId()), BizReceiptApplyQueryListPO::getParentMatId, DicMaterial.class, po.getMatId())
                .between(UtilObject.isNotNull(po.getApplyStartTime()) && UtilObject.isNotNull(po.getApplyEndTime()),
                        BizReceiptApplyQueryListPO::getApplyTime, BizReceiptOutputInfo.class, po.getApplyStartTime(), po.getApplyEndTime())
                .between(UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime()),
                        BizReceiptApplyQueryListPO::getCreateTime, BizReceiptApplyHead.class, po.getStartTime(), po.getEndTime())
                .like(UtilString.isNotNullOrEmpty(po.getUserName()), BizReceiptApplyQueryListPO::getUserName, SysUser.class, po.getUserName())
                .eq(UtilNumber.isNotEmpty(po.getIsExact()), BizReceiptApplyQueryListPO::getIsExact, BizReceiptApplyHead.class, po.getIsExact())
                .eq(UtilNumber.isNotEmpty(po.getIsSplit()), BizReceiptApplyQueryListPO::getIsSplit, BizReceiptApplyHead.class, po.getIsSplit())
                .eq(UtilString.isNotNullOrEmpty(po.getIsland()), BizReceiptApplyQueryListPO::getIsland, BizReceiptApplyHead.class, po.getIsland())
                .like(UtilString.isNotNullOrEmpty(po.getRemark()), BizReceiptApplyQueryListPO::getRemark, BizReceiptApplyHead.class, po.getRemark())
                .eq(UtilString.isNotNullOrEmpty(po.getMatnr()), BizReceiptApplyQueryListPO::getMatnr, BizReceiptRequireItem.class, po.getMatnr())
                .eq(UtilString.isNotNullOrEmpty(po.getFuncNo()), BizReceiptApplyQueryListPO::getFuncNo, BizReceiptRequireItem.class, po.getFuncNo())
                .eq(UtilString.isNotNullOrEmpty(po.getPreApplyReceiptCode()), BizReceiptApplyQueryListPO::getPreApplyReceiptCode, po.getPreApplyReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getPreReceiptCode()), BizReceiptApplyQueryListPO::getReceiptCode, BizReceiptRequireHead.class, po.getPreReceiptCode())
                // 拆分单据判断 : 前续单据bin表id 不是0
                .ne(UtilNumber.isNotEmpty(po.getIsAllReadySplit()), BizReceiptApplyQueryListPO::getPreApplyBinId, BizReceiptApplyBin.class, 0)
                 ;
        Page<BizReceiptApplyPageVO> pageObj = (Page<BizReceiptApplyPageVO>) po.getPageObj(BizReceiptApplyPageVO.class);
        pageObj.setOptimizeCountSql(false);
        pageObj.orders().forEach(obj -> {
            // 排序特殊处理
            obj.setColumn(obj.getColumn().replace("pre_apply_receipt_code", "pre.receipt_code"));
            obj.setColumn(obj.getColumn().replace("create_dept_name", "biz_receipt_apply_head.create_dept_id"));
        });
        bizReceiptApplyHeadDataWrap.getApplyInfoPageVOListUnitized(pageObj, wmsQueryWrapper);
        dataFillService.fillAttr(pageObj.getRecords());
        long totalCount = pageObj.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(pageObj.getRecords(), totalCount));
    }


    /**
     * 保存领料申请单信息
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBillInfo(BizContext ctx) {
        boolean isNew = true;
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        CurrentUser user = ctx.getCurrentUser();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        String receiptCode = applyHeadDTO.getReceiptCode();
        String applyCode = applyHeadDTO.getReceiptNum() == null ? null : applyHeadDTO.getReceiptNum();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        // 处理头信息
        if (UtilNumber.isNotEmpty(applyHeadDTO.getId())) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(applyHeadDTO.getId());
            // 根据id更新
            bizReceiptApplyHeadDataWrap.updateDtoById(applyHeadDTO);

            // item物理删除
            QueryWrapper<BizReceiptApplyItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(BizReceiptApplyItem::getHeadId, applyHeadDTO.getId());
            bizReceiptApplyItemDataWrap.physicalDelete(itemQueryWrapper);

            // assemble物理删除
            QueryWrapper<BizReceiptAssemble> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizReceiptAssemble::getReceiptHeadId, applyHeadDTO.getId());
            bizReceiptAssembleDataWrap.physicalDelete(queryWrapperAssemble);

            // outInfo 根据id、headId更新
            QueryWrapper<BizReceiptOutputInfo> outInfoWrapper = new QueryWrapper<>();
            outInfoWrapper.lambda()
                    .eq(BizReceiptOutputInfo::getId, applyHeadDTO.getOutInfoId());
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .build();
            bizReceiptOutputInfoDataWrap.updateDto(bizReceiptOutputInfoDTO, outInfoWrapper);

            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            /* ================ outInfo处理  领料申请单信息 ================ */
//            applyCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_APPLY_CODE.getValue());
            // SKX-CWH5XXXX-APS-ZZZZ
            applyCode = "SK" + applyHeadDTO.getUnit() + "-CWH5" + UtilDate.getYearMonth(new Date()) + "-APS-" + bizCommonService.getNextSequence("sequence_apply_code_hl");
            // 根据24位的WBS Code 获取 8位的WBS编号 8位的WBS Code是在创建预留单的时候使用
            applyHeadDTO.getSpecStockCode();
            WmsQueryWrapper<ErpPurchaseReceiptItem> purchaseReceiptItemWmsQueryWrapper = new WmsQueryWrapper<>();
            purchaseReceiptItemWmsQueryWrapper.lambda()
                    .eq(ErpPurchaseReceiptItem::getSpecStockCode, applyHeadDTO.getSpecStockCode());
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(purchaseReceiptItemWmsQueryWrapper);
            if (erpPurchaseReceiptItemList.size() <= 0){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_WBS_CODE_GET_FAILED);
            }
            ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemList.get(0);
//            ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(purchaseReceiptItemWmsQueryWrapper);
            if (UtilString.isNullOrEmpty(erpPurchaseReceiptItem.getWhCodeOut())) {
                log.error("保存领料申请申请单根据24位的WBS Code，从表erp_purchase_receipt_item获取8位的WBS编码失败;保存的领料申请信息为:{}", JSON.toJSONString(applyHeadDTO));
            }
            applyHeadDTO.setWhCodeOut(erpPurchaseReceiptItem.getWhCodeOut());

//            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = applyHeadDTO.getBizReceiptOutputInfoDTO();
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
                    .specStockCode(applyHeadDTO.getSpecStockCode())
                    .specStockName(applyHeadDTO.getSpecStockName())
                    .whCodeOut(applyHeadDTO.getWhCodeOut())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .createTime(UtilDate.getNow())
                    .modifyTime(UtilDate.getNow())
                    .createUserId(user.getCreateUserId())
                    .modifyUserId(user.getModifyUserId())
                    .build();
            bizReceiptOutputInfoDataWrap.saveDto(bizReceiptOutputInfoDTO);
            /* ================ head处理 ================ */
//            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
            receiptCode = bizCommonService.getNextSeqMaterialReq(user,EnumSequenceCode.MATERIAL_REQ.getValue(),applyHeadDTO.getItemList().get(EnumRealYn.FALSE.getIntValue()).getFtyCode());
            applyHeadDTO.setReceiptCode(receiptCode);
            applyHeadDTO.setCreateUserId(createUserId);
            applyHeadDTO.setModifyUserId(createUserId);
            applyHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
            applyHeadDTO.setReceiptStatus(status);
            applyHeadDTO.setOutInfoId(bizReceiptOutputInfoDTO.getId());
            bizReceiptApplyHeadDataWrap.saveDto(applyHeadDTO);

            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }

        // item处理
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            itemDTO.setId(null);
            itemDTO.setHeadId(applyHeadDTO.getId());
            itemDTO.setItemStatus(status);
            itemDTO.setCreateTime(createTime);
            itemDTO.setCreateUserId(createUserId);
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setMatId(dictionaryService.getMatIdByMatCode(itemDTO.getMatCode()));
            itemDTO.setWhId(dictionaryService.getLocationCacheById(itemDTO.getLocationId()).getWhId());
            itemDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);

        // assemble处理
        List<BizReceiptAssembleDTO> assembleList = new ArrayList<>();
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                assembleDTO.setId(null);
                assembleDTO.setReceiptHeadId(applyHeadDTO.getId());
                assembleDTO.setReceiptItemId(itemDto.getId());
                assembleDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
                assembleDTO.setCreateUserId(createUserId);
                assembleDTO.setModifyUserId(user.getId());
                assembleDTO.setSpecType(assembleDTO.getSpecType() == null ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue() : assembleDTO.getSpecType());
                if(UtilNumber.isEmpty(assembleDTO.getUnitizedBatchId())) {
                    assembleDTO.setUnitizedBatchId(assembleDTO.getBatchId());
                }
                assembleList.add(assembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleList);
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, applyHeadDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, applyHeadDTO.getId());
    }

    /**
     * 提交成套领料申请单申请
     *
     * @param ctx
     */
    public void submitReceiptApply(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        this.saveBillInfo(ctx);
    }


    /**
     * 状态变更已提交
     *
     * @param ctx
     */
    public void updateStatusSubmitted(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
    }

    /**
     * 领料申请单审批操作
     *
     * @param ctx
     */
    public void doOutputInstance(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        // 构建创建预留单的参数
        ReserveReceiptCreatePO reserveReceiptCreatePO = ReserveReceiptCreatePO.builder().bizReceiptApplyHeadDTO(headDTO).build();
        // 调用SAP创建预留单
        // TODO-BO 2022/5/24 应该参考创建物料凭证进行改造一下。。。。

        //创建预留，修改po中物料编码和数量
        List<BizReceiptApplyItemDTO> itemList = reserveReceiptCreatePO.getBizReceiptApplyHeadDTO().getItemList();

        //构建可用的matCode和物料金额
        itemList.forEach(u->{
            //将金额和数量对调用于过账
            BigDecimal qty = u.getQty();
            u.setQty(u.getAmount());
            u.setAmount(qty);
            //创建预留得时候，不同步sap使用了此rid
            u.setReservedOrderRid(u.getRid());
        });
        ReserveReceiptCreatePO sapCreateReserveReceiptInfo = reserveReceiptService.createReserveReceiptItemList(reserveReceiptCreatePO, currentUser);
        // SAP创建预留返回信息
        // 创建预留成功
//        if (sapCreateReserveReceiptInfo.getStatus().equals(Const.ERP_RETURN_TYPE_S)) {
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(sapCreateReserveReceiptInfo.getSuccess())) {
//            sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList().forEach(bizReceiptApplyItemDTO -> {
//                bizReceiptApplyItemDTO.setAssembleDTOList(new ArrayList<>());
//            });
            //过账，将数量和金额调换回来
            sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList().forEach(u->{
                //金额和数量回调用于后续处理
                BigDecimal qty = u.getQty();
                u.setQty(u.getAmount());
                u.setAmount(qty);
            });
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO());
            BizReceiptApplyHeadDTO receiptApplyHeadDTO = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO();
            List<BizReceiptApplyItemDTO> receiptApplyItemDTOList = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList();
            // 更新数据 将预留单信息 填充当申请表行项目中
            this.saveBillInfo(ctx);
            // 更新状态 已完成
            this.updateStatus(receiptApplyHeadDTO, receiptApplyItemDTOList, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

            /* ================================ 自动创建领料申请单 ================================ */
            List<BizReceiptOutputItemDTO> receiptOutputItemDTOList = new ArrayList<>();
            BizReceiptApplyHeadDTO bizReceiptApplyHeadDTO = reserveReceiptCreatePO.getBizReceiptApplyHeadDTO();
            List<BizReceiptApplyItemDTO> applyHeadDTOItemList = bizReceiptApplyHeadDTO.getItemList();
            // 领料申请单是基于领料申请单进行创建，进行属性填充
            applyHeadDTOItemList.stream().forEach(bizReceiptApplyItemDTO -> {
                BizReceiptOutputItemDTO receiptOutputItemDTO = BizReceiptOutputItemDTO.builder()
                        // 这里没有配置领料申请的行项目的rid
                        .matId(bizReceiptApplyItemDTO.getMatId())
                        .ftyId(bizReceiptApplyItemDTO.getFtyId())
                        .ftyCode(bizReceiptApplyItemDTO.getFtyCode())
                        .locationId(bizReceiptApplyItemDTO.getLocationId())
                        .whId(bizReceiptApplyItemDTO.getWhId())
                        .preReceiptQty(bizReceiptApplyItemDTO.getQty())
                        .receiptQty(bizReceiptApplyItemDTO.getQty())
                        //操作数量，单价，金额
                        .qty(bizReceiptApplyItemDTO.getQty())
                        .price(bizReceiptApplyItemDTO.getPrice())
                        .amount(bizReceiptApplyItemDTO.getAmount())
                        // 已退库数量
                        .returnQty(BigDecimal.ZERO)
                        // 已作业数量
                        .taskQty(BigDecimal.ZERO)
                        // 已完成数量
                        .finishQty(BigDecimal.ZERO)
                        // 特殊库存标识
                        .specStock(bizReceiptApplyItemDTO.getSpecStock())
                        .unitId(bizReceiptApplyItemDTO.getUnitId())
                        // 前续单据head id
                        .preReceiptHeadId(bizReceiptApplyItemDTO.getHeadId())
                        // 前序单据item表id
                        .preReceiptItemId(bizReceiptApplyItemDTO.getId())
                        // 前序单据类型
                        .preReceiptType(bizReceiptApplyHeadDTO.getReceiptType())
                        .referReceiptHeadId(bizReceiptApplyItemDTO.getPreReceiptHeadId())
                        .referReceiptItemId(bizReceiptApplyItemDTO.getPreReceiptItemId())
                        .referReceiptCode(bizReceiptApplyItemDTO.getPreReceiptCode())
                        .referReceiptType(EnumReceiptType.RESERVE_RECEIPT.getValue())
                        // 行项目状态
                        .itemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                        .createTime(UtilDate.getNow())
                        .createUserId(bizReceiptApplyItemDTO.getCreateUserId())
                        .specStockCode(bizReceiptApplyItemDTO.getSpecStockCode())
                        .assembleDTOList(bizReceiptApplyItemDTO.getAssembleDTOList())
                        .build();
                receiptOutputItemDTOList.add(receiptOutputItemDTO);
            });
            BizReceiptOutputHeadDTO receiptOutputHeadDTO = BizReceiptOutputHeadDTO.builder()
                    .itemDTOList(receiptOutputItemDTOList)
                    // 配置单据类型
                    .receiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                    .build();

            // 将领料申请信息保存到上下文中
            log.info("领料申请单保存的数据:{}", JSON.toJSONString(receiptOutputHeadDTO));
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptOutputHeadDTO);
            // TODO-BO: 2022/5/17
            //  1.保存领料申请单  这里是不是要判断是否存在 存在则更新，此时是一直追加的状态,
            //  2.创建预留成功需要自动创建领料申请单 这里需要避免失败，打印log 重试
            unitizedMaterialOutputService.save(ctx);
            /* ================================ 自动创建领料申请单 ================================ */
            // 出库配货策略
            outputComponent.autoDistribution(ctx);

        } else {
            BizReceiptApplyHeadDTO receiptApplyHeadDTO = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO();
            List<BizReceiptApplyItemDTO> receiptApplyItemDTOList = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList();
            // 更新状态 未同步
            this.updateStatus(receiptApplyHeadDTO, receiptApplyItemDTOList, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            log.warn("预留单{}同步SAP失败，返回信息：{}", headDTO.getId(), reserveReceiptCreatePO.getReturnMessage());
            throw new WmsException(EnumReturnMsg.CREATE_RESERVE_RECEIPT_FAIL, reserveReceiptCreatePO.getReturnMessage());
        }
    }

    /**
     * 开启审批流
     *
     * @param ctx
     */
    private void startProcessInstance(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Map<String, Object> variables = new HashMap<>();
        Long ftyId=headDTO.getItemList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        //启动工作流
        workflowService.startWorkFlow(headDTO.getId(), headDTO.getReceiptCode(), headDTO.getReceiptType(), variables);
    }

    /**
     * 生成下游领料申请单
     *
     * @param ctx
     */
    private void createReceiptOutput(BizContext ctx) {
    }

    /**
     * 更新单据，行项目状态 如果不更细单据状态，headDTO参数传null
     *
     * @param headDTO
     * @param itemDTOList
     * @param status
     */
    public void updateStatus(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        if (Objects.isNull(headDTO)) {
            this.updateAppLyReceiptItemStatus(itemDTOList, status);
        } else if (CollectionUtils.isEmpty(itemDTOList)) {
            this.updateApplyReceiptHeadStatus(headDTO, status);
        } else if (!CollectionUtils.isEmpty(itemDTOList)) {
            this.updateAppLyReceiptItemStatus(itemDTOList, status);
            this.updateApplyReceiptHeadStatus(headDTO, status);
        }
    }

    /**
     * 更新单据状态-抬头状态
     *
     * @param headDTO
     * @param status
     */
    private void updateApplyReceiptHeadStatus(BizReceiptApplyHeadDTO headDTO, Integer status) {
        if (Objects.nonNull(headDTO)) {
            // 单据状态
            headDTO.setReceiptStatus(status);
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 更新行项目状态
     *
     * @param itemDTOList
     * @param status
     */
    private void updateAppLyReceiptItemStatus(List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach(bizReceiptApplyItemDTO -> bizReceiptApplyItemDTO.setItemStatus(status));
            bizReceiptApplyItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }


    /**
     * 按钮组
     *
     * @param headDTO 申请单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 完成 -【打印】
            // return buttonVO.setButtonPrint(true);
        }if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            return buttonVO.setButtonSynchronized(true);
        }
        return buttonVO;
    }

    /**
     * 成套领料申请单申请回调
     *
     * @param wfReceiptCo
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取成套领料申请单申请信息
        BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        // 数据类型转换DTO
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptApplyHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);
        // 审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 封装上下文
            BizContext ctxInspect = new BizContext();
            ctxInspect.setCurrentUser(wfReceiptCo.getInitiator());
            ctxInspect.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            // TODO-BO: 2022/5/11 1.自动调用SAP创建预留单
            // TODO-BO: 2022/5/11 2.判断创建预留单是否成功

        }
    }

    /**
     * 删除成套领料申请单申请
     * @param ctx
     */

    /**
     * 删除单据【非同时模式】
     *
     * @param ctx 上下文
     */
    public void deleteReceipt(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptApplyHeadDTO headDTO = this.getItemListByIdNoDataFill(id);
        // 删除head
        bizReceiptApplyHeadDataWrap.removeById(id);
        // 删除item
        bizReceiptApplyItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptApplyItem>() {

            {
                eq(BizReceiptApplyItem::getHeadId, id);
            }
        });
        // 删除assemble
        bizReceiptAssembleDataWrap.remove(new LambdaQueryWrapper<BizReceiptAssemble>() {

            {
                eq(BizReceiptAssemble::getReceiptHeadId, id);
            }
        });
        // 删除out_info
        bizReceiptOutputInfoDataWrap.removeById(headDTO.getOutInfoId());
        receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "", user.getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }


    /**
     * 根据headId查询出库单列表(不填充)
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptApplyHeadDTO getItemListByIdNoDataFill(Long headId) {
        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        List<BizReceiptApplyItem> itemList = bizReceiptApplyItemDataWrap.list(new LambdaQueryWrapper<BizReceiptApplyItem>() {
            {
                eq(BizReceiptApplyItem::getHeadId, headId);
            }
        });

        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        List<BizReceiptApplyItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptApplyItemDTO.class);
        headDTO.setItemList(itemDTOList);
        return headDTO;
    }

    /**
     * 校验行项目的参数
     *
     * @param ctx
     */
    public void checkSave(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(headDTO) || UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 处理WBS编码  Comparator.nullsLast()
        ArrayList<BizReceiptApplyItemDTO> distinctWBSCode = headDTO.getItemList().stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(BizReceiptApplyItemDTO::getSpecStockCode))), ArrayList::new));
        // 判断工厂是否唯一
        ArrayList<BizReceiptApplyItemDTO> distinctFty = headDTO.getItemList().stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(BizReceiptApplyItemDTO::getFtyId))), ArrayList::new));
        if (distinctFty.size()>1){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FTY_DUPLICATE);
        }
        // 将行项目中的24位 WBS Code 、Name配置到抬头中
        headDTO.setSpecStockCode(distinctWBSCode.get(0).getSpecStockCode());
        headDTO.setSpecStockName(distinctWBSCode.get(0).getSpecStockName());
        // 必填参数校验 、领料人、
        if (UtilNumber.isEmpty(headDTO.getMatReceiverId())) {
            throw new WmsException("领料人不能为空");
        }
        // 工厂、库存地点、物料编码没有加校验

    }

    /**
     * 获取配货信息(特性库存)【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getItemInfoByFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询特性库存
        BizReceiptAssembleRuleDTO assembleRuleDTO = new BizReceiptAssembleRuleDTO();
        String specStock = Const.STRING_EMPTY;
        // 查询特性code和特性值库存
        if (isQueryFetureCodeAndValue(po)) {
            assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValue(po, null, po.getReceiptType(),
                    po.getFtyId(), po.getLocationId(), po.getMatId(),
                    EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), specStock);
        } else if (isQueryFetureCode(po)) {
            // 领料申请 领料申请出库设置specStock为“Q”
            if (EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue().equals(po.getReceiptType()) ||
                    EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue().equals(po.getReceiptType())) {
                specStock = EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue();
            }
            // 非限制库存
            Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
            // 根据单据类型获取特性
            List<StockBinDTO> stockBinDTOS = null;
            if (UtilCollection.isNotEmpty(po.getAssembleDTOList())) {
                stockBinDTOS = stockCommonService.fillSpecCodeAndValue(po.getAssembleDTOList());
            }
            BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
            pos.setStockStatus(stockStatus);
            pos.setSpecStock(specStock);
            assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(), pos);
        }
        // 若其他行项目无配货则直接返回
        List<BizReceiptOutputItemDTO> itemDTOList = po.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, assembleRuleDTO);
            return;
        }
        List<BizReceiptAssembleDTO> stockQtyList = assembleRuleDTO.getAssembleDTOList();
        // 已配货的物料idList
        List<Long> matIdList = itemDTOList.stream().map(BizReceiptOutputItemDTO::getMatId).collect(Collectors.toList());
        // 同一个物料重新计算数量
        if (matIdList.contains(po.getMatId())) {
            // 保存特性值与此特性值已操作数量
            Map<String, BigDecimal> totalMap = new HashMap<>();
            // 物料工厂库存地点 粗略分组,筛选出物料相同可能特性值不同的list
            Map<String, List<BizReceiptOutputItemDTO>> matMap =
                    itemDTOList.stream().collect(Collectors.groupingBy(a -> getItemInfoGroupKey(po)));
            List<BizReceiptOutputItemDTO> bizReceiptOutputItemDTOS = matMap.get(this.getItemInfoGroupKey(po));
            for (BizReceiptOutputItemDTO bizReceiptOutputItemDTO : bizReceiptOutputItemDTOS) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : bizReceiptOutputItemDTO.getAssembleDTOList()) {
                    BigDecimal qty = bizReceiptAssembleDTO.getQty();
                    if (totalMap.containsKey(bizReceiptAssembleDTO.getSpecValue())) {
                        qty = qty.add(totalMap.get(bizReceiptAssembleDTO.getSpecValue()));
                    }
                    totalMap.put(bizReceiptAssembleDTO.getSpecValue(), qty);
                }
            }
            // 重新赋值库存数量
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : stockQtyList) {
                // 已操作数量
                BigDecimal qty = totalMap.get(bizReceiptAssembleDTO.getSpecValue());
                if (Objects.nonNull(qty)) {
                    BigDecimal stockQty = bizReceiptAssembleDTO.getStockQty().subtract(qty);
                    stockQty = stockQty.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : stockQty;
                    bizReceiptAssembleDTO.setStockQty(stockQty);
                }
            }
        }
        assembleRuleDTO.setAssembleDTOList(stockQtyList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, assembleRuleDTO);
    }


    /**
     * 是否查询特性code和特性值库存
     *
     * @param po 库存特性查询PO
     * @return 是否
     */
    public boolean isQueryFetureCodeAndValue(BizReceiptOutputSearchPO po) {
        Set<Integer> set = new HashSet<>();
        set.add(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_SALE.getValue());
        return set.contains(po.getReceiptType());
    }

    /**
     * 是否查询特性code
     *
     * @param po 库存特性查询PO
     * @return 是否
     */
    public boolean isQueryFetureCode(BizReceiptOutputSearchPO po) {
        Set<Integer> set = new HashSet<>();
        set.add(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_WASTE_MAT.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_WORTHLESS.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_TEMP.getValue());
        set.add(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
        set.add(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue());
        return set.contains(po.getReceiptType());
    }


    /**
     * 获取配货分组条件
     *
     * @param po 查询库存入参
     * @return 分组key
     */
    private String getItemInfoGroupKey(BizReceiptOutputSearchPO po) {
        return String.valueOf(po.getMatId()) + po.getFtyId() + po.getLocationId();
    }


    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        Long mMatId = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilString.isNotNullOrEmpty(po.getMMatCode())) {
            mMatId = dictionaryService.getMatIdByMatCode(po.getMMatCode());
            if (Objects.isNull(mMatId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        String specStock = Const.STRING_EMPTY;
        // 领料申请 领料申请出库设置specStock为“Q”
        if (EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue().equals(po.getReceiptType()) ||
                EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue().equals(po.getReceiptType())) {
            specStock = EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue();
        }
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
        // 根据头部物料id，查询实际对应子物料ids，回填到过滤中查询批次特性
        QueryWrapper<DicMaterial> dicMaterialQueryWrapper = new QueryWrapper<>();
        dicMaterialQueryWrapper.lambda().eq(DicMaterial::getParentMatId, matId).select(DicMaterial::getId);
        List<DicMaterial> sonMatList = dicMaterialDataWrap.list(dicMaterialQueryWrapper);
        Set<Long> sonMatIdList = sonMatList.stream().map(DicMaterial::getId).collect(Collectors.toSet());
        if(UtilCollection.isEmpty(sonMatIdList)){
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
            return ;
        }
        pos.setMatId(mMatId);
        pos.setMatIdSet(sonMatIdList);
        pos.setStockStatus(stockStatus);
        pos.setSpecStock(specStock);
        if (UtilString.isNullOrEmpty(pos.getSpecStockCode())){
            pos.setSpecStockCode(null);
        }
        pos.setIsApplyFlag(po.getIsApplyFlag());
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(), pos);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
            this.setBatchImg(itemDTOList);
        }

        //计算行项目金额
        itemDTOList.forEach(u->{
            u.setPrice(UtilNumber.isNotEmpty(u.getBatchInfo().getPrice())?u.getBatchInfo().getPrice():BigDecimal.ZERO);
            u.setAmount(u.getStockQty().multiply(u.getPrice()).add(u.getBatchInfo().getRemainder()));
        });

        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        dataFillService.fillRlatAttrDataList(itemDTOList);
        matStockDTO.setItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * 获取按钮权限
     *
     * @param headId 单据id
     * @return 按钮设置
     */
    public ButtonVO setButton(Long headId) {
        ButtonVO button = new ButtonVO();
        QueryWrapper<BizReceiptOutputHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptOutputHead::getId, headId).eq(BizReceiptOutputHead::getIsDelete, 0);
        BizReceiptOutputHead one = bizReceiptOutputHeadDataWrap.getOne(queryWrapper);
        button.setButtonSave(this.setButtonSave(headId, one));
        button.setButtonDelete(this.setButtonDelete(headId, one));
        button.setButtonSubmit(this.setButtonSubmit(headId, one));
        button.setButtonPost(this.setButtonPost(headId, one));
        button.setButtonWriteOff(this.setButtonWriteOff(headId, one));
        button.setButtonRevoke(this.setButtonRevoke(headId, one));
        button.setButtonDebtOffset(this.setButtonDebtOffset(headId, one));
        return button;
    }


    /**
     * 设置批次图片信息
     *
     * @param itemDTOList 行项目
     */
    public void setBatchImg(List<BizReceiptOutputItemDTO> itemDTOList) {
        Set<Long> matIdSet = itemDTOList.stream().map(BizReceiptOutputItemDTO::getMatId).collect(Collectors.toSet());
        if (UtilCollection.isNotEmpty(matIdSet)) {
            Map<Long, List<BizBatchImgDTO>> batchImgMap = batchImgService.getBatchImgListByMatIdList(matIdSet, 4);
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                List<BizBatchImgDTO> bizBatchImgDTOS = batchImgMap.get(itemDTO.getMatId());
                itemDTO.setBatchImgList(bizBatchImgDTOS);
            }
        }
    }


    /**
     * 出库单能否保存
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonSave(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 出库单能否删除
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonDelete(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 驳回状态 可以删除
            return true;
        }
        return false;
    }

    /**
     * 出库单能否提交 0否、1是
     *
     * @param headId 单据id
     * @return 出库单能否提交 0否、1是
     */
    public Boolean setButtonSubmit(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 出库单能否未同步再次过账 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonPost(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步状态
            return true;
        }
        // 已作业状态
        return EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus);
    }


    /**
     * 出库单能否显示冲销按钮 0否、1是
     *
     * @param headId 单据id
     * @return 出库单能否冲销 0否、1是
     */
    public Boolean setButtonWriteOff(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        // 单据当前状态
        Integer receiptStatus = one.getReceiptStatus();
        QueryWrapper<BizReceiptOutputItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptOutputItem::getHeadId, headId);
        List<BizReceiptOutputItem> itemList = bizReceiptOutputItemDataWrap.list(queryWrapper);
        // 未同步状态和已完成
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 如果存在任意一个行项目未冲销，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptOutputItem item : itemList) {
                if (EnumRealYn.FALSE.getIntValue().equals(item.getIsWriteOff())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            // 已记账
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
            // 作业中
            // 如果存在任意一个行项目已过账，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptOutputItem item : itemList) {
                if (EnumRealYn.TRUE.getIntValue().equals(item.getIsPost())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        }
        return false;
    }

    /**
     * 出库单能否撤销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonRevoke(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus)) {
            // 待核销状态
            return true;
        }
        return false;
    }

    /**
     * 出库单能否核销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonDebtOffset(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus)) {
            // 待核销状态
            return true;
        }
        return false;
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "不符合项处置单"}
     */
    public void startWorkFlow(BizContext ctx) {
        // 发起流程审批
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(headDTO);

        // 审批校验
        Integer receiptType = this.approveCheckNew(ctx);

        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();

        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        Long ftyId=headDTO.getItemList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        // 用户所属部门、对口部门、对口科室
        variables.put("userDept", userDept);
        String counterpartDeptCode = headDTO.getCounterpartDeptCode();
        String counterpartOfficeCode = headDTO.getCounterpartOfficeCode();
        variables.put("counterpartDeptCode", counterpartDeptCode);
        variables.put("counterpartOfficeCode", counterpartOfficeCode);
        // 专业工程师
        variables.put("professionalEngineerUserCode", headDTO.getProfessionalEngineerUserCode());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getReceiptRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新领料申请单 - 审批中 - 审批回调方法 : APPROVAL_CALLBACK_UNITIZED_MATERIAL_OUT_APPLY
        updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }


    /**
     * 审批校验
     *
     * @param ctx
     * @return
     */
    private Integer approveCheck(BizContext ctx) {
        Integer receiptType = EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_SAME.getValue();
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        Integer isInner = headDTO.getIsInnerFlag();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();
        List<String> level3UserList = new ArrayList<>();
        List<String> level4UserList = new ArrayList<>();
        List<String> level5UserList = new ArrayList<>();
        List<String> userListPMD0 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.PMD, EnumOffice.PMD04, EnumApprovalLevel.LEVEL_2);
        List<String> userListPMD = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.PMD.getCode(), null, EnumApprovalLevel.LEVEL_4);
        String deptType = userDepartment.get(0).getDeptType();
        if (EnumDeptTypes.INSIDE.getValue().equals(deptType)) {
            for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
                // 查询用户所属部门的三级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
                level1UserList.addAll(userList);
            }
            for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
                // 查询用户所属部门的四级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
                level2UserList.addAll(userList);
            }
            // 内部单位
            if (EnumRealYn.FALSE.getIntValue().equals(isInner)) {
                // 内部单位跨部门领用
                String counterpartDeptCode = headDTO.getCounterpartDeptCode();
                String counterpartOfficeCode = headDTO.getCounterpartOfficeCode();
                // 查询对口部门、对口科室3级审批人
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_3);
                level3UserList.addAll(userList);
                level4UserList.addAll(userListPMD0);
                level5UserList.addAll(userListPMD);
                if (UtilCollection.isEmpty(level5UserList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "5");
                }
                receiptType = EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_CROSS.getValue();
            } else {
                level3UserList.addAll(userListPMD0);
                level4UserList.addAll(userListPMD);
            }
        } else if (EnumDeptTypes.OUTSIDE.getValue().equals(deptType)) {
            // 外部单位
            for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
                // 查询用户所属部门的三级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
                level1UserList.addAll(userList);
            }
            String counterpartDeptCode = headDTO.getCounterpartDeptCode();
            String counterpartOfficeCode = headDTO.getCounterpartOfficeCode();
            // 查询对口部门、对口科室1级审批人
            level2UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_1));
            // 查询对口部门3级审批人
            level3UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3));
            // 查询对口部门4级审批人
            level4UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_4));
            List<String> level6UserList = new ArrayList<>();
            level5UserList.addAll(userListPMD0);
            level6UserList.addAll(userListPMD);
            if (UtilCollection.isEmpty(level5UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "5");
            }
            if (UtilCollection.isEmpty(level6UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "6");
            }
            receiptType = EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_OUTSIDE.getValue();
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
        if (UtilCollection.isEmpty(level3UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
        }
        if (UtilCollection.isEmpty(level4UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "4");
        }
        return receiptType;
    }



    /**
     * 领料申请单打印校验
     *
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizMatApplyLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptApplyHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);

    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptApplyHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptApplyHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        headDTO.getItemList().stream().forEach(obj -> {
            obj.setPreReceiptCode(obj.getPreReceiptCode());
            obj.setPreReceiptRid(obj.getPreReceiptRid());
        });
        return headDTO;
    }

    /**
     * 填充打印数据
     *
     * @param ctx
     */
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizMatApplyLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptApplyHeadDTO headDTO = po.getHeadDTO();
        // 新建领料申请打印实体对象
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        List<LabelReceiptRegisterBox> outputBoxList = new ArrayList<>();
        itemDTOList.forEach(itemDTO->{
            LabelReceiptRegisterBox labelReceiptRegisterBox = UtilBean.newInstance(itemDTO, LabelReceiptRegisterBox.class);
            outputBoxList.add(labelReceiptRegisterBox);
        });
        // 填充打印信息
        printInfo.setLabelBoxList(outputBoxList);
        if (UtilCollection.isNotEmpty(outputBoxList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,outputBoxList);
        }
    }

    /**
     * 获取物料特性库存【非同时模式】（合并转性功能)
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock4Transfer(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
        Long mMatId = null;
        Set<Long> sonMatIdList = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
            // 根据头部物料id，查询实际对应子物料ids，回填到过滤中查询批次特性
            QueryWrapper<DicMaterial> dicMaterialQueryWrapper = new QueryWrapper<>();
            dicMaterialQueryWrapper.lambda().eq(DicMaterial::getParentMatId, matId).select(DicMaterial::getId);
            List<DicMaterial> sonMatList = dicMaterialDataWrap.list(dicMaterialQueryWrapper);
            sonMatIdList = sonMatList.stream().map(DicMaterial::getId).collect(Collectors.toSet());
            if(UtilCollection.isEmpty(sonMatIdList)){
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return ;
            }
        }
        if (UtilString.isNotNullOrEmpty(po.getMMatCode())) {
            mMatId = dictionaryService.getMatIdByMatCode(po.getMMatCode());
            if (Objects.isNull(mMatId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        // 领料申请 领料申请出库设置specStock为“Q”
        String specStock = EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue();
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
        pos.setMatId(mMatId);
        pos.setMatIdSet(sonMatIdList);
        pos.setStockStatus(stockStatus);
        pos.setSpecStock(specStock);
        if (UtilString.isNullOrEmpty(pos.getSpecStockCode())) {
            pos.setSpecStockCode(null);
        }
        pos.setIsApplyFlag(po.getIsApplyFlag());
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValueBySdw4Unitized(null, null, po.getReceiptType(), pos);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
            this.setBatchImg(itemDTOList);
        }
        //计算行项目金额
        itemDTOList.forEach(u->{
            u.setPrice(UtilNumber.isNotEmpty(u.getBatchInfo().getPrice())?u.getBatchInfo().getPrice():BigDecimal.ZERO);
            u.setAmount(u.getStockQty().multiply(u.getPrice()).add(u.getBatchInfo().getRemainder()));
        });
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        dataFillService.fillRlatAttrDataList(itemDTOList);
        matStockDTO.setItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * 领料申请单审批完成和过账(含转性)
     *
     * @param ctx
     */
    public void doOutputInstanceNew(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        boolean completeFlag = true;
        Integer trueFlag = EnumRealYn.TRUE.getIntValue();
        Integer falseFlag = EnumRealYn.FALSE.getIntValue();
        String receiptCode = headDTO.getReceiptCode();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        Integer receiptType = headDTO.getReceiptType();
        Long headId = headDTO.getId();
        Map<String, AtomicInteger> ridReservedMap = new HashMap<>();
        // 逐个行项目进行过账和转性，如果某行项目有问题，不影响其他行项目进行过账和转性
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            List<BizReceiptApplyTransferDTO> assembleDTOList = itemDTO.getTransferDTOList();
            for (BizReceiptApplyTransferDTO assembleDTO : assembleDTOList) {
                // 过滤掉已转性和已预留行项目
                Integer transferStatus = assembleDTO.getIsTransfer();
                Integer reservedStatus = assembleDTO.getIsReserved();
                if (transferStatus == trueFlag && reservedStatus == trueFlag) {
                    continue;
                }
                // 过滤行项目非主部件
                BigDecimal price = assembleDTO.getPrice();
                BigDecimal qty = assembleDTO.getQty();
                int transferFlag = assembleDTO.getTransferFlag();
                // 校验行项目是否需要转性，如果需要转性则先转性再创建预留，否则直接创建预留
                if ((transferFlag == trueFlag) && (transferStatus == falseFlag) && qty.compareTo(BigDecimal.ZERO) > 0) {
                    // 创建转性
                    dataFillService.fillAttr(assembleDTO);
                    // 生成ins凭证 - 转储接收发出一次过账
                    Date postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
                    StockInsMoveTypeDTO insMoveTypeDTO = generateInsDocToPost(headDTO, itemDTO, assembleDTO, ctx.getCurrentUser().getId());
                    try {
                        // 过账前校验和数量计算
                        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
                        if ((price == null) || price.compareTo(BigDecimal.ZERO) <= 0) {
                            Date docDate = UtilDate.getNow();
                            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                                insDocBatch.setPostingDate(docDate);
                                insDocBatch.setDocDate(docDate);
                            }
                            stockCommonService.modifyStock(insMoveTypeDTO);
                            assembleDTO.setIsTransfer(trueFlag);
                            assembleDTO.setIsReserved(trueFlag);
                            bizReceiptApplyTransferDataWrap.updateById(assembleDTO);
                            continue;
                        }
                        // 调用sap接口过账
                        boolean transferResult = transfer2SAP(itemDTO, assembleDTO, insMoveTypeDTO, currentUser, receiptCode, postingDate);
                        if (transferResult) {
                            stockCommonService.modifyStock(insMoveTypeDTO);
                            // 修改标签
                            modifyLabel(headDTO, headId, assembleDTO);
                            assembleDTO.setIsTransfer(trueFlag);
                        } else {
                            completeFlag = false;
                            assembleDTO.setIsTransfer(falseFlag);
                            bizReceiptApplyTransferDataWrap.updateById(assembleDTO);
                            continue;
                        }
                    } catch(Exception e) {
                        log.error("post item error:", e);
                        completeFlag = false;
                        assembleDTO.setIsTransfer(falseFlag);
                        assembleDTO.setIsReserved(falseFlag);
                        bizReceiptApplyTransferDataWrap.updateById(assembleDTO);
                        continue;
                    }
                }
                if ((price == null) || price.compareTo(BigDecimal.ZERO) <= 0 || qty.compareTo(BigDecimal.ZERO) <= 0) {
                    assembleDTO.setIsTransfer(trueFlag);
                    assembleDTO.setIsReserved(trueFlag);
                    bizReceiptApplyTransferDataWrap.updateById(assembleDTO);
                    continue;
                }
                if (reservedStatus == trueFlag) {
                    continue;
                }
                if ((falseFlag == transferFlag) && qty.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                // 创建预留
                boolean reservedResult = reserved2SAP(headDTO, itemDTO, assembleDTO, currentUser, receiptCode, ridReservedMap);
                if (reservedResult) {
                    assembleDTO.setIsTransfer(trueFlag);
                    assembleDTO.setIsReserved(trueFlag);
                } else {
                    completeFlag = false;
                    // 更新行项目状态为未同步
                    assembleDTO.setIsReserved(falseFlag);
                }
                // 更新入库单行项目【转性状态、预留状态】
                // 转性成功更新入库单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、凭证日期、过账日期、是否转性标识】
                // 预留成功更新入库单行项目【前置单据行项目ID、前置单据ID、前置单据类型、前置单据编号】
                bizReceiptApplyTransferDataWrap.updateById(assembleDTO);
            }
        }
        if (!completeFlag) {
            // 更新申请单状态未同步
            this.updateApplyReceiptHeadStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            return;
        }
        // 所有行项目都完成转性和创建预留后，更新领料申请单为已完成，且自动创建出库单
        this.updateApplyReceiptHeadStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        /* ================================ 自动创建领料申请单 ================================ */
        List<BizReceiptOutputItemDTO> receiptOutputItemDTOList = new ArrayList<>();
        // 领料出库单是基于领料申请单进行创建，进行属性填充
        for (BizReceiptApplyItemDTO item : itemDTOList) {
            List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
            for (BizReceiptApplyTransferDTO obj : item.getTransferDTOList()) {
                if (obj.getQty().compareTo(BigDecimal.ZERO) > 0) {
                    BizReceiptAssembleDTO assembleDTO = UtilBean.newInstance(obj, BizReceiptAssembleDTO.class);
                    int transferFlag = obj.getTransferFlag();
                    if (transferFlag == trueFlag) {
                        assembleDTO.setSpecStockCode(obj.getInputSpecStockCode());
                        // 更新spec、specDisplayValue、specValue
                        BizReceiptAssembleRuleSearchPO pos = new BizReceiptAssembleRuleSearchPO();
                        pos.setMatId(assembleDTO.getMatId());
                        pos.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                        pos.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue());
                        pos.setSpecStockCode(obj.getInputSpecStockCode());
                        pos.setIsApplyFlag(1);
                        pos.setFtyId(assembleDTO.getFtyId());
                        pos.setBatchCode(obj.getBatchCode());
                        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue(), pos);
                        if (assembleRuleDTO == null) {
                            // 更新申请单状态未同步
                            this.updateApplyReceiptHeadStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                            return;
                        }
                        List<BizReceiptAssembleDTO> assembleDTOListTemp = assembleRuleDTO.getAssembleDTOList();
                        if (assembleDTOListTemp.size() != 1) {
                            // 更新申请单状态未同步
                            this.updateApplyReceiptHeadStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                            return;
                        }
                        BizReceiptAssembleDTO assembleDTOTemp = assembleDTOListTemp.get(0);
                        assembleDTO.setSpecCode(assembleDTOTemp.getSpecCode());
                        assembleDTO.setSpecDisplayValue(assembleDTOTemp.getSpecDisplayValue());
                        assembleDTO.setSpecValue(assembleDTOTemp.getSpecValue());
                    } else {
                        assembleDTO.setSpecStockCode(obj.getOutputSpecStockCode());
                    }
                    assembleDTOList.add(assembleDTO);
                }
            }
            BizReceiptOutputItemDTO receiptOutputItemDTO = BizReceiptOutputItemDTO.builder()
                    // 这里没有配置领料申请的行项目的rid
                    .matId(item.getMatId())
                    .ftyId(item.getFtyId())
                    .ftyCode(item.getFtyCode())
                    .locationId(item.getLocationId())
                    .mainLocationId(item.getMainLocationId())
                    .whId(item.getWhId())
                    .preReceiptQty(item.getQty())
                    .receiptQty(item.getQty())
                    //操作数量，单价，金额
                    .qty(item.getQty())
                    .price(item.getPrice())
                    .amount(item.getAmount())
                    // 已退库数量
                    .returnQty(BigDecimal.ZERO)
                    // 已作业数量
                    .taskQty(BigDecimal.ZERO)
                    // 已完成数量
                    .finishQty(BigDecimal.ZERO)
                    // 特殊库存标识
                    .specStock(item.getSpecStock())
                    .unitId(item.getUnitId())
                    // 前续单据head id
                    .preReceiptHeadId(item.getHeadId())
                    // 前序单据item表id
                    .preReceiptItemId(item.getId())
                    // 前序单据类型
                    .preReceiptType(receiptType)
                    .referReceiptHeadId(item.getPreReceiptHeadId())
                    .referReceiptItemId(item.getPreReceiptItemId())
                    .referReceiptCode(item.getPreReceiptCode())
                    .referReceiptType(EnumReceiptType.RESERVE_RECEIPT.getValue())
                    // 行项目状态
                    .itemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .createTime(UtilDate.getNow())
                    .createUserId(item.getCreateUserId())
//                    .specStockCode(item.getSpecStockCode())
                    .assembleDTOList(assembleDTOList)
                    .build();
            receiptOutputItemDTOList.add(receiptOutputItemDTO);
        }
        BizReceiptOutputHeadDTO receiptOutputHeadDTO = BizReceiptOutputHeadDTO.builder()
                .unit(headDTO.getUnit())
                .itemDTOList(receiptOutputItemDTOList)
                .remark(headDTO.getRemark())
                .des(headDTO.getDes())
                .receiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue()).build();
        // 将领料申请信息保存到上下文中
        log.info("领料申请单保存的数据:{}", JSON.toJSONString(receiptOutputHeadDTO));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptOutputHeadDTO);
        unitizedMaterialOutputService.save(ctx);
        /* ================================ 自动创建领料申请单 ================================ */
        // 出库配货策略
        outputComponent.autoDistribution(ctx);
    }

    /**
     * 生成ins凭证 - 过账
     */
    public StockInsMoveTypeDTO generateInsDocToPost(BizReceiptApplyHeadDTO headDTO, BizReceiptApplyItemDTO itemDTO, BizReceiptApplyTransferDTO assembleDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        for (BizReceiptTransportBinDTO binDTO : assembleDTO.getBinDTOList()) {
            // 批次库存-发出方扣减
            insDocBatchList.add(this.getOutputInsBatch(headDTO, assembleDTO, binDTO, rid, code, userId));
            // 仓位库存-发出方扣减
            insDocBinList.add(this.getOutputInsBin(headDTO, itemDTO, assembleDTO, binDTO, rid++, code, userId));
            // 批次库存-接收方新增
            insDocBatchList.add(this.getInputInsBatch(headDTO, assembleDTO, binDTO, rid, code, userId));
            // 仓位库存-接收方新增
            insDocBinList.add(this.getInputInsBin(headDTO, itemDTO, assembleDTO, binDTO, rid++, code, userId));
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        return insMoveTypeDTO;
    }

    /**
     * 批次库存-发出方扣减
     */
    private StockInsDocBatch getOutputInsBatch(BizReceiptApplyHeadDTO headDTO, BizReceiptApplyTransferDTO assembleDTO, BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(assembleDTO.getOutputMatId());
        insDocBatch.setBatchId(binDTO.getOutputBatchId());
        insDocBatch.setFtyId(assembleDTO.getOutputFtyId());
        insDocBatch.setLocationId(assembleDTO.getOutputLocationId());
        insDocBatch.setUnitId(assembleDTO.getOutputUnitId());
        insDocBatch.setDecimalPlace(assembleDTO.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(binDTO.getQty());
//        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
//            // 冲销 - 新增
//            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
//        } else {
            // 发出方扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
//        }
        insDocBatch.setStockStatus(assembleDTO.getOutputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(assembleDTO.getId());
        insDocBatch.setPreReceiptBinId(binDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
//        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
//        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
//        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
//        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }

    /**
     * 批次库存-发出方扣减
     */
    private StockInsDocBin getOutputInsBin(BizReceiptApplyHeadDTO headDTO, BizReceiptApplyItemDTO itemDTO, BizReceiptApplyTransferDTO assembleDTO, BizReceiptTransportBinDTO binDTO,
                                           int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(assembleDTO.getOutputMatId());
        insDocBin.setBatchId(binDTO.getOutputBatchId());
        insDocBin.setFtyId(assembleDTO.getOutputFtyId());
        insDocBin.setLocationId(assembleDTO.getOutputLocationId());
        insDocBin.setWhId(assembleDTO.getOutputWhId());
        insDocBin.setCellId(binDTO.getInputCellId());
        insDocBin.setUnitId(assembleDTO.getInputUnitId());
        insDocBin.setDecimalPlace(assembleDTO.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
//        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
//            // 冲销 - 新增
//            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
//            // 实际仓位
//            insDocBin.setBinId(binDTO.getOutputBinId());
//            insDocBin.setTypeId(binDTO.getOutputTypeId());
//        } else {
            // 发出方扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            String typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(assembleDTO.getOutputWhCode(), typeCode);
            String binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
            Long binId = dictionaryService.getBinIdCacheByCode(assembleDTO.getOutputWhCode(), typeCode, binCode);
            /*List<Long> moveTypeIdList = null;
            if (UtilCollection.isEmpty(headDTO.getMoveTypeIds())) {
                moveTypeIdList = dictionaryService.getMoveTypeIDListCacheByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
            } else {
                moveTypeIdList = headDTO.getMoveTypeIds();
            }*/
        List<Long> moveTypeIdList = dictionaryService.getMoveTypeIDListCacheByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        if (moveTypeIdList.contains(headDTO.getMoveTypeId())) {
                // 冻结转非限制,这种移动类型,需要直接修改正式仓位数据
                typeId = binDTO.getOutputTypeId();
                binId = binDTO.getOutputBinId();
            }
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
//        }
        insDocBin.setStockStatus(assembleDTO.getOutputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(assembleDTO.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptItemId(itemDTO.getPreReceiptItemId());
//        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
//        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
//        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setReferReceiptHeadId(0L);
        insDocBin.setReferReceiptType(null);
        insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
//        insDocBin.setMatDocCode(itemDto.getMatDocCode());
//        insDocBin.setMatDocRid(itemDto.getMatDocRid());
//        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }

    /**
     * 批次库存-接收方新增
     */
    private StockInsDocBatch getInputInsBatch(BizReceiptApplyHeadDTO headDTO, BizReceiptApplyTransferDTO assembleDTO, BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(assembleDTO.getInputMatId());
        insDocBatch.setBatchId(binDTO.getInputBatchId());
        insDocBatch.setFtyId(assembleDTO.getInputFtyId());
        insDocBatch.setLocationId(assembleDTO.getInputLocationId());
        insDocBatch.setUnitId(assembleDTO.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBatch.setDecimalPlace(assembleDTO.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(binDTO.getQty());
//        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
//            // 冲销 - 扣减
//            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
//        } else {
            // 接收方新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
//        }
        insDocBatch.setStockStatus(assembleDTO.getInputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(assembleDTO.getId());
        insDocBatch.setPreReceiptBinId(binDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
//        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
//        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
//        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
//        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }

    /**
     * 批次库存-接收方新增
     */
    private StockInsDocBin getInputInsBin(BizReceiptApplyHeadDTO headDTO, BizReceiptApplyItemDTO itemDTO, BizReceiptApplyTransferDTO assembleDTO, BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(assembleDTO.getInputMatId());
        insDocBin.setBatchId(binDTO.getInputBatchId());
        insDocBin.setFtyId(assembleDTO.getInputFtyId());
        insDocBin.setLocationId(assembleDTO.getInputLocationId());
        insDocBin.setWhId(assembleDTO.getInputWhId());
        String typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
        Long typeId = dictionaryService.getStorageTypeIdCacheByCode(assembleDTO.getInputWhCode(), typeCode);
        String binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
        Long binId = dictionaryService.getBinIdCacheByCode(assembleDTO.getInputWhCode(), typeCode, binCode);
        /*List<Long> moveTypeIdList = null;
        if (UtilCollection.isEmpty(headDTO.getMoveTypeIds())) {
            moveTypeIdList = dictionaryService.getMoveTypeIDListCacheByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        } else {
            moveTypeIdList = headDTO.getMoveTypeIds();
        }*/
        List<Long> moveTypeIdList = dictionaryService.getMoveTypeIDListCacheByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        if (moveTypeIdList.contains(headDTO.getMoveTypeId())) {
            // 冻结转非限制,这种移动类型,需要直接修改正式仓位数据
            typeId = binDTO.getInputTypeId();
            binId = binDTO.getInputBinId();
        }
        insDocBin.setTypeId(typeId);
        insDocBin.setBinId(binId);
        // 托盘不变,取发出托盘
        insDocBin.setCellId(binDTO.getInputCellId());
        insDocBin.setUnitId(assembleDTO.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBin.setDecimalPlace(assembleDTO.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
//        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
//            // 冲销 - 扣减
//            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
//        } else {
            // 接收方新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
//        }
        insDocBin.setStockStatus(assembleDTO.getInputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(assembleDTO.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptItemId(itemDTO.getPreReceiptItemId());
//        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
//        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
//        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setReferReceiptHeadId(0L);
        insDocBin.setReferReceiptType(null);
        insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
//        insDocBin.setMatDocCode(itemDto.getMatDocCode());
//        insDocBin.setMatDocRid(itemDto.getMatDocRid());
//        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        if(UtilCollection.isNotEmpty(binDTO.getLabelDataList())){
            StockInsDocBinPo insDocBinPo = UtilBean.deepCopyNewInstance(insDocBin, StockInsDocBinPo.class);
            insDocBinPo.setLabelIdList(binDTO.getLabelDataList().stream().map(BizLabelReceiptRelDTO::getLabelId).collect(Collectors.toList()));
            insDocBin.setLabelPo(insDocBinPo);
        }
        return insDocBin;
    }

    /**
     * 【同时模式-提交】【先过账模式】调用sap接口过账
     *
     * @return true--转性成功;false--转性失败;
     */
    public boolean transfer2SAP(BizReceiptApplyItemDTO it, BizReceiptApplyTransferDTO itemDTO, StockInsMoveTypeDTO insMoveTypeDTO, CurrentUser user, String receiptCode, Date postingDate) {
        BigDecimal amount = itemDTO.getAmount();
        BigDecimal qty = itemDTO.getQty();
        itemDTO.getBinDTOList().stream().forEach(bin->{
            BizBatchInfoDTO bizBatchInfoDTO = bin.getOutputBatchInfoDTO();
            bin.setBatchCode(bin.getInputBatchInfoDTO().getBatchCode());
            String specStock = bizBatchInfoDTO.getSpecStock();
            bin.setOutputSpecStock(specStock);
            bin.setOutSpecStock(specStock);
            bin.setOutputSpecStockCode(bin.getOutputBatchInfoDTO().getSpecStockCode());
            bin.setQty(amount);
        });
        String matCode = it.getMatCode();
        String locationCode = it.getLocationCode();
        itemDTO.setReceiptCode(receiptCode);
        itemDTO.setOutputMatCode(matCode);
        itemDTO.setOutputFtyCode(it.getFtyCode());
        itemDTO.setOutputLocationCode(locationCode);
        itemDTO.setInputMatCode(matCode);
        itemDTO.setInputLocationCode(locationCode);
        itemDTO.setInputFtyCode(it.getFtyCode());
        itemDTO.setOutputUnitCode(it.getUnitCode());
        /* ******** 设置入库单账期 ******** */
        this.setInPostDate(itemDTO, user, postingDate);
        itemDTO.setReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        /* ******** 调用sap ******** */
        List<BizReceiptApplyTransferDTO> itemDTOList = new ArrayList<>();
        itemDTOList.add(itemDTO);
        // 过账SAP批次号处理
        ErpReturnObject returnObj = sapInterfaceService.transPosting(JSONArray.toJSONStringWithDateFormat(itemDTOList, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
        itemDTO.getBinDTOList().stream().forEach(bin->{
            bin.setQty(qty);
        });
        /* ******** 调用sap后处理开始 ******** */
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
            if (UtilCollection.isNotEmpty(returnObjectItems)) {
                // 获取当前item返回对象
                ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                        .filter(item -> item.getReceiptRid().equals(itemDTO.getRid())).findFirst().orElse(null);
                if (UtilObject.isNotNull(currentReturnObject)) {
                    itemDTO.setMatDocCode(currentReturnObject.getMatDocCode());
                    itemDTO.setMatDocRid(currentReturnObject.getMatDocRid());
                    itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                    itemDTO.setIsTransfer(EnumRealYn.TRUE.getIntValue());
                    // 过账成功，补全ins凭证
                    if (UtilObject.isNotNull(insMoveTypeDTO)) {
                        for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                            if (insDocBatch.getPreReceiptItemId().equals(itemDTO.getId())) {
                                insDocBatch.setMatDocCode(currentReturnObject.getMatDocCode());
                                insDocBatch.setMatDocRid(currentReturnObject.getMatDocRid());
                                insDocBatch.setPostingDate(itemDTO.getPostingDate());
                                insDocBatch.setDocDate(itemDTO.getDocDate());
                                insDocBatch.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                            }
                        }
                        for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                            if (insDocBin.getPreReceiptItemId().equals(itemDTO.getId())) {
                                insDocBin.setMatDocCode(currentReturnObject.getMatDocCode());
                                insDocBin.setMatDocRid(currentReturnObject.getMatDocRid());
                                insDocBin.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                            }
                        }
                    }
                }
            }
            return true;
        }
        log.error("入库单{}SAP转性失败", receiptCode);
        return false;
    }

    /**
     * 修改标签
     */
    public void modifyLabel(BizReceiptApplyHeadDTO headDTO, Long headId, BizReceiptApplyTransferDTO assembleDTO) {
        // 同时模式,在页面选择标签
        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
        labelReceiptRel.setReceiptHeadId(headId);
        List<BizLabelReceiptRel> relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
        if (UtilCollection.isEmpty(relList)) {
            // 未查询到对应的标签信息则不修改
            return;
        }
        List<BizLabelData> labelDataList = new ArrayList<>();
        for (BizLabelReceiptRel receiptRel : relList) {
            for (BizReceiptTransportBinDTO binDTO : assembleDTO.getBinDTOList()) {
                if (receiptRel.getReceiptBinId().equals(binDTO.getId()) || receiptRel.getPreReceiptBinId().equals(binDTO.getId())) {
                    // id一致
                    BizLabelData labelData = new BizLabelData();
                    labelData.setId(receiptRel.getLabelId());
//                        if (itemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
//                            // 冲销回发出
//                            labelData.setFtyId(itemDTO.getOutputFtyId());
//                            labelData.setLocationId(itemDTO.getOutputLocationId());
//                            labelData.setWhId(itemDTO.getOutputWhId());
//                            labelData.setMatId(itemDTO.getOutputMatId());
//                            labelData.setBatchId(binDTO.getOutputBatchId());
//                            labelData.setTypeId(binDTO.getOutputTypeId());
//                            labelData.setBinId(binDTO.getOutputBinId());
//                            labelData.setCellId(binDTO.getOutputCellId());
//                        } else {
                    // 批次信息更新为接收
                    labelData.setFtyId(assembleDTO.getInputFtyId());
                    labelData.setLocationId(assembleDTO.getInputLocationId());
                    labelData.setWhId(assembleDTO.getInputWhId());
                    labelData.setMatId(assembleDTO.getInputMatId());
                    labelData.setBatchId(binDTO.getInputBatchId());
                    if (!UtilNumber.isEmpty(binDTO.getInputTypeId())) {
                        labelData.setTypeId(binDTO.getInputTypeId());
                    }
                    if (!UtilNumber.isEmpty(binDTO.getInputBinId())) {
                        labelData.setBinId(binDTO.getInputBinId());
                    }
                    if (!UtilNumber.isEmpty(binDTO.getInputCellId())) {
                        labelData.setCellId(binDTO.getInputCellId());
                    }
//                        }
                    labelDataList.add(labelData);
                }
            }
        }
        labelDataService.multiUpdateLabelData(labelDataList);
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemDTO 未同步sap入库单行项目
     * @param user 当前用户
     * @param postingDate 过账日期
     */
    private void setInPostDate(BizReceiptApplyTransferDTO itemDTO, CurrentUser user, Date postingDate) {
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        itemDTO.setDocDate(UtilDate.getNow());
        itemDTO.setPostingDate(postingDate);
    }

    private boolean reserved2SAP(BizReceiptApplyHeadDTO headDTO, BizReceiptApplyItemDTO itemDTO, BizReceiptApplyTransferDTO assembleDTO, CurrentUser user, String receiptCode, Map<String, AtomicInteger> ridReservedMap) {
        // 构建创建预留单的参数
        BizReceiptApplyHeadDTO headDTOTemp = new BizReceiptApplyHeadDTO();
        headDTOTemp.setReceiptNum(headDTO.getReceiptNum());
        headDTOTemp.setCreateTime(headDTO.getCreateTime());
        headDTOTemp.setCreateUserCode(headDTO.getCreateUserCode());
        headDTOTemp.setApplyTime(headDTO.getApplyTime());
        headDTOTemp.setWhCodeOut(assembleDTO.getWhCodeOut());
        headDTOTemp.setReceiptCode(headDTO.getReceiptCode());
        headDTOTemp.setReceiptType(headDTO.getReceiptType());
        BizReceiptApplyItemDTO itemDTOTemp = new BizReceiptApplyItemDTO();
        String rid = itemDTO.getRid();
        itemDTOTemp.setRid(rid);
        itemDTOTemp.setFtyCode(itemDTO.getFtyCode());
        itemDTOTemp.setLocationCode(itemDTO.getLocationCode());
        itemDTOTemp.setSpecStock(itemDTO.getSpecStock());
        itemDTOTemp.setMatCode(itemDTO.getMatCode());
        itemDTOTemp.setRidReserved("1");
        itemDTOTemp.setRidReservedNum(1);
        // 金额对应SAP数量
        itemDTOTemp.setQty(assembleDTO.getAmount());
        List<BizReceiptApplyItemDTO> listTemp = new ArrayList<>();
        listTemp.add(itemDTOTemp);
        headDTOTemp.setItemList(listTemp);
        ReserveReceiptCreatePO reserveReceiptCreatePO = ReserveReceiptCreatePO.builder().bizReceiptApplyHeadDTO(headDTOTemp).build();
        // 创建预留得时候，不同步sap使用了此rid
        itemDTO.setReservedOrderRid(itemDTO.getRid());
        ReserveReceiptCreatePO sapCreateReserveReceiptInfo = reserveReceiptService.createReserveReceiptItemListNew(reserveReceiptCreatePO, user);
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(sapCreateReserveReceiptInfo.getSuccess())) {
            List<BizReceiptApplyItemDTO> receiptApplyItemDTOList = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList();
            for (BizReceiptApplyItemDTO dto : receiptApplyItemDTOList) {
                if (rid.equals(dto.getRid())) {
                    assembleDTO.setReservedOrderCode(dto.getReservedOrderCode());
                    assembleDTO.setReservedOrderRid(dto.getReservedOrderRid());
                    assembleDTO.setPreReceiptItemId(dto.getPreReceiptItemId());
                    assembleDTO.setPreReceiptHeadId(dto.getPreReceiptHeadId());
                    assembleDTO.setPreReceiptType(dto.getPreReceiptType());
                    assembleDTO.setPreReceiptCode(dto.getPreReceiptCode());
                }
            }
            return true;
        }
        log.error("入库单{}SAP创建预留失败", receiptCode);
        return false;
    }

    /**
     * 校验行项目的参数
     *
     * @param ctx
     */
    public void checkSaveNew(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(headDTO) || UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 判断工厂是否唯一
        ArrayList<BizReceiptApplyItemDTO> distinctFty = headDTO.getItemList().stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BizReceiptApplyItemDTO::getFtyId))), ArrayList::new));
        if (distinctFty.size() > 1) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FTY_DUPLICATE);
        }
        // 根据移动类型查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        if (rule == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        headDTO.setOutputStockStatus(rule.getOutputStockStatus());
        headDTO.setInputStockStatus(rule.getInputStockStatus());
//        headDTO.setInputSpecStock(rule.getInputSpecStock());
//        headDTO.setOutSpecStock(rule.getOutputSpecStock());
        for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptApplyTransferDTO transferDTO : itemDTO.getTransferDTOList()) {
                String parentId = transferDTO.getParentId();
                if (StringUtils.isBlank(parentId)) {
                    continue;
                }
                // 根据规则对目标信息校验/赋值
                transferDTO.setInputFtyId(this.checkRule(rule.getInputFtyId(), transferDTO.getOutputFtyId(), transferDTO.getInputFtyId()));
                transferDTO.setInputLocationId(this.checkRule(rule.getInputLocationId(), transferDTO.getOutputLocationId(), transferDTO.getInputLocationId()));
                transferDTO.setInputMatId(this.checkRule(rule.getInputMatId(), transferDTO.getOutputMatId(), transferDTO.getInputMatId()));
                transferDTO.setInputUnitId(this.checkRule(rule.getInputUnitId(), transferDTO.getOutputUnitId(), transferDTO.getInputUnitId()));
                transferDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(), transferDTO.getOutputSpecStockCode(), transferDTO.getInputSpecStockCode()));
                transferDTO.setOutSpecStock(rule.getOutputSpecStock());
                transferDTO.setOutputStockStatus(rule.getOutputStockStatus());
                transferDTO.setInputStockStatus(rule.getOutputStockStatus());
                transferDTO.setInputWhId(transferDTO.getWhId());
                transferDTO.setOutputWhId(transferDTO.getWhId());
            }
        }
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    private Long checkRule(Long ruleValue, Long outputValue, Long inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if (0 == ruleValue) {
            // 空值
            inputValue = null;
        } else if (1 == ruleValue) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if (2 == ruleValue) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    private String checkRule(String ruleValue, String outputValue, String inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if ("0".equals(ruleValue)) {
            // 空值
            inputValue = null;
        } else if ("1".equals(ruleValue)) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if ("2".equals(ruleValue)) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 保存领料申请单信息
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBillInfoNew(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        boolean isNew = true;
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        CurrentUser user = ctx.getCurrentUser();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        String receiptCode = applyHeadDTO.getReceiptCode();
        String applyCode = applyHeadDTO.getReceiptNum() == null ? null : applyHeadDTO.getReceiptNum();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        // 补充转性标识
        applyHeadDTO.setTransferFlag(EnumRealYn.FALSE.getIntValue());
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptApplyTransferDTO assembleDTO : itemDto.getTransferDTOList()) {
                String parentId = assembleDTO.getParentId();
                if (StringUtils.isNotBlank(parentId)) {
                    applyHeadDTO.setTransferFlag(EnumRealYn.TRUE.getIntValue());
                    break;
                }
            }
        }
        // 处理头信息
        if (UtilNumber.isNotEmpty(applyHeadDTO.getId())) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(applyHeadDTO.getId());
            // 根据id更新
            applyHeadDTO.setRemark(applyHeadDTO.getReceiptRemark());
            bizReceiptApplyHeadDataWrap.updateDtoById(applyHeadDTO);
            // item物理删除
            QueryWrapper<BizReceiptApplyItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(BizReceiptApplyItem::getHeadId, applyHeadDTO.getId());
            bizReceiptApplyItemDataWrap.physicalDelete(itemQueryWrapper);
            // assemble物理删除
            QueryWrapper<BizReceiptApplyTransfer> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizReceiptApplyTransfer::getReceiptHeadId, applyHeadDTO.getId());
            bizReceiptApplyTransferDataWrap.physicalDelete(queryWrapperAssemble);
            // outInfo 根据id、headId更新
            QueryWrapper<BizReceiptOutputInfo> outInfoWrapper = new QueryWrapper<>();
            outInfoWrapper.lambda().eq(BizReceiptOutputInfo::getId, applyHeadDTO.getOutInfoId());
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .build();
            bizReceiptOutputInfoDataWrap.updateDto(bizReceiptOutputInfoDTO, outInfoWrapper);
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            /* ================ outInfo处理  领料申请单信息 ================ */
            applyCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_APPLY_CODE.getValue());

            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
//                    .specStockCode(applyHeadDTO.getSpecStockCode())
//                    .specStockName(applyHeadDTO.getSpecStockName())
                    .whCodeOut(applyHeadDTO.getWhCodeOut())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .createTime(UtilDate.getNow())
                    .modifyTime(UtilDate.getNow())
                    .createUserId(user.getCreateUserId())
                    .modifyUserId(user.getModifyUserId())
                    .build();
            bizReceiptOutputInfoDataWrap.saveDto(bizReceiptOutputInfoDTO);
            /* ================ head处理 ================ */
            receiptCode = bizCommonService.getNextSeqMaterialReq(user,EnumSequenceCode.MATERIAL_REQ.getValue(),applyHeadDTO.getItemList().get(EnumRealYn.FALSE.getIntValue()).getFtyCode());
            Date now = UtilDate.getNow();
            applyHeadDTO.setCreateTime(now);
            applyHeadDTO.setModifyTime(now);
            applyHeadDTO.setReceiptCode(receiptCode);
            applyHeadDTO.setCreateUserId(createUserId);
            applyHeadDTO.setModifyUserId(createUserId);
            applyHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
            applyHeadDTO.setReceiptStatus(status);
            applyHeadDTO.setOutInfoId(bizReceiptOutputInfoDTO.getId());
            applyHeadDTO.setRemark(applyHeadDTO.getReceiptRemark());
            bizReceiptApplyHeadDataWrap.saveDto(applyHeadDTO);
            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }
        // item处理
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            BigDecimal temp = BigDecimal.ZERO;
            for (BizReceiptApplyTransferDTO assembleDTO : itemDTO.getTransferDTOList()) {
                temp = temp.add(assembleDTO.getQty());
            }
            itemDTO.setQty(temp);
            itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            itemDTO.setId(null);
            itemDTO.setHeadId(applyHeadDTO.getId());
            itemDTO.setItemStatus(status);
            itemDTO.setCreateTime(createTime);
            itemDTO.setCreateUserId(createUserId);
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setMatId(dictionaryService.getMatIdByMatCode(itemDTO.getMatCode()));
            itemDTO.setWhId(dictionaryService.getLocationCacheById(itemDTO.getLocationId()).getWhId());
            itemDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);
        // assemble处理
        List<BizReceiptApplyTransferDTO> assembleList = new ArrayList<>();
        AtomicInteger tid = new AtomicInteger(1);
        Map<String, String[]> stockCodeMap = new HashMap<>();
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptApplyTransferDTO assembleDTO : itemDto.getTransferDTOList()) {
                String parentId = assembleDTO.getParentId();
                if (StringUtils.isNotBlank(parentId)) {
                    assembleDTO.setTransferFlag(EnumRealYn.TRUE.getIntValue());
                }
                assembleDTO.setRid(String.valueOf(tid.getAndIncrement()));
                assembleDTO.setId(null);
                assembleDTO.setReceiptHeadId(applyHeadDTO.getId());
                assembleDTO.setReceiptItemId(itemDto.getId());
                assembleDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
                assembleDTO.setCreateUserId(createUserId);
                assembleDTO.setModifyUserId(user.getId());
                String batchCode = assembleDTO.getBatchCode();
                if (StringUtils.isBlank(batchCode)) {
                    BizBatchInfoDTO batchInfoDTO = assembleDTO.getBatchInfo();
                    if (batchInfoDTO != null) {
                        assembleDTO.setBatchCode(batchInfoDTO.getBatchCode());
                    }
                }
                assembleDTO.setSpecType(assembleDTO.getSpecType() == null ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue() : assembleDTO.getSpecType());
                if (UtilNumber.isEmpty(assembleDTO.getUnitizedBatchId())) {
                    assembleDTO.setUnitizedBatchId(assembleDTO.getBatchId());
                }
                String specStockCode = assembleDTO.getSpecStockCode();
                String specStockCodeTransfer = assembleDTO.getInputSpecStockCode();
                String whCodeOut = assembleDTO.getWhCodeOut();
                if (StringUtils.isNotBlank(parentId) && StringUtils.isNotBlank(specStockCodeTransfer)) {
                    specStockCode = specStockCodeTransfer;
                }
                if (StringUtils.isBlank(whCodeOut)) {
                    String[] codeNameList = stockCodeMap.get(specStockCode);
                    String inputSpecStockName = null;
                    if (codeNameList == null) {
                        // 根据24位的WBS Code 获取 8位的WBS编号 8位的WBS Code是在创建预留单的时候使用
                        WmsQueryWrapper<ErpPurchaseReceiptItem> purchaseReceiptItemWmsQueryWrapper = new WmsQueryWrapper<>();
                        purchaseReceiptItemWmsQueryWrapper.lambda().eq(ErpPurchaseReceiptItem::getSpecStockCode, specStockCode);
                        List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(purchaseReceiptItemWmsQueryWrapper);
                        if (CollectionUtils.isEmpty(erpPurchaseReceiptItemList)) {
                            SapWbs erpWbs = erpWbsService.findByCode(specStockCode);
                            if (erpWbs == null) {
                                throw new WmsException(EnumReturnMsg.RETURN_CODE_WBS_CODE_GET_FAILED);
                            } else {
                                whCodeOut = erpWbs.getWbsCodeIn();
                                inputSpecStockName = erpWbs.getWbsName();
                            }
                        } else {
                            ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemList.get(0);
                            whCodeOut = erpPurchaseReceiptItem.getWhCodeOut();
                            inputSpecStockName = erpPurchaseReceiptItem.getSpecStockName();
                        }
                        stockCodeMap.put(specStockCode, new String[]{whCodeOut, inputSpecStockName});
                    } else {
                        whCodeOut = codeNameList[0];
                        inputSpecStockName = codeNameList[1];
                    }
                    assembleDTO.setWhCodeOut(whCodeOut);
                    assembleDTO.setInputSpecStockName(inputSpecStockName);
                }
                String inputSpecStockName = assembleDTO.getInputSpecStockName();
                if (StringUtils.isBlank(inputSpecStockName)) {
                    String[] codeNameList = stockCodeMap.get(specStockCode);
                    if (codeNameList == null) {
                        SapWbs erpWbs = erpWbsService.findByCode(specStockCode);
                        if (erpWbs != null) {
                            String codeTemp = erpWbs.getWbsCodeIn();
                            String nameTemp = erpWbs.getWbsName();
                            assembleDTO.setInputSpecStockName(nameTemp);
                            stockCodeMap.put(specStockCode, new String[]{codeTemp, nameTemp});
                        }
                    }
                }
                assembleList.add(assembleDTO);
            }
        }
        bizReceiptApplyTransferDataWrap.saveBatchDto(assembleList);
        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptApplyTransferDTO bizReceiptAssembleDTO : itemDto.getTransferDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                        labelReceiptRel.setReceiptType(applyHeadDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(itemDto.getHeadId());
                        labelReceiptRel.setReceiptItemId(itemDto.getId());
                        labelReceiptRel.setReceiptBinId(bizReceiptAssembleDTO.getId());
                        bizLabelReceiptRelList.add(labelReceiptRel);
                        labelRelIdList.add(labelReceiptRelDTO.getId());
                    }
                }
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, applyHeadDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, applyHeadDTO.getId());
    }

    /**
     * 根据移动类型保存bin
     */
    public void saveOutputBinByMoveType(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 取表名,字段名
        String tableName = StockBin.class.getAnnotation(TableName.class).value();
        String tableFieldNameBatchId = tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        String tableFieldNameTypeId = tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
        String tableFieldNameCellId = tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
        String tableFieldNameBinId = tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
        List<BizLabelReceiptRel> labelReceiptRelList = new ArrayList<>();
        // bin处理
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        int bid = 1;
        for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptApplyTransferDTO assembleDTO : itemDTO.getTransferDTOList()) {
                String parentId = assembleDTO.getParentId();
                if (StringUtils.isBlank(parentId)) {
                    continue;
                }
                BizReceiptTransportBinDTO transportBinDTO = new BizReceiptTransportBinDTO();
                if (UtilString.isNotNullOrEmpty(assembleDTO.getInputSpecStockCode())) {
                    transportBinDTO.setInputSpecStockCode(assembleDTO.getInputSpecStockCode());
                }
                transportBinDTO.setId(null);
                transportBinDTO.setHeadId(headDTO.getId());
                transportBinDTO.setItemId(assembleDTO.getId());
                transportBinDTO.setBid(Integer.toString(bid++));
                transportBinDTO.setInputSpecStock(itemDTO.getSpecStock());
                transportBinDTO.setOutputFtyId(assembleDTO.getOutputFtyId());
                transportBinDTO.setOutputMatId(assembleDTO.getOutputMatId());
                transportBinDTO.setOutputSpecStockCode(assembleDTO.getOutputSpecStockCode());
                transportBinDTO.setInputFtyId(assembleDTO.getInputFtyId());
                transportBinDTO.setInputMatId(assembleDTO.getInputMatId());
                transportBinDTO.setInputSpecStockCode(assembleDTO.getInputSpecStockCode());
                transportBinDTO.setInputSpecStockName(assembleDTO.getInputSpecStockName());
                List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                for (int i = 0; i < codeList.size(); i++) {
                    if (codeList.get(i).equals(tableFieldNameBatchId)) {
                        // 批次
                        Long batchId = Long.parseLong(valueList.get(i));
                        transportBinDTO.setOutputBatchId(batchId);
                    } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                        // 存储类型
                        transportBinDTO.setOutputTypeId(Long.parseLong(valueList.get(i)));
                        if (UtilNumber.isEmpty(assembleDTO.getInputTypeId())) {
                            // 空值则取发出
                            transportBinDTO.setInputTypeId(transportBinDTO.getOutputTypeId());
                        } else {
                            transportBinDTO.setInputTypeId(assembleDTO.getInputTypeId());
                        }
                    } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                        // 存储单元
                        transportBinDTO.setOutputCellId(Long.parseLong(valueList.get(i)));
                        if (UtilNumber.isEmpty(assembleDTO.getInputCellId())) {
                            // 空值则取发出
                            transportBinDTO.setInputCellId(transportBinDTO.getOutputCellId());
                        } else {
                            transportBinDTO.setInputCellId(assembleDTO.getInputCellId());
                        }
                    } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                        // 仓位
                        transportBinDTO.setOutputBinId(Long.parseLong(valueList.get(i)));
                        if (UtilNumber.isEmpty(assembleDTO.getInputBinId())) {
                            // 空值则取发出
                            transportBinDTO.setInputBinId(transportBinDTO.getOutputBinId());
                        } else {
                            transportBinDTO.setInputBinId(assembleDTO.getInputBinId());
                        }
                    }
                }
                transportBinDTO.setQty(assembleDTO.getQty());
                transportBinDTOList.add(transportBinDTO);
                if (UtilCollection.isNotEmpty(assembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelData : assembleDTO.getLabelDataList()) {
                        // 拼装标签关联信息
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelData.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(transportBinDTO.getHeadId());
                        labelReceiptRel.setReceiptItemId(transportBinDTO.getItemId());
                        labelReceiptRel.setReceiptBinId(Long.parseLong(transportBinDTO.getBid()));
                        labelReceiptRelList.add(labelReceiptRel);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(transportBinDTOList)) {
            return;
        }
        dataFillService.fillAttr(transportBinDTOList);
        // 根据移动类型查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        // 特殊库存设置
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 特殊库存设置
            binDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(), binDTO.getOutputBatchInfoDTO().getSpecStockCode(), binDTO.getInputSpecStockCode()));
            binDTO.setOutputSpecStockCode(binDTO.getOutputBatchInfoDTO().getSpecStockCode());
        }
        // 生成接收方批次信息
        transferComponent.multiInsertBatchInfo(transportBinDTOList);
        // bin表保存
        bizReceiptTransportBinDataWrap.saveBatchDto(transportBinDTOList);
        // 重新填充headDto
        dataFillService.fillAttr(headDTO);
        // 回填 binId
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            for (BizLabelReceiptRel receiptRel : labelReceiptRelList) {
                if (receiptRel.getReceiptHeadId().equals(binDTO.getHeadId())
                        && receiptRel.getReceiptItemId().equals(binDTO.getItemId())
                        && receiptRel.getReceiptBinId().toString().equals(binDTO.getBid())) {
                    receiptRel.setReceiptBinId(binDTO.getId());
                }
            }
        }
        // 标签关联信息保存
        labelReceiptRelService.saveBatch(labelReceiptRelList);
    }

    public void getMatMain(BizContext ctx) {
        DicMaterialFactorySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String matCode = po.getMatCode();
        String matName = po.getMatName();
        String ftyId = po.getFtyId();
        QueryWrapper<DicMaterialFactorySearchPO> wmsQueryWrapper = new QueryWrapper<>();
        wmsQueryWrapper.lambda()
                .eq(Boolean.TRUE, DicMaterialFactorySearchPO::getUnitizedFlag, EnumRealYn.TRUE.getIntValue())
                .eq(StringUtils.isNotBlank(ftyId), DicMaterialFactorySearchPO::getFtyId, ftyId)
                .eq(StringUtils.isNotBlank(matCode), DicMaterialFactorySearchPO::getMatCode, matCode)
                .like(StringUtils.isNotBlank(matName), DicMaterialFactorySearchPO::getMatName, matName);
        Page<DicMaterialFactoryPageVO> pageObj = (Page<DicMaterialFactoryPageVO>) po.getPageObj(DicMaterialFactoryPageVO.class);
        dicMaterialFactoryDataWrap.getDicMaterialFactoryPageVOList(pageObj, wmsQueryWrapper);
        long totalCount = pageObj.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(pageObj.getRecords(), totalCount));
    }

    /**
     * 审批校验
     *
     * @param ctx
     * @return
     */
    private Integer approveCheckNew(BizContext ctx) {
        Integer receiptType = EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_OUTSIDE.getValue();//成套领料工作流单据类型-外部领料
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        Integer isInner = headDTO.getIsInnerFlag();
        if (headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue())) {
            // 是否本部门
            isInner = headDTO.getIsThisDept();
        }
        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();
        List<String> level3UserList = new ArrayList<>();
        List<String> level4UserList = new ArrayList<>();
        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            // 查询用户所属部门的三级审批人
            String deptCode = deptOfficePO.getDeptCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
            level1UserList.addAll(userList);
        }
        String counterpartDeptCode = headDTO.getCounterpartDeptCode();
        String counterpartOfficeCode = headDTO.getCounterpartOfficeCode();
        String deptType = userDepartment.get(0).getDeptType();
        if (EnumDeptTypes.INSIDE.getValue().equals(deptType)) {
            for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
                // 查询用户所属部门的四级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
                level2UserList.addAll(userList);
            }
            // 内部单位
            if (EnumRealYn.FALSE.getIntValue().equals(isInner)) {
                List<String> level5UserList = new ArrayList<>();
                level3UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_2));
                level4UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode( EnumDept.PMD.getCode(), EnumOffice.PMDC.getCode(), EnumApprovalLevel.LEVEL_2));//计划控制部综合计划科二级审批人
                level5UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode( EnumDept.PMD.getCode(), null, EnumApprovalLevel.LEVEL_4));//计划控制部四级审批人
                if (UtilCollection.isEmpty(level5UserList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "5");
                }
                receiptType = EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_CROSS.getValue();//成套领料工作流单据类型-内部领料跨部门
            }else{
                level3UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode( EnumDept.PMD.getCode(), EnumOffice.PMDC.getCode(), EnumApprovalLevel.LEVEL_2));//计划控制部综合计划科二级审批人
                level4UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode( EnumDept.PMD.getCode(), null, EnumApprovalLevel.LEVEL_4));//计划控制部四级审批人
                receiptType = EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_SAME.getValue();//成套领料工作流单据类型-内部领料相同部门
            }
            if (UtilCollection.isEmpty(level1UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
            }
            if (UtilCollection.isEmpty(level2UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
            }
            if (UtilCollection.isEmpty(level3UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
            }
            if (UtilCollection.isEmpty(level4UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "4");
            }
        }else{
            // 外部单位
            if (StringUtils.isBlank(counterpartDeptCode) || StringUtils.isBlank(counterpartOfficeCode)) {
                return receiptType;
            }
            // 查询对口部门、对口科室1级审批人
            level2UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_1));
            // 查询对口部门3级审批人
            Integer transferFlag = headDTO.getTransferFlag();
            if (transferFlag.equals(EnumRealYn.FALSE.getIntValue())) {
                level3UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3));
                level4UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_4));
            } else {
                level3UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_2));
                level4UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3));
                List<String> level5UserList = new ArrayList<>();
                level5UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_4));
                if (UtilCollection.isEmpty(level5UserList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "5");
                }
                receiptType = EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_OUTSIDE_TRANSFER.getValue();//成套领料工作流单据类型-外部转性领料
            }
            if (receiptType.equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_OUTSIDE.getValue())){
                // SO审批流程
                Boolean isSo = false;
                for (BizReceiptApplyBinDTO binDTO : headDTO.getBinList()) {
                    if (binDTO.getExtend28().equals(Const.MAT_TYPE_SO)) {
                        isSo = true;
                    }
                }
                if (isSo) {
                    List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_8);
                    if (UtilCollection.isEmpty(userList)) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
                    }
                    userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.PRD.getCode(), null, EnumApprovalLevel.LEVEL_4);
                    if (UtilCollection.isEmpty(userList)) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
                    }
                    return EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_OUTSIDE_SO.getValue();
                }

                // 外部用户无审批人提示
                List<String> userList;
                if(EnumDept.CDE.getCode().equals(counterpartDeptCode)){
                    userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_8);
                } else {
                    userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3);
                }
                if (UtilCollection.isEmpty(userList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "1");
                }
                return receiptType;
            }
            if (UtilCollection.isEmpty(level1UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
            }
            if (UtilCollection.isEmpty(level2UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
            }
            if (UtilCollection.isEmpty(level3UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
            }
            if (UtilCollection.isEmpty(level4UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "4");
            }
        }

        return receiptType;
    }

    /**
     * 提交前校验
     */
    public void checkSubmitByRequire(BizContext ctx) {
        // 发起流程审批
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        String deptType = userDepartment.get(0).getDeptType();
        if (EnumDeptTypes.INSIDE.getValue().equals(deptType)) {
            // 内部单位
            for (BizReceiptApplyBinDTO binDTO : headDTO.getBinList()) {
                if (binDTO.getExtend28().equals(Const.MAT_TYPE_SO)) {
                    // 提报时，若内部用户创建的领用申请中包含“SO：运行备件”时，则需要提示：“内部用户无法领用SO：运行备件”；
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
                }
            }

        } else {
            // 外部单位
            Boolean isSo = false, notSo = false;
            for (BizReceiptApplyBinDTO binDTO : headDTO.getBinList()) {
                if (binDTO.getExtend28().equals(Const.MAT_TYPE_SO)) {
                    isSo = true;
                } else {
                    notSo = true;
                }
            }
            String counterpartDeptCode = dicDeptDataWrap.getById(headDTO.getCounterpartDeptId()).getDeptCode();
            if (isSo && notSo) {
                // 若外部用户创建的领用申请中，除SO：运行备件以外，还有其他类型的物资，则提示“SO运行备件只能单独提报”；
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);

            } else if (isSo && !counterpartDeptCode.equals(EnumDept.CDE.getCode())) {
                // 若外部用户创建的领用申请中，只包含SO：运行备件，但对口部门及对口科室选择非工程部以及其他科室，则提示“SO：运行备件领用时，对口部门必须为工程管理部”；
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
            }
        }
    }

    /**
     * 需求库存信息自动匹配
     */
    public void getStock(BizContext ctx) {
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilNumber.isNull(po.getIsExact())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<BizBatchInfo> batchInfoList = new ArrayList<>();
        Map<String, BigDecimal> map = new HashMap<>();
        for (BizReceiptApplyItemDTO itemDTO : po.getItemList()) {
            BizBatchInfo bizBatchInfo = new BizBatchInfo();
            bizBatchInfo.setFunctionalLocationCode(itemDTO.getFuncNo());
            bizBatchInfo.setExtend20(itemDTO.getMatnr());
            batchInfoList.add(bizBatchInfo);
            if (UtilNumber.isEmpty(itemDTO.getAppliedQty())) {
                itemDTO.setAppliedQty(BigDecimal.ZERO);
            }

            // 优先匹配功能位置码, 功能位置码空时匹配物资编码
            String key = itemDTO.getFuncNo();
            if (UtilString.isNullOrEmpty(key)) {
                key = itemDTO.getMatnr();
            }
            if (map.isEmpty() || !map.containsKey(key)) {
                map.put(key, itemDTO.getPreQty().subtract(itemDTO.getAppliedQty()));
            } else {
                map.put(key, map.get(key).add(itemDTO.getPreQty().subtract(itemDTO.getAppliedQty())));
            }
        }

        List<BizReceiptApplyBinDTO> applyBinDTOList = new ArrayList<>();
        if (po.getIsExact().equals(Const.ONE)) {
            // 精准预留查询: 查询批次库存
            applyBinDTOList = bizReceiptApplyBinDataWrap.getStockBatchByBatchInfoList(batchInfoList);

        } else if (po.getIsExact().equals(Const.ZERO)) {
            // 非精准预留查询: 按照工厂、库存地点、WBS、主设备编码的展示维度，对CT物料编码、批次号、可预留量等数量进行聚合
            applyBinDTOList = bizReceiptApplyBinDataWrap.getStockByBatchInfoList(batchInfoList);
        }

        // 自动配货处理
        for (BizReceiptApplyBinDTO applyBinDTO : applyBinDTOList) {
            if (UtilNumber.isNotEmpty(applyBinDTO.getLockSubtractQty())) {
                applyBinDTO.setCanSubtractQty(applyBinDTO.getCanSubtractQty().subtract(applyBinDTO.getLockSubtractQty()));
            }
            if (UtilNumber.isEmpty(applyBinDTO.getAlreadyReserveQty())) {
                applyBinDTO.setAlreadyReserveQty(BigDecimal.ZERO);
            }
            applyBinDTO.setReserveQty(applyBinDTO.getStockQty().subtract(applyBinDTO.getAlreadyReserveQty()));
            // 优先匹配功能位置码, 功能位置码空时匹配物资编码
            String key = applyBinDTO.getFunctionalLocationCode();
            if (!map.containsKey(key)) {
                key = applyBinDTO.getExtend20();
            }
            BigDecimal maxQty = map.get(key);
            if (maxQty.compareTo(applyBinDTO.getReserveQty()) >= 0) {
                applyBinDTO.setQty(applyBinDTO.getReserveQty());
                map.put(key, maxQty.subtract(applyBinDTO.getQty()));

            } else {
                applyBinDTO.setQty(maxQty);
                map.put(key, BigDecimal.ZERO);
            }
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(applyBinDTOList));
    }

    public void initByRequire(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSubmit(true).setButtonSave(Boolean.TRUE);
        BizResultVO<BizReceiptApplyHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptApplyHeadDTO()
                        .setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue()).setIsExact(1)
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserCode(user.getUserCode())
                        .setCreateUserName(user.getUserName()), new ExtendVO(), buttonVO);
        if (UtilCollection.isNotEmpty(user.getUserDeptList())) {
            resultVO.getHead().setCreateDeptId(user.getUserDeptList().get(0).getDeptId());
            resultVO.getHead().setCreateOfficeId(user.getUserDeptList().get(0).getDeptOfficeId());
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public void initBySpilt(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSubmit(true);
        BizResultVO<BizReceiptApplyHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptApplyHeadDTO()
                        .setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue()).setIsExact(1)
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserCode(user.getUserCode())
                        .setCreateUserName(user.getUserName()), new ExtendVO(), buttonVO);
        if (UtilCollection.isNotEmpty(user.getUserDeptList())) {
            resultVO.getHead().setCreateDeptId(user.getUserDeptList().get(0).getDeptId());
            resultVO.getHead().setCreateOfficeId(user.getUserDeptList().get(0).getDeptOfficeId());
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveByRequire(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        boolean isNew = true;
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        CurrentUser user = ctx.getCurrentUser();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        String receiptCode = applyHeadDTO.getReceiptCode();
        String applyCode = applyHeadDTO.getReceiptNum() == null ? null : applyHeadDTO.getReceiptNum();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        // 补充转性标识
        applyHeadDTO.setTransferFlag(EnumRealYn.FALSE.getIntValue());
        // 处理头信息
        if (UtilNumber.isNotEmpty(applyHeadDTO.getId())) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(applyHeadDTO.getId());
            // 根据id更新
            bizReceiptApplyHeadDataWrap.updateDtoById(applyHeadDTO);
            // item物理删除
            QueryWrapper<BizReceiptApplyItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(BizReceiptApplyItem::getHeadId, applyHeadDTO.getId());
            bizReceiptApplyItemDataWrap.physicalDelete(itemQueryWrapper);
            // outInfo 根据id、headId更新
            QueryWrapper<BizReceiptOutputInfo> outInfoWrapper = new QueryWrapper<>();
            outInfoWrapper.lambda().eq(BizReceiptOutputInfo::getId, applyHeadDTO.getOutInfoId());
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .build();
            bizReceiptOutputInfoDataWrap.updateDto(bizReceiptOutputInfoDTO, outInfoWrapper);
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            /* ================ outInfo处理  领料申请单信息 ================ */
            applyCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_APPLY_CODE.getValue());
            Long outInfoId = applyHeadDTO.getOutInfoId();
            if (UtilNumber.isEmpty(outInfoId)) {
                outInfoId = null;
            }
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(outInfoId)
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
//                    .specStockCode(applyHeadDTO.getSpecStockCode())
//                    .specStockName(applyHeadDTO.getSpecStockName())
                    .whCodeOut(applyHeadDTO.getWhCodeOut())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .createTime(UtilDate.getNow())
                    .modifyTime(UtilDate.getNow())
                    .createUserId(user.getCreateUserId())
                    .modifyUserId(user.getModifyUserId())
                    .build();
            bizReceiptOutputInfoDataWrap.saveDto(bizReceiptOutputInfoDTO);
            /* ================ head处理 ================ */
            String ftyCode = applyHeadDTO.getItemList().get(0).getFtyCode();
            if (UtilString.isNullOrEmpty(ftyCode)) {
                ftyCode = applyHeadDTO.getBinList().get(0).getFtyCode();
            }
            // receiptCode = bizCommonService.getNextSeqMaterialReq(user,EnumSequenceCode.MATERIAL_REQ.getValue(), ftyCode);
            receiptCode = "SK" + applyHeadDTO.getUnit() + "-CWH5" + DateUtil.thisYear() + "-APS-" + DateUtil.format(new Date(), "MM") + bizCommonService.getNextSequenceCodeMonth(EnumSequenceCode.SEQUENCE_UNITIZED_MAT_REQ_APPLY.getValue());
            Date now = UtilDate.getNow();
            applyHeadDTO.setCreateTime(now);
            applyHeadDTO.setModifyTime(now);
            applyHeadDTO.setReceiptCode(receiptCode);
            applyHeadDTO.setCreateUserId(createUserId);
            applyHeadDTO.setModifyUserId(createUserId);
//            applyHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
            applyHeadDTO.setReceiptStatus(status);
            applyHeadDTO.setOutInfoId(bizReceiptOutputInfoDTO.getId());
            applyHeadDTO.setRemark(applyHeadDTO.getRemark());
            applyHeadDTO.setId(null);
            applyHeadDTO.setReceiptNum(applyCode);
            bizReceiptApplyHeadDataWrap.saveDto(applyHeadDTO);
            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }
        // item处理
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            BigDecimal temp = BigDecimal.ZERO;
            if(UtilCollection.isNotEmpty(itemDTO.getTransferDTOList())){
                for (BizReceiptApplyTransferDTO assembleDTO : itemDTO.getTransferDTOList()) {
                    temp = temp.add(assembleDTO.getQty());
                }
                itemDTO.setQty(temp);
            }
            itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            itemDTO.setId(null);
            itemDTO.setHeadId(applyHeadDTO.getId());
            itemDTO.setItemStatus(status);
            itemDTO.setCreateTime(createTime);
            itemDTO.setCreateUserId(createUserId);
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setMatId(dictionaryService.getMatIdByMatCode(itemDTO.getMatCode()));
            Long locationId = itemDTO.getLocationId();
            if(UtilNumber.isEmpty(locationId)){
                locationId = applyHeadDTO.getBinList().get(0).getLocationId();
            }
            itemDTO.setWhId(dictionaryService.getLocationCacheById(locationId).getWhId());
            itemDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);
        // bin处理
        List<BizReceiptApplyBinDTO> binList = applyHeadDTO.getBinList();
        if (UtilCollection.isNotEmpty(binList)) {
            AtomicInteger bid = new AtomicInteger(1);
            for (BizReceiptApplyBinDTO binDTO : binList) {
                binDTO.setId(null);
                binDTO.setBid(String.valueOf(bid.getAndIncrement()));
                binDTO.setHeadId(applyHeadDTO.getId());
            }
            bizReceiptApplyBinDataWrap.physicalDelete(new QueryWrapper<BizReceiptApplyBin>().lambda().eq(BizReceiptApplyBin::getHeadId, applyHeadDTO.getId()));
            bizReceiptApplyBinDataWrap.saveBatchDto(binList);
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, applyHeadDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, applyHeadDTO.getId());
    }

    /**
     * 占用库存
     */
    public void saveOccupy(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilNumber.isNull(headDTO.getIsExact())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);

        } else if (headDTO.getIsExact().equals(Const.ONE)) {
            // 精准预留占用批次库存
            List<StockOccupy> list = new ArrayList<>();
            for (BizReceiptApplyBinDTO binDTO : headDTO.getBinList()) {
                StockOccupy stockOccupy = new StockOccupy();
                stockOccupy.setStockBatchId(binDTO.getStockBatchId());
                stockOccupy.setStockBinId(0L);
                stockOccupy.setQty(binDTO.getQty());
                stockOccupy.setReceiptType(headDTO.getReceiptType());
                stockOccupy.setReceiptHeadId(binDTO.getHeadId());
                stockOccupy.setReceiptItemId(binDTO.getItemId());
                stockOccupy.setReceiptBinId(binDTO.getId());
                list.add(stockOccupy);
            }
            stockOccupyDataWrap.saveBatch(list);
        }
    }

    /**
     * 锁定冲减
     */
    public void saveSubtract(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 精准预留占用批次库存
        List<StockOver> list = new ArrayList<>();
        for (BizReceiptApplyBinDTO binDTO : headDTO.getBinList()) {
            if (UtilNumber.isNotEmpty(binDTO.getSubtractQty())) {
                StockOver stockOver = new StockOver();
                stockOver.setFtyId(binDTO.getFtyId());
                stockOver.setLocationId(binDTO.getLocationId());
                stockOver.setMatId(binDTO.getMatId());
                stockOver.setBatchId(binDTO.getBatchId());
                stockOver.setQty(binDTO.getSubtractQty());
                stockOver.setDebitCredit(Const.CREDIT_L_SUBTRACT);
                stockOver.setReceiptType(headDTO.getReceiptType());
                stockOver.setReceiptHeadId(binDTO.getHeadId());
                stockOver.setReceiptItemId(binDTO.getItemId());
                stockOver.setReceiptBinId(binDTO.getId());
                list.add(stockOver);
            }
        }
        if (UtilCollection.isNotEmpty(list)) {
            stockOverDataWrap.saveBatch(list);
        }
    }

    /**
     * 前序单据相关信息更新 : 占用,锁定冲减,已拆分数量
     */
    public void updatePreBySplit(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 前序单据idList
        List<Long> preApplyBinIdList = headDTO.getBinList().stream().map(o -> o.getPreApplyBinId()).collect(Collectors.toList());
        // 前序单据list
        List<BizReceiptApplyBin> preBinList = bizReceiptApplyBinDataWrap.listByIds(preApplyBinIdList);
        // 前序单据占用list
        List<StockOccupy> occupyList = stockOccupyDataWrap.list(new QueryWrapper<StockOccupy>().lambda().in(StockOccupy::getReceiptBinId, preApplyBinIdList).eq(StockOccupy::getReceiptType, headDTO.getReceiptType()));
        // 前序单据锁定冲减list
        List<StockOver> overList = stockOverDataWrap.list(new QueryWrapper<StockOver>().lambda().in(StockOver::getReceiptBinId, preApplyBinIdList).eq(StockOver::getDebitCredit, Const.CREDIT_L_SUBTRACT).eq(StockOver::getReceiptType, headDTO.getReceiptType()));
        for (BizReceiptApplyBinDTO binDTO : headDTO.getBinList()) {
            for (BizReceiptApplyBin preBin : preBinList) {
                if (binDTO.getPreApplyBinId().equals(preBin.getId())) {
                    // 已拆分数量更新
                    preBin.setAlreadySplitQty(preBin.getAlreadySplitQty().add(binDTO.getQty()));
                }
            }
            if (UtilCollection.isNotEmpty(occupyList)) {
                for (StockOccupy preOccupy : occupyList) {
                    if (binDTO.getPreApplyBinId().equals(preOccupy.getReceiptBinId())) {
                        // 占用数量更新
                        preOccupy.setQty(preOccupy.getQty().subtract(binDTO.getQty()));
                    }
                }
            }
            if (UtilCollection.isNotEmpty(overList)) {
                for (StockOver preOver : overList) {
                    if (binDTO.getPreApplyBinId().equals(preOver.getReceiptBinId())) {
                        // 锁定冲减更新
                        preOver.setQty(preOver.getQty().subtract(binDTO.getQty()));
                    }
                }
            }
        }
        bizReceiptApplyBinDataWrap.updateBatchById(preBinList);
        if (UtilCollection.isNotEmpty(occupyList)) {
            stockOccupyDataWrap.updateBatchById(occupyList);
            stockOccupyDataWrap.physicalDelete(new QueryWrapper<StockOccupy>().lambda().eq(StockOccupy::getQty, 0));
        }
        if (UtilCollection.isNotEmpty(overList)) {
            stockOverDataWrap.updateBatchById(overList);
            stockOverDataWrap.physicalDelete(new QueryWrapper<StockOver>().lambda().eq(StockOver::getQty, 0));
        }
    }

    /**
     * 释放占用库存
     */
    public void deleteOccupy(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        stockOccupyDataWrap.physicalDelete(new QueryWrapper<StockOccupy>().lambda().eq(StockOccupy::getReceiptHeadId, headDTO.getId()).eq(StockOccupy::getReceiptType, headDTO.getReceiptType()));
    }

    /**
     * 释放锁定的冲减数量
     */
    public void deleteSubtract(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        stockOverDataWrap.physicalDelete(new QueryWrapper<StockOver>().lambda().eq(StockOver::getReceiptHeadId, headDTO.getId()));
    }

    /**
     * 当选择拆分时，则通过审批后，不生成下游单据，需要用户在“拆分领用申请”功能中进一步拆分；
     * 当选择不拆分时，则需要调用SAP生成预留信息（传参与现有成套设备领料申请一致），并且直接生成领料出库单
     */
    public void saveByIsSplit(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilNumber.isNull(headDTO.getIsSplit())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);

        }
        // 发起审批
        this.startWorkFlow(ctx);
    }

    /**
     * SAP过账以及后续处理
     */
    public void postByRequire(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // SAP生成预留信息
        boolean completeFlag = this.reserved2SAP(ctx);
        if (!completeFlag) {
            // 未同步
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            return;
        }
        // 已完成
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 自动创建出库单
        this.saveOutput(ctx);
    }

    /**
     * SAP生成预留信息
     */
    public boolean reserved2SAP(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        for (BizReceiptApplyBinDTO itemDTO : headDTO.getBinList()) {
            // 构建创建预留单的参数
            BizReceiptApplyHeadDTO headDTOTemp = new BizReceiptApplyHeadDTO();
            headDTOTemp.setReceiptNum(headDTO.getReceiptNum());
            headDTOTemp.setCreateTime(headDTO.getCreateTime());
            headDTOTemp.setCreateUserCode(headDTO.getCreateUserCode());
            headDTOTemp.setApplyTime(headDTO.getCreateTime());
            headDTOTemp.setWhCodeOut(headDTO.getWhCodeOut());
            headDTOTemp.setReceiptCode(headDTO.getReceiptCode());
            headDTOTemp.setReceiptType(headDTO.getReceiptType());
            List<BizReceiptApplyItemDTO> listTemp = new ArrayList<>();
            BizReceiptApplyItemDTO itemDTOTemp = new BizReceiptApplyItemDTO();
            String rid = itemDTO.getBid();
            itemDTOTemp.setRid(rid);
            // 创建预留得时候，不同步sap使用了此rid
//            itemDTO.setReservedOrderRid(rid);
            itemDTOTemp.setFtyCode(itemDTO.getFtyCode());
            itemDTOTemp.setLocationCode(itemDTO.getLocationCode());
            itemDTOTemp.setSpecStock(itemDTO.getSpecStock());
            if (UtilNumber.isNotEmpty(itemDTO.getMatId())) {
                DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(itemDTO.getMatId());
                DicMaterialDTO parentMat = dictionaryService.getMatCacheById(dicMaterialDTO.getParentMatId());
                itemDTOTemp.setMatCode(parentMat.getMatCode());

            } else {
                // 非精准预留没有选择具体的物料编码
                itemDTOTemp.setMatCode(itemDTO.getParentMatCode());
            }
            itemDTOTemp.setRidReserved("1");
            itemDTOTemp.setRidReservedNum(1);
            // 计算行项目金额, 对应SAP数量
            if (UtilNumber.isEmpty(itemDTO.getPrice())) {
                continue;
            }
            itemDTOTemp.setQty(itemDTO.getQty().multiply(itemDTO.getPrice()).add(itemDTO.getRemainder()));
            String whCodeOut = null;
            WmsQueryWrapper<ErpPurchaseReceiptItem> purchaseReceiptItemWmsQueryWrapper = new WmsQueryWrapper<>();
            purchaseReceiptItemWmsQueryWrapper.lambda().eq(ErpPurchaseReceiptItem::getSpecStockCode, itemDTO.getSpecStockCode());
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(purchaseReceiptItemWmsQueryWrapper);
            if (CollectionUtils.isEmpty(erpPurchaseReceiptItemList)) {
                SapWbs erpWbs = erpWbsService.findByCode(itemDTO.getSpecStock());
                if (erpWbs == null) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_WBS_CODE_GET_FAILED);
                } else {
                    whCodeOut = erpWbs.getWbsCodeIn();
                }
            } else {
                ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemList.get(0);
                whCodeOut = erpPurchaseReceiptItem.getWhCodeOut();
            }
            headDTOTemp.setWhCodeOut(whCodeOut);

            if (UtilNumber.isNotEmpty(itemDTO.getQty())) {
                listTemp.add(itemDTOTemp);
            }
            if (UtilCollection.isEmpty(listTemp)) {
                continue;
            }
            headDTOTemp.setItemList(listTemp);
            ReserveReceiptCreatePO reserveReceiptCreatePO = ReserveReceiptCreatePO.builder().bizReceiptApplyHeadDTO(headDTOTemp).build();
            ReserveReceiptCreatePO sapCreateReserveReceiptInfo = reserveReceiptService.createReserveReceiptItemListNew(reserveReceiptCreatePO, ctx.getCurrentUser());
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(sapCreateReserveReceiptInfo.getSuccess())) {
                List<BizReceiptApplyItemDTO> receiptApplyItemDTOList = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList();
                for (BizReceiptApplyItemDTO dto : receiptApplyItemDTOList) {
                    if (itemDTO.getBid().equals(dto.getRid())) {
                        itemDTO.setReservedOrderCode(dto.getReservedOrderCode());
                        itemDTO.setReservedOrderRid(dto.getReservedOrderRid());
                        // 更新预留单号和行号
                        bizReceiptApplyBinDataWrap.updateDtoById(itemDTO);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 自动创建出库单
     */
    public void saveOutput(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> receiptOutputItemDTOList = new ArrayList<>();
        for (BizReceiptApplyItemDTO item : headDTO.getItemList()) {
            BizReceiptOutputItemDTO receiptOutputItemDTO = BizReceiptOutputItemDTO.builder()
                    // 这里没有配置领料申请的行项目的rid
                    .matId(item.getMatId())
                    .ftyId(item.getFtyId())
                    .ftyCode("")
                    .locationId(item.getLocationId())
                    .mainLocationId(item.getMainLocationId())
                    .whId(item.getWhId())
                    .preReceiptQty(item.getQty())
                    .receiptQty(item.getQty())
                    //操作数量，单价，金额
                    .qty(item.getQty())
                    .price(item.getPrice())
                    .amount(item.getAmount())
                    // 已退库数量
                    .returnQty(BigDecimal.ZERO)
                    // 已作业数量
                    .taskQty(BigDecimal.ZERO)
                    // 已完成数量
                    .finishQty(BigDecimal.ZERO)
                    // 特殊库存标识
                    .specStock(item.getSpecStock())
                    .unitId(item.getUnitId())
                    // 前续单据head id
                    .preReceiptHeadId(item.getHeadId())
                    // 前序单据item表id
                    .preReceiptItemId(item.getId())
                    // 前序单据类型
                    .preReceiptType(headDTO.getReceiptType())
                    .referReceiptHeadId(item.getReferReceiptHeadId())
                    .referReceiptItemId(item.getReferReceiptItemId())
                    .referReceiptType(item.getReferReceiptType())
                    .requireReceiptHeadId(item.getPreReceiptHeadId())
                    // 行项目状态
                    .itemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .createTime(UtilDate.getNow())
                    .createUserId(item.getCreateUserId())
                    .build();
            receiptOutputItemDTOList.add(receiptOutputItemDTO);
        }
        BizReceiptOutputHeadDTO receiptOutputHeadDTO = BizReceiptOutputHeadDTO.builder()
                .itemDTOList(receiptOutputItemDTOList)
                .remark(headDTO.getRemark())
                .des(headDTO.getDes())
                .receiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue()).build();
        // 将领料申请信息保存到上下文中
        log.info("领料申请单保存的数据:{}", JSON.toJSONString(receiptOutputHeadDTO));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptOutputHeadDTO);
        unitizedMaterialOutputService.save(ctx);
    }

    public void getPageByApply(BizContext ctx) {
        BizReceiptApplyQueryListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(StringUtils.isNotEmpty(po.getChildMatCode())){
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            po.setMatId(matId);
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptApplyQueryListPO> wmsQueryWrapper = new WmsQueryWrapper<>();
        wmsQueryWrapper.lambda()
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getReceiptType, BizReceiptApplyHead.class, po.getReceiptType())
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getIsSplit, BizReceiptApplyHead.class, 1)
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getIsDelete, BizReceiptApplyHead.class, 0)
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptApplyQueryListPO::getReceiptStatus, BizReceiptApplyHead.class, po.getReceiptStatusList())
                .eq(UtilString.isNotNullOrEmpty(po.getPreApplyReceiptCode()), BizReceiptApplyQueryListPO::getReceiptCode, BizReceiptApplyHead.class, po.getPreApplyReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptApplyQueryListPO::getReceiptCode, BizReceiptApplyHead.class, po.getReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getMatReceiver()), BizReceiptApplyQueryListPO::getMatReceiver, BizReceiptOutputInfo.class, po.getMatReceiver())
                .eq(UtilString.isNotNullOrEmpty(po.getMatDept()), BizReceiptApplyQueryListPO::getMatDept, BizReceiptOutputInfo.class, po.getMatDept())
                .eq(UtilString.isNotNullOrEmpty(po.getChildMatCode()), BizReceiptApplyQueryListPO::getMatCode, DicMaterial.class, po.getChildMatCode())
                .eq(UtilNumber.isNotNull(po.getMatId()), BizReceiptApplyQueryListPO::getParentMatId, DicMaterial.class, po.getMatId())
                .between(UtilObject.isNotNull(po.getApplyStartTime()) && UtilObject.isNotNull(po.getApplyEndTime()),
                        BizReceiptApplyQueryListPO::getApplyTime, BizReceiptOutputInfo.class, po.getApplyStartTime(), po.getApplyEndTime())
                .between(UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime()),
                        BizReceiptApplyQueryListPO::getCreateTime, BizReceiptApplyHead.class, po.getStartTime(), po.getEndTime())
                .like(UtilString.isNotNullOrEmpty(po.getUserName()), BizReceiptApplyQueryListPO::getUserName, SysUser.class, po.getUserName())
                .eq(UtilNumber.isNotEmpty(po.getIsExact()), BizReceiptApplyQueryListPO::getIsExact, BizReceiptApplyHead.class, po.getIsExact())
                .eq(UtilNumber.isNotEmpty(po.getIsSplit()), BizReceiptApplyQueryListPO::getIsSplit, BizReceiptApplyHead.class, po.getIsSplit())
                .eq(UtilString.isNotNullOrEmpty(po.getMatnr()), BizReceiptApplyQueryListPO::getMatnr, BizReceiptRequireItem.class, po.getMatnr())
                .eq(UtilString.isNotNullOrEmpty(po.getFuncNo()), BizReceiptApplyQueryListPO::getFuncNo, BizReceiptRequireItem.class, po.getFuncNo())
                .eq(UtilString.isNotNullOrEmpty(po.getPreReceiptCode()), BizReceiptApplyQueryListPO::getReceiptCode, BizReceiptRequireHead.class, po.getPreReceiptCode())
        ;
        Page<BizReceiptApplyPageVO> pageObj = (Page<BizReceiptApplyPageVO>) po.getPageObj(BizReceiptApplyPageVO.class);
        pageObj.setOptimizeCountSql(false);
        bizReceiptApplyHeadDataWrap.getApplyInfoPageVOListUnitized(pageObj, wmsQueryWrapper);
        long totalCount = pageObj.getTotal();
        List<BizReceiptApplyPageVO> voList = pageObj.getRecords();
        List<BizReceiptApplyHeadDTO> headDTOList = UtilCollection.toList(voList, BizReceiptApplyHeadDTO.class);
        if(UtilCollection.isEmpty(headDTOList)){
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(headDTOList, totalCount));
            return;
        }
        dataFillService.fillAttr(headDTOList);
        for (BizReceiptApplyHeadDTO headDTO : headDTOList) {
            if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
                for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
                    itemDTO.setPreCreateUserName(sysUserDataWrap.getById(itemDTO.getPreCreateUserId()).getUserName());
                }
            }
            for (BizReceiptApplyBinDTO binDTO : headDTO.getBinList()) {
                binDTO.setPreApplyHeadId(headDTO.getId());
                binDTO.setPreApplyBinId(binDTO.getId());
                binDTO.setPreQty(binDTO.getQty());
                if (UtilNumber.isNotEmpty(binDTO.getAlreadySplitQty())) {
                    // 本次拆分数量默认“本次申请数量”-“已拆分数量”
                    binDTO.setQty(binDTO.getPreQty().subtract(binDTO.getAlreadySplitQty()));
                }
            }
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(headDTOList, totalCount));
    }
}
