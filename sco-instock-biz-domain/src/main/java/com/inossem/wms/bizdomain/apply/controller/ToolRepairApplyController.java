package com.inossem.wms.bizdomain.apply.controller;

import com.inossem.wms.bizdomain.apply.service.biz.ToolRepairApplyOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPreVO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptToolVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.apply.BorrowTypeMapVO;
import com.inossem.wms.common.model.common.enums.apply.ToolStatusMapVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 维修申请
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "维修管理-维修申请")
public class ToolRepairApplyController {

    @Autowired
    private ToolRepairApplyOutputService toolRepairApplyOutputService;


    @ApiOperation(value = "维修申请创建", tags = {"维修管理-维修申请"})
    @PostMapping(value = "/repair/apply/init")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> init(BizContext ctx) {
        toolRepairApplyOutputService.init(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "维修申请查询列表（分页）", tags = {"维修管理-维修申请"})
    @PostMapping(value = "/repair/apply/results")
    public BaseResult<PageObjectVO<BizReceiptApplyPageVO>> getPage(@RequestBody BizReceiptApplySearchPO po,
                                                                   BizContext ctx) {
        toolRepairApplyOutputService.getPage(ctx);
        PageObjectVO<BizReceiptApplyPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "维修申请查询工具列表", tags = {"维修管理-维修申请"})
    @PostMapping(value = "/repair/apply/tool-item/results")
    public BaseResult<SingleResultVO<MatStockDTO>>
        getReserveReceiptItemList(@RequestBody BizReceiptApplySearchMatPO po, BizContext ctx) {
        toolRepairApplyOutputService.getToolItemList(ctx);
        SingleResultVO<MatStockDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "维修申请详情", tags = {"维修管理-维修申请"})
    @GetMapping(value = "/repair/apply/{id}")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        toolRepairApplyOutputService.getInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "维修申请保存", tags = {"维修管理-维修申请"})
    @PostMapping(value = "/repair/apply/save")
    public BaseResult save(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        toolRepairApplyOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "维修申请单提交", tags = {"维修管理-维修申请"})
    @PostMapping(value = "/repair/apply/submit")
    public BaseResult submit(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        toolRepairApplyOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "维修申请删除", tags = {"维修管理-维修申请"})
    @DeleteMapping(value = "/repair/apply/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        toolRepairApplyOutputService.delete(ctx);
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_DELETE_SUCCESS, headDTO.getReceiptCode());
    }



}