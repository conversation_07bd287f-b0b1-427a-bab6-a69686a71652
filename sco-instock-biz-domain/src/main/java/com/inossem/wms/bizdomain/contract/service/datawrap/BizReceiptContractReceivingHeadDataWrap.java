package com.inossem.wms.bizdomain.contract.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.contract.dao.BizReceiptContractReceivingHeadMapper;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractReceivingHead;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractReceivingSearchPO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractReceivingPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/29
 */
@Service
public class BizReceiptContractReceivingHeadDataWrap extends BaseDataWrap<BizReceiptContractReceivingHeadMapper, BizReceiptContractReceivingHead> {

    public IPage<BizReceiptContractReceivingPageVO> selectPage(IPage<BizReceiptContractReceivingPageVO> pageData,
                                                                WmsQueryWrapper<BizReceiptContractReceivingSearchPO> pageWrapper) {
        return pageData.setRecords(this.baseMapper.selectPage(pageData, pageWrapper));
    }
}
