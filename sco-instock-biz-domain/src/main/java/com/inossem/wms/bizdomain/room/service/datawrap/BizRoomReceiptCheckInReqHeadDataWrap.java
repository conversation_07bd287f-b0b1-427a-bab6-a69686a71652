package com.inossem.wms.bizdomain.room.service.datawrap;

import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptCheckInReqHead;
import com.inossem.wms.bizdomain.room.dao.BizRoomReceiptCheckInReqHeadMapper;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 住房申请单抬头表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class BizRoomReceiptCheckInReqHeadDataWrap extends BaseDataWrap<BizRoomReceiptCheckInReqHeadMapper, BizRoomReceiptCheckInReqHead> {

}
