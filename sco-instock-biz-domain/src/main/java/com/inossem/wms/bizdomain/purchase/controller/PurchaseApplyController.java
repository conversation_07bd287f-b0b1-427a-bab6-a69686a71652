package com.inossem.wms.bizdomain.purchase.controller;

import com.inossem.wms.bizdomain.purchase.service.biz.PurchaseApplyService;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplySearchPO;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplyUpdatePO;
import com.inossem.wms.common.model.bizdomain.purchase.vo.BizReceiptPurchaseApplyListVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.apply.BudgetClassMapVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购申请Controller
 * 
 * <AUTHOR>
 * @since 2024-10-31
 */
@Slf4j
@RestController
@Api(tags = "采购申请管理")
public class PurchaseApplyController {

    @Autowired
    private PurchaseApplyService purchaseApplyService;

    /**
     * 采购申请-获取预算分类预算科目列表
     */
    @ApiOperation(value = "获取预算分类预算科目列表", tags = {"采购申请管理"})
    @PostMapping("/purchase-applies/budgetList")
    public BaseResult<MultiResultVO<BudgetClassMapVO>> getEnumBudgetList(BizContext ctx) {
        purchaseApplyService.getEnumBudgetList(ctx);
        MultiResultVO<BudgetClassMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购申请分页查询
     */
    @ApiOperation(value = "采购申请分页查询", tags = {"采购申请管理"})
    @PostMapping("/purchase-applies/results")
    @In(parameter = "BizReceiptPurchaseApplySearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyListVO>")
    public BaseResult<PageObjectVO<BizReceiptPurchaseApplyListVO>> getPurchaseApplyPageVo(
            @RequestBody BizReceiptPurchaseApplySearchPO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        purchaseApplyService.getPurchaseApplyPageVo(ctx);
        PageObjectVO<BizReceiptPurchaseApplyListVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购申请初始化
     */
    @ApiOperation(value = "采购申请初始化", tags = {"采购申请管理"})
    @PostMapping("/purchase-applies/init")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> init(BizContext ctx) {
        purchaseApplyService.init(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购申请详情
     */
    @ApiOperation(value = "采购申请详情", tags = {"采购申请管理"})
    @GetMapping("/purchase-applies/{id}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> getPurchaseApplyDetail(
            @PathVariable("id") Long id, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        purchaseApplyService.getPurchaseApplyDetail(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购申请详情
     */
    @ApiOperation(value = "采购申请详情", tags = {"采购申请管理"})
    @GetMapping("/purchase-applies/{id}/{taskId}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> getPurchaseApplyDetail(
            @PathVariable("id") Long id, @PathVariable("taskId") String taskId, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        purchaseApplyService.getPurchaseApplyDetail(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购申请保存
     */
    @ApiOperation(value = "采购申请保存", tags = {"采购申请管理"})
    @PostMapping("/purchase-applies/save")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"receiptType", "applyUserId", "applyDeptId", "planArrivalDate"})
    @Out(parameter = "String")
    public BaseResult<String> save(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        purchaseApplyService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 采购申请提交
     */
    @ApiOperation(value = "采购申请提交", tags = {"采购申请管理"})
    @PostMapping("/purchase-applies/submit")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> submit(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        purchaseApplyService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 采购申请过账srm
     */
    @ApiOperation(value = "采购申请过账", tags = {"采购申请管理"})
    @PostMapping("/purchase-applies/post")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> post(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        purchaseApplyService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 采购申请删除
     */
    @ApiOperation(value = "采购申请删除", tags = {"采购申请管理"})
    @DeleteMapping("/purchase-applies/{id}")
    @In(parameter = "id", required = "id")
    public BaseResult<String> delete(@PathVariable("id") Long id, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        purchaseApplyService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 获取需求计划行项目列表
     */
    @ApiOperation(value = "获取需求计划行项目列表", tags = {"采购申请管理"})
    @PostMapping("/purchase-applies/demand-plan-items")
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyItemDTO>")
    public BaseResult<PageObjectVO<BizReceiptPurchaseApplyItemDTO>> getDemandPlanItems(
            @RequestBody BizReceiptDemandPlanSearchPO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        purchaseApplyService.getDemandPlanItems(ctx);
        PageObjectVO<BizReceiptPurchaseApplyItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 取消采购申请行项目
     */
    @ApiOperation(value = "取消采购申请行项目")
    @PostMapping("/purchase-applies/cancelItems")
    public BaseResult<String> cancelItems(@RequestBody BizReceiptPurchaseApplyUpdatePO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        purchaseApplyService.cancel(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }


    /**
     * 文件上传功能
     *
     * @param fileInClient 文件
     * @return 上传后文件信息
     */
    @ApiOperation(value = "文件上传", tags = {"附件管理"})
    @PostMapping(path = "/purchase-applies/file/upload", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizCommonFile> upload(@RequestPart("file") MultipartFile fileInClient, CurrentUser user) {
        return BaseResult.success(purchaseApplyService.upload(fileInClient, user));
    }

    @ApiOperation(value = "撤销")
    @PostMapping(value = "/purchase-applies/revoke")
    public BaseResult<?> revoke(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        purchaseApplyService.revoke(ctx);
        return BaseResult.success();
    }
} 
