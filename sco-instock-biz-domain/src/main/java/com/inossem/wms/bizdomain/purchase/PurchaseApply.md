# 采购申请功能开发文档

## 1. 功能概述

采购申请(Purchase Apply)是用于管理和跟踪企业采购需求的业务功能,支持从需求计划整合生成采购申请。主要功能包括采购申请的创建、修改、删除、提交、审批等全生命周期管理。

## 2. 系统架构

### 2.1 技术架构
- 后端框架：Spring Boot
- 持久层：MyBatis-Plus
- 数据库：MySQL
- 工作流：自定义工作流引擎
- 消息队列：用于审批回调处理

### 2.2 模块划分
- Controller层：`PurchaseApplyController` 
  - 处理HTTP请求和参数校验
  - 使用@In/@Out注解标记入参出参
  - 统一使用BizContext传递上下文
  - 所有接口返回BaseResult封装

- Service层：
  - `PurchaseApplyService`
    - 业务逻辑编排,事务控制
    - 使用@Entrance注解标记调用链
    - 使用@Transactional控制事务
    - 通过Component层实现具体业务
  - `BizCommonService` 
    - 提供单号生成服务
    - 基于EnumSequenceCode生成规则
  - `DataFillService`
    - 处理关联属性填充
    - 支持@RlatAttr和@SonAttr注解
  - `DictionaryService`
    - 提供字典数据服务
    - 包括物料、单位等基础数据

- Component层：
  - `PurchaseApplyComponent`
    - 采购申请核心业务实现
    - 包含数据校验逻辑
    - 实现状态流转控制
    - 处理需求计划整合
  - `WorkflowService`
    - 工作流引擎服务
    - 处理审批流程
    - 提供回调处理
  - `ReceiptOperationLogService`
    - 操作日志记录服务
    - 跟踪关键操作
  - `ReceiptAttachmentService`
    - 附件管理服务
    - 支持多文件上传
  - `ReceiptRelationService`
    - 单据关系管理
    - 维护单据流转记录

- 数据访问层：
  - `BizReceiptPurchaseApplyHeadDataWrap`
    - 继承WmsBaseMapper
    - 实现头表CRUD操作
  - `BizReceiptPurchaseApplyItemDataWrap`
    - 继承WmsBaseMapper
    - 实现行项目CRUD操作

## 3. 数据模型

### 3.1 基础对象
#### BaseResult
通用返回对象:
- code: 返回码
  - SUCCESS(200): 成功
  - PARAM_ERROR(400): 参数错误
  - UNAUTHORIZED(401): 未授权
  - SERVER_ERROR(500): 服务器错误
- msg: 返回消息
- data: 返回数据
- success: 是否成功

#### BizContext
业务上下文对象:
- currentUser: 当前用户
  - id: 用户ID
  - userCode: 用户编码
  - userName: 用户名称
  - deptId: 部门ID
- contextData: 上下文数据Map
  - BIZ_CONTEXT_KEY_PO: 请求参数
  - BIZ_CONTEXT_KEY_VO: 返回数据
  - BIZ_CONTEXT_KEY_ID: 主键ID
  - BIZ_CONTEXT_KEY_CODE: 单据编号
  - BIZ_CONTEXT_OPERATION_LOG_TYPE: 操作类型

#### PageObjectVO
分页对象:
- pageIndex: 当前页码
- pageSize: 每页大小
- total: 总记录数
- rows: 数据列表

#### BizResultVO
业务结果对象:
- data: 业务数据
- extendVO: 扩展信息
- buttonVO: 按钮权限

#### BizReceiptPurchaseApplySearchPO
采购申请查询对象:
- pageIndex: 页码
- pageSize: 每页大小
- receiptCode: 单据编号
- receiptStatusList: 单据状态列表
- purchaseType: 采购类型
- sendType: 采购类别
- purchaseDescription: 采购申请描述
- createTimeStart: 创建开始时间
- createTimeEnd: 创建结束时间
- createUserCode: 创建人编码
- createUserName: 创建人名称

### 3.2 采购申请头表(biz_receipt_purchase_apply_head)
主要字段：
- id：主键
- receipt_code：单据编号(CG开头)
- receipt_type：单据类型(固定为300:采购申请)
- receipt_status：单据状态
  - 10: 草稿 - 新建或修改保存的状态
  - 15: 审批中 - 工作流审批过程中的状态
  - 20: 已提交 - 提交到工作流前的状态
  - 90: 已完成 - 审批通过后的状态 
  - 5: 已驳回 - 审批驳回后的状态
  - 91: 已作废 - 已提交或已驳回状态下取消
- purchase_type：采购类型
  - 1: 生产物资类 - 用于生产过程的物资采购
  - 2: 非生产物资类 - 非生产用途的物资采购
  - 3: 服务类 - 服务采购
  - 4: 施工类 - 施工相关采购
  - 5: 资产类 - 固定资产采购
- send_type：采购类别
  - 1: 离岸采购 - 境外采购
  - 2: 在岸采购 - 境内采购
  - 3: 油品采购 - 油品专用采购
- purchase_description：采购申请描述
- remark：备注
- create_time：创建时间
- create_user_id：创建人ID
- modify_time：修改时间
- modify_user_id：修改人ID
- is_delete：是否删除(1:是,0:否)

### 3.3 采购申请明细表(biz_receipt_purchase_apply_item)
主要字段：
- id：主键
- head_id：头表ID
- rid：行号(4位数字,10递增)
- mat_id：物料ID
- unit_id：单位ID
- fty_id：工厂ID
- demand_qty：需求数量
- demand_date：需求日期
- item_status：行项目状态
  - 10: 草稿 - 新建或修改时的状态
  - 20: 已提交 - 提交后的状态
  - 91: 已作废 - 头表作废时行项目状态
- item_remark：行项目备注
- pre_receipt_type：前序单据类型
- pre_receipt_id：前序单据ID
- pre_receipt_item_id：前序单据行项目ID
- pre_receipt_code：前序单据编号
- pre_receipt_rid：前序单据行号
- un_cleared_qty：未清数量
- contract_qty：已创建合同数量
- contract_change_qty：合同变更数量
- stock_qty：库存数量
- last_year_purchase_qty：去年采购数量
- last_year_consume_qty：去年消耗数量
- delivery_qty：已送货数量
- input_qty：已入库数量
- demand_dept_id：需求部门ID
- demand_user_id：需求人ID
- create_time：创建时间
- create_user_id：创建人ID
- modify_time：修改时间
- modify_user_id：修改人ID
- is_delete：是否删除(1:是,0:否)

### 3.4 状态流转规则

#### 3.4.1 头表状态流转
```mermaid
stateDiagram-v2
    [*] --> 草稿:新建
    草稿 --> 已提交:提交
    已提交 --> 审批中:发起审批
    已提交 --> 已作废:作废
    审批中 --> 已完成:审批通过
    审批中 --> 已驳回:审批驳回
    已驳回 --> 已提交:重新提交
    已驳回 --> 已作废:作废
```

状态流转规则:
1. 新建时默认为草稿状态(10)
2. 草稿状态可以:
   - 修改、删除
   - 提交进入已提交状态(20)
3. 已提交状态可以:
   - 发起审批进入审批中状态(15)
   - 作废进入已作废状态(91)
4. 审批中状态:
   - 审批通过进入已完成状态(90)
   - 审批驳回进入已驳回状态(5)
5. 已驳回状态可以:
   - 修改后重新提交进入已提交状态(20)
   - 作废进入已作废状态(91)
6. 已完成(90)和已作废(91)为终态,不可变更

#### 3.4.2 行项目状态流转
```mermaid
stateDiagram-v2
    [*] --> 草稿:新建
    草稿 --> 已提交:头表提交
    已提交 --> 已作废:头表作废
```

状态流转规则:
1. 新建时默认为草稿状态(10)
2. 头表提交后,行项目进入已提交状态(20)
3. 头表作废时,所有行项目进入已作废状态(91)
4. 已作废(91)为终态,不可变更

#### 3.4.3 业务规则

1. 采购类型相关规则:
   - 生产物资类:
     - 必填物料信息(mat_id)
     - 物料必须是有效状态
   - 非生产物资类:
     - 必填物料信息(mat_id)
     - 必填需求部门
   - 服务类:
     - 必填服务描述
     - 必填需求部门
   - 施工类:
     - 必填施工项目
     - 必填工程编号
   - 资产类:
     - 必填资产信息
     - 必填物料组信息

2. 数量控制规则:
   - 需求数量必须大于0
   - 各类已使用数量不能超过需求数量:
     - 已创建合同数量 ≤ 需求数量
     - 合同变更数量 ≤ 需求数量
     - 已送货数量 ≤ 已创建合同数量
     - 已入库数量 ≤ 已送货数量

3. 引用控制规则:
   - 从需求计划整合时需检查可用数量
   - 整合后自动更新需求计划数量
   - 整合后自动更新需求计划状态
   - 需记录单据流关系

4. 审批流程规则:
   - 提交时必须填写所有必填字段
   - 审批过程中不允许修改单据
   - 驳回后可以修改重新提交
   - 作废后不能重新提交
   - 审批通过后自动更新行项目状态

## 4. 接口说明

### 4.1 基础操作接口

#### 4.1.1 采购申请分页查询
- 请求路径：POST /purchase-applies/results
- 功能说明：根据查询条件分页获取采购申请列表
- 入参说明：
  ```json
  {
    "pageIndex": 1,                    // 页码(必填)
    "pageSize": 10,                    // 每页大小(必填)
    "receiptCode": "CG241024001",     // 单据编号
    "receiptStatusList": [10,20,30],  // 单据状态列表
    "purchaseType": 1,                // 采购类型(1:生产物资类,2:非生产物资类,3:服务类,4:施工类,5:资产类)
    "sendType": 1,                    // 采购类别(1:离岸采购,2:在岸采购,3:油品采购)
    "purchaseDescription": "2024采购", // 采购申请描述
    "createTimeStart": "2024-01-01",  // 创建开始时间
    "createTimeEnd": "2024-12-31",    // 创建结束时间
    "createUserCode": "Admin",        // 创建人编码
    "createUserName": "管���员"         // 创建人名称
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "pageIndex": 1,
      "pageSize": 10,
      "total": 100,
      "rows": [{
        "id": 159843409264782,
        "receiptCode": "CG241024001",
        "receiptType": 300,
        "receiptStatus": 10,
        "receiptStatusI18n": "草稿",
        "purchaseType": 1,
        "purchaseTypeI18n": "生产物资类",
        "sendType": 1,
        "sendTypeI18n": "离岸采购",
        "purchaseDescription": "2024年第一季度物资采购",
        "createTime": "2024-01-01 10:00:00",
        "createUserName": "管理员"
      }]
    }
  }
  ```

#### 4.1.2 采购申请初始化
- 请求路径：POST /purchase-applies/init
- 功能说明：初始化新的采购申请单，设置默认值
- 入参说明：无需请求参数
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success", 
    "data": {
      "data": {
        "receiptType": 300,
        "receiptStatus": 10,
        "createTime": "2024-01-01 10:00:00",
        "createUserId": 1,
        "createUserName": "管理员",
        "purchaseTypeList": [{
          "code": 1,
          "name": "生产物资类"
        }],
        "sendTypeList": [{
          "code": 1,
          "name": "离岸采购"
        }]
      },
      "extendVO": {
        "attachmentRequired": true,
        "operationLogRequired": true,
        "relationRequired": true
      },
      "buttonVO": {
        "buttonSave": true,
        "buttonSubmit": true,
        "buttonDelete": false
      }
    }
  }
  ```

#### 4.1.3 采购申请详情
- 请求路径：GET /purchase-applies/{id}
- 功能说明：获取采购申请详细信息
- 入参说明：
  - id: 采购申请ID(必填,路径参数)
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "data": {
        "id": 159843409264782,
        "receiptCode": "CG241024001",
        "receiptType": 300,
        "receiptStatus": 10,
        "purchaseType": 1,
        "sendType": 1,
        "purchaseDescription": "2024年第一季度物资采购",
        "remark": "备注信息",
        "createTime": "2024-01-01 10:00:00",
        "createUserId": 1,
        "createUserName": "管理员",
        "itemList": [{
          "id": 159843409264783,
          "headId": 159843409264782,
          "rid": "0010",
          "matId": 60000001,
          "matCode": "M001005",
          "matName": "物料描述001003",
          "unitId": 7,
          "unitCode": "M3",
          "unitName": "立方米",
          "demandQty": 10.00,
          "itemStatus": 10,
          "itemRemark": "备注",
          "preReceiptType": 400,
          "preReceiptId": 159843409264780,
          "preReceiptItemId": 159843409264781,
          "preReceiptCode": "XQ241024001",
          "preReceiptRid": "0010"
        }],
        "logList": [{
          "operationType": 1,
          "operationName": "创建",
          "operationTime": "2024-01-01 10:00:00",
          "operationUser": "管理员"
        }],
        "approveList": [{
          "approveStatus": 1,
          "approveResult": "同意",
          "approveTime": "2024-01-01 11:00:00",
          "approveUser": "审批人"
        }]
      },
      "extendVO": {
        "attachmentRequired": true,
        "operationLogRequired": true,
        "relationRequired": true
      },
      "buttonVO": {
        "buttonSave": true,
        "buttonSubmit": true,
        "buttonDelete": true
      }
    }
  }
  ```

#### 4.1.4 采购申请保存
- 请求路径：POST /purchase-applies/save
- 功能说明：保存采购申请（新增或修改）
- 入参说明：
  ```json
  {
    "id": 159843409264782,           // 主键ID(修改时必填)
    "receiptCode": "CG241024001",    // 单据编号(修改时必填)
    "receiptType": 300,              // 单据类型(300:采购申请)
    "purchaseType": 1,               // 采购类型(必填)
    "sendType": 1,                   // 采购类别(必填)
    "purchaseDescription": "2024年第一季度物资采购", // 采购申请描述
    "remark": "备注信息",            // 备注
    "itemList": [{                   // 行项目列表(必填)
      "id": 159843409264783,         // 行项目ID(修改时必填)
      "headId": 159843409264782,     // 头表ID(修改时必填)
      "rid": "0010",                 // 行号
      "matId": 60000001,             // 物料ID(必填)
      "unitId": 7,                   // 单位ID(必填)
      "ftyId": 1,                    // 工厂ID
      "demandQty": 10.00,            // 需求数量(必填)
      "demandDate": "2024-10-24",    // 需求日期
      "itemRemark": "备注",          // 行项目备注
      "preReceiptType": 400,         // 前序单据类型
      "preReceiptId": 159843409264780, // 前序单据ID
      "preReceiptItemId": 159843409264781, // 前序单据行项目ID
      "preReceiptCode": "XQ241024001", // 前序单据编号
      "preReceiptRid": "0010"        // 前序单据行号
    }],
    "fileList": [{                   // 附件列表
      "fileName": "附件1.pdf",
      "fileUrl": "http://xxx"
    }]
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": "CG241024001"  // 返回单据编号
  }
  ```
- 业务规则：
  1. 新增时自动生成单据编号
  2. 必填字段校验
  3. 根据采购类型校验专用字段
  4. 保存头表、行项目、附件和操作日志
  5. 只有草稿状态可以修改

#### 4.1.5 采购申请提交
- 请求路径：POST /purchase-applies/submit
- 功能说明：提交采购申请进入审批流程
- 入参说明：
  ```json
  {
    "id": 159843409264782,           // 主键ID(必填)
    "receiptCode": "CG241024001",    // 单据编号(必填)
    "remark": "提交备注"             // 提交备注
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": "CG241024001"  // 返回单据编号
  }
  ```
- 业务规则：
  1. 校验单据状态必须为草稿
  2. 校验必填字段
  3. 保存数据并更新状态为已提交
  4. 记录提交人和提交时间
  5. 生成提交操作日志

#### 4.1.6 采购申请删除
- 请求路径：DELETE /purchase-applies/{id}
- 功能说明：删除草稿状态的采购申请
- 入参说明：
  - id: 采购申请ID(路径参数,必填)
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": null
  }
  ```
- 业务规则：
  1. 校验单据状态必须为草稿
  2. 删除头表数据
  3. 删除行项目数据
  4. 删除附件
  5. 删除单据流关系
  6. 生成删除操作日志

#### 4.1.7 采购申请取消
- 请求路径：POST /purchase-applies/cancel
- 功能说明：取消已提交的采购申请
- 入参说明：
  ```json
  {
    "id": 159843409264782,           // 主键ID(必填)
    "receiptCode": "CG241024001",    // 单据编号(必填)
    "remark": "取消原因"             // 取消原因
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": "CG241024001"  // 返回单据编号
  }
  ```
- 业务规则：
  1. 校验单据状态必须为已提交或已驳回
  2. 更新状态为已作废
  3. 生成取消操作日志

#### 4.1.8 获取需求计划行项目列表
- 请求路径：POST /purchase-applies/demand-plan-items
- 功能说明：获取待整合的需求计划行项目列表
- 入参说明：
  ```json
  {
    "pageIndex": 1,                    // 页码(必填)
    "pageSize": 10,                    // 每页大小(必填)
    "receiptCode": "XQ241024001",     // 需求计划编号
    "matCode": "M001005",             // 物料编码
    "matName": "物料描述",            // 物料名称
    "demandDeptCode": "D001",         // 需求部门编码
    "demandDeptName": "采购部",       // 需求部门名称
    "demandUserCode": "24001844",     // 需求人编码
    "demandUserName": "张三"          // 需求人名称
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "pageIndex": 1,
      "pageSize": 10,
      "total": 100,
      "rows": [{
        "id": 159843409264783,
        "headId": 159843409264782,
        "rid": "0010",
        "matId": 60000001,
        "matCode": "M001005",
        "matName": "物料描述001003",
        "unitId": 7,
        "unitCode": "M3",
        "unitName": "立方米",
        "demandQty": 10.00,
        "unClearedQty": 10.00,
        "contractQty": 0,
        "contractChangeQty": 0,
        "stockQty": 100.00,
        "lastYearPurchaseQty": 1000.00,
        "lastYearConsumeQty": 900.00,
        "deliveryQty": 0,
        "inputQty": 0,
        "demandDeptId": 1,
        "demandDeptCode": "D001",
        "demandDeptName": "采购部",
        "demandUserId": 1,
        "demandUserCode": "24001844",
        "demandUserName": "张三",
        "preReceiptType": 400,
        "preReceiptId": 159843409264780,
        "preReceiptItemId": 159843409264781,
        "preReceiptCode": "XQ241024001",
        "preReceiptRid": "0010"
      }]
    }
  }
  ```
- 业务规则：
  1. 只查询状态为待整合的需求计划行项目
  2. 支持多条件组合查询
  3. 需进行数据权限过滤
  4. 填充关联属性(物料、单位等)
  5. 默认按需求计划编号排序

## 5. 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | 操作成功 | 请求处理成功 |
| 400 | 参数错误 | 请求参数不符合要求 |
| 401 | 未授权 | 用户未登录或无权限 |
| 500 | 系统错误 | 服务器内部错误 |
| 1001 | 单据不存在 | 根据ID未找到单据 |
| 1002 | 单据状态错误 | 当前状态不允许操作 |
| 1003 | 必填字段为空 | 必填字段未填写 |
| 1004 | 数量错误 | 数量必须大于0 |
| 1005 | 单据已被引用 | 单据已被其他单据引用 |

## 6. 注意事项

1. 数据权限控制
   - 需求计划查询需进行数据权限过滤
   - 只能查看/操作有权限的数据

2. 并发控制
   - 使用乐观锁控制并发修改
   - 保存时检查版本号

3. 数据一致性
   - 使用事务确保数据一致性
   - 关联更新需在同一事务中

4. 性能优化
   - 合理使用索引
   - 批量操作���升性能
   - 大数据量分页查询

5. 日志记录
   - 记录关键操作日志
   - 异常信息完整记录
   - 便于问题定位

6. 安全控制
   - 接口权限控制
   - 敏感数据加密
   - SQL注入防护

7. 代码规范
   - 遵循项目编码规范
   - 注释完整规范
   - 命名符合规范 