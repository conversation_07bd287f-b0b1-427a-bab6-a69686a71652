package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.cases.service.biz.CasesImgService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.car.service.datawrap.DicCarDataWrap;
import com.inossem.wms.bizbasis.masterdata.car.service.datawrap.DicCarTypeDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizbasis.todo.service.datawrap.TemplateTaskDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumCheckRejectResultType;
import com.inossem.wms.common.enums.EnumCheckResultType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.register.EnumFileType;
import com.inossem.wms.common.enums.register.EnumUnitizedVisualCheck;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.todo.entity.TemplateTask;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptRegisterSearchPO;
import com.inossem.wms.common.model.bizdomain.register.vo.BizRecieptRegisterPageVO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.car.entity.DicCar;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilGzip;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilPrint;
import com.inossem.wms.common.util.UtilString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 成套设备到货登记 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-06
 */
@Service
public class UnitizedArrivalRegisterComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected StockCommonService stockCommonService;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected DicCarDataWrap dicCarDataWrap;

    @Autowired
    protected DicCarTypeDataWrap dicCarTypeDataWrap;

    @Autowired
    protected BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;

    @Autowired
    protected BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    protected BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    protected BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;

    @Autowired
    protected BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    protected BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    protected BizReceiptRegisterHeadDataWrap bizReceiptRegisterHeadDataWrap;
    @Autowired
    protected BizReceiptDeliveryNoticeHeadDataWrap bizReceiptDeliveryNoticeHeadDataWrap;
    @Autowired
    protected BizReceiptRegisterItemDataWrap bizReceiptRegisterItemDataWrap;
    @Autowired
    protected TemplateTaskDataWrap templateTaskDataWrap;
    @Autowired
    protected BizBatchInfoDataWrap bizBatchInfoDataWrap;
    @Autowired
    protected CasesImgService casesImgService;
    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected UnitizedReportComponent unitizedReportComponent;
    @Autowired
    protected UnitizedBoxPlanComponent unitizedBoxPlanComponent;
    /**
     * 查询车辆类型下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("dicCarTypeDataWrap.list()":"车辆类型下拉框")}
     */
    public void getCarTypeDown(BizContext ctx) {
        // 查询车辆类型下拉
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dicCarTypeDataWrap.list()));
    }

    /**
     * 查询车辆下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("icCarDataWrap.list(carQueryWrapper)":"车辆下拉框")}
     */
    public void getCarDown(BizContext ctx) {
        // 获取上下文
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        // 查询车辆下拉
        QueryWrapper<DicCar> carQueryWrapper = new QueryWrapper<>();
        carQueryWrapper.lambda().eq(DicCar::getCarType, code);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dicCarDataWrap.list(carQueryWrapper)));
    }

    /**
     * 查询外观检查下拉
     */
    public void getVisualCheckDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumUnitizedVisualCheck.toList()));
    }

    /**
     * 查询检查结果下拉
     */
    public void getCheckResultDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumCheckResultType.toList()));
    }
    /**
     * 查询检查结果下拉
     */
    public void getCheckRejectResultDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumCheckRejectResultType.toList()));
    }

    /**
     * 查询到货登记单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptRegisterSearchPO :"登记单分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptRegisterSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(StringUtils.isNotEmpty(po.getChildMatCode())){
            Long matId = dictionaryService.getMatIdByMatCode(po.getChildMatCode());
            po.setChildMatId(matId);
        }
        List<Long> locationIdList =null;
        CurrentUser user = ctx.getCurrentUser();
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        // 分页查询处理
        IPage<BizRecieptRegisterPageVO> page = po.getPageObj(BizRecieptRegisterPageVO.class);
        bizReceiptRegisterHeadDataWrap.getPageVOListUnitized(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 到货登记单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"到货登记单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取到货登记单详情
        BizReceiptRegisterHeadDTO headDTO = UtilBean.newInstance(bizReceiptRegisterHeadDataWrap.getById(headId), BizReceiptRegisterHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 属性填充
        List<BizReceiptRegisterItemDTO> itemDTOList = headDTO.getItemList();
        if (CollectionUtils.isNotEmpty(itemDTOList)) {
            Map<Long, String> itemMatMap = new HashMap<>(itemDTOList.size());
            for (int i = 0; i < itemDTOList.size(); i++) {
                BizReceiptRegisterItemDTO itemDTO = itemDTOList.get(i);
                if (i == 0) {
                    headDTO.setPurchaseUserCode(itemDTO.getPurchaseUserCode())
                            .setPurchaseUserName(itemDTO.getPurchaseUserName());
                    headDTO.setReferReceiptCode(itemDTO.getReferReceiptCode());
                    headDTO.setCustomsSealsNumber(itemDTO.getCustomsSealsNumber());
                    headDTO.setBillLadingNumber(itemDTO.getBillLadingNumber());
                    headDTO.setEstimatedArrivalTime(itemDTO.getEstimatedArrivalTime());

                }
                itemMatMap.put(itemDTO.getMatId(), itemDTO.getMatCode());
            }
            List<BizReceiptWaybillDTO> waybillDTOList = headDTO.getWaybillDTOList();
            for (BizReceiptWaybillDTO waybillDTO : waybillDTOList) {
                String parentMatCode = waybillDTO.getParentMatCode();
                if (StringUtils.isBlank(parentMatCode)) {
                    Long parentMatId = waybillDTO.getParentMatId();
                    parentMatCode = itemMatMap.get(parentMatId);
                    if (StringUtils.isNotBlank(parentMatCode)) {
                        waybillDTO.setParentMatCode(parentMatCode);
                    }
                }
            }
            Date createTime = headDTO.getCreateTime();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String createTimeStr = formatter.format(createTime);
            String reciver = headDTO.getReceiver();
            if (StringUtils.isBlank(reciver)) {
                headDTO.setReceiver(UtilPrint.SIGNATURE);
            } else {
                headDTO.setReceiverSignDate(createTimeStr);
            }
            String discharger = headDTO.getDischarger();
            if (StringUtils.isBlank(discharger)) {
                headDTO.setDischarger(UtilPrint.SIGNATURE);
            } else {
                headDTO.setDischargerSignDate(createTimeStr);
            }
            String sender = headDTO.getSender();
            if (StringUtils.isBlank(sender)) {
                headDTO.setSender(UtilPrint.SIGNATURE);
            } else {
                headDTO.setSenderSignDate(createTimeStr);
            }
            String projector = headDTO.getProjector();
            if (StringUtils.isBlank(projector)) {
                headDTO.setProjector(UtilPrint.SIGNATURE);
            } else {
                headDTO.setProjectorSignDate(createTimeStr);
            }
        }
        headDTO.setWareContractDept(Const.wareContractDept); //仓储承包商
        // 运单排序
        headDTO.setWaybillDTOList(headDTO.getWaybillDTOList().stream().sorted(Comparator.comparing(BizReceiptWaybillDTO::getCaseCode).reversed()).collect(Collectors.toList()));
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置到货登记单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 登记单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptRegisterHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        }
        else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
//            // 已完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 登记单保存校验
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void checkSaveRegister(BizContext ctx) {
        // 获取上下文
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 500) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 保存登记单
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     * @out ctx 出参 {"receiptCode" : "登记单单号"},{@link BizReceiptRegisterHeadDTO : "已保存的登记单}
     */
    public void saveApply(BizContext ctx) {
        // 入参上下文 - 要保存的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        headDTO.setCreateUserId(user.getId());
        headDTO.setModifyUserId(user.getId());
        // 查询判断是否箱件为空,或者与传参不一致, web页面选择部分运单时拆新
        int i = (int)bizReceiptWaybillDataWrap.count(new QueryWrapper<BizReceiptWaybill>().lambda().eq(BizReceiptWaybill::getArrivalRegisterHeadId, headDTO.getId()));
        if (i > 0 && i != headDTO.getWaybillDTOList().size()) {
            // 拆单处理-生成新单据
            headDTO.setId(null);
        }
        Map<Long, List<BizReceiptWaybillDTO>> arrivalRegisterItemMap = headDTO.getWaybillDTOList().stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getArrivalRegisterItemId));
        headDTO.getItemList().forEach(
                item -> {
                    List<BizReceiptWaybillDTO> waybillDTOList = arrivalRegisterItemMap.get(item.getId());
                    if (UtilCollection.isNotEmpty(waybillDTOList)) {
                        // 拒收的箱件所包含的运单信息，在到货登记提交后，不调用物料凭证接口，不触发103过账
                        waybillDTOList = waybillDTOList.stream().filter(o -> !EnumUnitizedVisualCheck.JS.getValue().equals(o.getUnitizedVisualCheck())).collect(Collectors.toList());
                        BigDecimal qty = waybillDTOList.stream().reduce(BigDecimal.ZERO, (x, y) -> x.add(y.getArrivalQty().multiply(y.getPrice())), BigDecimal::add);
                        // 过账金额取运单上的金额
                        item.setBillQty(qty);
                    }
                }
        );
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新登记单
            bizReceiptRegisterHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptRegisterItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptRegisterItem::getHeadId, headDTO.getId());
            bizReceiptRegisterItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
//            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_REGISTER.getValue());
            // SKX-EPH4YYXX-PIR-ZZZZ
            receiptCode = "SK" + bizReceiptDeliveryNoticeHeadDataWrap.getById(headDTO.getItemList().get(0).getPreReceiptHeadId()).getUnit()
                    + "-EPH4" + UtilDate.getYearMonth(new Date()) + "-PIR-" + bizCommonService.getNextSequence(EnumSequenceCode.SEQUENCE_ARRIVE_REGISTER.getValue());
            headDTO.setReceiptCode(receiptCode);
            bizReceiptRegisterHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptRegisterItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setId(null);
            // 拆单的单据号更新为新值
            itemDTO.setReceiptCode(receiptCode);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            // 到货登记104冲销特殊处理字段
            itemDTO.setWriteOffQty(itemDTO.getQty());
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        bizReceiptRegisterItemDataWrap.saveBatchDto(headDTO.getItemList());
        /* ********************** item处理结束 *************************/
        /* ********************** waybill处理开始 *************************/
        // 行项目按行号分组
        Map<String, List<BizReceiptRegisterItemDTO>> itemMap = headDTO.getItemList().stream().collect(Collectors.groupingBy(BizReceiptRegisterItemDTO::getRid));
        for (BizReceiptWaybillDTO waybillDTO : headDTO.getWaybillDTOList()) {
            waybillDTO.setArrivalRegisterHeadId(headDTO.getId());
            waybillDTO.setArrivalRegisterItemId(itemMap.get(waybillDTO.getDeliveryNoticeItemRid()).get(0).getId());
            waybillDTO.setArrivalRegisterItemRid(waybillDTO.getDeliveryNoticeItemRid());
        }
        bizReceiptWaybillDataWrap.updateBatchDtoById(headDTO.getWaybillDTOList());
        /* ********************** waybill处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(headDTO);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的登记单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 同步dts
     * @param ctx
     */
    public void syncDTS(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        if(po!=null){
            String fileName=po.getReceiptCode()+".pdf";
            List<BizCommonReceiptAttachment> list= receiptAttachmentService.getBizReceiptAttachmentListByFileName(po.getId(),po.getReceiptType(),fileName);
            if(UtilCollection.isEmpty(list)) { //之前没有附件 存入模版附件定时任务表
                Date createTime = po.getCreateTime();
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String createTimeStr = formatter.format(createTime);
                String reciver = po.getReceiver();
                if (StringUtils.isBlank(reciver)) {
                    po.setReceiver(UtilPrint.SIGNATURE);
                } else {
                    po.setReceiverSignDate(createTimeStr);
                }
                String discharger = po.getDischarger();
                if (StringUtils.isBlank(discharger)) {
                    po.setDischarger(UtilPrint.SIGNATURE);
                } else {
                    po.setDischargerSignDate(createTimeStr);
                }
                String sender = po.getSender();
                if (StringUtils.isBlank(sender)) {
                    po.setSender(UtilPrint.SIGNATURE);
                } else {
                    po.setSenderSignDate(createTimeStr);
                }
                TemplateTask templateTask=new TemplateTask();
                templateTask.setType(1);
                templateTask.setHeadId(po.getId());
                templateTask.setReceiptCode(po.getReceiptCode());
                templateTask.setReceiptType(po.getReceiptType());
                templateTask.setTemplateReceiptType(po.getReceiptType());
                String param = UtilGzip.compressStringToString(JSONArray.toJSONStringWithDateFormat(po, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat));
                templateTask.setPara(param);
                templateTask.setDealFlag(0);
                templateTask.setCreateUserId(user.getId());
                templateTask.setModifyUserId(user.getId());
                templateTask.setCreateTime(UtilDate.getNow());
                templateTask.setModifyTime(UtilDate.getNow());
                templateTaskDataWrap.save(templateTask);
            }else{ //存在附件 同步dts
                unitizedReportComponent.syncDTSArrivalRegister(ctx);
            }
        }

    }
    /**
     * 提交到货登记单
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要提交的到货登记单"}
     * @out ctx 出参 {"receiptCode" : "遗失登记单单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitLoseRegister(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 设置提交时间
        po.setSubmitTime(new Date());
        // 设置提交操作信息
        po.setSubmitUserId(ctx.getCurrentUserId());
        // 保存登记单
        this.saveApply(ctx);
    }

    /**
     * sap过账
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "到货登记单"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "补全凭证【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间】"}
     */
    public void postToSap(BizContext ctx) {
        // 入参上下文 - 到货登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ******** 设置到货登记单账期 ******** */
        headDTO.getItemList().forEach(p -> p.setPostingDate(headDTO.getPostingDate()));
        this.setInPostDate(headDTO.getItemList(), user);
        // 行项目金额为0的直接更新行项目状态已完成
        List<BizReceiptRegisterItemDTO> unToSapItemDTOList = headDTO.getItemList().stream().filter(p -> p.getQty().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(unToSapItemDTOList)) {
            unToSapItemDTOList.forEach(p -> p.setIsPost(EnumRealYn.TRUE.getIntValue()).setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
            bizReceiptRegisterItemDataWrap.updateBatchDtoById(unToSapItemDTOList);
        }
        List<BizReceiptRegisterItemDTO> itemDTOList = headDTO.getItemList().stream().filter(p -> p.getBillQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.postingNew(JSONArray.toJSONStringWithDateFormat(itemDTOList, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptRegisterItemDTO itemDTO : itemDTOList) {
                        // 获取当前item返回对象
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(itemDTO.getReceiptCode())
                                        && item.getReceiptRid().equals(itemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        itemDTO.setMatDocCode(currentReturnObject.getMatDocCode());
                        itemDTO.setMatDocRid(currentReturnObject.getMatDocRid());
                        itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        itemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                        itemDTO.setDmbtr(currentReturnObject.getDmbtr());
                    }
                    // 更新到货登记单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、过帐日期、凭证时间、sap过账标识】
                    bizReceiptRegisterItemDataWrap.updateBatchDtoById(itemDTOList);
                }
                // 更新到货登记单状态 - 已记账
                this.updateStatus(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                /* ******** 调用sap后处理结束 ******** */
            } else {
                // 更新到货登记单head、item状态-未同步
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    /**
     * 登记单冲销校验
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void checkWriteOffRegister(BizContext ctx) {
        // 获取上下文
        ReceiptItemActionPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 封装入参
        BizReceiptRegisterHeadDTO headDTO = UtilBean.newInstance(bizReceiptRegisterHeadDataWrap.getById(po.getHeadId()), BizReceiptRegisterHeadDTO.class);
        QueryWrapper<BizReceiptWaybill> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda().eq(BizReceiptWaybill::getArrivalRegisterHeadId, po.getHeadId()).in(BizReceiptWaybill::getId, po.getWaybillIds());
        List<BizReceiptWaybillDTO> wayBillList = UtilCollection.toList(bizReceiptWaybillDataWrap.list(itemQueryWrapper), BizReceiptWaybillDTO.class);
        // 数据填充
        dataFillService.fillRlatAttrDataList(wayBillList);
        wayBillList.forEach(p -> {
            // 设置 到货登记冲销数量 = 到货数量 - 不合格数量 - 未到货数量
            p.setAWriteOffQty(p.getArrivalQty().subtract(p.getUnqualifiedQty()).subtract(p.getUnarrivalQty()));
            // 设置 到货登记冲销过账日期 到货登记冲销原因
            p.setAWriteOffPostingDate(po.getWriteOffPostingDate()).setAWriteOffReason(po.getWriteOffReason());
            // 设置 到货登记对应行项目信息
            BizReceiptRegisterItemDTO receiptRegisterItemDTO = UtilBean.newInstance(bizReceiptRegisterItemDataWrap.getById(p.getArrivalRegisterItemId()), BizReceiptRegisterItemDTO.class);
            dataFillService.fillAttr(receiptRegisterItemDTO);
            p.setArrivalRegisterItem(receiptRegisterItemDTO);
        });
        headDTO.setWaybillDTOList(wayBillList);
        // 入參放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * sap冲销
     *
     * @param ctx ctx
     */
    public void writeOffToSap(BizContext ctx) {
        // 入参上下文 - 到货登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        /* ******** 设置冲销账期 ******** */
        this.setAWInPostDate(headDTO.getWaybillDTOList(), user);
        // 运单行到货登记冲销数量等于0 或运单行单价等于0的 更新运单到货登记冲销信息【过帐日期、凭证时间、sap冲销标识、冲销原因】
        List<BizReceiptWaybillDTO> unToSapItemDTOList = headDTO.getWaybillDTOList().stream().filter(p -> p. getAWriteOffQty().compareTo(BigDecimal.ZERO) == 0
                || p.getPrice().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(unToSapItemDTOList)) {
            unToSapItemDTOList.forEach(p -> p.setAIsWriteOff(EnumRealYn.TRUE.getIntValue()));
            bizReceiptWaybillDataWrap.updateBatchDtoById(unToSapItemDTOList);
        }
        // 运单行到货登记冲销数量大于0 运单行单价大于0的 过账SAP
        List<BizReceiptWaybillDTO> itemListNotSync = headDTO.getWaybillDTOList().stream().filter(p -> p. getAWriteOffQty().compareTo(BigDecimal.ZERO) > 0
                && p.getPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.postingNew(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptWaybillDTO waybillDTO : itemListNotSync) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(waybillDTO.getArrivalRegisterItem().getReceiptCode())
                                        && item.getReceiptRid().equals(waybillDTO.getArrivalRegisterItemRid())
                                        && item.getReceiptBid().equals(waybillDTO.getBid())
                                )
                                .findFirst().orElse(null);
                        if (null == currentReturnObject) {
                            continue;
                        }
                        waybillDTO.setAWriteOffMatDocCode(currentReturnObject.getMatDocCode());
                        waybillDTO.setAWriteOffMatDocRid(currentReturnObject.getMatDocRid());
                        waybillDTO.setAWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        waybillDTO.setAIsWriteOff(EnumRealYn.TRUE.getIntValue());
                        waybillDTO.setADmbtr(currentReturnObject.getDmbtr());
                    }
                    // 更新运单到货登记冲销信息【物料凭证编号、物料凭证的行序号、物料凭证年度、过帐日期、凭证时间、sap冲销标识、冲销原因、本位币金额】
                    bizReceiptWaybillDataWrap.updateBatchDtoById(itemListNotSync);
                }
                /* ******** 调用sap后处理结束 ******** */
            } else {
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemDTOList 到货登记单行项目
     * @param user 当前用户
     */
    public void setInPostDate(List<BizReceiptRegisterItemDTO> itemDTOList, CurrentUser user) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            Date postingDate = itemDTOList.get(0).getPostingDate();
            if (UtilObject.isNull(postingDate)) {
                postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            // 判断过账日期是否在帐期内
            postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
            for (BizReceiptRegisterItemDTO itemDTO : itemDTOList) {
                itemDTO.setDocDate(UtilDate.getNow());
                itemDTO.setPostingDate(postingDate);
            }
        }
    }
    /**
     * 过账前设置行项目账期
     *
     * @param waybillDTOList 运单行信息
     * @param user 当前用户
     */
    public void setAWInPostDate(List<BizReceiptWaybillDTO> waybillDTOList, CurrentUser user) {
        if (UtilCollection.isNotEmpty(waybillDTOList)) {
            Date writeOffPostingDate = waybillDTOList.get(0).getAWriteOffPostingDate();
            if(UtilObject.isNull(writeOffPostingDate)) {
                writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            // 判断过账日期是否在帐期内
            writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
            for (BizReceiptWaybillDTO itemDTO : waybillDTOList) {
                itemDTO.setAWriteOffDocDate(UtilDate.getNow());
                itemDTO.setAWriteOffPostingDate(writeOffPostingDate);
            }
        }
    }

    /**
     * 更新单据状态为已完成
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新单据状态
     *
     * @param headDTO 登记单抬头
     * @param itemDTOList 登记单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptRegisterHeadDTO headDTO, List<BizReceiptRegisterItemDTO> itemDTOList, Integer receiptStatus) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptRegisterHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptRegisterHead::getId, headDTO.getId())
                    .set(BizReceiptRegisterHead::getReceiptStatus, receiptStatus);
            bizReceiptRegisterHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptRegisterItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptRegisterItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptRegisterItem::getItemStatus, receiptStatus);
            bizReceiptRegisterItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 刪除登记单
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "登记单单号"}
     */
    public void deleteInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置登记单
        BizReceiptRegisterHeadDTO headDTO = UtilBean.newInstance(bizReceiptRegisterHeadDataWrap.getById(headId), BizReceiptRegisterHeadDTO.class);
        // 逻辑删除抬头表
        bizReceiptRegisterHeadDataWrap.removeById(headId);
        // 逻辑删除行项目表
        QueryWrapper<BizReceiptRegisterItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptRegisterItem::getHeadId, headId);
        bizReceiptRegisterItemDataWrap.remove(itemWrapper);
        // 登记单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 登记单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
    }

    /**
     * 生成分配质检单
     *
     * @param ctx - 到货登记单提交表单内容
     */
    public void genDistributeInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(po.getIsUnbox() == 1){
//            // 生成开箱计划
//            po.getItemList().forEach(o->o.setPreReceiptHeadId(po.getId()).setPreReceiptItemId(o.getId()).setPreReceiptType(po.getReceiptType()));
//            po.setId(null);
//            po.setReceiptType(EnumReceiptType.UNITIZED_BOX_PLAN.getValue());
//            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
//            unitizedBoxPlanComponent.saveApply(ctx);
            return;
        }
        // 拒收的箱件所包含的运单信息，在到货登记提交后，不流转到后续的开箱计划、质检会签等环节
        List<BizReceiptWaybillDTO> distributeWaybillDTOList = po.getWaybillDTOList().stream().filter(o -> !o.getUnitizedVisualCheck().equals(EnumUnitizedVisualCheck.JS.getValue())).collect(Collectors.toList());
        // 组装参数
        BizReceiptInspectHeadDTO headDTO = new BizReceiptInspectHeadDTO();
        List<BizReceiptInspectItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.UNITIZED_DISTRIBUTE_INSPECTION.getValue()).setReceiveDate(po.getReceiveDate())
                .setDepositPoint(po.getDepositPoint()).setIsSafe(po.getIsSafe()).setDistributeWaybillDTOList(distributeWaybillDTOList)
                .setUnit(po.getUnit());
        headDTO.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());
        // 过滤出实际运单数量大于0的行项目
        List<BizReceiptRegisterItemDTO> receiptRegisterItemDTOS = po.getItemList().stream().filter(p -> p.getBillQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (UtilCollection.isEmpty(receiptRegisterItemDTOS) || UtilCollection.isEmpty(distributeWaybillDTOList)) {
            // 空的不生成
            return;
        }
        for (BizReceiptRegisterItemDTO itemDTO : receiptRegisterItemDTOS) {
            BizReceiptInspectItemDTO inspectItemDTO = UtilBean.newInstance(itemDTO, BizReceiptInspectItemDTO.class);
            inspectItemDTO.setPreReceiptHeadId(po.getId());
            inspectItemDTO.setPreReceiptItemId(itemDTO.getId());
            inspectItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue());
            inspectItemDTO.setPreReceiptQty(itemDTO.getQty());
            inspectItemDTO.setQty(itemDTO.getBillQty());
            itemDTOList.add(inspectItemDTO);
        }
        headDTO.setItemList(itemDTOList);
        // 设置入参上下文
        BizContext ctxInspect = new BizContext();
        ctxInspect.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxInspect.setCurrentUser(ctx.getCurrentUser());
        // 推送MQ生成分配质检单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_DISTRIBUTE_INSPECT_STOCK, ctxInspect);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的登记单
     */
    public void saveReceiptTree(BizReceiptRegisterHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptRegisterItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保持附件的登记单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存登记单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
        // 更新抬头表附件状态
        UpdateWrapper<BizReceiptRegisterHead> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(BizReceiptRegisterHead::getId, headDTO.getId())
                .set(BizReceiptRegisterHead::getFileType, UtilCollection.isNotEmpty(headDTO.getFileList()) ? EnumFileType.UPLOADED.getValue() : EnumFileType.UN_UPLOAD.getValue());
        bizReceiptRegisterHeadDataWrap.update(updateWrapper);
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存操作日志的登记单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 保存冲销日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存保存冲销日志的登记单"}
     */
    public void saveBizReceiptWriteOffOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存冲销日志的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", user.getId());
    }

    /**
     * 删除登记单单据流
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除登记单单据流
        receiptRelationService.deleteReceiptTree(po.getReceiptType(), po.getId());
    }

    /**
     * 删除登记单附件
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(po.getId(), po.getReceiptType());
    }
    /**
     * 保存箱件图片
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "保存的到货登记单"}
     */
    public void saveBizCasesImg(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isNotEmpty(headDTO.getWaybillDTOList())) {
            for (BizReceiptWaybillDTO item : headDTO.getWaybillDTOList()) {
                if (UtilCollection.isNotEmpty(item.getCasesImgList())) {
                    item.getCasesImgList().forEach(caseImage -> {
                        caseImage.setReceiptId(headDTO.getId());
                        caseImage.setReceiptItemId(item.getId());
                        caseImage.setCaseId(item.getId());
                        caseImage.setCaseCode(item.getCaseCode());
                    });
                } else {
                    item.setCasesImgList(new ArrayList<>());
                }
            }
            // 批量保存到货登记单箱件图片
            casesImgService.multiSaveBizCasesImg(headDTO.getWaybillDTOList().stream().flatMap(obj -> obj.getCasesImgList().stream()).collect(Collectors.toList()));
        }
    }
}
