package com.inossem.wms.bizdomain.budget.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.masterdata.budget.entity.DicAnnualBudget;
import com.inossem.wms.common.model.masterdata.budget.po.DicAnnualBudgetSearchPO;
import com.inossem.wms.common.model.masterdata.budget.vo.DicAnnualBudgetPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AnnualBudgetMapper extends WmsBaseMapper<DicAnnualBudget> {

    List<DicAnnualBudgetPageVO> selectPageVOList(IPage<DicAnnualBudgetPageVO> page, @Param("po") DicAnnualBudgetSearchPO po);


}
