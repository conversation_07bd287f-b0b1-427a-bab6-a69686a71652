package com.inossem.wms.bizdomain.returns.service.component.movetype;

import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import com.inossem.wms.bizdomain.task.service.component.TaskComponent;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnBinDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnHeadDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSpring;

/**
 * 生成冲销移动类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-05-11
 */

@Service
public class InsSaleReturnWriteOffMoveTypeComponent {

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private TaskComponent taskComponent;

    /**
     * 生成记账ins凭证
     *
     * @param headDTO
     * @return
     */
    public StockInsMoveTypeDTO generatePostingInsDoc(BizReceiptReturnHeadDTO headDTO) {
        dictionaryService = this.getDictionaryService();
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                // 批次库存扣减
                StockInsDocBatch insDocBatch = new StockInsDocBatch();
                insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
                insDocBatch.setMatId(itemDTO.getMatId());
                insDocBatch.setBatchId(binDTO.getBatchId());
                insDocBatch.setFtyId(itemDTO.getFtyId());
                insDocBatch.setLocationId(itemDTO.getLocationId());
                insDocBatch.setUnitId(itemDTO.getUnitId());
                insDocBatch.setDecimalPlace(itemDTO.getDecimalPlace());
                insDocBatch.setDocDate(itemDTO.getDocDate());
                insDocBatch.setPostingDate(itemDTO.getPostingDate());
                insDocBatch.setMoveTypeId(itemDTO.getMoveTypeId());
                insDocBatch.setMoveQty(binDTO.getQty());
                insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
                insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBatch.setPreReceiptHeadId(headDTO.getId());
                insDocBatch.setPreReceiptItemId(itemDTO.getId());
                insDocBatch.setPreReceiptBinId(binDTO.getId());
                insDocBatch.setPreReceiptType(EnumReceiptType.STOCK_RETURN_SALE.getValue());
                insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
                insDocBatch.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
                insDocBatch.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
                insDocBatch.setReferReceiptType(itemDTO.getReferReceiptType());
                insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
                insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
                insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
                insDocBatch.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                insDocBatchList.add(insDocBatch);
                // 退库临时区库存扣减
                StockInsDocBin insDocBin = new StockInsDocBin();
                insDocBin.setInsDocRid(insDocRid.toString());
                insDocBin.setMatId(itemDTO.getMatId());
                insDocBin.setBatchId(binDTO.getBatchId());
                insDocBin.setFtyId(itemDTO.getFtyId());
                insDocBin.setLocationId(itemDTO.getLocationId());
                insDocBin.setUnitId(itemDTO.getUnitId());
                insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
                insDocBin.setMoveQty(binDTO.getQty());
                insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
                insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBin.setPreReceiptHeadId(headDTO.getId());
                insDocBin.setPreReceiptItemId(itemDTO.getId());
                insDocBin.setPreReceiptBinId(binDTO.getId());
                insDocBin.setPreReceiptType(EnumReceiptType.STOCK_RETURN_SALE.getValue());
                insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
                insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
                insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
                insDocBin.setMatDocCode(itemDTO.getMatDocCode());
                insDocBin.setMatDocRid(itemDTO.getMatDocRid());
                insDocBin.setMatDocYear(itemDTO.getMatDocYear());
                insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                String typeCode = EnumDefaultStorageType.OUTPUT_RETURN.getTypeCode();
                String binCode = EnumDefaultStorageType.OUTPUT_RETURN.getBinCode();
                Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
                Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
                insDocBin.setTypeId(typeId);
                insDocBin.setBinId(binId);
                insDocBin.setWhId(itemDTO.getWhId());
                insDocBin.setCellId(binDTO.getCellId());
                insDocBinList.add(insDocBin);
                insDocRid++;
            }
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }

    /**
     * 生成作业ins凭证(非同时模式)
     *
     * @param headDTO
     * @return
     */
    public StockInsMoveTypeDTO generateTaskInsDocNonSameTime(BizReceiptReturnHeadDTO headDTO) {
        dictionaryService = this.getDictionaryService();
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            List<BizReceiptTaskItemDTO> taskItemDTOList = taskComponent
                .getTaskItemByPreReceiptItemIds(new ArrayList<>(Collections.singletonList(itemDTO.getId())));
            for (BizReceiptReturnBinDTO bin : itemDTO.getItemInfoList()) {
                // 实际仓位库存扣减
                StockInsDocBin insDocBin = new StockInsDocBin();
                insDocBin.setInsDocRid(insDocRid.toString());
                insDocBin.setMatId(itemDTO.getMatId());
                insDocBin.setBatchId(bin.getBatchId());
                insDocBin.setFtyId(itemDTO.getFtyId());
                insDocBin.setLocationId(itemDTO.getLocationId());
                insDocBin.setWhId(itemDTO.getWhId());
                insDocBin.setUnitId(itemDTO.getUnitId());
                insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
                // insDocBin.setMoveQty(bin.getQty());
                insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
                insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBin.setPreReceiptHeadId(headDTO.getId());
                insDocBin.setPreReceiptItemId(itemDTO.getId());
                insDocBin.setPreReceiptBinId(bin.getId());
                insDocBin.setPreReceiptType(EnumReceiptType.STOCK_RETURN_SALE.getValue());
                insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
                insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
                insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
                insDocBin.setMatDocCode(itemDTO.getMatDocCode());
                insDocBin.setMatDocRid(itemDTO.getMatDocRid());
                insDocBin.setMatDocYear(itemDTO.getMatDocYear());
                insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                // 已作业作业单
                List<BizReceiptTaskItemDTO> taskList = taskItemDTOList.stream()
                    .filter(task -> task.getPreReceiptBinId().equals(bin.getId())).collect(Collectors.toList());
                for (BizReceiptTaskItemDTO taskItemDTO : taskList) {
                    insDocBin.setMoveQty(taskItemDTO.getQty());
                    insDocBin.setTypeId(taskItemDTO.getTargetTypeId());
                    insDocBin.setBinId(taskItemDTO.getTargetBinId());
                    insDocBin.setCellId(taskItemDTO.getTargetCellId());
                    insDocBinList.add(insDocBin);
                    insDocRid++;
                }
                // 退库临时区仓位库存增加
                insDocBin = new StockInsDocBin();
                insDocBin.setInsDocRid(insDocRid.toString());
                insDocBin.setMatId(itemDTO.getMatId());
                insDocBin.setBatchId(bin.getBatchId());
                insDocBin.setFtyId(itemDTO.getFtyId());
                insDocBin.setLocationId(itemDTO.getLocationId());
                String typeCode = EnumDefaultStorageType.OUTPUT_RETURN.getTypeCode();
                String binCode = EnumDefaultStorageType.OUTPUT_RETURN.getBinCode();
                Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
                Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
                insDocBin.setTypeId(typeId);
                insDocBin.setBinId(binId);
                insDocBin.setCellId(bin.getCellId());
                insDocBin.setWhId(itemDTO.getWhId());
                insDocBin.setUnitId(itemDTO.getUnitId());
                insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
                // 已作业的库存
                BigDecimal moveQty = bin.getTaskQty();
                insDocBin.setMoveQty(moveQty);
                insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
                insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBin.setPreReceiptHeadId(headDTO.getId());
                insDocBin.setPreReceiptItemId(itemDTO.getId());
                insDocBin.setPreReceiptBinId(bin.getId());
                insDocBin.setPreReceiptType(EnumReceiptType.STOCK_RETURN_SALE.getValue());
                insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
                insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
                insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
                insDocBin.setMatDocCode(itemDTO.getMatDocCode());
                insDocBin.setMatDocRid(itemDTO.getMatDocRid());
                insDocBin.setMatDocYear(itemDTO.getMatDocYear());
                insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                insDocBinList.add(insDocBin);
                insDocRid++;
            }
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }

    private DictionaryService getDictionaryService() {
        return UtilSpring.getBean("dictionaryService");
    }

}