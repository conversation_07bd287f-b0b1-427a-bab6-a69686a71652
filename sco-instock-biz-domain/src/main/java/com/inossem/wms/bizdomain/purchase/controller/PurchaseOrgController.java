package com.inossem.wms.bizdomain.purchase.controller;

import com.inossem.wms.bizdomain.purchase.service.biz.PurchaseOrgService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.purchase.dto.DicPurchaseOrgDTO;
import com.inossem.wms.common.model.bizdomain.purchase.po.DicPurchaseOrgPO;
import com.inossem.wms.common.model.bizdomain.purchase.vo.DicPurchaseOrgVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 采购组织管理
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@RestController
@Api(tags = "采购组织")
public class PurchaseOrgController {

    @Autowired
    protected PurchaseOrgService purchaseOrgService;

    @ApiOperation(value = "分页", tags = {"采购组织"})
    @PostMapping(value = "/purchaseOrg/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicPurchaseOrgVO>> getPage(@RequestBody DicPurchaseOrgPO po, BizContext ctx) {
        purchaseOrgService.getPage(ctx);
        PageObjectVO<DicPurchaseOrgVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "详情", tags = {"采购组织"})
    @GetMapping(value = "/purchaseOrg/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<DicPurchaseOrgDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        purchaseOrgService.getInfo(ctx);
        BizResultVO<DicPurchaseOrgDTO> dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(dto);
    }

    @ApiOperation(value = "保存", tags = {"采购组织"})
    @PostMapping(value = "/purchaseOrg/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody DicPurchaseOrgDTO po, BizContext ctx) {
        purchaseOrgService.save(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "删除", tags = {"采购组织"})
    @DeleteMapping("/purchaseOrg/delete")
    public BaseResult<?> delete(@RequestBody DicPurchaseOrgDTO po, BizContext ctx) {
        purchaseOrgService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }
}
