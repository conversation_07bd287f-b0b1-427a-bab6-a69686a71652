package com.inossem.wms.bizdomain.input.controller;

import com.inossem.wms.bizdomain.input.service.biz.LeisureInputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.tag.LabelMapVo;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 闲置物资入库 前端控制器
 * </p>
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "闲置管理-闲置物资")
public class LeisureInputController {

    @Autowired
    private LeisureInputService leisureInputService;

    /**
     * 闲置物资入库-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 闲置物资入库单分页
     */
    @ApiOperation(value = "闲置物资入库-分页", tags = {"闲置管理-闲置物资入库"})
    @PostMapping(value = "/input/leisure-input/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po, BizContext ctx) {
        leisureInputService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 闲置物资入库-详情
     *
     * @param id 闲置物资入库主键
     * @param ctx 入参上下文 {"id":"闲置物资入库主键"}
     */
    @ApiOperation(value = "闲置物资入库-详情", tags = {"闲置管理-闲置物资入库"})
    @GetMapping(value = "/input/leisure-input/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        leisureInputService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 闲置物资入库-保存
     *
     * @param po 保存闲置物资入库表单参数
     * @param ctx 入参上下文 {"po":"保存闲置物资入库表单参数"}
     */
    @ApiOperation(value = "闲置物资入库-保存", tags = {"闲置管理-闲置物资入库"})
    @PostMapping(value = "/input/leisure-input/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        leisureInputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 闲置物资入库-提交
     *
     * @param po 提交闲置物资入库表单参数
     * @param ctx 入参上下文 {"po":"提交闲置物资入库表单参数"}
     */
    @ApiOperation(value = "闲置物资入库-提交", tags = {"闲置管理-闲置物资入库"})
    @PostMapping(value = "/input/leisure-input/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        leisureInputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TEMP_STORE_INPUT_SUBMIT_SUCCESS, code);
    }

    /**
     * 验收入库-过账
     *
     * @param po 保存验收入库表单参数
     * @param ctx 入参上下文 {"po":"保存验收入库表单参数"}
     */
    @ApiOperation(value = "闲置物资入库-过账", tags = {"闲置管理-闲置物资入库"})
    @PostMapping(value = "/input/leisure-input/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> post(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        leisureInputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 闲置物资入库-冲销
     *
     * @param po 闲置物资入库冲销表单参数
     * @param ctx 入参上下文 {"po":"闲置物资入库冲销表单参数"}
     */
    @ApiOperation(value = "闲置物资入库-冲销", tags = {"闲置管理-闲置物资入库"})
    @PostMapping(value = "/input/leisure-input/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInputWriteOffPO po, BizContext ctx) {
        leisureInputService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 闲置物资入库单-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "闲置物资入库单-删除", tags = {"闲置管理-闲置物资入库"})
    @DeleteMapping("/input/leisure-input/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        leisureInputService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_DELETE_SUCCESS, po.getReceiptCode());
    }

    /**
     * 查询标签类型下拉
     *
     * @return 标签类型下拉框
     *
     */
    @ApiOperation(value = "查询标签类型下拉", tags = {"闲置管理-闲置物资入库"})
    @GetMapping(path = "/input/leisure-input/tag-type/down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<LabelMapVo>> getDown() {
        return BaseResult.success(leisureInputService.getDown());
    }

    /**
     * 闲置物资入库单-物料标签打印
     *
     * @param po 打印入参
     * @return 闲置物资入库单
     */
    @ApiOperation(value = "闲置物资入库-物料标签打印", tags = {"闲置管理-闲置物资入库"})
    @PostMapping(value = "/input/leisure-input/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptInputHeadDTO> po, BizContext ctx) {
        leisureInputService.boxApplyLabelPrint(ctx);
        List<LabelReceiptInputBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

}
