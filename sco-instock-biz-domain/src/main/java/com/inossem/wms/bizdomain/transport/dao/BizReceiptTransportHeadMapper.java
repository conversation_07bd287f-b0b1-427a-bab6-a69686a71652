package com.inossem.wms.bizdomain.transport.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 转储单表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
public interface BizReceiptTransportHeadMapper extends WmsBaseMapper<BizReceiptTransportHead> {

    /**
     *转储列表查询
     * @param page 分页参数
     * @return
     */
    List<BizReceiptTransportHeadDTO> selectTransportPageVoListByPo(IPage<BizReceiptTransportHeadDTO> page, @Param("po") BizReceiptTransportHeadSearchPO po);

    /**
     *转储列表查询
     * @param page 分页参数
     * @return
     */
    List<BizReceiptTransportHeadDTO> selectTransportPageVoListByPoUnitized(IPage<BizReceiptTransportHeadDTO> page, @Param("po") BizReceiptTransportHeadSearchPO po);


    /**
     *闲置转库列表查询
     * @param page 分页参数
     * @return
     */
    List<BizReceiptTransportHeadDTO> getLeisurePageVOList(IPage<BizReceiptTransportHeadDTO> page,@Param("po") BizReceiptTransportHeadSearchPO po);

}
