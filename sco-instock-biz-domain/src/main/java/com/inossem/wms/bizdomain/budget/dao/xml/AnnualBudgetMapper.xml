<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.budget.dao.AnnualBudgetMapper">

    <select id="selectPageVOList" resultType="com.inossem.wms.common.model.masterdata.budget.vo.DicAnnualBudgetPageVO">
        SELECT
        dab.*,
        su.user_name as createUserCode,
        su.user_name as createUserName,
        dbc.budget_classify_code as budgetClassifyCode,
        dbc.budget_classify_name as budgetClassifyName,
        dbs.budget_subject_code as budgetSubjectCode,
        dbs.budget_subject_name as budgetSubjectName,
        concat(dab.year,'年-',dbc.budget_classify_name,'-',dbs.budget_subject_name) as uniqueKey
        FROM
        dic_annual_budget dab
        inner join sys_user su ON dab.create_user_id = su.id
        inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id
        inner join dic_budget_subject dbs ON dbs.id = dab.budget_subject_id
        AND su.is_delete = 0
        AND dbc.is_delete = 0
        AND dab.is_delete = 0
        <where>
            <if test="po.budgetClassifyName != null and po.budgetClassifyName != ''">
                AND dbc.budget_classify_name like concat('%', #{po.budgetClassifyName}, '%')
            </if>
            <if test="po.budgetSubjectName != null and po.budgetSubjectName != ''">
                AND dbs.budget_subject_name like concat('%', #{po.budgetSubjectName}, '%')
            </if>
        </where>
        order by dab.id asc
    </select>


</mapper>
