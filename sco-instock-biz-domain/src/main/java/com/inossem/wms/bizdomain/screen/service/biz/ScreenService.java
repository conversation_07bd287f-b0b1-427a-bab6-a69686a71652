package com.inossem.wms.bizdomain.screen.service.biz;

import com.inossem.wms.bizdomain.screen.service.component.ScreenComponent;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 大屏
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Service
@Slf4j
public class ScreenService {

    @Autowired
    protected ScreenComponent screenComponent;

    /**
     * 大屏-运单量统计查询
     */
    public void getDeliveryWaybillCount(BizContext ctx) {
        screenComponent.getDeliveryWaybillCount(ctx);
    }

    /**
     * 大屏-货代运单量统计查询
     */
    public void getDeliveryWaybillSupplierCount(BizContext ctx) {
        screenComponent.getDeliveryWaybillSupplierCount(ctx);
    }

    /**
     * 大屏-运单月度统计查询
     */
    public void getDeliveryWaybillMonthCount(BizContext ctx) {
        screenComponent.getDeliveryWaybillMonthCount(ctx);
    }

    /**
     * 大屏-运单运输中单据查询
     */
    public void getDeliveryWaybillInTransitReceipt(BizContext ctx) {
        screenComponent.getDeliveryWaybillInTransitReceipt(ctx);

    }

}
