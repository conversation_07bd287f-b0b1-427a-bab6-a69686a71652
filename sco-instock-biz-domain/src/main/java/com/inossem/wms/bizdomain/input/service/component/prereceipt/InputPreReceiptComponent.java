package com.inossem.wms.bizdomain.input.service.component.prereceipt;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.erp.service.biz.ProductionReceiptService;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.spec.service.biz.BizSpecFeatureValueService;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumLabelType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.enums.spec.EnumSpecClassifyType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputPreHeadVo;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.erp.dto.ErpProductionReceiptItemDTO;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 入库单基于前续单据创建组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class InputPreReceiptComponent {

    @Autowired
    protected PurchaseReceiptService purchaseReceiptService;

    @Autowired
    protected ProductionReceiptService productionReceiptService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BizSpecFeatureValueService bizSpecFeatureValueService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    /**
     * 基于采购订单创建【非同时模式】
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO<BizReceiptInputPreHeadVo> :"采购订单head结果集"}
     */
    public void purchaseReceiptNotSameMode(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询采购订单
        po.setIsReturnFlag(EnumRealYn.FALSE.getIntValue());
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (EnumReceiptType.PURCHASE_RECEIPT.getValue().equals(po.getPreReceiptType())) {
            // 装载返回对象
            MultiResultVO<BizReceiptInputPreHeadVo> returnVo = new MultiResultVO<>();
            // 调用SAP查询采购订单
            List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList =
                purchaseReceiptService.getErpPurchaseReceiptItemList(po, user);
            // 上下文返回参数
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                this.purchaseDataFormatNotSameMode(returnVo, purchaseReceiptItemVoList));
        }
    }

    /**
     * 基于生产订单创建【非同时模式】
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO <BizReceiptInputPreHeadVo> :"生产订单head结果集"}
     */
    public void productReceiptNotSameMode(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (EnumReceiptType.PRUDOCTION_RECEIPT.getValue().equals(po.getPreReceiptType())) {
            // 装载返回对象
            MultiResultVO<BizReceiptInputPreHeadVo> returnVo = new MultiResultVO<>();
            // 调用SAP查询生产订单
            List<ErpProductionReceiptItemDTO> productionReceiptItemVoList =
                productionReceiptService.getErpProductionReceiptItemList(po, user);
            // 上下文返回参数
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                this.productionDataFormatNotSameMode(returnVo, productionReceiptItemVoList));
        }
    }

    /**
     * 基于物料主数据【非同时模式】
     *
     * @in ctx 入参 {@link BizReceiptInputItemDTO :"入库物料信息"}
     * @out ctx 出参 {@link BizReceiptInputItemDTO :"回填出库物料【批次、批次特性】"}
     */
    public void setMatInfoNotSameMode(BizContext ctx) {
        // 入参上下文 - 入库物料信息
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 设置物料的【批次、批次特性】
        this.setMatInfo(po);
        // 填充行项目关联属性
        dataFillService.fillRlatAttrDataList(po.getItemList());
        // 设置入库物料信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(po.getItemList()));
    }

    /**
     * 采购订单查询参数转换【非同时模式】
     *
     * @param returnVo 要设置的返回数据
     * @param purchaseReceiptItemVoList 采购订单信息
     * @return 物料信息
     */
    private MultiResultVO<BizReceiptInputPreHeadVo> purchaseDataFormatNotSameMode(
        MultiResultVO<BizReceiptInputPreHeadVo> returnVo, List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList) {
        if (UtilCollection.isNotEmpty(purchaseReceiptItemVoList)) {
            // 采购订单查询参转换
            this.purchaseDataFormat(returnVo, purchaseReceiptItemVoList);
        }
        return returnVo;
    }

    /**
     * 采购订单查询参数转换
     *
     * @param returnVo 要设置的返回数据
     * @param purchaseReceiptItemVoList 采购订单信息
     * @return 物料信息
     */
    private void purchaseDataFormat(MultiResultVO<BizReceiptInputPreHeadVo> returnVo,
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList) {
        // 计算可入库数量
        List<Long> purchaseReceiptItemIdList =
            purchaseReceiptItemVoList.stream().map(ErpPurchaseReceiptItemDTO::getId).collect(Collectors.toList());
        QueryWrapper<BizReceiptInputItem> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptInputItem::getIsPost, EnumRealYn.FALSE.getIntValue());
        wrapper.lambda().ne(BizReceiptInputItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        wrapper.lambda().eq(BizReceiptInputItem::getReferReceiptType, EnumReceiptType.PURCHASE_RECEIPT.getValue());
        wrapper.lambda().in(BizReceiptInputItem::getReferReceiptItemId, purchaseReceiptItemIdList);
        List<BizReceiptInputItem> bizReceiptInputItemList = bizReceiptInputItemDataWrap.list(wrapper);
        Map<Long, List<BizReceiptInputItem>> bizReceiptInputItemMap =
            bizReceiptInputItemList.stream().collect(Collectors.groupingBy(BizReceiptInputItem::getReferReceiptItemId));
        for (ErpPurchaseReceiptItemDTO purchaseReceiptItemDTO : purchaseReceiptItemVoList) {
            Long purchaseReceiptItemId = purchaseReceiptItemDTO.getId();
            BigDecimal allQty = BigDecimal.ZERO;
            if (bizReceiptInputItemMap.containsKey(purchaseReceiptItemId)
                && !bizReceiptInputItemMap.get(purchaseReceiptItemId).isEmpty()) {
                // 可采购数量 = receiptQty（采购订单数量） - submitQty（已采购数量）- 大于草稿状态并且小于未记账的入库单qty
                // 大于草稿状态并且小于未记账的入库单qty
                allQty = bizReceiptInputItemMap.get(purchaseReceiptItemId).stream().map(BizReceiptInputItem::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            purchaseReceiptItemDTO.setReceiptQty(purchaseReceiptItemDTO.getReceiptQty()
                .subtract(purchaseReceiptItemDTO.getSubmitQty()).subtract(allQty));
        }
        // 装载返回数据
        List<BizReceiptInputPreHeadVo> headInfoList = new ArrayList<>();
        // 根据采购订单号分组
        LinkedHashMap<String,
            List<ErpPurchaseReceiptItemDTO>> purchaseMap = purchaseReceiptItemVoList.stream()
                .filter(x -> x.getReceiptQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors
                    .groupingBy(ErpPurchaseReceiptItemDTO::getReceiptCode, LinkedHashMap::new, Collectors.toList()));
        Set<String> keys = purchaseMap.keySet();
        for (String key : keys) {
            // 装载返回数据head
            BizReceiptInputPreHeadVo headInfo = new BizReceiptInputPreHeadVo();
            // 装载返回数据item
            List<BizReceiptInputItemDTO> itemInfoList = new ArrayList<>();
            List<ErpPurchaseReceiptItemDTO> purchaseItemList = purchaseMap.get(key);
            for (int i = 0; i < purchaseItemList.size(); i++) {
                ErpPurchaseReceiptItemDTO purchaseDTO = purchaseItemList.get(i);
                BizReceiptInputItemDTO itemInfo = new BizReceiptInputItemDTO();
                /* ******** 设置head列字段 ******** */
                if (i == 0) {
                    headInfo = UtilBean.newInstance(purchaseDTO, headInfo.getClass());
                    headInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                }
                /* ******** 设置item列字段 ******** */
                itemInfo = UtilBean.newInstance(purchaseDTO, itemInfo.getClass());
                itemInfo.setId(null);
                itemInfo.setHeadId(null);
                itemInfo.setRid(Const.STRING_EMPTY);
                itemInfo.setReceiptCode(Const.STRING_EMPTY);
                itemInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                itemInfo.setReferReceiptRid(purchaseDTO.getRid());
                // 订单数量
                itemInfo.setQty(purchaseDTO.getReceiptQty());
                itemInfo.setPreReceiptHeadId(purchaseDTO.getHeadId());
                itemInfo.setPreReceiptItemId(purchaseDTO.getId());
                itemInfo.setPreReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                itemInfo.setPreReceiptQty(purchaseDTO.getReceiptQty());
                itemInfo.setReferReceiptHeadId(purchaseDTO.getHeadId());
                itemInfo.setReferReceiptItemId(purchaseDTO.getId());
                itemInfo.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                // 获取缓存中仓库信息
                DicStockLocationDTO dicStockLocationDTO = dictionaryService.getLocationCacheById(UtilObject.getLongOrNull(purchaseDTO.getLocationId()));
                itemInfo.setWhId(dicStockLocationDTO==null?null:dicStockLocationDTO.getWhId());
                /* ******** 设置批次信息 ******** */
                BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                batchInfoDTO = UtilBean.newInstance(itemInfo, batchInfoDTO.getClass());
                batchInfoDTO.setId(null);
                batchInfoDTO.setPurchaseReceiptHeadId(purchaseDTO.getHeadId());
                batchInfoDTO.setPurchaseReceiptItemId(purchaseDTO.getId());
                batchInfoDTO.setPurchaseReceiptRid(purchaseDTO.getRid());
                batchInfoDTO.setPurchaseReceiptCode(purchaseDTO.getReceiptCode());
                batchInfoDTO.setBatchErp(purchaseDTO.getBatchErp());
                batchInfoDTO.setSpecStock(purchaseDTO.getSpecStock());
                batchInfoDTO.setSpecStockCode(purchaseDTO.getSpecStockCode());
                batchInfoDTO.setSpecStockName(purchaseDTO.getSpecStockName());
                itemInfo.setBizBatchInfoDTO(batchInfoDTO);
                itemInfoList.add(itemInfo);
            }
            headInfo.setChildren(itemInfoList);
            headInfoList.add(headInfo);
            // 获取物料特性
            bizSpecFeatureValueService.getSpecList(itemInfoList, BizReceiptInputItemDTO.class,
                EnumSpecClassifyType.QUALITY_TYPE.getValue());
        }
        returnVo.setResultList(headInfoList);
    }

    /**
     * 生产订单查询参数转换【非同时模式】
     *
     * @param returnVo 要设置的返回数据
     * @param productionReceiptItemVoList 生产订单信息
     * @return 物料信息
     */
    private MultiResultVO<BizReceiptInputPreHeadVo> productionDataFormatNotSameMode(
        MultiResultVO<BizReceiptInputPreHeadVo> returnVo,
        List<ErpProductionReceiptItemDTO> productionReceiptItemVoList) {
        if (UtilCollection.isNotEmpty(productionReceiptItemVoList)) {
            // 生产订单查询参转换
            this.productionDataFormat(returnVo, productionReceiptItemVoList);
        }
        return returnVo;
    }

    /**
     * 生产订单查询参转换
     *
     * @param returnVo 要设置的返回数据
     * @param productionReceiptItemVoList 生产订单信息
     * @return 物料信息
     */
    private void productionDataFormat(MultiResultVO<BizReceiptInputPreHeadVo> returnVo,
        List<ErpProductionReceiptItemDTO> productionReceiptItemVoList) {
        // 计算可入库数量
        List<Long> purchaseReceiptItemIdList =
            productionReceiptItemVoList.stream().map(ErpProductionReceiptItemDTO::getId).collect(Collectors.toList());
        QueryWrapper<BizReceiptInputItem> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptInputItem::getIsPost, EnumRealYn.FALSE.getIntValue());
        wrapper.lambda().ne(BizReceiptInputItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        wrapper.lambda().eq(BizReceiptInputItem::getReferReceiptType, EnumReceiptType.PRUDOCTION_RECEIPT.getValue());
        wrapper.lambda().in(BizReceiptInputItem::getReferReceiptItemId, purchaseReceiptItemIdList);
        List<BizReceiptInputItem> bizReceiptInputItemList = bizReceiptInputItemDataWrap.list(wrapper);
        Map<Long, List<BizReceiptInputItem>> bizReceiptInputItemMap =
            bizReceiptInputItemList.stream().collect(Collectors.groupingBy(BizReceiptInputItem::getReferReceiptItemId));
        for (ErpProductionReceiptItemDTO productionReceiptItemDTO : productionReceiptItemVoList) {
            Long productionReceiptItemId = productionReceiptItemDTO.getId();
            BigDecimal allQty = BigDecimal.ZERO;
            if (bizReceiptInputItemMap.containsKey(productionReceiptItemId)
                && !bizReceiptInputItemMap.get(productionReceiptItemId).isEmpty()) {
                // 可采购数量 = receiptQty（采购订单数量） - submitQty（已采购数量）- 大于草稿状态并且小于未记账的入库单qty
                // 大于草稿状态并且小于未记账的入库单qty
                allQty = bizReceiptInputItemMap.get(productionReceiptItemId).stream().map(BizReceiptInputItem::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            productionReceiptItemDTO.setReceiptQty(productionReceiptItemDTO.getReceiptQty()
                .subtract(productionReceiptItemDTO.getSubmitQty()).subtract(allQty));
        }
        // 装载返回数据
        List<BizReceiptInputPreHeadVo> headInfoList = new ArrayList<>();
        // 根据生产订单号分组
        LinkedHashMap<String,
            List<ErpProductionReceiptItemDTO>> prodcutionMap = productionReceiptItemVoList.stream()
                .filter(x -> x.getReceiptQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors
                    .groupingBy(ErpProductionReceiptItemDTO::getReceiptCode, LinkedHashMap::new, Collectors.toList()));
        Set<String> keys = prodcutionMap.keySet();
        for (String key : keys) {
            // 装载返回数据head
            BizReceiptInputPreHeadVo headInfo = new BizReceiptInputPreHeadVo();
            // 装载返回数据item
            List<BizReceiptInputItemDTO> itemInfoList = new ArrayList<>();
            List<ErpProductionReceiptItemDTO> productionItemList = prodcutionMap.get(key);
            for (int i = 0; i < productionItemList.size(); i++) {
                ErpProductionReceiptItemDTO productionDTO = productionItemList.get(i);
                BizReceiptInputItemDTO itemInfo = new BizReceiptInputItemDTO();
                /* ******** 设置head列字段 ******** */
                if (i == 0) {
                    headInfo = UtilBean.newInstance(productionDTO, headInfo.getClass());
                    headInfo.setReferReceiptCode(productionDTO.getReceiptCode());
                }
                /* ******** 设置item列字段 ******** */
                itemInfo = UtilBean.newInstance(productionDTO, itemInfo.getClass());
                itemInfo.setId(null);
                itemInfo.setHeadId(null);
                itemInfo.setRid(Const.STRING_EMPTY);
                itemInfo.setReceiptCode(Const.STRING_EMPTY);
                itemInfo.setReferReceiptCode(productionDTO.getReceiptCode());
                itemInfo.setReferReceiptRid(productionDTO.getRid());
                // 订单数量
                itemInfo.setQty(productionDTO.getReceiptQty());
                itemInfo.setPreReceiptHeadId(productionDTO.getHeadId());
                itemInfo.setPreReceiptItemId(productionDTO.getId());
                itemInfo.setPreReceiptType(EnumReceiptType.PRUDOCTION_RECEIPT.getValue());
                itemInfo.setPreReceiptQty(productionDTO.getReceiptQty());
                itemInfo.setReferReceiptHeadId(productionDTO.getHeadId());
                itemInfo.setReferReceiptItemId(productionDTO.getId());
                itemInfo.setReferReceiptType(EnumReceiptType.PRUDOCTION_RECEIPT.getValue());
                itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                // 获取缓存中仓库信息
                itemInfo.setWhId(dictionaryService.getLocationCacheById(productionDTO.getLocationId()).getWhId());
                /* ******** 设置批次信息 ******** */
                BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                batchInfoDTO = UtilBean.newInstance(itemInfo, batchInfoDTO.getClass());
                batchInfoDTO.setId(null);
                batchInfoDTO.setProductionReceiptHeadId(productionDTO.getHeadId());
                batchInfoDTO.setProductionReceiptItemId(productionDTO.getId());
                batchInfoDTO.setProductionReceiptRid(productionDTO.getRid());
                batchInfoDTO.setProductionReceiptCode(productionDTO.getReceiptCode());
                batchInfoDTO.setBatchErp(productionDTO.getBatchErp());
                batchInfoDTO.setSpecStock(productionDTO.getSpecStock());
                batchInfoDTO.setSpecStockCode(productionDTO.getSpecStockCode());
                batchInfoDTO.setSpecStockName(productionDTO.getSpecStockName());
                itemInfo.setBizBatchInfoDTO(batchInfoDTO);
                itemInfoList.add(itemInfo);
            }
            headInfo.setChildren(itemInfoList);
            headInfoList.add(headInfo);
            // 获取物料特性
            bizSpecFeatureValueService.getSpecList(itemInfoList, BizReceiptInputItemDTO.class,
                EnumSpecClassifyType.QUALITY_TYPE.getValue());
        }
        returnVo.setResultList(headInfoList);
    }


    /**
     * 设置物料的【批次、批次特性】
     *
     * @param headDTO 入库单
     */
    private void setMatInfo(BizReceiptInputHeadDTO headDTO) {
        for (BizReceiptInputItemDTO inputItemDTO : headDTO.getItemList()) {
            BizBatchInfoDTO bizBatchInfoDTO = new BizBatchInfoDTO();
            bizBatchInfoDTO = UtilBean.newInstance(inputItemDTO, bizBatchInfoDTO.getClass());
            bizBatchInfoDTO.setId(null);
            if (EnumReceiptType.STOCK_INPUT_OTHER.getValue().equals(headDTO.getReceiptType())) {
                bizBatchInfoDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_OTHER_INPUT.getValue());
                bizBatchInfoDTO.setIsSingle(EnumLabelType.BATCH.getValue());//单品/批次  0批次 1单品
                bizBatchInfoDTO.setTagType(EnumTagType.GENERAL.getValue());//标签类型 0：普通标签 1：RFID抗金属  2：RFID非抗金属
            }
            if (EnumReceiptType.STOCK_WASTE_MATERIALS_INPUT.getValue().equals(headDTO.getReceiptType())) {
                bizBatchInfoDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_WASTER.getValue());
            }
            inputItemDTO.setBizBatchInfoDTO(bizBatchInfoDTO);
            inputItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
        }
        // 获取物料特性
        bizSpecFeatureValueService.getSpecList(headDTO.getItemList(), BizReceiptInputItemDTO.class,
            EnumSpecClassifyType.QUALITY_TYPE.getValue());
    }

}