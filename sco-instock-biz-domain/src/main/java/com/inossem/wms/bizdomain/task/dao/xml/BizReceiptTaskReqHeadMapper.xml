<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.task.dao.BizReceiptTaskReqHeadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqHead">
        <id column="id" property="id"/>
        <result column="receipt_code" property="receiptCode"/>
        <result column="receipt_type" property="receiptType"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="remark" property="remark"/>
        <result column="is_write_off" property="isWriteOff"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="modify_user_id" property="modifyUserId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, receipt_code, receipt_type, receipt_status, remark, is_write_off, is_delete, create_time, modify_time, create_user_id, modify_user_id
    </sql>


    <select id="selectStockTaskReqList" parameterType="com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskSearchPo"
            resultType="com.inossem.wms.common.model.bizdomain.task.vo.BizReceiptTaskVO">
        select
        head.receipt_code receipt_req_code, head.create_user_id, head.create_time,
        head.id, item.pre_receipt_type, item.pre_receipt_item_id, item.wh_id,
        item.pre_receipt_head_id, item.refer_receipt_head_id, head.receipt_status,head.des,
        task_item.submit_user_id
        from
        biz_receipt_task_req_head head
        <!-- 关联行项目表 -->
        inner join biz_receipt_task_req_item item on head.id = item.head_id
        inner join sys_user su on head.create_user_id = su.id
        left join biz_receipt_task_item task_item on item.id = task_item.task_req_item_id
        left join dic_material dm on dm.id = item.mat_id
        <if test="po.preReceiptCode !=null and po.preReceiptCode !=''">
            left join biz_receipt_input_head as input_head on  item.pre_receipt_head_id =  input_head.id
            left join biz_receipt_output_head as output_head on  item.pre_receipt_head_id =  output_head.id
            left join biz_receipt_transport_head as transport_head on  item.pre_receipt_head_id =  transport_head.id
            left join biz_receipt_return_head as return_head on  item.pre_receipt_head_id =  return_head.id
        </if>
        <if test="po.preApplyReceiptCode !=null and po.preApplyReceiptCode !=''">
            left join biz_receipt_output_item as output_item on  item.pre_receipt_item_id =  output_item.id
            left join biz_receipt_apply_head as apply_head on output_item.pre_receipt_head_id = apply_head.id
        </if>
        where head.is_delete = 0
        <!-- 单据状态 -->
        <if test="po.statusList!=null and po.statusList.size>0">
            and head.receipt_status in
            <foreach collection="po.statusList" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <!-- 单据号 -->
        <if test="po.receiptCode!=null and po.receiptCode !=''">
            AND head.receipt_code like CONCAT('%', #{po.receiptCode})
        </if>
        <!-- 单据类型 -->
        <if test="po.receiptType!=null">
            and head.receipt_type = #{po.receiptType}
        </if>
        <!-- 前序单据号 -->
        <if test="po.preReceiptCode !=null and po.preReceiptCode !=''">
            and (
            input_head.receipt_code= #{po.preReceiptCode} or
            output_head.receipt_code= #{po.preReceiptCode} or
            transport_head.receipt_code= #{po.preReceiptCode} or
            return_head.receipt_code= #{po.preReceiptCode}
            )
        </if>
        <!-- 前序申请单号 -->
        <if test="po.preApplyReceiptCode !=null and po.preApplyReceiptCode !=''">
            and apply_head.receipt_code= #{po.preApplyReceiptCode}
        </if>
        <!-- 创建人 -->
        <if test="po.createUserId !=null and po.createUserId !=''">
            and su.id = #{po.createUserId}
        </if>
        <if test="po.matCode !=null and po.matCode !=''">
            and dm.mat_code like concat('%', #{po.matCode} , '%')
        </if>
        <if test="po.matId !=null and po.matId !=''">
            and dm.id = #{po.matId}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND item.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by
        head.id
        order by
        head.create_time desc
    </select>

    <update id="updateStatusById">
        update biz_receipt_task_req_head set receipt_status=#{status} where id=#{id}
    </update>

    <update id="updateStatusByIds">
        update biz_receipt_task_req_head set receipt_status=#{status} where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")" index="index">
            #{item}
        </foreach>
    </update>


    <select id="selectUnitizedStockTaskReqList" parameterType="com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskSearchPo"
            resultType="com.inossem.wms.common.model.bizdomain.task.vo.BizReceiptTaskVO">
        select
        head.receipt_code receipt_req_code, head.create_user_id, head.create_time,
        head.id, item.pre_receipt_type, item.pre_receipt_item_id, item.wh_id,
        item.pre_receipt_head_id, item.refer_receipt_head_id, head.receipt_status,head.des,head.merge
        from
        biz_receipt_task_req_head head
        <!-- 关联行项目表 -->
        inner join biz_receipt_task_req_item item on head.id = item.head_id
        inner join sys_user su on head.create_user_id = su.id
        left join dic_material dm on dm.id = item.mat_id
        <if test="po.preReceiptCode !=null and po.preReceiptCode !=''">
            left join biz_receipt_input_head as input_head on  item.pre_receipt_head_id =  input_head.id
            left join biz_receipt_output_head as output_head on  item.pre_receipt_head_id =  output_head.id
            left join biz_receipt_transport_head as transport_head on  item.pre_receipt_head_id =  transport_head.id
            left join biz_receipt_return_head as return_head on  item.pre_receipt_head_id =  return_head.id
        </if>
        left join biz_batch_info on biz_batch_info.id = item.batch_id
        where head.is_delete = 0
        <!-- 单据状态 -->
        <if test="po.statusList!=null and po.statusList.size>0">
            and head.receipt_status in
            <foreach collection="po.statusList" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <!-- 单据号 -->
        <if test="po.receiptCode!=null and po.receiptCode !=''">
            AND head.receipt_code like CONCAT('%', #{po.receiptCode})
        </if>
        <!-- 单据类型 -->
        <if test="po.receiptType!=null">
            and head.receipt_type = #{po.receiptType}
        </if>
        <!-- 前序单据号 -->
        <if test="po.preReceiptCode !=null and po.preReceiptCode !=''">
            and (
            input_head.receipt_code= #{po.preReceiptCode} or
            output_head.receipt_code= #{po.preReceiptCode} or
            transport_head.receipt_code= #{po.preReceiptCode} or
            return_head.receipt_code= #{po.preReceiptCode}
            )
        </if>
        <!-- 创建人 -->
        <if test="po.createUserId !=null and po.createUserId !=''">
            and su.id = #{po.createUserId}
        </if>
        <if test="po.matCode !=null and po.matCode !=''">
            and dm.mat_code = #{po.matCode}
        </if>
        <if test="po.matId !=null and po.matId !=''">
            and dm.id = #{po.matId}
        </if>
        <if test="po.matName !=null and po.matName !=''">
            and dm.mat_name like CONCAT('%', #{po.matName},'%')
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND item.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.reReceiptType!=null">
            and item.pre_receipt_type = #{po.reReceiptType}
        </if>
        <if test="po.upCode!=null">
            and biz_batch_info.extend29 = #{po.reReceiptType}
        </if>
        <if test="po.reqMatOutReceiptIdList != null and po.reqMatOutReceiptIdList.size() > 0">
            and item.pre_receipt_head_id in
            <foreach collection="po.reqMatOutReceiptIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
            and item.pre_receipt_type = 108
        </if>
        group by
        head.id
        order by
        head.create_time desc
    </select>
</mapper>
