package com.inossem.wms.bizdomain.apply.service.component;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleRuleDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelReceiptRelDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputSignatureGraphDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.apply.EnumReceiveBasis;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssembleRule;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemGroupByDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplyQueryListPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputSignatureGraphDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputInfo;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputSignatureGraph;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.apply.ReceiveBasisMapVO;
import com.inossem.wms.common.model.file.img.entity.BizCommonImage;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.label.po.KeyGenerator;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.file.service.datawrap.BizCommonImageDataWrap;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/23 11:22
 * @desc EmergencyMatOrderOutApplyComponent
 */
@Slf4j
@Service
public class EmergencyMatOrderOutApplyComponent {

    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private BizReceiptApplyItemDataWrap bizReceiptApplyItemDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;
    @Autowired
    private I18nTextCommonService i18nTextCommonService;
    @Autowired
    private BizReceiptAssembleRuleDataWrap bizReceiptAssembleRuleDataWrap;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private BizReceiptOutputSignatureGraphDataWrap signatureGraphDataWrap;
    @Autowired
    private BizLabelReceiptRelDataWrap labelReceiptRelDataWrap;
    @Autowired
    private UserService userService;
    @Autowired
    private BizCommonImageDataWrap commonImageDataWrap;
    @Autowired
    private ApprovalService approvalService;


    /**
     * 获取领用依据枚举
     */
    public void getReceiveBasis(BizContext ctx) {
        MultiResultVO<ReceiveBasisMapVO> vo = new MultiResultVO<>(EnumReceiveBasis.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<BizReceiptApplyHeadDTO> resultVO =
                new BizResultVO<>(applyHeadDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_EMERGENCY_MAT_REQ_APPLY.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()).setCreateUserDeptName(userService.getSysUserInfoById(user.getId()).getDeptName()),
                        new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 获取详情
     *
     * @param ctx
     */
    public void getInfo(BizContext ctx) {

        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        BizReceiptApplyHeadDTO applyHeadDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        // 属性填充
        dataFillService.fillAttr(applyHeadDTO);
        // 填充打印数据
        applyHeadDTO.setCreateUserDeptName(applyHeadDTO.getCreateDeptName());
        this.fillSignature(applyHeadDTO);
        List<BizReceiptApplyItemGroupByDTO> applyItemGroupByDTOS = new ArrayList<>();
        Map<KeyGenerator, List<BizReceiptApplyItemDTO>> keyGeneratorListMap = applyHeadDTO.getItemList().stream().collect(Collectors.groupingBy(e -> new KeyGenerator(e.getMatId(), e.getFtyId(), e.getLocationId(), e.getBatchId(), e.getWhId(), e.getTypeId(), e.getBinId(), 0L)));
        keyGeneratorListMap.forEach((key, value) -> {
            BizReceiptApplyItemGroupByDTO bizReceiptApplyItemGroupByDTO = new BizReceiptApplyItemGroupByDTO();
            bizReceiptApplyItemGroupByDTO.setMatCode(value.get(0).getMatCode());
            bizReceiptApplyItemGroupByDTO.setMatName(value.get(0).getMatName());
            bizReceiptApplyItemGroupByDTO.setUnitName(value.get(0).getUnitName());
            bizReceiptApplyItemGroupByDTO.setQty(value.stream().map(BizReceiptApplyItemDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            bizReceiptApplyItemGroupByDTO.setActualQty(value.stream().map(BizReceiptApplyItemDTO::getActualQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            applyItemGroupByDTOS.add(bizReceiptApplyItemGroupByDTO);
        });
        applyHeadDTO.setGroupItemList(applyItemGroupByDTOS);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(applyHeadDTO);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(applyHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 申请单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交,删除】
            return buttonVO.setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue().equals(receiptStatus)) {
            // 待出库-【关闭，提交】
            return buttonVO.setButtonCloseOnly(true).setButtonSubmit(true);
        }
        return buttonVO;
    }


    /**
     * 获取分页
     *
     * @param ctx
     */
    public void gePage(BizContext ctx) {
        BizReceiptApplyQueryListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 物料编码检索条件
        List<Long> headIds = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (UtilNumber.isNotEmpty(matId)) {
                List<BizReceiptApplyItem> receiptApplyItems = bizReceiptApplyItemDataWrap.list(new QueryWrapper<BizReceiptApplyItem>()
                        .lambda()
                        .eq(BizReceiptApplyItem::getMatId, matId)
                );
                headIds = receiptApplyItems.stream().map(BizReceiptApplyItem::getHeadId).collect(Collectors.toList());
            } else {
                // 设置了物料编码查询条件，但没有符合的物料时，直接返回空列表
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(new ArrayList<>(), 0L));
                return;
            }
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList = null;
        if (user != null && user.getLocationList() != null) {
            List<DicStockLocationDTO> locationList = user.getLocationList();
            locationIdList = locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptApplyQueryListPO> wmsQueryWrapper = new WmsQueryWrapper<>();
        wmsQueryWrapper.lambda()
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getReceiptType, BizReceiptApplyHead.class, EnumReceiptType.STOCK_OUTPUT_EMERGENCY_MAT_REQ_APPLY.getValue())
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getIsDelete, BizReceiptApplyHead.class, 0)
                .eq(UtilObject.isNotNull(po.getReceiveType()), BizReceiptApplyQueryListPO::getReceiveType, BizReceiptApplyHead.class, po.getReceiveType())
                .in(UtilCollection.isNotEmpty(headIds), BizReceiptApplyQueryListPO::getId, BizReceiptApplyHead.class, headIds)
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptApplyQueryListPO::getReceiptStatus, po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList), BizReceiptApplyQueryListPO::getLocationId,
                        BizReceiptApplyItem.class, locationIdList)
                .between(UtilObject.isNotNull(po.getApplyStartTime()) && UtilObject.isNotNull(po.getApplyEndTime()),
                        BizReceiptApplyQueryListPO::getApplyTime, BizReceiptOutputInfo.class, po.getApplyStartTime(), po.getApplyEndTime())
                .between(UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime()),
                        BizReceiptApplyQueryListPO::getCreateTime, BizReceiptApplyHead.class, po.getStartTime(), po.getEndTime())
                .likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptApplyQueryListPO::getReceiptCode, BizReceiptApplyHead.class, po.getReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getRemark()), BizReceiptApplyQueryListPO::getRemark, BizReceiptApplyHead.class, po.getRemark())
                .like(UtilString.isNotNullOrEmpty(po.getUserName()), BizReceiptApplyQueryListPO::getUserName, SysUser.class, po.getUserName())
        ;
        Page<BizReceiptApplyPageVO> pageObj = (Page<BizReceiptApplyPageVO>) po.getPageObj(BizReceiptApplyPageVO.class);
        pageObj.setOptimizeCountSql(false);
        bizReceiptApplyHeadDataWrap.getApplyInfoPageVOList(pageObj, wmsQueryWrapper);
        dataFillService.fillRlatAttrDataList(pageObj.getRecords());
        long totalCount = pageObj.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(pageObj.getRecords(), totalCount));
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }
    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.TOOL_BORROW_APPLY.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 保存-校验入参
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "紧急领用出库"}
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

    }

    /**
     * 填充签字信息，包括实际领料人签字和保管员的签字信息
     *
     * @param headDTO
     */
    private void fillSignature(BizReceiptApplyHeadDTO headDTO) {

        BizReceiptOutputSignatureGraph receiverSignatureObj = signatureGraphDataWrap.getOne(new QueryWrapper<BizReceiptOutputSignatureGraph>()
                .lambda()
                .eq(BizReceiptOutputSignatureGraph::getHeadId, headDTO.getId())
                .eq(BizReceiptOutputSignatureGraph::getSignatureType, EnumOutputSignatureType.ACTUAL_RECEIVER.getValue()));
        BizReceiptOutputSignatureGraphDTO receiverSignDTO = UtilBean.newInstance(receiverSignatureObj, BizReceiptOutputSignatureGraphDTO.class);
        headDTO.setMatReceiverSignature(receiverSignDTO);
        if (UtilObject.isEmpty(receiverSignatureObj)) {
            receiverSignDTO.setSignatureType(1);
            receiverSignDTO.setSignatureGraph(StrUtil.EMPTY);
        }

        BizReceiptOutputSignatureGraph keeperSignatureObj = signatureGraphDataWrap.getOne(new QueryWrapper<BizReceiptOutputSignatureGraph>()
                .lambda()
                .eq(BizReceiptOutputSignatureGraph::getHeadId, headDTO.getId())
                .eq(BizReceiptOutputSignatureGraph::getSignatureType, EnumOutputSignatureType.STORE_KEEPER.getValue()));
        BizReceiptOutputSignatureGraphDTO keeperSignDTO = UtilBean.newInstance(keeperSignatureObj, BizReceiptOutputSignatureGraphDTO.class);
        headDTO.setStoreKeeperSignature(keeperSignDTO);
        if (UtilObject.isEmpty(keeperSignatureObj) && EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
            keeperSignDTO.setSignatureType(2);
            keeperSignDTO.setSignatureGraph(StrUtil.EMPTY);

        }
        BizReceiptOutputSignatureGraphDTO bizReceiptOutputSignatureGraphDTO = new BizReceiptOutputSignatureGraphDTO();
        if (UtilNumber.isNotEmpty(headDTO.getAssignUserId())) {
            SysUserDTO userDTO = userService.getSysUserInfoById(headDTO.getAssignUserId());
            if (Objects.nonNull(userDTO)) {
                Long commonImgId = userDTO.getCommonImgId();
                BizCommonImage commonImage = commonImageDataWrap.getById(commonImgId);
                if (UtilObject.isNotEmpty(commonImage)) {
                    bizReceiptOutputSignatureGraphDTO.setSignatureType(1);
                    bizReceiptOutputSignatureGraphDTO.setSignatureGraph(commonImage.getImgBase64());
                }
            }
        }
        headDTO.setAssignerSignature(bizReceiptOutputSignatureGraphDTO);

    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void saveApply(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue().equals(po.getReceiptStatus())) {
            // 更新实发数量
            po.getItemList().forEach(item -> {
                if (item.getActualQty().compareTo(BigDecimal.ZERO) > 0) {
                    bizReceiptApplyItemDataWrap.updateDtoById(item);
                }
            });
            // 保存单据标签关联
            // this.saveLabelReceiptRel(po);
            // 保存签字信息
            this.saveSignatureGraph(po);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, po.getReceiptCode());
            return;
        }
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.STOCK_OUTPUT_EMERGENCY_MAT_REQ_APPLY.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新紧急领用出库
            bizReceiptApplyHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteItem(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSeqEmergencyOrder(EnumSequenceCode.EMERGENCY_MATERIAL_ORDER.getValue(), po.getItemList().get(EnumRealYn.FALSE.getIntValue()).getFtyCode());
            po.setReceiptCode(code);
            bizReceiptApplyHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存紧急领用出库head成功!单号{},主键{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptApplyItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(po.getItemList());
        log.debug("批量保存紧急领用出库item成功,code{},headId{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }

    private void saveLabelReceiptRel(BizReceiptApplyHeadDTO po) {
        // 保存标签
        List<BizLabelReceiptRel> labelReceiptRelList = new ArrayList<>();
        for (BizReceiptApplyItemDTO itemDTO : po.getItemList()) {
            for (StockBinDTO stockBinDTO : itemDTO.getStockBinDTOList()) {
                for (BizLabelData bizLabelData : stockBinDTO.getLabelDataList()) {
                    BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                    labelReceiptRel.setLabelId(bizLabelData.getId());
                    labelReceiptRel.setReceiptType(po.getReceiptType());
                    labelReceiptRel.setReceiptHeadId(po.getId());
                    labelReceiptRel.setReceiptItemId(itemDTO.getId());
                    labelReceiptRelList.add(labelReceiptRel);
                }
            }
        }
        labelReceiptRelDataWrap.saveBatch(labelReceiptRelList);
    }

    private void saveSignatureGraph(BizReceiptApplyHeadDTO po) {
        // 签名信息处理
        if (UtilObject.isNotNull(po.getMatReceiverSignature())
                && UtilString.isNotNullOrEmpty(po.getMatReceiverSignature().getSignatureGraph())) {
            // 实际领料人签名信息
            BizReceiptOutputSignatureGraphDTO receiverSignature = po.getMatReceiverSignature();
            receiverSignature.setId(null);
            receiverSignature.setHeadId(po.getId());
            receiverSignature.setSignatureType(EnumOutputSignatureType.ACTUAL_RECEIVER.getValue());
            signatureGraphDataWrap.saveDto(receiverSignature);
        }
        if (UtilObject.isNotNull(po.getStoreKeeperSignature())
                && UtilString.isNotNullOrEmpty(po.getStoreKeeperSignature().getSignatureGraph())) {
            // 保管员签名信息
            BizReceiptOutputSignatureGraphDTO keeperSignature = po.getStoreKeeperSignature();
            keeperSignature.setId(null);
            keeperSignature.setHeadId(po.getId());
            keeperSignature.setSignatureType(EnumOutputSignatureType.STORE_KEEPER.getValue());
            signatureGraphDataWrap.saveDto(keeperSignature);
        }
    }

    /**
     * 删除申请行项目
     *
     * @param headDTO 紧急领用出库
     */
    private void deleteItem(BizReceiptApplyHeadDTO headDTO) {
        UpdateWrapper<BizReceiptApplyItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptApplyItem::getHeadId,
                headDTO.getId());
        bizReceiptApplyItemDataWrap.physicalDelete(wrapper);
    }

    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的紧急领用出库
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }


    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存紧急领用出库附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.STOCK_OUTPUT_EMERGENCY_MAT_REQ_APPLY.getValue(), user.getId());
        log.debug("保存紧急领用出库附件成功!");
    }


    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submitApply(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存紧急领用出库
        this.saveApply(ctx);
        // 更新紧急领用出库head、item状态 - 审批中
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())) {
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        }
        // 待出库提交后变为已完成
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue().equals(po.getReceiptStatus())) {
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
    }

    /**
     * 准备更新紧急领用出库状态
     *
     * @param headDTO     紧急领用出库head
     * @param itemDTOList 紧急领用出库item
     */
    public void updateStatus(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新紧急领用出库head状态
     *
     * @param headDto 紧急领用出库head
     */
    private void updateHead(BizReceiptApplyHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptApplyHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新紧急领用出库item状态
     *
     * @param itemDtoList 紧急领用出库item
     */
    private void updateItem(List<BizReceiptApplyItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptApplyItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 已经是待出库或是已完成状态，无需再次发起审批
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue().equals(headDTO.getReceiptStatus()) ||
                EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
            return;
        }
        // 当用户在“领用依据”时选择第五项，则提交后触发审批，
        // 审批完成后，单据由审批中变为待出库，
        // 其他状态不需要审批，单据直接变为待出库状态
        if (headDTO.getReceiveBasis().equals(EnumReceiveBasis.LEADERSHIP_APPROVAL.getValue())) {
            // 发起流程审批
            Long receiptId = headDTO.getId();
            String receiptCode = headDTO.getReceiptCode();
            Integer receiptType = headDTO.getReceiptType();
            Map<String, Object> variables = new HashMap<>();
            Long ftyId = headDTO.getItemList().get(0).getFtyId();
            variables.put("ftyId", ftyId);
            // 物项合同科审核人员存入流程变量，作为一级审核节点审批人
            if (Objects.isNull(headDTO.getAssignUserId())) {
                throw new WmsException("审核人员缺失");
            }
            List<String> userCode = new ArrayList<>();
            userCode.add(dictionaryService.getSysUserCacheById(headDTO.getAssignUserId()).getUserCode());
            variables.put("userCode", userCode);
            workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getRemark());
            workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        } else {
            // 更新head、item状态 - 待出库
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue());
        }
    }


    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            // 更新状态待出库
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue());

        } else {
            // 更新状态已驳回
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 刪除申请单
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "申请单单号"}
     */
    public void deleteInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置申请单
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHeadDataWrap.getById(headId), BizReceiptApplyHeadDTO.class);
        // 只有已驳回状态才可以删除
        if (!headDTO.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue())) {
            String receiptStatusI18n = i18nTextCommonService.getNameMessage(Const.DEFAULT_LANG_CODE, "receiptStatus", String.valueOf(headDTO.getReceiptStatus()));
            throw new WmsException(StrUtil.format("{}状态不支持删除单据", receiptStatusI18n));
        }
        // 逻辑删除抬头表
        bizReceiptApplyHeadDataWrap.removeById(headId);
        // 逻辑删除行项目表
        QueryWrapper<BizReceiptApplyItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptApplyItem::getHeadId, headId);
        bizReceiptApplyItemDataWrap.remove(itemWrapper);
        // 逻辑删除assemble表
        QueryWrapper<BizReceiptAssemble> assembleWrapper = new QueryWrapper<>();
        assembleWrapper.lambda().eq(BizReceiptAssemble::getReceiptHeadId, headId);
        bizReceiptAssembleDataWrap.remove(assembleWrapper);
        // 申请单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 申请单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
    }


    /**
     * 删除申请单附件
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(po.getId(), po.getReceiptType());
    }

    public void closeReceipt(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if (!headDTO.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue())) {
            String receiptStatusI18n = i18nTextCommonService.getNameMessage(Const.DEFAULT_LANG_CODE, "receiptStatus", String.valueOf(headDTO.getReceiptStatus()));
            throw new WmsException(StrUtil.format("{}状态不支持关闭单据", receiptStatusI18n));
        }
        // 待出库状态下支持关闭单据，关闭后单据直接变为已完成，实发数量为0
        headDTO.getItemList().forEach(item -> item.setActualQty(BigDecimal.ZERO));
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 申请单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }


    /**
     * 配货
     *
     * @param ctx 系统上下文
     */
    public void distribution(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        Long itemId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptApplyItem applyItem = bizReceiptApplyItemDataWrap.getById(itemId);
        BizReceiptApplyItemDTO itemDTO = UtilBean.newInstance(applyItem, BizReceiptApplyItemDTO.class);
        dataFillService.fillAttr(itemDTO);
        /* ************************ 组装返回结构 **********************************/
        // 配货
        BizReceiptAssembleRuleSearchPO rulePo = new BizReceiptAssembleRuleSearchPO();
        rulePo.setMatId(itemDTO.getMatId());
        rulePo.setLocationId(itemDTO.getLocationId());
        rulePo.setFtyId(itemDTO.getFtyId());
        rulePo.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, EnumReceiptType.STOCK_OUTPUT_EMERGENCY_MAT_REQ_APPLY.getValue());
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);
        List<String> featureCodeList = Arrays.asList(specFeature.getFeatureCode().split(Const.COMMA));
        String itemSpecCode = "";
        String itemSpecValue = "";
        for (int i = 0; i < featureCodeList.size(); i++) {
            String specCode = featureCodeList.get(i);
            // 去表名
            String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
            // 转驼峰
            fieldName = UtilMetadata.underlineToHump(fieldName);
            // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
            String specValue = UtilObject.getStringOrEmpty(UtilReflect.getValueByFieldNullReturnNull(fieldName, itemDTO));
            if (UtilString.isNullOrEmpty(specValue)) {
                // 特性值为空 则不参加特性匹配
                continue;
            }
            if (i == 0) {
                itemSpecCode = specCode;
                itemSpecValue = specValue;
            } else {
                itemSpecCode = itemSpecCode + Const.COMMA + specCode;
                itemSpecValue = itemSpecValue + Const.COMMA + specValue;
            }
        }
        itemDTO.setSpecCode(itemSpecCode);
        itemDTO.setSpecValue(itemSpecValue);
        List<StockBinDTO> stockBinDTOList = stockCommonService.getStockBinByFeatureCodeAndValue(itemDTO,
                itemDTO.getFtyId(), itemDTO.getLocationId(), itemDTO.getMatId(),
                EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), itemDTO.getSpecStock());
        // List<BizLabelData> labelDataList = new ArrayList<>();
        // stockBinDTOList.forEach(
        //         stockBin -> {
        //             labelDataList.addAll(stockBin.getLabelDataList());
        //         }
        // );
        // itemDTO.setBizLabelDataList(labelDataList);
        itemDTO.setStockBinDTOList(stockBinDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, itemDTO);
    }

    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptApplyItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        String specStock = Const.STRING_EMPTY;
        // 领料出库 领料申请出库设置specStock为“Q”
        if (EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue().equals(po.getReceiptType()) ||
                EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue().equals(po.getReceiptType()) ||
                EnumReceiptType.STOCK_OUTPUT_EMERGENCY_MAT_REQ_APPLY.getValue().equals(po.getReceiptType())) {
            specStock = EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue();
        }
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
        // 配置matId
        pos.setMatId(matId);
        pos.setStockStatus(stockStatus);
        pos.setSpecStock(specStock);
        if (UtilString.isNullOrEmpty(pos.getSpecStockCode())) {
            pos.setSpecStockCode(null);
        }
        pos.setIsApplyFlag(po.getIsApplyFlag());
        pos.setReferReceiptCodePara(po.getReferReceiptCode());
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(), pos);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptApplyItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptApplyItemDTO.class);
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTO.setBinId(assembleDTO.getBinIdTemp());
                itemDTO.setTypeId(assembleDTO.getTypeIdTemp());
                itemDTOList.add(itemDTO);
            }

        }
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        matStockDTO.setApplyItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

}
