package com.inossem.wms.bizdomain.require.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.require.entity.BizReceiptRequireHead;
import com.inossem.wms.bizdomain.require.dao.BizReceiptRequireHeadMapper;
import com.inossem.wms.common.model.bizdomain.require.po.BizReceiptRequireSearchPO;
import com.inossem.wms.common.model.bizdomain.require.vo.BizReceiptRequirePageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 需求计划提报抬头表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Service
public class BizReceiptRequireHeadDataWrap extends BaseDataWrap<BizReceiptRequireHeadMapper, BizReceiptRequireHead> {

    public void getPage(IPage<BizReceiptRequirePageVO> page, WmsQueryWrapper<BizReceiptRequireSearchPO> queryWrapper) {
        page.setRecords(this.baseMapper.getPage(page, queryWrapper));
    }
}
