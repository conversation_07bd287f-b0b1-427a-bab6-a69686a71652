package com.inossem.wms.bizdomain.paper.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.paper.dao.BizReceiptPaperItemMapper;
import com.inossem.wms.common.model.bizdomain.paper.dto.BizReceiptPaperItemDTO;
import com.inossem.wms.common.model.bizdomain.paper.entity.BizReceiptPaperItem;
import com.inossem.wms.common.model.bizdomain.paper.po.BizReceiptPaperSearchPO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 图纸物料信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class BizReceiptPaperItemDataWrap extends BaseDataWrap<BizReceiptPaperItemMapper, BizReceiptPaperItem> {

    public void getPage(IPage<BizReceiptPaperItemDTO> page, WmsQueryWrapper<BizReceiptPaperSearchPO> queryWrapper) {
        page.setRecords(this.baseMapper.getPage(page, queryWrapper));
    }
}
