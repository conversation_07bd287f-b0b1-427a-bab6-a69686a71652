package com.inossem.wms.bizdomain.transport.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.transport.dao.BizReceiptTransportHeadMapper;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 转储单表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
@Service
public class BizReceiptTransportHeadDataWrap extends BaseDataWrap<BizReceiptTransportHeadMapper, BizReceiptTransportHead> {

    public IPage<BizReceiptTransportHeadDTO> selectTransportPageVoListByPo(IPage<BizReceiptTransportHeadDTO> page, BizReceiptTransportHeadSearchPO po) {
        return page.setRecords(this.baseMapper.selectTransportPageVoListByPo(page, po));
    }

    public IPage<BizReceiptTransportHeadDTO> selectTransportPageVoListByPoUnitized(IPage<BizReceiptTransportHeadDTO> page, BizReceiptTransportHeadSearchPO po) {
        return page.setRecords(this.baseMapper.selectTransportPageVoListByPoUnitized(page, po));
    }

    public IPage<BizReceiptTransportHeadDTO> getLeisurePageVOList(IPage<BizReceiptTransportHeadDTO> page, BizReceiptTransportHeadSearchPO po) {
        return page.setRecords(this.baseMapper.getLeisurePageVOList(page, po));
    }
}
