package com.inossem.wms.bizdomain.budget.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizdomain.budget.service.datawrap.AnnualBudgetDataWrap;
import com.inossem.wms.bizdomain.budget.service.datawrap.BudgetSubjectDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.budget.dto.DicBudgetSubjectDTO;
import com.inossem.wms.common.model.masterdata.budget.entity.DicAnnualBudget;
import com.inossem.wms.common.model.masterdata.budget.entity.DicBudgetSubject;
import com.inossem.wms.common.model.masterdata.budget.po.DicBudgetSubjectSearchPO;
import com.inossem.wms.common.model.masterdata.budget.vo.DicBudgetSubjectPageVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@Slf4j
@Service
public class BudgetSubjectService {

    @Autowired
    private BudgetSubjectDataWrap budgetSubjectDataWrap;

    @Autowired
    private AnnualBudgetDataWrap annualBudgetDataWrap;

    @Autowired
    private DataFillService dataFillService;

    /**
     * 获取分页数据的方法。
     * 该方法从上下文中获取查询参数，并执行分页查询，返回包含查询结果和总记录数的PageObjectVO对象。
     *
     * @param ctx 业务上下文对象，包含查询参数
     * @return 返回包含分页数据和总记录数的PageObjectVO对象
     */
    public PageObjectVO<DicBudgetSubjectPageVO> getPage(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        DicBudgetSubjectSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<DicBudgetSubjectPageVO> result;
        long total;
        // 分页查询处理
        if (po.isPaging()) {
            IPage<DicBudgetSubjectPageVO> page = po.getPageObj(DicBudgetSubjectPageVO.class);
            result = budgetSubjectDataWrap.selectPageVOList(page, po);
            total = page.getTotal();
        } else {
            result = budgetSubjectDataWrap.selectPageVOList(null, po);
            total = result.size();
        }
        return new PageObjectVO<>(result, total);
    }

    /**
     * 获取预算科目信息的方法。
     *
     * @param ctx 业务上下文，包含请求参数
     * @return 返回包含预算科目信息的单结果对象
     */
    public SingleResultVO<DicBudgetSubjectDTO> getInfo(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicBudgetSubject budgetSubjectDataWrapById = budgetSubjectDataWrap.getById(id);

        DicBudgetSubjectDTO DicBudgetSubjectDTO = UtilBean.newInstance(budgetSubjectDataWrapById, DicBudgetSubjectDTO.class);
        dataFillService.fillAttr(DicBudgetSubjectDTO);
        return new SingleResultVO<>(DicBudgetSubjectDTO);
    }

    /**
     * 添加或更新预算科目信息。
     *
     * @param ctx 业务上下文，包含当前用户和待处理的数据对象
     */
    public void addOrUpdate(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        CurrentUser currentUser = ctx.getCurrentUser();
        DicBudgetSubjectDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNullOrEmpty(po.getBudgetSubjectCode()) || UtilString.isNullOrEmpty(po.getBudgetSubjectName()) || Objects.isNull(po.getBudgetClassifyId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 新增
        if (Objects.isNull(po.getId())) {
            // 校验编码是否已存在
            budgetSubjectDataWrap.list(new QueryWrapper<DicBudgetSubject>().lambda().eq(DicBudgetSubject::getBudgetSubjectCode, po.getBudgetSubjectCode())).stream().findFirst().ifPresent(e -> {
                throw new WmsException("预算科目编码" + po.getBudgetSubjectCode() + "已存在");
            });
            // 校验名称是否已存在
            budgetSubjectDataWrap.list(new QueryWrapper<DicBudgetSubject>().lambda().eq(DicBudgetSubject::getBudgetSubjectName, po.getBudgetSubjectName())).stream().findFirst().ifPresent(e -> {
                throw new WmsException("预算科目名称" + po.getBudgetSubjectName() + "已存在");
            });
            po.setCreateUserId(currentUser.getId());
            budgetSubjectDataWrap.saveDto(po);
        } else {
            // 校验名称是否已存在
            budgetSubjectDataWrap.list(new QueryWrapper<DicBudgetSubject>().lambda().eq(DicBudgetSubject::getBudgetSubjectName, po.getBudgetSubjectName()).ne(DicBudgetSubject::getId, po.getId())).stream().findFirst().ifPresent(e -> {
                throw new WmsException("预算科目名称" + po.getBudgetSubjectName() + "已存在");
            });
            po.setModifyUserId(currentUser.getId());
            budgetSubjectDataWrap.updateDtoById(po);

        }

    }

    /**
     * 根据给定的上下文删除预算科目信息，并返回删除的预算科目代码。
     *
     * @param ctx 业务上下文，包含删除操作所需的数据
     * @return 返回删除的预算科目代码
     */
    public String remove(BizContext ctx) {

        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        annualBudgetDataWrap.list(new QueryWrapper<DicAnnualBudget>().lambda().eq(DicAnnualBudget::getBudgetSubjectId, id)).stream().findAny().ifPresent(e -> {
            throw new WmsException("该预算科目关联了年度预算,不能删除");
        });
        // budgetSubjectDataWrap.checkPurchaseApplyReceiptExist(id).stream().findFirst().ifPresent(e -> {
        //     throw new WmsException("该预算科目关联了采购申请" + e.getReceiptCode() + "，不能删除");
        // });

        // 设置返回code
        String budgetSubjectCode = budgetSubjectDataWrap.getById(id).getBudgetSubjectCode();

        budgetSubjectDataWrap.physicalDeleteById(id);
        return budgetSubjectCode;
    }

}
