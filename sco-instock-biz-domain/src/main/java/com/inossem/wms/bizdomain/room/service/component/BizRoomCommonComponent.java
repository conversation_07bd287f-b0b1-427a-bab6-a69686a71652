package com.inossem.wms.bizdomain.room.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierUserRelDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.contract.EnumContractFirstParty;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoom;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomUsageHead;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomUsageItem;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomSearchPO;
import com.inossem.wms.common.model.bizdomain.room.vo.BizRoomCheckInUserVO;
import com.inossem.wms.common.model.bizdomain.room.vo.BizRoomPageVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplierUserRel;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 房间管理公共组件库
 *
 **/
@Slf4j
@Component
public class BizRoomCommonComponent {

    @Autowired
    private BizRoomDataWrap bizRoomDataWrap;

    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;

    @Autowired
    private DicSupplierUserRelDataWrap dicSupplierUserRelDataWrap;


    /**
     * 获取当前用户名下申请的房间列表
     */
    public void getCurrentUserRoomList(BizContext ctx) {

        // 获取当前用户名下申请的房间列表
        List<BizRoomPageVO> resultList = bizRoomDataWrap.getPageVo(null, new WmsLambdaQueryWrapper<BizRoomSearchPO>()
                .eq(true, BizRoomSearchPO::getIsDelete, BizRoom.class, EnumRealYn.FALSE.getIntValue())
                .eq(true, BizRoomSearchPO::getApplicantUserId, BizRoomUsageHead.class, ctx.getCurrentUserId())
        );

        // 设置返回信息到上下文
        ctx.setVoContextData(new MultiResultVO<>(resultList));
    }


    /**
     * 获取当前用户名下申请的房间入住人员信息
     */
    public void getCurrentUserRoomCheckInUserList(BizContext ctx) {
        // 上下文入参
        BizRoomSearchPO po = ctx.getPoContextData();

        // 分页处理
        IPage<BizRoomCheckInUserVO> page = po.isPaging() ? po.getPageObj(BizRoomCheckInUserVO.class) : null;

        // 获取当前用户名下申请的房间入住人员信息
        List<BizRoomCheckInUserVO> resultList = bizRoomDataWrap.selectRoomCheckInUser(null, new WmsLambdaQueryWrapper<BizRoomSearchPO>()
                .like(UtilString.isNotNullOrEmpty(po.getRoomCode()), BizRoomSearchPO::getRoomCode, BizRoomUsageHead.class, po.getRoomCode())
                .eq(true, BizRoomSearchPO::getApplicantUserId, BizRoomUsageHead.class, ctx.getCurrentUserId())
                .eq(true, BizRoomSearchPO::getCheckOutReqItemId, BizRoomUsageItem.class, Const.ZEROL)
        );

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(resultList, po.isPaging() ? page.getTotal() : resultList.size()));
    }


    /**
     * 获取当前用户对应供应商的合同列表(执行中的合同)
     */
    public void getCurrentSupplierContractList(BizContext ctx) {
        BizReceiptContractSearchPO po = ctx.getPoContextData();

        // 如果当前查询需要进行数据权限控制，则查看用户角色权限，或者获取当前用户对应的供应商信息列表
        if(EnumRealYn.TRUE.getIntValue().equals(po.getIsHaveDataPermissions())){
            // 有查询全部数据权限的角色列表
            List<String> roleList = Arrays.asList(Const.JS01_ROLE_CODE,  Const.JS04_ROLE_CODE, Const.JS23_ROLE_CODE, Const.JS24_ROLE_CODE);
            // 查看当前登录人是否有权限查看全部数据
            long roleCount = ctx.getCurrentUser().getSysUserRoleRelList().stream().filter(obj -> roleList.contains(obj.getRoleCode())).count();

            // 如果当前登录人不是超级管理员，则查看当前登录人的供应商信息
            if(roleCount == Const.ZEROL) {
                // 获取当前用户对应的供应商信息列表
                List<DicSupplierUserRel> supplierUserRelList = dicSupplierUserRelDataWrap.list(new LambdaQueryWrapper<DicSupplierUserRel>()
                        .eq(DicSupplierUserRel::getUserId, ctx.getCurrentUserId())
                );

                // 当前用户不是供应商，则直接返回空列表
                if (UtilCollection.isEmpty(supplierUserRelList)) {
                    // 设置返回信息到上下文
                    ctx.setVoContextData(new MultiResultVO<>(new ArrayList<>()));

                    return;
                }

                po.setSupplierId(supplierUserRelList.get(0).getSupplierId());
            }
        }

        po.setReceiptStatusList(Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_EXECUTING.getValue()));

        // 获取合同信息
        List<BizReceiptContractHead> bizReceiptContractHeadList = bizReceiptContractHeadDataWrap.list(new LambdaQueryWrapper<BizReceiptContractHead>()
                // 只查甲方是华信的数据
                .eq(BizReceiptContractHead::getFirstParty, EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode())
                .in(BizReceiptContractHead::getReceiptStatus, po.getReceiptStatusList())
                .eq(UtilNumber.isNotEmpty(po.getSupplierId()), BizReceiptContractHead::getSupplierId, po.getSupplierId())
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptContractHead::getReceiptCode, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptContractHead::getContractName, po.getContractName())
        );

        List<BizReceiptContractHeadDTO> resultList = UtilCollection.toList(bizReceiptContractHeadList, BizReceiptContractHeadDTO.class);

        // 设置返回信息到上下文
        ctx.setVoContextData(new MultiResultVO<>(resultList));

        return;

    }

    /**
     * 获取房间管理相关业务中的可用合同列表
     */
    public List<BizReceiptContractHead> getContractList(BizReceiptContractSearchPO po) {

        List<BizReceiptContractHead> resultList = bizReceiptContractHeadDataWrap.list(new LambdaQueryWrapper<BizReceiptContractHead>()
                // 只查甲方是华信的数据
                .eq(BizReceiptContractHead::getFirstParty, EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode())
                .eq(UtilNumber.isNotEmpty(po.getSupplierId()), BizReceiptContractHead::getSupplierId, po.getSupplierId())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptContractHead::getReceiptStatus, po.getReceiptStatusList())
                .eq(po.getValidityEndDateEnd() != null, BizReceiptContractHead::getValidityEndDate, UtilDate.getStartOfDay(po.getValidityEndDateEnd()))
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptContractHead::getReceiptCode, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptContractHead::getContractName, po.getContractName())
        );

        return resultList;
    }

}