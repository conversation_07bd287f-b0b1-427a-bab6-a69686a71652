package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizUnitizedUPSplitHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.arrange.dto.BizReceiptArrangeHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizUnitizedUPSplitHeadDTO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizUnitizedUPSplitItemDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizUnitizedUPSplitHead;
import com.inossem.wms.common.model.bizdomain.unitized.po.BizUnitizedUPSplitHeadSavePO;
import com.inossem.wms.common.model.bizdomain.unitized.po.BizUnitizedUPSplitHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.unitized.vo.BizUnitizedUPSplitHeadPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.label.dto.PrintItemDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 成套设备UP码拆分抬头 服务接口
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Slf4j
@Component
public class BizUnitizedUPSplitHeadComponent {

    @Autowired
    private BizUnitizedUPSplitHeadDataWrap bizUnitizedUPSplitHeadDataWrap;
    @Autowired
    private BizUnitizedUPSplitItemComponent bizUnitizedUPSplitItemComponent;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private ApprovalService approvalService;

    /**
     * 获取成套设备UP码拆分抬头 - 分页列表
     */
    public PageObjectVO<BizUnitizedUPSplitHeadPageVO> getPage(BizContext ctx) {
        BizUnitizedUPSplitHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询
        IPage<BizUnitizedUPSplitHeadPageVO> page = po.getPageObj(BizUnitizedUPSplitHeadPageVO.class);
        // 设置是否可以全量查询，直接取count数量
        bizUnitizedUPSplitHeadDataWrap.getPageVOList(page, po);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 获取成套设备UP码拆分抬头详情
     */
    public void getInfo(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("成套设备UP码拆分抬头详情查询 id：{}", id);
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizUnitizedUPSplitHead entity = bizUnitizedUPSplitHeadDataWrap.getById(id);
        log.info("成套设备UP码拆分抬头id：{}，详情：{}", id, JSONObject.toJSONString(entity));
        BizUnitizedUPSplitHeadDTO dto = UtilBean.newInstance(entity, BizUnitizedUPSplitHeadDTO.class);
        // 数据填充
        dataFillService.fillRlatAttrForDataObj(dto);
        List<BizUnitizedUPSplitItemDTO> itemList = bizUnitizedUPSplitItemComponent.getItemList(dto.getId());
        dto.setSourceItemDTOList(itemList);
        // 设置按钮组权限
        ButtonVO buttonVO = this.bizSetButton(dto);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(dto, new ExtendVO(), buttonVO));
    }

    /**
     * 设置按钮权限
     */
    protected ButtonVO bizSetButton(BizUnitizedUPSplitHeadDTO dto) {
        ButtonVO buttonVO = new ButtonVO();
        if (dto.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                || dto.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue())) {
            buttonVO.setButtonSubmit(Boolean.TRUE);
        }if (dto.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            buttonVO.setButtonPrint(Boolean.TRUE);
        }

        return buttonVO;
    }

    /**
     * 新增或修改方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(BizContext ctx) {
        BizUnitizedUPSplitHeadSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        CurrentUser currentUser = ctx.getCurrentUser();
        log.info("新增/修改成套设备UP码拆分抬头 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        po.setReceiptType(EnumReceiptType.UNITIZED_UP_SPLIT.getValue());
        if (UtilNumber.isEmpty(po.getId())) {//新增
            String receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_UP_SPLIT.getValue());
            po.setId(null);
            po.setReceiptCode(receiptCode);
            po.setCreateUserId(currentUser.getId());
            po.setCreateTime(new Date());
            po.setModifyUserId(currentUser.getId());
            po.setModifyTime(new Date());
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        } else {//修改
            po.setModifyUserId(currentUser.getId());
            po.setModifyTime(new Date());
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        }
        if (bizUnitizedUPSplitHeadDataWrap.saveOrUpdateDto(po)) {
            log.info("成套设备UP码拆分抬头：{}，保存成功", po.getId());
            //保存行项目
            bizUnitizedUPSplitItemComponent.save(po.getId(), po.getSourceItemDTOList());
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, po);
    }


    /**
     * 数据校验
     */
    public void check(BizContext ctx) {
        BizUnitizedUPSplitHeadSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PARAMETER_LOST_EXCEPTION);
        }
        if (UtilCollection.isEmpty(po.getSourceItemDTOList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        List<BizUnitizedUPSplitItemDTO> notSplitItemList = po.getSourceItemDTOList().stream().filter(e -> UtilCollection.isEmpty(e.getSplitItemDTOList())).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(notSplitItemList)) {
            String matCodeStr = notSplitItemList.stream().map(BizUnitizedUPSplitItemDTO::getMatCode).collect(Collectors.joining(Const.COMMA));
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
        for (BizUnitizedUPSplitItemDTO sourceItem : po.getSourceItemDTOList()) {
            boolean lostParam = sourceItem.getSplitItemDTOList().stream()
                    .anyMatch(e -> UtilString.isNullOrEmpty(e.getMatName())
                            || UtilNumber.isEmpty(e.getUnitId())
                            || UtilString.isNullOrEmpty(e.getMatType())
                            || UtilNumber.isEmpty(e.getQty()));
            if (lostParam) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NAME_CAN_NOT_BE_EMPTY);
            }
        }
    }

    /**
     * 更新单据状态 - 已完成
     */
    public void completed(BizContext ctx) {
        BizUnitizedUPSplitHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizUnitizedUPSplitHead entity = new BizUnitizedUPSplitHead();
        entity.setId(po.getId());
        entity.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        bizUnitizedUPSplitHeadDataWrap.updateById(entity);
        //完成所有行项目
        bizUnitizedUPSplitItemComponent.completed(entity.getId());
    }

    /**
     * 查询物资类型下拉列表
     */
    public MultiResultVO<String> getMatTypeDropDown() {
        List<String> list = EnumMatType.toList();
        return new MultiResultVO<>(list);
    }

    /**
     * 保存操作日志
     *
     * @param ctx 系统上下文
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的验收单
        BizUnitizedUPSplitHeadSavePO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT, "", ctx.getCurrentUser().getId());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizUnitizedUPSplitHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

//    public void checkPrint(BizContext ctx) {
//        // 从上下文获取单据head id
//        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
//        Long headId = po.getBizLabelPrintDTO().getHeadId();
//        // head id 为空
//        if (UtilNumber.isEmpty(headId)) {
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
//        }
//        // 过滤行项目
//        if (UtilCollection.isEmpty(po.getBizLabelPrintDTO().getPrintItemDTOList())) {
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
//        }
//        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID,headId);
//        this.getInfo(ctx);
//        BizResultVO<BizUnitizedUPSplitHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
//        BizUnitizedUPSplitHeadDTO headDTO = vo.getHead();
//
//        Map<Long, PrintItemDTO> printMap = po.getBizLabelPrintDTO().getPrintItemDTOList().stream().collect(Collectors.toMap(e -> e.getItemId(), e -> e));
//        List<BizUnitizedUPSplitItemDTO> printItemList = new ArrayList<>();
//        for (BizUnitizedUPSplitItemDTO itemDTO : headDTO.getSourceItemDTOList()) {
//            printItemList.addAll(itemDTO.getSplitItemDTOList());
//        }
//        if (UtilCollection.isEmpty(printItemList)) {
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
//        }
//        for (BizUnitizedUPSplitItemDTO itemDTO : printItemList) {
//            PrintItemDTO print = printMap.get(itemDTO.getId());
//            itemDTO.setPrintNum(print.getPrintNum());
//        }
//        // headDTO填充入参
//        po.setHeadDTO(headDTO);
//    }

    public void fillPrintData(BizContext ctx) {

    }


    public void setInit(BizContext ctx) {
        // 页面初始化返回空的行项目、扩展项对象、按钮组【保存和提交】
        BizResultVO<BizUnitizedUPSplitHeadDTO> resultVO = new BizResultVO<>(
                new BizUnitizedUPSplitHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_UP_SPLIT.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSubmit(true));
        // 页面初始化数据放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        // 发起流程审批
        BizUnitizedUPSplitHeadSavePO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        // 审批校验
//        Integer receiptType = this.approveCheck(ctx);
        Integer receiptType = EnumReceiptType.UNITIZED_UP_SPLIT.getValue();
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        Long ftyId=headDTO.getSourceItemDTOList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        // 用户所属部门、对口部门、对口科室
        variables.put("userDept", userDept);
        variables.put("professionalEngineerUserCode", headDTO.getProfessionalEngineerUserCode());
//        String counterpartDeptCode = headDTO.getCounterpartDeptCode();
//        String counterpartOfficeCode = headDTO.getCounterpartOfficeCode();
//        variables.put("counterpartDeptCode", counterpartDeptCode);
//        variables.put("counterpartOfficeCode", counterpartOfficeCode);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getDes());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        //审批中
        UpdateWrapper<BizUnitizedUPSplitHead> headUpdateWrapper = new UpdateWrapper<>();
        headUpdateWrapper.lambda().set(BizUnitizedUPSplitHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue()).eq(BizUnitizedUPSplitHead::getId, headDTO.getId());
        bizUnitizedUPSplitHeadDataWrap.update(headUpdateWrapper);
        //更新单据行项目审批中
        bizUnitizedUPSplitItemComponent.approving(headDTO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext idCtx = new BizContext();
        idCtx.setContextData(Const.BIZ_CONTEXT_KEY_ID,wfReceiptCo.getReceiptHeadId());
        this.getInfo(idCtx);
        BizResultVO<BizUnitizedUPSplitHeadDTO> vo = idCtx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizUnitizedUPSplitHeadDTO headDTO = vo.getHead();
        // 封装上下文
        idCtx.setCurrentUser(wfReceiptCo.getInitiator());
        idCtx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            //创建CT物料
            bizUnitizedUPSplitItemComponent.createCTMat(idCtx);
            //生成批次
            bizUnitizedUPSplitItemComponent.createBatchInfo(idCtx);
            //创建标签
            bizUnitizedUPSplitItemComponent.createLabel(idCtx);
            //更新关联数据id
            bizUnitizedUPSplitItemComponent.updateItemRelId(idCtx);
            //更新数据为已完成状态
            this.completed(idCtx);
            //生成库存
            bizUnitizedUPSplitItemComponent.createStock(idCtx);
        } else {
            // 单据状态已驳回
            this.updateStatusRejected(headDTO.getId());
        }
    }

    private void updateStatusRejected(Long id) {
        //拒绝
        UpdateWrapper<BizUnitizedUPSplitHead> headUpdateWrapper = new UpdateWrapper<>();
        headUpdateWrapper.lambda().set(BizUnitizedUPSplitHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue()).eq(BizUnitizedUPSplitHead::getId, id);
        bizUnitizedUPSplitHeadDataWrap.update(headUpdateWrapper);
        //更新单据行项目拒绝
        bizUnitizedUPSplitItemComponent.rejected(id);
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"UP码拆分","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"UP码拆分单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizUnitizedUPSplitHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        boolean wfByReceiptType = EnumReceiptType.UNITIZED_UP_SPLIT.getValue().equals(resultVO.getHead().getReceiptType());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

}
