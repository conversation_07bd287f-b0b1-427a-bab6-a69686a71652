package com.inossem.wms.bizdomain.arrange.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.arrange.entity.BizReceiptArrangeHead;
import com.inossem.wms.common.model.bizdomain.arrange.po.BizReceiptArrangeSearchPo;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓库整理单抬头表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
public interface BizReceiptArrangeHeadMapper extends WmsBaseMapper<BizReceiptArrangeHead> {

    List<BizReceiptArrangeHead> getArrangeHeadList(IPage<BizReceiptArrangeHead> page, @Param("po") BizReceiptArrangeSearchPo po);

    List<BizReceiptArrangeHead> getArrangeHeadListUnitized(IPage<BizReceiptArrangeHead> page, @Param("po") BizReceiptArrangeSearchPo po);
}
