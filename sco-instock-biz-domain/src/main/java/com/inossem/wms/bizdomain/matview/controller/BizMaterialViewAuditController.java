package com.inossem.wms.bizdomain.matview.controller;

import com.inossem.wms.bizdomain.matview.service.biz.BizMaterialViewAuditService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditDTO;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditFactoryDTO;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditNuclearDTO;
import com.inossem.wms.common.model.bizdomain.matview.po.BizMaterialViewAuditSavePO;
import com.inossem.wms.common.model.bizdomain.matview.po.BizMaterialViewAuditSearchPO;
import com.inossem.wms.common.model.bizdomain.matview.vo.BizMaterialViewAuditPageVO;
import com.inossem.wms.common.model.common.base.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 物料主数据视图审批 Api 接口
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@RestController
@Slf4j
@ApiModel(value = "BizMaterialViewAuditController", description = "物料主数据视图审批")
public class BizMaterialViewAuditController {

    @Autowired
    private BizMaterialViewAuditService bizMaterialViewAuditService;

    @ApiOperation(value = "验收入库-初始化", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/bizMaterialViewAudit/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizMaterialViewAuditDTO>> init(BizContext ctx) {
        bizMaterialViewAuditService.init(ctx);
        BizResultVO<BizMaterialViewAuditDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "分页获取物料主数据视图审批列表", tags = {"物料主数据视图审批"})
    @PostMapping("/bizMaterialViewAudit/getPage")
    public BaseResult<PageObjectVO<BizMaterialViewAuditPageVO>> getPage(@RequestBody BizMaterialViewAuditSearchPO po) {
        return BaseResult.success(bizMaterialViewAuditService.getPage(po));
    }

    @ApiOperation(value = "按照物料主数据视图审批id查找物料主数据视图审批", tags = {"物料主数据视图审批"})
    @GetMapping(path = "/bizMaterialViewAudit/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizMaterialViewAuditDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        bizMaterialViewAuditService.get(ctx);
        BizResultVO<BizMaterialViewAuditDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "新增物料主数据视图审批信息", notes = "对物料主数据视图审批信息进行添加", tags = {"物料主数据视图审批"})
    @PostMapping(path = "/bizMaterialViewAudit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody BizMaterialViewAuditSavePO po, BizContext ctx) {
        bizMaterialViewAuditService.save(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "修改物料主数据视图审批信息", notes = "对物料主数据视图审批信息进行修改", tags = {"物料主数据视图审批"})
    @PutMapping(path = "/bizMaterialViewAudit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody BizMaterialViewAuditSavePO po, BizContext ctx) {
        bizMaterialViewAuditService.save(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "提交物料主数据视图审批信息", notes = "提交物料主数据视图审批信息", tags = {"物料主数据视图审批"})
    @PostMapping(path = "/bizMaterialViewAudit/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizMaterialViewAuditSavePO po, BizContext ctx) {
        bizMaterialViewAuditService.submit(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "按照物料主数据视图审批id删除", notes = "逻辑删除", tags = {"物料主数据视图审批"})
    @DeleteMapping(path = "/bizMaterialViewAudit/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id) {
        bizMaterialViewAuditService.remove(id);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "物料主数据视图审批-工厂物料信息导入", notes = "物料主数据视图审批-工厂物料信息导入", tags = {"物料主数据视图审批"})
    @PostMapping(path = "/bizMaterialViewAudit/fty-mat/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizMaterialViewAuditFactoryDTO>> importFtyMat(@RequestPart("file") MultipartFile file, BizContext ctx) {
        List<BizMaterialViewAuditFactoryDTO> list = bizMaterialViewAuditService.importFtyMat(ctx);
        return BaseResult.success(new MultiResultVO<>(list));
    }

    @ApiOperation(value = "物料主数据视图审批-工厂物料信息导出", tags = {"物料主数据视图审批"})
    @GetMapping(path = "/bizMaterialViewAudit/fty-mat/excel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportFtyMat(@PathVariable("id") Long id, BizContext ctx) {
        bizMaterialViewAuditService.exportFtyMat(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "物料主数据视图审批-核电物料信息导入", notes = "物料主数据视图审批-工厂物料信息导入", tags = {"物料主数据视图审批"})
    @PostMapping(path = "/bizMaterialViewAudit/nuc-mat/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizMaterialViewAuditNuclearDTO>> importNucMat(@RequestPart("file") MultipartFile file, BizContext ctx) {
        List<BizMaterialViewAuditNuclearDTO> list = bizMaterialViewAuditService.importNucMat(ctx);
        return BaseResult.success(new MultiResultVO<>(list));
    }

    @ApiOperation(value = "物料主数据视图审批-核电物料信息导出", tags = {"物料主数据视图审批"})
    @GetMapping(path = "/bizMaterialViewAudit/nuc-mat/excel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportNucMat(@PathVariable("id") Long id, BizContext ctx) {
        bizMaterialViewAuditService.exportNucMat(ctx);
        return BaseResult.success();
    }
}
