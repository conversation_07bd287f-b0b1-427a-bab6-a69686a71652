package com.inossem.wms.bizdomain.bi.dao;

import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.*;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 大屏 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Mapper
public interface BiFinanceMapper {

    /**
     * BI-毛利分析
     */
    BiGrossMarginVO selectGrossMargin(@Param("po") BiSearchPO po);

    /**
     * BI-经营性现金流
     */
    List<BiOperatingCashVO> selectOperatingCash(@Param("po") BiSearchPO po);

    /**
     * BI-成本趋势分析
     */
    List<BiCostTrendVO> selectCostTrendTotalCost(@Param("po") BiSearchPO po);

    /**
     * BI-成本趋势分析-成本分组
     */
    List<BiCostTrendVO> selectCostTrendCostGroup(@Param("po") BiSearchPO po);

    /**
     * BI-收款金额
     */
    List<BiReceivableAmountVO> selectReceivableAmount(@Param("po") BiSearchPO po);

    /**
     * BI-收入分析-煤价调整
     */
    List<BiIncomeAnalysisVO> selectIncomeAnalysis(@Param("po") BiSearchPO po);

    /**
     * BI-收入分析-煤价调整总和
     */
    BiIncomeAnalysisVO selectIncomeAnalysisSum(@Param("po") BiSearchPO po);

    /**
     * BI-收入分析-所有
     */
    List<BiIncomeAnalysisVO> selectIncomeAnalysisAll(@Param("po") BiSearchPO po);

    /**
     * BI-收入分析-所有总和
     */
    BiIncomeAnalysisVO selectIncomeAnalysisAllSum(@Param("po") BiSearchPO po);

    /**
     * BI-管理费用
     */
    List<BiManagementExpenseVO> selectManagementExpense(@Param("po") BiSearchPO po);

    /**
     * BI-财务费用
     */
    List<BiManagementExpenseVO> getFinancialExpense(@Param("po") BiSearchPO po);

    /**
     * BI-管理和财务费用分析-总和
     */
    BiExpenseSumVO getExpenseSum(@Param("po") BiSearchPO po);

}
