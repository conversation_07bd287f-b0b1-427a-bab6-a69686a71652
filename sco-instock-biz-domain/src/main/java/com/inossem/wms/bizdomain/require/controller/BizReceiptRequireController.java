package com.inossem.wms.bizdomain.require.controller;


import com.inossem.wms.bizdomain.require.service.biz.BizReceiptRequireService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.require.dto.BizReceiptRequireHeadDTO;
import com.inossem.wms.common.model.bizdomain.require.dto.BizReceiptRequireItemDTO;
import com.inossem.wms.common.model.bizdomain.require.po.BizReceiptRequireSearchPO;
import com.inossem.wms.common.model.bizdomain.require.vo.BizReceiptRequirePageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成套设备-需求管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@RestController
public class BizReceiptRequireController {


    @Autowired
    protected BizReceiptRequireService bizReceiptRequireService;

    /**
     * 初始化
     */
    @ApiOperation(value = "初始化", tags = {"成套设备-需求管理"})
    @PostMapping(value = "/unitized/require/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRequireHeadDTO>> init(BizContext ctx) {
        bizReceiptRequireService.init(ctx);
        BizResultVO<BizReceiptRequireHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 初始化-非图纸需求
     */
    @ApiOperation(value = "初始化", tags = {"成套设备-需求管理"})
    @PostMapping(value = "/unitized/not-paper-require/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRequireHeadDTO>> notPaperRequireInit(BizContext ctx) {
        bizReceiptRequireService.notPaperRequireInit(ctx);
        BizResultVO<BizReceiptRequireHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 分页
     */
    @ApiOperation(value = "分页", tags = {"成套设备-需求管理"})
    @PostMapping(value = "/unitized/require/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptRequirePageVO>> getPage(@RequestBody BizReceiptRequireSearchPO po, BizContext ctx) {
        bizReceiptRequireService.getPage(ctx);
        PageObjectVO<BizReceiptRequirePageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     */
    @ApiOperation(value = "详情", tags = {"成套设备-需求管理"})
    @GetMapping(value = "/unitized/require/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRequireHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        bizReceiptRequireService.getInfo(ctx);
        BizResultVO<BizReceiptRequireHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     */
    @ApiOperation(value = "保存", tags = {"成套设备-需求管理"})
    @PostMapping(value = "/unitized/require/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptRequireHeadDTO po, BizContext ctx) {
        bizReceiptRequireService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 提交
     */
    @ApiOperation(value = "提交", tags = {"成套设备-需求管理"})
    @PostMapping(value = "/unitized/require/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptRequireHeadDTO po, BizContext ctx) {
        bizReceiptRequireService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 非图纸物料需求汇总查询分页
     */
    @ApiOperation(value = "非图纸物料需求汇总查询分页", tags = {"成套设备-图纸管理"})
    @PostMapping(value = "/unitized/require/results-not-paper-require", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptRequireItemDTO>> getNotPaperRequirePage(@RequestBody BizReceiptRequireSearchPO po, BizContext ctx) {
        bizReceiptRequireService.getNotPaperRequirePage(ctx);
        PageObjectVO<BizReceiptRequireItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 添加需求信息查询分页
     */
    @ApiOperation(value = "添加需求信息查询分页", tags = {"成套设备-图纸管理"})
    @PostMapping(value = "/unitized/require/results-require", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptRequireItemDTO>> getRequirePage(@RequestBody BizReceiptRequireSearchPO po, BizContext ctx) {
        bizReceiptRequireService.getRequirePage(ctx);
        PageObjectVO<BizReceiptRequireItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}
