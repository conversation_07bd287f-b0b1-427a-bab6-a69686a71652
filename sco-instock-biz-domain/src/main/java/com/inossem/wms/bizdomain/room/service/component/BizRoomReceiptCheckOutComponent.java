package com.inossem.wms.bizdomain.room.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptCheckOutHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptCheckOutItemDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomReceiptCheckOutRoomDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomUsageHeadDataWrap;
import com.inossem.wms.bizdomain.room.service.datawrap.BizRoomUsageItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.contract.EnumContractFirstParty;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckOutHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckOutItemDTO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckOutRoomDTO;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoom;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptCheckOutHead;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptCheckOutItem;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptCheckOutRoom;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomUsageHead;
import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomUsageItem;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomReceiptSearchPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDataFill;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilSequence;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 住房退订组件库
 *
 **/
@Slf4j
@Component
public class BizRoomReceiptCheckOutComponent {

    @Autowired
    private BizRoomDataWrap bizRoomDataWrap;

    @Autowired
    private BizRoomUsageHeadDataWrap bizRoomUsageHeadDataWrap;

    @Autowired
    private BizRoomUsageItemDataWrap bizRoomUsageItemDataWrap;

    @Autowired
    private BizRoomReceiptCheckOutHeadDataWrap bizRoomReceiptCheckOutHeadDataWrap;

    @Autowired
    private BizRoomReceiptCheckOutItemDataWrap bizRoomReceiptCheckOutItemDataWrap;

    @Autowired
    private BizRoomReceiptCheckOutRoomDataWrap bizRoomReceiptCheckOutRoomDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private SysUserDataWrap  sysUserDataWrap;

    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;

    /**
     * 页面初始化
     *
     */
    public void init(BizContext ctx) {
        BizRoomReceiptCheckOutHeadDTO headDTO = new BizRoomReceiptCheckOutHeadDTO()
                .setReceiptType(EnumReceiptType.ROOM_CHECK_OUT_REP.getValue())
                .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName());

        BizResultVO<BizRoomReceiptCheckOutHeadDTO> resultVO = new BizResultVO<>(
                headDTO
                , new ExtendVO().setAttachmentRequired(true)
                , this.setButton(headDTO));

        // 设置页面初始化数据到上下文
        ctx.setVoContextData(resultVO);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizRoomReceiptSearchPO po = ctx.getPoContextData();

        if (UtilCollection.isEmpty(po.getReceiptStatusList())) {
            ctx.setVoContextData(new PageObjectVO<BizRoomReceiptCheckOutHeadDTO>(new ArrayList(), 0L));
            return;
        }

        // 分页处理
        IPage<BizRoomReceiptCheckOutHead> page = po.getPageObj(BizRoomReceiptCheckOutHead.class);

        // 分页查询
        bizRoomReceiptCheckOutHeadDataWrap.page(page, new LambdaQueryWrapper<BizRoomReceiptCheckOutHead>()
                .in(BizRoomReceiptCheckOutHead::getReceiptStatus, po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizRoomReceiptCheckOutHead::getReceiptCode, po.getReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUser()), BizRoomReceiptCheckOutHead::getCreateUserName, po.getCreateUser())
                .ge(po.getCreateTimeStart() != null, BizRoomReceiptCheckOutHead::getCreateTime, UtilDate.getStartOfDay(po.getCreateTimeStart()))
                .le(po.getCreateTimeEnd() != null, BizRoomReceiptCheckOutHead::getCreateTime, UtilDate.getEndOfDay(po.getCreateTimeEnd()))
                .orderByDesc(BizRoomReceiptCheckOutHead::getCreateTime)
        );

        List<BizRoomReceiptCheckOutHeadDTO> dtoList = UtilCollection.toList(page.getRecords(), BizRoomReceiptCheckOutHeadDTO.class);

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getIdContextData();

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取抬头DTO
        BizRoomReceiptCheckOutHeadDTO headDto = bizRoomReceiptCheckOutHeadDataWrap.getDtoById(BizRoomReceiptCheckOutHeadDTO.class, headId);

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDto);

        // 设置详情到上下文
        ctx.setVoContextData(new BizResultVO<>(headDto, new ExtendVO().setAttachmentRequired(true), buttonVO));
    }

    /**
     * <AUTHOR>
     *
     * 根据退订人员信息获取需要退订的房间
     * 当房间内的人员都退订后，则自动退订该房间
     *
     * @param ctx
     * @return 需要退订的房间列表
     */
    public List<BizRoomReceiptCheckOutRoomDTO> getCheckOutRoomByCheckOutUser(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckOutHeadDTO po = ctx.getPoContextData();

        List<BizRoomReceiptCheckOutRoomDTO> resultList = new ArrayList<>();

        if(UtilCollection.isEmpty(po.getItemList())){
            return resultList;
        }

        // 要退订的房间使用记录行项目id列表
        List<Long> checkOutRoomUsageItemIdList = po.getItemList().stream().map(BizRoomReceiptCheckOutItemDTO::getRoomUsageItemId).collect(Collectors.toList());

        // 可能退订的房间使用记录抬头id列表
        List<Long> roomUsageHeadIdList = po.getItemList().stream().map(BizRoomReceiptCheckOutItemDTO::getRoomUsageHeadId).distinct().collect(Collectors.toList());

        // 刨除退订的使用明细记录之后，剩余的房间使用记录行项目列表
        List<BizRoomUsageItem> remainingBizRoomUsageItemList = bizRoomUsageItemDataWrap.list(new LambdaQueryWrapper<BizRoomUsageItem>()
                .in(BizRoomUsageItem::getHeadId, roomUsageHeadIdList)
                .eq(BizRoomUsageItem::getCheckOutReqItemId, Const.ZERO)
                .notIn(BizRoomUsageItem::getId, checkOutRoomUsageItemIdList)
        );

        // 还有人住的房间使用记录抬头id列表，这些房间不需要退订
        List<Long> remainingBizRoomUsageHeadIdList = remainingBizRoomUsageItemList.stream().map(BizRoomUsageItem::getHeadId).distinct().collect(Collectors.toList());

        // 需要退订的房间使用记录抬头id列表
        List<Long> checkOutRoomUsageHeadIdList = roomUsageHeadIdList.stream().filter(obj -> !remainingBizRoomUsageHeadIdList.contains(obj)).collect(Collectors.toList());

        if(UtilCollection.isEmpty(checkOutRoomUsageHeadIdList)){
            return resultList;
        }

        // 需要退订的房间使用记录抬头信息列表
        List<BizRoomUsageHead> checkOutRoomUsageHeadList = bizRoomUsageHeadDataWrap.listByIds(checkOutRoomUsageHeadIdList);

        AtomicInteger rid = new AtomicInteger(1);

        for(BizRoomUsageHead bizRoomUsageHead : checkOutRoomUsageHeadList){
            BizRoomReceiptCheckOutRoomDTO bizRoomReceiptCheckOutRoomDTO = new BizRoomReceiptCheckOutRoomDTO();
            resultList.add(bizRoomReceiptCheckOutRoomDTO);

            bizRoomReceiptCheckOutRoomDTO.setRid(Integer.toString(rid.getAndIncrement()));
            bizRoomReceiptCheckOutRoomDTO.setRoomUsageHeadId(bizRoomUsageHead.getId());
            bizRoomReceiptCheckOutRoomDTO.setRoomId(bizRoomUsageHead.getRoomId());
            bizRoomReceiptCheckOutRoomDTO.setRoomCode(bizRoomUsageHead.getRoomCode());
            bizRoomReceiptCheckOutRoomDTO.setStartUsageTime(bizRoomUsageHead.getStartUsageTime());
        }

        // 填充床位数量
        UtilDataFill.fillAttrWField(resultList, BizRoomReceiptCheckOutRoomDTO::getRoomId);

        return resultList;
    }

    /**
     * 保存校验
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckOutHeadDTO po = ctx.getPoContextData();
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

        // 退订时间
        if(po.getEndUsageTime() == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 只记录日期，清空时间
        po.setEndUsageTime(UtilDate.getStartOfDay(po.getEndUsageTime()));

        // 如果前端没有把退订房间推送过来，后端再尝试查询一次是否有房间需要退订
        po.setRoomList(UtilCollection.isEmpty(po.getRoomList()) ? this.getCheckOutRoomByCheckOutUser(ctx) : po.getRoomList());
    }

    /**
     * 提交校验
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckOutHeadDTO po = ctx.getPoContextData();

        if(UtilNumber.isNotEmpty(po.getId())) {
            // 从数据库查询最新的状态
            po.setReceiptStatus(bizRoomReceiptCheckOutHeadDataWrap.getById(po.getId()).getReceiptStatus());
        }

        // 待交接状态的提交
        if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDOVER.getValue().equals(po.getReceiptStatus())){

            // 需要上传交接单
            if(UtilCollection.isEmpty(po.getFileList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }

            // 需要填写交接时间
            po.getRoomList().stream().forEach(obj -> {
                if(obj.getCheckOutHandoverTime() == null){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
                // 只记录日期，清空时间
                obj.setCheckOutHandoverTime(UtilDate.getStartOfDay(obj.getCheckOutHandoverTime()));
            });
        }
    }

    /**
     * 保存单据
     */
    public void saveReceipt(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckOutHeadDTO headDto = ctx.getPoContextData();

        // 单据类型
        headDto.setReceiptType(EnumReceiptType.ROOM_CHECK_OUT_REP.getValue());

        // 单据状态
        headDto.setReceiptStatus(UtilNumber.isEmpty(headDto.getReceiptStatus()) || EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(headDto.getReceiptStatus()) ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue() : headDto.getReceiptStatus());

        // id为空则是新增数据，id有值则是修改数据
        if(UtilNumber.isEmpty(headDto.getId())){
            headDto.setId(null);
            headDto.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.ROOM_CHECK_OUT.getValue()));
        }

        // 保存抬头信息
        bizRoomReceiptCheckOutHeadDataWrap.saveOrUpdateDto(headDto);

        // 保存单据附件
        receiptAttachmentService.saveBizReceiptAttachment(headDto.getFileList(), headDto.getId(), headDto.getReceiptType(), ctx.getCurrentUser().getId());

        AtomicInteger rid = new AtomicInteger(1);

        for (BizRoomReceiptCheckOutItemDTO itemDto : headDto.getItemList()) {
            itemDto.setId(UtilNumber.isEmpty(itemDto.getId()) ? UtilSequence.nextId() : itemDto.getId());
            itemDto.setHeadId(headDto.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(headDto.getReceiptStatus());
        }

        // 删除原有数据库中的行项目数据，删除后重新保存
        bizRoomReceiptCheckOutItemDataWrap.physicalDelete(new LambdaQueryWrapper<BizRoomReceiptCheckOutItem>()
                .eq(BizRoomReceiptCheckOutItem::getHeadId, headDto.getId())
        );

        // 保存行项目列表
        bizRoomReceiptCheckOutItemDataWrap.saveBatchDto(headDto.getItemList());

        if(UtilCollection.isEmpty(headDto.getRoomList())){
            return;
        }

        rid = new AtomicInteger(1);

        for (BizRoomReceiptCheckOutRoomDTO roomDto : headDto.getRoomList()) {
            roomDto.setId(UtilNumber.isEmpty(roomDto.getId()) ? UtilSequence.nextId() : roomDto.getId());
            roomDto.setHeadId(headDto.getId());
            roomDto.setRid(Integer.toString(rid.getAndIncrement()));
        }

        // 删除原有数据库中的房间数据，删除后重新保存
        bizRoomReceiptCheckOutRoomDataWrap.physicalDelete(new LambdaQueryWrapper<BizRoomReceiptCheckOutRoom>()
                .eq(BizRoomReceiptCheckOutRoom::getHeadId, headDto.getId())
        );

        // 保存房间列表
        bizRoomReceiptCheckOutRoomDataWrap.saveBatchDto(headDto.getRoomList());
   }

    /**
     * <AUTHOR>
     *
     * 修改单据状态
     *
     * @param receiptHeadId 单据抬头id
     */
    public void updateStatus(Long receiptHeadId, EnumReceiptStatus receiptStatus) {

        // 修改单据抬头状态
        bizRoomReceiptCheckOutHeadDataWrap.update(new LambdaUpdateWrapper<BizRoomReceiptCheckOutHead>()
                .eq(BizRoomReceiptCheckOutHead::getId, receiptHeadId)
                .set(BizRoomReceiptCheckOutHead::getReceiptStatus, receiptStatus.getValue())
                .set(BizRoomReceiptCheckOutHead::getModifyUserId, UtilCurrentContext.getCurrentUser().getId())
                .set(BizRoomReceiptCheckOutHead::getModifyTime, new Date())
        );

        // 修改单据行项目状态
        bizRoomReceiptCheckOutItemDataWrap.update(new LambdaUpdateWrapper<BizRoomReceiptCheckOutItem>()
                .eq(BizRoomReceiptCheckOutItem::getHeadId, receiptHeadId)
                .set(BizRoomReceiptCheckOutItem::getItemStatus, receiptStatus.getValue())
                .set(BizRoomReceiptCheckOutItem::getModifyUserId, UtilCurrentContext.getCurrentUser().getId())
                .set(BizRoomReceiptCheckOutItem::getModifyTime, new Date())
        );
    }

    /**
     * <AUTHOR>
     *
     * 更新房间的使用情况
     * 把退订相关信息更新到房间使用情况中
     *
     * @param headDTO
     */
    public void updateRoomUsage(BizRoomReceiptCheckOutHeadDTO headDTO) {
        // 要修改的房间使用信息明细列表
        List<BizRoomUsageItem> bizRoomUsageItemList = new ArrayList<>();

        for(BizRoomReceiptCheckOutItemDTO bizRoomReceiptCheckOutItemDTO : headDTO.getItemList()){
            BizRoomUsageItem bizRoomUsageItem = new BizRoomUsageItem();
            bizRoomUsageItemList.add(bizRoomUsageItem);

            bizRoomUsageItem.setId(bizRoomReceiptCheckOutItemDTO.getRoomUsageItemId());
            bizRoomUsageItem.setCheckOutReqItemId(bizRoomReceiptCheckOutItemDTO.getId());
            bizRoomUsageItem.setCheckOutTime(headDTO.getEndUsageTime());
        }

        // 保存房间使用信息明细
        bizRoomUsageItemDataWrap.updateBatchById(bizRoomUsageItemList);

        // 退订人员的房间使用记录抬头id列表
        List<Long> roomUsageHeadIdList = headDTO.getItemList().stream().map(BizRoomReceiptCheckOutItemDTO::getRoomUsageHeadId).distinct().collect(Collectors.toList());

        // 剩余的房间使用信息明细列表（未退订的人员列表）
        List<BizRoomUsageItem> remainingBizRoomUsageItemList = bizRoomUsageItemDataWrap.list(new LambdaQueryWrapper<BizRoomUsageItem>()
                .in(BizRoomUsageItem::getHeadId, roomUsageHeadIdList)
                .eq(BizRoomUsageItem::getCheckOutReqItemId, Const.ZERO)

        );

        // 按照房间使用记录抬头id分组后的map key:房间使用记录抬头id，value:房间使用记录明细列表
        Map<Long, List<BizRoomUsageItem>> remainingBizRoomUsageHeadMap = remainingBizRoomUsageItemList.stream().collect(Collectors.groupingBy(BizRoomUsageItem::getHeadId));

        // 要修改的房间使用信息抬头列表
        List<BizRoomUsageHead> bizRoomUsageHeadList = new ArrayList<>();

        for(Long roomUsageHeadId : roomUsageHeadIdList){
            BizRoomUsageHead bizRoomUsageHead = new BizRoomUsageHead();
            bizRoomUsageHeadList.add(bizRoomUsageHead);

            bizRoomUsageHead.setId(roomUsageHeadId);
            bizRoomUsageHead.setCheckInCount(remainingBizRoomUsageHeadMap.containsKey(roomUsageHeadId) ? remainingBizRoomUsageHeadMap.get(roomUsageHeadId).size() : Const.ZERO);

            if(Const.ZERO.equals(bizRoomUsageHead.getCheckInCount())){
                bizRoomUsageHead.setCheckOutReqHeadId(headDTO.getId());
                /*** del by dingchuan 20250526 房间使用结束时间改为使用退订单的交接时间，暂时注释掉 start ***/
//                bizRoomUsageHead.setEndUsageTime(headDTO.getEndUsageTime());
                /*** del by dingchuan 20250526 房间使用结束时间改为使用退订单的交接时间，暂时注释掉 end *****/
            }
        }

        // 更新房间使用信息明细
        bizRoomUsageHeadDataWrap.updateBatchById(bizRoomUsageHeadList);
    }

    /**
     * <AUTHOR>
     *
     * 房间退订
     *
     * @param ctx
     */
    public void checkOutRoom(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckOutHeadDTO po = ctx.getPoContextData();

        if(UtilCollection.isEmpty(po.getRoomList())){
            return;
        }

        bizRoomDataWrap.update(new LambdaUpdateWrapper<BizRoom>()
                .in(BizRoom::getId, po.getRoomList().stream().map(BizRoomReceiptCheckOutRoomDTO::getRoomId).collect(Collectors.toSet()))
                .set(BizRoom::getCurrentRoomUsageHeadId, Const.ZERO)
        );
    }

    /**
     * <AUTHOR>
     *
     * 更新退房交接时间
     *
     * @param ctx
     */
    public void updateCheckOutHandoverTime(BizContext ctx) {
        // 入参上下文
        BizRoomReceiptCheckOutHeadDTO po = ctx.getPoContextData();


        // 要修改的房间使用信息抬头列表
        List<BizRoomUsageHead> bizRoomUsageHeadList = new ArrayList<>();

        for(BizRoomReceiptCheckOutRoomDTO bizRoomReceiptCheckOutRoomDTO : po.getRoomList()){
            BizRoomUsageHead bizRoomUsageHead = new BizRoomUsageHead();
            bizRoomUsageHeadList.add(bizRoomUsageHead);

            bizRoomUsageHead.setId(bizRoomReceiptCheckOutRoomDTO.getRoomUsageHeadId());
            bizRoomUsageHead.setCheckOutHandoverTime(bizRoomReceiptCheckOutRoomDTO.getCheckOutHandoverTime());
            // 房间的退房时间，以交接时间为准
            bizRoomUsageHead.setEndUsageTime(bizRoomReceiptCheckOutRoomDTO.getCheckOutHandoverTime());
        }

        // 更新房间使用信息明细
        bizRoomUsageHeadDataWrap.updateBatchById(bizRoomUsageHeadList);
    }

    /**
     * 删除单据
     */
    public void deleteReceipt(BizContext ctx) {
        Long receiptHeadId = ctx.getIdContextData();

        // 获取单据信息
        BizRoomReceiptCheckOutHead bizRoomReceiptCheckOutHead = bizRoomReceiptCheckOutHeadDataWrap.getById(receiptHeadId);

        // 只有草稿状态的单据才可删除
        if(!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(bizRoomReceiptCheckOutHead.getReceiptStatus())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_CAN_NOT_DELETE);
        }

        // 删除附件
        receiptAttachmentService.deleteBizReceiptAttachment(receiptHeadId, bizRoomReceiptCheckOutHead.getReceiptType());

        // 删除行项目
        bizRoomReceiptCheckOutItemDataWrap.remove(new LambdaQueryWrapper<BizRoomReceiptCheckOutItem>().eq(BizRoomReceiptCheckOutItem::getHeadId, receiptHeadId));

        // 删除房间
        bizRoomReceiptCheckOutRoomDataWrap.remove(new LambdaQueryWrapper<BizRoomReceiptCheckOutRoom>().eq(BizRoomReceiptCheckOutRoom::getHeadId, receiptHeadId));

        // 删除抬头
        bizRoomReceiptCheckOutHeadDataWrap.removeById(receiptHeadId);

    }

    /**
     * <AUTHOR>
     *
     * 自动创建住房退订单
     *
     * 查询当天到期的其他合同，根据合同查找该合同的房间使用记录，自动创建住房退订单
     *
     */
    public void autoCreateReceipt() {

        // 查询当天到期的合同
        List<BizReceiptContractHead> contractList = bizReceiptContractHeadDataWrap.list(new LambdaQueryWrapper<BizReceiptContractHead>()
                // 只查甲方是华信的数据
                .eq(BizReceiptContractHead::getFirstParty, EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode())
                .eq(BizReceiptContractHead::getValidityEndDate, UtilDate.getStartOfDay(new Date()))
        );

        if(UtilCollection.isEmpty(contractList)){
            return;
        }

        // 逐个合同创建住房退订单
        contractList.stream().forEach(obj -> this.createReceiptByContract(obj));
    }

    /**
     * <AUTHOR>
     *
     * 根据合同创建住房退订单
     *
     * @param bizReceiptContractHead
     */
    private void createReceiptByContract(BizReceiptContractHead bizReceiptContractHead){
        // 查询该合同的房间使用记录
        List<BizRoomUsageHead> bizRoomUsageHeadList = bizRoomUsageHeadDataWrap.list(new LambdaQueryWrapper<BizRoomUsageHead>()
                // 只查询未退订的记录
                .eq(BizRoomUsageHead::getCheckOutReqHeadId, Const.ZERO)
                .eq(BizRoomUsageHead::getContractId, bizReceiptContractHead.getId())
        );

        if(UtilCollection.isEmpty(bizRoomUsageHeadList)){
            return;
        }

        // 查询房间使用信息明细
        List<BizRoomUsageItem> bizRoomUsageItemList =bizRoomUsageItemDataWrap.list(new LambdaQueryWrapper<BizRoomUsageItem>()
                // 只查询未退订的记录
                .eq(BizRoomUsageItem::getCheckOutReqItemId, Const.ZEROL)
                .in(BizRoomUsageItem::getHeadId, bizRoomUsageHeadList.stream().map(BizRoomUsageHead::getId).collect(Collectors.toSet()))
        );

        if(UtilCollection.isEmpty(bizRoomUsageItemList)){
            return;
        }

        // 房间使用记录抬头Map key:使用记录抬头id,value:使用记录抬头信息
        Map<Long, BizRoomUsageHead> bizRoomUsageHeadMap  = bizRoomUsageHeadList.stream().collect(Collectors.toMap(BizRoomUsageHead::getId, Function.identity()));

        // 构建住房退订单
        BizRoomReceiptCheckOutHeadDTO bizRoomReceiptCheckOutHeadDTO = new BizRoomReceiptCheckOutHeadDTO().setItemList(new ArrayList<>());

        BizContext ctx = new BizContext();
        ctx.setCurrentUser(UtilBean.newInstance(sysUserDataWrap.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserCode, Const.ADMIN_ROLE_CODE)), CurrentUser.class));
        ctx.setPoContextData(bizRoomReceiptCheckOutHeadDTO);

        // 设置提交信息
        bizRoomReceiptCheckOutHeadDTO.setSubmitUserId(ctx.getCurrentUserId());
        bizRoomReceiptCheckOutHeadDTO.setSubmitTime(new Date());

        // 设置退订日期
        bizRoomReceiptCheckOutHeadDTO.setEndUsageTime(new Date());

        // 遍历房间使用信息明细，构建退订单行仙姑信息
        for(BizRoomUsageItem bizRoomUsageItem : bizRoomUsageItemList){
            BizRoomReceiptCheckOutItemDTO bizRoomReceiptCheckOutItemDTO = new BizRoomReceiptCheckOutItemDTO();
            bizRoomReceiptCheckOutHeadDTO.getItemList().add(bizRoomReceiptCheckOutItemDTO);

            bizRoomReceiptCheckOutItemDTO.setRoomId(bizRoomUsageItem.getRoomId());
            bizRoomReceiptCheckOutItemDTO.setRoomCode(bizRoomUsageHeadMap.get(bizRoomUsageItem.getHeadId()).getRoomCode());
            bizRoomReceiptCheckOutItemDTO.setRoomUsageHeadId(bizRoomUsageItem.getHeadId());
            bizRoomReceiptCheckOutItemDTO.setRoomUsageItemId(bizRoomUsageItem.getId());
        }

        // 保存校验
        this.checkSaveData(ctx);

        // 提交校验
        this.checkSubmitData(ctx);

        // 保存单据
        this.saveReceipt(ctx);

        // 更新房间的使用情况
        this.updateRoomUsage(bizRoomReceiptCheckOutHeadDTO);

        // 更新状态为待交接
        this.updateStatus(bizRoomReceiptCheckOutHeadDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDOVER);
    }

    /**
     * 按钮组
     *
     * @param headDTO 单据抬头信息
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizRoomReceiptCheckOutHeadDTO headDTO) {

        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();

        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(receiptStatus)) {
            // 创建 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDOVER.getValue().equals(receiptStatus)){
            // 待交接 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        return buttonVO;
    }
}