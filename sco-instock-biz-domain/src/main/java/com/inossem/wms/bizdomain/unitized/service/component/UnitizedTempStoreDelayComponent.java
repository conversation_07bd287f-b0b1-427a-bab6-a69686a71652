package com.inossem.wms.bizdomain.unitized.service.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.spec.service.biz.BizSpecFeatureValueService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.component.ApplyCommonComponent;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyPrintDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 成套设备 暂存延期申请组件类
 *
 * <AUTHOR>
 **/
@Service
@Slf4j
public class UnitizedTempStoreDelayComponent {

    @Autowired
    protected BatchInfoService bizBatchInfoService;

    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    protected BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;

    @Autowired
    protected ApplyCommonComponent applyCommonComponent;

    @Autowired
    protected BizReceiptApplyItemDataWrap bizReceiptApplyItemDataWrap;

    @Autowired
    protected BizSpecFeatureValueService bizSpecFeatureValueService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private StockCommonService stockCommonService;

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        // 页面初始化返回空的行项目、扩展项对象、按钮组【保存和提交】
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptApplyHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_DELAY.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 页面初始化数据放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询申请单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptApplySearchPO :"申请单分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptApplyPageVO> page = po.getPageObj(BizReceiptApplyPageVO.class);
        bizReceiptApplyHeadDataWrap.getPageVOList(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 保存单据
     *
     * @param ctx 上下文
     */
    public void saveReceipt(BizContext ctx) {
        boolean isNew = true;
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        String deptName;
        String deptOfficeName;
        // head处理
        if (UtilNumber.isNotEmpty(id)) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(id);
            // 根据id更新
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);

            // item物理删除
            QueryWrapper<BizReceiptApplyItem> queryWrapperItem = new QueryWrapper<>();
            queryWrapperItem.lambda().eq(BizReceiptApplyItem::getHeadId, id);
            bizReceiptApplyItemDataWrap.physicalDelete(queryWrapperItem);

            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue("unitized_temp_delay");
            headDTO.setId(null);
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(createUserId);
            headDTO.setModifyUserId(createUserId);
            headDTO.setReceiptType(headDTO.getReceiptType());
            headDTO.setReceiptStatus(status);
            bizReceiptApplyHeadDataWrap.saveDto(headDTO);
            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }

        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }

        // item处理
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        int rid = 1;
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(status);
            itemDto.setCreateTime(createTime);
            itemDto.setCreateUserId(createUserId);
            itemDto.setModifyUserId(user.getId());
            itemDto.setTempStoreDeptId(headDTO.getDeptId());
            itemDto.setTempStoreDeptOfficeId(headDTO.getDeptOfficeId());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }


    /**
     * 提交校验【同时模式】
     *
     * @param ctx 上下文
     */
    public void checkSubmitSame(BizContext ctx) {
        // 参数基本校验
        this.check(ctx);
    }

    /**
     * 参数基本校验
     *
     * @param ctx 上下文
     */
    public void check(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (Objects.isNull(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 提交单据【同时模式】
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitReceipt(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存
        this.saveReceipt(ctx);
    }

    /**
     * 更新单据状态为已完成
     *
     * @param ctx 上下文
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新单据,行项目状态 如不更新单据状态，headDTO参数传null
     *
     * @param headDTO     headDTO
     * @param itemDTOList 行项目列表
     * @param status      状态
     */
    public void updateStatus(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList,
                             Integer status) {
        if (Objects.isNull(headDTO)) {
            this.updateItemStatus(itemDTOList, status);
        } else if (CollectionUtils.isEmpty(itemDTOList)) {
            this.updateReceiptStatus(headDTO, status);
        } else if (!CollectionUtils.isEmpty(itemDTOList)) {
            this.updateItemStatus(itemDTOList, status);
            this.updateReceiptStatus(headDTO, status);
        }
    }

    /**
     * 更新行项目状态
     *
     * @param itemDTOList 行项目列表
     * @param status      状态
     */
    public void updateItemStatus(List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach(item -> item.setItemStatus(status));
            bizReceiptApplyItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }

    /**
     * 更新单据状态
     *
     * @param headDTO       headDTO
     * @param receiptStatus 单据状态
     */
    public void updateReceiptStatus(BizReceiptApplyHeadDTO headDTO, Integer receiptStatus) {
        if (Objects.nonNull(headDTO)) {
            // 单据状态
            headDTO.setReceiptStatus(receiptStatus);
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 暂存物项申请单详情
     *
     * @param ctx BizContext
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取借用申请单
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHeadDataWrap.getById(headId), BizReceiptApplyHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        headDTO.getItemList().forEach(item -> item.setReceiptType(headDTO.getReceiptType()));
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置借用申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 根据headId查询暂存物项申请单列表
     *
     * @param headId 单据id
     * @return 暂存物项申请单信息
     */
    public BizReceiptApplyHeadDTO getItemListById(Long headId) {
        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptApplyHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 按钮组
     *
     * @param headDTO 入库单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }

        // 完成状态显示打印按钮
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptApplySearchMatPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptApplyItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        String specStock = Const.STRING_EMPTY;
        // 成套设备 暂存物项领用 出库设置specStock为“Y”
        if (EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_DELAY.getValue().equals(po.getReceiptType())) {
            specStock = EnumSpecStock.SPEC_STOCK_UNITIZED_BATCH_STATUS_TEMP_STORE.getValue();
        }
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
        // 配置matId
        pos.setMatId(matId);
        pos.setStockStatus(stockStatus);
        pos.setSpecStock(specStock);
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(), pos);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptApplyItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptApplyItemDTO.class);
                BizBatchInfoDTO batchInfo = assembleDTO.getBatchInfo();
                if (UtilObject.isNotNull(batchInfo)) {
                    itemDTO.setBatchInfoDto(batchInfo);
                    itemDTO.setBatchCode(batchInfo.getBatchCode());
                    itemDTO.setTempStoreUser(batchInfo.getTempStoreUser());
                    itemDTO.setTempStorePreReceiptCode(batchInfo.getTempStorePreReceiptCode());
                    itemDTO.setTempStoreExpireDate(batchInfo.getTempStoreExpireDate());
                }
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
            // 这里可以设置批次图片
        }
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        matStockDTO.setApplyItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * 提交暂存物项申请单
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "要提交的暂存物项申请单"}
     * @out ctx 出参 {"receiptCode" : "暂存物项申请单单号"}
     */
    public void submitScrapApply(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存借用申请单
        applyCommonComponent.saveApply(ctx);
    }

    /**
     * 开启审批
     *
     * @param ctx
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(po);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Integer receiptType = po.getReceiptType();
        Map<String, Object> variables = new HashMap<>();

        // 校验审批人
        this.approveCheck(ctx);

        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        variables.put("ftyId", po.getItemList().get(0).getFtyId());
        // 用户所属部门
        variables.put("userDept", userDept);

        // receiptType 8181
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 更新转性单据状态 - 审批中
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizReceiptApplyHead receiptApplyHead = bizReceiptApplyHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptApplyHeadDTO receiptApplyHeadDTO = UtilBean.newInstance(receiptApplyHead, BizReceiptApplyHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(receiptApplyHeadDTO);
        // 封装上下文
        BizContext ctx = new BizContext();
        ctx.setCurrentUser(wfReceiptCo.getInitiator());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptApplyHeadDTO);
        // 判断是否审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 更新状态已完成
            applyCommonComponent.updateStatusCompleted(ctx);
            // 更新批次暂存物资延期日期
            this.updateBatchInfoTempStoreExpireDate(receiptApplyHeadDTO);
        } else {
            // 单据驳回
            applyCommonComponent.updateStatus(receiptApplyHeadDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        }
    }

    /**
     * 数据校验
     *
     * @param ctx 入参上下文
     */
    public void checkSaveData(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头字段必填
        if (UtilObject.isNull(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 校验行项目
        this.checkEmptyItem(headDTO, ctx);
    }

    /**
     * 判断行项目是否为空
     *
     * @param headDTO 单据抬头
     * @param ctx 入参上下文
     */
    private void checkEmptyItem(BizReceiptApplyHeadDTO headDTO, BizContext ctx) {
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            // 校验当前用户与所添加的物料对应的需求人是否一致
            long count = headDTO.getItemList().stream().map(BizReceiptApplyItemDTO::getTempStoreUser).distinct().count();
            if (count > 1 || !headDTO.getItemList().get(0).getTempStoreUser().equals(ctx.getCurrentUser().getUserName())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 更新批次暂存物资延期日期
     *
     * @param headDTO 入库单
     */
    private void updateBatchInfoTempStoreExpireDate(BizReceiptApplyHeadDTO headDTO) {
        List<BizBatchInfoDTO> batchInfoList = new ArrayList<>();
        for (BizReceiptApplyItemDTO itemDTO : headDTO.getItemList()) {
            batchInfoList.add(new BizBatchInfoDTO()
                    .setId(itemDTO.getBatchId())
                    .setTempStoreExpireDate(itemDTO.getTempStoreDelayExpireDate()));
        }
        bizBatchInfoService.multiUpdateBatchInfo(batchInfoList);
    }

    /**
     * 校验审批人
     *
     * @param ctx
     */
    private void approveCheck(BizContext ctx) {
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }

        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();

        // 一级审批节点 发起人所属部门负责人
        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            String deptCode = deptOfficePO.getDeptCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
            level1UserList.addAll(userList);
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }

        // 二级审批节点 工程部仓储管理人员 固定写死 唐娜 82000240 宋飞 82000247
        level2UserList = Arrays.asList("82000240", "82000247");
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
    }

    /**
     * 得到打印信息
     *
     * @param ctx ctx
     */
    public void getPrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取借用申请单
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHeadDataWrap.getById(headId), BizReceiptApplyHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);

        headDTO.getItemList().forEach(item -> {
            if(null != item.getTempStorePeriod()){
                item.setTempStorePeriodStr(item.getTempStorePeriod() + "（月）");
            }
        });
        
        // 设置借用申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), new ButtonVO()));
    }


    /**
     * 整理批准
     *
     * @param ctx ctx
     */
    public void assemApprove(BizContext ctx) {
        BizResultVO<BizReceiptApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptApplyHeadDTO headDTO = resultVO.getHead();
        BizReceiptApplyPrintDTO headPrintDTO = UtilBean.newInstance(headDTO, BizReceiptApplyPrintDTO.class);
        headPrintDTO.setAutograph(UtilPrint.SIGNATURE);
        headPrintDTO.setAutographLevel1(UtilPrint.SIGNATURE);
        headPrintDTO.setAutographLevel2(UtilPrint.SIGNATURE);
        headPrintDTO.setAutographLevel3(UtilPrint.SIGNATURE);
        if (UtilCollection.isNotEmpty(headDTO.getApproveList())){
            AtomicInteger i = new AtomicInteger();
            //设置审批人和审批时间
            headPrintDTO.getApproveList().forEach(approve -> {
                String endTime;
                if (approve.getEndTime() != null && approve.getEndTime().contains(".")){
                    endTime = approve.getEndTime().substring(0,approve.getEndTime().indexOf("."));
                }else {
                    endTime = approve.getEndTime();
                }
                if (i.intValue() == 0){
                    headPrintDTO.setAssigneeName(approve.getAssigneeName()); //申请人
                    headPrintDTO.setEndTime(endTime);
                    headPrintDTO.setAutograph(approve.getAutographData());
                } else if(i.intValue() == 1){
                    headPrintDTO.setAssigneeNameLevel1(approve.getAssigneeName());//一级审批人
                    headPrintDTO.setEndTimeLevel1(endTime);
                    headPrintDTO.setAutographLevel1(approve.getAutographData());
                } else if(i.intValue() == 2){
                    headPrintDTO.setAssigneeNameLevel2(approve.getAssigneeName());  //二级审批人
                    headPrintDTO.setEndTimeLevel2(endTime);
                    headPrintDTO.setAutographLevel2(approve.getAutographData());
                }else if(i.intValue() == 3){
                    headPrintDTO.setAssigneeNameLevel3(approve.getAssigneeName());  //三级审批人
                    headPrintDTO.setEndTimeLevel3(endTime);
                    headPrintDTO.setAutographLevel3(approve.getAutographData());
                }
                i.getAndIncrement();
            });
        }
        
        if (null!=headPrintDTO.getTempStoreType() && headPrintDTO.getTempStoreType() == 0){
            headPrintDTO.setTempStoreTypeF(EnumIsYesOrNo.YES.getValue());
            headPrintDTO.setTempStoreTypeR(EnumIsYesOrNo.NO.getValue());
        }else if (null!=headPrintDTO.getTempStoreType() && headPrintDTO.getTempStoreType() == 1){
            headPrintDTO.setTempStoreTypeF(EnumIsYesOrNo.NO.getValue());
            headPrintDTO.setTempStoreTypeR(EnumIsYesOrNo.YES.getValue());
        }else{
            headPrintDTO.setTempStoreTypeF(EnumIsYesOrNo.NO.getValue());
            headPrintDTO.setTempStoreTypeR(EnumIsYesOrNo.NO.getValue());
        }

        // 设置借用申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headPrintDTO, new ExtendVO(), new ButtonVO()));
    }
}
