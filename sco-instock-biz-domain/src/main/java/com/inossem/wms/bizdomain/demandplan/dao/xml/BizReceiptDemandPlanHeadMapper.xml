<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.demandplan.dao.BizReceiptDemandPlanHeadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanHead">
        <id column="id" property="id"/>
        <result column="receipt_code" property="receiptCode"/>
        <result column="receipt_type" property="receiptType"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="demand_type" property="demandType"/>
        <result column="demand_user_id" property="demandUserId"/>
        <result column="demand_dept_id" property="demandDeptId"/>
        <result column="urgent_flag" property="urgentFlag"/>
        <result column="budget_type" property="budgetType"/>
        <result column="plan_arrival_date" property="planArrivalDate"/>
        <result column="demand_plan_name" property="demandPlanName"/>
        <result column="purchase_reason" property="purchaseReason"/>
        <result column="suggest_vendor_list" property="suggestVendorList"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="modify_user_id" property="modifyUserId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, receipt_code, receipt_type, receipt_status, demand_type, demand_user_id, demand_dept_id,
        urgent_flag, budget_type, plan_arrival_date, demand_plan_name, purchase_reason, suggest_vendor_list,
        remark, is_delete, create_time, modify_time, create_user_id, modify_user_id
    </sql>

    <!-- 需求计划分页查询 -->
    <select id="getDemandPlanPageVo" resultType="com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanListVo">
        SELECT distinct
            h.id,
            h.receipt_code,
            h.receipt_type,
            h.receipt_status,
            h.demand_type,
            h.demand_plan_type,
            h.urgent_flag,
            h.budget_type,
            h.subject_type,
            h.demand_plan_name,
            h.plan_arrival_date,
            h.create_time,
            du.user_code as demand_user_code,
            du.user_name as demand_user_name,
            hu.user_code as handle_user_code,
            hu.user_name as handle_user_name,
            cu.user_code as create_user_code,
            cu.user_name as create_user_name,
            d.dept_code as demand_dept_code,
            d.dept_name as demand_dept_name,
            act.END_TIME_ as approve_time
        FROM biz_receipt_demand_plan_head h
        LEFT JOIN  sys_user du ON h.demand_user_id = du.id
        LEFT JOIN sys_user hu ON h.handle_user_id = hu.id
        LEFT JOIN sys_user cu ON h.create_user_id = cu.id
        LEFT JOIN dic_dept d ON h.demand_dept_id = d.id
        LEFT JOIN biz_receipt_demand_plan_item i ON h.id = i.head_id
        LEFT JOIN dic_material m ON i.mat_id = m.id
        LEFT JOIN dic_material_group mg ON i.mat_group_id = mg.id
        LEFT JOIN (select ahp.BUSINESS_KEY_, aha.END_TIME_
                   from ACT_HI_PROCINST ahp
                            LEFT JOIN ACT_HI_ACTINST aha ON ahp.ID_ = aha.PROC_INST_ID_ AND aha.ACT_ID_ = 'Level3ApprovalNode' AND aha.ASSIGNEE_ != ''
                   WHERE aha.END_TIME_ is not null
                   ORDER BY aha.END_TIME_ DESC) act ON act.BUSINESS_KEY_ = h.receipt_code AND h.receipt_status = 90
        ${ew.customSqlSegment}
    </select>

    <!-- 需求计划分页查询-报表 -->
    <select id="getDemandPlanReportPageVo" resultType="com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanReportListVo">
        SELECT i.*,
               h.receipt_code,
               h.receipt_type,
               h.receipt_status,
               h.demand_type,
               h.demand_plan_type,
               h.urgent_flag,
               h.budget_type,
               h.subject_type,
               h.demand_plan_name,
               h.plan_arrival_date,
               h.purchase_reason,
               pah.receipt_code         purchase_apply_receipt_code,
               pah.send_type,
               dab.year                 budget_year,
               dbs.budget_subject_name  budget_class,
               dbc.budget_classify_name budget_account,
               pah.purchase_description,
               pah.create_time          purchase_create_time,
               pah.create_user_id       purchase_create_user_id,
               pai.rid                  purchase_apply_rid,
               du.user_code as          demand_user_code,
               du.user_name as          demand_user_name,
               cu.user_code as          create_user_code,
               cu.user_name as          create_user_name,
               hu.user_name as          handle_user_name,
               d.dept_code  as          demand_dept_code,
               d.dept_name  as          demand_dept_name
        FROM biz_receipt_demand_plan_head h
                 INNER JOIN biz_receipt_demand_plan_item i ON i.head_id = h.id
                 LEFT JOIN biz_receipt_purchase_apply_item pai ON pai.pre_receipt_item_id = i.id
                 LEFT JOIN biz_receipt_purchase_apply_head pah ON pah.id = pai.head_id
                 LEFT JOIN dic_annual_budget dab ON dab.id = pah.annual_budget_id
                 LEFT JOIN dic_budget_subject dbs ON dbs.id = dab.budget_subject_id
                 LEFT JOIN dic_budget_classify dbc ON dbc.id = dab.budget_classify_id
                 LEFT JOIN sys_user du ON h.demand_user_id = du.id
                 LEFT JOIN sys_user cu ON h.create_user_id = cu.id
                 LEFT JOIN sys_user hu ON h.handle_user_id = hu.id
                 LEFT JOIN dic_dept d ON h.demand_dept_id = d.id
                 LEFT JOIN dic_material m ON i.mat_id = m.id
                 LEFT JOIN dic_material_group mg ON i.mat_group_id = mg.id
        ${ew.customSqlSegment}
    </select>

    <insert id="insertByMat">
    INSERT INTO sys_job_demand_mat_qty (id)
    SELECT m.id FROM dic_material m
    WHERE NOT EXISTS (SELECT 1 FROM sys_job_demand_mat_qty d WHERE d.id = m.id)
    </insert>

    <update id="updateQty">
UPDATE sys_job_demand_mat_qty d,
 (
	SELECT m.id,
		IFNULL(stock_batch.qty, 0) stock_batch_qty,
		IFNULL(demand_plan.qty - IFNULL(receipt_input.qty, 0) + IFNULL(receipt_output.qty, 0), 0) transfer_qty
	FROM dic_material m
	LEFT JOIN (
        <!-- 批次库存数量 -->
		SELECT sb.mat_id, sum(sb.qty) qty
		FROM stock_batch sb WHERE sb.fty_id = 1 GROUP BY sb.mat_id
	) stock_batch ON m.id = stock_batch.mat_id
	LEFT JOIN (
        <!-- 需求计划, 已完成 -->
		SELECT i.mat_id, sum(i.demand_qty) qty
		FROM biz_receipt_demand_plan_item i, biz_receipt_demand_plan_head h
		WHERE i.head_id = h.id AND h.receipt_status = 90 AND i.fty_id = 1
		GROUP BY i.mat_id
	) demand_plan ON m.id = demand_plan.mat_id
	LEFT JOIN (
        <!-- 验收入库, 不含冲销 -->
		SELECT i.mat_id, sum(i.qty) qty
		FROM biz_receipt_input_item i, biz_receipt_input_head h
		WHERE i.head_id = h.id AND h.receipt_type = 214 AND i.item_status = 90 AND i.fty_id = 1
		GROUP BY i.mat_id
	) receipt_input ON m.id = receipt_input.mat_id
	LEFT JOIN (
        <!-- 采购退货, 不含冲销 -->
		SELECT i.mat_id, sum(i.qty) qty
		FROM biz_receipt_output_item i, biz_receipt_output_head h
		WHERE i.head_id = h.id AND h.receipt_type = 413 AND i.item_status = 90 AND i.fty_id = 1
		GROUP BY i.mat_id
	) receipt_output ON m.id = receipt_output.mat_id
) t
SET d.stock_batch_qty = t.stock_batch_qty, d.transfer_qty = t.transfer_qty
WHERE t.id = d.id
    </update>

    <update id="updateQtyByYear">
UPDATE sys_job_demand_mat_qty d,
 (
	SELECT m.id,
		IFNULL(purchase_input.qty + IFNULL(purchase_output.qty, 0), 0) purchase_qty,
		IFNULL(consume_output.qty - IFNULL(consume_return.qty, 0), 0) consume_qty
	FROM dic_material m
	LEFT JOIN (
        <!-- 验收入库, 不含冲销 -->
		SELECT i.mat_id, sum(i.qty) qty
		FROM biz_receipt_input_item i, biz_receipt_input_head h
		WHERE i.head_id = h.id AND h.receipt_type = 214 AND i.item_status = 90 AND i.fty_id = 1 AND YEAR (i.doc_date) = YEAR (CURDATE()) - 1
		GROUP BY i.mat_id
	) purchase_input ON m.id = purchase_input.mat_id
	LEFT JOIN (
        <!-- 采购退货, 不含冲销 -->
		SELECT i.mat_id, sum(i.qty) qty
		FROM biz_receipt_output_item i, biz_receipt_output_head h
		WHERE i.head_id = h.id AND h.receipt_type = 413 AND i.item_status = 90 AND i.fty_id = 1 AND YEAR (i.doc_date) = YEAR (CURDATE()) - 1
		GROUP BY i.mat_id
	) purchase_output ON m.id = purchase_output.mat_id
	LEFT JOIN (
        <!-- 领料出库, 不含冲销 -->
        SELECT i.mat_id, sum(i.qty) qty
        FROM biz_receipt_output_item i, biz_receipt_output_head h
        WHERE i.head_id = h.id AND h.receipt_type = 414 AND i.item_status = 90 AND i.fty_id = 1 AND YEAR (i.doc_date) = YEAR (CURDATE()) - 1
        GROUP BY i.mat_id
	) consume_output ON m.id = consume_output.mat_id
	LEFT JOIN (
        <!-- 领料退库, 不含冲销 -->
        SELECT i.mat_id, sum(i.qty) qty
        FROM biz_receipt_return_item i, biz_receipt_return_head h
        WHERE i.head_id = h.id AND h.receipt_type = 312 AND i.item_status = 90 AND i.fty_id = 1 AND YEAR (i.doc_date) = YEAR (CURDATE()) - 1
        GROUP BY i.mat_id
	) consume_return ON m.id = consume_return.mat_id
) t
SET d.purchase_qty = t.purchase_qty, d.consume_qty = t.consume_qty
WHERE t.id = d.id
    </update>

</mapper> 
