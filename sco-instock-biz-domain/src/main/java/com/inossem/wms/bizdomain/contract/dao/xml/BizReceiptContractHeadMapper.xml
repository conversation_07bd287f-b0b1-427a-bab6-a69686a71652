<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.contract.dao.BizReceiptContractHeadMapper">

    <!-- 列表查询SQL -->
    <select id="getContractPageVo" resultType="com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractListVO">
        SELECT 
            h.id,
            h.receipt_code,
            h.receipt_type,
            h.receipt_status,
            h.contract_name,
            h.purchase_type,
            h.contract_sub_type,
            h.first_party,
            h.supplier_id,
            s.supplier_code,
            s.supplier_name,
            h.purchase_location,
            h.contract_sign_date,
            h.delivery_date,
            h.delivery_address,
            h.validity_start_date,
            h.validity_end_date,
            h.create_time,
            cu.user_code as create_user_code,
            cu.user_name as create_user_name
        FROM biz_receipt_contract_head h
        LEFT JOIN dic_supplier s ON h.supplier_id = s.id AND s.is_delete = 0
        LEFT JOIN sys_user cu ON h.create_user_id = cu.id AND cu.is_delete = 0
        ${ew.customSqlSegment}
    </select>

    <!-- 报表列表查询SQL -->
    <select id="getContractReportPageVo" resultType="com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractReportListVO">
        SELECT
            h.id,
            h.receipt_code,
            h.receipt_type,
            h.receipt_status,
            h.contract_name,
            h.purchase_type,
            h.contract_sub_type,
            h.first_party,
            h.supplier_id,
            s.supplier_code,
            s.supplier_name,
            h.purchase_location,
            h.contract_sign_date,
            h.delivery_date,
            h.delivery_address,
            h.validity_start_date,
            h.validity_end_date,
            h.create_time,
            h.warranty_period,
            h.cost_source_party,
            h.other_fee_name,
            h.other_amount,
            h.final_discount_amount,
            cu.user_code as create_user_code,
            cu.user_name as create_user_name,
            i.*
        FROM biz_receipt_contract_head h
        JOIN biz_receipt_contract_item i ON i.head_id = h.id AND h.is_delete = 0 AND i.is_delete = 0
        LEFT JOIN dic_material m ON i.mat_id = m.id
        LEFT JOIN sys_user du ON i.demand_person_id = du.id AND du.is_delete = 0
        LEFT JOIN dic_dept dd ON i.demand_dept_id = dd.id AND dd.is_delete = 0
        LEFT JOIN dic_supplier s ON h.supplier_id = s.id AND s.is_delete = 0
        LEFT JOIN sys_user cu ON h.create_user_id = cu.id AND cu.is_delete = 0
        ${ew.customSqlSegment}
    </select>

    <!-- 报表列表查询SQL -->
    <select id="getContractLedgerReportPageVo" resultType="com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractLedgerListExportVO">
        SELECT t.*, sum(t.price * t.qty) no_tax_amount from(
        SELECT
            h.receipt_code,
            h.contract_name,
            h.purchase_type,
            h.contract_sub_type,
            h.first_party,
            s.supplier_name,
            s.supplier_code,
            h.contract_sign_date,
            i.currency,
            h.remark,
            h.receipt_type,
            h.receipt_status,
            cu.user_name AS create_user_name,
            h.create_time,
			IFNULL(dni.purchase_code, i.purchase_receipt_code) purchase_receipt_code,
            dnh.receipt_code delivery_receipt_code,
            IF (dni.po_no_tax_price != 0, dni.po_no_tax_price, dni.no_tax_price) price,
            dni.qty
        FROM
            biz_receipt_contract_head h
         JOIN dic_supplier s ON h.supplier_id = s.id AND s.is_delete = 0
         JOIN sys_user cu ON h.create_user_id = cu.id AND cu.is_delete = 0
        left JOIN biz_receipt_delivery_notice_head dnh ON dnh.contract_id = h.id AND dnh.is_delete = 0
        left JOIN biz_receipt_delivery_notice_item dni ON dni.head_id = dnh.id AND dni.is_delete = 0
        left JOIN biz_receipt_contract_item i ON i.head_id = h.id AND h.is_delete = 0 AND i.is_delete = 0
        ${ew.customSqlSegment}
        GROUP BY i.id, dni.id
UNION ALL
        SELECT
            h.receipt_code,
            h.contract_name,
            h.purchase_type,
            h.contract_sub_type,
            h.first_party,
            s.supplier_name,
            s.supplier_code,
            h.contract_sign_date,
            i.currency,
            h.remark,
            h.receipt_type,
            h.receipt_status,
            cu.user_name AS create_user_name,
            h.create_time,
            IFNULL(li.purchase_code, i.purchase_receipt_code) purchase_receipt_code,
            lh.receipt_code delivery_receipt_code,
            IF (li.po_no_tax_price != 0, li.po_no_tax_price, li.no_tax_price) price,
            li.qty
        FROM
            biz_receipt_contract_head h
         JOIN dic_supplier s ON h.supplier_id = s.id AND s.is_delete = 0
         JOIN sys_user cu ON h.create_user_id = cu.id AND cu.is_delete = 0
        left JOIN biz_receipt_logistics_head lh ON lh.contract_id = h.id AND lh.is_delete = 0
        left JOIN biz_receipt_logistics_item li ON li.head_id = lh.id AND li.is_delete = 0
        left JOIN biz_receipt_contract_item i ON i.head_id = h.id AND h.is_delete = 0 AND i.is_delete = 0
        ${ew.customSqlSegment}
        GROUP BY i.id, li.id
        ) t
        GROUP BY t.receipt_code, t.delivery_receipt_code
        ORDER BY t.create_time DESC
    </select>

    <!-- 订单信息查询SQL -->
    <select id="getPurchaseList" resultType="com.inossem.wms.common.model.bizdomain.contract.dto.PurchaseDTO">
SELECT * from(
SELECT DISTINCT
	i.purchase_code,
	h.receipt_code,
    sum(IF (i.po_no_tax_price != 0, i.po_no_tax_price, i.no_tax_price) * i.qty) po_no_tax_amount,
    h.modify_user_id create_user_id,
    h.modify_time create_time
FROM
	biz_receipt_delivery_notice_head h,
	biz_receipt_delivery_notice_item i
WHERE
	h.id = i.head_id
<if test="po.id != null">
    AND h.contract_id = #{po.id}
</if>
GROUP BY h.receipt_code
UNION ALL
SELECT DISTINCT
	i.purchase_code,
	h.receipt_code,
	'' po_no_tax_amount,
	h.modify_user_id,
	h.modify_time
FROM
	biz_receipt_logistics_head h,
	biz_receipt_logistics_item i
WHERE
	h.id = i.head_id
<if test="po.id != null">
    AND h.contract_id = #{po.id}
</if>
        ) t order by t.create_time desc
    </select>

</mapper>
