<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.purchase.dao.BizReceiptPurchaseApplyHeadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplyHead">
        <id column="id" property="id"/>
        <result column="receipt_code" property="receiptCode"/>
        <result column="receipt_type" property="receiptType"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_dept_id" property="applyDeptId"/>
        <result column="plan_arrival_date" property="planArrivalDate"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="modify_user_id" property="modifyUserId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, receipt_code, receipt_type, receipt_status, apply_user_id, apply_dept_id,
        plan_arrival_date, remark, is_delete, create_time, modify_time, create_user_id, modify_user_id
    </sql>

    <!-- 采购申请分页查询 -->
    <select id="getPurchaseApplyPageVo" resultType="com.inossem.wms.common.model.bizdomain.purchase.vo.BizReceiptPurchaseApplyListVO">
        SELECT distinct
            h.id,
            h.receipt_code,
            h.receipt_type,
            h.receipt_status,
            h.purchase_description,
            h.purchase_type,
            h.send_type,
            h.create_time,
            h.create_user_id,
            cu.user_code as create_user_code,
            cu.user_name as create_user_name
        FROM biz_receipt_purchase_apply_head h
        LEFT JOIN sys_user cu ON h.create_user_id = cu.id
        ${ew.customSqlSegment}
    </select>

</mapper> 