package com.inossem.wms.bizdomain.output.controller;

import com.inossem.wms.bizdomain.output.service.biz.ToolScrapOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptToolScrapSearchPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工器具报废出库Controller
 * <AUTHOR>
 * @date 2022/04/08 10:44
 **/
@RestController
@Api(tags = "工器具管理-工器具报废出库")
public class ToolScrapOutputController {

    @Autowired
    private ToolScrapOutputService toolScrapOutputService;

    @ApiOperation(value = "工器具报废出库单查询列表（分页）", tags = {"工器具管理-工器具报废出库"})
    @PostMapping(value = "/outputs/tool-scrap-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptToolScrapSearchPO po, BizContext ctx) {
        toolScrapOutputService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "工器具报废出库详情", tags = {"工器具管理-工器具报废出库"})
    @GetMapping(value = "/outputs/tool-scrap-output/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        toolScrapOutputService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "工器具报废出库提交", tags = {"工器具管理-工器具报废出库"})
    @PostMapping(value = "/outputs/tool-scrap-output/submit")
    public BaseResult<?> submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        toolScrapOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.TOOL_SCRAP_OUTPUT_CODE_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "工器具报废出库保存", tags = {"工器具管理-工器具报废出库"})
    @PostMapping(value = "/outputs/tool-scrap-output/save")
    public BaseResult<?> save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        toolScrapOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.TOOL_SCRAP_OUTPUT_CODE_SAVE_SUCCESS, code);
    }

}
