package com.inossem.wms.bizdomain.apply.controller;

import com.inossem.wms.bizdomain.apply.service.biz.BorrowApplyService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.apply.BorrowTypeMapVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 借用申请 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@RestController
public class BorrowApplyController {

    @Autowired
    protected BorrowApplyService borrowApplyService;

    /**
     * 查询借用类型下拉
     *
     * @return 借用类型下拉框
     */
    @ApiOperation(value = "查询借用类型下拉", tags = {"工器具管理-借用申请"})
    @GetMapping(path = "/apply/borrow-apply/borrow-type-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BorrowTypeMapVO>> getBorrowTypeDown(BizContext ctx) {
        borrowApplyService.getBorrowTypeDown(ctx);
        MultiResultVO<BorrowTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 借用申请-初始化
     *
     * @return 借用申请单
     */
    @ApiOperation(value = "借用申请-初始化", tags = {"工器具管理-借用申请"})
    @GetMapping(value = "/apply/borrow-apply/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> init(BizContext ctx) {
        borrowApplyService.init(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询申请单列表-分页
     *
     * @param po 申请单分页查询入参
     * @return 申请单列表
     */
    @ApiOperation(value = "查询申请单列表-分页", tags = {"工器具管理-借用申请"})
    @PostMapping(value = "/apply/borrow-apply/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptApplyPageVO>> getPage(@RequestBody BizReceiptApplySearchPO po, BizContext ctx) {
        borrowApplyService.getPage(ctx);
        PageObjectVO<BizReceiptApplyPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询借用申请单详情
     *
     * @param id 借用申请单抬头表主键
     * @return 借用申请单详情
     */
    @ApiOperation(value = "查询借用申请单详情", tags = {"工器具管理-借用申请"})
    @GetMapping(value = "/apply/borrow-apply/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        borrowApplyService.getInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 借用申请-保存
     *
     * @param po 保存借用申请表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "借用申请-保存", tags = {"工器具管理-借用申请"})
    @PostMapping(value = "/apply/borrow-apply/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        borrowApplyService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 借用申请-提交
     *
     * @param po 提交借用申请表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "借用申请-提交", tags = {"工器具管理-借用申请"})
    @PostMapping(value = "/apply/borrow-apply/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        borrowApplyService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 借用申请-删除
     *
     * @param id 借用申请单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "借用申请-删除", tags = {"工器具管理-借用申请"})
    @DeleteMapping(value = "/apply/borrow-apply/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        borrowApplyService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_DELETE_SUCCESS, receiptCode);
    }

    /**
     * 查询物料库存
     *
     * @param po 申请单查询物料库存入参
     * @return 物料库存信息
     */
    @ApiOperation(value = "查询物料库存", tags = {"工器具管理-借用申请"})
    @PostMapping(value = "/apply/borrow-apply/mat-stock-list")
    public BaseResult<SingleResultVO<MatStockDTO>> getMatStock(@RequestBody BizReceiptApplySearchMatPO po, BizContext ctx) {
        borrowApplyService.getMatStock(ctx);
        SingleResultVO<MatStockDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}

