package com.inossem.wms.bizdomain.budget.controller;

import com.inossem.wms.bizdomain.budget.service.biz.BudgetSubjectService;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.budget.dto.DicBudgetSubjectDTO;
import com.inossem.wms.common.model.masterdata.budget.po.DicBudgetSubjectSearchPO;
import com.inossem.wms.common.model.masterdata.budget.vo.DicBudgetSubjectPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@RestController
@Slf4j
@Api(tags = "预算科目管理")
public class BudgetSubjectController {

    @Autowired
    protected BudgetSubjectService budgetSubjectService;


    @ApiOperation(value = "预算科目列表分页查询", tags = {"主数据管理-预算科目"})
    @PostMapping(path = "/masterdata/budget-subject/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicBudgetSubjectPageVO>> getPage(@RequestBody DicBudgetSubjectSearchPO po, BizContext ctx) {
        return BaseResult.success(budgetSubjectService.getPage(ctx));
    }


    @ApiOperation(value = "查询预算科目详情信息", tags = {"主数据管理-预算科目"})
    @GetMapping(path = "/masterdata/budget-subject/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicBudgetSubjectDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(budgetSubjectService.getInfo(ctx));
    }

    @ApiOperation(value = "保存更新预算科目", tags = {"主数据管理-预算科目"})
    @PostMapping(path = "/masterdata/budget-subject/addOrUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody DicBudgetSubjectDTO po, BizContext ctx) {
        budgetSubjectService.addOrUpdate(ctx);
        return BaseResult.success();
    }


    @ApiOperation(value = "删除预算科目", tags = {"主数据管理-预算科目"})
    @DeleteMapping(path = "/masterdata/budget-subject/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        budgetSubjectService.remove(ctx);
        return BaseResult.success();
    }
}
