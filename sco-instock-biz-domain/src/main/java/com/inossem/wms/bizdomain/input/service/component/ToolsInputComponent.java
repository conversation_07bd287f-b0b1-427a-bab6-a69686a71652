package com.inossem.wms.bizdomain.input.service.component;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
//import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.dao.BizBatchInfoMapper;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockBatchDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockBinDataWrap;
import com.inossem.wms.bizdomain.input.service.component.inputbase.InputComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.ToolsInputMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.ToolsInputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputBinDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.tool.service.ToolCommonService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumFreezeType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.enums.EnumToolDefaultStorageType;
import com.inossem.wms.common.enums.apply.EnumToolStatus;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputBinDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputBin;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.po.ToolsInputImportPO;
import com.inossem.wms.common.model.bizdomain.input.po.ToolsMaterialInfoSearchPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.input.vo.ToolsMaterialInfoVO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectUpdatePO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialListConfirmSplitPO;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.DicMaterialListVO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelBatch;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.tool.entity.DicToolType;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> wang
 * @description 工器具入库组件库
 * @date 2022/3/29 18:49
 */
@Service
@Slf4j
public class ToolsInputComponent {

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;
    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;
    @Autowired
    private DicMaterialDataWrap dicMaterialDataWrap;
    @Autowired
    private BizReceiptInputBinDataWrap binDataWrap;
    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private InputComponent inputComponent;
    @Autowired
    private BizBatchInfoMapper bizBatchInfoMapper;
    @Autowired
    protected ReceiptRelationService receiptRelationService;
    @Autowired
    protected ToolsInputMoveTypeComponent toolsInputMoveTypeComponent;
    @Autowired
    protected ToolsInputWriteOffMoveTypeComponent toolsInputWriteOffMoveTypeComponent;
    @Autowired
    private BizBatchInfoDataWrap batchInfoDataWrap;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;
    @Autowired
    protected BatchInfoService bizBatchInfoService;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private ToolCommonService toolCommonService;
    @Autowired
    private StockBinDataWrap stockBinDataWrap;
    @Autowired
    private StockBatchDataWrap stockBatchDataWrap;
    @Autowired
    private EditCacheService editCacheService;
    /**
     * 页面初始化: 1、设置其他入库【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】 3、设置扩展功能【单据流】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"其他入库","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInputHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptInputHeadDTO().setReceiptType(EnumReceiptType.STOCK_TOOLS_INPUT.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO().setRelationRequired(true), new ButtonVO().setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 工器具入库单-分页
     *
     * @in ctx 入参 {@link BizReceiptInputSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO < BizReceiptInputHeadDTO > ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (UtilCollection.isEmpty(currentUser.getLocationList())) {
            return;
        }
        List<Long> locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());

        // 组装查询条件
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = this.setQueryWrapper(po, locationIds);
        // 分页处理
        IPage<BizReceiptInputHeadVO> page = po.getPageObj(BizReceiptInputHeadVO.class);
        // 获取工器具入库单
        bizReceiptInputHeadDataWrap.getToolInputList(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return QueryWrapper<BizStockInputHead>
     */
    private WmsQueryWrapper<BizReceiptInputSearchPO> setQueryWrapper(BizReceiptInputSearchPO po, List<Long> locationIds) {
        if (null == po) {
            po = new BizReceiptInputSearchPO();
        }
        Date postCreateTime = null;
        if (UtilObject.isNotNull(po.getPostCreateTime())) {
            postCreateTime = UtilLocalDateTime.getStartTime(po.getPostCreateTime());
        }
        Date postEndTime = null;
        if (UtilObject.isNotNull(po.getPostEndTime())) {
            postEndTime = UtilLocalDateTime.getEndTime(po.getPostEndTime());
        }

        // 查询条件设置
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = new WmsQueryWrapper<>();

        //单据类型
        wrapper.lambda().eq(BizReceiptInputSearchPO::getReceiptType, po.getReceiptType());

        // 单据号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInputSearchPO::getReceiptCode, BizReceiptInputHead.class, po.getReceiptCode());

        // 工具编码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getBatchCode()), BizReceiptInputSearchPO::getBatchCode, BizBatchInfo.class, po.getBatchCode());

        // 物料描述 dic_material DicMaterial
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getMatName()), BizReceiptInputSearchPO::getMatName,
                DicMaterial.class, po.getMatName());
        // 物料编码 dic_material
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptInputSearchPO::getMatCode,
                DicMaterial.class, po.getMatCode());

        //创建人
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptInputSearchPO::getUserName, SysUser.class, po.getCreateUserName());

        //前置单据号转前置单据ID
        String preReceiptHeadId = Const.STRING_EMPTY;
        if (EnumReceiptType.TOOL_MAINTAIN_INPUT.getValue().equals(po.getReceiptType())) {
            if (UtilString.isNotNullOrEmpty(po.getPreReceiptCode())) {
                LambdaQueryWrapper<BizReceiptOutputHead> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BizReceiptOutputHead::getReceiptCode, po.getPreReceiptCode());
                preReceiptHeadId = bizReceiptOutputHeadDataWrap.getOne(queryWrapper).getId().toString();
            }
        }
        //前置单据
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getPreReceiptCode()), BizReceiptInputSearchPO::getPreReceiptHeadId, BizReceiptInputItem.class, preReceiptHeadId);

        //单据状态
        wrapper.lambda().in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptInputSearchPO::getReceiptStatus, BizReceiptInputHead.class, po.getReceiptStatusList());

        // 创建时间 todo  创建时间条件是否正确
        wrapper.lambda().between((UtilObject.isNotNull(postCreateTime)), BizReceiptInputSearchPO::getCreateTime,
                BizReceiptInputItem.class, postCreateTime, postEndTime);

        // 库存ID
        wrapper.lambda().in(UtilCollection.isNotEmpty(locationIds), BizReceiptInputSearchPO::getLocationId, BizReceiptInputItem.class, locationIds);

        return wrapper.setEntity(po);
    }

    /**
     * 工器具入库详情
     *
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取工器具入库单 抬头
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizOtherInputHeadDTO =
                UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizOtherInputHeadDTO);

        inputComponent.setPrintInfo(bizOtherInputHeadDTO);

        bizOtherInputHeadDTO.getItemList().stream().forEach(item -> {
            item.setInputNum(1L);
            item.setToolTypeId(item.getBizBatchInfoDTO().getToolTypeId());
            item.setToolTypeName(item.getBizBatchInfoDTO().getToolTypeName());
            item.setFormatCode(item.getBizBatchInfoDTO().getFormatCode());
            item.setProductDate(item.getBizBatchInfoDTO().getProductionDate());
            item.setTechnicalSpecification(item.getBizBatchInfoDTO().getTechnicalSpecification());
            item.setToolManageStatusRemark(item.getBizBatchInfoDTO().getToolManageStatusRemark());
        });
        // 根据headId查询主数据信息 并设置入库数量为1
//        List<BizReceiptInputItemDTO> bizReceiptInputItemDTOS = bizReceiptInputHeadDataWrap.selectToolsInfoByHeadId(headId, EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TOOL.getValue());
//        bizReceiptInputItemDTOS.stream().forEach(bizReceiptInputItemDTO -> bizReceiptInputItemDTO.setInputNum(1L));
//        bizOtherInputHeadDTO.setItemList(bizReceiptInputItemDTOS);
        // 设置按钮组权限
        ButtonVO buttonVO = inputComponent.setButton(bizOtherInputHeadDTO);
        // 设置其他入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizOtherInputHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 保存工器具入库前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的工器具入库单}
     */
    public void checkSaveOtherInput(BizContext ctx) {
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验行项目是都为空
        inputComponent.checkEmptyItem(po);
    }

    /**
     * 保存入库单
     *
     * @param ctx
     */
    public void  saveInput(BizContext ctx) {
        // 入参上下文 - 要保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /*############################################### 处理 head 开始 ############################################### */
        String stockInputCode = po.getReceiptCode();
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        if(UtilNumber.isEmpty(po.getId())){
            po.setCreateUserId(user.getId());
        }

        po.setModifyUserId(user.getId());
        po.setCreateTime(UtilDate.getNow());
        po.setModifyTime(UtilDate.getNow());

        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新入库抬头表单
            bizReceiptInputHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteInputItem(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_INPUT.getValue());
            po.setReceiptCode(stockInputCode);
            bizReceiptInputHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存入库单head成功!单号{},主键{}", stockInputCode, po.getId());
        /*############################################### 处理 head 结束 ############################################### */
        /*############################################### 处理 item 开始 ############################################### */
        AtomicInteger rid = new AtomicInteger(1);
        List<BizBatchInfoDTO> saveBatchInfoDtoList = new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(po.getId());
            // 行号
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            // 行项目状态
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            // 工具编码id
            // 工具类型id
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
            itemDTO.setReceiptCode(po.getReceiptCode());
            itemDTO.setReceiptType(po.getReceiptType());
            // TODO-BO: 2022/7/13 后续注意需求变更
            Long ftyId = dictionaryService.getFtyIdCacheByCode(Const.TOOL_FTY_CODE);
            Long locationId = dictionaryService.getLocationIdCacheByCode(Const.TOOL_FTY_CODE, Const.TOOL_LOCATION_CODE);
            Long whIdCacheByCode = dictionaryService.getWhIdCacheByCode(Const.TOOL_WH_CODE);
            itemDTO.setFtyId(ftyId);
            itemDTO.setLocationId(locationId);
            itemDTO.setWhId(whIdCacheByCode);
            itemDTO.setQty(BigDecimal.valueOf(itemDTO.getInputNum()));
            // 非空字段赋值
            nonNullFiledAssign(itemDTO);
            BizBatchInfoDTO batchInfoDto = itemDTO.getBizBatchInfoDTO();

            if (UtilObject.isNotNull(batchInfoDto)) {
                batchInfoDto.setMatCode(itemDTO.getMatCode());
                if (UtilNumber.isEmpty(itemDTO.getMatId())) {
                    WmsQueryWrapper<DicMaterial> wrapper = new WmsQueryWrapper<>();
                    wrapper.lambda().eq(UtilString.isNotNullOrEmpty(batchInfoDto.getMatCode()), DicMaterial::getMatCode, batchInfoDto.getMatCode());
                    DicMaterial dicMaterial = dicMaterialDataWrap.getOne(wrapper);
                    if (dicMaterial == null) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_VALIDITY_TARGET_MAT_FAIL);
                    }
                    itemDTO.setMatId(dicMaterial.getId());
                }

                // 规格型号
                batchInfoDto.setFormatCode(itemDTO.getFormatCode());
                // 状态
                batchInfoDto.setToolStatus(itemDTO.getToolStatus());
                // 设置入库时间
                if (!(null == operationLogType)) {
                    if (UtilObject.isNotNull(batchInfoDto.getInputDate())) {
                        batchInfoDto.setInputDate(batchInfoDto.getInputDate());
                    } else {
                        batchInfoDto.setInputDate(UtilDate.getNow());
                    }
                }
                // 采购订单号  为空则填0
                batchInfoDto.setPurchaseReceiptCode(UtilString.isNotNullOrEmpty(String.valueOf(itemDTO.getBizBatchInfoDTO().getPurchaseReceiptCode())) ? itemDTO.getBizBatchInfoDTO().getPurchaseReceiptCode().toString() : "0");
                // 采购订单头id  为空则填0
                batchInfoDto.setPurchaseReceiptHeadId(UtilNumber.isNotEmpty(itemDTO.getBizBatchInfoDTO().getPurchaseReceiptHeadId()) ? itemDTO.getBizBatchInfoDTO().getPurchaseReceiptHeadId() : 0L);
                // 采购订单行项目号  为空则填0
                batchInfoDto.setPurchaseReceiptItemId(UtilNumber.isNotEmpty(itemDTO.getBizBatchInfoDTO().getPurchaseReceiptItemId()) ? itemDTO.getBizBatchInfoDTO().getPurchaseReceiptItemId() : 0L);
                // 维保日期默认30天
                batchInfoDto.setMaintenanceWarningPeriod(30);
                // 库存类型 T
                batchInfoDto.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TOOL.getValue());
                batchInfoDto.setMatId(itemDTO.getMatId());
                batchInfoDto.setFtyId(itemDTO.getFtyId());
                batchInfoDto.setCreateUserId(user.getId());
                batchInfoDto.setLocateUserId(user.getId());
                batchInfoDto.setDeptId(user.getUserDeptList().get(0).getDeptId());
                batchInfoDto.setDeptOfficeId(user.getUserDeptList().get(0).getDeptOfficeId());

                // 2023-06-14 工器具需求新增
                batchInfoDto.setTechnicalSpecification(itemDTO.getTechnicalSpecification());
                batchInfoDto.setToolSourceReceiptHeadId(itemDTO.getHeadId());
                batchInfoDto.setToolSourceReceiptType(EnumReceiptType.STOCK_TOOLS_INPUT.getValue());
                batchInfoDto.setToolSourceReceiptCode(stockInputCode);
                if (UtilString.isNotNullOrEmpty(itemDTO.getToolInspectUnit())) {
                    batchInfoDto.setToolInspectUnit(itemDTO.getToolInspectUnit());
                }
                // 维保有效期提前计算，写入到批次信息中，不再每次查询计算
                if (UtilObject.isNotNull(batchInfoDto.getMaintenanceDate()) && UtilObject.isNotNull(batchInfoDto.getMaintenanceCycle())) {
                    // 通用类和专用类工具允许不填写维保日期和周期，做空值判断
                    Date date = UtilDate.plusMonths(batchInfoDto.getMaintenanceDate(), batchInfoDto.getMaintenanceCycle());
                    batchInfoDto.setMaintenanceValidDate(date);
                }
                batchInfoDto.setToolCodingInitial(dictionaryService.getToolTypeCacheById(itemDTO.getToolTypeId()).getToolCodingInitial());

                saveBatchInfoDtoList.add(batchInfoDto);
            }
        }
//        log.debug("批量保存验收单批次信息成功!批次号{},操作人{}", saveBatchInfoDtoList.get(0).getBatchCode(), user.getUserName());
        // 批量保存批次 保存时直接保存在了batch_info表里 提交的时候根据batch_code进行更新
        this.multiCheckUKSaveOrUpdateBatchInfo(saveBatchInfoDtoList,ctx);
        List<BizReceiptInputBinDTO> binDTOList = new ArrayList<>();
        // 批量保存item
        AtomicInteger bid = new AtomicInteger(1);
        po.getItemList().forEach(item -> {
            BizReceiptInputBinDTO bizReceiptInputBinDTO = new BizReceiptInputBinDTO();
            item.setId(null);
            item.setBatchId(item.getBizBatchInfoDTO().getId());
            if (UtilString.isNullOrEmpty(String.valueOf(item.getPreReceiptHeadId()))) {
                item.setPreReceiptHeadId(0L);
            }
            if (UtilString.isNullOrEmpty(String.valueOf(item.getPreReceiptItemId()))) {
                item.setPreReceiptItemId(0L);
            }
            if(UtilNumber.isEmpty(item.getFtyId())){
                Long ftyId = dictionaryService.getFtyIdCacheByCode("S046");
                item.setFtyId(ftyId);
            }
            if (UtilNumber.isEmpty(item.getLocationId())){
                Long locationId = dictionaryService.getLocationIdCacheByCode("S046","W006");
                item.setLocationId(locationId);
            }
            bizReceiptInputItemDataWrap.saveOrUpdateDto(item);
        });
//        bizReceiptInputItemDataWrap.saveBatchDto(po.getItemList());
        log.debug("保存入库单item成功!单号{},headId{}", stockInputCode, po.getId());
        /*############################################### 处理 item 结束 ############################################### */
        // 保存单据流
        this.saveReceiptTree(po);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, stockInputCode);
        // 返回保存的入库单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, po);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, po.getId());

    }

    /**
     * 非空字段赋值
     *
     * @param itemDTO
     */
    public static void nonNullFiledAssign(BizReceiptInputItemDTO itemDTO) {
        /* ===================================== 非空字段赋值 =====================================*/
        itemDTO.setPreReceiptType(0);
        itemDTO.setPreReceiptQty(new BigDecimal(0));
        itemDTO.setReferReceiptHeadId(0L);
        itemDTO.setReferReceiptItemId(0L);
        itemDTO.setReferReceiptType(0);
        itemDTO.setTaskQty(new BigDecimal(0));
        itemDTO.setMatDocCode(" ");
        itemDTO.setMatDocYear(" ");
        itemDTO.setMoveTypeId(0L);
        itemDTO.setIsWriteOff(0);
        itemDTO.setIsPost(0);
        itemDTO.setWriteOffMatDocCode(" ");
        itemDTO.setWriteOffMatDocRid(" ");
        itemDTO.setWriteOffMatDocYear(" ");
        itemDTO.setItemRemark("工器具入库-行项目备注");
        /* ===================================== 非空字段赋值 =====================================*/
    }

    /**
     * 提交工器具入库前校验
     *
     * @param ctx
     */
    public void checkSubmitOtherInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验行项目是都为空 ******** */
        inputComponent.checkEmptyItem(po);
        /*  ################### 检查已入库数量和未入库数量 ###################*/
//        this.checkAlreadyInputNum(po, ctx);
    }

    /**
     * 提交工器具入库单
     *
     * @param ctx
     */
    public void submitToolsInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存工器具入库单
        this.saveInput(ctx);
    }

    /**
     * 生成ins凭证
     *
     * @param ctx
     */
    public void generateInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成ins凭证
            insMoveTypeDTO = toolsInputMoveTypeComponent.generateInsDocToPost(headDTO);
            // 过账前的校验和数量计算
            stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        } catch (Exception e) {
            log.error("入库单{}生成ins凭证，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 更新单据 - 未同步
            inputComponent.updateStatus(headDTO, headDTO.getItemList(),
                    EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            if (e instanceof WmsException) {
                throw new WmsException(((WmsException) e).getErrorCode(), ((WmsException) e).getArgs());
            } else {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
            }
        }
        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 将 output_item 的出库数量更新
     *
     * @param po 根据前序单据 preReceiptItemId 去修改
     */
    public void updateOutputInputQty(BizReceiptInputHeadDTO po, BizContext ctx) {
        try {
            // 更新设置行项目抬头状态
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            /* 将提交的 itemList 按照 preReceiptItemId 去分组 如果没有 preReceiptItemId 说明是手动添加或者是Excel导入的
                则不需要修改出库单的数量  当 preReceiptItemId 为0 时则代表不是基于出库单创建
            */
            Map<Long, List<BizReceiptInputItemDTO>> itemGroupByPreItemID = po.getItemList().stream()
                    .filter(itemDTO -> !(Long.valueOf("0").equals(itemDTO.getPreReceiptItemId())))
                    .collect(Collectors.groupingBy(BizReceiptInputItemDTO::getPreReceiptItemId));

            if (!itemGroupByPreItemID.isEmpty()) {
                itemGroupByPreItemID.entrySet().stream().forEach(item -> {
                    //将要入库的数量
                    BigDecimal willInputNum = new BigDecimal(item.getValue().size());
                    Long outputItemId = item.getKey();
                    BizReceiptOutputItem outputItem = bizReceiptOutputItemDataWrap.getById(outputItemId);
                    // 已经入库的数量
                    BigDecimal inputQty = outputItem.getInputQty();
                    // 修改后的数量
                    BigDecimal modifyInputQty = inputQty.add(willInputNum);
                    outputItem.setInputQty(modifyInputQty);
                    // 更新操作
                    bizReceiptOutputItemDataWrap.updateById(outputItem);
                });
            }

        } catch (Exception e) {
            log.error("入库单{}ins过账失败，失败原因：{}", po.getReceiptCode(), e.getMessage());
            // 失败时更新入库单及行项目为未同步
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 准备更新入库单状态
     *
     * @param headDTO     入库单head
     * @param itemDTOList 入库单item
     * @param status      要修改的单据状态
     */
    public void updateStatus(BizReceiptInputHeadDTO headDTO, List<BizReceiptInputItemDTO> itemDTOList, Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新入库单head
     *
     * @param headDto 入库单head
     */
    private void updateHead(BizReceiptInputHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptInputHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新入库单item
     *
     * @param itemDtoList 入库单item
     */
    public void updateItem(List<BizReceiptInputItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptInputItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 检查入库数量 和 未入库数量是否满足条件
     *
     * @param po
     */
    private void checkAlreadyInputNum(BizReceiptInputHeadDTO po, BizContext ctx) {
        /*如果preReceiptItemId 为空 则不需要检验*/
        Map<Long, List<BizReceiptInputItemDTO>> itemGroupByPreItemID = po.getItemList().stream()
                .filter(bizReceiptInputItemDTO -> UtilString.isNotNullOrEmpty(String.valueOf(bizReceiptInputItemDTO.getPreReceiptItemId())))
                .collect(Collectors.groupingBy(BizReceiptInputItemDTO::getPreReceiptItemId));
        if (!itemGroupByPreItemID.isEmpty()) {
            itemGroupByPreItemID.entrySet().stream().forEach(item -> {
                // 将要入库的数量
                int willInputNum = item.getValue().size();
                Long id = item.getKey();
                BizReceiptOutputItem outputItem = bizReceiptOutputItemDataWrap.getById(id);
                BigDecimal qty = outputItem.getQty();
                BigDecimal inputQty = outputItem.getInputQty();
                int canInputNum = qty.subtract(inputQty).intValue();
                if (!(willInputNum <= canInputNum)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_NOT_ENOUGH);
                }
            });
        }
    }

    /**
     * 删除入库单行项目
     *
     * @param po 要删除的入库信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInputItem(BizReceiptInputHeadDTO po) {
        UpdateWrapper<BizReceiptInputItem> wrapperItem = new UpdateWrapper<>();
        wrapperItem.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputItem::getHeadId, po.getId());
        bizReceiptInputItemDataWrap.physicalDelete(wrapperItem);
        UpdateWrapper<BizReceiptInputBin> wrapperBin = new UpdateWrapper<>();
        wrapperBin.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputBin::getHeadId, po.getId());
        binDataWrap.physicalDelete(wrapperBin);
    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的入库单
     */
    public void saveReceiptTree(BizReceiptInputHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptInputItemDTO item : headDTO.getItemList()) {
            if (UtilNumber.isNotEmpty(item.getPreReceiptType()) && UtilNumber.isNotEmpty(item.getPreReceiptHeadId())
                    && UtilNumber.isNotEmpty(item.getPreReceiptItemId())) {
                BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                        .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                        .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                        .setPreReceiptItemId(item.getPreReceiptItemId());
                dtoList.add(dto);
            }
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 工器具冲销前校验
     *
     * @param ctx
     */
    public void checkOtherInputWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilNumber.isEmpty(po.getHeadId()) && UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单行项目
        List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptInputItemDTO> inputItemDTOList =
                UtilCollection.toList(inputItemList, BizReceiptInputItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(inputItemDTOList);
        // 校验行项目单据类型及状态
        Set<String> ridSet = inputItemDTOList.stream().filter(e -> !this.itemCanWriteOff(e))
                .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (ridSet.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, ridSet.toString());
        }
        // 冲销标识等于1或者过账标识等于0
        Set<String> isWriteOff = inputItemDTOList.stream()
                .filter(e -> EnumRealYn.TRUE.getIntValue().equals(e.getIsWriteOff())
                        || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
                .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (isWriteOff.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, isWriteOff.toString());
        }
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        // 转dto
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 设置冲销标识
        inputItemDTOList.forEach(itemDTO -> itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        inputHeadDTO.setItemList(inputItemDTOList);
        // 设置要冲销的其他入库单到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, inputHeadDTO);
    }


    /**
     * 冲销行项目校验
     *
     * @param inputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptInputItemDTO inputItemDTO) {
        Integer receiptType = inputItemDTO.getReceiptType();
        Integer receiptStatus = inputItemDTO.getReceiptStatus();
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus))
                && EnumReceiptType.STOCK_TOOLS_INPUT.getValue().equals(receiptType);
    }

    /**
     * 生成ins冲销过账凭证
     *
     * @param ctx
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成凭证
            insMoveTypeDTO = toolsInputWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    public void writeOffInputToIns(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - 凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        try {
            /* ***** 修改库存 ***** */
            stockCommonService.modifyStock(insMoveTypeDTO);
            Map<Long, List<BizReceiptInputItemDTO>> itemGroupByPreItemID = headDTO.getItemList().stream()
                    .filter(itemDTO -> !(Long.valueOf("0").equals(itemDTO.getPreReceiptItemId())))
                    .collect(Collectors.groupingBy(BizReceiptInputItemDTO::getPreReceiptItemId));
            if (!itemGroupByPreItemID.isEmpty()) {
                itemGroupByPreItemID.entrySet().stream().forEach(item -> {
                    // 要冲销的数量
                    BigDecimal willInputNum = new BigDecimal(item.getValue().size());
                    Long outputItemId = item.getKey();
                    BizReceiptOutputItem outputItem = bizReceiptOutputItemDataWrap.getById(outputItemId);
                    // 已经入库的数量
                    BigDecimal inputQty = outputItem.getInputQty();
                    // 修改后的数量
                    BigDecimal modifyInputQty = inputQty.subtract(willInputNum);
                    outputItem.setInputQty(modifyInputQty);
                    // 更新操作
                    bizReceiptOutputItemDataWrap.updateById(outputItem);
                });
            }
            /* ***** 更新item状态-已冲销 ***** */
            this.updateStatus(null, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            /* ***** 更新head状态-已完成 ***** */
            List<Integer> itemStatusList = this.getItemStatusList(headDTO.getId());
            if (bizCommonService.getCompletedItemStatusSet().containsAll(itemStatusList)) {
                // 所有行项目状态都是【已完成】或【冲销中】或【已冲销】时，修改单据状态为已完成
                this.updateStatus(headDTO, null, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            }
            /* ***** 单据日志 - 冲销 ***** */
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                    EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("入库单{}ins冲销过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 获取行项目状态集合
     *
     * @param headId 单据id
     * @return 状态列表
     */
    public List<Integer> getItemStatusList(Long headId) {
        // 获取入库单行项目
        List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap
                .list(new QueryWrapper<BizReceiptInputItem>().lambda().eq(BizReceiptInputItem::getHeadId, headId));
        // 行项目状态集合
        return inputItemList.stream().map(BizReceiptInputItem::getItemStatus).collect(Collectors.toList());
    }

    /**
     * 工器具主数据导出报表
     *
     * @param ctx
     */
    public void exportToolsBatchDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("工器具库存报表"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        this.selectToolsBatchDetail(ctx);
        PageObjectVO<ToolsMaterialInfoVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ToolsMaterialInfoVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

    /**
     * 查询工器具库存
     *
     * @param ctx ctx
     */
    public void selectToolsBatchDetail(BizContext ctx) {
        ToolsMaterialInfoSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<ToolsMaterialInfoVO> result;
        IPage<ToolsMaterialInfoVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ToolsMaterialInfoVO.class);
        }
        if (null == po) {
            po = new ToolsMaterialInfoSearchPO();
        }
        // 设置查询条件
        WmsQueryWrapper<ToolsMaterialInfoSearchPO> wrapper = new WmsQueryWrapper<>();
        // 拼装参数
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getSpecStock()), ToolsMaterialInfoSearchPO::getSpecStock, BizBatchInfo.class, po.getSpecStock())
                .like(UtilString.isNotNullOrEmpty(po.getMatName()), ToolsMaterialInfoSearchPO::getMatName,DicMaterial.class, po.getMatName())
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), ToolsMaterialInfoSearchPO::getMatCode,DicMaterial.class, po.getMatCode())
                .eq(UtilString.isNotNullOrEmpty(po.getBatchCode()),ToolsMaterialInfoSearchPO::getBatchCode,BizBatchInfo.class,po.getBatchCode())
                .eq(UtilNumber.isNotEmpty(po.getMaintenanceCycle()), ToolsMaterialInfoSearchPO::getMaintenanceCycle, BizBatchInfo.class,po.getMaintenanceCycle())
                .like(UtilString.isNotNullOrEmpty(po.getOutFtyCode()), ToolsMaterialInfoSearchPO::getOutFtyCode, BizBatchInfo.class, po.getOutFtyCode())
                .eq(UtilNumber.isNotEmpty(po.getToolTypeId()),ToolsMaterialInfoSearchPO::getToolTypeId,BizBatchInfo.class,po.getToolTypeId())
                .lt(UtilObject.isNotNull(po.getMaintenanceValidDate()),ToolsMaterialInfoSearchPO::getMaintenanceValidDate, BizBatchInfo.class,po.getMaintenanceValidDate())
                .like(UtilString.isNotNullOrEmpty(po.getToolInspectUnit()), ToolsMaterialInfoSearchPO::getToolInspectUnit, BizBatchInfo.class, po.getToolInspectUnit())
                .eq(UtilNumber.isNotEmpty(po.getMaintenanceWarningPeriod()), ToolsMaterialInfoSearchPO::getMaintenanceWarningPeriod, po.getMaintenanceWarningPeriod());
        wrapper.like(UtilString.isNotNullOrEmpty(po.getBorrowUserName()), "su2.user_name", po.getBorrowUserName());
        wrapper.like(UtilString.isNotNullOrEmpty(po.getBorrowDeptName()), "dp2.dept_name", po.getBorrowDeptName());
        wrapper.eq(UtilNumber.isNotEmpty(po.getTypeId()), "t8.id", po.getTypeId());
        result = bizBatchInfoMapper.selectToolDataPageVOList(page, wrapper);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<ToolsMaterialInfoVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 工器具提交信息打印
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void webPrint(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectUpdatePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (UtilObject.isNotNull(po) && UtilCollection.isNotEmpty(po.getItemList())) {
            // 装载要保存的标签数据集合
            List<BizLabelDataDTO> labelDataList = new ArrayList<>();
            // 装载要更新的批次信息
            List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
            // 装载打印机打印数据
            List<LabelBatch> labelBatchList = new ArrayList<>();
            for (BizReceiptInspectItemDTO inspectNoticeItemDTO : po.getItemList()) {
                // 行项目打印状态
                Integer printItemStatus = inspectNoticeItemDTO.getPrintItemStatus();
                // 标签类型 0：普通标签 1：RFID抗金属 2：RFID非抗金属
                Integer tagType = inspectNoticeItemDTO.getBizBatchInfoDTO().getTagType();
                // 单品/批次 0批次 1单品
                Integer isSingle = inspectNoticeItemDTO.getBizBatchInfoDTO().getIsSingle();
                // 未打印
//                if (EnumRealYn.FALSE.getIntValue().equals(printItemStatus)) {
                // 设置要更新的采购验收单数据
                inspectNoticeItemDTO.setPrintItemStatus(EnumRealYn.TRUE.getIntValue());
                inspectNoticeItemDTO.setIsSingle(isSingle);
                inspectNoticeItemDTO.setTagType(tagType);
                // 设置要更新的批次信息数据
                inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectHeadId(inspectNoticeItemDTO.getHeadId());
                inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectItemId(inspectNoticeItemDTO.getId());
                inspectNoticeItemDTO.getBizBatchInfoDTO()
                        .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
                inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectCode(inspectNoticeItemDTO.getReceiptCode());
                inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
                bizBatchInfoDTOList.add(inspectNoticeItemDTO.getBizBatchInfoDTO());
                // 批次 + 普通标签不需要生成rfid,其他生成rfid
                if (!(EnumRealYn.FALSE.getIntValue().equals(isSingle)
                        && EnumTagType.GENERAL.getValue().equals(tagType))) {
                    for (BizLabelDataDTO label : inspectNoticeItemDTO.getBizLabelDataDTOList()) {
                        // 生成标签编码
                        String labelCode =
                                bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());
                        BigDecimal qty = label.getQty();
                        // 设置标签数据
                        label = UtilBean.newInstance(inspectNoticeItemDTO, label.getClass());
                        label.setId(null);
                        label.setLabelCode(labelCode);
                        label.setSnCode(labelCode);
                        label.setQty(qty);
                        label.setLabelType(inspectNoticeItemDTO.getTagType());
                        label.setReceiptHeadId(inspectNoticeItemDTO.getHeadId());
                        label.setReceiptItemId(inspectNoticeItemDTO.getId());
                        label.setReceiptType(inspectNoticeItemDTO.getReceiptType());
                        label.setPreReceiptHeadId(inspectNoticeItemDTO.getPreReceiptHeadId());
                        label.setPreReceiptItemId(inspectNoticeItemDTO.getPreReceiptItemId());
                        label.setPreReceiptType(inspectNoticeItemDTO.getPreReceiptType());
                        labelDataList.add(label);
                        // 设置rfid标签打印数据
                        this.setPrintData(labelBatchList, label, inspectNoticeItemDTO, user);
                    }
                } else {
                    // 设置普通标签打印数据
                    this.setPrintData(labelBatchList, null, inspectNoticeItemDTO, user);
                }
            }
            /* *** 更新采购验收单【行项目打印状态、标签类型、打印份数】del *** */
            log.info("工器具信息-web端-打印-成功");
            /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
            /* *** 插入标签数据及关联属性 del *** */
            // 推送MQ - 调用物料标签打印
            if (UtilCollection.isNotEmpty(labelBatchList)) {
                ProducerMessageContent message =
                        ProducerMessageContent.messageContent(TagConst.PRINT_MAT_LABEL, labelBatchList);
                RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
            }
        }
    }

    /**
     * 设置标签打印数据
     *
     * @param labelBatchList 装载打印机打印数据
     * @param labelDataDTO   标签数据
     * @param itemDTO        要打印的采购验收单行项目
     * @param user           当前用户
     */
    private void setPrintData(List<LabelBatch> labelBatchList, BizLabelDataDTO labelDataDTO,
                              BizReceiptInspectItemDTO itemDTO, CurrentUser user) {
        // 标签类型 0：普通标签 1：RFID抗金属 2：RFID非抗金属
        Integer tagType = itemDTO.getBizBatchInfoDTO().getTagType();
        // 单品/批次 0批次 1单品
        Integer isSingle = itemDTO.getBizBatchInfoDTO().getIsSingle();
        LabelBatch labelBatch = new LabelBatch().setMatCode(itemDTO.getMatCode()).setMatName(itemDTO.getMatName())
                .setPurchaseOrder(itemDTO.getReceiptCode()).setMaterialNeeds("采矿部李楼采矿场").setQrCode("1000000001 2")
                .setSupplier(itemDTO.getSupplierName()).setReceiver(user.getUserName()).setPrinterIp(itemDTO.getPrinterIp())
                .setPrinterPort(itemDTO.getPrinterPort()).setPrinterIsDefault(itemDTO.getPrinterDefault())
                .setPrinterIsPortable(itemDTO.getPrinterIsPortable());
        if (!(EnumRealYn.FALSE.getIntValue().equals(isSingle) && EnumTagType.GENERAL.getValue().equals(tagType))) {
            labelBatch.setRfidCode(labelDataDTO.getLabelCode());
            labelBatch.setQty(labelDataDTO.getQty());
        } else {
            labelBatch.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
            labelBatch.setQty(itemDTO.getQty());
        }
        for (int pNum = 0; pNum < itemDTO.getPrintNum(); pNum++) {
            labelBatchList.add(labelBatch);
        }
    }

    /**
     * 删除工器具入库单
     *
     * @param ctx
     */
    public void deleteToolsInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除工器具入库单 ******** */
        if (po.isDeleteAll()) {
            // 物理删除工器具主数据
            batchInfoDataWrap.multiPhysicalDeleteByIdList(po.getBatchIds());
            // 删除 bin表信息
            binDataWrap.deleteByHeadId(po.getHeadId());
            // 删除工器具入库单head
            bizReceiptInputHeadDataWrap.physicalDeleteById(po.getHeadId());
            // 删除工器具入库单item
            bizReceiptInputItemDataWrap.deleteByHeadId(po.getHeadId());
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                    EnumReceiptType.STOCK_TOOLS_INPUT.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                    ctx.getCurrentUser().getId());
        } else {
            // 删除工器具主数据
            batchInfoDataWrap.multiPhysicalDeleteByIdList(po.getBatchIds());
            // 删除 bin表信息
            binDataWrap.deleteByHeadIdAndItemId(po.getHeadId(), po.getItemIds());
            // 删除工器具入库单item
            bizReceiptInputItemDataWrap.multiPhysicalDeleteByIdList(po.getItemIds());
        }
    }

    /**
     * 工器具入库单-前续单据
     *
     * @param ctx
     * @return
     */
    public MultiResultVO<DicMaterialListVO> getList(BizContext ctx) {

        DicMaterialSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("获取物料列表 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new DicMaterialSearchPO();
        }
        // 查询条件设置
        QueryWrapper<DicMaterialSearchPO> wrapper = new QueryWrapper<>();
        po.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
        // 已完成
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 拼装参数
        wrapper.lambda()
                // 状态
                .eq(UtilNumber.isNotNull(po.getReceiptStatus()), DicMaterialSearchPO::getReceiptStatus, po.getReceiptStatus())
                // 单据类型
                .eq(UtilNumber.isNotNull(po.getReceiptType()), DicMaterialSearchPO::getReceiptType, po.getReceiptType())
                // 领料出库单号
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), DicMaterialSearchPO::getReceiptCode, po.getReceiptCode())
                // 冻结标识
                .eq(UtilNumber.isNotNull(po.getIsFreeze()), DicMaterialSearchPO::getIsFreeze, po.getIsFreeze())
                // 物料编码
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), DicMaterialSearchPO::getMatCode, po.getMatCode())
                // 物料描述
                .like(UtilString.isNotNullOrEmpty(po.getMatName()), DicMaterialSearchPO::getMatName, po.getMatName())
                // 物料类型
                .eq(UtilNumber.isNotEmpty(po.getMatTypeId()), DicMaterialSearchPO::getMatTypeId, po.getMatTypeId())
                // 物料组
                .eq(UtilNumber.isNotEmpty(po.getMatGroupId()), DicMaterialSearchPO::getMatGroupId, po.getMatGroupId());
        List<DicMaterialListVO> dicMaterialVOList = dicMaterialDataWrap.getDicMaterialVOListByReceiptCode(wrapper);
        if (UtilNumber.isNotEmpty(po.getFtyId()) && UtilNumber.isNotEmpty(po.getLocationId())) {
            for (DicMaterialListVO dicMaterialListVO : dicMaterialVOList) {
                dicMaterialListVO.setFtyId(po.getFtyId());
                dicMaterialListVO.setLocationId(po.getLocationId());
                dicMaterialListVO.setWhId(po.getWhId());
            }
        }
        return new MultiResultVO<>(dicMaterialVOList);
    }

    private void importDataCheckNonNull(ToolsInputImportPO line, int idx) {
        if (UtilString.isNullOrEmpty(String.valueOf(line.getMatName()))) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 请填写工具名称信息", idx));
        }
        if (UtilNumber.isEmpty(line.getInputQty().intValue())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 请填写入库数量", idx));
        }
        if (UtilString.isNullOrEmpty(String.valueOf(line.getBinCode()))) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 请填写仓位信息", idx));
        }
        if (UtilString.isNullOrEmpty(String.valueOf(line.getToolTypeId()))) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 请填写工具类型", idx));
        }
        if (UtilString.isNullOrEmpty(String.valueOf(line.getMaintenanceDate()))
            && !("通用工器具".equals(line.getToolTypeName()) || "专用工器具".equals(line.getToolTypeName()))
        ) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 请填写维保日期", idx));
        }
        if (UtilNumber.isEmpty(line.getMaintenanceCycle())
            && !("通用工器具".equals(line.getToolTypeName()) || "专用工器具".equals(line.getToolTypeName()))
        ) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 请填写维保周期", idx));
        }
        if (UtilString.isNullOrEmpty(line.getUnitName())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 请填写计量单位", idx));
        }
    }

    /**
     * 工器具入库单导入
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void importToolsData(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        List<BizBatchInfoDTO> materialDTOList = new ArrayList<>();
        DicMaterialListConfirmSplitPO po = new DicMaterialListConfirmSplitPO();
        po.setMaterialDTOList(materialDTOList);
        try {
            //获取EXCEL数据matGroupCode = null
            List<ToolsInputImportPO> toolsInputImportPOList = (List<ToolsInputImportPO>) UtilExcel.readExcelData(file.getInputStream(), ToolsInputImportPO.class);
            for (int i = 1; i <= toolsInputImportPOList.size(); i++) {
                ToolsInputImportPO source = toolsInputImportPOList.get(i - 1);
                // 校验不可为空字段
                this.importDataCheckNonNull(source, i);
                List<DicWhStorageBinDTO> binDown = toolCommonService.getBinDown(EnumToolDefaultStorageType.TOOL_NORMAL.getTypeCode());
                binDown.addAll(toolCommonService.getBinDown(EnumToolDefaultStorageType.TOOL_DISABLED.getTypeCode()));
                Optional<DicWhStorageBinDTO> binCodeInfo = binDown.stream().filter(bin -> bin.getBinCode().equals(source.getBinCode())).findAny();
                /* ================================ 仓位判断 ================================*/
                if (binCodeInfo.isPresent()){
                    source.setBinCode(binCodeInfo.get().getBinCode());
                    source.setBinId(binCodeInfo.get().getId());
                } else {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, StrUtil.format("第{}行, 填写的不是有效仓位", i));
                }

                /* ================================ 工具类计量单位判断 ================================*/
                Long unitIdCacheByName = dictionaryService.getUnitIdCacheByName(source.getUnitName());
                if (unitIdCacheByName == null) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, StrUtil.format("第{}行, 填写的不是计量单位", i));
                } else {
                    source.setUnitId(unitIdCacheByName);
                    source.setUnitCode(dictionaryService.getUnitCacheById(unitIdCacheByName).getUnitCode());
                }

                // 通过缓存获取工具类型来匹配传入的值从而设置工具类型id，
                /* ================================ 工具类型判断 ================================*/
                List<DicToolType> allToolTypeCache = dictionaryService.getAllToolTypeCache();
                Optional<DicToolType> any = allToolTypeCache.stream().filter(dicToolType -> dicToolType.getToolTypeName().equals(source.getToolTypeName()))
                        .findAny();
                if (any.isPresent()) {
                    source.setToolTypeId(any.get().getId());
                    source.setToolTypeName(any.get().getToolTypeName());
                    // 如果工具类型不是通用工具类型，则必须填写维保周期、维保日期
                    if (!(any.get().getToolTypeName().equals("通用工器具") || any.get().getToolTypeName().equals("专用工器具"))){
                        if (UtilString.isNullOrEmpty(String.valueOf(source.getMaintenanceDate()))||UtilNumber.isEmpty(source.getMaintenanceCycle())){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, StrUtil.format("第{}行, 工具类型不是通用工具，必须填写维保周期、维保日期", i));
                        }
                    }
                } else {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, StrUtil.format("第{}行, 不是有效的工具类型", i));
                }

                /* ================================ 管理状态处理 ================================*/
                if (EnumToolStatus.NORMAL.getCode().equals(source.getToolStatusName())) {
                    source.setToolStatus(EnumToolStatus.NORMAL.getValue());
                } else if (EnumToolStatus.RESTRICTED.getCode().equals(source.getToolStatusName())) {
                    source.setToolStatus(EnumToolStatus.RESTRICTED.getValue());
                } else if (EnumToolStatus.DISABLE.getCode().equals(source.getToolStatusName())) {
                    source.setToolStatus(EnumToolStatus.DISABLE.getValue());
                    source.setBinCode(null);
                    source.setBinId(null);
                } else {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, StrUtil.format("第{}行, 不是有效的管理状态", i));
                }

                /* ================================ 入库数量处理 ================================*/
                if (source.getInputQty() == null || !NumberUtil.isInteger(source.getInputQty().toString())
                    || source.getInputQty().compareTo(BigDecimal.ONE) < 0) {
                    // 入库数量不是整数或者入库数量小于1
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, StrUtil.format("第{}行, 不是有效的入库数量", i));
                }
                // 导入时，入库数量不为1时，拆分为多个
                for (int j = 0; j < source.getInputQty().longValue(); j++) {
                    BizBatchInfoDTO target = new BizBatchInfoDTO();
                    UtilBean.copy(source, target);
                    target.setInputQty(BigDecimal.ONE);
                    materialDTOList.add(target);
                }
            }
            po.setMaterialDTOList(materialDTOList);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        } catch (IOException e) {
            e.printStackTrace();
        }
        this.confirmSplitMat(ctx, false);
    }

    /**
     * 确认物料并拆分
     *
     * @param ctx
     * @param checkNum 是否要根据库存拆分，false 不需要根据库存拆分
     * @return
     */
    public List<BizBatchInfoDTO> confirmSplitMat(BizContext ctx, boolean checkNum) {
        DicMaterialListConfirmSplitPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.getMaterialDTOList().size() == 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<BizBatchInfoDTO> batchInfoDTOList = po.getMaterialDTOList();
        List<BizBatchInfoDTO> splitMaterialList = new ArrayList<>();
        // 计算出未出库数量
        AtomicReference<BigDecimal> noOutNum = new AtomicReference<>(BigDecimal.ZERO);
        for (BizBatchInfoDTO bizBatchInfoDTO : batchInfoDTOList) {

            if (checkNum) {
                noOutNum.set(bizBatchInfoDTO.getQty().subtract(bizBatchInfoDTO.getInputQty()));
            } else {
                noOutNum.set(bizBatchInfoDTO.getInputQty());
            }
            // 判断是否有该物料主数据
            // 查询条件设置
            DicMaterialDTO materialDTO = null;
            if (UtilString.isNotNullOrEmpty(bizBatchInfoDTO.getMatCode())
                    || UtilString.isNotNullOrEmpty(bizBatchInfoDTO.getMatName())) {
                QueryWrapper<DicMaterial> wrapper = new QueryWrapper<>();
                wrapper.lambda()
                        .eq(UtilString.isNotNullOrEmpty(bizBatchInfoDTO.getMatCode()), DicMaterial::getMatCode, bizBatchInfoDTO.getMatCode())
                        .eq(UtilString.isNotNullOrEmpty(bizBatchInfoDTO.getMatName()), DicMaterial::getMatName, bizBatchInfoDTO.getMatName())
                        .last("limit 1");
                DicMaterial one = dicMaterialDataWrap.getOne(wrapper);
                if (one != null) {
                    materialDTO = UtilBean.newInstance(one, DicMaterialDTO.class);
                    dataFillService.fillAttr(materialDTO);
                    bizBatchInfoDTO.setMatCode(materialDTO.getMatCode());
                }
            }

            if (materialDTO == null) {
                // 工器具导入，物料不存在时，生成新的物料主数据
                DicMaterial newMaterial = new DicMaterial();
                newMaterial.setMatCode(bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TOOL_MATERIAL_CODE.getValue()));
                newMaterial.setMatName(bizBatchInfoDTO.getMatName());
                newMaterial.setUnitId(dictionaryService.getUnitIdCacheByName(bizBatchInfoDTO.getUnitName()));
//                newMaterial.setMatGroupId(dictionaryService.getMatGroupIdByMatGroupCode("成套手工工具"));
//                newMaterial.setMatTypeId(dictionaryService.getMatTypeIdByMatTypeCode("华能逻辑编码物资-临时"));
                newMaterial.setMatGroupId(0L);
                newMaterial.setMatTypeId(0L);
                newMaterial.setCreateUserId(ctx.getCurrentUser().getId());
                newMaterial.setModifyUserId(ctx.getCurrentUser().getId());
                dicMaterialDataWrap.save(newMaterial);
                editCacheService.refreshMatCacheById(newMaterial.getId());
                materialDTO = UtilBean.newInstance(newMaterial, DicMaterialDTO.class);
                dataFillService.fillAttr(materialDTO);
                bizBatchInfoDTO.setMatCode(materialDTO.getMatCode());
                // 新增物料后刷新物料缓存，后续填充需要
            }
            // 按照未入库的数量并且数量大于2进行拆分并加入到list集合中
            if (!(noOutNum.get().compareTo(new BigDecimal(1)) < 1)) {
                for (int i = 0; i < noOutNum.get().intValue(); i++) {
                    BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                    UtilBean.copy(bizBatchInfoDTO, batchInfoDTO);
                    batchInfoDTO.setMatId(materialDTO.getId());
                    batchInfoDTO.setUnitName(materialDTO.getUnitName());
                    batchInfoDTO.setMatName(materialDTO.getMatName());
                    // 手动配置一下行项目状态
                    batchInfoDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());

                    splitMaterialList.add(batchInfoDTO);
                }
            }
            // 按照未入库的数量并且数量为1 的放入到list集合中
            if ((noOutNum.get().compareTo(new BigDecimal(1)) < 1) && noOutNum.get().compareTo(BigDecimal.ZERO)!=0) {
                bizBatchInfoDTO.setMatId(materialDTO.getId());
                bizBatchInfoDTO.setUnitName(materialDTO.getUnitName());
                bizBatchInfoDTO.setMatName(materialDTO.getMatName());
                // 手动配置一下行项目状态
                bizBatchInfoDTO.setItemStatus(10);
                splitMaterialList.add(bizBatchInfoDTO);
            }
        }
        if (noOutNum.get().compareTo(BigDecimal.ZERO)==0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_OUTPUT_ORDER_OUTCOUNT_IS_ZERO);
        }
        // 设置入库数量为1,行项目状态为草稿状态
        splitMaterialList.stream().forEach(item -> {
            item.setInputNum(1L);
//            item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        });
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, splitMaterialList);
        return splitMaterialList;
    }

    /**
     * 工器具主数据查询
     *
     * @param ctx
     * @return
     */
    public PageObjectVO<ToolsMaterialInfoVO> getToolMaterialInfo(BizContext ctx) {
        ToolsMaterialInfoSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            po = new ToolsMaterialInfoSearchPO();
        }

        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return new PageObjectVO<>();
        }
        if (CollectionUtils.isEmpty(currentUser.getLocationList())) {
            return new PageObjectVO<>();
        }
        List<Long> locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());

        // 设置查询条件
        WmsQueryWrapper<ToolsMaterialInfoSearchPO> wrapper = new WmsQueryWrapper<>();
        po.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TOOL.getValue());
        // 拼装参数
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getSpecStock()), ToolsMaterialInfoSearchPO::getSpecStock, BizBatchInfo.class, po.getSpecStock())
                .like(UtilString.isNotNullOrEmpty(po.getMatName()), ToolsMaterialInfoSearchPO::getMatName, DicMaterial.class, po.getMatName())
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), ToolsMaterialInfoSearchPO::getMatCode, DicMaterial.class, po.getMatCode())
                .eq(UtilString.isNotNullOrEmpty(po.getBatchCode()), ToolsMaterialInfoSearchPO::getBatchCode, BizBatchInfo.class, po.getBatchCode())
                .eq(UtilNumber.isNotEmpty(po.getMaintenanceCycle()), ToolsMaterialInfoSearchPO::getMaintenanceCycle, BizBatchInfo.class, po.getMaintenanceCycle())
                .like(UtilString.isNotNullOrEmpty(po.getOutFtyCode()), ToolsMaterialInfoSearchPO::getOutFtyCode, BizBatchInfo.class, po.getOutFtyCode())
                .eq(UtilNumber.isNotEmpty(po.getToolTypeId()), ToolsMaterialInfoSearchPO::getToolTypeId, BizBatchInfo.class, po.getToolTypeId())
                .lt(UtilObject.isNotNull(po.getMaintenanceValidDate()), ToolsMaterialInfoSearchPO::getMaintenanceValidDate, BizBatchInfo.class, po.getMaintenanceValidDate())
                .like(UtilString.isNotNullOrEmpty(po.getToolInspectUnit()), ToolsMaterialInfoSearchPO::getToolInspectUnit, BizBatchInfo.class, po.getToolInspectUnit())
                .eq(UtilNumber.isNotEmpty(po.getMaintenanceWarningPeriod()), ToolsMaterialInfoSearchPO::getMaintenanceWarningPeriod, po.getMaintenanceWarningPeriod())
                .eq(UtilString.isNotNullOrEmpty(po.getFormatCode()), ToolsMaterialInfoSearchPO::getFormatCode, BizBatchInfo.class, po.getFormatCode())
                .in(UtilCollection.isNotEmpty(locationIds), ToolsMaterialInfoSearchPO::getLocationId, StockBin.class, locationIds);
        wrapper.like(UtilString.isNotNullOrEmpty(po.getBorrowUserName()), "su2.user_name", po.getBorrowUserName());
        wrapper.like(UtilString.isNotNullOrEmpty(po.getBorrowDeptName()), "dp2.dept_name", po.getBorrowDeptName());
        wrapper.eq(UtilNumber.isNotEmpty(po.getTypeId()), "t8.id", po.getTypeId());
        Page<ToolsMaterialInfoVO> page = (Page<ToolsMaterialInfoVO>) po.getPageObj(ToolsMaterialInfoVO.class);
        page.setOptimizeCountSql(false);
        batchInfoDataWrap.getToolDataPageVOList(page, wrapper);
        long totalCount = page.getTotal();
        // 计算维保有效期
        page.getRecords().stream().forEach(toolsMaterialInfoVO -> {
            if (!(toolsMaterialInfoVO.getMaintenanceDate() == null || toolsMaterialInfoVO.getMaintenanceCycle() == null)) {
                Date date = UtilDate.plusMonths(toolsMaterialInfoVO.getMaintenanceDate(), toolsMaterialInfoVO.getMaintenanceCycle());
                toolsMaterialInfoVO.setMaintenancePeriod(date);
            }
        });
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
     *
     * @param batchInfoDtoList
     */
    public void multiCheckUKSaveOrUpdateBatchInfo(List<BizBatchInfoDTO> batchInfoDtoList,BizContext ctx) {
        // 获取批次号集合,去重
        Set<String> batchCodeList = new HashSet<>();
        for (BizBatchInfoDTO batchInfoDTO : batchInfoDtoList) {
            if (UtilString.isNotNullOrEmpty(batchInfoDTO.getBatchCode())) {
                batchCodeList.add(batchInfoDTO.getBatchCode());
            }
        }
        // 根据批次号集合批量查询所有批次信息
        List<BizBatchInfo> queryList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(batchCodeList)) {
            QueryWrapper<BizBatchInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(BizBatchInfo::getBatchCode, batchCodeList);
            queryList = batchInfoDataWrap.list(queryWrapper);
        }
        // 根据唯一索引,赋值id
        for (BizBatchInfo queryInfo : queryList) {
            String queryKey = queryInfo.getBatchCode() + "-" + queryInfo.getFtyId() + "-" + queryInfo.getMatId() + "-" + queryInfo.getSpecStock() + "-" + queryInfo.getSpecStockCode();
            for (BizBatchInfoDTO batchInfoDTO : batchInfoDtoList) {
                String batchInfoKey = batchInfoDTO.getBatchCode() + "-" + batchInfoDTO.getFtyId() + "-" + batchInfoDTO.getMatId() + "-" + UtilString.getStrIfNull(batchInfoDTO.getSpecStock()) + "-" + UtilString.getStrIfNull(batchInfoDTO.getSpecStockCode());
                if (queryKey.equals(batchInfoKey)) {
                    batchInfoDTO.setModifyTime(UtilDate.getNow());
                    batchInfoDTO.setModifyUserId(ctx.getCurrentUser().getModifyUserId());
                    batchInfoDTO.setId(queryInfo.getId());
                }
            }
        }
        // 无匹配id数据,赋值insertList(新增列表)
        List<BizBatchInfoDTO> insertList = new ArrayList<>();
        for (BizBatchInfoDTO batchInfoDTO : batchInfoDtoList) {
            if (UtilNumber.isEmpty(batchInfoDTO.getId())) {
                insertList.add(batchInfoDTO);
            }
        }
        // 批量保存批次信息
        if (insertList.size() > 0) {
            for (BizBatchInfoDTO batchInfoDTO : insertList) {
                batchInfoDTO.setId(null);
                if (UtilString.isNullOrEmpty(batchInfoDTO.getBatchCode())) {
                    // 若批次号为空则生成批次
                    String nextSequenceValue = bizCommonService.getNextSequenceValue(batchInfoDTO.getToolCodingInitial());
                    batchInfoDTO.setBatchCode(nextSequenceValue);
                }
                batchInfoDTO.setIsFreeze(batchInfoDTO.getIsFreeze() == null ? EnumFreezeType.NOT_FROZEN.getValue() : batchInfoDTO.getIsFreeze());
                batchInfoDTO.setIsSingle(batchInfoDTO.getIsSingle() == null ? EnumRealYn.FALSE.getIntValue() : batchInfoDTO.getIsSingle());
                batchInfoDTO.setTagType(batchInfoDTO.getTagType() == null ? EnumTagType.GENERAL.getValue() : batchInfoDTO.getTagType());
            }

            batchInfoDataWrap.saveBatchDto(insertList);
        }
        // 更新批次信息
        if (queryList.size() > 0) {
            batchInfoDataWrap.updateBatchDtoById(batchInfoDtoList);
        }
    }


    /**
     * ins入库过账
     *
     * @param ctx
     */
    public void postInputToIns(BizContext ctx) {
        // 入参上下文-ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 入参上下文-入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        try {
            // 修改库存
            stockCommonService.modifyStock(insMoveTypeDTO);
            // 将要入库的数量回写到output_item表里面
            this.updateOutputInputQty(headDTO, ctx);
            // 更新入库单状态 - 已提交
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            // 单据日志 - 过账
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("入库单{}ins过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 失败时更新入库单及行项目为未同步
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    public void checkSubmitToolsInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        /* ******** 校验行项目是都为空 ******** */
        inputComponent.checkEmptyItem(po);

        /* ******** 校验其他入库单行项目相关数量开始 ******** */
        inputComponent.checkEmptyItemQty(po);

        /* ******** 提交前校验物料、库存地点是否冻结 ******** */
        inputComponent.checkFreeze(po);
    }

    /**
     * 保存仓位【同时模式】
     *
     * @param ctx
     */
    public void saveInputBin(BizContext ctx) {
        // 入参上下文 - 已保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - 已保存的入库单code
        String stockInputCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);

        /* ********************** bin处理开始 *************************/
        AtomicInteger bid = new AtomicInteger(1);
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            if (UtilCollection.isNotEmpty(itemDTO.getBinList())) {
                itemDTO.getBinList().forEach(bin -> {
                    bin.setId(null);
                    bin.setHeadId(itemDTO.getHeadId());
                    bin.setItemId(itemDTO.getId());
                    bin.setBinId(itemDTO.getBinList().get(0).getBinId());
                    // 2023-06-17 工器具调整，创建时管理状态为禁用的，存储类型设置为禁用区
                    if (EnumToolStatus.DISABLE.getValue().equals(itemDTO.getToolStatus())) {
                        bin.setTypeId(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_DISABLED.getTypeCode()));
                    } else {
                        // 管理状态为【正常】和【限用】的，存储类型设置为901工器具非限制区
                        bin.setTypeId(dictionaryService.getStorageTypeIdCacheByCode(Const.TOOL_WH_CODE, EnumToolDefaultStorageType.TOOL_NORMAL.getTypeCode()));
                    }
                    bin.setBid(Integer.toString(bid.getAndIncrement()));
                    bin.setQty(BigDecimal.valueOf(itemDTO.getInputNum()));
                });
            } else {
                itemDTO.setBinList(new ArrayList<>());
            }
        }

        binDataWrap.saveBatchDto(po.getItemList().stream().flatMap(item -> item.getBinList().stream()).collect(Collectors.toList()));
        log.debug("保存入库单bin成功!单号{},headId{}", stockInputCode, po.getId());
        /* ********************** bin处理结束 *************************/
    }


    /**
     * 保存单品标签【同时模式】
     *
     * @in ctx  入参 {@link BizReceiptInputHeadDTO : "已保存的入库单}
     */
    public void saveLabelData(BizContext ctx) {
        // 入参上下文 - 已保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 装载批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();

        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            // 单品/批次 0批次 1单品
            Integer isSingle = itemDTO.getBizBatchInfoDTO().getIsSingle();
            if (UtilCollection.isNotEmpty(itemDTO.getBinList()) && EnumRealYn.TRUE.getIntValue().equals(isSingle)) {
                itemDTO.getBinList().forEach(binDTO -> {
                    for (int qty = 0; qty < binDTO.getQty().intValue(); qty++) {
                        BizLabelDataDTO label = new BizLabelDataDTO();
                        String labelCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());
                        label = UtilBean.newInstance(itemDTO, label.getClass());
                        label.setId(null);
                        label.setLabelCode(labelCode);
                        label.setSnCode(labelCode);
                        label.setQty(new BigDecimal(1));
                        label.setTypeId(binDTO.getTypeId());
                        label.setBinId(binDTO.getBinId());
                        label.setCellId(binDTO.getCellId());
                        label.setReceiptType(po.getReceiptType());
                        label.setReceiptHeadId(itemDTO.getHeadId());
                        label.setReceiptItemId(itemDTO.getId());
                        label.setReceiptBinId(binDTO.getId());
                        label.setReceiptType(itemDTO.getReceiptType());
                        labelDataList.add(label);
                    }
                });
            }
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());
        }

        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelReceiptRelService.multiRemoveLabel(po.getId());
            log.info("入库单删除标签及关联数据成功,headId{}", po.getId());


            log.info("入库单插入标签数据成功 " + JSONObject.toJSONString(labelDataList));

            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(po.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_CODE.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("入库单插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        /* *** 更新批次新信息标签类型 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("入库单更新批次标签类型成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
    }

    /**
     * 冻结操作
     * @param ctx
     */
    public void freezeOperation(BizContext ctx) {
        // 入参上下文 - 要冻结的入库单
        ToolsMaterialInfoSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilNumber.isNotEmpty(po.getBatchId())&&UtilNumber.isNotNull(po.getIsFreeze())){
            if (po.getIsFreeze()==1){
                // 根据batchId将仓位库存冻结
                stockBinDataWrap.updateByBatchId(po.getBatchId(),1L);
                // 根据batchId将批次库存表
                stockBatchDataWrap.updateByBatchId(po.getBatchId(),1L);
            }
            if (po.getIsFreeze()==0){
                // 根据batchId将仓位库存冻结
                stockBinDataWrap.updateByBatchId(po.getBatchId(),-1L);
                // 根据batchId将批次库存表
                stockBatchDataWrap.updateByBatchId(po.getBatchId(),-1L);
            }
        }
    }

    /**
     * 根据工具类型批量修改 维保周期 维保预警器
     */
    public void multiUpdateByToolTypeId(BizContext ctx) {
        // 入参上下文 - 要冻结的入库单
        ToolsMaterialInfoSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilNumber.isNotEmpty(po.getToolTypeId())) {
            Integer maintenanceCycle = po.getMaintenanceCycle();
            Integer maintenanceWarningPeriod = po.getMaintenanceWarningPeriod();
            if (UtilNumber.isNotEmpty(maintenanceCycle)&&UtilNumber.isNotEmpty(maintenanceWarningPeriod)){
                batchInfoDataWrap.updateByToolTypeId(po.getToolTypeId(),maintenanceCycle,maintenanceWarningPeriod);
            }
        }
        if(UtilString.isNotNullOrEmpty(po.getBatchCode())){
            BizBatchInfo batchInfo = batchInfoDataWrap.getById(po.getBatchId());
            Date maintenanceValidDate = null;
            if (batchInfo != null && batchInfo.getMaintenanceDate() != null && po.getMaintenanceCycle() != null) {
                maintenanceValidDate = UtilDate.plusMonths(batchInfo.getMaintenanceDate(), po.getMaintenanceCycle());
            }

            Date finalMaintenanceValidDate = maintenanceValidDate;
            batchInfoDataWrap.update(new UpdateWrapper<BizBatchInfo>(){{
                lambda().set(BizBatchInfo::getDescription,po.getDescription())
                        .set(BizBatchInfo::getToolManageStatusRemark,po.getToolManageStatusRemark())
                        .set(BizBatchInfo::getToolStatus,po.getToolStatus())
                        .set(UtilNumber.isNotEmpty(po.getMaintenanceCycle()), BizBatchInfo::getMaintenanceCycle, po.getMaintenanceCycle())
                        .set(finalMaintenanceValidDate != null, BizBatchInfo::getMaintenanceValidDate, finalMaintenanceValidDate)
                        .eq(BizBatchInfo::getBatchCode,po.getBatchCode())
                        .eq(UtilNumber.isNotEmpty(po.getBatchId()), BizBatchInfo::getId, po.getBatchId());
            }});
        }
    }

}
