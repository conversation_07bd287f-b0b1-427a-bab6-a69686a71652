package com.inossem.wms.bizdomain.apply.controller;

import com.inossem.wms.bizdomain.apply.service.biz.ReturnOldApplyService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPreVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.ReturnOldTypeMapVO;
import com.inossem.wms.common.model.erp.po.PreReceiptQueryPO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 退旧管理 退旧物资申请 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "退旧管理-退旧物资")
public class ReturnOldApplyController {

    @Autowired
    protected ReturnOldApplyService returnApplyService;

    /**
     * 退旧类型-下拉选项
     *
     * @return 退旧类型 vo
     */
    @ApiOperation(value = "退旧物资申请-退旧类型下拉", tags = {"退旧管理-退旧物资申请"})
    @GetMapping(path = "/apply/return-old-apply/return-old-type/down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<ReturnOldTypeMapVO>> getReturnOldType(BizContext ctx) {
        returnApplyService.getReturnOldType(ctx);
        MultiResultVO<ReturnOldTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 退旧物资申请-初始化
     *
     * @return 退旧物资申请单
     */
    @ApiOperation(value = "退旧物资申请-初始化", tags = {"退旧管理-退旧物资申请"})
    @GetMapping(value = "/apply/return-old-apply/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> init(BizContext ctx) {
        returnApplyService.init(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询退旧物资申请单列表-分页
     *
     * @param po 申请单分页查询入参
     * @return 退旧物资申请单列表
     */
    @ApiOperation(value = "查询退旧物资申请单列表-分页", tags = {"退旧管理-退旧物资申请"})
    @PostMapping(value = "/apply/return-old-apply/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptApplyPageVO>> getPage(@RequestBody BizReceiptApplySearchPO po, BizContext ctx) {
        returnApplyService.getPage(ctx);
        PageObjectVO<BizReceiptApplyPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询退旧物资申请单-详情
     *
     * @param id 退旧物资申请单抬头表主键
     * @return 退旧物资申请单详情
     */
    @ApiOperation(value = "查询退旧物资申请单详情", tags = {"退旧管理-退旧物资申请"})
    @GetMapping(value = "/apply/return-old-apply/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        returnApplyService.getInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询领料出库单行项目列表
     *
     * @param po 查询前序单据入参
     * @return 领料出库单行项目
     */
    @ApiOperation(value = "查询前序单据", tags = {"退旧管理-退旧物资申请"})
    @PostMapping(value = "/apply/return-old-apply/output-item/results")
    public BaseResult<MultiResultVO<BizReceiptApplyPreVO>> getOutputReceiptItemList(@RequestBody PreReceiptQueryPO po, BizContext ctx) {
        returnApplyService.getOutputReceiptItemList(ctx);
        MultiResultVO<BizReceiptApplyPreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 退旧物资申请-保存
     *
     * @param po 保存退旧物资申请表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "退旧物资申请-保存", tags = {"退旧管理-退旧物资申请"})
    @PostMapping(value = "/apply/return-old-apply/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        returnApplyService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 退旧物资申请-提交
     *
     * @param po 提交退旧物资申请表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "退旧物资申请-提交", tags = {"退旧管理-退旧物资申请"})
    @PostMapping(value = "/apply/return-old-apply/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        returnApplyService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 退旧物资申请-删除
     *
     * @param id 退旧物资申请单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "退旧物资申请-删除", tags = {"退旧管理-退旧物资申请"})
    @DeleteMapping(value = "/apply/return-old-apply/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        returnApplyService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_DELETE_SUCCESS, receiptCode);
    }

}
