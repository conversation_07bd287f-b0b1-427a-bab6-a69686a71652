<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.matview.dao.BizMaterialViewAuditMapper">
    <!-- biz_material_view_audit 物料主数据视图审批-->

    <!--温馨提示varchar2长度默认为50, 时间类型 datetime(3)/datetime 请自行修改
    create table biz_material_view_audit(
         	id bigint not null   comment 'id'primary key ,
         	receipt_code VARCHAR(50) null   comment '单据号' ,
         	receipt_status VARCHAR null   comment '状态【草稿、审批中、已驳回、已完成】' ,
         	dept_id VARCHAR null   comment '领用部门' ,
         	des VARCHAR(50) null   comment '单据描述' ,
         	is_delete VARCHAR null   comment '删除标识' ,
         	create_time  datetime null   comment '创建时间' ,
         	modify_time  datetime null   comment '修改时间' ,
         	create_user_id bigint null   comment '创建人id' ,
         	modify_user_id bigint null   comment '修改人id' 
    )
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_general_ci
     comment '物料主数据视图审批';
    -->

</mapper>
