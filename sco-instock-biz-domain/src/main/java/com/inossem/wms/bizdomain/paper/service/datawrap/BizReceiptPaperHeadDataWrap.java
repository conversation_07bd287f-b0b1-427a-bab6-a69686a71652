package com.inossem.wms.bizdomain.paper.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.paper.dao.BizReceiptPaperHeadMapper;
import com.inossem.wms.common.model.bizdomain.paper.entity.BizReceiptPaperHead;
import com.inossem.wms.common.model.bizdomain.paper.po.BizReceiptPaperSearchPO;
import com.inossem.wms.common.model.bizdomain.paper.vo.BizReceiptPaperPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 图纸抬头表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Service
public class BizReceiptPaperHeadDataWrap extends BaseDataWrap<BizReceiptPaperHeadMapper, BizReceiptPaperHead> {

    public void getPage(IPage<BizReceiptPaperPageVO> page, WmsQueryWrapper<BizReceiptPaperSearchPO> queryWrapper) {
        page.setRecords(this.baseMapper.getPage(page, queryWrapper));
    }
}
