package com.inossem.wms.bizdomain.transport.service.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelReceiptRelDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilCollection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * MQ公用类MessageQueue
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportMessageQueueComponent {

    @Autowired
    private BizLabelReceiptRelDataWrap bizLabelReceiptRelDataWrap;
    @Autowired
    private TransportMoveTypeComponent transportMoveTypeComponent;

    /**
     * 推送mq生成下架
     */
    public void generateOutputTaskReq(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 拼装下架请求
        List<BizReceiptTaskReqItemDTO> reqItemList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                BizReceiptTaskReqItemDTO reqItem = new BizReceiptTaskReqItemDTO();
                reqItem.setPreReceiptType(headDTO.getReceiptType());
                reqItem.setPreReceiptHeadId(headDTO.getId());
                reqItem.setPreReceiptItemId(itemDTO.getId());
                reqItem.setPreReceiptBinId(assembleDTO.getId());
                reqItem.setFtyId(itemDTO.getOutputFtyId());
                reqItem.setLocationId(itemDTO.getOutputLocationId());
                reqItem.setMatId(itemDTO.getOutputMatId());
                reqItem.setWhId(itemDTO.getOutputWhId());
                reqItem.setUnitId(itemDTO.getOutputUnitId());
                reqItem.setQty(assembleDTO.getQty());
                reqItem.setSpecCode(assembleDTO.getSpecCode());
                reqItem.setSpecValue(assembleDTO.getSpecValue());
                reqItem.setSpecStock(assembleDTO.getSpecStock());
                reqItem.setCreateUserId(headDTO.getArrangeUserId());
                reqItemList.add(reqItem);
            }
        }
        BizReceiptTaskReqSavePo po = new BizReceiptTaskReqSavePo();
        BizReceiptTaskReqHeadDTO reqHeadDTO = new BizReceiptTaskReqHeadDTO();
        reqHeadDTO.setItemList(reqItemList);
        reqHeadDTO.setDes(headDTO.getDes());
        reqHeadDTO.setCreateUserId(headDTO.getArrangeUserId());
        po.setStockTaskReqHeadInfo(reqHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, po);
        // 推送MQ生成下架请求
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.GENERATE_UNLOAD_REQ_TAGS, ctx));
    }

    /**
     * 推送mq生成上架
     */
    public void generateInputTaskReq(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 拼装上架请求
        List<BizReceiptTaskReqItemDTO> reqItemList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptTransportBinDTO binDTO : itemDTO.getBinDTOList()) {
                BizReceiptTaskReqItemDTO reqItem = new BizReceiptTaskReqItemDTO();
                reqItem.setPreReceiptType(headDTO.getReceiptType());
                reqItem.setPreReceiptHeadId(headDTO.getId());
                reqItem.setPreReceiptItemId(itemDTO.getId());
                reqItem.setPreReceiptBinId(binDTO.getId());
                reqItem.setFtyId(itemDTO.getInputFtyId());
                reqItem.setLocationId(itemDTO.getInputLocationId());
                reqItem.setMatId(itemDTO.getInputMatId());
                reqItem.setWhId(itemDTO.getInputWhId());
                reqItem.setUnitId(itemDTO.getInputUnitId());
                reqItem.setBatchId(binDTO.getInputBatchId());
                reqItem.setQty(binDTO.getQty());
                reqItem.setSpecStock(itemDTO.getInputSpecStockCode());
                reqItem.setCreateUserId(headDTO.getArrangeUserId());
                // 根据转储行项目获取下架的单品物料信息
                QueryWrapper<BizLabelReceiptRel> queryWrapper = new QueryWrapper();
                queryWrapper.lambda().eq(BizLabelReceiptRel::getReceiptItemId, binDTO.getTaskItemId());
                List<BizLabelReceiptRel> lableList = bizLabelReceiptRelDataWrap.list(queryWrapper);
                List<BizLabelReceiptRelDTO> lableListDTO =
                    UtilCollection.toList(lableList, BizLabelReceiptRelDTO.class);
                reqItem.setLabelReceiptRelDTOList(lableListDTO);
                reqItemList.add(reqItem);
            }
        }
        BizReceiptTaskReqSavePo po = new BizReceiptTaskReqSavePo();
        BizReceiptTaskReqHeadDTO reqHeadDTO = new BizReceiptTaskReqHeadDTO();
        reqHeadDTO.setItemList(reqItemList);
        if(headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_TRANSPORT.getValue())){
            reqHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_TASK_REQ_SHELF_LOAD.getValue());
        }else{
            reqHeadDTO.setReceiptType(EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue());
        }
        reqHeadDTO.setCreateUserId(headDTO.getArrangeUserId());
        reqHeadDTO.setDes(headDTO.getDes());
        po.setStockTaskReqHeadInfo(reqHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, po);
        // 推送MQ生成上架请求
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.GENERATE_LOAD_REQ_TAGS, ctx));
    }

    /**
     * 推送mq生成上架，基于assemble
     */
    public void generateInputTaskReqByAssemble(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 拼装上架请求
        List<BizReceiptTaskReqItemDTO> reqItemList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                BizReceiptTaskReqItemDTO reqItem = new BizReceiptTaskReqItemDTO();
                reqItem.setPreReceiptType(headDTO.getReceiptType());
                reqItem.setPreReceiptHeadId(headDTO.getId());
                reqItem.setPreReceiptItemId(itemDTO.getId());
                reqItem.setPreReceiptBinId(assembleDTO.getId());
                reqItem.setFtyId(itemDTO.getInputFtyId());
                reqItem.setLocationId(itemDTO.getInputLocationId());
                reqItem.setMatId(itemDTO.getInputMatId());
                reqItem.setWhId(itemDTO.getInputWhId());
                reqItem.setUnitId(itemDTO.getInputUnitId());
                reqItem.setBatchId(assembleDTO.getInputBatchId());
                reqItem.setQty(assembleDTO.getQty());
                reqItem.setSpecCode(assembleDTO.getSpecCode());
                reqItem.setSpecValue(assembleDTO.getSpecValue());
                reqItem.setSpecStock(headDTO.getSpecStock());
                reqItemList.add(reqItem);
            }
        }
        BizReceiptTaskReqSavePo po = new BizReceiptTaskReqSavePo();
        BizReceiptTaskReqHeadDTO reqHeadDTO = new BizReceiptTaskReqHeadDTO();
        reqHeadDTO.setReceiptType(EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue());
        reqHeadDTO.setItemList(reqItemList);
        reqHeadDTO.setDes(headDTO.getDes());
        po.setStockTaskReqHeadInfo(reqHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, po);
        // 推送MQ生成上架请求
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.GENERATE_LOAD_REQ_TAGS, ctx));
    }

}