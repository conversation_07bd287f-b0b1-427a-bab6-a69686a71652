package com.inossem.wms.bizdomain.returns.service.biz;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.inossem.wms.bizdomain.returns.service.component.ReturnComponent;
import com.inossem.wms.bizdomain.returns.service.component.SaleReturnComponent;
import com.inossem.wms.bizdomain.returns.service.component.callback.ReturnTaskCallbackComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;

/**
 * 销售退库
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-05-11
 */

@Service
public class SaleReturnService {

    @Autowired
    private SaleReturnComponent saleReturnComponent;

    @Autowired
    private ReturnComponent returnComponent;

    @Autowired
    private ReturnTaskCallbackComponent returnTaskCallbackComponent;

    /**
     * 销售退库-初始化
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"saleReturnComponent#setInit", "returnComponent#setExtendRelation",
        "returnComponent#setExtendAttachment", "returnComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 设置按钮
        saleReturnComponent.setInit(ctx);

        // 设置单据流
        returnComponent.setExtendRelation(ctx);

        // 开启附件
        returnComponent.setExtendAttachment(ctx);

        // 开启日志
        returnComponent.setExtendOperationLog(ctx);
    }

    /**
     * 分页获取退库列表
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"returnComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 获取退库单列表(分页)
        returnComponent.getPage(ctx);
    }

    /**
     * 查询销售订单行项目列表
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"saleReturnComponent#getSaleReceiptItemList"})
    public void getSaleReceiptItemList(BizContext ctx) {

        // 查询销售订单行项目列表
        saleReturnComponent.getSaleReceiptItemList(ctx);
    }

    /**
     * 查询出库单行项目列表
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"saleReturnComponent#getOutputReceiptItemList"})
    public void getOutputReceiptItemList(BizContext ctx) {

        // 查询出库单行项目列表
        saleReturnComponent.getOutputReceiptItemList(ctx);
    }

    /**
     * 获取退库单详情
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"saleReturnComponent#getInfo", "returnComponent#setBatchImg", "returnComponent#setSpecFeature",
        "returnComponent#setExtendRelation", "returnComponent#setExtendAttachment",
        "returnComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 获取详情
        saleReturnComponent.getInfo(ctx);

        // 设置批次图片信息
        returnComponent.setBatchImg(ctx);

        // 设置批次特性
        returnComponent.setSpecFeature(ctx);

        // 设置单据流
        returnComponent.setExtendRelation(ctx);

        // 开启附件
        returnComponent.setExtendAttachment(ctx);

        // 开启日志
        returnComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存单据
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Entrance(call = {"returnComponent#checkSave", "returnComponent#saveReceipt",
        "returnComponent#saveBizReceiptOperationLog", "returnComponent#saveBizReceiptAttachment",
        "returnComponent#saveReceiptTree"})
    public void save(BizContext ctx) {

        // 保存校验
        returnComponent.checkSave(ctx);

        // 保存单据
        returnComponent.saveReceipt(ctx);

        // 保存日志
        returnComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        returnComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        returnComponent.saveReceiptTree(ctx);
    }

    /**
     * 提交单据
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Entrance(call = {"saleReturnComponent#checkSubmit", "saleReturnComponent#submitReceipt",
        "returnComponent#saveBatchInfo", "returnComponent#saveBatchImg", "returnComponent#saveSpecFeature",
        "returnComponent#saveBizReceiptOperationLog", "returnComponent#saveBizReceiptAttachment",
        "returnComponent#saveReceiptTree", "returnComponent#saveLabelData", "returnComponent#generateLoadReq",
        "returnComponent#savePalletSorting", "saleReturnComponent#generatePostInsMoveTypeAndCheck",
        "returnComponent#postToSap", "returnComponent#postToInStock", "returnComponent#updateOutputReturnAQty",
        "returnComponent#updateStatusCompleted", "saleReturnComponent#generateInsMoveTypeAndCheckSameTime"})
    public void submit(BizContext ctx) {

        // 提交校验
        saleReturnComponent.checkSubmit(ctx);

        // 提交单据
        saleReturnComponent.submitReceipt(ctx);

        // 保存批次信息
        returnComponent.saveBatchInfo(ctx);

        // 保存批次图片
        returnComponent.saveBatchImg(ctx);

        // 保存批次特性
        returnComponent.saveSpecFeature(ctx);

        // 保存日志
        returnComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        returnComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        returnComponent.saveReceiptTree(ctx);

        // 生成标签 生成标签单据关系
        returnComponent.saveLabelData(ctx);

        // 获取过账移动类型并校验（非同时模式）
        saleReturnComponent.generatePostInsMoveTypeAndCheck(ctx);

        // sap过账
        returnComponent.postToSap(ctx);

        // InStock过账
        returnComponent.postToInStock(ctx);

        // 更新出库单已退库数量
        returnComponent.updateOutputReturnAQty(ctx);

        // 普通标签生成上架请求
        returnComponent.generateLoadReq(ctx);

        // 非普通标签生成码盘
        returnComponent.savePalletSorting(ctx);
    }

    /**
     * 获取配货信息
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"saleReturnComponent#getItemInfo"})
    public void getItemInfo(BizContext ctx) {

        // 获取配货信息
        saleReturnComponent.getItemInfo(ctx);
    }

    /**
     * 手动点击过账
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Entrance(call = {"returnComponent#checkPost", "saleReturnComponent#generatePostInsMoveTypeAndCheck",
        "returnComponent#postToSap", "returnComponent#postToInStock", "returnComponent#updateOutputReturnAQty",
        "returnComponent#updateStatusCompleted", "saleReturnComponent#generateInsMoveTypeAndCheckSameTime",
        "returnComponent#generateLoadReq", "returnComponent#savePalletSorting"})
    public void post(BizContext ctx) {

        // 再次过账，单据状态校验
        returnComponent.checkPost(ctx);

        // 获取过账移动类型并校验（非同时模式）
        saleReturnComponent.generatePostInsMoveTypeAndCheck(ctx);

        // sap过账
        returnComponent.postToSap(ctx);

        // InStock过账
        returnComponent.postToInStock(ctx);

        // 更新出库单已退库数量
        returnComponent.updateOutputReturnAQty(ctx);

        // 普通标签生成上架请求
        returnComponent.generateLoadReq(ctx);

        // 非普通标签生成码盘
        returnComponent.savePalletSorting(ctx);
    }

    /**
     * 冲销
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Entrance(call = {"returnComponent#checkWriteOff", "saleReturnComponent#generateWriteOffInsMoveTypeAndCheck",
        "returnComponent#writeOffToSap", "returnComponent#writeOffToInStock", "returnComponent#updateOutputReturnAQty",
        "returnComponent#updateTaskRequest", "saleReturnComponent#generateWriteOffInsMoveTypeAndCheckSameTime"})
    public void writeOff(BizContext ctx) {

        // 冲销校验
        returnComponent.checkWriteOff(ctx);

        // 获取冲销移动类型并校验（非同时模式）
        saleReturnComponent.generateWriteOffInsMoveTypeAndCheck(ctx);

        // sap冲销
        returnComponent.writeOffToSap(ctx);

        // InStock冲销
        returnComponent.writeOffToInStock(ctx);

        // 更新出库单已退库数量
        returnComponent.updateOutputReturnAQty(ctx);

        // 推送冲销修改请求
        returnComponent.updateTaskRequest(ctx);
    }

    /**
     * 删除单据
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Entrance(call = {"returnComponent#checkDelete", "returnComponent#deleteReceipt", "returnComponent#deleteBatchImg",
        "returnComponent#deleteBatchInfo", "returnComponent#deleteReceiptTree",
        "returnComponent#deleteBizReceiptAttachment", "returnComponent#cancelTaskRequest"})
    public void delete(BizContext ctx) {

        // 校验删除
        returnComponent.checkDelete(ctx);

        // 删除单据
        returnComponent.deleteReceipt(ctx);

        // 删除批次图片
        returnComponent.deleteBatchImg(ctx);

        // 删除批次信息
        returnComponent.deleteBatchInfo(ctx);

        // 逻辑逻辑单据流
        returnComponent.deleteReceiptTree(ctx);

        // 删除附件
        returnComponent.deleteBizReceiptAttachment(ctx);

        // 删除作业请求
        returnComponent.cancelTaskRequest(ctx);
    }

    /**
     * 上架回调
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"returnTaskCallbackComponent#updateQtyAndStatus", "returnComponent#isTask",
        "returnComponent#checkPost", "saleReturnComponent#generatePostInsMoveTypeAndCheck", "returnComponent#postToSap",
        "returnComponent#postToInStock", "returnComponent#updateOutputReturnAQty",
        "returnComponent#updateStatusCompleted"})
    @WmsMQListener(tags = TagConst.TASK_SALE_RETURN_CALLBACK)
    public void callbackByTask(BizContext ctx) {

        // 更新数量和状态
        returnTaskCallbackComponent.updateQtyAndStatus(ctx);
    }

    /**
     * 销售退库 -打印标签
     * 
     * @param ctx 入参上下文
     */
    @Entrance(call = {"returnComponent#print"})
    public void print(BizContext ctx) {

        // 打印标签
        returnComponent.print(ctx);
    }

}