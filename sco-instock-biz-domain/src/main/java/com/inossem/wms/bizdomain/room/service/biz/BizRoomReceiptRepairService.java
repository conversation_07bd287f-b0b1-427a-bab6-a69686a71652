package com.inossem.wms.bizdomain.room.service.biz;

import com.inossem.wms.bizdomain.room.service.component.BizRoomReceiptRepairComponent;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptRepairHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 住房报修服务类
 *
 **/
@Slf4j
@Service
public class BizRoomReceiptRepairService {

    @Autowired
    private BizRoomReceiptRepairComponent bizRoomReceiptRepairComponent;

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        bizRoomReceiptRepairComponent.init(ctx);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 分页查询
        bizRoomReceiptRepairComponent.getPage(ctx);
    }

    /**
     * 获取详情数据
     */
    public void getInfo(BizContext ctx) {
        bizRoomReceiptRepairComponent.getInfo(ctx);
    }

    /**
     * 保存单据
     */
    @Transactional
    public void saveReceipt(BizContext ctx) {
        // 保存校验
        bizRoomReceiptRepairComponent.checkSaveData(ctx);

        // 保存单据
        bizRoomReceiptRepairComponent.saveReceipt(ctx);
    }

    /**
     * 提交单据
     */
    @Transactional
    public void submitReceipt(BizContext ctx) {

        // 入参上下文
        BizRoomReceiptRepairHeadDTO po = ctx.getPoContextData();

        // 设置提交信息
        po.setSubmitUserId(ctx.getCurrentUserId());
        po.setSubmitTime(new Date());

        // 保存校验
        bizRoomReceiptRepairComponent.checkSaveData(ctx);

        // 提交校验
        bizRoomReceiptRepairComponent.checkSubmitData(ctx);

        // 保存单据
        bizRoomReceiptRepairComponent.saveReceipt(ctx);

        // 草稿状态的提交，更新状态为待处理
        if(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())){

            // 更新状态为待处理
            bizRoomReceiptRepairComponent.updateStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE);

        } else if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(po.getReceiptStatus())){

            // 待处理状态的提交，更新状态为已完成
            bizRoomReceiptRepairComponent.updateStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED);
        }
    }

    /**
     * 删除单据
     */
    @Transactional
    public void deleteReceipt(BizContext ctx) {
        // 删除单据
        bizRoomReceiptRepairComponent.deleteReceipt(ctx);
    }

}
