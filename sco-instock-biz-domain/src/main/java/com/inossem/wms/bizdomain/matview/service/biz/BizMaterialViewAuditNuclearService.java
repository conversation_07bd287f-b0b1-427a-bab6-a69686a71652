package com.inossem.wms.bizdomain.matview.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizdomain.matview.service.datawrap.BizMaterialViewAuditNuclearDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.input.vo.ToolsMaterialInfoVO;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditNuclearDTO;
import com.inossem.wms.common.model.bizdomain.matview.entity.BizMaterialViewAuditNuclear;
import com.inossem.wms.common.model.bizdomain.matview.po.BizMaterialViewAuditNuclearImportPO;
import com.inossem.wms.common.model.bizdomain.matview.vo.BizMaterialViewAuditNuclearExportVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 物料主数据视图审批-核电级别 服务接口
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Slf4j
@Service
public class BizMaterialViewAuditNuclearService {

    @Autowired
    private BizMaterialViewAuditNuclearDataWrap bizMaterialViewAuditNuclearDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private DataFillService dataFillService;

    /**
     * 保存核电级别的物料视图
     */
    public void save(List<BizMaterialViewAuditNuclearDTO> nuclearViewDTOList, Long headId,CurrentUser currentUser ) {
        QueryWrapper<BizMaterialViewAuditNuclear> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(BizMaterialViewAuditNuclear::getHeadId,headId);
        bizMaterialViewAuditNuclearDataWrap.remove(deleteWrapper);
        if (UtilCollection.isEmpty(nuclearViewDTOList)){
            return;
        }
        int rid = 1;
        for (BizMaterialViewAuditNuclearDTO dto : nuclearViewDTOList) {
            dto.setId(null);
            dto.setHeadId(headId);
            dto.setCreateTime(new Date());
            dto.setCreateUserId(currentUser.getId());
            dto.setModifyTime(new Date());
            dto.setModifyUserId(currentUser.getId());
            dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            dto.setRid(rid);
            rid++;
        }
        bizMaterialViewAuditNuclearDataWrap.saveBatchDto(nuclearViewDTOList);
    }

    /**
     * 根据单据id查询
     */
    public List<BizMaterialViewAuditNuclearDTO> getByHeadId(Long headId){
        QueryWrapper<BizMaterialViewAuditNuclear> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizMaterialViewAuditNuclear::getHeadId,headId);
        wrapper.lambda().orderByAsc(BizMaterialViewAuditNuclear::getRid);
        List<BizMaterialViewAuditNuclear> list = bizMaterialViewAuditNuclearDataWrap.list(wrapper);
        return UtilCollection.toList(list,BizMaterialViewAuditNuclearDTO.class);
    }

    /**
     * 根据单据id删除
     */
    public void removeByHeadId(Long headId){
        QueryWrapper<BizMaterialViewAuditNuclear> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(BizMaterialViewAuditNuclear::getHeadId,headId);
        deleteWrapper.lambda().orderByAsc(BizMaterialViewAuditNuclear::getRid);
        bizMaterialViewAuditNuclearDataWrap.remove(deleteWrapper);
    }

    /**
     * 核电级别物料数据校验
     */
    public void check(List<BizMaterialViewAuditNuclearDTO> nuclearViewDTOList) {
        //按物料码统计个数
        Map<String, Long> countMap = nuclearViewDTOList.stream()
                .filter(e -> UtilString.isNotNullOrEmpty(e.getMatCode()))
                .collect(Collectors.groupingBy(BizMaterialViewAuditNuclearDTO::getMatCode, Collectors.counting()));
        List<String> repeatCodeList = countMap.entrySet().stream().filter(entry -> entry.getValue() > 1L).map(Map.Entry::getKey).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(repeatCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODE_HAVE_BEEN_USED);
        }
        for (BizMaterialViewAuditNuclearDTO dto : nuclearViewDTOList) {
            this.checkItem(dto);
        }
    }

    private void checkItem(BizMaterialViewAuditNuclearDTO dto){
        if (UtilString.isNullOrEmpty(dto.getMatCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
        Long matId = dictionaryService.getMatIdByMatCode(dto.getMatCode());
        if (UtilNumber.isEmpty(matId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
//        if(UtilString.isNullOrEmpty(dto.getLifeTime())){
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "总货架寿命不能为空");
//        }
//        if(UtilString.isNullOrEmpty(dto.getLevel())){
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "质保等级");
//        }
//        if(UtilString.isNullOrEmpty(dto.getMinLeaveLifeTime())){
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "最小剩余货架寿命");
//        }

        dto.setMatId(matId);
    }

    /**
     * 更新状态为审批中
     */
    public void approving(Long headId) {
        UpdateWrapper<BizMaterialViewAuditNuclear> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizMaterialViewAuditNuclear::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue())
                .eq(BizMaterialViewAuditNuclear::getHeadId, headId);
        bizMaterialViewAuditNuclearDataWrap.update(wrapper);
    }

    /**
     * 更新状态为已拒绝
     */
    public void rejected(Long headId) {
        UpdateWrapper<BizMaterialViewAuditNuclear> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizMaterialViewAuditNuclear::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue())
                .eq(BizMaterialViewAuditNuclear::getHeadId, headId);
        bizMaterialViewAuditNuclearDataWrap.update(wrapper);
    }

    /**
     * 更新状态为已完成
     */
    public void completed(Long headId) {
        UpdateWrapper<BizMaterialViewAuditNuclear> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizMaterialViewAuditNuclear::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                .eq(BizMaterialViewAuditNuclear::getHeadId, headId);
        bizMaterialViewAuditNuclearDataWrap.update(wrapper);
    }

    /**
     * 核电物料导入
     */
    public List<BizMaterialViewAuditNuclearDTO> importNucMat(BizContext ctx) {
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        List<BizMaterialViewAuditNuclearImportPO> importPOList;
        try {
            //获取EXCEL数据
            importPOList = (List<BizMaterialViewAuditNuclearImportPO>) UtilExcel.readExcelData(file.getInputStream(), BizMaterialViewAuditNuclearImportPO.class, 1);
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
        if (UtilCollection.isEmpty(importPOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        }
        Map<String, Long> countMap = importPOList.stream()
                .filter(e -> UtilString.isNotNullOrEmpty(e.getMatCode()))
                .collect(Collectors.groupingBy(BizMaterialViewAuditNuclearImportPO::getMatCode, Collectors.counting()));
        List<String> repeatCodeList = countMap.entrySet().stream().filter(entry -> entry.getValue() > 1L).map(Map.Entry::getKey).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(repeatCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODE_HAVE_BEEN_USED);
        }
        List<BizMaterialViewAuditNuclearDTO> list = UtilCollection.toList(importPOList, BizMaterialViewAuditNuclearDTO.class);
        for (int i = 0; i < list.size(); i++) {
            BizMaterialViewAuditNuclearDTO dto = list.get(i);
            try {
                this.checkItem(dto);
            }catch (WmsException e){
                String arg = e.getArgs()[0];
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
            }
        }
        dataFillService.fillAttr(list);
        return list;
    }

    /**
     * 核电物料导出
     */
    public void exportNucMat(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(id)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<BizMaterialViewAuditNuclearDTO> list = this.getByHeadId(id);
        if (UtilCollection.isEmpty(list)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
        dataFillService.fillAttr(list);
        List<BizMaterialViewAuditNuclearExportVO> exportVOList = UtilCollection.toList(list, BizMaterialViewAuditNuclearExportVO.class);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("核电物料"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        UtilExcel.writeExcel(ToolsMaterialInfoVO.class, exportVOList, bizCommonFile);
        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }
}
