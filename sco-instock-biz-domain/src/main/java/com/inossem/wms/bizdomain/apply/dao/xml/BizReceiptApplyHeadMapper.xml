<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.apply.dao.BizReceiptApplyHeadMapper">

    <sql id="tableColumn">
        id,receipt_code,receipt_type,receipt_status,borrow_type,estimate_borrow_day,repair_fty,remark,is_delete,create_time,modify_time,create_user_id,modify_user_id,move_type_id,out_info_id,
        is_inner_flag,counterpart_dept_id,counterpart_office_id,receiver_id,dept_id,dept_office_id,apply_dept_name,des
    </sql>

    <select id="selectPageVOList" resultType="com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO">
        SELECT
        brah.id,
        brah.receipt_code,
        brah.receipt_type,
        brah.receipt_status,
        brah.borrow_type,
        brah.remark,
        brah.create_time,
        su.user_name createUserName
        FROM biz_receipt_apply_head brah
        JOIN biz_receipt_apply_item brai ON brah.id = brai.head_id AND brai.is_delete = 0
        <if test="po.toolCode != null and po.toolCode != ''">
            JOIN biz_batch_info bbi ON brai.batch_id = bbi.id AND bbi.is_delete = 0
        </if>
        LEFT JOIN sys_user su ON brah.create_user_id = su.id
        LEFT JOIN dic_material dm ON brai.mat_id = dm.id
        <if test="po.matReqReceiptCode != null and po.matReqReceiptCode != ''">
            LEFT JOIN biz_receipt_output_head broh ON brai.pre_receipt_head_id = broh.id AND broh.is_delete = 0
        </if>
        <if test="po.purchaseReceiptCode != null and po.purchaseReceiptCode != ''">
            LEFT JOIN erp_purchase_receipt_head eprh ON brai.pre_receipt_head_id = eprh.id
        </if>
        WHERE brah.is_delete = 0
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brah.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND brah.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND brah.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.toolCode != null and po.toolCode != ''">
            AND bbi.batch_code = #{po.toolCode}
        </if>
        <if test="po.matName != null and po.matName != ''">
            AND dm.mat_name LIKE CONCAT('%',#{po.matName},'%' ) AND dm.is_delete = 0
        </if>
        <if test="po.matCode != null and po.matCode != ''">
            AND dm.mat_code = #{po.matCode}
        </if>
        <if test="po.createUserName != null and po.createUserName != ''">
            AND su.user_name LIKE CONCAT('%',#{po.createUserName},'%' )
        </if>
        <if test="po.startTime !=null and po.endTime != null ">
            AND DATE_FORMAT(brah.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.startTime},'%Y-%m-%d') AND DATE_FORMAT(#{po.endTime},'%Y-%m-%d')
        </if>
        <if test="po.matReqReceiptCode != null and po.matReqReceiptCode != ''">
            AND brah.receipt_code = #{po.matReqReceiptCode}
        </if>
        <if test="po.remark != null and po.remark != ''">
            AND brah.remark LIKE CONCAT('%',#{po.remark},'%' )
        </if>
        <if test="po.purchaseReceiptCode != null and po.purchaseReceiptCode != ''">
            AND eprh.receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND brai.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP by brah.id
        ORDER BY brah.create_time DESC
    </select>

    <select id="selectPageVOListUnitized" resultType="com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO">
        SELECT
        brah.id,
        brah.receipt_code,
        brah.receipt_type,
        brah.receipt_status,
        brah.borrow_type,
        brah.remark,
        brah.create_time,
        su.user_name createUserName
        FROM biz_receipt_apply_head brah
        JOIN biz_receipt_apply_item brai ON brah.id = brai.head_id AND brai.is_delete = 0
        <if test="po.toolCode != null and po.toolCode != ''">
            JOIN biz_batch_info bbi ON brai.batch_id = bbi.id AND bbi.is_delete = 0
        </if>
        LEFT JOIN sys_user su ON brah.create_user_id = su.id
        LEFT JOIN dic_material dm ON brai.mat_id = dm.id
        LEFT JOIN  dic_material pdm on pdm.id = dm.parent_mat_id
        <if test="po.matReqReceiptCode != null and po.matReqReceiptCode != ''">
            LEFT JOIN biz_receipt_output_head broh ON brai.pre_receipt_head_id = broh.id AND broh.is_delete = 0
        </if>
        <if test="po.purchaseReceiptCode != null and po.purchaseReceiptCode != ''">
            LEFT JOIN erp_purchase_receipt_head eprh ON brai.pre_receipt_head_id = eprh.id
        </if>
        WHERE brah.is_delete = 0
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brah.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND brah.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND brah.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.toolCode != null and po.toolCode != ''">
            AND bbi.batch_code = #{po.toolCode}
        </if>
        <if test="po.matName != null and po.matName != ''">
            AND dm.mat_name LIKE CONCAT('%',#{po.matName},'%' ) AND dm.is_delete = 0
        </if>
        <if test="po.childMatCode != null and po.childMatCode != '' ">
            AND dm.mat_code = #{po.childMatCode}
        </if>
        <if test="po.matCode != null and po.matCode != '' ">
            AND pdm.mat_code = #{po.matCode}
        </if>
        <if test="po.createUserName != null and po.createUserName != ''">
            AND su.user_name LIKE CONCAT('%',#{po.createUserName},'%' )
        </if>
        <if test="po.startTime !=null and po.endTime != null ">
            AND DATE_FORMAT(brah.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.startTime},'%Y-%m-%d') AND DATE_FORMAT(#{po.endTime},'%Y-%m-%d')
        </if>
        <if test="po.matReqReceiptCode != null and po.matReqReceiptCode != ''">
            AND broh.receipt_code = #{po.matReqReceiptCode}
        </if>
        <if test="po.purchaseReceiptCode != null and po.purchaseReceiptCode != ''">
            AND eprh.receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND brai.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP by brah.id
        ORDER BY brah.create_time DESC
    </select>

    <select id="selectOutputPageVoList" resultType="com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO">
        SELECT
        head.*
        FROM
        biz_receipt_apply_head head
        LEFT JOIN biz_receipt_apply_item item on item.head_id = head.id
        LEFT JOIN dic_material dm ON dm.id = item.mat_id
        LEFT JOIN biz_batch_info bbi on bbi.id = item.batch_id and bbi.mat_id = item.mat_id
        LEFT join sys_user ON head.create_user_id = sys_user.id
        <where>
            head.is_delete = 0  and head.receipt_type = #{po.receiptType}
            AND head.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="po.receiptCode != null and po.receiptCode != '' ">
                AND head.receipt_code = #{po.receiptCode}
            </if>
            <if test="po.toolCode != null and po.toolCode != '' ">
                AND bbi.batch_code = #{po.toolCode}
            </if>
            <if test="po.createUserName != null and po.createUserName != '' ">
                AND sys_user.user_name = #{po.createUserName}
            </if>
            <if test="po.matName != null and po.matName != '' ">
                AND dm.mat_name like concat( '%',#{po.matName}, '%')
            </if>
            <if test="po.startTime != null and po.endTime != null">
                AND DATE(head.create_time)
                BETWEEN #{po.startTime, jdbcType=TIMESTAMP} AND DATE_ADD(#{po.endTime, jdbcType=TIMESTAMP},INTERVAL
                1 DAY)
            </if>
        </where>
        group by head.id
        order by head.create_time desc
    </select>
    <!--领料出库单申请分页查询-->
    <select id="selectApplyInfoPageVOList"
            resultType="com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO">
        SELECT
            `biz_receipt_apply_head`.`id`,
            `biz_receipt_apply_head`.`receipt_code`,
            `biz_receipt_apply_head`.`receipt_type`,
            `biz_receipt_apply_head`.`receipt_status`,
            `biz_receipt_apply_head`.`borrow_type`,
            `biz_receipt_apply_head`.`dept_id`,
            `biz_receipt_apply_head`.`estimate_borrow_day`,
            `biz_receipt_apply_head`.`repair_fty`,
            `biz_receipt_apply_head`.`des`,
            `biz_receipt_apply_head`.`remark`,
            `biz_receipt_apply_head`.`create_time`,
            `biz_receipt_apply_head`.`modify_time`,
            `biz_receipt_apply_head`.`create_user_id`,
            `biz_receipt_apply_head`.`modify_user_id`,
            `biz_receipt_apply_head`.`submit_time`,
            `biz_receipt_apply_head`.`submit_user_id`,
            `biz_receipt_apply_head`.`out_info_id`,
            `biz_receipt_apply_head`.`receive_type`,
            `biz_receipt_apply_head`.`create_dept_id`
        FROM
            `biz_receipt_apply_head`
            LEFT JOIN `biz_receipt_apply_item` ON `biz_receipt_apply_item`.`head_id` = `biz_receipt_apply_head`.`id`
            LEFT JOIN `biz_receipt_output_info` ON `biz_receipt_apply_head`.`out_info_id` = `biz_receipt_output_info`.`id`
                LEFT JOIN `sys_user` ON `sys_user`.`id` = `biz_receipt_apply_head`.`create_user_id`
                LEFT JOIN `dic_material` ON `dic_material`.`id` = `biz_receipt_apply_item`.`mat_id`
            ${ew.customSqlSegment}
        group by biz_receipt_apply_head.id
        ORDER BY `biz_receipt_apply_head`.`create_time` DESC
    </select>

    <!--领料出库单申请分页查询-->
    <select id="selectApplyInfoPageVOListUnitized"
            resultType="com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO">
        SELECT distinct
            `biz_receipt_apply_head`.`id`,
            `biz_receipt_apply_head`.`receipt_code`,
            `biz_receipt_apply_head`.`receipt_type`,
            `biz_receipt_apply_head`.`receipt_status`,
            `biz_receipt_apply_head`.`borrow_type`,
            `biz_receipt_apply_head`.`dept_id`,
            `biz_receipt_apply_head`.`estimate_borrow_day`,
            `biz_receipt_apply_head`.`repair_fty`,
            `biz_receipt_apply_head`.`remark`,
            `biz_receipt_apply_head`.`des`,
            `biz_receipt_apply_head`.`island`,
            `biz_receipt_apply_head`.`is_exact`,
            `biz_receipt_apply_head`.`is_split`,
            `biz_receipt_apply_head`.`create_time`,
            `biz_receipt_apply_head`.`modify_time`,
            `biz_receipt_apply_head`.`create_user_id`,
            `biz_receipt_apply_head`.`modify_user_id`,
            `biz_receipt_apply_head`.`submit_time`,
            `biz_receipt_apply_head`.`submit_user_id`,
            `biz_receipt_apply_head`.`used_dept_name`,
            `biz_receipt_apply_head`.`used_time`,
            `biz_receipt_apply_head`.`out_info_id`,
            biz_receipt_apply_bin.pre_apply_head_id,
            `biz_receipt_apply_head`.`create_dept_id`
        FROM
            `biz_receipt_apply_head`
            LEFT JOIN `biz_receipt_apply_item` ON `biz_receipt_apply_item`.`head_id` = `biz_receipt_apply_head`.`id`
            LEFT JOIN `biz_receipt_apply_bin` ON `biz_receipt_apply_bin`.`head_id` = `biz_receipt_apply_head`.`id`
            LEFT JOIN `biz_receipt_apply_head` pre ON `biz_receipt_apply_bin`.`pre_apply_head_id` = `pre`.`id`
            LEFT JOIN `biz_receipt_output_info` ON `biz_receipt_apply_head`.`out_info_id` = `biz_receipt_output_info`.`id`
            LEFT JOIN `biz_receipt_apply_transfer` ON `biz_receipt_apply_head`.`id` = `biz_receipt_apply_transfer`.`receipt_head_id`
            LEFT JOIN dic_material   ON biz_receipt_apply_transfer.mat_id = dic_material.id
            LEFT JOIN `sys_user` ON `sys_user`.`id` = `biz_receipt_apply_head`.`create_user_id`
            LEFT JOIN biz_receipt_require_item ON biz_receipt_require_item.id = biz_receipt_apply_item.pre_receipt_item_id
            LEFT JOIN biz_receipt_require_head ON biz_receipt_require_head.id = biz_receipt_apply_item.pre_receipt_head_id
            ${ew.customSqlSegment}
        group by biz_receipt_apply_head.id
        ORDER BY `biz_receipt_apply_head`.`create_time` DESC
    </select>

    <select id="findByIds" resultType="com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO">
        SELECT
        <include refid="tableColumn"></include>
        FROM biz_receipt_apply_head WHERE is_delete = 0 AND id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="updateStatusById">
        update biz_receipt_apply_head set receipt_status=#{receiptStatus} where id=#{id}
    </update>
</mapper>
