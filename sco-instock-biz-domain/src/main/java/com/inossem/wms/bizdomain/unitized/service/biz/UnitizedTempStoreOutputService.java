package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.bizdomain.output.service.component.callback.OutputTaskCallbackComponent;
import com.inossem.wms.bizdomain.unitized.service.component.UnitizedTempStoreOutputComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 成套设备 暂存出库service
 * <AUTHOR>
 */

@Service
public class UnitizedTempStoreOutputService {

    @Autowired
    private UnitizedTempStoreOutputComponent tempStoreOutputComponent;

    @Autowired
    private OutputComponent outputComponent;

    @Autowired
    private OutputTaskCallbackComponent outputTaskCallbackComponent;

    /**
     * 初始化出库单
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"tempStoreOutputComponent#setInit", "outputComponent#setExtendRelation",
        "outputComponent#setExtendAttachment", "outputComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 设置初始化信息
        tempStoreOutputComponent.setInit(ctx);

        // 设置单据流
        outputComponent.setExtendRelation(ctx);

        // 开启附件
        outputComponent.setExtendAttachment(ctx);

        // 开启日志
        outputComponent.setExtendOperationLog(ctx);
    }

    /**
     * 分页获取出库列表
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 获取出库单列表(分页)
        outputComponent.getPage(ctx);
    }

    /**
     * 获取出库单详情
     *
     * @param ctx 上下文
     */
    @Entrance(
        call = {"tempStoreOutputComponent#getInfo", "outputComponent#setBatchImg", "outputComponent#setExtendRelation",
            "outputComponent#setExtendAttachment", "outputComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 获取详情
        tempStoreOutputComponent.getInfo(ctx);

        // 设置批次图片信息
//        outputComponent.setBatchImg(ctx);

        // 设置单据流
        outputComponent.setExtendRelation(ctx);

        // 开启审批
        tempStoreOutputComponent.setExtendWf(ctx);

        // 开启附件
        outputComponent.setExtendAttachment(ctx);

        // 开启日志
        outputComponent.setExtendOperationLog(ctx);
    }

    /**
     * 获取出库单打印详情
     *
     * @param ctx 上下文
     */
    @Entrance(
        call = {"tempStoreOutputComponent#getInfo", "outputComponent#setBatchImg", "outputComponent#setExtendRelation",
            "outputComponent#setExtendAttachment", "outputComponent#setExtendOperationLog"})
    public void getPrintInfo(BizContext ctx) {

        // 获取详情
        tempStoreOutputComponent.getPrintInfo(ctx);

        // 设置批次图片信息
//        outputComponent.setBatchImg(ctx);

        // 开启审批
        tempStoreOutputComponent.setExtendWf(ctx);
    }

    /**
     * 保存单据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputComponent#checkSave", "outputComponent#saveReceipt", "outputComponent#saveReceiptSameTime",
        "outputComponent#saveLabelReceiptRel", "outputComponent#saveBizReceiptOperationLog",
        "outputComponent#saveBizReceiptAttachment", "tempStoreOutputComponent#saveReceiptTree",
        "outputComponent#trimZeroQtyBinList"})
    @Transactional(rollbackFor = Exception.class)
    //@WmsMQListener(tags = TagConst.GEN_PURCHASE_RETURN_STOCK)
    public void save(BizContext ctx) {

        // 保存校验
        tempStoreOutputComponent.check(ctx);

        // 保存单据【非同时模式】
        outputComponent.saveReceipt(ctx);

        // 保存日志
        outputComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        outputComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交单据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"tempStoreOutputComponent#checkSubmit", "outputComponent#submitReceipt",
        "outputComponent#submitReceiptSameTime", "outputComponent#occupyStock",
        "outputComponent#saveBizReceiptOperationLog", "outputComponent#saveBizReceiptAttachment",
        "tempStoreOutputComponent#saveReceiptTree", "tempStoreOutputComponent#generateInsMoveTypeAndCheck",
        "tempStoreOutputComponent#generateInsMoveTypeAndCheckSameTime", "outputComponent#postToSap",
        "outputComponent#postToInStock", "outputComponent#addTaskRequest", "outputComponent#updateStatusCompleted",
        "outputComponent#trimZeroQtyBinList", "tempStoreOutputComponent#checkSubmitSame",
        "outputComponent#updateStatusSubmitted"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交校验
        tempStoreOutputComponent.checkSubmit(ctx);

        // 提交单据【非同时模式】
        outputComponent.submitReceiptNew(ctx);

        // 保存签名
        tempStoreOutputComponent.saveAutograph(ctx);

        // 保存日志
        outputComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        outputComponent.saveBizReceiptAttachment(ctx);

        // 待出库状态提交生成下架单, 草稿状态提交发起审批
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_OUTPUT.getValue().equals(headDTO.getReceiptStatus())) {

            // 占用库存
            outputComponent.occupyStock(ctx);

            // 发送下架请求【非同时模式】
            outputComponent.addTaskRequest(ctx);

            // 单据状态 - 已提交
            outputComponent.updateStatus(headDTO, headDTO.getItemDTOList(), EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
        } else {

            //开启审批流
            tempStoreOutputComponent.startWorkFlow(ctx);
        }
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_UNITIZED_TS_OUT_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {

        // 审批回调
        tempStoreOutputComponent.approvalCallback(wfReceiptCo);
    }

    /**
     * 查询物料库存
     *
     * @param ctx-po 查询物料库存入参
     * @return 物料库存信息
     */
    public void getMatStock(BizContext ctx) {

        // 查询物料库存
        tempStoreOutputComponent.getMatFeatureStock(ctx);
    }

    /**
     * 获取配货信息
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputComponent#getItemInfoByFeatureStock", "outputComponent#getItemInfoByStockBin"})
    public void getItemInfo(BizContext ctx) {
        // 获取配货信息(特性库存)【非同时模式】
        this.getItemInfoNoSameTime(ctx);
    }

    /**
     * 非同时模式 获取配货信息
     * @param ctx
     */
    private void getItemInfoNoSameTime(BizContext ctx) {
        // 配置单据类型
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setReceiptType(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_OUTPUT.getValue());
        // 获取配货信息(特性库存)【非同时模式】
        tempStoreOutputComponent.getItemInfoByFeatureStock(ctx);
    }

    /**
     * 手动点击过账
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputComponent#checkPost", "tempStoreOutputComponent#generateInsMoveTypeAndCheck",
        "tempStoreOutputComponent#generateInsMoveTypeAndCheckSameTime", "outputComponent#postToSap",
        "outputComponent#postToInStock", "outputComponent#deleteOccupyStock", "outputComponent#updateStatusCompleted"})
    public void post(BizContext ctx) {

        // 再次过账，单据状态校验
        outputComponent.checkPost(ctx);

        // 获取过账移动类型并校验
        tempStoreOutputComponent.generateInsMoveTypeAndCheck(ctx);

        //43536 【第二版-暂存物项出库】过账失败
        //此单不过账
//        // sap过账
//        outputComponent.postToSap(ctx);

        //暂存出库凭证日期及出库单的凭证日期
        tempStoreOutputComponent.updateDocDate(ctx);

        // InStock过账
        outputComponent.postToInStock(ctx);

        // 过账后删除库存占用
        outputComponent.deleteOccupyStock(ctx);

        // 更新单据状态为已完成
        outputComponent.updateStatusCompleted(ctx);
    }

    /**
     * 冲销
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputComponent#checkWriteOff", "tempStoreOutputComponent#generateWriteOffInsMoveTypeAndCheck",
        "tempStoreOutputComponent#generateWriteOffInsMoveTypeAndCheckNonPostFirst", "outputComponent#writeOffToSap",
        "outputComponent#writeOffToInStock", "outputComponent#writeOffToInStockLable",
        "outputComponent#addWriteOffRequest"})
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 冲销校验
        outputComponent.checkWriteOff(ctx);

        // 生成冲销移动类型【非先过账模式】
        tempStoreOutputComponent.generateWriteOffInsMoveTypeAndCheckNonPostFirst(ctx);

        // sap冲销
      //  outputComponent.writeOffToSap(ctx);
        // 更新冲销过账时间
        outputComponent.updateWriteOffPostingDateAndIsWriteOff(ctx);

        // InStock冲销
        outputComponent.writeOffToInStock(ctx);

        // 冲销单品【先过账模式】
        outputComponent.writeOffToInStockLable(ctx);

        // 推送冲销修改请求
        outputComponent.addWriteOffRequest(ctx);
    }

    /**
     * 删除单据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"tempStoreOutputComponent#checkDelete", "outputComponent#checkTaskStatus",
        "outputComponent#deleteReceipt", "outputComponent#deleteReceiptSameTime", "outputComponent#deleteOccupyStock",
        "tempStoreOutputComponent#deleteReceiptTree", "outputComponent#deleteBizReceiptAttachment",
        "outputComponent#cancelTaskRequest"})
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 校验删除
        tempStoreOutputComponent.checkDelete(ctx);

        // 删除时校验作业状态
        outputComponent.checkTaskStatus(ctx);

        // 删除单据【非同时模式】
        outputComponent.deleteReceipt(ctx);

        // 删除库存占用
        outputComponent.deleteOccupyStock(ctx);

        // 逻辑删除单据流
        tempStoreOutputComponent.deleteReceiptTree(ctx);

        // 删除附件
        outputComponent.deleteBizReceiptAttachment(ctx);

        // 删除作业请求
        outputComponent.cancelTaskRequest(ctx);
    }

    /**
     * 下架回调
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputTaskCallbackComponent#updateQtyAndStatus", "outputComponent#isTask",
        "outputComponent#checkPost", "tempStoreOutputComponent#generateInsMoveTypeAndCheck", "outputComponent#postToSap",
        "outputComponent#postToInStock", "outputComponent#deleteOccupyStock", "outputComponent#updateStatusCompleted"})
    @WmsMQListener(tags = TagConst.UNITIZED_TEMP_STORE_OUTPUT)
    @Transactional(rollbackFor = Exception.class)
    public void callbackByTask(BizContext ctx) {

        // 更新数量和状态
        outputTaskCallbackComponent.updateQtyAndStatus(ctx);

        // 先作业模式
        if (outputComponent.isTask(ctx)) {

            // 校验
            outputComponent.checkPost(ctx);

            // 生成移动类型
            tempStoreOutputComponent.generateInsMoveTypeAndCheck(ctx);

            // InStock过账
            outputComponent.postToInStock(ctx);

            // 删除占用库存
            outputComponent.deleteOccupyStock(ctx);

            // 单据状态已完成
            outputComponent.updateStatusCompleted(ctx);
        }
    }

    /**
     * 过门回调
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputTaskCallbackComponent#updateFinishQtyAndStatus", "outputComponent#isTask",
        "outputComponent#checkPost", "tempStoreOutputComponent#generateInsMoveTypeAndCheck", "outputComponent#postToSap",
        "outputComponent#postToInStock", "outputComponent#deleteOccupyStock", "outputComponent#updateStatusCompleted"})
    @WmsMQListener(tags = TagConst.PASS_DOOR_PURCHASE_RETURN_CALLBACK)
    public void callbackByDoor(BizContext ctx) {

        // 更新已完成数量和状态
        outputTaskCallbackComponent.updateFinishQtyAndStatus(ctx);

        // 作业后删除库存占用
        outputComponent.deleteBatchOccupyStock(ctx);

        // 先作业模式
        if (outputComponent.isTask(ctx)) {

            // 校验
            outputComponent.checkPost(ctx);

            // 生成移动类型
            tempStoreOutputComponent.generateInsMoveTypeAndCheck(ctx);

            // sap过账
            outputComponent.postToSap(ctx);

            // InStock过账
            outputComponent.postToInStock(ctx);

            // 删除占用库存
            outputComponent.deleteOccupyStock(ctx);

            // 单据状态已完成
            outputComponent.updateStatusCompleted(ctx);
        }
    }

    /**
     * 获取参检人列表
     *
     * @param ctx-po 查询用户列表入参
     * @return 用户列表
     */
    public void getUserList(BizContext ctx) {

        // 获取参检人列表
        tempStoreOutputComponent.setUserList(ctx);
    }

}
