package com.inossem.wms.bizdomain.purchase.service.biz;

import com.inossem.wms.bizdomain.purchase.service.component.ContractChangeComponent;
import com.inossem.wms.bizdomain.purchase.service.component.DirectPurchaseComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class DirectPurchaseService {

    @Autowired
    private DirectPurchaseComponent directPurchaseComponent;


    // ==================== 基础操作方法 ====================

    /**
     * 获取预算分类预算科目列表
     */
    public void getEnumBudgetList(BizContext ctx) {
        directPurchaseComponent.getEnumBudgetList(ctx);
    }
    /**
     * 直接采购分页查询
     * 根据查询条件获取直接采购列表,支持分页和排序
     *
     * @param ctx 上下文,包含查询条件
     */
    @Entrance(call = {"directPurchaseComponent#getPurchaseApplyPageVo"})
    @In(parameter = "BizReceiptPurchaseApplySearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyListVO>")
    public void getPurchaseApplyPageVo(BizContext ctx) {
        directPurchaseComponent.getPurchaseApplyPageVo(ctx);
    }

    /**
     * 直接采购初始化
     * 创建新的直接采购时初始化基础数据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"directPurchaseComponent#init"})
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public void init(BizContext ctx) {
        directPurchaseComponent.init(ctx);
    }

    /**
     * 获取直接采购详情
     * 根据ID获取直接采购的详细信息,包括头表、行项目、附件等
     *
     * @param ctx 上下文,包含直接采购ID
     */
    @Entrance(call = {"directPurchaseComponent#getPurchaseApplyDetail"})
    @In(parameter = "id", required = {"id"})
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public void getPurchaseApplyDetail(BizContext ctx) {
        directPurchaseComponent.getPurchaseApplyDetail(ctx);
        directPurchaseComponent.setInfoExtendRelation(ctx);
        directPurchaseComponent.setExtendWf1(ctx);
    }

    /**
     * 保存直接采购
     * 新增或更新直接采购信息,包括:
     * - 保存头表和行项目
     * - 保存操作日志
     * - 保存附件
     *
     * @param ctx 上下文,包含直接采购信息
     */
    @Entrance(call = {
            "directPurchaseComponent#checkSave",
            "directPurchaseComponent#save",
            "directPurchaseComponent#saveBizReceiptOperationLog",
            "directPurchaseComponent#saveBizReceiptAttachment",
            "directPurchaseComponent#saveReceiptTree"
    })
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"receiptType", "applyUserId", "applyDeptId", "planArrivalDate"})
    @Out(parameter = "String")
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        // 保存前校验
        directPurchaseComponent.checkSave(ctx);

        // 保存直接采购单
        directPurchaseComponent.save(ctx);

        // 保存操作日志
        directPurchaseComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        directPurchaseComponent.saveBizReceiptAttachment(ctx);

    }

    /**
     * 提交直接采购
     * 保存并提交直接采购进入审批流程,包括:
     * - 校验数据
     * - 保存直接采购
     * - 保存相关信息(日志、附件等)
     * - 发起审批流程
     *
     * @param ctx 上下文,包含直接采购信息
     */
    @Entrance(call = {
            "directPurchaseComponent#checkSubmit",
            "directPurchaseComponent#submit",
            "directPurchaseComponent#saveBizReceiptOperationLog",
            "directPurchaseComponent#saveBizReceiptAttachment",
            "directPurchaseComponent#saveReceiptTree",
            "directPurchaseComponent#startWorkFlow"
    })
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交前校验
        directPurchaseComponent.checkSubmit(ctx);

        // 提交单据
        directPurchaseComponent.submit(ctx);

        // 保存操作日志
        directPurchaseComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        directPurchaseComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        directPurchaseComponent.saveReceiptTree(ctx);

        // 开启审批
        directPurchaseComponent.startWorkFlow(ctx);

    }

    /**
     * 直接采购过账srm
     *
     * @param ctx 上下文,包含直接采购信息
     */
    @Entrance(call = {
            "directPurchaseComponent#saveBizReceiptOperationLog",
            "directPurchaseComponent#pullSrm",
    })
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public void post(BizContext ctx) {

        // 设置上下文单据日志 - 过账
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_POSTING);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO));

        // 保存操作日志
        directPurchaseComponent.saveBizReceiptOperationLog(ctx);

        // 同步srm
        directPurchaseComponent.pullSrm(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_DIRECT_PURCHASE)
//    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        directPurchaseComponent.approvalCallback(wfReceiptCo);

    }

    /**
     * 删除直接采购
     * 删除直接采购及相关信息,包括:
     * - 删除头表和行项目
     * - 删除单据流
     * - 删除附件
     *
     * @param ctx 上下文,包含直接采购ID
     */
    @Entrance(call = {
            "directPurchaseComponent#checkDelete",
            "directPurchaseComponent#delete",
            "directPurchaseComponent#deleteReceiptTree",
            "directPurchaseComponent#deleteReceiptAttachment"
    })
    @In(parameter = "id", required = {"id"})
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        // 删除前校验
        directPurchaseComponent.checkDelete(ctx);

        // 删除直接采购单
        directPurchaseComponent.delete(ctx);

        // 删除单据流
        directPurchaseComponent.deleteReceiptTree(ctx);

        // 删除单据附件
        directPurchaseComponent.deleteReceiptAttachment(ctx);
    }

    /**
     * 取消直接采购
     * 取消已提交的直接采购
     *
     * @param ctx 上下文,包含直接采购信息
     */
    @Entrance(call = {"directPurchaseComponent#checkCancel", "directPurchaseComponent#cancel"})
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    @Transactional(rollbackFor = Exception.class)
    public void cancel(BizContext ctx) {
        // 取消直接采购行项目
        directPurchaseComponent.cancelItems(ctx);
    }

    /**
     * 获取需求计划行项目列表
     * 获取待整合的需求计划行项目,并转换为直接采购行项目DTO
     *
     * @param ctx 上下文,包含查询条件
     */
    @Entrance(call = {"directPurchaseComponent#getDemandPlanItems"})
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyItemDTO>")
    public void getDemandPlanItems(BizContext ctx) {
        directPurchaseComponent.getDemandPlanItems(ctx);
    }



    @Transactional(rollbackFor = Exception.class)
    public BizCommonFile upload(MultipartFile fileInClient, CurrentUser user) {
        return directPurchaseComponent.upload(fileInClient, user);
    }


    public void revoke(BizContext ctx) {
        directPurchaseComponent.revoke(ctx);
    }
}
