package com.inossem.wms.bizdomain.room.service.datawrap;

import com.inossem.wms.common.model.bizdomain.room.entity.BizRoomReceiptCheckOutRoom;
import com.inossem.wms.bizdomain.room.dao.BizRoomReceiptCheckOutRoomMapper;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 住房退订单房间表，当房间人员全部退房后，房间自动退订，生成此表数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Service
public class BizRoomReceiptCheckOutRoomDataWrap extends BaseDataWrap<BizRoomReceiptCheckOutRoomMapper, BizRoomReceiptCheckOutRoom> {

}
