package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.input.service.component.inputbase.InputComponent;
import com.inossem.wms.bizdomain.unitized.service.component.UnitizedInspectInputWriteOffComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 成套设备入库冲销
 * </p>
 */

@Service
@Slf4j
public class UnitizedInspectInputWriteOffService {

    @Autowired
    private UnitizedInspectInputWriteOffComponent inputWriteOffComponent;

    @Autowired
    private InputComponent inputComponent;

    /**
     * 成套设备入库冲销-初始化
     *
     * @param ctx 入参上下文
     */
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置成套设备入库冲销【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交、保存】
        // 3、设置扩展功能【单据流】
        inputWriteOffComponent.setInit(ctx);

        // 开启附件
        inputComponent.setExtendAttachment(ctx);

        // 开启操作日志
        inputComponent.setExtendOperationLog(ctx);
    }

    /**
     * 成套设备入库冲销-分页
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"inspectInputComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 成套设备入库冲销单-分页
        inputWriteOffComponent.getPage(ctx);
    }

    /**
     * 成套设备入库冲销-详情
     *
     * @param ctx 入参上下文 {"id":"成套设备入库冲销主键"}
     */
    public void getInfo(BizContext ctx) {

        // 成套设备入库冲销单详情
        inputWriteOffComponent.getInfo(ctx);

        // 设置批次图片信息
        inputComponent.setBatchImg(ctx);

        // 设置详情页单据流
        inputComponent.setInfoExtendRelation(ctx);

        // 开启附件
        inputComponent.setExtendAttachment(ctx);

        // 开启操作日志
        inputComponent.setExtendOperationLog(ctx);
        
        // 需求组角色隐藏打印按钮外的其他按钮
        inputComponent.setButtonHidden(ctx);
    }

    /**
     * 成套设备入库冲销-查看验收单
     *
     * @param ctx 入参上下文 {"id":"成套设备入库冲销主键"}
     */
    public void getInsepctInput(BizContext ctx) {

        // 成套设备入库冲销单详情
        inputWriteOffComponent.getInsepctInput(ctx);
    }

    public void fillEntity(BizContext ctx) {

        // 成套设备入库冲销单详情
        inputWriteOffComponent.fillEntity(ctx);
    }

    /**
     * 成套设备入库冲销-保存
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存成套设备入库冲销前校验
        inputWriteOffComponent.checkSaveInspectInput(ctx);

        // 保存入库单
        inputWriteOffComponent.saveInput(ctx);

        // 保存批次图片
        inputComponent.saveBizBatchImg(ctx);

        // 保存附件
        inputComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        inputComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 成套设备入库冲销-提交
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交成套设备入库冲销单前校验
        inputWriteOffComponent.checkSubmitInspectInput(ctx);

        // 提交成套设备入库冲销单
        inputWriteOffComponent.submitInspectInput(ctx);

        // 保存批次图片
        inputComponent.saveBizBatchImg(ctx);

        // 保存附件
        inputComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        inputComponent.saveBizReceiptOperationLog(ctx);

//        // 单据过账特殊处理
//        inputWriteOffComponent.handleInputReceiptPost(ctx);

        this.writeOff(ctx);
    }

    /**
     * 成套设备入库冲销-过账
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {

        // 成套设备入库冲销冲销前校验
        inputWriteOffComponent.checkInspectInputWriteOff(ctx);

        // 单据冲销特殊处理
        inputWriteOffComponent.handleInputReceiptWriteOff(ctx);

    }

    /**
     * 成套设备入库冲销-冲销
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 成套设备入库冲销冲销前校验
        inputWriteOffComponent.checkInspectInputWriteOff(ctx);

        // 单据冲销特殊处理
        inputWriteOffComponent.handleInputReceiptWriteOff(ctx);

    }

    /**
     * 成套设备入库冲销单-删除
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 删除校验
        inputWriteOffComponent.checkDeleteInspectInput(ctx);

        // 删除成套设备入库冲销单
        inputWriteOffComponent.deleteInspectInput(ctx);

        // 删除入库单单据流
        inputComponent.deleteReceiptTree(ctx);

        // 删除入库单单据附件
        inputComponent.deleteReceiptAttachment(ctx);

        // 删除作业请求
        inputComponent.cancelTaskRequest(ctx);
    }

    /**
     * 根据采购验收单生成入库单
     *
     * @param ctx 入参上下文
     */
    //@WmsMQListener(tags = TagConst.GEN_INSPECT_INPUT_STOCK)
    @Transactional(rollbackFor = Exception.class)
    public void genInspectInput(BizContext ctx) {

        // 采购验收-生成成套设备入库冲销单
        inputWriteOffComponent.genInspectInput(ctx);

        // 生成ins凭证
        //inspectInputComponent.generateInsDocToPost(ctx);
        // 生成ins凭证 质检
        //inspectInputComponent.generateInspectInsDocToPost(ctx);

        // sap入库过账
        //inputComponent.postInputToSap(ctx);

        // ins入库过账
        //inputComponent.postInputToIns(ctx);

        // 普通标签生成上架请求
        //inputComponent.generateLoadReq(ctx);
    }

    /**
     *  成套设备入库冲销单-物料标签打印-PDA
     * @param ctx
     */
    public void boxApplyLabelPrint(BizContext ctx) {
        // 打印物料标签校验
        inputWriteOffComponent.checkPrint(ctx);
        // 填充打印数据
        inputWriteOffComponent.fillPrintData(ctx);
    }

    /**
     * 成套设备入库冲销-详情
     *
     * @param ctx 入参上下文 {"id":"成套设备入库冲销主键"}
     */
    public void getPrintInfo(BizContext ctx) {
        // 成套设备入库冲销单详情
        inputWriteOffComponent.getPrintInfo(ctx);
        // 开启操作日志
        inputComponent.setExtendOperationLog(ctx);
    }

    public void distribution(BizContext ctx) {
        // 成套设备入库冲销单详情
        inputWriteOffComponent.distribution(ctx);
    }
}