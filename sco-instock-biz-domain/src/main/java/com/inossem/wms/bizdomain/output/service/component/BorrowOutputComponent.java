package com.inossem.wms.bizdomain.output.service.component;

//import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockOccupyService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsBorrowOutputMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputBinDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.apply.EnumBorrowType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputBin;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypePostTaskDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 借用出库 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-08
 */
@Service
public class BorrowOutputComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected BatchInfoService batchInfoService;

    @Autowired
    protected StockOccupyService stockOccupyService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected OutputComponent outputComponent;

    @Autowired
    protected InsBorrowOutputMoveTypeComponent insBorrowOutputMoveTypeComponent;

    @Autowired
    protected BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    protected BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;

    @Autowired
    protected BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;

    @Autowired
    protected BizReceiptOutputBinDataWrap bizReceiptOutputBinDataWrap;

    @Autowired
    protected BizReceiptApplyHeadDataWrap applyHeadDataWrap;

    @Autowired
    protected BizReceiptApplyItemDataWrap applyItemDataWrap;

    /**
     * 查询出库单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptOutputQueryListPO :"出库单分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptOutputQueryListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (UtilCollection.isEmpty(currentUser.getLocationList())) {
            return;
        }
        List locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        po.setLocationIdList(locationIds);

        // 分页查询处理
        IPage<BizReceiptOutputPageVO> page = po.getPageObj(BizReceiptOutputPageVO.class);
        bizReceiptOutputHeadDataWrap.getBorrowOutputPageVoList(page, po);
        // 非已完成状态单据去掉操作人
        page.getRecords().forEach(p -> {
            if(!p.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
                p.setModifyUserName(null);
            }
        });
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 查询借用出库单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"借用出库单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取借用出库单
        BizReceiptOutputHeadDTO headDTO = UtilBean.newInstance(bizReceiptOutputHeadDataWrap.getById(headId), BizReceiptOutputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 属性填充
        headDTO.setBorrowType(headDTO.getItemDTOList().get(0).getBorrowType())
                .setDeptCode(headDTO.getItemDTOList().get(0).getDeptCode())
                .setDeptName(headDTO.getItemDTOList().get(0).getDeptName())
                .setDeptOfficeCode(headDTO.getItemDTOList().get(0).getDeptOfficeCode())
                .setDeptOfficeName(headDTO.getItemDTOList().get(0).getDeptOfficeName())
                .setEstimateBorrowDay(headDTO.getItemDTOList().get(0).getEstimateBorrowDay());
        headDTO.getItemDTOList().forEach(
                p -> p.setToolCode(CollectionUtils.isEmpty(p.getBinDTOList()) ? null : p.getBinDTOList().get(0).getBatchInfo().getBatchCode())
                        .setToolStatus(CollectionUtils.isEmpty(p.getBinDTOList()) ? null : p.getBinDTOList().get(0).getBatchInfo().getToolStatus())
                        .setToolTypeId(CollectionUtils.isEmpty(p.getBinDTOList()) ? null : p.getBinDTOList().get(0).getBatchInfo().getToolTypeId())
                        .setToolTypeName(CollectionUtils.isEmpty(p.getBinDTOList()) ? null : p.getBinDTOList().get(0).getBatchInfo().getToolTypeName())
                        .setToolManageStatusRemark(CollectionUtils.isEmpty(p.getBinDTOList()) ? null : p.getBinDTOList().get(0).getBatchInfo().getToolManageStatusRemark())
                        .setOutFtyCode(CollectionUtils.isEmpty(p.getBinDTOList()) ? null : p.getBinDTOList().get(0).getBatchInfo().getOutFtyCode())
                        .setBinCode(CollectionUtils.isEmpty(p.getBinDTOList()) ? null : p.getBinDTOList().get(0).getBinCode()));
        // web打印配置规格型号
        headDTO.getItemDTOList().forEach(item -> {
            item.setFormatCode(CollectionUtils.isEmpty(item.getBinDTOList()) ? null : item.getBinDTOList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().getFormatCode());
        });
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置借用出库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 出库单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptOutputHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除、打印】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true).setButtonPrint(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 工器具 借用出库 保存单据
     *
     * @param ctx 上下文
     */
    public void saveReceipt(BizContext ctx) {
        boolean isNew = true;
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptOutputHead bizReceiptOutputHead = null;
        // head处理
        if (UtilNumber.isNotEmpty(id)) {
            bizReceiptOutputHead = bizReceiptOutputHeadDataWrap.getById(id);
            // 根据id更新
            headDTO.setModifyUserId(createUserId);
            bizReceiptOutputHeadDataWrap.updateDtoById(headDTO);
            // item物理删除
            QueryWrapper<BizReceiptOutputItem> queryWrapperItem = new QueryWrapper<>();
            queryWrapperItem.lambda().eq(BizReceiptOutputItem::getHeadId, id);
            bizReceiptOutputItemDataWrap.physicalDelete(queryWrapperItem);
            // assemble物理删除
            QueryWrapper<BizReceiptAssemble> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizReceiptAssemble::getReceiptHeadId, id);
            bizReceiptAssembleDataWrap.physicalDelete(queryWrapperAssemble);
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(createUserId);
            headDTO.setModifyUserId(createUserId);
            headDTO.setReceiptType(headDTO.getReceiptType());
            headDTO.setReceiptStatus(status);
            bizReceiptOutputHeadDataWrap.saveDto(headDTO);
            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptOutputHead.getReceiptStatus();
            createTime = bizReceiptOutputHead.getCreateTime();
            createUserId = bizReceiptOutputHead.getCreateUserId();
        }
        // item处理
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptOutputItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(status);
            itemDto.setCreateTime(createTime);
            itemDto.setCreateUserId(createUserId);
            itemDto.setModifyUserId(user.getId());
            itemDto.setQty(this.getItemOperatedQty(itemDto));
            if (EnumReceiptType.STOCK_OUTPUT_OTHER.getValue().equals(headDTO.getReceiptType())) {
                itemDto.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_OTHER_INPUT.getValue());
            }
            if (EnumReceiptType.STOCK_OUTPUT_WASTE_MAT.getValue().equals(headDTO.getReceiptType())) {
                itemDto.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_WASTER.getValue());
            }
            if (EnumReceiptType.STOCK_TEMP_MAT_OUTPUT.getValue().equals(headDTO.getReceiptType())) {
                itemDto.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TEMP_STORE.getValue());
            }
        }
        bizReceiptOutputItemDataWrap.saveBatchDto(itemDTOList);
        // assemble处理
        List<BizReceiptAssembleDTO> assembleList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                assembleDTO.setId(null);
                assembleDTO.setReceiptHeadId(headDTO.getId());
                assembleDTO.setReceiptItemId(itemDto.getId());
                assembleDTO.setReceiptType(headDTO.getReceiptType());
                assembleDTO.setCreateUserId(createUserId);
                assembleDTO.setModifyUserId(user.getId());
                assembleDTO.setSpecType(assembleDTO.getSpecType() == null
                        ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue() : assembleDTO.getSpecType());
                assembleList.add(assembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleList);

        /* ********************** 工器具借用出库 处理批次信息，将工器具使用人、使用地点、使用事由写入批次信息 *************************/
        if (EnumReceiptType.STOCK_OUTPUT_BORROW.getValue().equals(headDTO.getReceiptType())) {
            List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
            for (BizReceiptOutputItemDTO itemDTO : headDTO.getItemDTOList()) {
                BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                batchInfoDTO.setId(itemDTO.getBatchInfo() != null ? itemDTO.getBatchInfo().getId() : itemDTO.getBatchId());
                batchInfoDTO.setToolUserName(itemDTO.getToolUserName());
                batchInfoDTO.setToolUsePlace(itemDTO.getToolUsePlace());
                batchInfoDTO.setToolUseReason(itemDTO.getToolUseReason());
                bizBatchInfoDTOList.add(batchInfoDTO);
            }
            batchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
        }


        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 获取行项目出库数
     *
     * @param itemDTO 行项目
     * @return 出库数量
     */
    public BigDecimal getItemOperatedQty(BizReceiptOutputItemDTO itemDTO) {
        BigDecimal operatedQty = BigDecimal.ZERO;
        if (Objects.nonNull(itemDTO) && itemDTO.getAssembleDTOList() != null) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                if (assembleDTO.getQty() != null && assembleDTO.getQty().compareTo(BigDecimal.ZERO) > 0) {
                    operatedQty = operatedQty.add(assembleDTO.getQty());
                }
            }
        }
        return operatedQty;
    }

    /**
     * 出库单bin表处理
     *
     * @param ctx MQ入参上下文
     */
    public void saveReceiptBin(BizContext ctx) {
        // MQ入参上下文 - 借用出库单
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // MQ入参上下文 - 当前登录人信息
        CurrentUser cUser = ctx.getCurrentUser();
        List<BizReceiptOutputBinDTO> binDTOList = new ArrayList<>();
        for(BizReceiptOutputItemDTO itemDTO : headDTO.getItemDTOList()) {
            int bid = 1;
            for(BizReceiptOutputBinDTO binDTO : itemDTO.getBinDTOList()) {
                binDTO.setId(null);
                binDTO.setHeadId(headDTO.getId());
                binDTO.setItemId(itemDTO.getId());
                binDTO.setBid(Integer.toString(bid++));
                binDTO.setCreateTime(headDTO.getCreateTime());
                binDTO.setCreateUserId(headDTO.getCreateUserId());
                binDTO.setModifyUserId(cUser.getId());
                binDTOList.add(binDTO);
            }
        }
        QueryWrapper<BizReceiptOutputBin> queryWrapperbin = new QueryWrapper<>();
        queryWrapperbin.lambda().eq(BizReceiptOutputBin::getHeadId, headDTO.getId());
        bizReceiptOutputBinDataWrap.physicalDelete(queryWrapperbin);
        bizReceiptOutputBinDataWrap.saveBatchDto(binDTOList);
    }

    /**
     * 保存单据流
     *
     * @param ctx MQ入参上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        // MQ入参上下文 - 借用出库单
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptOutputItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(EnumReceiptType.STOCK_OUTPUT_BORROW.getValue());
            bizCommonReceiptRelation.setReceiptHeadId(headDTO.getId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.BORROW_APPLY.getValue());
            bizCommonReceiptRelation.setPreReceiptHeadId(item.getPreReceiptHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     * 获取过账移动类型并校验
     *
     * @in ctx 入参 {@link BizReceiptOutputHeadDTO : "出库单"}
     *
     */
    public void generateInsMoveTypeAndCheck(BizContext ctx) {
        // 入参上下文
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = new StockInsMoveTypePostTaskDTO();
        StockInsMoveTypeDTO postingInsMoveTypeDTO;
        try {
            postingInsMoveTypeDTO = insBorrowOutputMoveTypeComponent.generatePostInsDoc(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeDTO);
        // 过账凭证(处理仓位库存)
        stockInsMoveTypePostTaskDTO.setPostDTO(postingInsMoveTypeDTO);
        // 根据物料凭证计算库存是否可以正常修改
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        // ins凭证放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    /**
     * 删除占用库存
     *
     * @in ctx 入参 {@link BizReceiptOutputHeadDTO : "出库单"}
     */
    public void deleteOccupyStock(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        stockOccupyService.deleteByReceipt(headDTO.getItemDTOList().get(0).getPreReceiptType(), headDTO.getItemDTOList().get(0).getPreReceiptHeadId(), null, null);
    }

    /**
     * 更新批次借用部门和借用科室
     *
     * @in ctx 入参 {@link BizReceiptOutputHeadDTO : "借用出库单"}
     */
    public void updateBatchDeptAndOffice(BizContext ctx) {
        // 入参上下文
        BizReceiptOutputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizBatchInfoDTO> batchInfoDTOList = new ArrayList<>();
        po.getItemDTOList().forEach(p -> {
            p.getBinDTOList().forEach(q -> {
                q.getBatchInfo().setBorrowUserId(po.getModifyUserId()).setBorrowDeptId(p.getDeptId()).setBorrowDeptOfficeId(p.getDeptOfficeId()).setBorrowType(po.getBorrowType());
                batchInfoDTOList.add(q.getBatchInfo());
            });
        });
        batchInfoService.multiUpdateBatchInfo(batchInfoDTOList);
    }

    /**
     * 生成工具归还单
     *
     * @in ctx 入参 {@link BizReceiptOutputHeadDTO : "借用出库单"}
     */
    public void genBorrowInput(BizContext ctx) {
        // 入参上下文
        BizReceiptOutputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 业务要求短期借用类型，自动生成草稿状态归还单，长期借用手动创建归还单
        if(po.getBorrowType().equals(EnumBorrowType.SHORT_TERM_BORROW_RECEIPT.getValue())) {
            // 组装参数
            BizReceiptInputHeadDTO headDTO = new BizReceiptInputHeadDTO();
            List<BizReceiptInputItemDTO> itemDTOList = new ArrayList<>();
            headDTO.setReceiptType(EnumReceiptType.STOCK_INPUT_BORROW.getValue()).setCreateUserId(po.getCreateUserId());
            for (BizReceiptOutputItemDTO itemDTO : po.getItemDTOList()) {
                BizReceiptInputItemDTO borrowItemDTO = UtilBean.newInstance(itemDTO, BizReceiptInputItemDTO.class);
                borrowItemDTO.setBatchId(itemDTO.getBinDTOList().get(0).getBatchId());
                borrowItemDTO.setPreReceiptHeadId(po.getId());
                borrowItemDTO.setPreReceiptItemId(itemDTO.getId());
                borrowItemDTO.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_BORROW.getValue());
                borrowItemDTO.setPreReceiptQty(itemDTO.getQty());
                borrowItemDTO.setReferReceiptType(EnumReceiptType.BORROW_APPLY.getValue());
                // 归还默认出库仓位
                borrowItemDTO.setTypeId(itemDTO.getBinDTOList().get(0).getTypeId());
                borrowItemDTO.setBinId(itemDTO.getBinDTOList().get(0).getBinId());
                itemDTOList.add(borrowItemDTO);
            }
            headDTO.setItemList(itemDTOList);
            // 设置入参上下文
            BizContext ctxIntput = new BizContext();
            ctxIntput.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxIntput.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成工具归还单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_BORROW_INPUT_STOCK, ctxIntput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 借用出库-删除
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "借用出库单号"}
     */
    public void deleteInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置借用出库单
        BizReceiptOutputHeadDTO headDTO = UtilBean.newInstance(bizReceiptOutputHeadDataWrap.getById(headId), BizReceiptOutputHeadDTO.class);
        QueryWrapper<BizReceiptOutputItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda().eq(BizReceiptOutputItem::getHeadId, headId);
        headDTO.setItemDTOList(UtilCollection.toList(bizReceiptOutputItemDataWrap.list(itemQueryWrapper), BizReceiptOutputItemDTO.class));
        // 逻辑删除抬头表
        bizReceiptOutputHeadDataWrap.removeById(headId);
        // 逻辑删除行项目表
        QueryWrapper<BizReceiptOutputItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptOutputItem::getHeadId, headId);
        bizReceiptOutputItemDataWrap.remove(itemWrapper);
        // 逻辑删除配货信息表
        QueryWrapper<BizReceiptOutputBin> binWrapper = new QueryWrapper<>();
        binWrapper.lambda().eq(BizReceiptOutputBin::getHeadId, headId);
        bizReceiptOutputBinDataWrap.remove(binWrapper);
        // 逻辑删除assemble表
        QueryWrapper<BizReceiptAssemble> assembleWrapper = new QueryWrapper<>();
        assembleWrapper.lambda().eq(BizReceiptAssemble::getReceiptHeadId, headId);
        bizReceiptAssembleDataWrap.remove(assembleWrapper);

        /* ********************* 2023-06-16 工器具调整 借用出库删除联动关闭借用申请 开始  *********************** */
        Long preReceiptHeadId = headDTO.getItemDTOList().get(0).getPreReceiptHeadId();
        UpdateWrapper<BizReceiptApplyHead> headUpdateWrapper = new UpdateWrapper<>();
        headUpdateWrapper.lambda().set(BizReceiptApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue())
                .eq(BizReceiptApplyHead::getId, preReceiptHeadId);
        applyHeadDataWrap.update(headUpdateWrapper);

        UpdateWrapper<BizReceiptApplyItem> itemUpdateWrapper = new UpdateWrapper<>();
        itemUpdateWrapper.lambda().set(BizReceiptApplyItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue())
                .eq(BizReceiptApplyItem::getHeadId, preReceiptHeadId);
        applyItemDataWrap.update(itemUpdateWrapper);
        /* ********************* 借用出库删除联动关闭借用申请 结束  *********************** */

        // 借用出库单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 借用出库单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
    }

    /**
     * 逻辑删除单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        receiptRelationService.deleteReceiptTree(EnumReceiptType.STOCK_OUTPUT_BORROW.getValue(), headId);
    }

}
