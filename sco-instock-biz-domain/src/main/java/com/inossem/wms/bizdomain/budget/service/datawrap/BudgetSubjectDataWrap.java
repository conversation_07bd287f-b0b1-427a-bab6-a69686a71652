package com.inossem.wms.bizdomain.budget.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.budget.dao.BudgetSubjectMapper;
import com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplyHead;
import com.inossem.wms.common.model.masterdata.budget.entity.DicBudgetSubject;
import com.inossem.wms.common.model.masterdata.budget.po.DicBudgetSubjectSearchPO;
import com.inossem.wms.common.model.masterdata.budget.vo.DicBudgetSubjectPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@Service
public class BudgetSubjectDataWrap extends BaseDataWrap<BudgetSubjectMapper, DicBudgetSubject> {

    public List<DicBudgetSubjectPageVO> selectPageVOList(IPage<DicBudgetSubjectPageVO> page, DicBudgetSubjectSearchPO po) {
        return this.baseMapper.selectPageVOList(page, po);
    }

    public List<BizReceiptPurchaseApplyHead> checkPurchaseApplyReceiptExist(@Param("id") Long id) {
        return this.baseMapper.checkPurchaseApplyReceiptExist(id);
    }
}
