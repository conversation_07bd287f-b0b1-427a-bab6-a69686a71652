package com.inossem.wms.bizdomain.output.controller;

import com.inossem.wms.bizdomain.output.service.biz.InitOutputService;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 期初退库导入controller
 *
 * <AUTHOR>
 * @date 20220725
 */

@RestController
@Api(tags = "出库管理-期初退库")
public class InitOutputController {

    @Autowired
    private InitOutputService initOutputService;

    /**
     * 物料工厂数据导入
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "期初退库导入", notes = "期初退库导入", tags = {"出库管理"})
    @PostMapping(path = "/output/init/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importOutputInit(@RequestPart("file") MultipartFile file, BizContext ctx) {

        initOutputService.importOutputInit(ctx);
        return BaseResult.success();
    }

}