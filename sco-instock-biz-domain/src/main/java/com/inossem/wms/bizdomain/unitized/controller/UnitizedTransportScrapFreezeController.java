package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedTransportScrapFreezeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumScrapFreezeCauseType;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.print.label.LabelTransportLeisureBox;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 成套设备-报废冻结
 *
 * @Author: 刘嘉宁
 */

@RestController
@Api(tags = "成套设备-物资冻结")
public class UnitizedTransportScrapFreezeController {

    @Autowired
    private UnitizedTransportScrapFreezeService transportScrapFreezeService;

    @ApiOperation(value = "移动类型列表", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/getMoveTypeList")
    public BaseResult getMoveTypeList(BizContext ctx) {
        transportScrapFreezeService.getMoveTypeList(ctx);
        MultiResultVO<DicMoveType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "移动原因下拉列表", tags = {"成套设备-物资解冻"})
    @GetMapping(value = "/unitized/scrap-freeze-output/cause-list")
    public BaseResult getScrapCause(BizContext ctx) {
        return BaseResult.success(EnumScrapFreezeCauseType.toList());
    }

    @ApiOperation(value = "物资冻结创建", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/init")
    public BaseResult init(BizContext ctx) {
        transportScrapFreezeService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "物资冻结查询列表（分页）", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportScrapFreezeService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "物资冻结查询物料库存列表", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/mat-stock/list")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportScrapFreezeService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "物资冻结查询物料库存列表", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/mat-stock/list-import")
    public BaseResult getStockImport(@RequestPart String moveTypeId, @RequestPart("file") MultipartFile file, BizContext ctx) {
        transportScrapFreezeService.getStockImport(ctx);
        MultiResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "物资冻结详情", tags = {"成套设备-物资冻结"})
    @GetMapping(value = "/unitized/scrap-freeze-output/{id}")
    public BaseResult<BizResultVO<BizReceiptTransportHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportScrapFreezeService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "物资冻结保存", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportScrapFreezeService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "物资冻结提交", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportScrapFreezeService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "物资冻结过账", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/post")
    public BaseResult post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportScrapFreezeService.post(ctx);
        return BaseResult.success(po.getReceiptCode());
    }

    @ApiOperation(value = "物资冻结删除", tags = {"成套设备-物资冻结"})
    @DeleteMapping(value = "/unitized/scrap-freeze-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportScrapFreezeService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "物资冻结-物料标签打印", tags = {"成套设备-物资冻结"})
    @PostMapping(value = "/unitized/scrap-freeze-output/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptTransportHeadDTO> po, BizContext ctx) {
        transportScrapFreezeService.boxApplyLabelPrint(ctx);
        List<LabelTransportLeisureBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

}
