package com.inossem.wms.bizdomain.output.service.component.movetype;

import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import org.springframework.beans.factory.annotation.Autowired;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import io.jsonwebtoken.lang.Collections;

/**
 * 生成过账移动类型
 *
 * <AUTHOR>
 * @date 2021/3/31 11:11
 */

@Service
public class InsSaleOutputMoveTypeComponent {

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    protected OutputComponent outputComponent;

    /**
     * 生成记账ins凭证
     *
     * @param headDTO
     * @return
     */
    public StockInsMoveTypeDTO generatePostingInsDoc(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            if (!Collections.isEmpty(itemDTO.getAssembleDTOList()) && Collections.isEmpty(itemDTO.getBinDTOList())) {
                // 先过账模式，过账时bin表没数据，通过assemble表赋值
                insDocRid = this.getInsPostFirst(headDTO, itemDTO, insDocRid, insDocBatchList, insDocBinList);
            } else {
                // 非先过账模式，通过bin表赋值
                insDocRid = this.getInsNonPostFirst(headDTO, itemDTO, insDocRid, insDocBatchList, insDocBinList);
            }
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }

    /**
     * 非先过账模式
     *
     * @param headDTO
     * @param itemDTO
     * @param insDocRid
     * @param insDocBatchList
     * @param insDocBinList
     */
    private Integer getInsNonPostFirst(BizReceiptOutputHeadDTO headDTO, BizReceiptOutputItemDTO itemDTO,
        Integer insDocRid, List<StockInsDocBatch> insDocBatchList, List<StockInsDocBin> insDocBinList) {
        for (BizReceiptOutputBinDTO binDTO : itemDTO.getBinDTOList()) {
            // 批次库存扣减
            StockInsDocBatch insDocBatch = new StockInsDocBatch();
            insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
            insDocBatch.setMatId(itemDTO.getMatId());
            insDocBatch.setBatchId(binDTO.getBatchId());
            insDocBatch.setFtyId(itemDTO.getFtyId());
            insDocBatch.setLocationId(itemDTO.getLocationId());
            insDocBatch.setUnitId(itemDTO.getUnitId());
            insDocBatch.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBatch.setDocDate(itemDTO.getDocDate());
            insDocBatch.setPostingDate(itemDTO.getPostingDate());
            insDocBatch.setMoveTypeId(itemDTO.getMoveTypeId());
            insDocBatch.setMoveQty(binDTO.getQty());
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBatch.setPreReceiptHeadId(headDTO.getId());
            insDocBatch.setPreReceiptItemId(itemDTO.getId());
            insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
            insDocBatch.setPreReceiptBinId(binDTO.getId());
            insDocBatch.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_SALE.getValue());
            insDocBatch.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBatch.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBatch.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
            insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
            insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
            insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            insDocBatch.setCreateUserId(itemDTO.getCreateUserId());
            insDocBatchList.add(insDocBatch);
            // 出库临时区扣减
            StockInsDocBin insDocBin = new StockInsDocBin();
            insDocBin.setInsDocRid(insDocRid.toString());
            insDocBin.setMatId(itemDTO.getMatId());
            insDocBin.setBatchId(binDTO.getBatchId());
            insDocBin.setFtyId(itemDTO.getFtyId());
            insDocBin.setLocationId(itemDTO.getLocationId());
            insDocBin.setUnitId(itemDTO.getUnitId());
            insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBin.setMoveQty(binDTO.getQty());
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBin.setPreReceiptHeadId(headDTO.getId());
            insDocBin.setPreReceiptItemId(itemDTO.getId());
            insDocBin.setPreReceiptBinId(binDTO.getId());
            insDocBin.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_SALE.getValue());
            insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBin.setMatDocCode(itemDTO.getMatDocCode());
            insDocBin.setMatDocRid(itemDTO.getMatDocRid());
            insDocBin.setMatDocYear(itemDTO.getMatDocYear());
            insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            String typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
            insDocBin.setWhId(itemDTO.getWhId());
            insDocBin.setCellId(Objects.isNull(binDTO.getCellId()) ? 0 : binDTO.getCellId());
            insDocBin.setCreateUserId(itemDTO.getCreateUserId());
            insDocBinList.add(insDocBin);
            insDocRid++;
        }
        return insDocRid;
    }

    /**
     * 先过账模式
     *
     * @param headDTO
     * @param itemDTO
     * @param insDocRid
     * @param insDocBatchList
     * @param insDocBinList
     */
    private Integer getInsPostFirst(BizReceiptOutputHeadDTO headDTO, BizReceiptOutputItemDTO itemDTO, Integer insDocRid,
        List<StockInsDocBatch> insDocBatchList, List<StockInsDocBin> insDocBinList) {
        for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
            // 批次库存扣减
            StockInsDocBatch insDocBatch = new StockInsDocBatch();
            insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
            insDocBatch.setMatId(itemDTO.getMatId());
            Long batchId = outputComponent.getBatchId(assembleDTO);
            insDocBatch.setBatchId(batchId);
            insDocBatch.setFtyId(itemDTO.getFtyId());
            insDocBatch.setLocationId(itemDTO.getLocationId());
            insDocBatch.setUnitId(itemDTO.getUnitId());
            insDocBatch.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBatch.setDocDate(itemDTO.getDocDate());
            insDocBatch.setPostingDate(itemDTO.getPostingDate());
            insDocBatch.setMoveTypeId(itemDTO.getMoveTypeId());
            insDocBatch.setMoveQty(assembleDTO.getQty());
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBatch.setPreReceiptHeadId(headDTO.getId());
            insDocBatch.setPreReceiptItemId(itemDTO.getId());
            insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
            insDocBatch.setPreReceiptBinId(null);
            insDocBatch.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_SALE.getValue());
            insDocBatch.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBatch.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBatch.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
            insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
            insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
            insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            insDocBatch.setCreateUserId(itemDTO.getCreateUserId());
            insDocBatchList.add(insDocBatch);
            // 出库临时区扣减
            StockInsDocBin insDocBin = new StockInsDocBin();
            insDocBin.setInsDocRid(insDocRid.toString());
            insDocBin.setMatId(itemDTO.getMatId());
            insDocBin.setBatchId(batchId);
            insDocBin.setFtyId(itemDTO.getFtyId());
            insDocBin.setLocationId(itemDTO.getLocationId());
            insDocBin.setUnitId(itemDTO.getUnitId());
            insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBin.setMoveQty(assembleDTO.getQty());
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBin.setPreReceiptHeadId(headDTO.getId());
            insDocBin.setPreReceiptItemId(itemDTO.getId());
            insDocBin.setPreReceiptBinId(null);
            insDocBin.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_SALE.getValue());
            insDocBin.setReferReceiptHeadId(itemDTO.getReferReceiptHeadId());
            insDocBin.setReferReceiptItemId(itemDTO.getReferReceiptItemId());
            insDocBin.setReferReceiptType(itemDTO.getReferReceiptType());
            insDocBin.setMatDocCode(itemDTO.getMatDocCode());
            insDocBin.setMatDocRid(itemDTO.getMatDocRid());
            insDocBin.setMatDocYear(itemDTO.getMatDocYear());
            insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            String typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
            insDocBin.setWhId(itemDTO.getWhId());
            insDocBin.setCellId(0L);
            insDocBin.setCreateUserId(itemDTO.getCreateUserId());
            insDocBinList.add(insDocBin);
            insDocRid++;
        }
        return insDocRid;
    }

}