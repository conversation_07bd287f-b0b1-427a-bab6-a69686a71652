package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.lifetime.service.component.LifetimeAppraisalComponent;
import com.inossem.wms.bizdomain.lifetime.service.component.LifetimeCommonComponent;
import com.inossem.wms.bizdomain.unitized.service.component.UnitizedLifetimeAppraisalComponent;
import com.inossem.wms.bizdomain.unitized.service.component.UnitizedLifetimeCommonComponent;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 寿期检定 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Service
public class UnitizedLifetimeAppraisalService {

    @Autowired
    protected UnitizedLifetimeCommonComponent lifetimeCommonComponent;

    @Autowired
    protected UnitizedLifetimeAppraisalComponent lifetimeAppraisalComponent;

    /**
     * 寿期检定-初始化
     *
     * @return 寿期检定单
     */
    public void init(BizContext ctx) {

        // 页面初始化
        lifetimeAppraisalComponent.setInit(ctx);

    }

    /**
     * 查询寿期检定列表-分页
     *
     * @param ctx-po 寿期分页查询入参
     * @return 寿期检定单列表
     */
    public void getPage(BizContext ctx) {

        // 查询寿期检定列表-分页
        lifetimeAppraisalComponent.setPage(ctx);

    }

    /**
     * 查询寿期检定单详情
     *
     * @param ctx-id 寿期检定单抬头表主键
     * @return 寿期检定单详情
     */
    public void getInfo(BizContext ctx) {

        // 寿期检定单详情
        lifetimeAppraisalComponent.getInfo(ctx);

        // 设置详情页单据流
        lifetimeCommonComponent.setInfoExtendRelation(ctx);

        // 开启附件
        lifetimeCommonComponent.setExtendAttachment(ctx);

        // 开启操作日志
        lifetimeCommonComponent.setExtendOperationLog(ctx);

    }

    /**
     * 寿期检定-保存
     *
     * @param ctx-po 保存寿期检定表单参数
     * @return ctx-receiptCode 寿期检定单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 寿期检定保存校验
        lifetimeCommonComponent.checkSaveLifetime(ctx);

        // 保存寿期单
        lifetimeCommonComponent.saveLifetime(ctx);

        // 保存附件
        lifetimeCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        lifetimeCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 寿期检定-提交
     *
     * @param ctx-po 提交寿期检定表单参数
     * @return ctx-receiptCode 寿期检定单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 寿期检定保存校验
        lifetimeCommonComponent.checkSaveLifetime(ctx);

        // 提交寿期检定单
        lifetimeAppraisalComponent.submitLifetimeAppraisal(ctx);

        // 保存附件
        lifetimeCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        lifetimeCommonComponent.saveBizReceiptOperationLog(ctx);

        // 更新单据已完成
        lifetimeCommonComponent.updateStatusCompleted(ctx);

        // 生成寿期检定结果维护单
        lifetimeAppraisalComponent.genLifetimeMaintain(ctx);

    }

    /**
     * 寿期检定-删除
     *
     * @param ctx-id 寿期检定单抬头表主键
     * @return ctx-receiptCode 寿期检定单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 刪除寿期检定单
        lifetimeCommonComponent.deleteInfo(ctx);

        // 删除附件
        lifetimeCommonComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        lifetimeCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 查询物料批次库存
     *
     * @param ctx-po 寿期查询物料批次库存入参
     * @return 物料批次库存
     */
    public void getMatStock(BizContext ctx) {

        // 查询物料批次库存
        lifetimeAppraisalComponent.getMatFeatureStock(ctx);

    }

    public void export(BizContext ctx) {
        lifetimeAppraisalComponent.export(ctx);
    }
}
