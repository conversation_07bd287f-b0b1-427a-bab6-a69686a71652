package com.inossem.wms.bizdomain.output.service.component;

import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsWasteOutputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsWasteOutputMoveTypeComponent;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypePostTaskDTO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 废旧物资出库组件类
 *
 * <AUTHOR>
 * @date 2021/4/7 10:16
 */

@Service
@Slf4j
public class WasterOutputComponent {

    @Autowired
    private OutputComponent outputComponent;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private InsWasteOutputMoveTypeComponent insWasteOutputMoveTypeComponent;

    @Autowired
    private InsWasteOutputWriteOffMoveTypeComponent insWasteOutputWriteOffMoveTypeComponent;

    /**
     * 废旧物资出库详情
     *
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptOutputHeadDTO headDTO = outputComponent.getItemListById(headId);
        BizReceiptOutputItemDTO itemDTO = headDTO.getItemDTOList().get(0);
        headDTO.setMoveTypeId(itemDTO.getMoveTypeId());
        headDTO.setMoveTypeCode(itemDTO.getMoveTypeCode());
        headDTO.setMoveTypeName(itemDTO.getMoveTypeName());
        // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
        outputComponent.setMaterialFactoryInfo(headDTO.getItemDTOList());
        ButtonVO button = outputComponent.setButton(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), button));
    }

    /**
     * 获取过账移动类型并校验
     *
     * @param ctx 上下文
     */
    public void generateInsMoveTypeAndCheck(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = new StockInsMoveTypePostTaskDTO();
        StockInsMoveTypeDTO postingInsMoveTypeDTO;
        try {
            postingInsMoveTypeDTO = insWasteOutputMoveTypeComponent.generatePostingInsDoc(headDTO);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeDTO);
        // 过账凭证(处理批次库存、临时区仓位库存)
        stockInsMoveTypePostTaskDTO.setPostDTO(postingInsMoveTypeDTO);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        stockInsMoveTypePostTaskDTO.setTaskDTO(null);
        // 校验
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    /**
     * 非先过账模式 - 获取冲销移动类型并校验
     *
     * @param ctx 上下文
     *
     */
    public void generateWriteOffInsMoveTypeAndCheckNonPostFirst(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = this.setWriteOffMoveTypeNonPostFirst(headDTO);
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    private StockInsMoveTypePostTaskDTO setWriteOffMoveTypeNonPostFirst(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO postingInsMoveTypeVo;
        StockInsMoveTypeDTO taskInsMoveTypeVo = null;
        try {
            postingInsMoveTypeVo = insWasteOutputWriteOffMoveTypeComponent.generatePostingInsDocNonPostFirst(headDTO);
            List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
            boolean isGenerateTaskInsDoc = false;
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                if(UtilCollection.isNotEmpty(itemDTO.getBinDTOList())) {
                    isGenerateTaskInsDoc = true;
                    break;
                }
            }
            // 非同时模式判断是否生成task凭证
            if(isGenerateTaskInsDoc) {
                taskInsMoveTypeVo = insWasteOutputWriteOffMoveTypeComponent.generateTaskInsDoc(headDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeVo);
        bizCommonService.setInsDocCode(taskInsMoveTypeVo);
        StockInsMoveTypePostTaskDTO dto = new StockInsMoveTypePostTaskDTO();
        // 过账凭证(处理批次库存、临时区仓位库存)
        dto.setPostDTO(postingInsMoveTypeVo);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        dto.setTaskDTO(taskInsMoveTypeVo);
        return dto;
    }

    /**
     * 提交校验
     *
     * @param ctx 上下文
     */
    public void checkSubmit(BizContext ctx) {
        // 参数基本校验
        outputComponent.check(ctx);
        // 出库数量是否为0校验
        outputComponent.checkItemQtyIsZero(ctx);
        // 校验单据状态
        outputComponent.checkReceiptStatus(ctx);
    }

    /**
     * 校验删除
     *
     * @param ctx 上下文
     */
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    /**
     * 逻辑删除单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        receiptRelationService.deleteReceiptTree(EnumReceiptType.STOCK_OUTPUT_WASTE_MAT.getValue(), headId);
    }

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<
            BizReceiptOutputHeadDTO> resultVO =
                new BizResultVO<>(
                    new BizReceiptOutputHeadDTO().setReceiptType(EnumReceiptType.STOCK_OUTPUT_WASTE_MAT.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()),
                    new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

}