package com.inossem.wms.bizdomain.input.service.datawrap;

import com.inossem.wms.bizdomain.input.dao.BizReceiptInputWaybillMapper;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputWaybill;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 入库单成套运单表 服务实现类
 * </p>

 */
@Service
public class BizReceiptInputWaybillDataWrap extends BaseDataWrap<BizReceiptInputWaybillMapper, BizReceiptInputWaybill> {


}
