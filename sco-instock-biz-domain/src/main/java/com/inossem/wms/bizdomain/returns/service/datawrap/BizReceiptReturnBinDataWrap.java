package com.inossem.wms.bizdomain.returns.service.datawrap;

import org.springframework.stereotype.Service;

import com.inossem.wms.bizdomain.returns.dao.BizReceiptReturnBinMapper;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnBin;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;

/**
 * <p>
 * 退库单配货明细表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Service
public class BizReceiptReturnBinDataWrap extends BaseDataWrap<BizReceiptReturnBinMapper, BizReceiptReturnBin> {

}
