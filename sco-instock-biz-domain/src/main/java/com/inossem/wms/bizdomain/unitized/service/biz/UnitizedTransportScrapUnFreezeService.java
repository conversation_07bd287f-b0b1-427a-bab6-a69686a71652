package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.transport.service.component.TransferComponent;
import com.inossem.wms.bizdomain.transport.service.component.TransportComponent;
import com.inossem.wms.bizdomain.unitized.service.component.UnitizedTransportScrapUnFreezeComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 成套设备-报废解冻
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class UnitizedTransportScrapUnFreezeService {

    @Autowired
    private UnitizedTransportScrapUnFreezeComponent transportScrapFreezeComponent;

    @Autowired
    private TransportComponent transportComponent;

    @Autowired
    private TransferComponent transferComponent;

    /**
     * 移动类型列表
     */
    @Entrance(call = {"transportComponent#getMoveTypeList"})
    public void getMoveTypeList(BizContext ctx) {

        // 移动类型列表
        transportScrapFreezeComponent.getMoveTypeList(ctx);
    }

    /**
     * 页面初始化
     */
    @Entrance(call = {"transferScrapFreezeComponent#init", "transferScrapFreezeComponent#setExtendWf",
        "transferScrapFreezeComponent#setExtendAttachment", "transferScrapFreezeComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportScrapFreezeComponent.init(ctx);

        // 开启审批
        transportScrapFreezeComponent.setExtendWf(ctx);

        // 开启附件
        transportScrapFreezeComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportScrapFreezeComponent.setExtendOperationLog(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transferScrapFreezeComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportScrapFreezeComponent.getStock(ctx);
    }

    /**
     * 界面物料创建导入
     * @param ctx
     */
    public void getStockImport(BizContext ctx) {
        transportScrapFreezeComponent.getStockImport(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transferScrapFreezeComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportScrapFreezeComponent.getPage(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transferScrapFreezeComponent#getInfo", "transferScrapFreezeComponent#setExtendWf",
        "transferScrapFreezeComponent#setExtendAttachment", "transferScrapFreezeComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 查询单据详情,包含按钮组和扩展功能
        transportScrapFreezeComponent.getInfo(ctx);

        // 开启附件
        transportScrapFreezeComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportScrapFreezeComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transferScrapFreezeComponent#checkSaveData", "transferScrapFreezeComponent#save",
        "transferScrapFreezeComponent#saveBizReceiptAttachment", "transferScrapFreezeComponent#saveBizReceiptOperationLog"})
    public void save(BizContext ctx) {

        // 保存时校验数据
        transportScrapFreezeComponent.checkSaveData(ctx);

        // 保存
        transportScrapFreezeComponent.save(ctx);

        // 保存附件
        transportScrapFreezeComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportScrapFreezeComponent.saveBizReceiptOperationLog(ctx);
    }

    @Entrance(call = {"transferScrapFreezeComponent#checkSaveData", "transferScrapFreezeComponent#save",
            "transferScrapFreezeComponent#saveBizReceiptAttachment", "transferScrapFreezeComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 保存时校验数据
        transportScrapFreezeComponent.checkSubmitData(ctx);

        // 保存
        transportScrapFreezeComponent.submit(ctx);

        // 寿期流转时-保存单据流
        transportScrapFreezeComponent.saveReceiptTree(ctx);

        // 保存附件
        transportScrapFreezeComponent.saveBizReceiptAttachment(ctx);

        // 处理标签关联关系 - 拆分标签
        transportScrapFreezeComponent.handleLabel(ctx);

        // 根据移动类型保存bin
        transportScrapFreezeComponent.saveOutputBinByMoveType(ctx);

        // 生成凭证
        transportScrapFreezeComponent.generateInsMoveTypeAndCheck(ctx);

        // 状态变更-未同步
        transportComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transferComponent.post(ctx);

        // 修改库存
        transportComponent.modifyStock(ctx);

        // 状态变更-已完成
        transportComponent.updateStatusCompleted(ctx);

        // 保存操作日志
        transportScrapFreezeComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 过账
     */
    @Entrance(call = {"transferScrapFreezeComponent#checkSubmitData", "transferScrapFreezeComponent#submit",
        "transferScrapFreezeComponent#saveBizReceiptAttachment", "transferScrapFreezeComponent#saveBizReceiptOperationLog",
        "transferScrapFreezeComponent#saveOutputBinByMoveType", "transferScrapFreezeComponent#generateInsDocToPost",
        "transferScrapFreezeComponent#checkAndComputeForModifyStock", "transferScrapFreezeComponent#updateStatusUnsync",
        "transferScrapFreezeComponent#post", "transferScrapFreezeComponent#modifyStock", "transferScrapFreezeComponent#modifyLabel",
        "transferScrapFreezeComponent#updateStatusCompleted"})
    public void post(BizContext ctx) {

        //生成凭证
        transportScrapFreezeComponent.generateInsMoveTypeAndCheck(ctx);

        // 状态变更-未同步
        transportComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transferComponent.post(ctx);

        // 修改库存
        transportComponent.modifyStock(ctx);

        // 状态变更-已完成
        transportComponent.updateStatusCompleted(ctx);

        // 保存操作日志
        transportScrapFreezeComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 删除
     */
    @Entrance(call = {"transferScrapFreezeComponent#delete", "transferScrapFreezeComponent#deleteBizReceiptAttachment"})
    public void delete(BizContext ctx) {

        // 删除
        transportScrapFreezeComponent.delete(ctx);

        // 逻辑删除附件
        transportScrapFreezeComponent.deleteBizReceiptAttachment(ctx);
    }

}
