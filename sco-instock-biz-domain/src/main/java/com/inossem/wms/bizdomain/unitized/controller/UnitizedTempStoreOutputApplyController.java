package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.apply.service.biz.ApplyCommonService;
import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedTempStoreOutputApplyService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 成套设备 暂存物项领用申请Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "成套设备-暂存物项领用申请")
@Deprecated
public class UnitizedTempStoreOutputApplyController {

    @Autowired
    private UnitizedTempStoreOutputApplyService tempStoreOutputApplyService;

    @Autowired
    protected ApplyCommonService applyCommonService;

    @ApiOperation(value = "创建暂存物项领用申请初始化", tags = {"成套设备-暂存物项领用申请"})
    @PostMapping(value = "/unitized/temp-store-output-apply/init")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> init(BizContext ctx) {
        tempStoreOutputApplyService.init(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "暂存物项领用申请单查询详情", tags = {"成套设备-暂存物项领用申请"})
    @GetMapping(value = "/unitized/temp-store-output-apply/{id}")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        tempStoreOutputApplyService.getInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "暂存物项领用申请单保存", tags = {"成套设备-暂存物项领用申请"})
    @PostMapping(value = "/unitized/temp-store-output-apply/save")
    public BaseResult<?> save(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        tempStoreOutputApplyService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TEMPORARY_STORAGE_APPLY_OUTPUT_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "暂存物项领用申请单提交", tags = {"成套设备-暂存物项领用申请"})
    @PostMapping(value = "/unitized/temp-store-output-apply/submit")
    public BaseResult<?> submit(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        tempStoreOutputApplyService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_TEMPORARY_STORAGE_APPLY_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "暂存物项领用申请单查询列表（分页）", tags = {"成套设备-暂存物项领用申请"})
    @PostMapping(value = "/unitized/temp-store-output-apply/results")
    public BaseResult<PageObjectVO<BizReceiptApplyPageVO>> getPage(@RequestBody BizReceiptApplySearchPO po, BizContext ctx) {
        applyCommonService.getPage(ctx);
        PageObjectVO<BizReceiptApplyPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = " 物料库存查询", tags = {"成套设备-暂存物项领用申请"})
    @PostMapping(value = "/unitized/temp-store-output-apply/mat-list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptApplySearchMatPO po, BizContext ctx) {
        tempStoreOutputApplyService.getMatStock(ctx);
        MatStockDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "暂存物项领用申请-删除", tags = {"成套设备-暂存物项领用申请"})
    @DeleteMapping(value = "/unitized/temp-store-output-apply/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        tempStoreOutputApplyService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_DELETE_SUCCESS, receiptCode);
    }

    @ApiOperation(value = "暂存物项领用打印信息", tags = {"仓储管理-暂存物项领用打印信息"})
    @GetMapping(value = "/unitized/temp-store-output-apply/printInfo/{id}")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getPrintInfo(@PathVariable("id") Long id, BizContext ctx) {
        tempStoreOutputApplyService.getPrintInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}
