package com.inossem.wms.bizdomain.transport.service.component;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizdomain.task.service.component.TaskComponent;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPo;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportMoveTypeComponent {

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private TaskComponent taskComponent;

    /**
     * 生成ins凭证 - 过账
     */
    public StockInsMoveTypeDTO generateInsDocToPost(BizReceiptTransportHeadDTO headDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptTransportBinDTO binDTO : itemDto.getBinDTOList()) {
                // 批次库存-发出方扣减
                insDocBatchList.add(this.getOutputInsBatch(headDTO, itemDto, binDTO, rid, code, userId));
                // 仓位库存-发出方扣减
                insDocBinList.add(this.getOutputInsBin(headDTO, itemDto, binDTO, rid++, code, userId));
                // 批次库存-接收方新增
                insDocBatchList.add(this.getInputInsBatch(headDTO, itemDto, binDTO, rid, code, userId));
                // 仓位库存-接收方新增
                insDocBinList.add(this.getInputInsBin(headDTO, itemDto, binDTO, rid++, code, userId));
            }
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        return insMoveTypeDTO;
    }



    /**
     * 先过账模式, 根据assemble修改库存 *
     */
    /**
     * 生成ins凭证
     */
    public StockInsMoveTypeDTO generateInsDocToPostByAssemble(BizReceiptTransportHeadDTO headDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                // 批次库存-发出方扣减
                insDocBatchList.add(this.getOutputInsBatchByAssemble(headDTO, itemDto, assembleDTO, rid, code, userId));
                // 仓位库存-发出方扣减
                insDocBinList.add(this.getOutputInsBinByAssemble(headDTO, itemDto, assembleDTO, rid++, code, userId));
                // 批次库存-接收方新增
                insDocBatchList.add(this.getInputInsBatchByAssemble(headDTO, itemDto, assembleDTO, rid, code, userId));
                // 仓位库存-接收方新增
                insDocBinList.add(this.getInputInsBinByAssemble(headDTO, itemDto, assembleDTO, rid++, code, userId));
            }
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        return insMoveTypeDTO;
    }
    /**
     * 快速模式
     * 生成ins凭证
     */
    public StockInsMoveTypeDTO generateInsDocToPostQuickModel(BizReceiptTransportHeadDTO headDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int brid = 1;
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptTransportBinDTO binDTO : itemDto.getBinDTOList()) {
                // 批次库存-发出方扣减
                insDocBatchList.add(this.getOutputInsBatchQuickModel(headDTO, itemDto, binDTO, brid++, code, userId));
                // 批次库存-接收方新增
                insDocBatchList.add(this.getInputInsBatchQuickModel(headDTO, itemDto, binDTO, brid++, code, userId));
                // 仓位库存-发出方扣减
                insDocBinList.add(this.getOutputInsBinQuickModel(headDTO, itemDto, binDTO, rid++, code, userId));
                // 仓位库存-接收方新增
                insDocBinList.add(this.getInputInsBinQuickModel(headDTO, itemDto, binDTO, rid++, code, userId));
            }
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        return insMoveTypeDTO;
    }


    /**
     * 生成ins凭证 - 冲销
     */
    public StockInsMoveTypeDTO generateInsDocToPostWriteOff(BizReceiptTransportHeadDTO headDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        List<Long> itemIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            itemIdList.add(itemDto.getId());
            for (BizReceiptTransportBinDTO binDTO : itemDto.getBinDTOList()) {
                // 批次库存-发出方扣减
                insDocBatchList.add(this.getOutputInsBatch(headDTO, itemDto, binDTO, rid, code, userId));
                // 仓位库存-发出方扣减
                insDocBinList.add(this.getOutputInsBin(headDTO, itemDto, binDTO, rid++, code, userId));
                // 批次库存-接收方新增
                insDocBatchList.add(this.getInputInsBatch(headDTO, itemDto, binDTO, rid++, code, userId));
            }
        }
        rid = 2;
        // 仓位库存-接收方新增-冲销扣减实际仓位
        List<BizReceiptTaskItemDTO> taskItemDTOList = taskComponent.getTaskItemByPreReceiptItemIds(itemIdList);
        if (!UtilCollection.isEmpty(taskItemDTOList)) {
            for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
                if (EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue().equals(taskItemDTO.getReceiptType())) {
                    // 取上架
                    insDocBinList.add(this.getInsBinByTask(Const.CREDIT_H_SUBTRACT, taskItemDTO, rid++, code, userId,
                        headDTO.getMoveTypeId(), taskItemDTO.getTargetTypeCode(), taskItemDTO.getTargetBinCode()));
                    rid++;
                }
            }
        } else {
            // 同时模式
            for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
                for (BizReceiptTransportBinDTO binDTO : itemDto.getBinDTOList()) {
                    // 仓位库存-接收方新增
                    insDocBinList.add(this.getInputInsBin(headDTO, itemDto, binDTO, rid++, code, userId));
                    rid++;
                }
            }
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        return insMoveTypeDTO;
    }



    /**
     * 仓位库存-发出方扣减
     */
    private StockInsDocBin getOutputScrapFreezeInsBin(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO itemDto,
                                                      BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getOutputMatId());
        insDocBin.setBatchId(binDTO.getOutputBatchId());
        insDocBin.setFtyId(itemDto.getOutputFtyId());
        insDocBin.setLocationId(itemDto.getOutputLocationId());
        insDocBin.setWhId(itemDto.getOutputWhId());
        insDocBin.setCellId(binDTO.getOutputCellId());
        insDocBin.setUnitId(itemDto.getOutputUnitId());
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
            // 实际仓位
            insDocBin.setBinId(binDTO.getOutputBinId());
            insDocBin.setTypeId(binDTO.getOutputTypeId());

        } else {
            // 发出方扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            // 实际仓位
            insDocBin.setBinId(binDTO.getOutputBinId());
            insDocBin.setTypeId(binDTO.getOutputTypeId());
        }
        insDocBin.setStockStatus(headDTO.getOutputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }


    /**
     * 批次库存-发出方扣减
     */
    private StockInsDocBin getOutputInsBin(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO itemDto,
                                           BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getOutputMatId());
        insDocBin.setBatchId(binDTO.getOutputBatchId());
        insDocBin.setFtyId(itemDto.getOutputFtyId());
        insDocBin.setLocationId(itemDto.getOutputLocationId());
        insDocBin.setWhId(itemDto.getOutputWhId());
        insDocBin.setCellId(binDTO.getInputCellId());
        insDocBin.setUnitId(itemDto.getInputUnitId());
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
            // 实际仓位
            insDocBin.setBinId(binDTO.getOutputBinId());
            insDocBin.setTypeId(binDTO.getOutputTypeId());
        } else {
            // 发出方扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
            String typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDto.getOutputWhCode(), typeCode);
            String binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
            Long binId = dictionaryService.getBinIdCacheByCode(itemDto.getOutputWhCode(), typeCode, binCode);
            List<Long> moveTypeIdList = null;
            if (UtilCollection.isEmpty(headDTO.getMoveTypeIds())) {
                moveTypeIdList =
                        dictionaryService.getMoveTypeIDListCacheByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
            } else {
                moveTypeIdList = headDTO.getMoveTypeIds();
            }
            if (moveTypeIdList.contains(headDTO.getMoveTypeId())) {
                // 冻结转非限制,这种移动类型,需要直接修改正式仓位数据
                typeId = binDTO.getOutputTypeId();
                binId = binDTO.getOutputBinId();
            }
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
        }
        insDocBin.setStockStatus(headDTO.getOutputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }

    /**
     * 批次库存-接收方新增
     */
    private StockInsDocBin getInsBinByTask(String debitCredit, BizReceiptTaskItemDTO taskItemDTO, int rid, String code,
        Long userId, Long moveTypeId, String typeCode, String binCode) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(taskItemDTO.getMatId());
        insDocBin.setBatchId(taskItemDTO.getBatchId());
        insDocBin.setFtyId(taskItemDTO.getFtyId());
        insDocBin.setLocationId(taskItemDTO.getLocationId());
        insDocBin.setWhId(taskItemDTO.getWhId());
        // 单位小数点不变,取发出小数
        insDocBin.setDecimalPlace(dictionaryService.getUnitCacheById(taskItemDTO.getUnitId()).getDecimalPlace());
        String whCode = dictionaryService.getWhCacheById(taskItemDTO.getWhId()).getWhCode();
        Long typeId = dictionaryService.getStorageTypeIdCacheByCode(whCode, typeCode);
        Long binId = dictionaryService.getBinIdCacheByCode(whCode, typeCode, binCode);
        insDocBin.setTypeId(typeId);
        insDocBin.setBinId(binId);
        insDocBin.setCellId(taskItemDTO.getTargetCellId());
        insDocBin.setUnitId(taskItemDTO.getUnitId());
        insDocBin.setMoveQty(taskItemDTO.getQty());
        // 借贷标识
        insDocBin.setDebitCredit(debitCredit);
        // 非限制
        insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(taskItemDTO.getPreReceiptHeadId());
        insDocBin.setPreReceiptItemId(taskItemDTO.getPreReceiptItemId());
        insDocBin.setPreReceiptBinId(taskItemDTO.getPreReceiptBinId());
        insDocBin.setPreReceiptType(taskItemDTO.getPreReceiptType());
        // 参考单据相关
        insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
        insDocBin.setMoveTypeId(moveTypeId);
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }

    /**
     * 批次库存-接收方新增
     */
    private StockInsDocBin getInputInsBin(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO itemDto,
        BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getInputMatId());
        insDocBin.setBatchId(binDTO.getInputBatchId());
        insDocBin.setFtyId(itemDto.getInputFtyId());
        insDocBin.setLocationId(itemDto.getInputLocationId());
        insDocBin.setWhId(itemDto.getInputWhId());
        String typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
        Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDto.getInputWhCode(), typeCode);
        String binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
        Long binId = dictionaryService.getBinIdCacheByCode(itemDto.getInputWhCode(), typeCode, binCode);
        List<Long> moveTypeIdList = null;
        if (UtilCollection.isEmpty(headDTO.getMoveTypeIds())) {
            moveTypeIdList =
                dictionaryService.getMoveTypeIDListCacheByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        } else {
            moveTypeIdList = headDTO.getMoveTypeIds();
        }
        if (moveTypeIdList.contains(headDTO.getMoveTypeId())) {
            // 冻结转非限制,这种移动类型,需要直接修改正式仓位数据
            typeId = binDTO.getInputTypeId();
            binId = binDTO.getInputBinId();
        }
        insDocBin.setTypeId(typeId);
        insDocBin.setBinId(binId);
        // 托盘不变,取发出托盘
        insDocBin.setCellId(binDTO.getInputCellId());
        insDocBin.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        }
        insDocBin.setStockStatus(headDTO.getInputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);

        if(UtilCollection.isNotEmpty(binDTO.getLabelDataList())){
            StockInsDocBinPo insDocBinPo = UtilBean.deepCopyNewInstance(insDocBin, StockInsDocBinPo.class);
            insDocBinPo.setLabelIdList(binDTO.getLabelDataList().stream()
                .map(BizLabelReceiptRelDTO::getLabelId).collect(Collectors.toList()));
            insDocBin.setLabelPo(insDocBinPo);
        }

        return insDocBin;
    }


    /**
     * 仓位库存-接收方新增
     */
    private StockInsDocBin getInputScrapFreezeInsBin(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO itemDto,
                                                     BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getInputMatId());
        insDocBin.setBatchId(binDTO.getInputBatchId());
        insDocBin.setFtyId(itemDto.getInputFtyId());
        insDocBin.setLocationId(itemDto.getInputLocationId());
        insDocBin.setWhId(itemDto.getInputWhId());
        List<Long> moveTypeIdList = null;
        if (UtilCollection.isEmpty(headDTO.getMoveTypeIds())) {
            moveTypeIdList =
                    dictionaryService.getMoveTypeIDListCacheByReceiptType(EnumReceiptType.STOCK_FREEZE_SCRAP.getValue());
        } else {
            moveTypeIdList = headDTO.getMoveTypeIds();
        }
        insDocBin.setTypeId(binDTO.getInputTypeId());
        insDocBin.setBinId(binDTO.getInputBinId());
        // 托盘不变,取发出托盘
        insDocBin.setCellId(binDTO.getInputCellId());
        insDocBin.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        }

        if (EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue().equals(headDTO.getOutputStockStatus())) {
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        } else {
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue());
        }
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);

        return insDocBin;
    }

    /**
     * 批次库存-发出方扣减
     */
    private StockInsDocBatch getOutputInsBatch(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO itemDto,
        BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(itemDto.getOutputMatId());
        insDocBatch.setBatchId(binDTO.getOutputBatchId());
        insDocBatch.setFtyId(itemDto.getOutputFtyId());
        insDocBatch.setLocationId(itemDto.getOutputLocationId());
        insDocBatch.setUnitId(itemDto.getOutputUnitId());
        insDocBatch.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        } else {
            // 发出方扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        }
        insDocBatch.setStockStatus(headDTO.getOutputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(itemDto.getId());
        insDocBatch.setPreReceiptBinId(binDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }

    /**
     * 批次库存-接收方新增
     */
    private StockInsDocBatch getInputScrapFreezeInsBatch(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO itemDto,
        BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(itemDto.getInputMatId());
        insDocBatch.setBatchId(binDTO.getInputBatchId());
        insDocBatch.setFtyId(itemDto.getInputFtyId());
        insDocBatch.setLocationId(itemDto.getInputLocationId());
        insDocBatch.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBatch.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        }

        if (EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue().equals(headDTO.getOutputStockStatus())) {
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        } else {
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue());
        }
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(itemDto.getId());
        insDocBatch.setPreReceiptBinId(binDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }



    /**
     * 批次库存-接收方新增
     */
    private StockInsDocBatch getInputInsBatch(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO itemDto,
                                              BizReceiptTransportBinDTO binDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(itemDto.getInputMatId());
        insDocBatch.setBatchId(binDTO.getInputBatchId());
        insDocBatch.setFtyId(itemDto.getInputFtyId());
        insDocBatch.setLocationId(itemDto.getInputLocationId());
        insDocBatch.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBatch.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        }
        insDocBatch.setStockStatus(headDTO.getInputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(itemDto.getId());
        insDocBatch.setPreReceiptBinId(binDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }


    /**
     * 先过账模式, 根据assemble修改库存 *
     */
    /**
     * 生成ins凭证
     */
    public StockInsMoveTypeDTO generateFreezeInsDocToPostByBin(BizReceiptTransportHeadDTO headDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptTransportBinDTO binDTO : itemDto.getBinDTOList()) {
                // 批次库存-发出方扣减
                insDocBatchList.add(this.getOutputInsBatch(headDTO, itemDto, binDTO, rid, code, userId));
                // 仓位库存-发出方扣减
                insDocBinList.add(this.getOutputScrapFreezeInsBin(headDTO, itemDto, binDTO, rid++, code, userId));
                // 批次库存-接收方新增
                insDocBatchList.add(this.getInputScrapFreezeInsBatch(headDTO, itemDto, binDTO, rid++, code, userId));
                // 仓位库存-接收方新增
                insDocBinList.add(this.getInputScrapFreezeInsBin(headDTO, itemDto, binDTO, rid++, code, userId));
            }
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        List<StockInsDocBinPo> insDocBinPoList = new ArrayList<>();
        for(StockInsDocBin bin : insDocBinList){
            if(bin.getLabelPo()!=null){
                insDocBinPoList.add(bin.getLabelPo());
            }

        }
        insMoveTypeDTO.setInsDocBinPoList(insDocBinPoList);
        return insMoveTypeDTO;
    }

    /**
     * 生成ins凭证 - 冲销 - 先过账
     */
    public StockInsMoveTypeDTO generateInsDocToPostWriteOffByAssemble(BizReceiptTransportHeadDTO headDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        List<Long> itemIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            itemIdList.add(itemDto.getId());
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                // 批次库存-发出方扣减-冲销新增
                insDocBatchList.add(this.getOutputInsBatchByAssemble(headDTO, itemDto, assembleDTO, rid, code, userId));
                // 仓位库存-发出方扣减-冲销新增
                insDocBinList.add(this.getOutputInsBinByAssemble(headDTO, itemDto, assembleDTO, rid++, code, userId));
                // 批次库存-接收方新增-冲销扣减805
                insDocBatchList.add(this.getInputInsBatchByAssemble(headDTO, itemDto, assembleDTO, rid, code, userId));
                // 仓位库存-接收方新增-冲销扣减805
                insDocBinList.add(this.getInputInsBinByAssemble(headDTO, itemDto, assembleDTO, rid++, code, userId));
            }
        }
        // 查询对应的作业单数据
        List<BizReceiptTaskItemDTO> taskItemDTOList = taskComponent.getTaskItemByPreReceiptItemIds(itemIdList);
        if (!UtilCollection.isEmpty(taskItemDTOList)) {
            String typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
            String binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
            for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
                if (EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue().equals(taskItemDTO.getReceiptType())) {
                    // 已上架的,冲销扣减,正式仓位,上架取target
                    insDocBinList.add(this.getInsBinByTask(Const.CREDIT_H_SUBTRACT, taskItemDTO, rid++, code, userId,
                        headDTO.getMoveTypeId(), taskItemDTO.getTargetTypeCode(), taskItemDTO.getTargetBinCode()));
                    // 补805新增
                    insDocBinList.add(this.getInsBinByTask(Const.DEBIT_S_ADD, taskItemDTO, rid++, code, userId,
                        headDTO.getMoveTypeId(), typeCode, binCode));
                }
                if (EnumReceiptType.STOCK_TASK_SHELF_UNLOAD.getValue().equals(taskItemDTO.getReceiptType())) {
                    // 已下架的,冲销增加,正式仓位,下架取source
                    insDocBinList.add(this.getInsBinByTask(Const.DEBIT_S_ADD, taskItemDTO, rid++, code, userId,
                        headDTO.getMoveTypeId(), taskItemDTO.getSourceTypeCode(), taskItemDTO.getSourceBinCode()));
                    // 补805扣减
                    insDocBinList.add(this.getInsBinByTask(Const.CREDIT_H_SUBTRACT, taskItemDTO, rid++, code, userId,
                        headDTO.getMoveTypeId(), typeCode, binCode));
                }
            }
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        return insMoveTypeDTO;
    }

    /**
     * 批次库存-发出方扣减
     */
    public StockInsDocBin getOutputInsBinByAssemble(BizReceiptTransportHeadDTO headDTO,
        BizReceiptTransportItemDTO itemDto, BizReceiptAssembleDTO assembleDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getOutputMatId());
        // 根据特征获取批次id
        insDocBin.setBatchId(this.getBatchId(assembleDTO));
        insDocBin.setFtyId(itemDto.getOutputFtyId());
        insDocBin.setLocationId(itemDto.getOutputLocationId());
        insDocBin.setWhId(itemDto.getOutputWhId());
        insDocBin.setUnitId(itemDto.getInputUnitId());
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(assembleDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        } else {
            // 发出方扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        }
        String typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
        Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDto.getOutputWhCode(), typeCode);
        String binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
        Long binId = dictionaryService.getBinIdCacheByCode(itemDto.getOutputWhCode(), typeCode, binCode);
        insDocBin.setTypeId(typeId);
        insDocBin.setBinId(binId);
        insDocBin.setCellId((long)0);
        insDocBin.setStockStatus(headDTO.getOutputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(assembleDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }


    /**
     * 批次库存-发出方扣减
     */
    public StockInsDocBin getOutputInsBinQuickModel(BizReceiptTransportHeadDTO headDTO,
                                                     BizReceiptTransportItemDTO itemDto, BizReceiptTransportBinDTO  binDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getOutputMatId());
        // 根据特征获取批次id
        insDocBin.setBatchId(binDTO.getOutputBatchId());
        insDocBin.setFtyId(itemDto.getOutputFtyId());
        insDocBin.setLocationId(itemDto.getOutputLocationId());
        insDocBin.setWhId(itemDto.getOutputWhId());
        insDocBin.setUnitId(itemDto.getOutputUnitId());
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        } else {
            // 发出方扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        }
        insDocBin.setTypeId(binDTO.getOutputTypeId());
        insDocBin.setBinId(binDTO.getOutputBinId());
        insDocBin.setCellId((long)0);
        insDocBin.setStockStatus(headDTO.getOutputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }

    /**
     * 批次库存-接收方新增
     */
    // private StockInsDocBin getInputInsBinByTask(BizReceiptTransportHeadDTO headDTO, BizReceiptTransportItemDTO
    // itemDto,
    // BizReceiptTransportBinDTO binDTO, BizReceiptTaskItemDTO taskItemDTO, int rid, String code, Long userId) {
    // StockInsDocBin insDocBin = this.getInputInsBin(headDTO, itemDto, binDTO, rid, code, userId);
    // // 实际作业仓位
    // Long typeId = taskItemDTO.getTargetTypeId();
    // Long binId = taskItemDTO.getTargetBinId();
    // BigDecimal qty = taskItemDTO.getQty();
    // insDocBin.setTypeId(typeId);
    // insDocBin.setBinId(binId);
    // insDocBin.setMoveQty(qty);
    // return insDocBin;
    // }
    /**
     * 批次库存-接收方新增
     */
    public StockInsDocBin getInputInsBinByAssemble(BizReceiptTransportHeadDTO headDTO,
        BizReceiptTransportItemDTO itemDto, BizReceiptAssembleDTO assembleDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getInputMatId());
        // 根据特征获取批次id
        insDocBin.setBatchId(assembleDTO.getInputBatchId());
        insDocBin.setFtyId(itemDto.getInputFtyId());
        insDocBin.setLocationId(itemDto.getInputLocationId());
        insDocBin.setWhId(itemDto.getInputWhId());
        String typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
        Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDto.getInputWhCode(), typeCode);
        String binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
        Long binId = dictionaryService.getBinIdCacheByCode(itemDto.getInputWhCode(), typeCode, binCode);
        insDocBin.setTypeId(typeId);
        insDocBin.setBinId(binId);
        insDocBin.setCellId((long)0);
        // 托盘不变,取发出托盘
        insDocBin.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(assembleDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        }
        insDocBin.setStockStatus(headDTO.getInputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(assembleDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }

    /**
     * 批次库存-接收方新增
     */
    public StockInsDocBin getInputInsBinQuickModel(BizReceiptTransportHeadDTO headDTO,
                                                    BizReceiptTransportItemDTO itemDto, BizReceiptTransportBinDTO  binDTO, int rid, String code, Long userId) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setInsDocCode(code);
        insDocBin.setInsDocRid(String.valueOf(rid));
        insDocBin.setMatId(itemDto.getInputMatId());
        // 根据特征获取批次id
        insDocBin.setBatchId(binDTO.getInputBatchId());
        insDocBin.setFtyId(itemDto.getInputFtyId());
        insDocBin.setLocationId(itemDto.getInputLocationId());
        insDocBin.setWhId(itemDto.getInputWhId());
        insDocBin.setTypeId(binDTO.getInputTypeId());
        insDocBin.setBinId(binDTO.getInputBinId());
        insDocBin.setCellId((long)0);
        // 托盘不变,取发出托盘
        insDocBin.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBin.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBin.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        }
        insDocBin.setStockStatus(headDTO.getInputStockStatus());
        // 前置单据相关
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(itemDto.getId());
        insDocBin.setPreReceiptBinId(binDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据相关
        insDocBin.setReferReceiptHeadId(headDTO.getPreReceiptHeadId());
        insDocBin.setReferReceiptItemId(itemDto.getPreReceiptItemId());
        insDocBin.setReferReceiptType(headDTO.getPreReceiptType());
        insDocBin.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBin.setMatDocCode(itemDto.getMatDocCode());
        insDocBin.setMatDocRid(itemDto.getMatDocRid());
        insDocBin.setMatDocYear(itemDto.getMatDocYear());
        insDocBin.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBin.setCreateUserId(userId);
        return insDocBin;
    }

    /**
     * 批次库存-发出方扣减
     */
    public StockInsDocBatch getOutputInsBatchByAssemble(BizReceiptTransportHeadDTO headDTO,
        BizReceiptTransportItemDTO itemDto, BizReceiptAssembleDTO assembleDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(itemDto.getOutputMatId());
        // 根据特征获取批次id
        insDocBatch.setBatchId(this.getBatchId(assembleDTO));
        insDocBatch.setFtyId(itemDto.getOutputFtyId());
        insDocBatch.setLocationId(itemDto.getOutputLocationId());
        insDocBatch.setUnitId(itemDto.getOutputUnitId());
        insDocBatch.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(assembleDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        } else {
            // 发出方扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        }
        insDocBatch.setStockStatus(headDTO.getOutputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(itemDto.getId());
        insDocBatch.setPreReceiptBinId(assembleDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }

    /**
     * 批次库存-发出方扣减
     */
    public StockInsDocBatch getOutputInsBatchQuickModel(BizReceiptTransportHeadDTO headDTO,
                                                         BizReceiptTransportItemDTO itemDto,  BizReceiptTransportBinDTO  binDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(itemDto.getOutputMatId());
        // 根据特征获取批次id
        insDocBatch.setBatchId(binDTO.getOutputBatchId());
        insDocBatch.setFtyId(itemDto.getOutputFtyId());
        insDocBatch.setLocationId(itemDto.getOutputLocationId());
        insDocBatch.setUnitId(itemDto.getOutputUnitId());
        insDocBatch.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        } else {
            // 发出方扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        }
        insDocBatch.setStockStatus(headDTO.getOutputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(itemDto.getId());
        insDocBatch.setPreReceiptBinId(binDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }

    /**
     * 批次库存-接收方新增
     */
    public StockInsDocBatch getInputInsBatchByAssemble(BizReceiptTransportHeadDTO headDTO,
        BizReceiptTransportItemDTO itemDto, BizReceiptAssembleDTO assembleDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(itemDto.getInputMatId());
        // 根据特征获取批次id
        insDocBatch.setBatchId(assembleDTO.getInputBatchId());
        insDocBatch.setFtyId(itemDto.getInputFtyId());
        insDocBatch.setLocationId(itemDto.getInputLocationId());
        insDocBatch.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBatch.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(assembleDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        }
        insDocBatch.setStockStatus(headDTO.getInputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(itemDto.getId());
        insDocBatch.setPreReceiptBinId(assembleDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }

    /**
     * 批次库存-接收方新增
     */
    public StockInsDocBatch getInputInsBatchQuickModel(BizReceiptTransportHeadDTO headDTO,
                                                        BizReceiptTransportItemDTO itemDto,  BizReceiptTransportBinDTO  binDTO, int rid, String code, Long userId) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        insDocBatch.setInsDocCode(code);
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(rid));
        insDocBatch.setMatId(itemDto.getInputMatId());
        // 根据特征获取批次id
        insDocBatch.setBatchId(binDTO.getInputBatchId());
        insDocBatch.setFtyId(itemDto.getInputFtyId());
        insDocBatch.setLocationId(itemDto.getInputLocationId());
        insDocBatch.setUnitId(itemDto.getInputUnitId());
        // 单位小数点不变,取发出小数
        insDocBatch.setDecimalPlace(itemDto.getDecimalPlace());
        insDocBatch.setMoveTypeId(headDTO.getMoveTypeId());
        insDocBatch.setMoveQty(binDTO.getQty());
        if (EnumRealYn.TRUE.getIntValue().equals(itemDto.getIsWriteOff())) {
            // 冲销 - 扣减
            insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        } else {
            // 接收方新增
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
        }
        insDocBatch.setStockStatus(headDTO.getInputStockStatus());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptItemId(itemDto.getId());
        insDocBatch.setPreReceiptBinId(binDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        insDocBatch.setIsWriteOff(itemDto.getIsWriteOff());
        insDocBatch.setMatDocCode(itemDto.getMatDocCode());
        insDocBatch.setMatDocRid(itemDto.getMatDocRid());
        insDocBatch.setMatDocYear(itemDto.getMatDocYear());
        insDocBatch.setCreateUserId(userId);
        return insDocBatch;
    }
    /**
     * 获取批次id(用于先过账模式生成凭证)
     */
    public Long getBatchId(BizReceiptAssembleDTO assembleDTO) {
        String stockBinBatchId = StockBin.class.getAnnotation(TableName.class).value() + Const.POINT
            + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(stockBinBatchId)) {
                return Long.valueOf(valueList.get(i));
            }
        }
        return null;
    }

}