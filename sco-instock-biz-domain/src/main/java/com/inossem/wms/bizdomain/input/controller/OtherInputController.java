package com.inossem.wms.bizdomain.input.controller;

import com.inossem.wms.bizdomain.input.service.biz.OtherInputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.returns.vo.BizReceiptReturnPreVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.PreReceiptQueryPO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 入库单表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-10
 */

@RestController
@Api(tags = "入库管理-油品入库")
public class OtherInputController {

    @Autowired
    private OtherInputService otherInputService;

    /**
     * 油品入库-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "油品入库-初始化", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        otherInputService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 油品入库-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 油品入库单分页
     */
    @ApiOperation(value = "油品入库-分页", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po,
        BizContext ctx) {
        otherInputService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 油品入库-详情
     *
     * @param id 油品入库主键
     * @param ctx 入参上下文 {"id":"油品入库主键"}
     */
    @ApiOperation(value = "油品入库-详情", tags = {"入库管理-油品入库"})
    @GetMapping(value = "/input/other-inputs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        otherInputService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 油品入库-回填出库物料【批次、批次特性】
     *
     * @param po 入库物料信息
     * @param ctx 入参上下文 {"po":"入库物料信息"}
     */
    @ApiOperation(value = "油品入库-回填出库物料属性值", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/set-mat-info", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptInputItemDTO>> setMatInfo(@RequestBody BizReceiptInputHeadDTO po,
        BizContext ctx) {
        otherInputService.setMatInfo(ctx);
        MultiResultVO<BizReceiptInputItemDTO> inputItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(inputItemDTOList);
    }

    /**
     * 油品入库-保存
     *
     * @param po 保存油品入库表单参数
     * @param ctx 入参上下文 {"po":"保存油品入库表单参数"}
     */
    @ApiOperation(value = "油品入库-保存", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        otherInputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 油品入库-提交
     *
     * @param po 提交油品入库表单参数
     * @param ctx 入参上下文 {"po":"提交油品入库表单参数"}
     */
    @ApiOperation(value = "油品入库-提交", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        otherInputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_SUBMIT_SUCCESS, code);
    }

    /**
     * 油品入库-提交
     *
     * @param po 提交油品入库表单参数
     * @param ctx 入参上下文 {"po":"提交油品入库表单参数"}
     */
    @ApiOperation(value = "油品入库-提交", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/submitSameTime", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submitSameTime(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        otherInputService.submitSameTime(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_SUBMIT_SUCCESS, code);
    }

    /**
     * 验收入库-过账
     *
     * @param po 保存验收入库表单参数
     * @param ctx 入参上下文 {"po":"保存验收入库表单参数"}
     */
    @ApiOperation(value = "油品入库-过账", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> post(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        otherInputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 油品入库-冲销
     *
     * @param po 油品入库冲销表单参数
     * @param ctx 入参上下文 {"po":"油品入库冲销表单参数"}
     */
    @ApiOperation(value = "油品入库-冲销", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInputWriteOffPO po, BizContext ctx) {
        otherInputService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 油品入库单-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "油品入库单-删除", tags = {"入库管理-油品入库"})
    @DeleteMapping("/input/other-inputs/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        otherInputService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OTHER_INPUT_DELETE_SUCCESS, po.getReceiptCode());
    }


    /**
     * 油品入库单-物料标签打印
     * @auth zhibo
     * @param po 打印入参
     * @param ctx 请求上下文
     * @return 领料申请单数据
     */
    @ApiOperation(value = "油品入库单-物料标签打印", tags = {"入库管理-油品入库单"})
    @PostMapping(value = "/input/other-inputs/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptInputHeadDTO> po, BizContext ctx) {
        otherInputService.boxApplyLabelPrint(ctx);
        List<LabelReceiptInputBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "获取领料出库单", tags = {"入库管理-油品入库单"})
    @PostMapping(value = "/input/other-inputs/get-mat-output-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptReturnPreVO>> getMatOutputList(@RequestBody PreReceiptQueryPO po, BizContext ctx) {
        otherInputService.getMatOutputList(ctx);
        MultiResultVO<BizReceiptReturnPreVO> voList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(voList);
    }


    /**
     * 导出油品入库
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 油品入库单分页
     */
    @ApiOperation(value = "油品入库-导出", tags = {"入库管理-油品入库"})
    @PostMapping(value = "/input/other-inputs/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@RequestBody BizReceiptInputSearchPO po,
                                                                   BizContext ctx) {
        otherInputService.export(ctx);
        return  BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

}
