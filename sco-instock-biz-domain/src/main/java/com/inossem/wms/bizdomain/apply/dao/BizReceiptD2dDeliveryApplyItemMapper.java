package com.inossem.wms.bizdomain.apply.dao;

import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptD2dDeliveryApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptD2dDeliveryApplyItemExportVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BizReceiptD2dDeliveryApplyItemMapper extends WmsBaseMapper<BizReceiptD2dDeliveryApplyItem> {

    /**
     * 根据抬头单据id列表查询门到门送货导出清单
     * @param idList 抬头单据id列表
     * @return 门到门送货导出清单
     */
    List<BizReceiptD2dDeliveryApplyItemExportVO> selectExportVOByHeadIdList(@Param("idList") List<Long> idList);

}
