package com.inossem.wms.bizdomain.input.dao;

import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputBin;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 入库单配货明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
public interface BizReceiptInputBinMapper extends WmsBaseMapper<BizReceiptInputBin> {

    /**
     * item物理删除
     *
     * @param headId 主表主键
     */
    void deleteByHeadId(Long headId);

    /**
     * 根据headId和ItemId物理删除
     * @param headId
     * @param itemIds
     */
    void deleteByHeadIdAndItemId(@Param("headId")Long headId, @Param("itemIds")List<Long> itemIds);
}
