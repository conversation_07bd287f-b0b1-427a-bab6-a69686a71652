package com.inossem.wms.bizdomain.input.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.feign.output.OutputFeignApi;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.input.service.component.inputbase.InputComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.OtherInputMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.OtherInputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadCallbackVO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputOilsExportVO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import com.inossem.wms.common.model.bizdomain.returns.vo.BizReceiptReturnPreVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.PreReceiptQueryPO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 油品入库组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class OtherInputComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private InputComponent inputComponent;

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private OtherInputMoveTypeComponent otherInputMoveTypeComponent;

    @Autowired
    private OtherInputWriteOffMoveTypeComponent otherInputWriteOffMoveTypeComponent;
    @Autowired
    protected OutputFeignApi outputFeignApi;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    protected BatchInfoService bizBatchInfoService;
    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;
    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;

    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    protected I18nTextCommonService i18nTextCommonService;

    /**
     * 页面初始化: 1、设置油品入库【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】 3、设置扩展功能【单据流】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"油品入库","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInputHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptInputHeadDTO().setReceiptType(EnumReceiptType.STOCK_INPUT_OTHER.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
     //  老毕说的 去掉保存  防止pda同时模式保存web先作业模式 差异
     //       new ExtendVO().setRelationRequired(true), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
            new ExtendVO().setRelationRequired(true), new ButtonVO().setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 油品入库单-分页
     *
     * @in ctx 入参 {@link BizReceiptInputSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO<BizReceiptInputHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = this.setQueryWrapper(po,user);
        // 分页处理
        IPage<BizReceiptInputHeadVO> page = po.getPageObj(BizReceiptInputHeadVO.class);
        // 获取油品入库单
        bizReceiptInputHeadDataWrap.getOtherInputList(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 油品入库单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"油品入库单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取油品入库单
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizOtherInputHeadDTO =
            UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizOtherInputHeadDTO);

        inputComponent.setPrintInfo(bizOtherInputHeadDTO);

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(bizOtherInputHeadDTO);
        // 设置油品入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizOtherInputHeadDTO, new ExtendVO(), buttonVO));
    }


    /**
     * 按钮组  老毕说的 去掉保存 冲销 防止pda同时模式保存web先作业模式 差异
     *
     * @param headDTO 入库单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInputHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 已提交 -【删除】
            return buttonVO.setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            // 已记账 -【冲销、打印】
            return buttonVO.setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
            // 作业中 -【冲销】

            buttonVO.setButtonPrint(true);
            return buttonVO;
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 -【冲销、打印】

            return buttonVO.setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus)) {
            // 已作业 -【过账】
            return buttonVO.setButtonPost(true);
        }
        return buttonVO;
    }

    /**
     * 保存油品入库前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的油品入库单}
     */
    public void checkSaveOtherInput(BizContext ctx) {
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验行项目是都为空
        inputComponent.checkEmptyItem(po);
    }

    /**
     * 提交油品入库单前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的油品入库单}
     */
    public void checkSubmitOtherInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验行项目是都为空 ******** */
        inputComponent.checkEmptyItem(po);
        /* ******** 校验油品入库单行项目相关数量开始 ******** */
        inputComponent.checkEmptyItemQty(po);
        /* ******** 提交前校验物料、库存地点是否冻结 ******** */
        inputComponent.checkFreeze(po);
    }

    /**
     * 提交油品入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的油品入库单}
     * @out ctx 出参 {"stockInputCode" : "油品入库单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitOtherInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存油品入库单
        inputComponent.saveInput(ctx);
    }

    /**
     * 油品入库过账前数据校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要过账的油品入库单}
     */
    public void checkOtherInputPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性及父子属性
        dataFillService.fillAttr(inputHeadDTO);
        // 校验数据
        inputComponent.checkEmptyItem(inputHeadDTO);
        // 校验可过账的状态
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        if (!canRePostStatusSet.contains(inputHeadDTO.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
        }
    }

    /**
     * 生成ins凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "油品入库单}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证}
     */
    public void generateInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成ins凭证
            insMoveTypeDTO = otherInputMoveTypeComponent.generateInsDocToPost(headDTO);
            // 过账前的校验和数量计算
            stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        } catch (Exception e) {
            log.error("入库单{}生成ins凭证，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 更新单据 - 未同步
            inputComponent.updateStatus(headDTO, headDTO.getItemList(),
                EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            if (e instanceof WmsException) {
                throw new WmsException(((WmsException)e).getErrorCode(), ((WmsException)e).getArgs());
            } else {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
            }
        }
        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 油品入库冲销前校验
     *
     * @in ctx 入参 {@link BizReceiptInputWriteOffPO : "油品入库冲销入参"}
     * @out ctx 出参 {@link BizReceiptInputHeadDTO : "油品入库单"}
     */
    public void checkOtherInputWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilNumber.isEmpty(po.getHeadId()) && UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单行项目
        List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptInputItemDTO> inputItemDTOList =
            UtilCollection.toList(inputItemList, BizReceiptInputItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(inputItemDTOList);
        // 校验行项目单据类型及状态
        Set<String> ridSet = inputItemDTOList.stream().filter(e -> !this.itemCanWriteOff(e))
            .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (ridSet.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, ridSet.toString());
        }
        // 冲销标识等于1或者过账标识等于0
        Set<String> isWriteOff = inputItemDTOList.stream()
            .filter(e -> EnumRealYn.TRUE.getIntValue().equals(e.getIsWriteOff())
                || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
            .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (isWriteOff.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, isWriteOff.toString());
        }
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        // 转dto
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 设置冲销标识
        inputItemDTOList.forEach(itemDTO -> itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        inputHeadDTO.setItemList(inputItemDTOList);
        // 设置要冲销的油品入库单到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, inputHeadDTO);
    }

    /**
     * 生成ins冲销过账凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "油品入库冲销入参"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成凭证
            insMoveTypeDTO = otherInputWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 油品入库单上架回调校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO:"入库单上架回调入参"}
     */
    public void checkTaskOtherInputCallback(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验入参
        if (null == vo) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验行项目
        if (UtilCollection.isEmpty(vo.getInputItemCallbackVoList())) {
            inputComponent.inputHeadCallbackToFail(vo);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 校验单据类型
        if (!EnumReceiptType.STOCK_INPUT_OTHER.getValue().equals(vo.getReceiptType())) {
            inputComponent.inputHeadCallbackToFail(vo);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FALSE);
        }
    }

    /**
     * 删除校验
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "油品入库单删除入参"}
     * @out ctx 出参 {@link BizReceiptInputDeletePO : "如果是全部删除，设置行项目id集合"}
     */
    public void checkDeleteOtherInput(BizContext ctx) {
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单信息
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(inputHeadDTO);
        /* ******** 校验油品入库单head ******** */
        if (UtilObject.isNotNull(inputHead)) {
            if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(inputHead.getReceiptStatus())
                && !EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(inputHead.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(
                inputHeadDTO.getItemList().stream().map(BizReceiptInputItemDTO::getId).collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 删除油品入库单
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "其他单行删除入参"}
     */
    public void deleteOtherInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除油品入库单 ******** */
        if (po.isDeleteAll()) {
            // 删除油品入库单head
            bizReceiptInputHeadDataWrap.removeById(po.getHeadId());
            // 删除油品入库单item
            UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptInputItem::getHeadId, po.getHeadId());
            bizReceiptInputItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.STOCK_INPUT_OTHER.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除油品入库单item
            bizReceiptInputItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 冲销行项目校验
     *
     * @param inputItemDTO BizOtherInputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptInputItemDTO inputItemDTO) {
        Integer receiptType = inputItemDTO.getReceiptType();
        Integer receiptStatus = inputItemDTO.getReceiptStatus();
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus))
            && EnumReceiptType.STOCK_INPUT_OTHER.getValue().equals(receiptType);
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return QueryWrapper<BizStockInputHead>
     */
    private WmsQueryWrapper<BizReceiptInputSearchPO> setQueryWrapper(BizReceiptInputSearchPO po,CurrentUser user) {
        if (null == po) {
            po = new BizReceiptInputSearchPO();
        }
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        Date postCreateTime = null;
        if (UtilObject.isNotNull(po.getPostCreateTime())) {
            postCreateTime = UtilLocalDateTime.getStartTime(po.getPostCreateTime());
        }
        Date postEndTime = null;
        if (UtilObject.isNotNull(po.getPostEndTime())) {
            postEndTime = UtilLocalDateTime.getEndTime(po.getPostEndTime());
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = new WmsQueryWrapper<>();
        // 入库单据号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
            BizReceiptInputHead.class, po.getReceiptCode());
        // 单据类型
        wrapper.lambda().eq(Boolean.TRUE, BizReceiptInputSearchPO::getReceiptType, BizReceiptInputHead.class,
            EnumReceiptType.STOCK_INPUT_OTHER.getValue());
        // 单据状态
        wrapper.lambda().in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
            BizReceiptInputSearchPO::getReceiptStatus, BizReceiptInputHead.class, po.getReceiptStatusList());
        wrapper.lambda().in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptInputSearchPO::getLocationId,
                BizReceiptInputItem.class,locationIdList);
        // 凭证创建时间
        wrapper.lambda().between((UtilObject.isNotNull(postCreateTime)), BizReceiptInputSearchPO::getDocDate,
            BizReceiptInputItem.class, postCreateTime, postEndTime);
        // 物料凭证号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatDocCode()), BizReceiptInputSearchPO::getMatDocCode,
            BizReceiptInputItem.class, po.getMatDocCode());
        // 物料编码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptInputSearchPO::getMatCode,
                DicMaterial.class, po.getMatCode());
        // 物料描述
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatName()), BizReceiptInputSearchPO::getMatName,
                DicMaterial.class, po.getMatName());
        // 创建人
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptInputSearchPO::getUserName, SysUser.class, po.getCreateUserName());
        // 创建时间
        wrapper.lambda().between(createTimeIsNotNull(po),BizReceiptInputSearchPO::getCreateTime,BizReceiptInputHead.class,po.getStartTime(),po.getEndTime());
        return wrapper.setEntity(po);
    }

    private boolean createTimeIsNotNull(BizReceiptInputSearchPO po){
        return Objects.nonNull(po.getStartTime()) && Objects.nonNull(po.getEndTime());
    }



    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptInputHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }


    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptInputHeadDTO headDTO = (BizReceiptInputHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<BizReceiptInputItemDTO> itemDTOList = headDTO.getItemList();

        itemDTOList.forEach(itemDTO->{
            // 生成标签编码
            String labelCode =
                    bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());

            // 标签打印数据
            this.setPrintLabelData(receiptInputBoxes, itemDTO, labelCode);

            // 设置要更新的批次信息数据
            itemDTO.getBizBatchInfoDTO().setInspectHeadId(itemDTO.getHeadId());
            itemDTO.getBizBatchInfoDTO().setInspectItemId(itemDTO.getId());
            itemDTO.getBizBatchInfoDTO()
                    .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getBizBatchInfoDTO().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
            itemDTO.getBizBatchInfoDTO().setTagType(2);
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());

            // 设置需要保存的标签数据
            BizLabelDataDTO label = BizLabelDataDTO.builder()
                    .id(null)
                    .matId(itemDTO.getMatId())
                    .ftyId(itemDTO.getFtyId())
                    .locationId(itemDTO.getLocationId())
                    .batchId(itemDTO.getBizBatchInfoDTO().getId())
                    .binId(itemDTO.getBinId())
                    .whId(itemDTO.getWhId())
                    .typeId(itemDTO.getTypeId())
                    .labelCode(labelCode)
                    .snCode(labelCode)
                    .qty(itemDTO.getQty())
                    .labelType(itemDTO.getBizBatchInfoDTO().getTagType())
                    .receiptHeadId(itemDTO.getHeadId())
                    .receiptItemId(itemDTO.getId())
                    .receiptType(itemDTO.getReceiptType())
                    .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                    .preReceiptItemId(itemDTO.getPreReceiptItemId())
                    .preReceiptType(itemDTO.getPreReceiptType())
                    .build();
            labelDataList.add(label);

        });

        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));
            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 设置标签打印数据
     * @param receiptInputBoxes 装载打印机打印数据
     * @param itemDTO 要打印的验收单行项目
     * @param labelCode rfid 编码
     * @return
     */
    private void setPrintLabelData(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {

        // 单品打印将行项目拆分
        if (itemDTO.getIsSingle().equals("单品")){
            int qty = itemDTO.getQty().intValue();
            for (int i = 0; i < qty; i++) {
                printCount(receiptInputBoxes, itemDTO, labelCode);
            }
        }else { // 批次打印不需要拆分
            printCount(receiptInputBoxes, itemDTO, labelCode);
        }

    }

    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCount(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(itemDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(itemDTO.getTagType());
        labelReceiptInputBox.setIsSingle(itemDTO.getIsSingle());
        labelReceiptInputBox.setBatchCode(itemDTO.getBizBatchInfoDTO().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setLifetimeDate(itemDTO.getBizBatchInfoDTO().getLifetimeDate());
        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (itemDTO.getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }
        // 批次 + 普通标签不需要生成rfid,其他生成rfid
//        if (!(EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsSingle()) && EnumTagType.GENERAL.getValue().equals(itemDTO.getTagType()))) {
//            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
//        } else {
//            labelReceiptInputBox.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
//        }
        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        receiptInputBoxes.add(labelReceiptInputBox);
    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptInputHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptInputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }


    public void getMatOutputList(BizContext ctx) {
        // 入参上下文
        PreReceiptQueryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 库存地点权限
        List<DicStockLocationDTO> locationDTOList = bizCommonService.getLocationDTOList(ctx.getCurrentUser().getId());
        po.setLocationIdList(locationDTOList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList()));
        // 查询出库单据类型
        List<Integer> outputReceiptTypeList = new ArrayList<>();
        outputReceiptTypeList.add(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
        po.setOutputReceiptTypeList(outputReceiptTypeList);
        // feign查询 出库单行项目
        List<BizReceiptOutputItemDTO> outputItemDTOList = outputFeignApi.getOutputReceiptItemList(po);
        if (UtilCollection.isEmpty(outputItemDTOList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>());
            return;
        }
        // 填充 出库单创建人
        dataFillService.fillRlatAttrDataList(outputItemDTOList);
        // 组装数据
        List<BizReceiptReturnPreVO> bizReceiptReturnPreVOList = new ArrayList<>();
        Map<String, List<BizReceiptOutputItemDTO>> outputMap = outputItemDTOList.stream().collect(Collectors.groupingBy(BizReceiptOutputItemDTO::getReceiptCode));
        for (Map.Entry<String, List<BizReceiptOutputItemDTO>> entry : outputMap.entrySet()) {
            List<BizReceiptReturnItemDTO> itemDTOList = new ArrayList<>();
            List<BizReceiptOutputItemDTO> dtoList = entry.getValue();
            for (BizReceiptOutputItemDTO outputItemDTO : dtoList) {
                BizReceiptReturnItemDTO itemDTO = UtilBean.newInstance(outputItemDTO, BizReceiptReturnItemDTO.class);
                itemDTO.setId(null);
                itemDTO.setHeadId(null);
                itemDTO.setPreReceiptHeadId(outputItemDTO.getHeadId());
                itemDTO.setPreReceiptItemId(outputItemDTO.getId());
                itemDTO.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
                itemDTO.setPreReceiptRid(outputItemDTO.getRid());
                itemDTO.setPreReceiptCode(entry.getKey());
                itemDTO.setReceiptRemark(outputItemDTO.getReceiptRemark());
                itemDTO.setPreReceiptCreateUserName(outputItemDTO.getCreateUserName());
                itemDTO.setPreReceiptCreateTime(outputItemDTO.getCreateTime());
                itemDTO.setPreReceiptQty(outputItemDTO.getQty());
                itemDTO.setSubmitQty(BigDecimal.ZERO);
                itemDTO.setQty(BigDecimal.ZERO);
                itemDTO.setTaskQty(BigDecimal.ZERO);
                itemDTO.setReferReceiptHeadId(outputItemDTO.getHeadId());
                itemDTO.setReferReceiptItemId(outputItemDTO.getId());
                itemDTO.setReferReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTO.setMatDocCode(null);
                itemDTO.setMatDocRid(null);
                itemDTO.setMatDocYear(null);
                // 可操作数量 = 出库数量 - 已油品入库数量
                BigDecimal availableQty = outputItemDTO.getQty().subtract(outputItemDTO.getInputOtherQty());
                if (availableQty.compareTo(BigDecimal.ZERO) < 0) {
                    itemDTO.setAvailableQty(BigDecimal.ZERO);
                    itemDTO.setQty(BigDecimal.ZERO);
                } else {
                    itemDTO.setAvailableQty(availableQty);
                    itemDTO.setQty(availableQty);
                }
                itemDTO.setWhId(null);
                itemDTO.setLocationId(null);
                itemDTO.setFtyId(null);
                BizBatchInfoDTO batchInfo = bizBatchInfoService.getBatchInfoDto(outputItemDTO.getBatchId());
                batchInfo.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_OTHER_INPUT.getValue());
                batchInfo.setBatchCode(null);
                batchInfo.setId(null);
                itemDTO.setBatchInfo(batchInfo);
                itemDTO.setBatchId(null);
                itemDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_OTHER_INPUT.getValue());
                itemDTOList.add(itemDTO);
            }
            if (UtilCollection.isEmpty(itemDTOList)) {
                continue;
            }
            BizReceiptReturnPreVO bizReceiptReturnPreVO = new BizReceiptReturnPreVO();
            bizReceiptReturnPreVO.setPreReceiptCode(entry.getKey());
            bizReceiptReturnPreVO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            bizReceiptReturnPreVO.setReceiptType(EnumReceiptType.STOCK_INPUT_OTHER.getValue());
            bizReceiptReturnPreVO.setReceiptRemark(itemDTOList.get(0).getReceiptRemark());
            bizReceiptReturnPreVO.setChildren(itemDTOList);
            bizReceiptReturnPreVO.setCreateUserName(itemDTOList.get(0).getCreateUserName());
            bizReceiptReturnPreVO.setCreateTime(itemDTOList.get(0).getCreateTime());
            bizReceiptReturnPreVO.setHeadRemark(dtoList.get(0).getHeadRemark());
            bizReceiptReturnPreVOList.add(bizReceiptReturnPreVO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(bizReceiptReturnPreVOList));
    }

    public void updateInputOtherQty(BizContext ctx) {
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<BizReceiptOutputItem> bizReceiptOutputItemList = new ArrayList<>();
        headDTO.getItemList().forEach(itemDTO -> {
            BizReceiptOutputItem outputItem = bizReceiptOutputItemDataWrap.getById(itemDTO.getPreReceiptItemId());
            if (UtilObject.isNotNull(outputItem)) {
                outputItem.setInputOtherQty(itemDTO.getQty().add(outputItem.getInputOtherQty()));
                bizReceiptOutputItemList.add(outputItem);
            }
        });

        bizReceiptOutputItemDataWrap.updateBatchById(bizReceiptOutputItemList);
    }

    public void setWh(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        for (BizReceiptInputItemDTO bizReceiptInputItemDTO : po.getItemList()) {
            DicStockLocationDTO dicStockLocationDTO = dictionaryService.getLocationCacheById(bizReceiptInputItemDTO.getLocationId());
            if (UtilObject.isNotNull(dicStockLocationDTO)) {
                bizReceiptInputItemDTO.setWhId(dicStockLocationDTO.getWhId()).setWhCode(dicStockLocationDTO.getWhCode());
            }
        }
    }

    /**
     * 导出
     */
    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("油品入库"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);


        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装查询条件
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = this.setQueryWrapper(po,user);
        List<BizReceiptInputOilsExportVO> list = bizReceiptInputItemDataWrap.selectExportItemList(wrapper);
        dataFillService.fillAttr(list);

        String langCode = this.getLangCodeFromRequest();

        for (BizReceiptInputOilsExportVO exportVo : list) {
            exportVo.setReceiptStatusI18n(i18nTextCommonService.getNameMessage(langCode, "receiptStatus", exportVo.getReceiptStatus().toString()));
        }
        UtilExcel.writeExcel(BizReceiptInputOilsExportVO.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }
}
