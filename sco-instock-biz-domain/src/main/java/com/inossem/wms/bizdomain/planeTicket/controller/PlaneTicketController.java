package com.inossem.wms.bizdomain.planeTicket.controller;

import com.inossem.wms.bizdomain.planeTicket.service.biz.PlaneTicketService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.planeTicket.dto.BizReceiptPlaneTicketHeadDTO;
import com.inossem.wms.common.model.bizdomain.planeTicket.po.BizReceiptPlaneTicketHeadPO;
import com.inossem.wms.common.model.bizdomain.planeTicket.vo.BizReceiptPlaneTicketHeadVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 机票表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@RestController
@Api(tags = "机票")
public class PlaneTicketController {

    @Autowired
    protected PlaneTicketService planeTicketService;

    @ApiOperation(value = "初始化", tags = {"机票"})
    @PostMapping(value = "/planeTicket/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptPlaneTicketHeadDTO>> init(BizContext ctx) {
        planeTicketService.init(ctx);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "初始化-结算", tags = {"机票"})
    @PostMapping(value = "/planeTicket/initSettlement", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptPlaneTicketHeadDTO>> initSettlement(BizContext ctx) {
        planeTicketService.initSettlement(ctx);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "分页", tags = {"机票"})
    @PostMapping(value = "/planeTicket/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptPlaneTicketHeadVO>> getPage(@RequestBody BizReceiptPlaneTicketHeadPO po, BizContext ctx) {
        planeTicketService.getPage(ctx);
        PageObjectVO<BizReceiptPlaneTicketHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "详情", tags = {"机票"})
    @GetMapping(value = "/planeTicket/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptPlaneTicketHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        planeTicketService.getInfo(ctx);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(dto);
    }

    @ApiOperation(value = "保存", tags = {"机票"})
    @PostMapping(value = "/planeTicket/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptPlaneTicketHeadDTO po, BizContext ctx) {
        planeTicketService.save(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "提交", tags = {"机票"})
    @PostMapping(value = "/planeTicket/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptPlaneTicketHeadDTO po, BizContext ctx) {
        planeTicketService.submit(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "结算", tags = {"机票"})
    @PostMapping(value = "/planeTicket/settlement", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> settlement(@RequestBody BizReceiptPlaneTicketHeadDTO po, BizContext ctx) {
        planeTicketService.settlement(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "删除", tags = {"机票"})
    @GetMapping("/planeTicket/delete/{id}")
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        planeTicketService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "导入", tags = {"机票"})
    @PostMapping(value = "/planeTicket/importExcel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptPlaneTicketHeadDTO>> importExcel(@RequestPart("file") MultipartFile file, BizContext ctx) {
        planeTicketService.importExcel(ctx);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(dto);
    }

    @ApiOperation(value = "导出", tags = {"机票"})
    @PostMapping(value = "/planeTicket/exportExcel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportExcel(@RequestBody BizReceiptPlaneTicketHeadDTO po, BizContext ctx) {
        planeTicketService.exportExcel(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "未结算的人员查询接口", tags = {"机票"})
    @PostMapping(value = "/planeTicket/getItemList", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptPlaneTicketHeadDTO>> gegetItemListtItem(@RequestBody BizReceiptPlaneTicketHeadPO po, BizContext ctx) {
        planeTicketService.getItemList(ctx);
        BizResultVO<BizReceiptPlaneTicketHeadDTO> dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(dto);
    }
}

