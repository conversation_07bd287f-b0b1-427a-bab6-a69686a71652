package com.inossem.wms.bizdomain.apply.service.component;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpWbsService;
import com.inossem.wms.bizbasis.erp.service.biz.ReceiveReceiptService;
import com.inossem.wms.bizbasis.erp.service.biz.ReserveReceiptService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReceiveReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReceiveReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyItemDataWrap;
import com.inossem.wms.bizdomain.output.service.biz.MaterialOutputService;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputInfoDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.virtual.service.datawrap.BizReceiptVirtualOutputApplyHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumReturnOldType;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.apply.EnumReceiveType;
import com.inossem.wms.common.enums.auth.EnumUserJob;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumDeptTypes;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.rel.entity.SysUserDeptOfficeRel;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplyQueryListPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchReqPlanPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputInfoDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputInfo;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.output.po.BizMatApplyLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.virtual.entity.BizReceiptVirtualOutputApplyHead;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.enums.ReturnOldTypeMapVO;
import com.inossem.wms.common.model.common.enums.apply.ReceiveTypeMapVO;
import com.inossem.wms.common.model.erp.dto.ErpReceiveReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.entity.ErpReceiveReceiptHead;
import com.inossem.wms.common.model.erp.entity.SapWbs;
import com.inossem.wms.common.model.erp.po.ReserveReceiptCreatePO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.corp.entity.DicCorp;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptRegisterBox;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSequence;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wang
 * <p>领料出库单组件库</p>
 * @date 2022/5/7 14:28
 */
@Service
@Slf4j
public class MaterialOrderOutApplyComponent {

    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private BizReceiptApplyItemDataWrap bizReceiptApplyItemDataWrap;
    @Autowired
    private BizReceiptOutputInfoDataWrap bizReceiptOutputInfoDataWrap;
    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;
    @Autowired
    private OutputComponent outputComponent;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private BatchImgService batchImgService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private ReserveReceiptService reserveReceiptService;
    @Autowired
    @Lazy
    private MaterialOutputService materialOutputService;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private ErpWbsService erpWbsService;
    @Autowired
    private ReceiveReceiptService receiveReceiptService;
    @Autowired
    private ErpReceiveReceiptItemDataWrap erpReceiveReceiptItemDataWrap;
    @Autowired
    private ErpReceiveReceiptHeadDataWrap erpReceiveReceiptHeadDataWrap;
    @Autowired
    private BizReceiptVirtualOutputApplyHeadDataWrap bizReceiptVirtualOutputApplyHeadDataWrap;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;

    /**
     * 获取退旧类型下拉
     */
    public void getReturnOldType(BizContext ctx) {
        MultiResultVO<ReturnOldTypeMapVO> vo = new MultiResultVO<>(EnumReturnOldType.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<BizReceiptApplyHeadDTO> resultVO =
                new BizResultVO<>(applyHeadDTO
                        .setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue())
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserName(user.getUserName())
                        .setMatDeptId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptId())
                        .setDeptId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptId())
                        .setDeptCode(ctx.getCurrentUser().getUserDeptList().get(0).getDeptCode())
                        .setDeptName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptName()),
                        new ExtendVO(), buttonVO);
        if (UtilCollection.isNotEmpty(user.getUserDeptList())) {
            resultVO.getHead().setCreateDeptId(user.getUserDeptList().get(0).getDeptId());
            resultVO.getHead().setCreateOfficeId(user.getUserDeptList().get(0).getDeptOfficeId());
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 获取领料单出库详情
     *
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        BizReceiptApplyHeadDTO applyHeadDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        // 属性填充
        dataFillService.fillAttr(applyHeadDTO);
        Long ftyId = applyHeadDTO.getItemList().get(0).getFtyId();
        DicFactoryDTO ftyCache = dictionaryService.getFtyCacheById(ftyId);
        DicCorp corpCache = dictionaryService.getCorpCacheById(ftyCache.getCorpId());
        applyHeadDTO.setCorpName(corpCache==null? null : corpCache.getCorpName());
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        itemDTOList.stream().forEach(bizReceiptApplyItemDTO -> {
            if (bizReceiptApplyItemDTO.getIsReturnFlag()==0){
                bizReceiptApplyItemDTO.setIsReturnFlagStr("否");
            }else {
                bizReceiptApplyItemDTO.setIsReturnFlagStr("是");
            }
        });
        String applyDeptName = applyHeadDTO.getApplyDeptName();
        if (StringUtils.isBlank(applyDeptName)) {
            Long createUserId = applyHeadDTO.getCreateUserId();
            List<MetaDataDeptOfficePO> deptList = sysUserDeptOfficeRelDataWrap.getUserDept(createUserId);
            if (!CollectionUtils.isEmpty(deptList)) {
                MetaDataDeptOfficePO defaultDept = deptList.get(0);
                String deptName = defaultDept.getDeptName();
                applyHeadDTO.setApplyDeptName(deptName);
            }
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(applyHeadDTO);

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(applyHeadDTO, new ExtendVO(), buttonVO));
    }


    /**
     * 获取领料单出库详情
     *
     * @param ctx
     */
    public void reqPlanGetInfo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        BizReceiptApplyHeadDTO applyHeadDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        // 属性填充
        dataFillService.fillAttr(applyHeadDTO);
        Long ftyId = applyHeadDTO.getItemList().get(0).getFtyId();
        DicFactoryDTO ftyCache = dictionaryService.getFtyCacheById(ftyId);
        DicCorp corpCache = dictionaryService.getCorpCacheById(ftyCache.getCorpId());
        applyHeadDTO.setCorpName(corpCache==null? null : corpCache.getCorpName());
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        itemDTOList.stream().forEach(bizReceiptApplyItemDTO -> {
            if (bizReceiptApplyItemDTO.getIsReturnFlag()==0){
                bizReceiptApplyItemDTO.setIsReturnFlagStr("否");
            }else {
                bizReceiptApplyItemDTO.setIsReturnFlagStr("是");
            }
        });
        String applyDeptName = applyHeadDTO.getApplyDeptName();
        if (StringUtils.isBlank(applyDeptName)) {
            Long createUserId = applyHeadDTO.getCreateUserId();
            List<MetaDataDeptOfficePO> deptList = sysUserDeptOfficeRelDataWrap.getUserDept(createUserId);
            if (!CollectionUtils.isEmpty(deptList)) {
                MetaDataDeptOfficePO defaultDept = deptList.get(0);
                String deptName = defaultDept.getDeptName();
                applyHeadDTO.setApplyDeptName(deptName);
            }
        }


        // 属性填充
        ErpReceiveReceiptHead erpReceiveReceiptHead = erpReceiveReceiptHeadDataWrap.getById(itemDTOList.get(0).getPreReceiptHeadId());
        if (UtilObject.isNotNull(erpReceiveReceiptHead)) {
            applyHeadDTO.setErpCreateUserCode(erpReceiveReceiptHead.getErpCreateUserCode());
        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.reqPlanSetButton(applyHeadDTO);

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(applyHeadDTO, new ExtendVO(), buttonVO));
    }
    /**
     * 获取领料出库单申请分页
     *
     * @param ctx
     */
    public void gePage(BizContext ctx) {
        BizReceiptApplyQueryListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 物料编码检索条件
        List<Long> headIds = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (UtilNumber.isNotEmpty(matId)) {
                List<BizReceiptApplyItem> receiptApplyItems = bizReceiptApplyItemDataWrap.list(new QueryWrapper<BizReceiptApplyItem>()
                        .lambda()
                        .eq(BizReceiptApplyItem::getMatId, matId)
                );
                headIds = receiptApplyItems.stream().map(BizReceiptApplyItem::getHeadId).collect(Collectors.toList());
            } else {
                // 设置了物料编码查询条件，但没有符合的物料时，直接返回空列表
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(new ArrayList<>(), 0L));
                return;
            }
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptApplyQueryListPO> wmsQueryWrapper = new WmsQueryWrapper<>();
        wmsQueryWrapper.lambda()
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getReceiptType, BizReceiptApplyHead.class, EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue())
                .eq(Boolean.TRUE, BizReceiptApplyQueryListPO::getIsDelete, BizReceiptApplyHead.class, 0)
                .eq(UtilObject.isNotNull(po.getReceiveType()), BizReceiptApplyQueryListPO::getReceiveType, BizReceiptApplyHead.class, po.getReceiveType())
                .in(UtilCollection.isNotEmpty(headIds), BizReceiptApplyQueryListPO::getId, BizReceiptApplyHead.class, headIds)
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptApplyQueryListPO::getReceiptStatus, po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptApplyQueryListPO::getLocationId,
                        BizReceiptApplyItem.class,locationIdList)
                .between(UtilObject.isNotNull(po.getApplyStartTime()) && UtilObject.isNotNull(po.getApplyEndTime()),
                        BizReceiptApplyQueryListPO::getApplyTime, BizReceiptOutputInfo.class, po.getApplyStartTime(), po.getApplyEndTime())
                .between(UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime()),
                        BizReceiptApplyQueryListPO::getCreateTime, BizReceiptApplyHead.class, po.getStartTime(), po.getEndTime())
                .likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptApplyQueryListPO::getReceiptCode, BizReceiptApplyHead.class, po.getReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getRemark()), BizReceiptApplyQueryListPO::getRemark, BizReceiptApplyHead.class, po.getRemark())
                .like(UtilString.isNotNullOrEmpty(po.getUserName()), BizReceiptApplyQueryListPO::getUserName, SysUser.class, po.getUserName())
                .like(UtilString.isNotNullOrEmpty(po.getMatName()), BizReceiptApplyQueryListPO::getMatName, DicMaterial.class, po.getMatName())
        ;
        Page<BizReceiptApplyPageVO> pageObj = (Page<BizReceiptApplyPageVO>) po.getPageObj(BizReceiptApplyPageVO.class);
        pageObj.setOptimizeCountSql(false);
        pageObj.orders().forEach(obj -> {
            // 排序特殊处理
            obj.setColumn(obj.getColumn().replace("create_dept_name", "biz_receipt_apply_head.create_dept_id"));
        });
        bizReceiptApplyHeadDataWrap.getApplyInfoPageVOList(pageObj, wmsQueryWrapper);
        dataFillService.fillRlatAttrDataList(pageObj.getRecords());
        long totalCount = pageObj.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(pageObj.getRecords(), totalCount));
    }


    /**
     * 保存领料出库单信息
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOutInfo(BizContext ctx) {
        boolean isNew = true;
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        CurrentUser user = ctx.getCurrentUser();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        String receiptCode = applyHeadDTO.getReceiptCode();
        String applyCode = applyHeadDTO.getReceiptNum() == null ? null : applyHeadDTO.getReceiptNum();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        // 处理头信息
        if (UtilNumber.isNotEmpty(applyHeadDTO.getId())) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(applyHeadDTO.getId());
            // 根据id更新
            bizReceiptApplyHeadDataWrap.updateDtoById(applyHeadDTO);

            // item物理删除
            QueryWrapper<BizReceiptApplyItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(BizReceiptApplyItem::getHeadId, applyHeadDTO.getId());
            bizReceiptApplyItemDataWrap.physicalDelete(itemQueryWrapper);

            // assemble物理删除
            QueryWrapper<BizReceiptAssemble> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizReceiptAssemble::getReceiptHeadId, applyHeadDTO.getId());
            bizReceiptAssembleDataWrap.physicalDelete(queryWrapperAssemble);

            // outInfo 根据id、headId更新
            QueryWrapper<BizReceiptOutputInfo> outInfoWrapper = new QueryWrapper<>();
            outInfoWrapper.lambda()
                    .eq(BizReceiptOutputInfo::getId, applyHeadDTO.getOutInfoId());
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .build();
            bizReceiptOutputInfoDataWrap.updateDto(bizReceiptOutputInfoDTO, outInfoWrapper);

            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            /* ================ outInfo处理  领料出库单信息 ================ */
            applyCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_APPLY_CODE.getValue());
            // 根据24位的WBS Code 获取 8位的WBS编号 8位的WBS Code是在创建预留单的时候使用
            WmsQueryWrapper<ErpPurchaseReceiptItem> purchaseReceiptItemWmsQueryWrapper = new WmsQueryWrapper<>();
            purchaseReceiptItemWmsQueryWrapper.lambda()
                    .eq(ErpPurchaseReceiptItem::getSpecStockCode, applyHeadDTO.getSpecStockCode());
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(purchaseReceiptItemWmsQueryWrapper);
            if (erpPurchaseReceiptItemList.size() <= 0){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_WBS_CODE_GET_FAILED);
            }
            ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemList.get(0);
//            ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(purchaseReceiptItemWmsQueryWrapper);
            if (UtilString.isNullOrEmpty(erpPurchaseReceiptItem.getWhCodeOut())) {
                log.error("保存领料出库申请单根据24位的WBS Code，从表erp_purchase_receipt_item获取8位的WBS编码失败;保存的领料出库信息为:{}", JSON.toJSONString(applyHeadDTO));
            }
            applyHeadDTO.setWhCodeOut(erpPurchaseReceiptItem.getWhCodeOut());

//            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = applyHeadDTO.getBizReceiptOutputInfoDTO();
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
                    .specStockCode(applyHeadDTO.getSpecStockCode())
                    .specStockName(applyHeadDTO.getSpecStockName())
                    .whCodeOut(applyHeadDTO.getWhCodeOut())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .createTime(UtilDate.getNow())
                    .modifyTime(UtilDate.getNow())
                    .createUserId(user.getCreateUserId())
                    .modifyUserId(user.getModifyUserId())
                    .build();
            bizReceiptOutputInfoDataWrap.saveDto(bizReceiptOutputInfoDTO);
            /* ================ head处理 ================ */
//            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
            receiptCode = bizCommonService.getNextSeqMaterialReq(user,EnumSequenceCode.MATERIAL_REQ.getValue(),applyHeadDTO.getItemList().get(EnumRealYn.FALSE.getIntValue()).getFtyCode());
            applyHeadDTO.setReceiptCode(receiptCode);
            applyHeadDTO.setCreateUserId(createUserId);
            applyHeadDTO.setModifyUserId(createUserId);
            applyHeadDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
            applyHeadDTO.setReceiptStatus(status);
            applyHeadDTO.setOutInfoId(bizReceiptOutputInfoDTO.getId());
            bizReceiptApplyHeadDataWrap.saveDto(applyHeadDTO);

            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }

        // item处理
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            itemDTO.setId(null);
            itemDTO.setHeadId(applyHeadDTO.getId());
            itemDTO.setItemStatus(status);
            itemDTO.setCreateTime(createTime);
            itemDTO.setCreateUserId(createUserId);
            itemDTO.setModifyUserId(user.getId());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);

        // assemble处理
        List<BizReceiptAssembleDTO> assembleList = new ArrayList<>();
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                assembleDTO.setId(null);
                assembleDTO.setReceiptHeadId(applyHeadDTO.getId());
                assembleDTO.setReceiptItemId(itemDto.getId());
                assembleDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
                assembleDTO.setCreateUserId(createUserId);
                assembleDTO.setModifyUserId(user.getId());
                assembleDTO.setSpecType(assembleDTO.getSpecType() == null ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue() : assembleDTO.getSpecType());
                assembleList.add(assembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleList);
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, applyHeadDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, applyHeadDTO.getId());
    }

    /**
     * 提交领料出库单申请
     *
     * @param ctx
     */
    public void submitReceiptApply(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        this.saveOutInfoNew(ctx);
    }


    /**
     * 状态变更已提交
     *
     * @param ctx
     */
    public void updateStatusSubmitted(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
    }

    /**
     * 领料出库单审批操作
     *
     * @param ctx
     */
    public void doOutputInstance(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        // 构建创建预留单的参数
        ReserveReceiptCreatePO reserveReceiptCreatePO = ReserveReceiptCreatePO.builder().bizReceiptApplyHeadDTO(headDTO).build();
        // 调用SAP创建预留单
        // TODO-BO 2022/5/24 应该参考创建物料凭证进行改造一下。。。。
        ReserveReceiptCreatePO sapCreateReserveReceiptInfo = reserveReceiptService.createReserveReceiptItemList(reserveReceiptCreatePO, currentUser);
        // SAP创建预留返回信息
        // 创建预留成功
//        if (sapCreateReserveReceiptInfo.getStatus().equals(Const.ERP_RETURN_TYPE_S)) {
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(sapCreateReserveReceiptInfo.getSuccess())) {
            sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList().forEach(bizReceiptApplyItemDTO -> {
                bizReceiptApplyItemDTO.setAssembleDTOList(new ArrayList<>());
            });
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO());
            BizReceiptApplyHeadDTO receiptApplyHeadDTO = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO();
            List<BizReceiptApplyItemDTO> receiptApplyItemDTOList = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList();
            // 更新数据 将预留单信息 填充当申请表行项目中
            this.saveOutInfo(ctx);
            // 更新状态 已完成
            this.updateStatus(receiptApplyHeadDTO, receiptApplyItemDTOList, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

            /* ================================ 自动创建领料出库单 ================================ */
            List<BizReceiptOutputItemDTO> receiptOutputItemDTOList = new ArrayList<>();
            BizReceiptApplyHeadDTO bizReceiptApplyHeadDTO = reserveReceiptCreatePO.getBizReceiptApplyHeadDTO();
            List<BizReceiptApplyItemDTO> applyHeadDTOItemList = bizReceiptApplyHeadDTO.getItemList();
            // 配置assemble 设置为null
            List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
            // 领料出库单是基于领料申请单进行创建，进行属性填充
            applyHeadDTOItemList.stream().forEach(bizReceiptApplyItemDTO -> {
                BizReceiptOutputItemDTO receiptOutputItemDTO = BizReceiptOutputItemDTO.builder()
                        // 这里没有配置领料出库的行项目的rid
                        .matId(bizReceiptApplyItemDTO.getMatId())
                        .ftyId(bizReceiptApplyItemDTO.getFtyId())
                        .locationId(bizReceiptApplyItemDTO.getLocationId())
                        .whId(bizReceiptApplyItemDTO.getWhId())
                        .preReceiptQty(bizReceiptApplyItemDTO.getQty())
                        .receiptQty(bizReceiptApplyItemDTO.getQty())
                        //操作数量
                        .qty(BigDecimal.ZERO)
                        // 已退库数量
                        .returnQty(BigDecimal.ZERO)
                        // 已作业数量
                        .taskQty(BigDecimal.ZERO)
                        // 已完成数量
                        .finishQty(BigDecimal.ZERO)
                        // 特殊库存标识
                        .specStock(bizReceiptApplyItemDTO.getSpecStock())
                        .unitId(bizReceiptApplyItemDTO.getUnitId())
                        // 前续单据head id
                        .preReceiptHeadId(bizReceiptApplyItemDTO.getHeadId())
                        // 前序单据item表id
                        .preReceiptItemId(bizReceiptApplyItemDTO.getId())
                        // 前序单据类型
                        .preReceiptType(bizReceiptApplyHeadDTO.getReceiptType())
                        .referReceiptHeadId(bizReceiptApplyItemDTO.getPreReceiptHeadId())
                        .referReceiptItemId(bizReceiptApplyItemDTO.getPreReceiptItemId())
                        .referReceiptCode(bizReceiptApplyItemDTO.getPreReceiptCode())
                        .referReceiptType(EnumReceiptType.RESERVE_RECEIPT.getValue())
                        // 行项目状态
                        .itemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                        .createTime(UtilDate.getNow())
                        .createUserId(bizReceiptApplyItemDTO.getCreateUserId())
                        .specStockCode(bizReceiptApplyItemDTO.getSpecStockCode())
                        .assembleDTOList(assembleDTOList)
                        .build();
                receiptOutputItemDTOList.add(receiptOutputItemDTO);
            });
            BizReceiptOutputHeadDTO receiptOutputHeadDTO = BizReceiptOutputHeadDTO.builder()
                    .itemDTOList(receiptOutputItemDTOList)
                    // 配置单据类型
                    .receiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                    .build();

            // 将领料出库信息保存到上下文中
            log.info("领料出库单保存的数据:{}", JSON.toJSONString(receiptOutputHeadDTO));
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptOutputHeadDTO);
            // TODO-BO: 2022/5/17
            //  1.保存领料出库单  这里是不是要判断是否存在 存在则更新，此时是一直追加的状态,
            //  2.创建预留成功需要自动创建领料出库单 这里需要避免失败，打印log 重试
            materialOutputService.save(ctx);
            /* ================================ 自动创建领料出库单 ================================ */
            // 出库配货策略
            outputComponent.autoDistribution(ctx);

        } else {
            BizReceiptApplyHeadDTO receiptApplyHeadDTO = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO();
            List<BizReceiptApplyItemDTO> receiptApplyItemDTOList = sapCreateReserveReceiptInfo.getBizReceiptApplyHeadDTO().getItemList();
            // 更新状态 未同步
            this.updateStatus(receiptApplyHeadDTO, receiptApplyItemDTOList, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            log.warn("预留单{}同步SAP失败，返回信息：{}", headDTO.getId(), reserveReceiptCreatePO.getReturnMessage());
            throw new WmsException(EnumReturnMsg.CREATE_RESERVE_RECEIPT_FAIL, reserveReceiptCreatePO.getReturnMessage());
        }
    }

    /**
     * 开启审批流
     *
     * @param ctx
     */
    private void startProcessInstance(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //启动工作流
        workflowService.startWorkFlow(headDTO.getId(), headDTO.getReceiptCode(), headDTO.getReceiptType(), new HashMap<>());
    }

    /**
     * 生成下游领料出库单
     *
     * @param ctx
     */
    private void createReceiptOutput(BizContext ctx) {
    }

    /**
     * 更新单据，行项目状态 如果不更细单据状态，headDTO参数传null
     *
     * @param headDTO
     * @param itemDTOList
     * @param status
     */
    public void updateStatus(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        if (Objects.isNull(headDTO)) {
            this.updateAppLyReceiptItemStatus(itemDTOList, status);
        } else if (CollectionUtils.isEmpty(itemDTOList)) {
            this.updateApplyReceiptHeadStatus(headDTO, status);
        } else if (!CollectionUtils.isEmpty(itemDTOList)) {
            this.updateAppLyReceiptItemStatus(itemDTOList, status);
            this.updateApplyReceiptHeadStatus(headDTO, status);
        }
    }

    /**
     * 更新单据状态-抬头状态
     *
     * @param headDTO
     * @param status
     */
    private void updateApplyReceiptHeadStatus(BizReceiptApplyHeadDTO headDTO, Integer status) {
        if (Objects.nonNull(headDTO)) {
            // 单据状态
            headDTO.setReceiptStatus(status);
            bizReceiptApplyHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 更新行项目状态
     *
     * @param itemDTOList
     * @param status
     */
    private void updateAppLyReceiptItemStatus(List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach(bizReceiptApplyItemDTO -> bizReceiptApplyItemDTO.setItemStatus(status));
            bizReceiptApplyItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }


    /**
     * 按钮组
     *
     * @param headDTO 申请单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 完成 -【打印】
            return buttonVO.setButtonPrint(true).setButtonDeal(checkIsGenerateMaterialOut(headDTO));
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步
            return buttonVO.setButtonSynchronized(true);
        }
        return buttonVO;
    }

    /**
     * 按钮组
     *
     * @param headDTO 申请单
     * @return 按钮组对象
     */
    public ButtonVO reqPlanSetButton(BizReceiptApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 完成 -【打印】
            return buttonVO.setButtonDeal(checkIsGenerateMaterialOut(headDTO));
        }if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            return buttonVO.setButtonSynchronized(true);
        }
        return buttonVO;
    }

    /**
     * 查询是否生成领料出库单
     * @param headDTO
     * @return
     */
    public boolean checkIsGenerateMaterialOut(BizReceiptApplyHeadDTO headDTO) {
        boolean flag=false ;
        QueryWrapper<BizReceiptOutputItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptOutputItem::getPreReceiptHeadId,headDTO.getId());
        List<BizReceiptOutputItem> itemList = bizReceiptOutputItemDataWrap.list(queryWrapper);
        if(UtilCollection.isEmpty(itemList)) {
            flag=true;
        }
        return flag;
    }

    /**
     * 领料出库单申请回调
     *
     * @param wfReceiptCo
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取领料出库单申请信息
        BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        // 数据类型转换DTO
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptApplyHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);
        // 审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 封装上下文
            BizContext ctxInspect = new BizContext();
            ctxInspect.setCurrentUser(wfReceiptCo.getInitiator());
            ctxInspect.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            // TODO-BO: 2022/5/11 1.自动调用SAP创建预留单
            // TODO-BO: 2022/5/11 2.判断创建预留单是否成功

        }
    }

    /**
     * 删除领料出库单申请
     * @param ctx
     */

    /**
     * 删除单据【非同时模式】
     *
     * @param ctx 上下文
     */
    public void deleteReceipt(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptApplyHeadDTO headDTO = this.getItemListByIdNoDataFill(id);
        // 删除head
        bizReceiptApplyHeadDataWrap.removeById(id);
        // 删除item
        bizReceiptApplyItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptApplyItem>() {

            {
                eq(BizReceiptApplyItem::getHeadId, id);
            }
        });
        // 删除assemble
        bizReceiptAssembleDataWrap.remove(new LambdaQueryWrapper<BizReceiptAssemble>() {

            {
                eq(BizReceiptAssemble::getReceiptHeadId, id);
            }
        });
        // 删除out_info
        bizReceiptOutputInfoDataWrap.removeById(headDTO.getOutInfoId());
        receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "", user.getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }


    /**
     * 根据headId查询出库单列表(不填充)
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptApplyHeadDTO getItemListByIdNoDataFill(Long headId) {
        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        List<BizReceiptApplyItem> itemList = bizReceiptApplyItemDataWrap.list(new LambdaQueryWrapper<BizReceiptApplyItem>() {
            {
                eq(BizReceiptApplyItem::getHeadId, headId);
            }
        });

        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        List<BizReceiptApplyItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptApplyItemDTO.class);
        headDTO.setItemList(itemDTOList);
        return headDTO;
    }

    /**
     * 校验行项目的参数
     *
     * @param ctx
     */
    public void checkSave(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(headDTO) || UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        //需求变更，领用部门变更，字段被移除，为避免后续错误节点过多，前台移除，这里设置个无用值
        //headDTO.setMatDeptId(1L);
        // 判断工厂是否唯一
        ArrayList<BizReceiptApplyItemDTO> distinctFty = headDTO.getItemList().stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(BizReceiptApplyItemDTO::getFtyId))), ArrayList::new));
        if (distinctFty.size()>1){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FTY_DUPLICATE);
        }

        // 手工领料类型下校验是否存在已完成的虚拟入库申请单号
        if (EnumReceiveType.DEPT_REQ_USE.getValue().equals(headDTO.getReceiveType())
                && EnumRealYn.TRUE.getIntValue().equals(headDTO.getIsVirtual())
                && UtilString.isNotNullOrEmpty(headDTO.getVirtualOutputApplyReceiptCode())) {
            bizReceiptVirtualOutputApplyHeadDataWrap.list(new QueryWrapper<BizReceiptVirtualOutputApplyHead>().lambda().
                            eq(BizReceiptVirtualOutputApplyHead::getReceiptCode, headDTO.getVirtualOutputApplyReceiptCode())
                            .eq(BizReceiptVirtualOutputApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()))
                    .stream().findFirst().
                    orElseThrow(() -> new WmsException(StrUtil.format("虚拟出入库申请单号:{}不存在或未完成", headDTO.getVirtualOutputApplyReceiptCode())));
        }

        // 手工领料类型下校验是否存在已完成的紧急领用出库申请单号
        if (EnumReceiveType.DEPT_REQ_USE.getValue().equals(headDTO.getReceiveType())
                && EnumRealYn.TRUE.getIntValue().equals(headDTO.getIsEmergency())
                && UtilString.isNotNullOrEmpty(headDTO.getEmergencyMatOrderApplyReceiptCode())) {
            bizReceiptApplyHeadDataWrap.list(new QueryWrapper<BizReceiptApplyHead>().lambda().
                            eq(BizReceiptApplyHead::getReceiptCode, headDTO.getEmergencyMatOrderApplyReceiptCode())
                            .eq(BizReceiptApplyHead::getReceiptType, EnumReceiptType.STOCK_OUTPUT_EMERGENCY_MAT_REQ_APPLY.getValue())
                            .eq(BizReceiptApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()))
                    .stream().findFirst().
                    orElseThrow(() -> new WmsException(StrUtil.format("紧急领用出库申请单号:{}不存在或未完成", headDTO.getEmergencyMatOrderApplyReceiptCode())));
        }
        // 必填参数校验 、领料人、
        // bug 44046 去掉领料部门领料人
        /*if (UtilNumber.isEmpty(headDTO.getMatReceiverId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }*/
        // 工厂、库存地点、物料编码没有加校验

    }

    /**
     * 获取配货信息(特性库存)【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getItemInfoByFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询特性库存
        BizReceiptAssembleRuleDTO assembleRuleDTO = new BizReceiptAssembleRuleDTO();
        String specStock = Const.STRING_EMPTY;
        // 查询特性code和特性值库存
        if (isQueryFetureCodeAndValue(po)) {
            assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValue(po, null, po.getReceiptType(),
                    po.getFtyId(), po.getLocationId(), po.getMatId(),
                    EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), specStock);
        } else if (isQueryFetureCode(po)) {
            // 领料出库 领料申请出库设置specStock为“Q”
            if (EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue().equals(po.getReceiptType()) ||
                    EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue().equals(po.getReceiptType())) {
                specStock = EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue();
            }
            // 非限制库存
            Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
            // 根据单据类型获取特性
            List<StockBinDTO> stockBinDTOS = null;
            if (UtilCollection.isNotEmpty(po.getAssembleDTOList())) {
                stockBinDTOS = stockCommonService.fillSpecCodeAndValue(po.getAssembleDTOList());
            }
            BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
            pos.setStockStatus(stockStatus);
            pos.setSpecStock(specStock);
            assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(), pos);
        }
        // 若其他行项目无配货则直接返回
        List<BizReceiptOutputItemDTO> itemDTOList = po.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, assembleRuleDTO);
            return;
        }
        List<BizReceiptAssembleDTO> stockQtyList = assembleRuleDTO.getAssembleDTOList();
        // 已配货的物料idList
        List<Long> matIdList = itemDTOList.stream().map(BizReceiptOutputItemDTO::getMatId).collect(Collectors.toList());
        // 同一个物料重新计算数量
        if (matIdList.contains(po.getMatId())) {
            // 保存特性值与此特性值已操作数量
            Map<String, BigDecimal> totalMap = new HashMap<>();
            // 物料工厂库存地点 粗略分组,筛选出物料相同可能特性值不同的list
            Map<String, List<BizReceiptOutputItemDTO>> matMap =
                    itemDTOList.stream().collect(Collectors.groupingBy(a -> getItemInfoGroupKey(po)));
            List<BizReceiptOutputItemDTO> bizReceiptOutputItemDTOS = matMap.get(this.getItemInfoGroupKey(po));
            for (BizReceiptOutputItemDTO bizReceiptOutputItemDTO : bizReceiptOutputItemDTOS) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : bizReceiptOutputItemDTO.getAssembleDTOList()) {
                    BigDecimal qty = bizReceiptAssembleDTO.getQty();
                    if (totalMap.containsKey(bizReceiptAssembleDTO.getSpecValue())) {
                        qty = qty.add(totalMap.get(bizReceiptAssembleDTO.getSpecValue()));
                    }
                    totalMap.put(bizReceiptAssembleDTO.getSpecValue(), qty);
                }
            }
            // 重新赋值库存数量
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : stockQtyList) {
                // 已操作数量
                BigDecimal qty = totalMap.get(bizReceiptAssembleDTO.getSpecValue());
                if (Objects.nonNull(qty)) {
                    BigDecimal stockQty = bizReceiptAssembleDTO.getStockQty().subtract(qty);
                    stockQty = stockQty.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : stockQty;
                    bizReceiptAssembleDTO.setStockQty(stockQty);
                }
            }
        }
        assembleRuleDTO.setAssembleDTOList(stockQtyList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, assembleRuleDTO);
    }


    /**
     * 是否查询特性code和特性值库存
     *
     * @param po 库存特性查询PO
     * @return 是否
     */
    public boolean isQueryFetureCodeAndValue(BizReceiptOutputSearchPO po) {
        Set<Integer> set = new HashSet<>();
        set.add(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_SALE.getValue());
        return set.contains(po.getReceiptType());
    }

    /**
     * 是否查询特性code
     *
     * @param po 库存特性查询PO
     * @return 是否
     */
    public boolean isQueryFetureCode(BizReceiptOutputSearchPO po) {
        Set<Integer> set = new HashSet<>();
        set.add(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_WASTE_MAT.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_WORTHLESS.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_TEMP.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
        set.add(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
        return set.contains(po.getReceiptType());
    }


    /**
     * 获取配货分组条件
     *
     * @param po 查询库存入参
     * @return 分组key
     */
    private String getItemInfoGroupKey(BizReceiptOutputSearchPO po) {
        return String.valueOf(po.getMatId()) + po.getFtyId() + po.getLocationId();
    }


    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
//        Long matId = null;
//        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
//            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
//            if (Objects.isNull(matId)) {
//                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
//                return;
//            }
//        }
        String specStock = Const.STRING_EMPTY;
        // 领料出库 领料申请出库设置specStock为“Q”
//        if (EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue().equals(po.getReceiptType()) ||
//                EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue().equals(po.getReceiptType())) {
//            specStock = EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue();
//        }
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
//        // 配置matId
//        pos.setMatId(matId);
        pos.setPreMatCode(po.getMatCode());
        pos.setStockStatus(stockStatus);
        pos.setSpecStock(specStock);
        if (UtilString.isNullOrEmpty(pos.getSpecStockCode())){
            pos.setSpecStockCode(null);
        }
        pos.setIsApplyFlag(po.getIsApplyFlag());
        pos.setReferReceiptCodePara(po.getReferReceiptCode());
        // [30994]【领料申请】部门领用、生产领用、劳保领用的领料申请添加库存时需过滤“YQ01-缺件仓”的所有库存；
        pos.setNqLocationId(dictionaryService.getLocationIdCacheByCode("1104","YQ01"));
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(), pos);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
            this.setBatchImg(itemDTOList);
        }
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        matStockDTO.setItemDTOList(itemDTOList);
        matStockDTO.setTotalCount(pos.getTotalCount());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * 获取按钮权限
     *
     * @param headId 单据id
     * @return 按钮设置
     */
    public ButtonVO setButton(Long headId) {
        ButtonVO button = new ButtonVO();
        QueryWrapper<BizReceiptOutputHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptOutputHead::getId, headId).eq(BizReceiptOutputHead::getIsDelete, 0);
        BizReceiptOutputHead one = bizReceiptOutputHeadDataWrap.getOne(queryWrapper);
        button.setButtonSave(this.setButtonSave(headId, one));
        button.setButtonDelete(this.setButtonDelete(headId, one));
        button.setButtonSubmit(this.setButtonSubmit(headId, one));
        button.setButtonPost(this.setButtonPost(headId, one));
        button.setButtonWriteOff(this.setButtonWriteOff(headId, one));
        button.setButtonRevoke(this.setButtonRevoke(headId, one));
        button.setButtonDebtOffset(this.setButtonDebtOffset(headId, one));
        return button;
    }


    /**
     * 设置批次图片信息
     *
     * @param itemDTOList 行项目
     */
    public void setBatchImg(List<BizReceiptOutputItemDTO> itemDTOList) {
        Set<Long> matIdSet = itemDTOList.stream().map(BizReceiptOutputItemDTO::getMatId).collect(Collectors.toSet());
        if (UtilCollection.isNotEmpty(matIdSet)) {
            Map<Long, List<BizBatchImgDTO>> batchImgMap = batchImgService.getBatchImgListByMatIdList(matIdSet, 4);
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                List<BizBatchImgDTO> bizBatchImgDTOS = batchImgMap.get(itemDTO.getMatId());
                itemDTO.setBatchImgList(bizBatchImgDTOS);
            }
        }
    }


    /**
     * 出库单能否保存
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonSave(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 出库单能否删除
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonDelete(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 驳回状态 可以删除
            return true;
        }
        return false;
    }

    /**
     * 出库单能否提交 0否、1是
     *
     * @param headId 单据id
     * @return 出库单能否提交 0否、1是
     */
    public Boolean setButtonSubmit(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 出库单能否未同步再次过账 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonPost(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步状态
            return true;
        }
        // 已作业状态
        return EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus);
    }


    /**
     * 出库单能否显示冲销按钮 0否、1是
     *
     * @param headId 单据id
     * @return 出库单能否冲销 0否、1是
     */
    public Boolean setButtonWriteOff(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        // 单据当前状态
        Integer receiptStatus = one.getReceiptStatus();
        QueryWrapper<BizReceiptOutputItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptOutputItem::getHeadId, headId);
        List<BizReceiptOutputItem> itemList = bizReceiptOutputItemDataWrap.list(queryWrapper);
        // 未同步状态和已完成
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 如果存在任意一个行项目未冲销，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptOutputItem item : itemList) {
                if (EnumRealYn.FALSE.getIntValue().equals(item.getIsWriteOff())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            // 已记账
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
            // 作业中
            // 如果存在任意一个行项目已过账，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptOutputItem item : itemList) {
                if (EnumRealYn.TRUE.getIntValue().equals(item.getIsPost())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        }
        return false;
    }

    /**
     * 出库单能否撤销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonRevoke(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus)) {
            // 待核销状态
            return true;
        }
        return false;
    }

    /**
     * 出库单能否核销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonDebtOffset(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus)) {
            // 待核销状态
            return true;
        }
        return false;
    }




    /**
     * 审批校验
     *
     * @param ctx
     * @return
     */
    private Integer approveCheck(BizContext ctx) {
        Integer receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_SAME.getValue();//领料出库申请单-内部单位同部门
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        Integer isInner = headDTO.getIsInnerFlag();
        Integer isLaborFlag=headDTO.getIsLaborFlag();//是否劳保物资【1是，0否】
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();
        List<String> level3UserList = new ArrayList<>();
        List<String> level4UserList = new ArrayList<>();
        String deptType = userDepartment.get(0).getDeptType();
        if (EnumDeptTypes.INSIDE.getValue().equals(deptType)) {
            boolean mtdFlag = false;
            String mtdCode = EnumDept.MTD.getCode();
            for (MetaDataDeptOfficePO officePO : userDepartment) {
                String deptCode = officePO.getDeptCode();
                if (mtdCode.equals(deptCode)) {
                    mtdFlag = true;
                    break;
                }
            }
            String counterpartDeptCode = headDTO.getCounterpartDeptCode();
            if (mtdFlag) {
                receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_SAME_DEPT.getValue();//领料出库申请单-内部单位同部门(维修部)
                // 内部单位
                if (EnumRealYn.FALSE.getIntValue().equals(isInner)) {
                    List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3);
                    level2UserList.addAll(userList);
                    if (UtilCollection.isEmpty(level2UserList)) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
                    }
                    receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_CROSS_DEPT.getValue();//领料出库申请单-内部单位跨部门(维修部)
                }else{
                    if (EnumRealYn.TRUE.getIntValue().equals(isLaborFlag)) { //是劳保物资
//                        String deptCode = EnumDept.RPD.getCode(); //辐射防护与环境应急部 --- 作废
//                        String officeCode = EnumOffice.RPDH.getCode();//职业卫生科
//                        List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_1);
                        List<String> userCodeList = Arrays.asList("80055280", "82002808", "82069102"); // 改为固定三个用户
                        WmsQueryWrapper<SysUser> wrapper = new WmsQueryWrapper<>();
                        wrapper.lambda().in(SysUser::getUserCode, userCodeList);
                        int count = (int)sysUserDataWrap.count(wrapper);
                        if (UtilNumber.isEmpty(count)) {
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
                        }
                        level2UserList.addAll(userCodeList);
                        receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_SAME_LABOR_DEPT.getValue();//领料出库申请单-内部单位劳保物资(维修部)
                    }
                }
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(mtdCode, null, EnumApprovalLevel.LEVEL_4);
                level1UserList.addAll(userList);
            } else {
                // 内部单位
                if (EnumRealYn.FALSE.getIntValue().equals(isInner)) {
                    // 内部单位跨部门领用
                    // 查询对口部门、对口科室3级审批人
                    List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3);
                    level3UserList.addAll(userList);
                    if (UtilCollection.isEmpty(level3UserList)) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "3");
                    }
                    receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_CROSS.getValue();//领料出库申请单-内部单位跨部门
                }else{
                    if (EnumRealYn.TRUE.getIntValue().equals(isLaborFlag)) {//是劳保物资
//                        String deptCode = EnumDept.RPD.getCode(); //辐射防护与环境应急部 --- 作废
//                        String officeCode = EnumOffice.RPDH.getCode();//职业卫生科
//                        List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_1);
                        List<String> userCodeList = Arrays.asList("80055280", "82002808", "82069102"); // 改为固定三个用户
                        WmsQueryWrapper<SysUser> wrapper = new WmsQueryWrapper<>();
                        wrapper.lambda().in(SysUser::getUserCode, userCodeList);
                        int count = (int)sysUserDataWrap.count(wrapper);
                        if (UtilNumber.isEmpty(count)) {
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
                        }
                        level3UserList.addAll(userCodeList);
                        receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_SAME_LABOR.getValue();//领料出库申请单-内部单位劳保物资
                    }
                }
                for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
                    // 查询用户所属部门的三级审批人
                    String deptCode = deptOfficePO.getDeptCode();
                    List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
                    level1UserList.addAll(userList);
                }
                for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
                    // 查询用户所属部门的四级审批人
                    String deptCode = deptOfficePO.getDeptCode();
                    List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
                    level2UserList.addAll(userList);
                }
                if (UtilCollection.isEmpty(level2UserList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
                }
            }
        } else if (EnumDeptTypes.OUTSIDE.getValue().equals(deptType)) {
            // 外部单位
            for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
                // 查询用户所属部门的三级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
                level1UserList.addAll(userList);
            }
            String counterpartDeptCode = headDTO.getCounterpartDeptCode();
            String counterpartOfficeCode = headDTO.getCounterpartOfficeCode();
            // 查询对口部门、对口科室1级审批人
            level2UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_1));
            // 查询对口部门3级审批人
            level3UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3));
            // 查询对口部门4级审批人
            level4UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_4));
            if (UtilCollection.isEmpty(level3UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
            }
            if (UtilCollection.isEmpty(level4UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "4");
            }
            receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_OUTSIDE.getValue();//领料出库申请单-外部单位
            if (UtilCollection.isEmpty(level2UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
            }
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        return receiptType;
    }



    /**
     * 领料申请单打印校验
     *
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizMatApplyLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptApplyHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);

    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptApplyHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptApplyHead bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptApplyHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptApplyHeadDTO headDTO = UtilBean.newInstance(bizReceiptApplyHead, BizReceiptApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        headDTO.getItemList().stream().forEach(obj -> {
            obj.setPreReceiptCode(obj.getPreReceiptCode());
            obj.setPreReceiptRid(obj.getPreReceiptRid());
        });
        return headDTO;
    }

    /**
     * 填充打印数据
     *
     * @param ctx
     */
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizMatApplyLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptApplyHeadDTO headDTO = po.getHeadDTO();
        // 新建领料申请打印实体对象
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        List<LabelReceiptRegisterBox> outputBoxList = new ArrayList<>();
        itemDTOList.forEach(itemDTO->{
            LabelReceiptRegisterBox labelReceiptRegisterBox = UtilBean.newInstance(itemDTO, LabelReceiptRegisterBox.class);
            outputBoxList.add(labelReceiptRegisterBox);
        });
        // 填充打印信息
        printInfo.setLabelBoxList(outputBoxList);
        if (UtilCollection.isNotEmpty(outputBoxList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,outputBoxList);
        }
    }

    private boolean reserved2SAP(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList, CurrentUser user) {
        // 构建创建预留单的参数
        BizReceiptApplyHeadDTO headDTOTemp = new BizReceiptApplyHeadDTO();
        headDTOTemp.setReceiptNum(headDTO.getReceiptNum());
        headDTOTemp.setCreateTime(headDTO.getCreateTime());
        headDTOTemp.setCreateUserCode(headDTO.getCreateUserCode());
        headDTOTemp.setApplyTime(headDTO.getApplyTime());
        headDTOTemp.setWhCodeOut(itemDTOList.get(0).getWhCodeOut());
        headDTOTemp.setItemList(itemDTOList);
        headDTOTemp.setReceiptType(headDTO.getReceiptType());
        headDTOTemp.setReceiptCode(headDTO.getReceiptCode());
        // 创建预留得时候，不同步sap使用了此rid
        itemDTOList.stream().forEach(e->{e.setReservedOrderRid(e.getRid());});
        ReserveReceiptCreatePO reserveReceiptCreatePO = ReserveReceiptCreatePO.builder().bizReceiptApplyHeadDTO(headDTOTemp).build();
        ReserveReceiptCreatePO sapCreateReserveReceiptInfo = reserveReceiptService.createReserveReceiptItemListNew(reserveReceiptCreatePO, user);
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(sapCreateReserveReceiptInfo.getSuccess())) {
            return true;
        }
        log.error("领料申请单{}SAP创建预留失败，返回信息：{}", headDTO.getReceiptCode(), reserveReceiptCreatePO.getReturnMessage());
        return false;
    }

    /**
     * 领料出库单审批操作
     *
     * @param ctx
     */
    public void doOutputInstanceNew(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        // 按wbs进行分组(不含已创建预留)
        Map<String, List<BizReceiptApplyItemDTO>> itemDTOMap = new HashMap<>(itemDTOList.size());
        for (BizReceiptApplyItemDTO itemDTO : itemDTOList) {
            Long reservedOrderCode = itemDTO.getReservedOrderCode();
            if (reservedOrderCode != null && reservedOrderCode > 0) {
                continue;
            }
            String specStockCode = itemDTO.getSpecStockCode();
            List<BizReceiptApplyItemDTO> itemList = itemDTOMap.get(specStockCode);
            if (itemList == null) {
                itemList = new ArrayList<>();
            }
            itemList.add(itemDTO);
            itemDTOMap.put(specStockCode, itemList);
        }
        // 按wbs进行创建预留
        boolean completeFlag = true;
        for (String specStockCode : itemDTOMap.keySet()) {
            List<BizReceiptApplyItemDTO> itemList = itemDTOMap.get(specStockCode);
            AtomicInteger rid = new AtomicInteger(1);
            itemList.forEach(item -> {
                int ridReserved = rid.getAndIncrement();
                item.setRidReservedNum(ridReserved);
                item.setRidReserved(ridReserved + "");
            });
            boolean result = reserved2SAP(headDTO, itemList, currentUser);
            if (!result) {
                completeFlag = false;
                continue;
            }
            // 创建预留成功，更新预留号到对应行项目
            for (BizReceiptApplyItemDTO itemDTO : itemList) {
                bizReceiptApplyItemDataWrap.updateStatusById(itemDTO);
            }
        }
        // 如果所有有预留未创建成功，则更新单据状态为未同步
        if (!completeFlag) {
            updateStatusNew(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            throw new WmsException(EnumReturnMsg.CREATE_RESERVE_RECEIPT_FAIL);
        }
        // 如果所有预留都创建成功，则更新单据状态为已完成
        updateStatusNew(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        /* ================================ 自动创建领料出库单 ================================ */
        List<BizReceiptOutputItemDTO> receiptOutputItemDTOList = new ArrayList<>();
        List<BizReceiptApplyItemDTO> applyHeadDTOItemList = headDTO.getItemList();
        // 配置assemble 设置为null
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        // 领料出库单是基于领料申请单进行创建，进行属性填充
        applyHeadDTOItemList.stream().forEach(bizReceiptApplyItemDTO -> {
            BizReceiptOutputItemDTO receiptOutputItemDTO = BizReceiptOutputItemDTO.builder()
                    // 这里没有配置领料出库的行项目的rid
                    .matId(bizReceiptApplyItemDTO.getMatId())
                    .ftyId(bizReceiptApplyItemDTO.getFtyId())
                    .locationId(bizReceiptApplyItemDTO.getLocationId())
                    .whId(bizReceiptApplyItemDTO.getWhId())
                    .preReceiptQty(bizReceiptApplyItemDTO.getQty())
                    .receiptQty(bizReceiptApplyItemDTO.getQty())
                    //操作数量
                    .qty(BigDecimal.ZERO)
                    // 已退库数量
                    .returnQty(BigDecimal.ZERO)
                    // 已作业数量
                    .taskQty(BigDecimal.ZERO)
                    // 已完成数量
                    .finishQty(BigDecimal.ZERO)
                    // 特殊库存标识
                    .specStock(bizReceiptApplyItemDTO.getSpecStock())
                    .unitId(bizReceiptApplyItemDTO.getUnitId())
                    // 前续单据head id
                    .preReceiptHeadId(bizReceiptApplyItemDTO.getHeadId())
                    // 前序单据item表id
                    .preReceiptItemId(bizReceiptApplyItemDTO.getId())
                    // 前序单据类型
                    .preReceiptType(headDTO.getReceiptType())
                    .referReceiptHeadId(bizReceiptApplyItemDTO.getPreReceiptHeadId())
                    .referReceiptItemId(bizReceiptApplyItemDTO.getPreReceiptItemId())
                    .referReceiptCode(bizReceiptApplyItemDTO.getPreReceiptCode())
                    .referReceiptType(EnumReceiptType.RESERVE_RECEIPT.getValue())
                    // 行项目状态
                    .itemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .createTime(UtilDate.getNow())
                    .createUserId(bizReceiptApplyItemDTO.getCreateUserId())
                    .specStockCode(bizReceiptApplyItemDTO.getSpecStockCode())
                    .assembleDTOList(assembleDTOList)
                    .build();
            receiptOutputItemDTOList.add(receiptOutputItemDTO);
        });
        BizReceiptOutputHeadDTO receiptOutputHeadDTO = BizReceiptOutputHeadDTO.builder()
                .itemDTOList(receiptOutputItemDTOList)
                .receiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                .remark(headDTO.getRemark())
                .actualReceiverName(currentUser.getUserName())
                .workOrder(headDTO.getWorkOrder())
                .virtualOutputApplyReceiptCode(headDTO.getVirtualOutputApplyReceiptCode())
                .build();
        // 将领料出库信息保存到上下文中
        log.info("领料出库单保存的数据:{}", JSON.toJSONString(receiptOutputHeadDTO));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptOutputHeadDTO);
        materialOutputService.save(ctx);
        /* ================================ 自动创建领料出库单 ================================ */
        // 出库配货策略
        outputComponent.autoDistribution(ctx);
    }

    /**
     * 生成领料出库单据
     */
    public void createMatOutputReceipt(BizContext ctx){
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> receiptOutputItemDTOList = new ArrayList<>();
        List<BizReceiptApplyItemDTO> applyHeadDTOItemList = headDTO.getItemList();
        // 配置assemble 设置为null
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        // 领料出库单是基于领料申请单进行创建，进行属性填充
        applyHeadDTOItemList.stream().forEach(bizReceiptApplyItemDTO -> {
            BizReceiptOutputItemDTO receiptOutputItemDTO = BizReceiptOutputItemDTO.builder()
                    // 这里没有配置领料出库的行项目的rid
                    .matId(bizReceiptApplyItemDTO.getMatId())
                    .ftyId(bizReceiptApplyItemDTO.getFtyId())
                    .locationId(bizReceiptApplyItemDTO.getLocationId())
                    .whId(bizReceiptApplyItemDTO.getWhId())
                    .preReceiptQty(bizReceiptApplyItemDTO.getQty())
                    .receiptQty(bizReceiptApplyItemDTO.getQty())
                    //操作数量
                    .qty(BigDecimal.ZERO)
                    // 已退库数量
                    .returnQty(BigDecimal.ZERO)
                    // 已作业数量
                    .taskQty(BigDecimal.ZERO)
                    // 已完成数量
                    .finishQty(BigDecimal.ZERO)
                    // 特殊库存标识
                    .specStock(bizReceiptApplyItemDTO.getSpecStock())
                    .unitId(bizReceiptApplyItemDTO.getUnitId())
                    // 前续单据head id
                    .preReceiptHeadId(bizReceiptApplyItemDTO.getHeadId())
                    // 前序单据item表id
                    .preReceiptItemId(bizReceiptApplyItemDTO.getId())
                    // 前序单据类型
                    .preReceiptType(headDTO.getReceiptType())
                    .referReceiptHeadId(bizReceiptApplyItemDTO.getPreReceiptHeadId())
                    .referReceiptItemId(bizReceiptApplyItemDTO.getPreReceiptItemId())
                    .referReceiptCode(bizReceiptApplyItemDTO.getPreReceiptCode())
                    .referReceiptType(EnumReceiptType.RESERVE_RECEIPT.getValue())
                    // 行项目状态
                    .itemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .createTime(UtilDate.getNow())
                    .createUserId(bizReceiptApplyItemDTO.getCreateUserId())
                    .specStockCode(bizReceiptApplyItemDTO.getSpecStockCode())
                    .assembleDTOList(assembleDTOList)
                    .build();
            receiptOutputItemDTOList.add(receiptOutputItemDTO);
        });
        BizReceiptOutputHeadDTO receiptOutputHeadDTO = BizReceiptOutputHeadDTO.builder()
                .itemDTOList(receiptOutputItemDTOList)
                .receiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                .remark(headDTO.getRemark())
                .actualReceiverName(UtilString.isNotNullOrEmpty(headDTO.getActualReceiverName()) ? headDTO.getActualReceiverName() : headDTO.getCreateUserName())
                .workOrder(headDTO.getWorkOrder())
                .wbsCode(headDTO.getWbsCode())
                .assetId(headDTO.getAssetId())
                .costCenterId(headDTO.getCostCenterId())
                .costCenterCode(headDTO.getWbsCode())
                .virtualOutputApplyReceiptCode(headDTO.getVirtualOutputApplyReceiptCode())
                .build();
        // 将领料出库信息保存到上下文中
        log.info("领料出库单保存的数据:{}", JSON.toJSONString(receiptOutputHeadDTO));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptOutputHeadDTO);
        materialOutputService.save(ctx);
        // 出库配货策略
        outputComponent.autoDistribution(ctx);
    }

    /**
     * 创建领料出库单
     *
     * @param ctx
     */
    public void reqPlanDoOutputInstance(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        // 如果所有预留都创建成功，则更新单据状态为已完成
        updateStatusNew(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        /* ================================ 自动创建领料出库单 ================================ */
        List<BizReceiptOutputItemDTO> receiptOutputItemDTOList = new ArrayList<>();
        List<BizReceiptApplyItemDTO> applyHeadDTOItemList = headDTO.getItemList();
        // 配置assemble 设置为null
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        // 领料出库单是基于领料申请单进行创建，进行属性填充
        applyHeadDTOItemList.stream().forEach(bizReceiptApplyItemDTO -> {
            BizReceiptOutputItemDTO receiptOutputItemDTO = BizReceiptOutputItemDTO.builder()
                    // 这里没有配置领料出库的行项目的rid
                    .matId(bizReceiptApplyItemDTO.getMatId())
                    .ftyId(bizReceiptApplyItemDTO.getFtyId())
                    .locationId(bizReceiptApplyItemDTO.getLocationId())
                    .whId(bizReceiptApplyItemDTO.getWhId())
                    .preReceiptQty(bizReceiptApplyItemDTO.getQty())
                    .receiptQty(bizReceiptApplyItemDTO.getQty())
                    //操作数量
                    .qty(BigDecimal.ZERO)
                    // 已退库数量
                    .returnQty(BigDecimal.ZERO)
                    // 已作业数量
                    .taskQty(BigDecimal.ZERO)
                    // 已完成数量
                    .finishQty(BigDecimal.ZERO)
                    // 特殊库存标识
                    .specStock(bizReceiptApplyItemDTO.getSpecStock())
                    .unitId(bizReceiptApplyItemDTO.getUnitId())
                    // 前续单据head id
                    .preReceiptHeadId(bizReceiptApplyItemDTO.getHeadId())
                    // 前序单据item表id
                    .preReceiptItemId(bizReceiptApplyItemDTO.getId())
                    // 前序单据类型
                    .preReceiptType(headDTO.getReceiptType())
                    .referReceiptHeadId(bizReceiptApplyItemDTO.getPreReceiptHeadId())
                    .referReceiptItemId(bizReceiptApplyItemDTO.getPreReceiptItemId())
                    .referReceiptCode(bizReceiptApplyItemDTO.getPreReceiptCode())
                    .referReceiptType(bizReceiptApplyItemDTO.getPreReceiptType())
                    // 行项目状态
                    .itemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .createTime(UtilDate.getNow())
                    .createUserId(bizReceiptApplyItemDTO.getCreateUserId())
                    .specStockCode(bizReceiptApplyItemDTO.getSpecStockCode())
                    .assembleDTOList(assembleDTOList)
                    .build();
            receiptOutputItemDTOList.add(receiptOutputItemDTO);
        });
        BizReceiptOutputHeadDTO receiptOutputHeadDTO = BizReceiptOutputHeadDTO.builder()
                .itemDTOList(receiptOutputItemDTOList)
                .receiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                .remark(headDTO.getRemark())
                .actualReceiverName(currentUser.getUserName())
                .build();
        // 将领料出库信息保存到上下文中
        log.info("领料出库单保存的数据:{}", JSON.toJSONString(receiptOutputHeadDTO));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, receiptOutputHeadDTO);
        materialOutputService.save(ctx);
        /* ================================ 自动创建领料出库单 ================================ */
        // 出库配货策略
        outputComponent.autoDistribution(ctx);
    }


    /**
     * 更新单据，行项目状态
     *
     * @param headDTO
     * @param itemDTOList
     * @param status
     */
    public void updateStatusNew(BizReceiptApplyHeadDTO headDTO, List<BizReceiptApplyItemDTO> itemDTOList, Integer status) {
        bizReceiptApplyHeadDataWrap.updateStatusById(status, headDTO.getId());
        List<Long> idList = itemDTOList.stream().map(BizReceiptApplyItemDTO::getId).collect(Collectors.toList());
        bizReceiptApplyItemDataWrap.updateByIds(status, idList);
    }

    /**
     * 保存领料出库单信息
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOutInfoNew(BizContext ctx) {
        boolean isNew = true;
        BizReceiptApplyHeadDTO applyHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        CurrentUser user = ctx.getCurrentUser();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        String receiptCode = applyHeadDTO.getReceiptCode();
        String applyCode = applyHeadDTO.getReceiptNum() == null ? null : applyHeadDTO.getReceiptNum();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptApplyHead bizReceiptApplyHead = null;
        // 处理头信息
        if (UtilNumber.isNotEmpty(applyHeadDTO.getId())) {
            bizReceiptApplyHead = bizReceiptApplyHeadDataWrap.getById(applyHeadDTO.getId());
            // 根据id更新
            applyHeadDTO.setRemark(applyHeadDTO.getReceiptRemark());
            bizReceiptApplyHeadDataWrap.updateDtoById(applyHeadDTO);

            // item物理删除
            QueryWrapper<BizReceiptApplyItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(BizReceiptApplyItem::getHeadId, applyHeadDTO.getId());
            bizReceiptApplyItemDataWrap.physicalDelete(itemQueryWrapper);

            // assemble物理删除
            QueryWrapper<BizReceiptAssemble> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizReceiptAssemble::getReceiptHeadId, applyHeadDTO.getId());
            bizReceiptAssembleDataWrap.physicalDelete(queryWrapperAssemble);

            // outInfo 根据id、headId更新
            QueryWrapper<BizReceiptOutputInfo> outInfoWrapper = new QueryWrapper<>();
            outInfoWrapper.lambda()
                    .eq(BizReceiptOutputInfo::getId, applyHeadDTO.getOutInfoId());
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(applyHeadDTO.getOutInfoId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .build();
            bizReceiptOutputInfoDataWrap.updateDto(bizReceiptOutputInfoDTO, outInfoWrapper);

            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            /* ================ outInfo处理  领料出库单信息 ================ */
            if(StringUtils.isEmpty(applyCode)){
                applyCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_APPLY_CODE.getValue());
            }
            BizReceiptOutputInfoDTO bizReceiptOutputInfoDTO = BizReceiptOutputInfoDTO.builder()
                    .id(UtilSequence.nextId())
                    .receiptNum(applyCode)
                    .receiptRemark(applyHeadDTO.getReceiptRemark())
                    .matReceiverId(applyHeadDTO.getMatReceiverId())
                    .matDeptId(applyHeadDTO.getMatDeptId())
                    .applyTime(applyHeadDTO.getApplyTime())
                    .installSite(applyHeadDTO.getInstallSite())
                    .installSystem(applyHeadDTO.getInstallSystem())
//                    .specStockCode(applyHeadDTO.getSpecStockCode())
//                    .specStockName(applyHeadDTO.getSpecStockName())
                    .whCodeOut(applyHeadDTO.getWhCodeOut())
                    .deptId(applyHeadDTO.getCounterpartDeptId())
                    .deptOfficeId(applyHeadDTO.getCounterpartOfficeId())
                    .createTime(UtilDate.getNow())
                    .modifyTime(UtilDate.getNow())
                    .createUserId(user.getCreateUserId())
                    .modifyUserId(user.getModifyUserId())
                    .build();
            bizReceiptOutputInfoDataWrap.saveDto(bizReceiptOutputInfoDTO);
            /* ================ head处理 ================ */
//            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
//            if(dictionaryService.getCorpCacheById(user.getCorpId()).getCorpCode().equals(Const.HL_59C0)){
//                // HL-5ST-RCBD1-WXLYYYZZZ
//                receiptCode = "HL-5ST-RCBD1-WXLY" + bizCommonService.getNextSequenceYear("sequence_apply_code_hl");
//
//            } else {
//                receiptCode = bizCommonService.getNextSeqMaterialReq(user,EnumSequenceCode.MATERIAL_REQ.getValue(),applyHeadDTO.getItemList().get(EnumRealYn.FALSE.getIntValue()).getFtyCode());
//            }
            receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_OUT_MAT_REQ.getValue());
            applyHeadDTO.setId(null);
            applyHeadDTO.setReceiptCode(receiptCode);
            applyHeadDTO.setCreateUserId(createUserId);
            applyHeadDTO.setModifyUserId(createUserId);
            applyHeadDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
            applyHeadDTO.setReceiptStatus(status);
            applyHeadDTO.setOutInfoId(bizReceiptOutputInfoDTO.getId());
            applyHeadDTO.setRemark(applyHeadDTO.getReceiptRemark());
            bizReceiptApplyHeadDataWrap.saveDto(applyHeadDTO);

            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptApplyHead.getReceiptStatus();
            createTime = bizReceiptApplyHead.getCreateTime();
            createUserId = bizReceiptApplyHead.getCreateUserId();
        }

        // item处理
        List<BizReceiptApplyItemDTO> itemDTOList = applyHeadDTO.getItemList();
        AtomicInteger rid = new AtomicInteger(1);
        Map<String, String> stockCodeMap = new HashMap<>();
        for (int i = 0; i < itemDTOList.size(); i++) {
            BizReceiptApplyItemDTO itemDTO = itemDTOList.get(i);
            String specStockCode = itemDTO.getSpecStockCode();
            String whCodeOut = stockCodeMap.get(specStockCode);
            if (whCodeOut == null) {
                whCodeOut = getWhCodeOut(specStockCode, i + 1);
                stockCodeMap.put(specStockCode, whCodeOut);
            }
            itemDTO.setWhCodeOut(whCodeOut);
            itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            itemDTO.setId(null);
            itemDTO.setHeadId(applyHeadDTO.getId());
            itemDTO.setItemStatus(status);
            itemDTO.setCreateTime(createTime);
            itemDTO.setCreateUserId(createUserId);
            itemDTO.setModifyUserId(user.getId());
        }
        bizReceiptApplyItemDataWrap.saveBatchDto(itemDTOList);

        // assemble处理
        List<BizReceiptAssembleDTO> assembleList = new ArrayList<>();
        for (BizReceiptApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                assembleDTO.setId(null);
                assembleDTO.setReceiptHeadId(applyHeadDTO.getId());
                assembleDTO.setReceiptItemId(itemDto.getId());
                assembleDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
                assembleDTO.setCreateUserId(createUserId);
                assembleDTO.setModifyUserId(user.getId());
                assembleDTO.setSpecType(assembleDTO.getSpecType() == null ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue() : assembleDTO.getSpecType());
                assembleList.add(assembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleList);
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, applyHeadDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, applyHeadDTO.getId());
    }

    // 根据24位的WBS Code 获取 8位的WBS编号 8位的WBS Code是在创建预留单的时候使用
    private String getWhCodeOut(String specStockCode, int lineIdx) {
        if(StringUtils.isEmpty(specStockCode)){
            return  "";
        }
        SapWbs sapWbs = erpWbsService.findByCode(specStockCode);
        if (sapWbs == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_WBS_CODE_GET_FAILED, specStockCode);
        }
        String leafFlag = sapWbs.getWbsLeafFlag();
        if (StringUtils.isBlank(leafFlag) || !Const.WBS_LEAF_FLAG.equals(leafFlag))
            throw new WmsException(EnumReturnMsg.WBS_NOT_LEAF, String.valueOf(lineIdx), specStockCode);
        return sapWbs.getWbsCodeIn();
    }

    public void updateLocation(BizContext ctx) {
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList();
        //性能优化，校验从前端取值的locationId在缓存中是否存在，若存在则继续，否则报错
        List<Long> locationIdList = itemDTOList.stream().map(obj -> obj.getLocationId()).distinct().collect(Collectors.toList());
        Collection<DicStockLocationDTO> locationCacheByIds = dictionaryService.getLocationCacheByIds(locationIdList);
        if (UtilCollection.isEmpty(locationCacheByIds)||locationCacheByIds.size()!=locationIdList.size()){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_LOCATION_NOT_EXIST);
        }
        List<Long> itemIds = itemDTOList.stream().map(BizReceiptApplyItemDTO::getId).collect(Collectors.toList());
        List<BizReceiptApplyItem> itemListFromDb = bizReceiptApplyItemDataWrap.listByIds(itemIds);

        // 检查行项目，对于已经生成预留的行项目，不允许变更库存地点
        for (BizReceiptApplyItemDTO itemFromParam : itemDTOList) {
            for (BizReceiptApplyItem itemFormDb : itemListFromDb) {
                if (itemFormDb.getId().equals(itemFromParam.getId())
                        && UtilNumber.isNotEmpty(itemFormDb.getReservedOrderCode())
                        && !itemFormDb.getLocationId().equals(itemFromParam.getLocationId())
                ) {
                    // 对于已经生成预留的行项目，不允许变更库存地点
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_LOCATION_AUTHORITY_EXCEPTION);
                }
            }
        }

        // 按照库存地点数据，同时更新库存地点对应的仓库信息
        for (BizReceiptApplyItemDTO applyItemDTO : itemDTOList) {
            DicStockLocationDTO locationDTO = dictionaryService.getLocationCacheById(applyItemDTO.getLocationId());
            applyItemDTO.setWhId(locationDTO.getWhId());
        }

        bizReceiptApplyItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * 查询领用类型下拉
     */
    public void getReceiveTypeDown(BizContext ctx) {
        List<ReceiveTypeMapVO> list = UtilCollection.toList(EnumReceiveType.toList(), ReceiveTypeMapVO.class);
        List<String> roleCodeList = ctx.getCurrentUser().getSysUserRoleRelList().stream().map(o -> o.getRoleCode()).collect(Collectors.toList());
        if (!roleCodeList.contains(Const.JS17_ROLE_CODE)) {
            // 权限要求：用户必须拥有“JS17”劳保领用角色功能；才能发起“劳保领用”
            list.removeIf(o -> o.getReceiveType().equals(EnumReceiveType.LABOR_USE.getValue()));
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(list));
    }

    /**
     * 领料申请查询需求计划
     */
    public void getReferReceiveItemList(BizContext ctx) {
        // 入参上下文
        BizReceiptApplySearchReqPlanPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载返回对象
        MultiResultVO<BizReceiptApplyHeadDTO> returnVo = new MultiResultVO<>();
        // 调用SAP查询领料单
        List<ErpReceiveReceiptItemDTO> receiveReceiptItemVoList = receiveReceiptService
                .getErpReceiveReceiptItemList(po, user);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, this.receiptDataFormat(returnVo, receiveReceiptItemVoList));

    }


    /**
     * 申请单查询参转换
     */
    private MultiResultVO<BizReceiptApplyHeadDTO> receiptDataFormat(
            MultiResultVO<BizReceiptApplyHeadDTO> returnVo,
            List<ErpReceiveReceiptItemDTO> receiveReceiptItemVoList) {
        if (UtilCollection.isNotEmpty(receiveReceiptItemVoList)) {

            List<Long> itemIdList = receiveReceiptItemVoList.stream().map(ErpReceiveReceiptItemDTO::getId).collect(Collectors.toList());

            List<ErpReceiveReceiptItemDTO> itemDTOList=  erpReceiveReceiptItemDataWrap.getReceiveReceiptItemAttrList(itemIdList);

            receiveReceiptItemVoList=itemDTOList;
            // 装载返回数据
            List<BizReceiptApplyHeadDTO> headInfoList = new ArrayList<>();
            // 根据采购订单号分组
            LinkedHashMap<String, List<ErpReceiveReceiptItemDTO>> receiveMap =
                    receiveReceiptItemVoList.stream().collect(Collectors
                            .groupingBy(ErpReceiveReceiptItemDTO::getReceiptCode, LinkedHashMap::new, Collectors.toList()));
            Set<String> keys = receiveMap.keySet();
            for (String key : keys) {
                // 装载返回数据head
                BizReceiptApplyHeadDTO headInfo = new BizReceiptApplyHeadDTO();
                // 装载返回数据item
                List<BizReceiptApplyItemDTO> itemInfoList = new ArrayList<>();
                List<ErpReceiveReceiptItemDTO> receiveItemList = receiveMap.get(key);
                for (int i = 0; i < receiveItemList.size(); i++) {
                    ErpReceiveReceiptItemDTO receiveDTO = receiveItemList.get(i);
                    BizReceiptApplyItemDTO itemInfo = new BizReceiptApplyItemDTO();
                    /* ******** 设置head列字段 ******** */
                    if (i == 0) {
                        headInfo = UtilBean.newInstance(receiveDTO, headInfo.getClass());
                        headInfo.setReceiptNum(receiveDTO.getReceiptCode());
                        headInfo.setReceiptRemark(receiveDTO.getReceiptRemark());
                        headInfo.setErpCreateUserCode(receiveDTO.getErpCreateUserCode());
                    }
                    /* ******** 设置item列字段 ******** */
                    itemInfo = UtilBean.newInstance(receiveDTO, itemInfo.getClass());
                    itemInfo.setId(null);
                    itemInfo.setHeadId(null);
                    itemInfo.setRid(Const.STRING_EMPTY);
                    itemInfo.setReceiptCode(Const.STRING_EMPTY);
                    itemInfo.setReservedOrderCode(receiveDTO.getReservedOrderCode());
                    itemInfo.setReservedOrderRid(receiveDTO.getReservedOrderRid());
                    itemInfo.setPreReceiptHeadId(receiveDTO.getHeadId());
                    itemInfo.setPreReceiptItemId(receiveDTO.getId());
                    itemInfo.setPreReceiptType(EnumReceiptType.RECEIVE_RECEIPT.getValue());
                    itemInfo.setPreReceiptQty(receiveDTO.getQty());
                    itemInfo.setReferReceiptHeadId(receiveDTO.getHeadId());
                    itemInfo.setReferReceiptItemId(receiveDTO.getId());
                    itemInfo.setReferReceiptType(EnumReceiptType.RECEIVE_RECEIPT.getValue());
                    itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                    itemInfo.setSendQty(receiveDTO.getSendQty());
                    itemInfo.setStockQty(receiveDTO.getStockQty());
                    itemInfo.setApproveQty(receiveDTO.getQty());
                    itemInfo.setQty(calQty(receiveDTO));
                    itemInfo.setWorkOrder(receiveDTO.getWorkOrder());
                    itemInfo.setMoveType(receiveDTO.getMoveType());
                    itemInfoList.add(itemInfo);
                }
                if (UtilCollection.isNotEmpty(itemInfoList)){
                    headInfo.setWorkOrder(itemInfoList.get(0).getWorkOrder());
                }
                headInfo.setItemList(itemInfoList);
                headInfoList.add(headInfo);
            }
            returnVo.setResultList(headInfoList);
        }
        return returnVo;
    }

    /**
     * 计算可发数量
     * @param receiveDTO
     * @return
     */
    private BigDecimal calQty( ErpReceiveReceiptItemDTO receiveDTO) {

         BigDecimal stockQty=receiveDTO.getStockQty()==null ? BigDecimal.ZERO :receiveDTO.getStockQty(); //库存数量
         BigDecimal sendQty=receiveDTO.getSendQty()==null ? BigDecimal.ZERO :receiveDTO.getSendQty(); //已发数量
         BigDecimal approveQty=receiveDTO.getQty()==null ? BigDecimal.ZERO :receiveDTO.getQty(); //批准数量
         BigDecimal qty=approveQty.subtract(sendQty);//可发数量 =批准数量-已发数量
         if(stockQty.compareTo(qty)<0){ //库存数量小于可发数量 取库存数量
             qty=stockQty;
         }else{ //库存数量大于批准数量  取可发数量

         }
        return qty;
    }

    /**
     * 发起审批
     */
    @Transactional(rollbackFor = Exception.class)
    public void startWorkFlow(BizContext ctx) {
        // 发起流程审批
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();
        // 部门领用 421
        Integer receiveType = headDTO.getReceiveType();
        variables.put("receiveType", receiveType);
        // 【领料申请】领料申请审批待办同步OA
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + headDTO.getCreateUserName() + "提交的流程：" + headDTO.getReceiptCode());
        Integer receiptType = headDTO.getReceiptType();
        if (EnumReceiveType.LABOR_USE.getValue().equals(receiveType)) {
            // “劳保领用”的所有审批，均由“SED安健环部“部门领导”或“专工”审批”。
            String deptCode = EnumDept.SED.getCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_1);
            List<String> user2List = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
            userList.addAll(user2List);
            variables.put("deptCode", deptCode);
            if (UtilCollection.isEmpty(userList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_1_APPROVAL.getValue().toString());
            }
            // 劳保 4214
            receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_LABOR.getValue();
        }
        if (EnumReceiveType.DEPT_REQ_USE.getValue().equals(receiveType) || EnumReceiveType.ASSET_USE.getValue().equals(receiveType)) {
            // 发起人所属部门“部门领导”
            String deptCode = ctx.getCurrentUser().getUserDeptList().get(0).getDeptCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
            variables.put("deptCode", deptCode);
            if (UtilCollection.isEmpty(userList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_1_APPROVAL.getValue().toString());
            }
        }
        // 生产领用
        if (EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(receiveType)) {
            // 所选“专工”
            variables.put("assignUserCode", headDTO.getAssignUserCode());
            if (UtilString.isNullOrEmpty(headDTO.getAssignUserCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_1_APPROVAL.getValue().toString());
            }
            if (UtilNumber.isNotEmpty(headDTO.getInnerPlan())) {
                // 计划内 4212
                receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_INSIDE_CROSS.getValue();

            } else {
                // 计划外 4213
                receiptType = EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY_OUTSIDE.getValue();
                // 专工所属“部门领导”
                List<SysUserDeptOfficeRel> list = sysUserDeptOfficeRelDataWrap.list(new QueryWrapper<SysUserDeptOfficeRel>().lambda()
                        .eq(SysUserDeptOfficeRel::getUserId, headDTO.getAssignUserId())
                        .eq(SysUserDeptOfficeRel::getJobLevel, EnumUserJob.LEVEL_1_APPROVAL.getValue()));
                String deptCode = dicDeptDataWrap.getById(list.get(0).getDeptId()).getDeptCode();
                variables.put("deptCode", deptCode);
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
                if (UtilCollection.isEmpty(userList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_2_APPROVAL.getValue().toString());
                }
            }
        }

        // 领料申请：“请审批”[公司+部门]用户姓名+“提交的流程”+领料单描述（取领料申请单抬头的领料单描述）
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getReceiptRemark());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getReceiptRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新领料申请单 - 审批中
        updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());

    }

    /**
     * 获取审批配置权限是专工的用户列表
     */
    public void getUserList(BizContext ctx) {
        List<SysUserDeptOfficeRel> list = sysUserDeptOfficeRelDataWrap.list(new QueryWrapper<SysUserDeptOfficeRel>().lambda().eq(SysUserDeptOfficeRel::getJobLevel, EnumUserJob.LEVEL_1_APPROVAL.getValue()));
        List<Long> userIds = list.stream().map(o -> o.getUserId()).distinct().collect(Collectors.toList());
        List<SysUser> sysUserList = (List<SysUser>) dictionaryService.getSysUserCacheByIds(userIds);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(sysUserList));
    }

    /**
     * 撤销
     */
    public void revoke(BizContext ctx) {
        BizReceiptApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除待办
        workflowService.deleteTodo(po.getId());
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(po.getProInstanceId().toString());
        workflowService.revoke(revokeDTO);
        // 更新领料申请单 - 草稿
        updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, po.getReceiptCode());
    }

    /**
     * 批量修改状态备注
     */
    public void updateStatusRemark(BizContext ctx) {
        BizReceiptApplySearchReqPlanPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizReceiptApplyItemDataWrap.update(new UpdateWrapper<BizReceiptApplyItem>().lambda()
                .set(BizReceiptApplyItem::getStatusRemark, po.getStatusRemark())
                .in(BizReceiptApplyItem::getId, po.getItemIdList()));
    }
}
