package com.inossem.wms.bizdomain.room.controller;


import com.inossem.wms.bizdomain.room.service.biz.BizRoomReceiptAllocationService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptAllocationHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.po.BizRoomReceiptSearchPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(tags = "房间管理-住房分配")
public class BizRoomReceiptAllocationController {

    @Autowired
    private BizRoomReceiptAllocationService bizRoomReceiptAllocationService;


    @ApiOperation(value = "住房分配-分页")
    @PostMapping(value = "/room/allocation/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizRoomReceiptAllocationHeadDTO>> getPage(@RequestBody BizRoomReceiptSearchPO po, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptAllocationService.getPage(ctx);
        return BaseResult.success(ctx.getVoContextData(PageObjectVO.class));
    }

    @ApiOperation(value = "住房分配-详情")
    @GetMapping(value = "/room/allocation/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizRoomReceiptAllocationHeadDTO>> getInfo(@PathVariable("id") Long id, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptAllocationService.getInfo(ctx);
        return BaseResult.success(ctx.getVoContextData(BizResultVO.class));
    }

    @ApiOperation(value = "住房分配-保存")
    @PostMapping(value = "/room/allocation/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizRoomReceiptAllocationHeadDTO po, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptAllocationService.saveReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "住房分配-提交")
    @PostMapping(value = "/room/allocation/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizRoomReceiptAllocationHeadDTO po, @ApiParam(hidden = true) BizContext ctx) {
        bizRoomReceiptAllocationService.submitReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }
}

