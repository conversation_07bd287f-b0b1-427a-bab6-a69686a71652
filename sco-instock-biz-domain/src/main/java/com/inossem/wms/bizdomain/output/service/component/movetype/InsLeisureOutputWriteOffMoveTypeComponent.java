package com.inossem.wms.bizdomain.output.service.component.movetype;

import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPo;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 生成冲销移动类型
 *
 * <AUTHOR>
 * @date 2021/4/2 22:31
 */

@Service
public class InsLeisureOutputWriteOffMoveTypeComponent {

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 生成记账ins凭证(批次库存增加，出库临时区库存增加)
     *
     * @param headDTO
     * @return
     *
     */
    public StockInsMoveTypeDTO generatePostingInsDocNonPostFirst(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();

        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            // 非先过账模式，通过bin表赋值
            insDocRid = this.getInsNonPostFirst(headDTO, itemDTO, insDocRid, insDocBatchList, insDocBinList);
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }

    private Integer getInsNonPostFirst(BizReceiptOutputHeadDTO headDTO, BizReceiptOutputItemDTO itemDTO, Integer insDocRid,
                                       List<StockInsDocBatch> insDocBatchList, List<StockInsDocBin> insDocBinList) {
        for (BizReceiptOutputBinDTO binDTO : itemDTO.getBinDTOList()) {
            // 批次库存增加
            StockInsDocBatch insDocBatch = new StockInsDocBatch();
            insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
            insDocBatch.setMatId(itemDTO.getMatId());
            insDocBatch.setBatchId(binDTO.getBatchId());
            insDocBatch.setFtyId(itemDTO.getFtyId());
            insDocBatch.setLocationId(itemDTO.getLocationId());
            insDocBatch.setUnitId(itemDTO.getUnitId());
            insDocBatch.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBatch.setDocDate(itemDTO.getDocDate());
            insDocBatch.setPostingDate(itemDTO.getPostingDate());
            insDocBatch.setMoveQty(binDTO.getQty());
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBatch.setPreReceiptHeadId(headDTO.getId());
            insDocBatch.setPreReceiptItemId(itemDTO.getId());
            insDocBatch.setPreReceiptBinId(binDTO.getId());
            insDocBatch.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue());
            insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
            insDocBatch.setReferReceiptHeadId(0L);
            insDocBatch.setReferReceiptItemId(0L);
            insDocBatch.setReferReceiptType(0);
            insDocBatch.setMatDocCode(itemDTO.getMatDocCode());
            insDocBatch.setMatDocRid(itemDTO.getMatDocRid());
            insDocBatch.setMatDocYear(itemDTO.getMatDocYear());
            insDocBatch.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
            insDocBatch.setCreateUserId(itemDTO.getCreateUserId());
            insDocBatchList.add(insDocBatch);

            // 出库临时区增加
            StockInsDocBin insDocBin = new StockInsDocBin();
            insDocBin.setInsDocRid(insDocRid.toString());
            insDocBin.setMatId(itemDTO.getMatId());
            insDocBin.setBatchId(binDTO.getBatchId());
            insDocBin.setFtyId(itemDTO.getFtyId());
            insDocBin.setLocationId(itemDTO.getLocationId());
            insDocBin.setUnitId(itemDTO.getUnitId());
            insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBin.setMoveQty(binDTO.getQty());
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBin.setPreReceiptHeadId(headDTO.getId());
            insDocBin.setPreReceiptItemId(itemDTO.getId());
            insDocBin.setPreReceiptBinId(binDTO.getId());
            insDocBin.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue());
            insDocBin.setReferReceiptHeadId(0L);
            insDocBin.setReferReceiptItemId(0L);
            insDocBin.setReferReceiptType(0);
            insDocBin.setMatDocCode(itemDTO.getMatDocCode());
            insDocBin.setMatDocRid(itemDTO.getMatDocRid());
            insDocBin.setMatDocYear(itemDTO.getMatDocYear());
            insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
            String typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
            insDocBin.setTypeId(typeId);
            insDocBin.setBinId(binId);
            insDocBin.setWhId(itemDTO.getWhId());
            insDocBin.setCellId(Objects.isNull(binDTO.getCellId()) ? 0 : binDTO.getCellId());
            insDocBin.setCreateUserId(itemDTO.getCreateUserId());

            insDocBinList.add(insDocBin);
            insDocRid++;
        }
        return insDocRid;
    }

    /**
     * 生成作业ins凭证
     *
     * @param headDTO
     * @return
     */
    public StockInsMoveTypeDTO generateTaskInsDoc(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        List<StockInsDocBinPo> insDocBinPoList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            if (UtilCollection.isEmpty(itemDTO.getBinDTOList())) {
                continue;
            }
            for (BizReceiptOutputBinDTO binDTO : itemDTO.getBinDTOList()) {
                BigDecimal moveQty = binDTO.getQty();
                // 先过账模式 作业一半时，操作实际已作业数量
                if (binDTO.getTaskQty().compareTo(binDTO.getQty()) != 0) {
                    moveQty = binDTO.getTaskQty();
                }
                // 增加实际仓位库存
                StockInsDocBin insDocBin = new StockInsDocBin();
                insDocBin.setInsDocRid(insDocRid.toString());
                insDocBin.setMatId(itemDTO.getMatId());
                insDocBin.setBatchId(binDTO.getBatchId());
                insDocBin.setFtyId(itemDTO.getFtyId());
                insDocBin.setLocationId(itemDTO.getLocationId());
                insDocBin.setTypeId(binDTO.getTypeId());
                insDocBin.setBinId(binDTO.getBinId());
                insDocBin.setWhId(itemDTO.getWhId());
                insDocBin.setUnitId(itemDTO.getUnitId());
                insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
                insDocBin.setMoveQty(moveQty);
                insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
                insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBin.setPreReceiptHeadId(headDTO.getId());
                insDocBin.setPreReceiptItemId(itemDTO.getId());
                insDocBin.setPreReceiptBinId(binDTO.getId());
                insDocBin.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue());
                insDocBin.setReferReceiptHeadId(0L);
                insDocBin.setReferReceiptItemId(0L);
                insDocBin.setReferReceiptType(0);
                insDocBin.setMatDocCode(itemDTO.getMatDocCode());
                insDocBin.setMatDocRid(itemDTO.getMatDocRid());
                insDocBin.setMatDocYear(itemDTO.getMatDocYear());
                insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                insDocBin.setCellId(Objects.isNull(binDTO.getCellId()) ? 0 : binDTO.getCellId());
                insDocBin.setCreateUserId(itemDTO.getCreateUserId());
                insDocBinList.add(insDocBin);
                insDocRid++;
                // 设置标签集合
                if (UtilCollection.isNotEmpty(binDTO.getLabelReceiptRelDTOList())) {
                    StockInsDocBinPo insDocBinPo = UtilBean.deepCopyNewInstance(insDocBin, StockInsDocBinPo.class);
                    insDocBinPo.setLabelIdList(binDTO.getLabelReceiptRelDTOList().stream()
                            .map(BizLabelReceiptRelDTO::getLabelId).collect(Collectors.toList()));
                    insDocBinPoList.add(insDocBinPo);
                }
                // 扣减出库临时区仓位库存
                insDocBin = new StockInsDocBin();
                insDocBin.setInsDocRid(insDocRid.toString());
                insDocBin.setMatId(itemDTO.getMatId());
                insDocBin.setBatchId(binDTO.getBatchId());
                insDocBin.setFtyId(itemDTO.getFtyId());
                insDocBin.setLocationId(itemDTO.getLocationId());
                String typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
                String binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
                Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), typeCode);
                Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode);
                insDocBin.setTypeId(typeId);
                insDocBin.setBinId(binId);
                insDocBin.setWhId(itemDTO.getWhId());
                insDocBin.setUnitId(itemDTO.getUnitId());
                insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
                insDocBin.setMoveQty(moveQty);
                insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
                insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                insDocBin.setPreReceiptHeadId(headDTO.getId());
                insDocBin.setPreReceiptItemId(itemDTO.getId());
                insDocBin.setPreReceiptBinId(binDTO.getId());
                insDocBin.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue());
                insDocBin.setReferReceiptHeadId(0L);
                insDocBin.setReferReceiptItemId(0L);
                insDocBin.setReferReceiptType(0);
                insDocBin.setMatDocCode(itemDTO.getMatDocCode());
                insDocBin.setMatDocRid(itemDTO.getMatDocRid());
                insDocBin.setMatDocYear(itemDTO.getMatDocYear());
                insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                insDocBin.setCellId(Objects.isNull(binDTO.getCellId()) ? 0 : binDTO.getCellId());
                insDocBin.setCreateUserId(itemDTO.getCreateUserId());
                insDocBinList.add(insDocBin);
                insDocRid++;
            }
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        insMoveTypeVo.setInsDocBinPoList(insDocBinPoList);
        return insMoveTypeVo;
    }

}