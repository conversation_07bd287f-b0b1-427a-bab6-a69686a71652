package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizMaterialReturnHeadDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizMaterialReturnItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizMaterialReturnUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizMaterialReturnWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumImageBizType;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnHeadDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnItemDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnUserDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnWaybillDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnHead;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnItem;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnUser;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnWaybill;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.mat.info.po.BizMaterialReturnSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.BizMaterialReturnHeadVO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilPrint;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @description 物资返运组件库
 */
@Service
@Slf4j
public class UnitizedMaterialReturnComponent {


    @Autowired
    private BizMaterialReturnHeadDataWrap bizMaterialReturnHeadDataWrap;

    @Autowired
    private BizMaterialReturnItemDataWrap bizMaterialReturnItemDataWrap;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    private PurchaseReceiptService purchaseReceiptService;

    @Autowired
    private BizMaterialReturnUserDataWrap bizMaterialReturnUserDataWrap;

    @Autowired
    private BizMaterialReturnWaybillDataWrap bizMaterialReturnWaybillDataWrap;
    @Autowired
    protected ReceiptRelationService receiptRelationService;
    @Autowired
    protected DictionaryService dictionaryService;
    /**
     * 物资返运列表-分页
     *
     * @param ctx
     */
    public void getPage(BizContext ctx) {
        BizMaterialReturnSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("获取单位关系列表 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new BizMaterialReturnSearchPO();
        }
        if(StringUtils.isNotEmpty(po.getChildMatCode())){
            Long matId = dictionaryService.getMatIdByMatCode(po.getChildMatCode());
            po.setChildMatId(matId);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        // 查询条件设置
        WmsQueryWrapper<BizMaterialReturnSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .eq(UtilString.isNotNullOrEmpty(po.getReferReceiptCode()), BizMaterialReturnSearchPO::getReferReceiptCode, BizMaterialReturnItem.class, po.getReferReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizMaterialReturnSearchPO::getMatCode, DicMaterial.class, po.getMatCode())
                .eq(UtilNumber.isNotNull(po.getChildMatId()), BizMaterialReturnSearchPO::getMatId,
                        BizReceiptWaybill.class, po.getChildMatId())
                .eq(true,BizMaterialReturnSearchPO::getReceiptType, BizMaterialReturnHead.class, EnumReceiptType.UNITIZED_STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizMaterialReturnSearchPO::getReceiptStatus,
                        po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList),  BizMaterialReturnSearchPO::getLocationId,
                        BizReceiptWaybill.class,locationIdList)
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizMaterialReturnSearchPO::getReceiptCode, BizMaterialReturnHead.class, po.getReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getMatName()), BizMaterialReturnSearchPO::getMatName, DicMaterial.class, po.getMatName())
                .eq(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizMaterialReturnSearchPO::getUserName, SysUser.class, po.getCreateUserName())
                .between(Objects.nonNull(po.getCreateTime()), BizMaterialReturnSearchPO::getCreateTime,BizMaterialReturnHead.class, po.getCreateTime(), po.getEndTime());

        IPage<BizMaterialReturnHeadVO> page = po.getPageObj(BizMaterialReturnHeadVO.class);
        bizMaterialReturnHeadDataWrap.getDicMaterialReturnPageVOListUnitized(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    public void check(BizContext ctx) {
        // 入参上下文 - 要保存的入库单
        BizMaterialReturnHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 返运数量不能为空
        po.getItemDTOList().forEach(bizMaterialReturnItemDTO -> {
            if (bizMaterialReturnItemDTO.getReturnQty().compareTo(BigDecimal.ZERO)<1){
                throw new WmsException(EnumReturnMsg.MATERIAL_RETURN_CODE_RETURN_QTY_NO_ZERO);
            }
        });
    }

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;


    /**
     * 新增物料返运
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInput(BizContext ctx) {
        // 入参上下文 - 要保存的入库单
        BizMaterialReturnHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = po.getReceiptCode();
//        if (po.getIsSubmit()==0){
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
//        }else {
//            po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
//        }
        if(UtilNumber.isEmpty(po.getId())){
            po.setCreateUserId(user.getId());
        }

        po.setModifyUserId(user.getId());
        po.setModifyTime(UtilDate.getNow());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {

            // 更新入库单
            bizMaterialReturnHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteInputItem(po);
            this.deleteInputWaybill(po);
            // 修改前删除user
            QueryWrapper<BizMaterialReturnUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BizMaterialReturnUser::getHeadId, po.getId());
            List<BizMaterialReturnUser> entityList = bizMaterialReturnUserDataWrap.list(queryWrapper);
            if (!CollectionUtils.isEmpty(entityList)) {
                List<Long> idList = entityList.stream().map(BizMaterialReturnUser::getId).collect(Collectors.toList());
                bizMaterialReturnUserDataWrap.multiPhysicalDeleteByIdList(idList);
            }
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            po.setCreateTime(UtilDate.getNow());
            //根据类型生成单据code
//            stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.MATERIAL_RETURN_SAVE.getValue());
            receiptCode = bizCommonService.getNextSeqDelivery(user, EnumSequenceCode.DELIVERY_NUM.getValue());
            po.setReceiptCode(receiptCode);
            bizMaterialReturnHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存成套返运单head成功!单号{},主键{}", receiptCode, po.getId());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        List<BizMaterialReturnItemDTO> itemDTOList = po.getItemDTOList();
        for (BizMaterialReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setId(null);
            itemDTO.setHeadId(po.getId());
//            if (po.getIsSubmit() == 0) {
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
//            } else {
//                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
//            }
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        bizMaterialReturnItemDataWrap.saveBatchDto(itemDTOList);
        log.debug("保存成套返运单item成功!单号{},headId{}", receiptCode, po.getId());

        // 保存运单信息
        List<BizMaterialReturnWaybillDTO> waybillDTOList = new ArrayList<>();
        for (BizMaterialReturnItemDTO itemDTO : itemDTOList) {
            Integer bid = 0;
            for (BizMaterialReturnWaybillDTO waybillDTO : itemDTO.getWaybillDTOList()) {
                waybillDTO.setId(null);
                waybillDTO.setHeadId(po.getId());
                waybillDTO.setItemId(itemDTO.getId());
                waybillDTO.setBid(String.valueOf(++bid));
                waybillDTO.setCreateUserId(user.getId());
                waybillDTO.setModifyUserId(user.getId());
                waybillDTO.setCreateTime(UtilDate.getNow());
                waybillDTO.setModifyTime(UtilDate.getNow());
                waybillDTOList.add(waybillDTO);
            }
        }
        bizMaterialReturnWaybillDataWrap.saveBatchDto(waybillDTOList);
        log.debug("保存成套返运单waybill成功!单号{},headId{}", receiptCode, po.getId());

        // 保存质检单附件
        receiptAttachmentService.saveBizReceiptAttachment(po.getFileList(), po.getId(), po.getReceiptType(), po.getCreateUserId());

        if(po.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_PURCHASE_RETURN.getValue()) && UtilCollection.isNotEmpty(po.getInspectUserList())) {
            /* ********************** user处理开始 *************************/
            AtomicInteger rids = new AtomicInteger(1);
            for (BizMaterialReturnUserDTO userDTO : po.getInspectUserList()) {
                userDTO.setId(null);
                userDTO.setHeadId(po.getId());
                userDTO.setRid(Integer.toString(rids.getAndIncrement()));
                userDTO.setCreateUserId(user.getId());
                userDTO.setModifyUserId(user.getId());
                if(userDTO.getInspectUserName()!=null && userDTO.getCreateTime()==null){
                    userDTO.setCreateTime(UtilDate.getNow());
                    userDTO.setModifyTime(UtilDate.getNow());
                }
            }
            bizMaterialReturnUserDataWrap.saveBatchDto(po.getInspectUserList());
            /* ********************** user处理开始 *************************/
        }
        /* ********************** item处理结束 *************************/
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的入库单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, po);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, po.getId());
    }


    public void fillSign(BizMaterialReturnHeadDTO  headDTO){
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        headDTO.setSign5(UtilPrint.SIGNATURE);
        headDTO.setSign6(UtilPrint.SIGNATURE);
        headDTO.setSign7(UtilPrint.SIGNATURE);
        List<BizMaterialReturnUserDTO> constUserDTOList = JSON.parseArray(Const.unitizedMaterialReturnUserJson,BizMaterialReturnUserDTO.class);
        List<BizMaterialReturnUserDTO> userDTOList = headDTO.getInspectUserList();
        if (!org.springframework.util.CollectionUtils.isEmpty(userDTOList)) {
            for (int i = 0; i < userDTOList.size(); i++) {
                if(i>6){
                    continue;
                }
                BizMaterialReturnUserDTO userDTO = userDTOList.get(i);
                BizMaterialReturnUserDTO constUserDTO = constUserDTOList.get(i);
                String constInspectCompany=constUserDTO.getInspectCompany();
                BizMaterialReturnUserDTO inspectUserDTO = UtilBean.newInstance(userDTO, BizMaterialReturnUserDTO.class);
                inspectUserDTO.setInspectCompany(constInspectCompany);
                constUserDTOList.set(i,inspectUserDTO);
            }
        }
        for (int i = 0; i < constUserDTOList.size(); i++) {
            BizMaterialReturnUserDTO userDTO = constUserDTOList.get(i);
            String inspectUserName = userDTO.getInspectUserName();
            String inspectCompany = userDTO.getInspectCompany();
            Date signDate = userDTO.getCreateTime();
            if (StringUtils.isNoneBlank(inspectUserName) && inspectUserName.startsWith("data:image/svg")) {
                if (i == 0) {
                    headDTO.setSign1(inspectUserName);
                    headDTO.setSignCompany1(inspectCompany);
                    headDTO.setSignDate1(signDate);
                    continue;
                }
                if (i == 1) {
                    headDTO.setSign2(inspectUserName);
                    headDTO.setSignCompany2(inspectCompany);
                    headDTO.setSignDate2(signDate);
                    continue;
                }
                if (i == 2) {
                    headDTO.setSign3(inspectUserName);
                    headDTO.setSignCompany3(inspectCompany);
                    headDTO.setSignDate3(signDate);
                    continue;
                }
                if (i == 3) {
                    headDTO.setSign4(inspectUserName);
                    headDTO.setSignCompany4(inspectCompany);
                    headDTO.setSignDate4(signDate);
                    continue;
                }
                if (i == 4) {
                    headDTO.setSign5(inspectUserName);
                    headDTO.setSignCompany5(inspectCompany);
                    headDTO.setSignDate5(signDate);
                    continue;
                }
                if (i == 5) {
                    headDTO.setSign6(inspectUserName);
                    headDTO.setSignCompany6(inspectCompany);
                    headDTO.setSignDate6(signDate);
                    continue;
                }
                if (i == 6) {
                    headDTO.setSign7(inspectUserName);
                    headDTO.setSignCompany7(inspectCompany);
                    headDTO.setSignDate7(signDate);
                }
            }
        }
        headDTO.setInspectUserList(constUserDTOList);

    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的入库单
     */
    public void saveReceiptTree(BizMaterialReturnHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for ( BizMaterialReturnItemDTO item : headDTO.getItemDTOList()) {
            if (UtilNumber.isNotEmpty(item.getPreReceiptType()) && UtilNumber.isNotEmpty(item.getPreReceiptHeadId())
                    && UtilNumber.isNotEmpty(item.getPreReceiptItemId())) {
                BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                        .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                        .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                        .setPreReceiptItemId(item.getPreReceiptItemId());
                dtoList.add(dto);
            }
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    public void deleteInputWaybill(BizMaterialReturnHeadDTO po) {
        UpdateWrapper<BizMaterialReturnWaybill> wrapperItem = new UpdateWrapper<>();
        wrapperItem.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizMaterialReturnWaybill::getHeadId, po.getId());
        bizMaterialReturnWaybillDataWrap.physicalDelete(wrapperItem);
    }

    /**
     * 保存批次图片
     */
    public void saveBizBatchImg(BizContext ctx) {
        // 入参上下文
        BizMaterialReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizBatchImgDTO> batchImgList=new ArrayList<>();
        for (BizMaterialReturnItemDTO itemDto : headDTO.getItemDTOList()) {
            if (UtilCollection.isNotEmpty(itemDto.getWaybillDTOList()) ){
                for (BizMaterialReturnWaybillDTO returnWaybillDto : itemDto.getWaybillDTOList()) {
                    if (UtilCollection.isNotEmpty(returnWaybillDto.getBizBatchImgDTOList())) {
                        returnWaybillDto.getBizBatchImgDTOList().forEach(imgDTO -> {
                            imgDTO.setId(null);
                            imgDTO.setMatId(returnWaybillDto.getWaybillDTO().getMatId());
                            imgDTO.setFtyId(returnWaybillDto.getWaybillDTO().getFtyId());
                            imgDTO.setImgBizType(EnumImageBizType.SIGN_INSPECTION.getValue());
                            imgDTO.setReceiptType(headDTO.getReceiptType());
                            imgDTO.setReceiptHeadId(headDTO.getId());
                            imgDTO.setReceiptItemId(returnWaybillDto.getId());
                            imgDTO.setCreateUserId(ctx.getCurrentUser().getId());
                            batchImgList.add(imgDTO);
                        });
                    } else {
                        returnWaybillDto.setBizBatchImgDTOList(new ArrayList<>());
                    }
                }
            }
        }
        // 批量保存质检单批次图片
        bizBatchImgService.multiSaveBizBatchImg(batchImgList);
    }

    /**
     * 删除物料返运入库单行项目
     *
     * @param po 要删除的入库信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInputItem(BizMaterialReturnHeadDTO po) {
        UpdateWrapper<BizMaterialReturnItem> wrapperItem = new UpdateWrapper<>();
        wrapperItem.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizMaterialReturnItem::getHeadId, po.getId());
        bizMaterialReturnItemDataWrap.physicalDelete(wrapperItem);
    }

    /**
     * 开启操单据流
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启单据流")}
     */
    public void setExtendRelation(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizMaterialReturnHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }
    /**
     * 按钮组
     *
     * @param headDTO 入库单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizMaterialReturnHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、编辑】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);

        } else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            buttonVO.setButtonPrint(true);
            // 已完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }


    /**
     * 根据headId查询出库单列表
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizMaterialReturnHeadDTO getItemListById(Long headId) {
        BizMaterialReturnHead inputHead = bizMaterialReturnHeadDataWrap.getById(headId);
        BizMaterialReturnHeadDTO headDTO = UtilBean.newInstance(inputHead, BizMaterialReturnHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }


    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizMaterialReturnHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    public void deleteReturnInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除其他入库单 ******** */
        if (po.isDeleteAll()) {
            // 删除其他入库单head
            bizMaterialReturnHeadDataWrap.removeById(po.getHeadId());
            // 删除其他入库单item
            UpdateWrapper<BizMaterialReturnItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizMaterialReturnItem::getHeadId, po.getHeadId());
            bizMaterialReturnItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                    EnumReceiptType.STOCK_INPUT_OTHER.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                    ctx.getCurrentUser().getId());
        } else {
            // 删除其他入库单item
            bizMaterialReturnItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 更新单据状态 - 已完成
     *
     * @param ctx
     */
    public void updateStatusCompleted(BizContext ctx) {
        // 入参上下文
        BizMaterialReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新入库单 - 已完成
        this.updateStatus(headDTO, headDTO.getItemDTOList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新单据状态 - 已编辑
     */
    public void updateStatusEdit(BizContext ctx) {
        // 入参上下文
        BizMaterialReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新入库单 - 已编辑
        this.updateStatus(headDTO, headDTO.getItemDTOList(), EnumReceiptStatus.RECEIPT_STATUS_EDIT.getValue());
    }

    /**
     * 准备更新物资返运单状态
     *
     * @param headDTO     head
     * @param itemDTOList item
     * @param status      要修改的单据状态
     */
    public void updateStatus(BizMaterialReturnHeadDTO headDTO, List<BizMaterialReturnItemDTO> itemDTOList, Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新物资返运行项目状态
     *
     * @param itemDTOList
     */
    private void updateItem(List<BizMaterialReturnItemDTO> itemDTOList) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            bizMaterialReturnItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }

    /**
     * 更新物资返运抬头状态
     *
     * @param headDto 入库单head
     */
    private void updateHead(BizMaterialReturnHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizMaterialReturnHeadDataWrap.updateDtoById(headDto);
        }
    }

}
