package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.maintain.service.datawrap.BizReceiptMaintainHeadDataWrap;
import com.inossem.wms.bizdomain.maintain.service.datawrap.BizReceiptMaintainItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.maintain.EnumMaintenanceType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleMaintainDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainHeadDTO;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainItemDTO;
import com.inossem.wms.common.model.bizdomain.maintain.entity.BizReceiptMaintainHead;
import com.inossem.wms.common.model.bizdomain.maintain.entity.BizReceiptMaintainItem;
import com.inossem.wms.common.model.bizdomain.maintain.po.BizReceiptMaintainSearchPO;
import com.inossem.wms.common.model.bizdomain.maintain.po.BizReceiptMaintainSearchStockPO;
import com.inossem.wms.common.model.bizdomain.maintain.po.MaintainPlanImport;
import com.inossem.wms.common.model.bizdomain.maintain.vo.BizReceiptMaintainPageVO;
import com.inossem.wms.common.model.bizdomain.maintain.vo.BizReceiptMaintainPlanVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 维保计划 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@Service
public class UnitizedMaintainPlanComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BatchInfoService batchInfoService;

    @Autowired
    protected StockCommonService stockCommonService;

    @Autowired
    protected UnitizedMaintainCommonComponent maintainCommonComponent;

    @Autowired
    protected BizReceiptMaintainHeadDataWrap bizReceiptMaintainHeadDataWrap;
    @Autowired
    protected BizReceiptMaintainItemDataWrap bizReceiptMaintainItemDataWrap;
    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    protected SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;

    /**
     * 查询维保类型下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("EnumMaintenanceType.toList()":"维保类型下拉框")}
     */
    public void getMaintenanceTypeDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumMaintenanceType.toCreateList()));
    }

    /**
     * 页面初始化
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"维保计划单明细","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化返回空的行项目、扩展项对象、按钮组【保存和提交】
        BizResultVO<BizReceiptMaintainHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptMaintainHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_STOCK_MAINTAIN_PLAN.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 页面初始化数据放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询维保计划列表-分页
     *
     * @in ctx 入参 {@link BizReceiptMaintainSearchPO :"维保分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptMaintainSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptMaintainPageVO> page = po.getPageObj(BizReceiptMaintainPageVO.class);
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        bizReceiptMaintainHeadDataWrap.selectPageVOListUnitized(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 维保计划单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"维保计划单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取维保集合单
        BizReceiptMaintainHeadDTO headDTO = UtilBean.newInstance(bizReceiptMaintainHeadDataWrap.getById(headId), BizReceiptMaintainHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置维保计划单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 维保单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptMaintainHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        return buttonVO;
    }

    /**
     * 提交维保计划单
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "要提交的维保计划单"}
     * @out ctx 出参 {"receiptCode" : "维保计划单单号"}
     */
    public void submitMaintainPlan(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        BizReceiptMaintainHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交寿维保单时赋值提交时间提交人
        headDTO.setSubmitTime(UtilDate.getNow());
        headDTO.setSubmitUserId(user.getId());
        // 保存维保单
        maintainCommonComponent.saveMaintain(ctx);
    }

    /**
     * 更新批次保养大纲
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "要提交的维保计划单"}
     */
    public void updateBatchMaintenanceProgram(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(po.getMaintenanceType().equals(EnumMaintenanceType.SPECIAL_MAINTAIN.getValue())) {
            List<BizReceiptMaintainItemDTO> itemDTOList = po.getItemList();
            int size = itemDTOList.size();
            Map<Long, BizReceiptMaintainItemDTO> itemDTOMap = new HashMap<>(size);
            Set<Long> batchIdSet = new HashSet<>(size);
            for (BizReceiptMaintainItemDTO itemDTO : itemDTOList) {
                Long batchId = itemDTO.getBatchId();
                if (batchIdSet.add(batchId)) {
                    String program = itemDTO.getMaintenanceProgram();
                    if (StringUtils.isBlank(program))
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                    itemDTOMap.put(batchId, itemDTO);
                }
            }
            // 更新批次保养大纲
            List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getBatchInfoList(new ArrayList<>(batchIdSet));
            batchInfoDTOList.forEach(p -> {
                Long id = p.getId();
                BizReceiptMaintainItemDTO itemDTO = itemDTOMap.get(id);
                p.setMaintenanceProgram(itemDTO.getMaintenanceProgram());
            });
            batchInfoService.multiUpdateBatchInfo(batchInfoDTOList);
        }
    }

    /**
     * 生成维保结果维护单
     *
     * @in ctx 入参 {@link BizReceiptMaintainHeadDTO : "维保计划单"}
     */
    public void genMaintainResult(BizContext ctx) {
        // 入参上下文
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        BizReceiptMaintainHeadDTO headDTO = new BizReceiptMaintainHeadDTO();
        List<BizReceiptMaintainItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_MAINTAIN_RESULT.getValue())
                .setMaintenanceType(po.getMaintenanceType())
                .setExecuteUserId(po.getExecuteUserId())
                .setRemark(po.getRemark())
                .setPlanCompleteDate(po.getPlanCompleteDate())
                .setUnit(po.getUnit());
        for (BizReceiptMaintainItemDTO itemDTO : po.getItemList()) {
            BizReceiptMaintainItemDTO resultItemDTO = UtilBean.newInstance(itemDTO, BizReceiptMaintainItemDTO.class);
            resultItemDTO.setPreReceiptHeadId(po.getId());
            resultItemDTO.setPreReceiptItemId(itemDTO.getId());
            resultItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_STOCK_MAINTAIN_PLAN.getValue());
            resultItemDTO.setPreReceiptQty(itemDTO.getQty());
            itemDTOList.add(resultItemDTO);
        }
        headDTO.setItemList(itemDTOList);
        // 设置入参上下文
        BizContext ctxResult = new BizContext();
        ctxResult.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxResult.setCurrentUser(ctx.getCurrentUser());
        // 推送MQ生成维保结果维护单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_MAINTAIN_RESULT_STOCK_CE, ctxResult);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 查询物料批次库存
     *
     * @in ctx 入参 {@link BizReceiptMaintainSearchStockPO : "维保查询物料批次库存入参"}
     * @out ctx 出参 {@link SingleResultVO ("matStockDTO":"物料批次库存")}
     */
    public void getMatFeatureStock(BizContext ctx) {
        // 获取上下文
        BizReceiptMaintainSearchStockPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptMaintainItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        Set<Long> matIdSet = null;
        String matCode = po.getMatCode();
        if (UtilString.isNotNullOrEmpty(matCode)) {
            if (matCode.contains(",")) {
                matIdSet = new HashSet<>();
                String[] matCodes = matCode.split(",");
                for (String code : matCodes) {
                    if (StringUtils.isBlank(code))
                        continue;
                    Long codeId = dictionaryService.getMatIdByMatCode(code);
                    if (Objects.isNull(codeId))
                        continue;
                    matIdSet.add(codeId);
                }
                if (CollectionUtils.isEmpty(matIdSet)) {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                    return;
                }
            } else {
                matId = dictionaryService.getMatIdByMatCode(matCode);
                if (Objects.isNull(matId)) {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                    return;
                }
            }
        }
        Long binId = null;
        if (UtilString.isNotNullOrEmpty(po.getBinCode())) {
            binId = dictionaryService.getBinCacheByCode(po.getBinCode()).getId();
            if (Objects.isNull(binId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        // 入参封装
        BizReceiptAssembleRuleSearchPO searchPO = new BizReceiptAssembleRuleSearchPO();
        searchPO.setIsUnitized(true);
        searchPO.setFtyId(po.getFtyId());
        searchPO.setLocationId(po.getLocationId());
        searchPO.setMatId(matId);
        searchPO.setMatIdSet(matIdSet);
        searchPO.setBinId(binId);
        searchPO.setMaintenanceType(po.getMaintenanceType());
        Integer maintainType = po.getMaintenanceType();
        if (EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(maintainType)) {
            searchPO.setMaintenanceValidDateStart(po.getMaintenanceValidDateStart());
            searchPO.setMaintenanceValidDateEnd(po.getMaintenanceValidDateEnd());
        } else if (EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(maintainType)) {
            searchPO.setMaintenanceValidDateStart(po.getMaintenanceValidDateStart());
            searchPO.setMaintenanceValidDateEnd(po.getMaintenanceValidDateEnd());
//            searchPO.setMaintainProFlag(EnumRealYn.TRUE.getIntValue());
        }
        searchPO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 根据特性code查询特性库存
        List<BizReceiptAssembleMaintainDTO> assembleDTOList = stockCommonService.getMaintainStockBySdwUnitized(po.getReceiptType(), searchPO);
        MatStockDTO matStockDTO = new MatStockDTO();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            // 特性库存转行项目
            for (BizReceiptAssembleMaintainDTO assembleDTO : assembleDTOList) {
                BizReceiptMaintainItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptMaintainItemDTO.class);
                itemDTO.setQty(assembleDTO.getStockQty());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTO.setBinId(assembleDTO.getBinIdTemp());
                itemDTO.setBinCode(assembleDTO.getBinCodeTemp());
                itemDTOList.add(itemDTO);
            }
            BizReceiptAssembleMaintainDTO firstItem = assembleDTOList.get(0);
            matStockDTO.setFeatureCode(firstItem.getRuleFeatureCode());
            matStockDTO.setFeatureName(firstItem.getRuleFeatureName());
        }
        matStockDTO.setMaintainItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * 查询物料批次库存导入
     *
     * @param ctx-po 查询物料批次库存入参
     * @return 物料批次库存
     */
    public void importMaterial(BizContext ctx) {
        // 上下文获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        String poStr = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptMaintainSearchStockPO po = JSON.parseObject(poStr, BizReceiptMaintainSearchStockPO.class);
        po.setReceiptType(EnumReceiptType.UNITIZED_STOCK_MAINTAIN_PLAN.getValue());
        try {
            int maxCount = 200;
            // 获取EXCEL数据
            List<MaintainPlanImport> importList = (List<MaintainPlanImport>) UtilExcel.readExcelData(file.getInputStream(), MaintainPlanImport.class);
            if(importList.size() > maxCount){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMP_QTR, String.valueOf(maxCount));
            }
            List<BizReceiptMaintainItemDTO> itemDTOList = new ArrayList<>();
            MatStockDTO matStockDTO = new MatStockDTO();
            int i = 0;
            for(MaintainPlanImport importObj : importList){
                i++;
                try {
                    List<String> ftyCodeList = Arrays.asList(EnumFactory.J047.getFtyCode(), EnumFactory.Z047.getFtyCode(), EnumFactory.S047.getFtyCode());
                    if (!ftyCodeList.contains(importObj.getFtyCode())) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
                    }
                    Long ftyId = dictionaryService.getFtyIdCacheByCode(importObj.getFtyCode());
                    Long locationId = dictionaryService.getLocationIdCacheByCode(importObj.getFtyCode(), importObj.getLocationCode());
                    Long matId = dictionaryService.getMatIdByMatCode(importObj.getMatCode());
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDtoByCodeAndMatId(importObj.getBatchCode(), matId, null);
                    if (UtilObject.isNull(batchInfoDTO)) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_BATCH_MSG_FAILURE);
                    }
                    // 入参封装
                    BizReceiptAssembleRuleSearchPO searchPO = new BizReceiptAssembleRuleSearchPO();
                    searchPO.setIsUnitized(true);
                    searchPO.setFtyId(ftyId);
                    searchPO.setLocationId(locationId);
                    searchPO.setMatId(matId);
                    searchPO.setBatchId(batchInfoDTO.getId());
                    Integer maintainType = po.getMaintenanceType();
                    searchPO.setMaintenanceType(maintainType);
                    if (EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(maintainType)) {
                        searchPO.setMaintenanceValidDateStart(po.getMaintenanceValidDateStart());
                        searchPO.setMaintenanceValidDateEnd(po.getMaintenanceValidDateEnd());
                    } else if (EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(maintainType)) {
                        searchPO.setMaintenanceValidDateStart(po.getMaintenanceValidDateStart());
                        searchPO.setMaintenanceValidDateEnd(po.getMaintenanceValidDateEnd());
//                        searchPO.setMaintainProFlag(EnumRealYn.TRUE.getIntValue());
                    }
                    searchPO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                    // 根据特性code查询特性库存
                    List<BizReceiptAssembleMaintainDTO> assembleDTOList = stockCommonService.getMaintainStockBySdwUnitized(po.getReceiptType(), searchPO);
                    if (UtilCollection.isNotEmpty(assembleDTOList)) {
                        // 特性库存转行项目
                        for (BizReceiptAssembleMaintainDTO assembleDTO : assembleDTOList) {
                            BizReceiptMaintainItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptMaintainItemDTO.class);
                            itemDTO.setQty(assembleDTO.getStockQty());
                            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                            itemDTO.setBinId(assembleDTO.getBinIdTemp());
                            itemDTO.setBinCode(assembleDTO.getBinCodeTemp());
                            itemDTOList.add(itemDTO);
                        }
                        BizReceiptAssembleMaintainDTO firstItem = assembleDTOList.get(0);
                        matStockDTO.setFeatureCode(firstItem.getRuleFeatureCode());
                        matStockDTO.setFeatureName(firstItem.getRuleFeatureName());
                    } else {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_FOURKEY_QTY_NOT_ENOUGH);
                    }
                } catch (WmsException e) {
                    throw e;
                } catch (Exception e) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
                }
            }
            // 返回对象上下文
            matStockDTO.setMaintainItemDTOList(itemDTOList);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }


    /**
     * 维保计划导出
     *
     * @param ctx
     */
    public void exportPlan(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("成套设备-维保计划"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        BizReceiptMaintainHead head = bizReceiptMaintainHeadDataWrap.getById(headId);
        if (head == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        String receiptCode = head.getReceiptCode();
        // 获取维保集合单
        List<BizReceiptMaintainPlanVO> planVOList = bizReceiptMaintainItemDataWrap.exportByPlan(headId);
        dataFillService.fillAttr(planVOList);
        if (!CollectionUtils.isEmpty(planVOList)) {
            Map<Integer, String> depositMap = new HashMap<>();
            Map<Integer, String> pkgMap = new HashMap<>();
            String lang = Const.DEFAULT_LANG_CODE;
            planVOList.forEach(item -> {
                item.setReceiptCode(receiptCode);
                Integer depositType = item.getDepositType();
                if (depositType != null) {
                    String depositTypeI18n = depositMap.get(depositType);
                    if (depositTypeI18n == null) {
                        depositTypeI18n = i18nTextCommonService.getNameMessage(lang, "depositType", depositType.toString());
                        depositMap.put(depositType, depositTypeI18n);
                    }
                    item.setDepositTypeI18n(depositTypeI18n);
                }
                Integer pkgType = item.getPackageType();
                if (pkgType != null) {
                    String pkgTypeI18n = pkgMap.get(pkgType);
                    if (pkgTypeI18n == null) {
                        pkgTypeI18n = i18nTextCommonService.getNameMessage(lang, "packageType", pkgType.toString());
                        pkgMap.put(pkgType, pkgTypeI18n);
                    }
                    item.setPackageTypeI18n(pkgTypeI18n);
                }
            });
        }
        UtilExcel.writeExcel(BizReceiptMaintainPlanVO.class, planVOList, bizCommonFile);
        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

    public void startWorkFlow(BizContext ctx) {
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        BizReceiptMaintainHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        // 2023-08-02 再次修改,仅保留包装更换(原日常维保)业务的审批
        // Integer receiptType = po.getMaintenanceType().equals(EnumMaintenanceType.DEFECT_MAINTAIN.getValue()) ? EnumReceiptType.STOCK_MAINTAIN_PLAN_DEFECTIVE.getValue() : po.getReceiptType();
        Integer receiptType = po.getReceiptType();
        // 发起流程审批
        // 缺陷维保移除审批，仅对非缺陷维保的进行
        // 2023-08-02再次修改,仅保留包装更换业务的审批
        // 2024-09-19再次修改,所有业务都要有审批
        // 2024-09-20再次修改,常规/特殊需要有审批, 库存状态检查无审批
        if (EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(po.getMaintenanceType())
                || EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(po.getMaintenanceType())
                || EnumMaintenanceType.FIRST_MAINTAIN.getValue().equals(po.getMaintenanceType())) {
            this.approverCheck(userDept);
        }

        String procId = null;
        // 2023-07-24 根据业务方要求，缺陷维保取消审批，为避免后期缺陷维保再独立启用审批，缺陷维保单据单据单独传递单据类型
        // 2023-08-02 再次修改,仅保留包装更换(原日常维保)业务的审批
        // 2024-09-19 再次修改,所有业务都要有审批
        // 2024-09-20 再次修改,常规/特殊需要有审批, 库存状态检查无审批
        if (EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(po.getMaintenanceType())
                || EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(po.getMaintenanceType())
                || EnumMaintenanceType.FIRST_MAINTAIN.getValue().equals(po.getMaintenanceType())) {
            Map<String, Object> variables = new HashMap<>();
            Long ftyId=po.getItemList().get(0).getFtyId();
            variables.put("ftyId", ftyId);
            // 用户所属部门、对口部门、对口科室
            variables.put("userDept", userDept);
            workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
            procId = workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        }

        if (UtilString.isNotNullOrEmpty(procId)) {
            // 更新 - 审批中
            UpdateWrapper<BizReceiptMaintainHead> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(BizReceiptMaintainHead::getId,po.getId()).set(BizReceiptMaintainHead::getReceiptStatus,EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
            bizReceiptMaintainHeadDataWrap.update(updateWrapper);
            List<Long> itemIdList = po.getItemList().stream().map(obj -> obj.getId()).collect(Collectors.toList());
            UpdateWrapper<BizReceiptMaintainItem> itemListUpdateWrapper = new UpdateWrapper<>();
            itemListUpdateWrapper.lambda().in(BizReceiptMaintainItem::getId,itemIdList).set(BizReceiptMaintainItem::getItemStatus,EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
            bizReceiptMaintainItemDataWrap.update(itemListUpdateWrapper);
        } else {
            // 找不到审批按照无审批处理，直接完成
            BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
            wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
            wfReceiptCo.setInitiator(ctx.getCurrentUser());
            wfReceiptCo.setReceiptHeadId(receiptId);
            this.approvalCallback(wfReceiptCo);
        }
    }

    private void approverCheck(List<MetaDataDeptOfficePO> userDept) {
        if (UtilCollection.isEmpty(userDept)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 发起人所属【物资领用主管】审批
        List<String> level1UserList = new ArrayList<>();
        for (MetaDataDeptOfficePO deptOfficePO : userDept) {
            level1UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptOfficePO.getDeptCode(), null, EnumApprovalLevel.LEVEL_3));
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "1");
        }
        // 发起人所属【物资部门经理】审批
        List<String> level2UserList = new ArrayList<>();
        for (MetaDataDeptOfficePO deptOfficePO : userDept) {
            level2UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptOfficePO.getDeptCode(), null, EnumApprovalLevel.LEVEL_6));
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "2");
        }
    }

    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            BizContext ctx = new BizContext();
            CurrentUser currentUser = wfReceiptCo.getInitiator();
            ctx.setCurrentUser(currentUser);
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            BizReceiptMaintainHead maintainHead = bizReceiptMaintainHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
            BizReceiptMaintainHeadDTO headDTO = UtilBean.newInstance(maintainHead, BizReceiptMaintainHeadDTO.class);
            dataFillService.fillAttr(headDTO);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
             // 更新单据已完成
             maintainCommonComponent.updateStatusCompleted(ctx);
             // 生成维保结果维护单
             this.genMaintainResult(ctx);
             //修改状态
             maintainCommonComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        } else {
            maintainCommonComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 获取维保执行人列表
     *
     * @in ctx 入参 {@link BizReceiptMaintainSearchPO : "查询用户列表入参"}
     * @out ctx 出参 {@link MultiResultVO <> ("sysUserDTOList":"用户列表")}
     */
    public void setUserList(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptMaintainSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<SysUser> page = new Page<>(po.getPageIndex(), po.getPageSize());
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getUserName()), SysUser::getUserName, po.getUserName());
        sysUserDataWrap.page(page, queryWrapper);
        List<SysUser> userList = page.getRecords();
        // 匹配处室科室信息
        List<SysUserDTO> userDTOList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(userList)) {
            userList.forEach(item -> {
                MetaDataDeptOfficePO deptOfficePO = new MetaDataDeptOfficePO();
                deptOfficePO.setUserId(item.getId());
                List<DicDeptDTO> dicDeptDTOList = dicDeptDataWrap.getUserDeptOfficeTree(deptOfficePO);
                Set<String> deptOfficeIds = new HashSet<>();
                List<String> deptOfficeNames = new ArrayList<>();
                dicDeptDTOList.forEach(deptDTO -> {
                    if (UtilCollection.isEmpty(deptDTO.getOfficeDTOList())) {
                        String deptId = String.valueOf(deptDTO.getId());
                        String deptName = deptDTO.getDeptName();
                        if (!deptOfficeIds.contains(deptId)) {
                            deptOfficeIds.add(String.valueOf(deptId));
                            deptOfficeNames.add(deptName);
                        }
                    } else {
                        deptDTO.getOfficeDTOList().forEach(officeDTO -> {
                            Long officeId = officeDTO.getId();
                            String officeName = officeDTO.getDeptOfficeName();
                            if (UtilObject.isNotEmpty(officeId)) {
                                deptOfficeIds.add(String.valueOf(officeId));
                                deptOfficeNames.add(officeName);
                            }
                            String deptId = String.valueOf(deptDTO.getId());
                            String deptName = deptDTO.getDeptName();
                            if (!deptOfficeIds.contains(deptId)) {
                                deptOfficeIds.add(String.valueOf(deptId));
                                deptOfficeNames.add(deptName);
                            }
                        });
                    }
                });
                SysUserDTO sysUserDTO = UtilBean.newInstance(item, SysUserDTO.class);
                if (UtilCollection.isNotEmpty(dicDeptDTOList)){
                    sysUserDTO.setDeptId(dicDeptDTOList.get(0).getId());
                    sysUserDTO.setDeptCode(dicDeptDTOList.get(0).getDeptCode());
                    sysUserDTO.setDeptName(dicDeptDTOList.get(0).getDeptName());
                }
                sysUserDTO.setDeptOfficeNames(String.join(",", deptOfficeNames));
                userDTOList.add(sysUserDTO);
            });
        }
        // 放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(userDTOList, page.getTotal()));
    }

}
