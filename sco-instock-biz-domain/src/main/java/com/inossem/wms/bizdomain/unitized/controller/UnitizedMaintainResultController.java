package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.maintain.service.biz.MaintainResultService;
import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedMaintainResultService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainHeadDTO;
import com.inossem.wms.common.model.bizdomain.maintain.po.BizReceiptMaintainSearchPO;
import com.inossem.wms.common.model.bizdomain.maintain.vo.BizReceiptMaintainPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 维保结果 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@RestController
public class UnitizedMaintainResultController {
    
    @Autowired
    protected UnitizedMaintainResultService maintainResultService;

    /**
     * 查询维保结果列表-分页
     *
     * @param po 维保分页查询入参
     * @return 维保结果单列表
     */
    @ApiOperation(value = "查询维保结果列表-分页", tags = {"维保管理-维保结果维护"})
    @PostMapping(value = "/unitized/maintain/maintain-result/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptMaintainPageVO>> getPage(@RequestBody BizReceiptMaintainSearchPO po, BizContext ctx) {
        maintainResultService.getPage(ctx);
        PageObjectVO<BizReceiptMaintainPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询维保结果单详情
     *
     * @param id 维保结果单抬头表主键
     * @return 维保结果单详情
     */
    @ApiOperation(value = "查询维保结果单详情", tags = {"维保管理-维保结果维护"})
    @GetMapping(value = "/unitized/maintain/maintain-result/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptMaintainHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        maintainResultService.getInfo(ctx);
        BizResultVO<BizReceiptMaintainHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 维保结果-保存
     *
     * @param po 保存维保结果表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "维保结果-保存", tags = {"维保管理-维保结果维护"})
    @PostMapping(value = "/unitized/maintain/maintain-result/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptMaintainHeadDTO po, BizContext ctx) {
        maintainResultService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 维保结果-提交
     *
     * @param po 提交维保结果表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "维保结果-提交", tags = {"维保管理-维保结果维护"})
    @PostMapping(value = "/unitized/maintain/maintain-result/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptMaintainHeadDTO po, BizContext ctx) {
        maintainResultService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 维保结果-删除
     *
     * @param id 维保结果单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "维保结果-删除", tags = {"维保管理-维保结果维护"})
    @DeleteMapping(value = "/unitized/maintain/maintain-result/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        maintainResultService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_DELETE_SUCCESS, receiptCode);
    }
    
}
