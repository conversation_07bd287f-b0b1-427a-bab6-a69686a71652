<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.report.dao.BizReportMapper">

    <select id="selectStockBatchDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBatchVO">
        SELECT
        dm.mat_code,
        dm.mat_name,
        dm.mat_name_en,
        dm.parent_mat_id,
        dmf.package_type,
        dmf.stock_group,
        du.unit_code,
        du.unit_name,
        dmg.mat_group_code,
        dmg.mat_group_name,
        dm.ext_manufacturer_part_number,
        dm.ext_main_material,
        dm.ext_industry_standard_desc,
        sb.qty,
        sb.qty_freeze,
        sb.qty_transfer,
        sb.qty_inspection,
        sb.qty_temp,
        sb.qty_haste,
        df.fty_code,
        df.fty_name,
        dl.location_code,
        dl.location_name,
        dw.wh_code,
        dw.wh_name,
        bi.id as batch_id,
        bi.batch_code,
        bi.delivery_notice_batch_code,
        bi.batch_erp,
        bi.spec_stock,
        bi.description,
        bi.spec_stock_code,
        bi.spec_stock_name,
        bi.purchase_receipt_code,
        bi.purchase_receipt_rid,
        bi.demand_dept,
        bi.contract_code,
        bi.contract_name,
        bi.supplier_code,
        bi.supplier_name,
        bi.demand_plan_code,
        bi.demand_plan_rid,
        bi.demand_user_name,
        bi.purchase_code,
        bi.purchase_rid,
        bi.head_contract_code,
        bi.head_contract_name,
        bi.head_supplier_code,
        bi.head_supplier_name,
        bi.input_date,
        bi.apply_user_name,
        bi.apply_user_dept_name,
        bi.apply_user_office_name,
        bi.posting_date,
        bi.mat_doc_code,
        bi.mat_doc_rid,
        bi.return_date,
        bi.guarantee_period,
        bi.extend2,
        bi.extend20,
        bi.extend24,
        bi.extend25,
        bi.extend26,
        bi.extend27,
        bi.extend28,
        bi.extend29,
        bi.extend31,
        bi.extend34,
        bi.extend35,
        bi.extend36,
        bi.extend37,
        bi.extend38,
        bi.extend40,
        bi.extend41,
        bi.extend42,
        bi.extend43,
        bi.extend44,
        bi.extend46,
        bi.extend47,
        bi.extend48,
        bi.extend49,
        bi.extend50,
        bi.extend60,
        bi.extend61,
        bi.extend62,
        bi.extend63,
        bi.extend64,
        bi.extend65,
        bi.extend66,
        bi.extend67,
        bi.extend68,
        bi.extend69,
        bi.extend70,
        bi.extend71,
        bi.extend72,
        bi.extend73,
        bi.extend74,
        bi.price as batch_price,
        bi.maintenance_cycle,
        bi.functional_location_code,
        bi.case_weight,
        bi.case_code,
        bi.package_form,
        bi.is_main_parts,
        bi.arrival_qty,
        bi.lifetime_date,
        bi.car_code,
        bi.driver_name,
        bi.contact_way,
        bi.invoice_no,
        bi.invoice_date,
        dm.shelf_life_min,
        dm.shelf_life_max,
        DATE_ADD(bi.maintenance_date,
        INTERVAL
        (case
        when 0=dmf.package_type then 0
        when 1=dmf.package_type then 5
        when 2=dmf.package_type then 5
        when 3=dmf.package_type then 2
        when 4=dmf.package_type then 3
        when 5=dmf.package_type then 1
        when 6=dmf.package_type then 0
        when 7=dmf.package_type then 5
        when 8=dmf.package_type then 5
        end)
        YEAR) AS maintenance_in_date,
        DATEDIFF(bi.lifetime_date, NOW()) AS remainder_days,
        DATEDIFF(now(), input_date) + 1 stock_age,
        bi.production_date,
        bi.shelf_line,
        ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END) price,
        (
        sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste
        ) * ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END) money,
        epri.contract_code
        FROM
        stock_batch sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id
        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id  AND epri.delete_tag = 0
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        left join dic_material_facotry_wbs dmfw on dmfw.mat_id = sb.mat_id
        and dmfw.fty_id = sb.fty_id and dmfw.spec_stock = bi.spec_stock and dmfw.spec_stock_code = bi.spec_stock_code
        WHERE 1=1
        <if test="po.isUnitized == true" >
            AND dm.mat_code LIKE 'CT%'
        </if>
        <if test="po.isUnitized == false" >
            AND dm.mat_code not LIKE 'CT%'
        </if>
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND sb.fty_id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND sb.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.whId != null" >
            AND dw.id = #{po.whId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.batchCodeList !=null and po.batchCodeList.size() > 0 ">
            AND bi.batch_code in
            <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockList !=null and po.specStockList.size() > 0 ">
            AND bi.spec_stock in
            <foreach collection="po.specStockList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
    </select>


    <select id="selectStockBatchDetailUnitized" resultType="com.inossem.wms.common.model.bizdomain.report.vo.UnitizedStockBatchVO">
        SELECT
        dm.mat_code,
        dm.mat_name,
        dm.parent_mat_id,
        dmf.package_type,
        dmf.stock_group,
        du.unit_code,
        du.unit_name,
        dmg.mat_group_code,
        dmg.mat_group_name,
        sb.qty,
        sb.qty_freeze,
        sb.qty_transfer,
        sb.qty_inspection,
        sb.qty_temp,
        sb.qty_haste,
        df.fty_code,
        df.fty_name,
        dl.location_code,
        dl.location_name,
        dlm.location_code main_location_code ,
        dlm.location_name main_location_name,
        dw.wh_code,
        dw.wh_name,
        bi.id as batch_id,
        bi.batch_code,
        bi.batch_erp,
        bi.spec_stock,
        bi.spec_stock_code,
        bi.spec_stock_name,
        bi.purchase_receipt_code,
        bi.purchase_receipt_rid,
        bi.demand_dept,
        bi.input_date,
        bi.apply_user_name,
        bi.apply_user_dept_name,
        bi.apply_user_office_name,
        bi.supplier_code,
        bi.supplier_name,
        bi.posting_date,
        bi.mat_doc_code,
        bi.mat_doc_rid,
        bi.return_date,
        bi.guarantee_period,
        bi.extend2,
        bi.extend20,
        bi.extend24,
        bi.extend25,
        bi.extend26,
        bi.extend27,
        bi.extend28,
        bi.extend29,
        bi.extend31,
        bi.extend34,
        bi.extend35,
        bi.extend36,
        bi.extend37,
        bi.extend38,
        bi.extend40,
        bi.extend41,
        bi.extend42,
        bi.extend43,
        bi.extend44,
        bi.extend46,
        bi.extend47,
        bi.extend48,
        bi.extend49,
        bi.extend50,
        bi.extend60,
        bi.extend61,
        bi.extend62,
        bi.extend63,
        bi.extend64,
        bi.extend65,
        bi.extend66,
        bi.extend67,
        bi.extend68,
        bi.extend69,
        bi.extend70,
        bi.extend71,
        bi.extend72,
        bi.extend73,
        bi.extend74,
        bi.price as batch_price,
        bi.maintenance_cycle,
        bi.functional_location_code,
        bi.case_weight,
        bi.case_code,
        bi.package_form,
        bi.is_main_parts,
        bi.arrival_qty,
        bi.lifetime_date,
        dm.shelf_life_min,
        dm.shelf_life_max,
        DATE_ADD(bi.maintenance_date,
        INTERVAL
        bi.maintenance_cycle
        YEAR) AS maintenance_in_date,
        DATEDIFF(bi.lifetime_date, NOW()) AS remainder_days,
        DATEDIFF(now(), input_date) + 1 stock_age,
        bi.production_date,
        bi.shelf_line,
        ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END) price,
        (
        sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste
        ) * ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END) money,
        epri.contract_code,
        bi.temp_store_dept_id,
        bi.temp_store_dept_office_id,
        bi.temp_store_expire_date,
        bi.temp_store_pre_receipt_code,
        bi.temp_store_user
        FROM
        stock_batch sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id
        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id  AND epri.delete_tag = 0
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        left join dic_material_facotry_wbs dmfw on dmfw.mat_id = sb.mat_id
        and dmfw.fty_id = sb.fty_id and dmfw.spec_stock = bi.spec_stock and dmfw.spec_stock_code = bi.spec_stock_code
        LEFT JOIN biz_receipt_waybill waybill on waybill.mat_id=sb.mat_id and waybill.pre_id = 0
        LEFT JOIN biz_receipt_delivery_notice_item brdni ON brdni.id = waybill.delivery_notice_item_id
        LEFT JOIN dic_stock_location dlm ON brdni.location_id = dlm.id
        WHERE 1=1
        <if test="po.isUnitized == true" >
            and dm.is_ct_code = 1
        </if>
        <if test="po.isUnitized == false" >
            and dm.is_ct_code = 0
        </if>
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND sb.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.whId != null" >
            AND dw.id = #{po.whId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
    </select>

    <select id="selectStockBatchGroupByMatGroup" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBatchVO">
        SELECT
            dmg.mat_group_code,
            dmg.mat_group_name,

            sum(
                (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 )
            ) money
        FROM
        stock_batch sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id AND epri.delete_tag = 0
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND dw.id = #{po.whId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null and po.specStock != '' ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
        GROUP BY dm.mat_group_id
    </select>

    <select id="selectStockBatchGroupByLocation" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBatchVO">
        SELECT
            df.fty_code,
            df.fty_name,
            dl.location_code,
            dl.location_name,
            dw.wh_code,
            dw.wh_name,

            sum(
            (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 )
            ) money
        FROM
        stock_batch sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id AND epri.delete_tag = 0
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND dw.id = #{po.whId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null and po.specStock != '' ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
        GROUP BY dl.id
    </select>

    <select id="selectStockBatchGroupByWh" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBatchVO">
        SELECT

            dw.wh_code,
            dw.wh_name,

            sum(
            (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 )
            ) money
        FROM
        stock_batch sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id AND epri.delete_tag = 0
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND dw.id = #{po.whId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null and po.specStock != '' ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
        GROUP BY dw.id
    </select>

    <!-- 查询 仓位库存详情-->
    <select id="selectStockBinDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBinVO">
        SELECT
        sb.id,
        sb.mat_id,
        sb.fty_id,
               sb.location_id,
               sb.batch_id,
               sb.wh_id,
               sb.type_id,
               sb.bin_id,
               sb.cell_id,
        dm.mat_code,
        dm.mat_name,
        dm.mat_name_en,
        du.unit_code,
        du.unit_name,
        dmg.mat_group_code,
        dmg.mat_group_name,
        dm.ext_manufacturer_part_number,
        dm.ext_main_material,
        dm.ext_industry_standard_desc,
        sb.qty,
        IFNULL(occupy_temp.qty, 0) as occupy_qty,
        sb.qty_freeze,
        sb.qty_transfer,
        sb.qty_inspection,
        sb.qty_temp,
        sb.qty_haste,
        df.fty_code,
        df.fty_name,
        dl.location_code,
        dl.location_name,
        dw.wh_code,
        dw.wh_name,
        bi.batch_code,
        bi.delivery_notice_batch_code,
        bi.batch_erp,
        bi.spec_stock,
        bi.description,
        bi.spec_stock_code,
        bi.spec_stock_name,
        bi.purchase_receipt_code,
        bi.purchase_receipt_rid,
        bi.demand_dept,
        bi.apply_user_name,
        bi.apply_user_dept_name,
        bi.apply_user_office_name,
        dm.shelf_life_min,
        dm.shelf_life_max,
        bi.maintenance_date_pro,
        bi.lifetime_date,
        bi.car_code,
        bi.driver_name,
        bi.contact_way,
        bi.invoice_no,
        bi.invoice_date,
        bi.currency,
        DATEDIFF(bi.lifetime_date, NOW()) AS remainder_days,
        bi.input_date,
        DATEDIFF(now(), input_date) + 1 stock_age,
        bi.production_date,
        bi.shelf_line,
        bi.main_requirement,
        bi.counter_sign_remark,
        ch.receipt_code subContractCode,
        ch.first_party subFirstParty,
        ch.supplier_id subSupplierId,
        dwt.type_code,
        dwt.type_name,
        dwb.bin_code,
        dwb.group_bin_no,
        dwss.section_code,
        dwss.section_name,
        dwb.section_id,
        dwb.over_weight
        FROM
        stock_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id

        INNER JOIN dic_unit du ON dm.unit_id = du.id

        INNER JOIN dic_factory df ON sb.fty_id = df.id

        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id

        INNER JOIN dic_wh dw ON sb.wh_id = dw.id

        INNER JOIN dic_wh_storage_type dwt ON sb.type_id = dwt.id

        

        INNER JOIN dic_wh_storage_bin dwb ON sb.bin_id = dwb.id

        left join dic_wh_storage_section dwss on dwb.section_id = dwss.id

        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN biz_receipt_contract_head ch on bi.contract_id = ch.id
        LEFT JOIN (SELECT stock_bin_id, SUM(qty) as qty FROM stock_occupy GROUP BY stock_bin_id ) occupy_temp ON occupy_temp.stock_bin_id = sb.id
        WHERE 1=1
        
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND sb.fty_id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND sb.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.whId != null" >
            AND sb.wh_id = #{po.whId}
        </if>
        <if test="po.typeId != null" >
            AND sb.type_id = #{po.typeId}
        </if>
        <if test="po.binId != null" >
            AND sb.bin_id = #{po.binId}
        </if>
        <if test="po.binCode != null and po.binCode != '' " >
            AND dwb.bin_code = #{po.binCode}
        </if>
        <if test="po.binCodeList !=null and po.binCodeList.size() > 0 ">
            AND dwb.bin_code in
            <foreach collection="po.binCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.batchCodeList !=null and po.batchCodeList.size() > 0 ">
            AND bi.batch_code in
            <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockList !=null and po.specStockList.size() > 0 ">
            AND bi.spec_stock in
            <foreach collection="po.specStockList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.isLeisure !=null and po.isLeisure != '' ">
            AND bi.is_leisure = #{po.isLeisure}
        </if>
        <if test="po.groupBinNo !=null and po.groupBinNo != '' ">
            AND dwb.group_bin_no = #{po.groupBinNo}
        </if>
        <if test="po.batchId !=null and po.batchId > 0">
            And bi.batch_id = #{po.batchId}
        </if>
        <if test="po.contractCode !=null and po.contractCode != ''">
            And ch.receipt_code = #{po.contractCode}
        </if>
        <if test="po.condition !=null and po.condition != ''">
            AND (sb.bin_code = #{po.condition} OR dm.mat_code = #{po.condition})
        </if>
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''">
            And bi.purchase_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.purchaseReceiptRid !=null and po.purchaseReceiptRid != ''">
            And bi.purchase_rid = #{po.purchaseReceiptRid}
        </if>
    </select>
    <!-- 查询 仓位库存详情 成套设备-->
    <select id="selectStockBinDetailUnitized" resultType="com.inossem.wms.common.model.bizdomain.report.vo.UnitizedStockBinVO">
        SELECT
        sb.mat_id,
        sb.fty_id,
        sb.location_id,
        sb.batch_id,
        sb.wh_id,
        sb.type_id,
        sb.bin_id,
        sb.cell_id,
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        dmg.mat_group_code,
        dmg.mat_group_name,
        sb.qty,
        IFNULL(occupy_temp.qty, 0) as occupy_qty,
        sb.qty_freeze,
        sb.qty_transfer,
        sb.qty_inspection,
        sb.qty_temp,
        sb.qty_haste,
        df.fty_code,
        df.fty_name,
        dlm.location_code main_location_code ,
        dlm.location_name main_location_name,
        dl.location_code,
        dl.location_name,
        dw.wh_code,
        dw.wh_name,
        bi.batch_code,
        bi.batch_erp,
        bi.spec_stock,
        bi.spec_stock_code,
        bi.spec_stock_name,
        bi.purchase_receipt_code,
        bi.purchase_receipt_rid,
        bi.demand_dept,
        bi.apply_user_name,
        bi.apply_user_dept_name,
        bi.apply_user_office_name,
        dm.shelf_life_min,
        dm.shelf_life_max,
        bi.maintenance_cycle,
        DATE_ADD(bi.maintenance_date,
        INTERVAL
        bi.maintenance_cycle
        YEAR) AS maintenance_in_date,
        bi.maintenance_date_pro,
        bi.lifetime_date,
        DATEDIFF(bi.lifetime_date, NOW()) AS remainder_days,
        bi.input_date,
        DATEDIFF(now(), input_date) + 1 stock_age,
        bi.production_date,
        bi.shelf_line,
        bi.main_requirement,
        bi.counter_sign_remark,
        ifnull(bi.price,0 ) price,
        (
        sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste
        ) * ifnull(bi.price,0 ) money,
        dwt.type_code,
        dwt.type_name,
        dwb.bin_code,
        dwb.group_bin_no,
        dwss.section_code,
        dwss.section_name,
        dwc.cell_code,
        dmf.package_type,
        dmf.deposit_type,
        dmf.stock_group,
        epri.contract_code,
        bi.extend29,
        bi.extend74,
        bi.extend73,
        bi.temp_store_dept_id,
        bi.temp_store_dept_office_id,
        bi.temp_store_expire_date,
        bi.temp_store_pre_receipt_code,
        bi.temp_store_user
        FROM
        stock_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id

        INNER JOIN dic_unit du ON dm.unit_id = du.id

        INNER JOIN dic_factory df ON sb.fty_id = df.id

        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id

        INNER JOIN dic_wh dw ON dl.wh_id = dw.id

        INNER JOIN dic_wh_storage_type dwt ON sb.type_id = dwt.id

        INNER JOIN dic_wh_storage_bin dwb ON sb.bin_id = dwb.id

        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id  AND epri.delete_tag = 0
        LEFT JOIN dic_wh_storage_section dwss ON dwb.section_id = dwss.id
        LEFT JOIN dic_wh_storage_cell dwc ON sb.cell_id = dwc.id

        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        left join dic_material_facotry_wbs dmfw on dmfw.mat_id = sb.mat_id
        and dmfw.fty_id = sb.fty_id and dmfw.spec_stock = bi.spec_stock and dmfw.spec_stock_code = bi.spec_stock_code
        LEFT JOIN (SELECT stock_bin_id, SUM(qty) as qty FROM stock_occupy GROUP BY stock_bin_id ) occupy_temp ON occupy_temp.stock_bin_id = sb.id
        LEFT JOIN biz_receipt_waybill waybill on waybill.mat_id=sb.mat_id and waybill.pre_id = 0
        LEFT JOIN biz_receipt_delivery_notice_item brdni ON brdni.id = waybill.delivery_notice_item_id
        LEFT JOIN dic_stock_location dlm ON brdni.location_id = dlm.id
        WHERE 1=1
        <if test="po.isUnitized == true" >
            and dm.is_ct_code = 1
        </if>
        <if test="po.isUnitized == false" >
            and dm.is_ct_code = 0
        </if>
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>

        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND sb.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.whId != null" >
            AND sb.wh_id = #{po.whId}
        </if>
        <if test="po.typeId != null" >
            AND sb.type_id = #{po.typeId}
        </if>
        <if test="po.binId != null" >
            AND sb.bin_id = #{po.binId}
        </if>
        <if test="po.binCode != null and po.binCode != '' " >
            AND dwb.bin_code = #{po.binCode}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.isLeisure !=null and po.isLeisure != '' ">
            AND bi.is_leisure = #{po.isLeisure}
        </if>
        <if test="po.groupBinNo !=null and po.groupBinNo != '' ">
            AND dwb.group_bin_no = #{po.groupBinNo}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
    </select>

    <select id="selectStockBinGroupByWh" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBinVO">
        SELECT

            dw.wh_code,
            dw.wh_name,

            sum(
                (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 )
            ) money
        FROM
        stock_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        
        INNER JOIN dic_wh_storage_type dwt ON sb.type_id = dwt.id
        
        INNER JOIN dic_wh_storage_bin dwb ON sb.bin_id = dwb.id
        
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id AND epri.delete_tag = 0
        LEFT JOIN dic_wh_storage_section dwss ON dwb.section_id = dwss.id
        LEFT JOIN dic_wh_storage_cell dwc ON sb.cell_id = dwc.id
        
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND sb.wh_id = #{po.whId}
        </if>
        <if test="po.typeId != null" >
            AND sb.type_id = #{po.typeId}
        </if>
        <if test="po.binId != null" >
            AND sb.bin_id = #{po.binId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
            group by sb.wh_id
    </select>

    <select id="selectStockBinGroupByType" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBinVO">
        SELECT
            dw.wh_code,
            dw.wh_name,
            dwt.type_code,
            dwt.type_name,
            sum(
            (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 )
            ) money
        FROM
        stock_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        
        INNER JOIN dic_wh_storage_type dwt ON sb.type_id = dwt.id
        
        INNER JOIN dic_wh_storage_bin dwb ON sb.bin_id = dwb.id
        
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id AND epri.delete_tag = 0
        LEFT JOIN dic_wh_storage_section dwss ON dwb.section_id = dwss.id
        LEFT JOIN dic_wh_storage_cell dwc ON sb.cell_id = dwc.id
        
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND sb.wh_id = #{po.whId}
        </if>
        <if test="po.typeId != null" >
            AND sb.type_id = #{po.typeId}
        </if>
        <if test="po.binId != null" >
            AND sb.bin_id = #{po.binId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
            group by sb.type_id
    </select>


    <select id="selectStockBinGroupBySection" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBinVO">
        SELECT

            dw.wh_code,
            dw.wh_name,
            dwt.type_code,
            dwt.type_name,
            dwss.section_code,
            dwss.section_name,
            sum(
            (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 )
            ) money
        FROM
        stock_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        
        INNER JOIN dic_wh_storage_type dwt ON sb.type_id = dwt.id
        
        INNER JOIN dic_wh_storage_bin dwb ON sb.bin_id = dwb.id
        
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id AND epri.delete_tag = 0
        LEFT JOIN dic_wh_storage_section dwss ON dwb.section_id = dwss.id
        LEFT JOIN dic_wh_storage_cell dwc ON sb.cell_id = dwc.id
        
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND sb.wh_id = #{po.whId}
        </if>
        <if test="po.typeId != null" >
            AND sb.type_id = #{po.typeId}
        </if>
        <if test="po.binId != null" >
            AND sb.bin_id = #{po.binId}
        </if>
        <if test="po.stockStatus == 10">
            AND sb.qty > 0
        </if>
        <if test="po.stockStatus == 20">
            AND sb.qty_transfer > 0
        </if>
        <if test="po.stockStatus == 30">
            AND sb.qty_inspection > 0
        </if>
        <if test="po.stockStatus == 40">
            AND sb.qty_freeze > 0
        </if>
        <if test="po.stockStatus == 50">
            AND sb.qty_haste > 0
        </if>
        <if test="po.stockStatus == 60">
            AND sb.qty_temp > 0
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.contractCode !=null and po.contractCode != '' ">
            AND epri.contract_code = #{po.contractCode}
        </if>
        group by dwb.section_id
    </select>

    <select id="selectStockAnalyse" parameterType="com.inossem.wms.common.model.bizdomain.report.po.StockAgeAnalysePO" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockAgeAnalyseVO" >
        select
            dw.wh_code,
            dw.wh_name,
            sum(
                (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 ) / 10000
            ) stock_money,

            <foreach collection="ageTypeList" item="item" separator=" " open="CASE " index="index" close="END as stock_age_type" >
                    <if test="item.endDate != null and item.startDate != null " >
                        when bi.input_date > #{item.startDate} AND bi.input_date <![CDATA[ <= ]]> #{item.endDate} THEN #{item.stockAgeType}
                    </if>
                    <if test="item.startDate == null and item.endDate != null " >
                        when bi.input_date <![CDATA[ <= ]]>  #{item.endDate}  THEN #{item.stockAgeType}
                    </if>
            </foreach>


        FROM
            stock_batch sb
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        INNER JOIN dic_stock_location dl on sb.location_id = dl.id
        INNER JOIN dic_wh dw on dl.wh_id = dw.id
        LEFT JOIN dic_material_factory dmf ON sb.mat_id = dmf.mat_id
        AND sb.fty_id = dmf.fty_id
        WHERE
            input_date IS NOT NULL
        <if test="whId !=null ">
            AND dl.wh_id = #{whId}
        </if>
        GROUP BY stock_age_type
        ORDER BY stock_age_type
    </select>

    <select id="selectStockAnalyseGroupByWh" parameterType="com.inossem.wms.common.model.bizdomain.report.po.StockAgeAnalysePO" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockAgeAnalyseVO" >
        select
        dw.wh_code,
        dw.wh_name,
        sum(
        (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 ) / 10000
        ) stock_money,

        <foreach collection="ageTypeList" item="item" separator=" " open="CASE " index="index" close="END as stock_age_type" >
            <if test="item.endDate != null and item.startDate != null " >
                when bi.input_date > #{item.startDate} AND bi.input_date <![CDATA[ <= ]]> #{item.endDate} THEN #{item.stockAgeType}
            </if>
            <if test="item.startDate == null and item.endDate != null " >
                when bi.input_date <![CDATA[ <= ]]>  #{item.endDate}  THEN #{item.stockAgeType}
            </if>
        </foreach>


        FROM
        stock_batch sb
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        INNER JOIN dic_stock_location dl on sb.location_id = dl.id
        INNER JOIN dic_wh dw on dl.wh_id = dw.id
        LEFT JOIN dic_material_factory dmf ON sb.mat_id = dmf.mat_id
        AND sb.fty_id = dmf.fty_id
        WHERE
        input_date IS NOT NULL
        <if test="whId !=null ">
            AND dl.wh_id = #{whId}
        </if>
        GROUP BY stock_age_type,dl.wh_id
        ORDER BY stock_age_type
    </select>

    <select id="selectStockAnalyseGroupByLocation" parameterType="com.inossem.wms.common.model.bizdomain.report.po.StockAgeAnalysePO" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockAgeAnalyseVO" >
        select
            dw.wh_code,
            dw.wh_name,
            df.fty_code,
            df.fty_name,
            dl.location_code,
            dl.location_name,
            sb.location_id,

            sum(
            (sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste) * ifnull(dmf.move_avg_price,0 )/ 10000
            ) stock_money,

        <foreach collection="ageTypeList" item="item" separator=" " open="CASE " index="index" close="END as stock_age_type" >
            <if test="item.endDate != null and item.startDate != null " >
                when bi.input_date > #{item.startDate} AND bi.input_date <![CDATA[ <= ]]> #{item.endDate} THEN #{item.stockAgeType}
            </if>
            <if test="item.startDate == null and item.endDate != null " >
                when bi.input_date <![CDATA[ <= ]]>  #{item.endDate}  THEN #{item.stockAgeType}
            </if>
        </foreach>


        FROM
        stock_batch sb
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        INNER JOIN dic_stock_location dl on sb.location_id = dl.id
        INNER JOIN dic_wh dw on dl.wh_id = dw.id
        LEFT JOIN dic_material_factory dmf ON sb.mat_id = dmf.mat_id
        AND sb.fty_id = dmf.fty_id
        WHERE
            input_date IS NOT NULL

        GROUP BY stock_age_type,dl.id
        ORDER BY stock_age_type
    </select>



    <select id="selectStockInsDocBatch" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockInsDocBatchVO" >
        SELECT
            dm.mat_code,
            dm.mat_name,
            dmg.mat_group_code,
            dmg.mat_group_name,
            du.unit_code,
            du.unit_name,
            sb.move_qty,
            df.fty_code,
            df.fty_name,
            dl.location_code,
            dl.location_name,
            dw.wh_code,
            dw.wh_name,
            bi.batch_code,
            bi.batch_erp,
            bi.spec_stock,
            bi.spec_stock_code,
            bi.spec_stock_name,
            sb.pre_receipt_code,
            sb.pre_receipt_type,
            sb.refer_receipt_head_id,
            sb.refer_receipt_type,
            sb.refer_receipt_type receipt_type,
            sb.create_time,
            sb.posting_date,
            sb.mat_doc_code,
            sb.mat_doc_rid,
            sb.mat_doc_year,
            su.user_name create_user_name
        FROM
            stock_ins_doc_batch sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id
        
        INNER JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
       
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        
        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        
        LEFT JOIN dic_move_type move on move.id = sb.move_type_id 
        LEFT JOIN sys_user su on sb.create_user_id = su.id
        WHERE
            1 = 1
        <if test="po.ftyId != null" >
            AND sb.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sb.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND dw.id = #{po.whId}
        </if>

        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.createTimeStart !=null and po.createTimeEnd != null ">
            AND DATE_FORMAT(bi.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matGroupName !=null and po.matGroupName != '' ">
            AND dmg.mat_group_name like concat( '%',#{po.matGroupName}, '%')
        </if>
        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.preReceiptCode !=null and po.preReceiptCode != '' ">
            AND sb.pre_receipt_code = #{po.preReceiptCode}
        </if>
        <if test="po.preReceiptType !=null  ">
            AND sb.pre_receipt_type = #{po.preReceiptType}
        </if>
        <if test="po.referReceiptType !=null  ">
            AND sb.refer_receipt_type = #{po.referReceiptType}
        </if>
        <if test="po.referReceiptHeadId !=null  ">
            AND sb.refer_receipt_head_id = #{po.referReceiptHeadId}
        </if>
        <if test="po.createUserId != null ">
            AND sb.create_user_id = #{po.createUserId}
        </if>
        <if test="po.createUserName != null and po.createUserName != '' ">
            AND su.user_name like concat( '%',#{po.createUserName}, '%')
        </if>
    </select>

    <select id="selectTask" resultType="com.inossem.wms.common.model.bizdomain.report.vo.TaskVO">
        SELECT
            dw.wh_code,
            dw.wh_name,
            th.receipt_code,
            th.receipt_type task_receipt_type,
            ti.rid,
            ri.refer_receipt_head_id,
            ri.refer_receipt_type,
            ri.pre_receipt_head_id,
            ri.pre_receipt_type,
            ri.pre_receipt_type receipt_type,
            ifnull(su.user_name, '') create_user_name,
            dm.mat_code,
            dm.mat_name,
            du.unit_code,
            du.unit_name,
            df.fty_code,
            df.fty_name,
            dl.location_code,
            dl.location_name,
            bi.batch_code,
            ti.batch_id,
            bi.batch_erp,
            bi.spec_stock,
            bi.spec_stock_code,
            dwt.type_code target_type_code,
            dwb.bin_code target_bin_code,
            ifNUll(dwc.cell_code, '') target_cell_code,
            dwts.type_code source_type_code,
            dwbs.bin_code source_bin_code,
            ifNUll(dwcs.cell_code, '') source_cell_code,
            ti.qty,
            th.create_time
        FROM
            biz_receipt_task_head th
        INNER JOIN biz_receipt_task_item ti ON th.id = ti.head_id
        INNER JOIN biz_receipt_task_req_item ri ON ri.id = ti.task_req_item_id
        INNER JOIN dic_material dm ON ri.mat_id = dm.id
        INNER JOIN dic_unit du ON ri.unit_id = du.id
        INNER JOIN dic_factory df ON df.id = ri.fty_id
        INNER JOIN dic_stock_location dl ON ri.location_id = dl.id
        INNER JOIN dic_wh dw ON ri.wh_id = dw.id
        INNER JOIN dic_wh_storage_type dwt ON ti.target_type_id = dwt.id
        INNER JOIN dic_wh_storage_bin dwb ON ti.target_bin_id = dwb.id
        INNER JOIN dic_wh_storage_type dwts ON ti.source_type_id = dwts.id
        INNER JOIN dic_wh_storage_bin dwbs ON ti.source_bin_id = dwbs.id
        INNER JOIN biz_batch_info bi ON ti.batch_id = bi.id
        LEFT JOIN dic_wh_storage_cell dwc ON ti.target_cell_id = dwc.id
        LEFT JOIN dic_wh_storage_cell dwcs ON ti.source_cell_id = dwcs.id
        LEFT JOIN sys_user su ON ti.create_user_id = su.id
        WHERE
        1 = 1
        <if test="po.ftyId != null" >
            AND ri.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND ri.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND ri.wh_id = #{po.whId}
        </if>

        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.createTimeStart !=null and po.createTimeEnd != null ">
            AND DATE_FORMAT(ti.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.specStock !=null ">
            AND bi.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != '' ">
            AND bi.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>

        <if test="po.batchErp !=null and po.batchErp != '' ">
            AND bi.batch_erp = #{po.batchErp}
        </if>
        <if test="po.preReceiptHeadId !=null  ">
            AND ri.pre_receipt_head_id = #{po.preReceiptHeadId}
        </if>

        <if test="po.preReceiptType !=null  ">
            AND ri.pre_receipt_type = #{po.preReceiptType}
        </if>
        <if test="po.referReceiptHeadId !=null  ">
            AND ri.refer_receipt_head_id = #{po.referReceiptHeadId}
        </if>
        <if test="po.createUserId != null ">
            AND ti.create_user_id = #{po.createUserId}
        </if>
        <if test="po.createUserName != null and po.createUserName != '' ">
            AND su.user_name like concat( '%',#{po.createUserName}, '%')
        </if>

    </select>

    <update id="insertErpStockToDiff" parameterType="com.inossem.wms.common.model.bizdomain.report.po.StockDiffSearchPO" >
        insert into erp_stock_diff (

            fty_id,
            location_id,
            mat_id,
            batch_erp,
            spec_stock,
            spec_stock_code,
            erp_qty,
            erp_qty_transfer,
            erp_qty_inspection,
            erp_qty_freeze,
            create_time
        ) SELECT

            fty_id,
            location_id,
            mat_id,
            batch_erp,
            spec_stock,
            spec_stock_code,
            qty,
            qty_transfer,
            qty_inspection,
            qty_freeze,
            create_time
        FROM
            erp_stock_batch
        WHERE 1=1
            <if test="ftyId!=null">
                AND fty_id = #{ftyId}
            </if>
            <if test="locationId!=null">
                AND location_id = #{locationId}
            </if>

        ON DUPLICATE KEY UPDATE
        erp_qty = VALUES(erp_qty),
        erp_qty_transfer = VALUES(erp_qty_transfer),
        erp_qty_inspection = VALUES(erp_qty_inspection),
        erp_qty_freeze = VALUES(erp_qty_freeze);
    </update>
    <update id="insertInsStockToDiff" parameterType="java.util.List" >
        insert into erp_stock_diff (

            fty_id,
            location_id,
            mat_id,
            batch_erp,
            spec_stock,
            spec_stock_code,
            batch_qty,
            batch_qty_transfer,
            batch_qty_inspection,
            batch_qty_freeze,
            batch_diff_qty,
            batch_diff_qty_transfer,
            batch_diff_qty_inspection,
            batch_diff_qty_freeze,
            create_time
        ) values
            <foreach collection="list" item="item" separator="," index="index" >
                (#{item.ftyId},
                #{item.locationId},
                #{item.matId},
                #{item.batchErp},
                #{item.specStock},
                #{item.specStockCode},
                #{item.batchQty},
                #{item.batchQtyTransfer},
                #{item.batchQtyInspection},
                #{item.batchQtyFreeze},
                -#{item.batchQty},
                -#{item.batchQtyTransfer},
                -#{item.batchQtyInspection},
                -#{item.batchQtyFreeze},
                #{item.createTime})
            </foreach>


        ON DUPLICATE KEY UPDATE
        batch_qty = VALUES(batch_qty),
        batch_qty_transfer = VALUES(batch_qty_transfer),
        batch_qty_inspection = VALUES(batch_qty_inspection),
        batch_qty_freeze = VALUES(batch_qty_freeze),
        batch_diff_qty = ifnull(erp_qty,0) - VALUES(batch_qty),
        batch_diff_qty_transfer = ifnull(erp_qty_transfer,0) - VALUES(batch_qty_transfer),
        batch_diff_qty_inspection = ifnull(erp_qty_inspection,0) - VALUES(batch_qty_inspection),
        batch_diff_qty_freeze = ifnull(erp_qty_freeze,0) - VALUES(batch_qty_freeze);
    </update>

    <delete id="deleteInsStockToDiff">
        delete from erp_stock_diff
    </delete>

    <select id="getInsStockByLocationId"  resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockDiffVO">
        SELECT

            sb.fty_id,
            sb.location_id,
            sb.mat_id,
            bi.batch_erp,
            bi.spec_stock,
            bi.spec_stock_code,
            sum(sb.qty) batch_qty,
            sum(sb.qty_transfer) batch_qty_transfer,
            sum(sb.qty_inspection) batch_qty_inspection,
            sum(sb.qty_freeze) batch_qty_freeze,
            sb.create_time
        FROM
        stock_batch sb
        inner join biz_batch_info bi on sb.batch_id = bi.id
        WHERE sb.location_id = #{locationId}
        GROUP BY
            sb.fty_id,
            sb.location_id,
            sb.mat_id,
            bi.batch_erp,
            bi.spec_stock,
            bi.spec_stock_code
    </select>

    <select id="selectStockDiff" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockDiffVO">
        select
            dm.mat_code,
            dm.mat_name,
            du.unit_code,
            du.unit_name,

            df.fty_code,
            df.fty_name,
            dl.location_code,
            dl.location_name,
            dw.wh_code,
            dw.wh_name,
            sd.fty_id,
            sd.location_id,
            sd.mat_id,
            sd.batch_erp,
            sd.spec_stock,
            sd.spec_stock_code,
            sd.erp_qty,
            sd.erp_qty_transfer,
            sd.erp_qty_inspection,
            sd.erp_qty_freeze,
            sd.batch_qty,
            sd.batch_qty_transfer,
            sd.batch_qty_inspection,
            sd.batch_qty_freeze,
            sd.batch_diff_qty,
            sd.batch_diff_qty_transfer,
            sd.batch_diff_qty_inspection,
            sd.batch_diff_qty_freeze,
            sd.create_time
        from erp_stock_diff sd

        INNER JOIN dic_material dm ON sd.mat_id = dm.id

        INNER JOIN dic_unit du ON dm.unit_id = du.id

        INNER JOIN dic_factory df ON sd.fty_id = df.id

        INNER JOIN dic_stock_location dl ON sd.location_id = dl.id

        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
            where 1=1
        <if test="po.ftyId!=null">
            AND sd.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId!=null">
            AND sd.location_id = #{po.locationId}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size()>0">
            AND dm.mat_code IN
            <foreach collection="po.matCodeList" item="item" close=")" open="(" index="index" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectErpStockMoney" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockMoneyAnalyseVO">
        select
            df.fty_code,
            df.fty_name,
            dsl.location_code,
            dsl.location_name,
            sum(esb.money)/10000 stock_money
        from
            erp_stock_batch esb
        inner join dic_factory df on esb.fty_id = df.id
        inner join dic_stock_location dsl on esb.location_id = dsl.id
        group by location_id
    </select>

    <select id="selectInAndOut" parameterType="com.inossem.wms.common.model.bizdomain.report.po.InAndOutAnalysePO" resultType="com.inossem.wms.common.model.bizdomain.report.vo.InAndOutAnalyseVO">
        SELECT
            t.create_date,
            sum(
                    CASE
                        WHEN t.debit_credit = 'H' THEN
                            1
                        ELSE
                            0
                        END
                ) out_num,
            sum(
                    CASE
                        WHEN t.debit_credit = 'S' THEN
                            1
                        ELSE
                            0
                        END
                ) in_num
        FROM
            (
                SELECT
                    DATE_FORMAT(create_time, '%Y-%m-%d') create_date,

                    debit_credit
                FROM
                    stock_ins_doc_batch
                where create_time between #{startDate} And #{endDate}
                GROUP BY
                    create_date,
                    ins_doc_code,
                    debit_credit
            ) t
        GROUP BY
            t.create_date;
    </select>

    <select id="selectStockTurnover" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockTurnoverVO" parameterType="com.inossem.wms.common.model.bizdomain.report.po.StockTurnoverPO">
        SELECT
            est.year,
            est.month,
            sum(est.beginning_balance+est.ending_balance) / 2 /10000 avg_stock_money,
            case when sum(est.beginning_balance+est.ending_balance)=0 then 0
                 else sum(est.month_out_money) / (sum(est.beginning_balance+est.ending_balance) / 2) end
             stock_turnover

        from
             erp_stock_turnover est
        inner join dic_material dm on est.mat_id = dm.id
        where 1=1
        <if test="ftyId != null">
            AND est.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND est.location_id = #{locationId}
        </if>
        <if test="matGroupId != null">
            AND dm.mat_group_id = #{matGroupId}
        </if>
        <if test="startMonth !=null and endMonth!=null">
            AND est.month between #{startMonth} and #{endMonth}
        </if>
        group by est.month

    </select>
    <select id="selectStockByLocationId" resultType="com.inossem.wms.common.model.erp.entity.ErpStockBatch">
        select
            esb.mat_id,
            esb.fty_id,
            esb.location_id,
            sum(esb.money) money
        from erp_stock_batch esb
        where location_id = #{locationId}
        group by esb.mat_id,esb.fty_id,esb.location_id

    </select>
    <insert id="insertIntoStockTurnOver" parameterType="java.util.List">
        insert into erp_stock_turnover (
            id,
            year,
            month,
            month_in_money,
            month_out_money,
            beginning_balance,
            ending_balance,
            mat_id,
            location_id,
            fty_id
        ) values
        <foreach collection="list" item="item" separator="," >
            (
             #{item.id},
             #{item.year},
             #{item.month},
             #{item.monthInMoney},
             #{item.monthOutMoney},
             #{item.beginningBalance},
             #{item.endingBalance},
             #{item.matId},
             #{item.locationId},
             #{item.ftyId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        month_in_money = month_in_money + VALUES(month_in_money),
        month_out_money = month_out_money + VALUES(month_out_money),
        beginning_balance = beginning_balance + VALUES(beginning_balance),
        ending_balance = ending_balance + VALUES(ending_balance);

    </insert>

    <select id="selectInAndOutMoney" resultType="com.inossem.wms.common.model.erp.entity.ErpMatDoc">
        select emd.fty_id,
               emd.location_id,
               emd.mat_id,

               emd.debit_credit,
               emd.mat_doc_month,
               sum(emd.mat_doc_money) mat_doc_money
        from erp_mat_doc emd
        where emd.location_id = #{locationId}
              AND emd.mat_doc_month = #{month}
        group by emd.fty_id,
                 emd.location_id,
                 emd.mat_id,
                 emd.mat_doc_month,
                 emd.debit_credit

    </select>

    <select id="selectStockBinWeightDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBinWeightVo">
        SELECT
        dm.mat_code,
        dm.mat_name,
        dm.net_weight,
        dm.unit_weight,
        sbw.qty,
        df.fty_code,
        df.fty_name,
        dl.location_code,
        dl.location_name,
        dw.wh_code,
        dw.wh_name,
        dwt.type_code,
        dwt.type_name,
        dwb.bin_code,
        dwss.section_code,
        dwss.section_name,
        dwc.cell_code
        FROM
        stock_bin_weight sbw
        INNER JOIN dic_material dm ON sbw.mat_id = dm.id

        INNER JOIN dic_factory df ON sbw.fty_id = df.id

        INNER JOIN biz_batch_info bi ON sbw.batch_id = bi.id

        INNER JOIN dic_stock_location dl ON sbw.location_id = dl.id

        INNER JOIN dic_wh dw ON dl.wh_id = dw.id

        INNER JOIN dic_wh_storage_type dwt ON sbw.type_id = dwt.id

        INNER JOIN dic_wh_storage_bin dwb ON sbw.bin_id = dwb.id

        LEFT JOIN dic_wh_storage_section dwss ON dwb.section_id = dwss.id
        LEFT JOIN dic_wh_storage_cell dwc ON sbw.cell_id = dwc.id

        WHERE 1=1
        <if test="po.ftyId != null" >
            AND sbw.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND sbw.location_id = #{po.locationId}
        </if>
        <if test="po.whId != null" >
            AND sbw.wh_id = #{po.whId}
        </if>
        <if test="po.typeId != null" >
            AND sbw.type_id = #{po.typeId}
        </if>
        <if test="po.binId != null" >
            AND sbw.bin_id = #{po.binId}
        </if>
        <if test="po.cellCode != null and po.cellCode != ''" >
            AND dwc.cell_code = #{po.cellCode}
        </if>
        <if test="po.batchCode !=null and po.batchCode != '' ">
            AND bi.batch_code = #{po.batchCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != '' ">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
    </select>

    <!--电子秤报表查询-->
    <select id="selectElectronicScaleRecord"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.ElectronicScaleVO">
        SELECT
            dwsc.cell_code AS electronicScaleId,
            CASE dwsc.electric_status WHEN "0" THEN "未知" ELSE dwsc.electric_status END electric_status,
            dwsc.electric_quantity,
            dwsc.wake_up_threshold,
            dwsc.create_time,
            lesr.current_weight,
            MAX( lesr.create_time )
        FROM
            dic_wh_storage_cell dwsc
                INNER JOIN log_electronic_scale_record lesr ON dwsc.cell_code = lesr.electronic_scale_id
        <where>
            dwsc.cell_type = 2
            <if test="po.electronicScaleId !=null and po.electronicScaleId != ''" >
                AND dwsc.cell_code = #{po.electronicScaleId}
            </if>
            <if test="po.startTime !=null and po.endTime!=null">
                AND dwsc.create_time between #{po.startTime} and #{po.endTime}
            </if>
        </where>
        GROUP BY
            dwsc.cell_code
    </select>


    <!--入库台账报表查询-->
    <select id="selectInputLedgerRecord"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.InputLedgerVo">
        SELECT
            dh.receipt_code delivery_code, # 送货单号
            di.customs_class, # 报关分类
            dh.send_type, # 送货单类型
            ch.receipt_code contract_code, #合同号
            ch.contract_name, #合同名称 
            ch.first_party, # 甲方
            ch.supplier_id, # 供应商id
            s.supplier_code, # 供应商code
            s.supplier_name, # 供应商name 
            dh.batch_code send_batch, # 发运批次号
            ch.delivery_address, # 供方交货/服务地点
            u.user_code create_user_code, # 创建人code
            u.user_name create_user_name, # 创建人 name
            dh.create_time, # 创建时间
            di.rid, # 序号
            di.demand_plan_code, # 需求计划编号
            di.demand_person, # 需求人
            di.case_code, # 箱件编号
            m.mat_code, # 物料编码
            m.mat_name, # 物料描述
            m.mat_name_en, # 物料描述(英文)
            m.ext_manufacturer_part_number, # 制造商零件编号
            m.ext_main_material, # 基本物料
            m.ext_industry_standard_desc, # 行业标准描述

            du.unit_code, # 计量单位编码
            du.unit_name, # 计量单位名称
            di.qty send_qty, # 本次发运数量
            
        CASE
                
                WHEN dh.send_type = 1 
                OR dh.send_type = 4 THEN
                    di.po_no_tax_price ELSE di.no_tax_price 
                END po_no_tax_price, # PO单价(不含税)
        CASE
                
                WHEN dh.send_type = 1 
                OR dh.send_type = 4 THEN
                    di.po_no_tax_amount ELSE di.no_tax_amount 
                END po_no_tax_amount, # PO总价(不含税)
            ch.currency, # 币种
            di.refer_receipt_item_id, # 参考单据itemid(合同)
            di.refer_receipt_head_id, # 参考单据headid(合同)
            di.mat_type_str, # 物资类型
            di.fee_ratio, # 费用占比(%)
            di.fty_id,  # 工厂
            di.location_id, # 库存地点
            lh.receipt_code logistics_code,  # 物流清关单号
            lh.submit_time logistics_date, # 物流清关完成日期
            di.purchase_code, # 采购订单号
            rh.receipt_code register_code, #到货登记单号
            rh.receive_date, #接货时间
        IF(rh.receipt_status = 10, null, rh.modify_user_id)  register_user_id, # 接货人
            ih.receipt_code inspect_notice_code, # 质检分配单号
            ih.inspect_user_name, # 参检人
            ih.inspect_time, # 参检时间
            ih.modify_time inspect_notice_complete_time, #质检分配完成日期
            sh.receipt_code inspect_sign_code, #质检会签单号
            si.qty qualified_qty, #合格数量
            si.unqualified_qty, #不合格数量
            si.unarrival_qty, # 未到货数量
            if(inputi.write_off_mat_doc_code is null or inputi.write_off_mat_doc_code = '', inputi.qty, 0) input_qty, # 入库数量
        sh.modify_time inspect_sign_complete_time, #质检会签完成日期
            inputh.receipt_code input_code,  #验收入库单号
            inputi.posting_date, #验收入库过账日期
            inputi.wh_id, #仓库
            inputi.mat_doc_code,   #物料凭证号
            inputi.write_off_mat_doc_code, #物料凭证号(冲销)
            inputi.car_code,
            inputi.invoice_no,
            inputi.invoice_date,
            inputh.modify_user_id input_user_id, #操作人
            
            bi.batch_code,  #批次号
            (select sum(qty) from stock_bin b, dic_wh w where b.mat_id = bi.mat_id and b.batch_id = bi.id and b.wh_id = w.id and w.wh_code = 'Q01') q01_qty,
        brtri.modify_time input_complete_time #验收入库完成日期
        FROM
            biz_receipt_delivery_notice_head dh
            INNER JOIN biz_receipt_delivery_notice_item di ON dh.id = di.head_id AND dh.is_delete = 0 AND di.is_delete = 0
            INNER JOIN biz_receipt_contract_head ch ON dh.contract_id = ch.id AND ch.is_delete = 0
            INNER JOIN dic_supplier s ON ch.supplier_id = s.id AND s.is_delete = 0
            INNER JOIN sys_user u ON dh.create_user_id = u.id
            LEFT JOIN dic_material m ON di.mat_id = m.id
            LEFT JOIN dic_unit du ON du.id = di.unit_id
            LEFT JOIN biz_receipt_logistics_item li ON di.id = li.pre_receipt_item_id 
            AND li.is_delete = 0
            LEFT JOIN biz_receipt_logistics_head lh ON lh.id = li.head_id 
            AND lh.is_delete = 0
            LEFT JOIN biz_receipt_register_item ri ON ri.delivery_item_id = di.id 
            AND ri.is_delete = 0
            LEFT JOIN biz_receipt_register_head rh ON rh.id = ri.head_id 
            AND rh.is_delete = 0
            LEFT JOIN biz_receipt_inspect_item ii ON ri.id = ii.pre_receipt_item_id
            LEFT JOIN biz_receipt_inspect_head ih ON ii.head_id = ih.id
            LEFT JOIN biz_receipt_inspect_item si ON ii.id = si.pre_receipt_item_id
            LEFT JOIN biz_receipt_inspect_head sh ON si.head_id = sh.id
            #LEFT JOIN biz_receipt_input_item inputi ON si.purchase_code = inputi.purchase_code
            #AND si.purchase_rid = inputi.purchase_rid
            #AND inputi.purchase_code != ''
            # 2025-03-29  bijinfeng 只考虑质检会签生成入库单场景，其余不考虑
        LEFT JOIN biz_receipt_input_item inputi ON si.id = inputi.pre_receipt_item_id
        LEFT JOIN biz_receipt_input_head inputh ON inputi.head_id = inputh.id
        LEFT JOIN biz_batch_info bi ON bi.id = inputi.batch_id
        LEFT JOIN biz_receipt_task_req_item brtri on brtri.pre_receipt_item_id = inputi.id

        where 1 =1 
        <if test ="po.deliveryCode !=null and po.deliveryCode!=''">
            and dh.receipt_code = #{po.deliveryCode}
        </if>
        <if test ="po.contractCode !=null and po.contractCode!=''">
            and ch.receipt_code = #{po.contractCode}
        </if>
        <if test ="po.sendBatch !=null and po.sendBatch!=''">
            and dh.batch_code = #{po.sendBatch}
        </if>
        <if test ="po.matCode !=null and po.matCode!=''">
            and m.mat_code = #{po.matCode}
        </if>
        <if test ="po.logisticsCode !=null and po.logisticsCode!=''">
            and lh.receipt_code = #{po.logisticsCode}
        </if>
        <if test ="po.registerCode !=null and po.registerCode!=''">
            and rh.receipt_code = #{po.registerCode}
        </if>
        <if test ="po.inspectSignCode !=null and po.inspectSignCode!=''">
            and sh.receipt_code = #{po.inspectSignCode}
        </if>
        <if test ="po.inputCode !=null and po.inputCode!=''">
            and inputh.receipt_code = #{po.inputCode}
        </if>
        <if test="po.postingDateStart !=null and po.postingDateEnd != null ">
            AND DATE_FORMAT(inputi.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.postingDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.postingDateEnd},'%Y-%m-%d')
        </if>
        ORDER BY
            di.id,
            ri.id,
            ii.id,
            si.id,
            inputi.id
    </select>


    <!--成套设备入库台账报表查询-->
    <select id="selectInputLedgerRecordUnitized"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.UnitizedInputLedgerVo">
        WITH base  AS (
            select  df.fty_code,
            df.fty_name,
            df.id fty_id,
            dm.mat_code main_mat_code,
            dm.mat_name main_mat_name,
            du.unit_code,
            du.unit_name,
            dl.id location_id,
            dl.location_code,
            dl.location_name,
            dlm.id main_location_id,
            dlm.location_code main_location_code ,
            dlm.location_name main_location_name,
            cgdI.spec_stock_code,
            cgdI.spec_stock_name,
            cgdH.receipt_code AS purchase_receipt_code,
            cgdI.rid AS purchase_receipt_rid ,
            cgdI.contract_code,
            bill.price,
            bill.waybill_remark,
            bill.mat_name,
            bill.arrival_qty,
            bill.is_main_parts,
            bill.product_date,
            bill.extend64,
            bill.package_form,
            bill.extend70,
            bill.extend71,
            bill.extend72,
            bill.extend73,
            bill.extend74,
            bill.case_weight,
            bill.functional_location_code,
            bill.extend2,
            bill.extend20,
            bill.extend24,
            bill.extend25,
            bill.extend26,
            bill.extend27,
            bill.extend28,
            bill.extend29,
            bill.extend31,
            bill.extend34,
            bill.extend35,
            bill.extend36,
            bill.extend37,
            bill.extend38,
            bill.extend40,
            bill.extend41,
            bill.extend42,
            bill.extend43,
            bill.extend44,
            bill.extend46,
            bill.extend47,
            bill.extend48,
            bill.extend49,
            bill.extend50,
            bill.extend62,
            bill.extend63,
            bill.case_code,
            bill.extend65,
            bill.extend66,
            bill.extend67,
            bill.extend68,
            bill.extend69,
            bill.extend60,
            bill.extend61,
            dhtzh.receipt_code notice_code,
            dhdjh.receipt_code register_code,
            bill.unitized_visual_check ,
            fpzjh.receive_date,
            dhdji.mat_doc_code register_mat_doc_code,
            dhdji.mat_doc_rid register_mat_doc_rid,
            dhdji.posting_date register_posting_date,
            dhdji.doc_date register_mat_doc_date,
            fpzjh.inspect_time,
            cdm.mat_code,
            zjhqh.receipt_code sign_inspect_code,
            bill.qualified_qty  qty0,
            bill.unqualified_qty qty1,
            bill.unarrival_qty qty2,
            zjhqh.submit_time  sign_inspect_complete_date,
            bill.id bill_id
            from biz_receipt_delivery_notice_head dhtzh
            INNER JOIN  biz_receipt_delivery_notice_item dhtzi  on dhtzh.id=dhtzi.head_id and  dhtzi.is_delete=0
            INNER JOIN biz_receipt_waybill bill  on dhtzh.id=bill.delivery_notice_head_id and  dhtzi.id=bill.delivery_notice_item_id
            INNER JOIN erp_purchase_receipt_head cgdH ON cgdH.id = dhtzi.refer_receipt_head_id
            INNER JOIN erp_purchase_receipt_item cgdI ON cgdI.id = dhtzi.refer_receipt_item_id  AND cgdI.delete_tag = 0
            INNER JOIN dic_material dm ON dhtzi.mat_id = dm.id
            INNER JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
            INNER JOIN dic_factory df ON dhtzi.fty_id = df.id
            INNER JOIN dic_unit du ON bill.unit_id = du.id
            INNER JOIN dic_stock_location dl ON bill.location_id = dl.id
            INNER JOIN dic_wh dw ON dl.wh_id = dw.id
            LEFT JOIN dic_material cdm ON bill.mat_id = cdm.id
            LEFT JOIN biz_receipt_register_head dhdjh on  bill.arrival_register_head_id=dhdjh.id and dhdjh.is_delete=0 and dhdjh.receipt_status=90
            LEFT JOIN biz_receipt_register_item dhdji on  bill.arrival_register_item_id=dhdji.id and dhdji.is_delete=0
            LEFT JOIN biz_receipt_inspect_head fpzjh on  bill.distribute_inspect_head_id=fpzjh.id and fpzjh.is_delete=0 and fpzjh.receipt_status=90
            LEFT JOIN biz_receipt_inspect_item fpzji on  bill.distribute_inspect_item_id=fpzji.id and fpzji.is_delete=0
            LEFT JOIN biz_receipt_inspect_head zjhqh on  bill.sign_inspect_head_id=zjhqh.id and zjhqh.is_delete=0
            and zjhqh.receipt_status=90
            LEFT JOIN biz_receipt_inspect_item zjhqi on  bill.sign_inspect_item_id=zjhqi.id and zjhqi.is_delete=0
            LEFT JOIN dic_stock_location dlm ON dhtzi.location_id = dlm.id
            where dhtzh.receipt_status =90 and  dhtzh.is_delete=0
            <if test="po.locationIdList != null and po.locationIdList.size() > 0">
                AND dhtzi.location_id in
                <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        ),
        rkd as (
            select  rkdh.receipt_code input_receipt_code ,  rkdi.mat_doc_code input_mat_doc_code, rkdi.mat_doc_rid input_mat_doc_rid,
            case when rkdi.mat_doc_code is not null and rkdi.mat_doc_code !='' then rkbill.posting_date else null end input_posting_date,
            case when rkdi.mat_doc_code is not null and rkdi.mat_doc_code !='' then rkbill.doc_date else null end  input_doc_date  ,
            rkbill.bill_id ,bi.batch_code  ,rkbill.qty input_qty ,rkbill.qty*base.price  input_price ,rkbill.id input_waybill_id,
            rkdi.pre_receipt_type , base.price
            from  biz_receipt_input_waybill rkbill
            JOIN biz_receipt_input_item rkdi on  rkbill.item_id=rkdi.id and rkdi.is_delete=0
            JOIN biz_receipt_input_head rkdh on  rkbill.head_id=rkdh.id and rkdh.is_delete=0 AND rkdh.receipt_status in (90,40)
            JOIN  biz_batch_info bi ON rkbill.batch_id = bi.id
            JOIN base ON  rkbill.bill_id=base.bill_id
            where  rkbill.is_delete=0 and rkdh.receipt_type=106
        ),
        cxd as (
            select  cxdh.receipt_code input_write_off_receipt_code ,  cxdi.delivery_write_off_mat_doc_code  , cxdi.write_off_mat_doc_code  ,
            cxbill.qty input_qty ,cxbill.qty*rkd.price  input_price,cxbill.bill_id ,rkd.input_waybill_id
            from rkd
            JOIN  biz_receipt_input_waybill cxbill ON cxbill.pre_input_waybill_id=rkd.input_waybill_id
            JOIN biz_receipt_input_item cxdi on  cxbill.item_id=cxdi.id and cxdi.is_delete=0
            JOIN biz_receipt_input_head cxdh on  cxbill.head_id=cxdh.id and cxdh.is_delete=0 and cxdh.receipt_status=90
            where  cxbill.is_delete=0 and cxdh.receipt_type=-106
        ),
        qt as (
            select qta.* ,
            null  ncrbh , null inconformity_Reason_quality ,null  disposal_result ,null gvnbh, null number_maintain_code,
            null batch_code , null  input_receipt_code ,  null input_mat_doc_code, null input_mat_doc_rid,
            null input_posting_date, 	null input_doc_date ,null input_qty ,null input_price,
            null input_write_off_receipt_code, null delivery_write_off_mat_doc_code, null write_off_mat_doc_code,
            null  move_type
            from (
            select base.* , null qualified_qty ,null unqualified_qty,null unarrival_qty from base where sign_inspect_code is null
            union all
            select base.*, 0 qualified_qty ,0 unqualified_qty,0 unarrival_qty from base where sign_inspect_code is not null and qty0=0 and qty1=0      and qty2=0
            ) qta
        ) ,
        hg as (
            select base.*, base.qty0 qualified_qty ,0 unqualified_qty,0 unarrival_qty ,
            null  ncrbh , null inconformity_Reason_quality ,null  disposal_result ,null gvnbh, null number_maintain_code,
            rkd.batch_code , rkd.input_receipt_code ,  rkd.input_mat_doc_code, rkd.input_mat_doc_rid,
            rkd.input_posting_date, 	rkd.input_doc_date  ,rkd.input_qty ,rkd.input_price,
            null input_write_off_receipt_code, null delivery_write_off_mat_doc_code, null write_off_mat_doc_code ,
            '105'  move_type
            from base
            LEFT JOIN rkd on  rkd.bill_id=base.bill_id and rkd.pre_receipt_type=104
            where sign_inspect_code is not null and base.qty0>0
        ) ,
        hgcx as (
            select base.*, base.qty0 qualified_qty ,0 unqualified_qty,0 unarrival_qty ,
            null  ncrbh , null inconformity_Reason_quality ,null  disposal_result ,null gvnbh, null number_maintain_code,
            rkd.batch_code , rkd.input_receipt_code ,  rkd.input_mat_doc_code, rkd.input_mat_doc_rid,
            rkd.input_posting_date, 	rkd.input_doc_date  ,-cxd.input_qty ,-cxd.input_price,
            cxd.input_write_off_receipt_code, cxd.delivery_write_off_mat_doc_code, cxd.write_off_mat_doc_code,
            '106'  move_type
            from base
            JOIN rkd on  rkd.bill_id=base.bill_id and rkd.pre_receipt_type=104
            JOIN cxd on  cxd.bill_id=base.bill_id and cxd.input_waybill_id=rkd.input_waybill_id
            where sign_inspect_code is not null and base.qty0>0

        ) ,
        bhg as (
            select base.* , 0 qualified_qty ,base.qty1 unqualified_qty,0 unarrival_qty ,
            bill.ncrbh , bill.inconformity_Reason_quality ,bill.disposal_result ,null gvnbh, null number_maintain_code
            from base
            INNER JOIN biz_receipt_waybill bill  on base.bill_id=bill.id
            LEFT JOIN biz_receipt_inconformity_head bhgtz on  bill.quality_inconformity_notice_head_id=bhgtz.id and bhgtz.is_delete=0  	and bhgtz.receipt_status=90
            where base.sign_inspect_code is not null and base.qty1>0
        ) ,
        bhgqt as (
            select bhg.* ,
            null batch_code , null  input_receipt_code ,  null input_mat_doc_code, null input_mat_doc_rid ,
            null input_posting_date, 	null input_doc_date ,null input_qty ,null input_price,
            null input_write_off_receipt_code, null delivery_write_off_mat_doc_code, null write_off_mat_doc_code ,
            null  move_type
            from bhg where bhg.disposal_result is null or bhg.disposal_result=0
        ),
        bhgfc as (
            select bhg.* ,
            null batch_code , null  input_receipt_code ,  null input_mat_doc_code, null input_mat_doc_rid ,
            null input_posting_date, 	null input_doc_date ,null input_qty ,null input_price,
            null input_write_off_receipt_code, null delivery_write_off_mat_doc_code, null write_off_mat_doc_code ,
            '104'  move_type
            from bhg where bhg.disposal_result is null or bhg.disposal_result=2
        ) ,
        bhgjs as (
            select bhg.* ,
            rkd.batch_code , rkd.input_receipt_code ,  rkd.input_mat_doc_code, rkd.input_mat_doc_rid,
            rkd.input_posting_date, 	rkd.input_doc_date  ,rkd.input_qty ,rkd.input_price,
            null input_write_off_receipt_code, null delivery_write_off_mat_doc_code, null write_off_mat_doc_code,
            '105'  move_type
            from bhg
            LEFT JOIN rkd on  bhg.bill_id=rkd.bill_id and rkd.pre_receipt_type=126
            where bhg.disposal_result is null or bhg.disposal_result=1
        ) ,
        bhgcx as (
            select bhg.* ,
            rkd.batch_code , rkd.input_receipt_code ,  rkd.input_mat_doc_code, rkd.input_mat_doc_rid,
            rkd.input_posting_date, 	rkd.input_doc_date  ,-cxd.input_qty ,-cxd.input_price,
            cxd.input_write_off_receipt_code, cxd.delivery_write_off_mat_doc_code, cxd.write_off_mat_doc_code,
            '106'  move_type
            from bhg
            JOIN rkd on  bhg.bill_id=rkd.bill_id and rkd.pre_receipt_type=126
            JOIN cxd on  cxd.bill_id=bhg.bill_id and cxd.input_waybill_id=rkd.input_waybill_id
            where bhg.disposal_result is null or bhg.disposal_result=1
        ) ,
        wdh as (
            select base.* , 0 qualified_qty ,0 unqualified_qty,base.qty2 unarrival_qty ,
            null  ncrbh , null inconformity_Reason_quality ,null  disposal_result ,bill.gvnbh, wdhcz.receipt_code number_maintain_code,
            null batch_code , null  input_receipt_code ,  null input_mat_doc_code, null input_mat_doc_rid,
            null input_posting_date, 	null input_doc_date ,null input_qty ,null input_price,
            null input_write_off_receipt_code, null delivery_write_off_mat_doc_code, null write_off_mat_doc_code ,
            '104'  move_type
            from base
            INNER JOIN biz_receipt_waybill bill  on base.bill_id=bill.id
            LEFT JOIN biz_receipt_inconformity_head wdhcz on  bill.number_inconformity_maintain_head_id=wdhcz.id and wdhcz.is_delete=0  	and wdhcz.receipt_status=90
            where sign_inspect_code is not null  and qty2>0
        ) ,
        fy as(
            select base.bill_id ,
            wzfyh.receipt_code	mat_return_receipt_code , fybill.return_box mat_return_box ,
            fybill.return_qty mat_return_qty , fybill.status_desc mat_return_status_desc
            from base
            JOIN biz_material_return_waybill  fybill on  fybill.bill_id=base.bill_id
            JOIN biz_material_return_head  wzfyh on  wzfyh.id=fybill.head_id and wzfyh.is_delete=0
            where wzfyh.receipt_status=90 and wzfyh.receipt_type=127
        ),
        a0 as(
            select * from qt
            union all
            select * from hg
            union all
            select * from hgcx
            union all
            select * from bhgqt
            union all
            select * from bhgfc
            union all
            select * from bhgjs
            union all
            select * from bhgcx
            union all
            select * from wdh
        )

        select a0.* ,
        fy.mat_return_receipt_code , fy.mat_return_box ,
        fy.mat_return_qty , fy.mat_return_status_desc
        from a0
        LEFT JOIN fy    on  fy.bill_id=a0.bill_id
        where 1=1
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            AND a0.purchase_receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND a0.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND a0.location_id = #{po.locationId}
        </if>
        <if test="po.matCode !=null and po.matCode != ''" >
            AND a0.mat_code = #{po.matCode}
        </if>
        <if test="po.mainMatCode !=null and po.mainMatCode != ''" >
            AND a0.main_mat_code = #{po.mainMatCode}
        </if>
        <if test="po.batchCode !=null and po.batchCode != ''" >
            AND a0.batch_code = #{po.batchCode}
        </if>
        <if test="po.noticeReceiptCode !=null and po.noticeReceiptCode != ''" >
            AND a0.notice_code = #{po.noticeReceiptCode}
        </if>
        <if test="po.registerReceiptCode !=null and po.registerReceiptCode != ''" >
            AND a0.register_code = #{po.registerReceiptCode}
        </if>
        <if test="po.registerPostStartTime !=null and po.registerPostEndTime!=null">
            AND a0.register_posting_date between #{po.registerPostStartTime} and #{po.registerPostEndTime}
        </if>
        <if test="po.registerDocDateStartTime !=null and po.registerDocDateEndTime!=null">
            AND a0.register_mat_doc_date between #{po.registerDocDateStartTime} and #{po.registerDocDateEndTime}
        </if>
        <if test="po.counterSignReceiptCode !=null and po.counterSignReceiptCode != ''" >
            AND a0.sign_inspect_code = #{po.counterSignReceiptCode}
        </if>
        <if test="po.inspectInputReceiptCode !=null and po.inspectInputReceiptCode != ''" >
            AND a0.input_receipt_code = #{po.inspectInputReceiptCode}
        </if>
        <if test="po.inspectInputPostStartTime !=null and po.inspectInputPostEndTime!=null">
            AND a0.input_posting_date between #{po.inspectInputPostStartTime} and #{po.inspectInputPostEndTime}
        </if>
        <if test="po.inspectInputDocDateStartTime !=null and po.inspectInputDocDateEndTime!=null">
            AND a0.input_doc_date between #{po.inspectInputDocDateStartTime} and #{po.inspectInputDocDateEndTime}
        </if>
        <if test="po.moveType !=null and po.moveType != ''" >
            AND a0.move_type = #{po.moveType}
        </if>
        <if test="po.matName !=null and po.matName != ''" >
            and a0.mat_name  like concat('%' #{po.matName}, '%')
        </if>
    </select>


    <!--查询质检会签同步数据-->
    <select id="selectDtsSyncRecordBySignInspect"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.UnitizedInputLedgerVo">

        select
            bill.price,
            bill.waybill_remark,
            bill.mat_name,
            bill.arrival_qty,
            bill.is_main_parts,
            bill.product_date,
            bill.extend64,
            bill.package_form,
            bill.extend70,
            bill.extend71,
            bill.extend72,
            bill.extend73,
            bill.extend74,
            bill.case_weight,
            bill.functional_location_code,
            bill.extend2,
            bill.extend20,
            bill.extend24,
            bill.extend25,
            bill.extend26,
            bill.extend27,
            bill.extend28,
            bill.extend29,
            bill.extend31,
            bill.extend34,
            bill.extend35,
            bill.extend36,
            bill.extend37,
            bill.extend38,
            bill.extend40,
            bill.extend41,
            bill.extend42,
            bill.extend43,
            bill.extend44,
            bill.extend46,
            bill.extend47,
            bill.extend48,
            bill.extend49,
            bill.extend50,
            bill.extend62,
            bill.extend63,
            bill.case_code,
            bill.extend65,
            bill.extend66,
            bill.extend67,
            bill.extend68,
            bill.extend69,
            bill.extend60,
            bill.extend61,
            bill.unitized_visual_check ,
            zjhqh.receipt_code sign_inspect_code,
            bill.qualified_qty   ,
            bill.unqualified_qty  ,
            bill.unarrival_qty  ,
            zjhqh.submit_time  sign_inspect_complete_date,
            bill.id bill_id ,
            bill.ncrbh,
            bill.gvnbh,
            wdhtz.receipt_status number_inconformity_notice_status,
            wdhtz.modify_time close_date ,
            su.user_name close_operator
        from  biz_receipt_inspect_head zjhqh
          JOIN biz_receipt_waybill bill  ON bill.sign_inspect_head_id=zjhqh.id and zjhqh.is_delete=0
               and zjhqh.receipt_status=90
          LEFT JOIN biz_receipt_inconformity_head bhgtz on  bill.quality_inconformity_notice_head_id=bhgtz.id and bhgtz.is_delete=0
          LEFT JOIN biz_receipt_inconformity_head wdhtz on  bill.number_inconformity_notice_head_id=wdhtz.id and wdhtz.is_delete=0
          LEFT JOIN sys_user su on wdhtz.modify_user_id=su.id
        where zjhqh.id=#{id} and zjhqh.receipt_type = 104

    </select>


    <!--直抵现场报表查询-->
    <select id="selectDirectSceneRecord"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.DirectSceneVo">
        select a.*,shtzH.* from (
        SELECT
        df.id AS fty_id,
        df.fty_code,
        df.fty_name,
        dl.id AS location_id,
        dl.location_code,
        dl.location_name,
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        cgdH.receipt_code AS purchase_receipt_code,
        cgdI.rid AS purchase_receipt_rid,
        bi.spec_stock_code,
        bi.spec_stock_name,
        rkI.qty,
        bi.batch_code,
        <!-- 收获金额 -->
        rkI.dmbtr,
        bi.input_date,
        appH.receipt_code as apply_code,
        outH.create_time as approve_date,
        su.user_code AS user_code,
        su.user_name AS user_name,
        outH.receipt_code as output_code,
        outH.receipt_status as receipt_status,
        outI.rid as output_rid,
        outI.mat_doc_code as output_mat_doc_code,
        outI.doc_date as output_doc_date,
        case
        WHEN rkI.pre_receipt_type = 2320 THEN
        rkI.pre_receipt_item_id
        WHEN rkI.pre_receipt_type = 315 THEN
        bfhxTZI.pre_receipt_item_id
        ELSE '' END as zjhq_item_id
        FROM
        biz_receipt_input_head rkH
        INNER JOIN biz_receipt_input_item rkI ON rkI.head_id = rkH.id
        INNER JOIN dic_material dm ON rkI.mat_id = dm.id
        INNER JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN dic_factory df ON rkI.fty_id = df.id
        INNER JOIN dic_stock_location dl ON rkI.location_id = dl.id
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        INNER JOIN biz_batch_info bi ON rkI.batch_id = bi.id
        INNER JOIN erp_purchase_receipt_head cgdH ON cgdH.id = bi.purchase_receipt_head_id
        LEFT JOIN erp_purchase_receipt_item cgdI ON cgdI.id = bi.purchase_receipt_item_id AND cgdI.delete_tag = 0
        LEFT JOIN biz_receipt_output_bin outB ON outB.batch_id = bi.id
        LEFT JOIN biz_receipt_output_item outI ON outI.head_id = outB.head_id AND outI.id = outB.item_id
        LEFT JOIN biz_receipt_output_head outH ON outH.id = outI.head_id and outB.head_id = outH.id AND outH.receipt_type = 414
        LEFT JOIN biz_receipt_apply_item appI ON appI.id = outI.pre_receipt_item_id
        LEFT JOIN biz_receipt_apply_head appH ON appH.id = appI.head_id and appH.receipt_type = 421
        LEFT JOIN biz_receipt_output_info outInfo ON outInfo.id = appH.out_info_id
        LEFT JOIN sys_user su ON appI.modify_user_id = su.id
        LEFT JOIN biz_receipt_inconformity_head bfhxH ON bfhxH.id = rkI.pre_receipt_head_id and bfhxH.receipt_type = 315
        LEFT JOIN biz_receipt_inconformity_item bfhxI ON bfhxI.id = rkI.pre_receipt_item_id -- 不符合项处置
        LEFT JOIN biz_receipt_inconformity_head bfhxTZH ON bfhxTZH.id = bfhxI.pre_receipt_head_id and bfhxTZH.receipt_type = 314
        LEFT JOIN biz_receipt_inconformity_item bfhxTZI ON bfhxTZI.id = bfhxI.pre_receipt_item_id -- 不符合项通知
        WHERE
        rkH.receipt_type = 214
        AND rkH.receipt_status IN (90)
        ) a
        INNER JOIN biz_receipt_inspect_item zjhqI ON zjhqI.id = a.zjhq_item_id -- 质检会签
        INNER JOIN biz_receipt_inspect_head zjhqH ON zjhqH.id = zjhqI.head_id and zjhqH.receipt_type = 2320
        INNER JOIN biz_receipt_inspect_head fpzjH ON fpzjH.id = zjhqI.pre_receipt_head_id and fpzjH.receipt_type = 2310
        INNER JOIN biz_receipt_inspect_item fpzjI ON zjhqI.pre_receipt_item_id = fpzjI.id -- 分配质检
        INNER JOIN biz_receipt_register_head dhdjH ON dhdjH.id = fpzjI.pre_receipt_head_id and dhdjH.receipt_type = 221
        INNER JOIN biz_receipt_register_item dhdjI ON dhdjI.id = fpzjI.pre_receipt_item_id -- 到货登记
        INNER JOIN biz_receipt_delivery_notice_head shtzH ON shtzH.id = dhdjI.pre_receipt_head_id
        where shtzH.is_direct_scene = 1
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            AND a.purchase_receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND a.fty_id = #{po.ftyId}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND a.fty_id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND a.location_id = #{po.locationId}
        </if>
        <if test="po.inputStartTime !=null and po.inputEndTime!=null">
            AND a.input_date between #{po.inputStartTime} and #{po.inputEndTime}
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND a.output_doc_date between #{po.startTime} and #{po.endTime}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND zjhqI.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY a.input_date desc
    </select>

    <!--入库物资跟踪报表查询-->
    <select id="selectDeliveryTrackRecord"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.DeliveryTrackVo">
        select
        cgdH.receipt_code as purchase_receipt_code,
        cgdI.rid as purchase_receipt_rid,
        cgdI.contract_code,
        cgdI.contract_name,
        cgdI.supplier_code,
        cgdI.supplier_name,
        shtzH.receipt_code as delivery_code,
        shtzH.modify_time as delivery_complete_date,
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        shtzI.qty as delivery_qty,
        dmg.mat_group_code,
        dmg.mat_group_name,
        df.id as fty_id,
        df.fty_code,
        df.fty_name,
        dl.id as location_id,
        dl.location_code,
        dl.location_name,
        dhdjH.receipt_code as register_code,
        dhdjI.mat_doc_code as register_mat_doc_code,
        dhdjI.doc_date as register_mat_doc_date,
        dhdjI.create_user_id as register_user_id,
        suu.user_code as register_user_code,
        suu.user_name as register_user_name,
        dhdjH.delivery_car,
        dhdjH.deposit_point,
        zjhqH.receipt_code as sign_inspect_code,
        case WHEN zjhqH.receipt_status = 90 THEN zjhqH.modify_time
        else NULL END as sign_inspect_complete_date,
        a.inconformity_notice_code,
        a.input_code,
        a.input_mat_doc_code,
        a.input_doc_date,
        a.dmbtr,
        taskI.create_time as input_date,
        su.user_code as input_user_code,
        su.user_name as input_user_name,
        bin.bin_code,
        taskI.qty
        from (
        SELECT
        bi.input_date,
        rkI.dmbtr,
        rkI.qty,
        rkI.id AS input_item_id,
        bfhxTZH.receipt_code AS inconformity_notice_code,
        rkH.receipt_code AS input_code,
        rkI.mat_doc_code AS input_mat_doc_code,
        rkI.doc_date AS input_doc_date,
        CASE
        WHEN rkI.pre_receipt_type = 2320 THEN
        rkI.pre_receipt_item_id
        WHEN rkI.pre_receipt_type = 315 THEN
        bfhxTZI.pre_receipt_item_id
        ELSE
        ''
        END AS zjhq_item_id
        FROM
        biz_receipt_input_head rkH
        LEFT JOIN biz_receipt_input_item rkI ON rkI.head_id = rkH.id
        LEFT JOIN biz_batch_info bi ON rkI.batch_id = bi.id
        LEFT JOIN biz_receipt_inconformity_head bfhxH ON bfhxH.id = rkI.pre_receipt_head_id
        AND bfhxH.receipt_type = 315
        LEFT JOIN biz_receipt_inconformity_item bfhxI ON bfhxI.id = rkI.pre_receipt_item_id -- 不符合项处置
        LEFT JOIN biz_receipt_inconformity_head bfhxTZH ON bfhxTZH.id = bfhxI.pre_receipt_head_id
        AND bfhxTZH.receipt_type = 314
        LEFT JOIN biz_receipt_inconformity_item bfhxTZI ON bfhxTZI.id = bfhxI.pre_receipt_item_id -- 不符合项通知
        WHERE
        rkH.receipt_type = 214
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND rkI.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) a
        LEFT JOIN biz_receipt_inspect_item zjhqI ON zjhqI.id = a.zjhq_item_id -- 质检会签
        LEFT JOIN biz_receipt_inspect_head zjhqH ON zjhqH.id = zjhqI.head_id
        AND zjhqH.receipt_type = 2320
        LEFT JOIN biz_receipt_inspect_item fpzjI ON fpzjI.id = zjhqI.pre_receipt_item_id -- 分配质检
        LEFT JOIN biz_receipt_inspect_head fpzjH ON fpzjH.id = fpzjI.head_id
        AND fpzjH.receipt_type = 2310
        LEFT JOIN biz_receipt_register_item dhdjI ON dhdjI.id = fpzjI.pre_receipt_item_id -- 到货登记
        LEFT JOIN biz_receipt_register_head dhdjH ON dhdjH.id = dhdjI.head_id
        AND dhdjH.receipt_type = 221
        LEFT JOIN biz_receipt_delivery_notice_item shtzI ON shtzI.id = dhdjI.pre_receipt_item_id
        LEFT join biz_receipt_delivery_notice_head shtzH ON shtzH.id = shtzI.head_id
        LEFT JOIN biz_receipt_task_req_item reqI ON a.input_item_id = reqI.pre_receipt_item_id
        LEFT JOIN biz_receipt_task_req_head reqH ON reqH.id = reqI.head_id
        LEFT JOIN biz_receipt_task_item taskI ON reqI.id = taskI.task_req_item_id
        LEFT JOIN biz_receipt_task_head taskH ON taskH.id = reqI.head_id
        LEFT JOIN erp_purchase_receipt_head cgdH ON cgdH.id = shtzI.refer_receipt_head_id
        LEFT JOIN erp_purchase_receipt_item cgdI ON cgdI.id = shtzI.refer_receipt_item_id  AND cgdI.delete_tag = 0
        LEFT JOIN dic_wh_storage_bin bin ON bin.id = taskI.target_bin_id
        LEFT JOIN dic_material dm ON shtzI.mat_id = dm.id
        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        LEFT JOIN dic_unit du ON dm.unit_id = du.id
        LEFT JOIN dic_factory df ON shtzI.fty_id = df.id
        LEFT JOIN dic_stock_location dl ON shtzI.location_id = dl.id
        LEFT JOIN dic_wh dw ON dl.wh_id = dw.id
        LEFT JOIN sys_user su ON taskI.modify_user_id = su.id
        LEFT JOIN sys_user suu ON dhdjI.create_user_id = suu.id
        where shtzH.receipt_status = 90
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            AND cgdH.receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND df.id = #{po.ftyId}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND df.id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND dl.id = #{po.locationId}
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND shtzH.modify_time between #{po.startTime} and #{po.endTime}
        </if>
        ORDER BY shtzH.modify_time desc
    </select>


    <!--出库台账报表查询-->
    <select id="selectOutputLedgerRecord"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.OutputLedgerVo">
        select
        df.id AS fty_id,
        df.fty_code,
        df.fty_name,
        dc.corp_code,
        dl.id AS location_id,
        dl.location_code,
        dl.location_name,
        bi.spec_stock_code,
        bi.spec_stock_name,
        item.refer_receipt_item_id as work_receipt,
        item.refer_receipt_type as work_receipt_name,
        dm.mat_code,
        dm.mat_name,
        dm.mat_name_en,
        dm.ext_manufacturer_part_number,
        dm.ext_main_material,
        dm.ext_industry_standard_desc,
        dmg.mat_group_code,
        dmg.mat_group_name,
        du.unit_code,
        du.unit_name,
        applyHead.receipt_code,
        applyHead.modify_time applyTime,
        ifnull(applyHead.inner_plan,0) inner_plan,
        su.user_code,
        su.user_name,
        CASE WHEN dbatch.is_write_off = 0 THEN item.doc_date ELSE item.write_off_doc_date end  doc_date,
        CASE WHEN dbatch.is_write_off = 0 THEN item.posting_date ELSE item.write_off_posting_date end  posting_date,
        CASE WHEN dbatch.is_write_off = 0 THEN item.mat_doc_code ELSE item.write_off_mat_doc_code end  mat_doc_code,
        CASE WHEN dbatch.is_write_off = 0 THEN item.mat_doc_rid ELSE item.write_off_mat_doc_rid end  mat_doc_rid,
        item.item_remark as output_receipt_item_remark,
        bin.qty,
        bi.batch_code,
        bi.input_date,
        bin.dmbtr/bin.qty as price,
        bin.dmbtr as money,
        CASE WHEN applyItem.is_return_flag = 1 THEN '是' ELSE '否' end as isReturnFlag,
        applyItem.status_remark,
        applyItem.id itemId,
        suu.user_code as out_user_code,
        suu.user_name as out_user_name,
        dd.dept_code,
        dd.dept_name,
        ddo.dept_office_code,
        ddo.dept_office_name,
        head.receipt_code as outputReceiptCode,
        head.remark,
        head.receipt_status,
        reqh.modify_time reqTime,
        reqh.receipt_code reqReceiptCode,
        reqh.receipt_status preReceiptStatus,
        dbatch.is_write_off,
        dsb.bin_code
        from biz_receipt_output_head head
        LEFT JOIN biz_receipt_output_item item ON head.id = item.head_id and item.is_delete = 0
        LEFT JOIN biz_receipt_output_bin bin  on bin.item_id = item.id and bin.is_delete = 0
        LEFT JOIN biz_receipt_apply_item applyItem ON applyItem.id = item.pre_receipt_item_id and applyItem.is_delete = 0
        LEFT JOIN biz_receipt_apply_head applyHead ON applyHead.id = item.pre_receipt_head_id and applyHead.is_delete = 0
        LEFT JOIN biz_receipt_task_req_item req ON req.pre_receipt_item_id = item.id and req.is_delete = 0
        LEFT JOIN biz_receipt_task_req_head reqh ON reqh.id = req.head_id and reqh.is_delete = 0
        LEFT JOIN biz_receipt_task_item task ON task.task_req_item_id = req.id and task.is_delete = 0
        LEFT JOIN sys_user suu ON task.create_user_id = suu.id
        LEFT JOIN biz_batch_info bi ON bi.id = bin.batch_id
        LEFT JOIN dic_material dm ON item.mat_id = dm.id
        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        LEFT JOIN stock_ins_doc_batch dbatch on dbatch.pre_receipt_head_id=head.id and dbatch.pre_receipt_item_id=item.id and dbatch.pre_receipt_bin_id=bin.id
        INNER JOIN dic_unit du ON item.unit_id = du.id
        INNER JOIN dic_factory df ON item.fty_id = df.id
        INNER JOIN dic_corp dc ON df.corp_id = dc.id
        INNER JOIN dic_stock_location dl ON item.location_id = dl.id
        INNER JOIN dic_wh dw ON item.wh_id = dw.id
        LEFT JOIN dic_wh_storage_bin dsb ON dsb.id = bin.bin_id
        LEFT JOIN sys_user su ON applyHead.create_user_id = su.id
        LEFT JOIN dic_dept dd ON applyHead.create_dept_id = dd.id
        LEFT JOIN dic_dept_office ddo ON applyHead.create_office_id = ddo.id
        where head.is_delete = 0
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND item.fty_id = #{po.ftyId}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND item.fty_id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND item.location_id = #{po.locationId}
        </if>
        <if test="po.matCode !=null and po.matCode != ''" >
            and dm.mat_code  = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode !=null and po.receiptCode != ''" >
            and applyHead.receipt_code  = #{po.receiptCode}
        </if>
        <if test="po.innerPlan !=null and po.innerPlan != ''" >
            and applyHead.inner_plan  = #{po.innerPlan}
        </if>
        <if test="po.outputReceiptCode !=null and po.outputReceiptCode != ''" >
            and head.receipt_code  = #{po.outputReceiptCode}
        </if>
        <if test="po.matDocCode !=null and po.matDocCode != ''" >
            and item.mat_doc_code  = #{po.matDocCode}
        </if>
        <if test="po.batchCode !=null and po.batchCode != ''" >
            and bi.batch_code  = #{po.batchCode}
        </if>
        <if test="po.batchCodeList != null and po.batchCodeList.size() > 0">
            AND bi.batch_code in
            <foreach collection="po.batchCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.userName !=null and po.userName != ''" >
            and su.user_name  like concat('%' #{po.userName}, '%')
        </if>
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            and bi.purchase_receipt_code  = #{po.purchaseReceiptCode}
        </if>
        <if test="po.deptId !=null and po.deptId != ''" >
            and dd.id  = #{po.deptId}
        </if>
        <if test="po.officeId !=null and po.officeId != ''" >
            and ddo.id = #{po.officeId}
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND item.posting_date between #{po.startTime} and #{po.endTime}
        </if>
        <if test="po.docDateStartTime !=null and po.docDateEndTime!=null">
            AND item.doc_date between #{po.docDateStartTime} and #{po.docDateEndTime}
        </if>
        <if test="po.isReturnFlag !=null" >
            and applyItem.is_return_flag  = #{po.isReturnFlag}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND item.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY item.id,bin.id,dbatch.is_write_off

        union all

        select	df.id AS fty_id,
        df.fty_code,
        df.fty_name,
        dc.corp_code,
        dl.id AS location_id,
        dl.location_code,
        dl.location_name,
        bi.spec_stock_code,
        bi.spec_stock_name ,
        rkI.refer_receipt_item_id AS work_receipt,
        rkI.refer_receipt_type AS work_receipt_name,
        dm.mat_code,
        dm.mat_name,
        dm.mat_name_en,
        dm.ext_manufacturer_part_number,
        dm.ext_main_material,
        dm.ext_industry_standard_desc,
        dmg.mat_group_code,
        dmg.mat_group_name,
        du.unit_code,
        du.unit_name,
        braH.receipt_code,
        braH.modify_time applyTime,
        ifnull(braH.inner_plan,0) inner_plan,
        su.user_code,
        su.user_name,
        rkI.doc_date,
        rkI.posting_date,
        rkI.mat_doc_code,
        rkI.mat_doc_rid,
        rkI.item_remark AS output_receipt_item_remark,
        rkB.qty,
        bi.batch_code,
        bi.input_date,
        rkB.dmbtr / rkB.qty AS price,
        rkB.dmbtr AS money,
        '是'  AS isReturnFlag,
        braI.status_remark,
        braI.id itemId,
        suu.user_code AS out_user_code,
        suu.user_name AS out_user_name,
        dd.dept_code,
        dd.dept_name,
        ddo.dept_office_code,
        ddo.dept_office_name,
        rkH.receipt_code AS outputReceiptCode,
        rkH.remark,
        rkH.receipt_status,
        null reqTime,
        '' reqReceiptCode,
        0 preReceiptStatus,
        1  is_write_off,
        null  bin_code
        FROM
        biz_receipt_return_head rkH
        INNER JOIN biz_receipt_return_item rkI ON rkI.head_id = rkH.id
        INNER JOIN biz_receipt_return_bin rkB ON rkB.item_id = rkI.id
        INNER JOIN biz_receipt_inspect_head zjhqH ON zjhqH.id = rkI.pre_receipt_head_id
        AND zjhqH.receipt_type = 2321
        INNER JOIN biz_receipt_inspect_item zjhqI ON zjhqI.id = rkI.pre_receipt_item_id
        LEFT JOIN biz_receipt_inspect_head fpzjH ON fpzjH.id = zjhqI.pre_receipt_head_id
        AND fpzjH.receipt_type = 2311
        LEFT JOIN biz_receipt_inspect_item fpzjI ON zjhqI.pre_receipt_item_id = fpzjI.id
        LEFT JOIN biz_receipt_apply_head braH ON braH.id = fpzjI.pre_receipt_head_id
        LEFT JOIN biz_receipt_apply_item braI ON fpzjI.pre_receipt_item_id = braI.id
        LEFT JOIN sys_user suu ON rkH.create_user_id = suu.id
        LEFT JOIN biz_batch_info bi ON bi.id = rkB.batch_id
        LEFT JOIN dic_material dm ON rkI.mat_id = dm.id
        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        LEFT JOIN stock_ins_doc_batch dbatch ON dbatch.pre_receipt_head_id = rkH.id
        AND dbatch.pre_receipt_item_id = rkI.id
        AND dbatch.pre_receipt_bin_id = rkB.id
        INNER JOIN dic_unit du ON braI.unit_id = du.id
        INNER JOIN dic_factory df ON braI.fty_id = df.id
        INNER JOIN dic_corp dc ON df.corp_id = dc.id
        INNER JOIN dic_stock_location dl ON braI.location_id = dl.id
        INNER JOIN dic_wh dw ON braI.wh_id = dw.id
        LEFT JOIN sys_user su ON braH.create_user_id = su.id
        LEFT JOIN dic_dept dd ON braH.create_dept_id = dd.id
        LEFT JOIN dic_dept_office ddo ON braH.create_office_id = ddo.id
        where rkH.is_delete = 0
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND df.id = #{po.ftyId}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND df.id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND dl.id = #{po.locationId}
        </if>
        <if test="po.matCode !=null and po.matCode != ''" >
            and dm.mat_code  = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode !=null and po.receiptCode != ''" >
            and braH.receipt_code  = #{po.receiptCode}
        </if>
        <if test="po.innerPlan !=null and po.innerPlan != ''" >
            and braH.inner_plan  = #{po.innerPlan}
        </if>
        <if test="po.outputReceiptCode !=null and po.outputReceiptCode != ''" >
            and rkH.receipt_code  = #{po.outputReceiptCode}
        </if>
        <if test="po.matDocCode !=null and po.matDocCode != ''" >
            and rkI.mat_doc_code  = #{po.matDocCode}
        </if>
        <if test="po.batchCode !=null and po.batchCode != ''" >
            and bi.batch_code  = #{po.batchCode}
        </if>
        <if test="po.batchCodeList != null and po.batchCodeList.size() > 0">
            AND bi.batch_code in
            <foreach collection="po.batchCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.userName !=null and po.userName != ''" >
            and su.user_name  like concat('%' #{po.userName}, '%')
        </if>
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            and bi.purchase_receipt_code  = #{po.purchaseReceiptCode}
        </if>
        <if test="po.deptId !=null and po.deptId != ''" >
            and dd.id  = #{po.deptId}
        </if>
        <if test="po.officeId !=null and po.officeId != ''" >
            and ddo.id = #{po.officeId}
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND rkI.posting_date between #{po.startTime} and #{po.endTime}
        </if>
        <if test="po.docDateStartTime !=null and po.docDateEndTime!=null">
            AND rkI.doc_date between #{po.docDateStartTime} and #{po.docDateEndTime}
        </if>
        <if test="po.isReturnFlag !=null" >
            and 1  = #{po.isReturnFlag}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND rkI.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--成套设备出库台账报表查询-->
    <select id="selectOutputLedgerRecordUnitized"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.UnitizedOutputLedgerVo">
        select fty.fty_code,
               fty.fty_name,
               main_loc.location_code          AS main_location_code,
               main_loc.location_name          AS main_location_name,
               loc.location_code,
               loc.location_name,
               dm.mat_code                AS main_mat_code,
               dm.mat_name                AS main_mat_name,
               cdm.mat_code,
               cdm.mat_name,
               cgdI.spec_stock_code,
               cgdI.spec_stock_name,
               cgdH.receipt_code          AS purchase_receipt_code,
               cgdI.rid                   AS purchase_receipt_rid,
               cgdI.contract_code,
               cgdI.contract_name,
               du.unit_code,
               du.unit_name,
               brob.qty,
               brah.receipt_code          as pre_receipt_code,
               broh.receipt_code,
               bill.price,
               brob.qty * bill.price as output_price,
               broi.mat_doc_code,
               broi.mat_doc_rid,
               broi.posting_date,
               broi.doc_date,
               dbatch.is_write_off,
               brai.reserved_order_code,
               brai.reserved_order_rid,
        ph.receipt_code paper_code,
        ph.island,
        ph.paper_system,
        ph.area,
        ph.factory_building,
               bill.is_main_parts,
               bill.product_date,
               bill.case_code,
               bill.package_form,
               bill.extend70,
               bill.extend71,
               bill.extend72,
               bill.case_weight,
               bill.functional_location_code,
               bill.extend2,
               bill.extend20,
               bill.extend24,
               bill.extend25,
               bill.extend26,
               bill.extend27,
               bill.extend28,
               bill.extend29,
               bill.extend31,
               bill.extend34,
               bill.extend35,
               bill.extend36,
               bill.extend37,
               bill.extend38,
               bill.extend40,
               bill.extend41,
               bill.extend42,
               bill.extend43,
               bill.extend44,
               bill.extend46,
               bill.extend47,
               bill.extend48,
               bill.extend49,
               bill.extend50,
               bill.extend62,
               bill.extend63,
               bill.extend64,
               bill.extend65,
               bill.extend66
        from biz_receipt_output_head broh
        INNER join biz_receipt_output_bin brob on brob.head_id = broh.id and brob.is_delete = 0
        INNER JOIN biz_receipt_apply_bin brab on brab.id = brob.apply_bin_id
        INNER JOIN biz_receipt_apply_head brah on brah.id = brab.head_id
        INNER JOIN biz_receipt_apply_item brai on brai.head_id = brab.head_id
        INNER join biz_receipt_output_item broi on broi.head_id = broh.id and broi.pre_receipt_item_id = brai.id and broi.is_delete = 0
        INNER join biz_batch_info bbi on bbi.id = brob.batch_id
        INNER join dic_material dm on dm.id = brob.mat_id
        INNER join biz_receipt_waybill bill on bill.mat_id = bbi.mat_id and bill.pre_id = 0
        INNER JOIN dic_unit du ON bill.unit_id = du.id
        INNER JOIN biz_receipt_delivery_notice_item brdni ON brdni.id = bill.delivery_notice_item_id
        LEFT JOIN erp_purchase_receipt_head cgdH ON cgdH.id = brdni.refer_receipt_head_id
        LEFT JOIN erp_purchase_receipt_item cgdI ON cgdI.id = brdni.refer_receipt_item_id AND cgdI.delete_tag = 0
        LEFT JOIN stock_ins_doc_batch dbatch on dbatch.pre_receipt_head_id = broh.id and dbatch.pre_receipt_item_id = broi.id and dbatch.pre_receipt_bin_id = brob.id
        LEFT JOIN biz_receipt_paper_head ph ON ph.id = brai.refer_receipt_head_id
        LEFT JOIN dic_material cdm ON bill.mat_id = cdm.id
        LEFT JOIN dic_factory fty ON fty.id = brab.fty_id
        LEFT JOIN dic_stock_location main_loc ON main_loc.id = brdni.location_id
        LEFT JOIN dic_stock_location loc ON loc.id = brab.location_id
        where broh.receipt_type = 108
          and broh.receipt_status = 90
          and broh.is_delete = 0
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND brob.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND brob.location_id = #{po.locationId}
        </if>
        <if test="po.mainMatCode !=null and po.mainMatCode != ''" >
            AND dm.mat_code = #{po.mainMatCode}
        </if>
        <if test="po.mainMatCodeList !=null and po.mainMatCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.mainMatCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matCode !=null and po.matCode != ''" >
            AND cdm.mat_code = #{po.matCode}
        </if>
        <if test="po.matCodeList !=null and po.matCodeList.size() > 0 ">
            AND cdm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend20 !=null and po.extend20 != ''" >
            AND bill.extend20 = #{po.extend20}
        </if>
        <if test="po.extend20List !=null and po.extend20List.size() > 0 ">
            AND bill.extend20 in
            <foreach collection="po.extend20List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.functionalLocationCode !=null and po.functionalLocationCode != ''" >
            AND bill.functional_location_code = #{po.functionalLocationCode}
        </if>
        <if test="po.functionalLocationCodeList !=null and po.functionalLocationCodeList.size() > 0 ">
            AND bill.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend29 !=null and po.extend29 != ''" >
            AND bill.extend29 = #{po.extend29}
        </if>
        <if test="po.extend29List !=null and po.extend29List.size() > 0 ">
            AND bill.extend29 in
            <foreach collection="po.extend29List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.specStockCode !=null and po.specStockCode != ''" >
            AND cgdI.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.specStockCodeList !=null and po.specStockCodeList.size() > 0 ">
            AND cgdI.spec_stock_code in
            <foreach collection="po.specStockCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.preReceiptCode !=null and po.preReceiptCode != ''" >
            AND brah.receipt_code = #{po.preReceiptCode}
        </if>
        <if test="po.preReceiptCodeList !=null and po.preReceiptCodeList.size() > 0 ">
            AND brah.receipt_code in
            <foreach collection="po.preReceiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode !=null and po.receiptCode != ''" >
            AND broh.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList !=null and po.receiptCodeList.size() > 0 ">
            AND broh.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matDocCode !=null and po.matDocCode != ''" >
            AND broi.mat_doc_code = #{po.matDocCode}
        </if>
        <if test="po.matDocCodeList !=null and po.matDocCodeList.size() > 0 ">
            AND broi.mat_doc_code in
            <foreach collection="po.matDocCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.batchCode !=null and po.batchCode != ''" >
            AND bbi.batch_code = #{po.batchCode}
        </if>
        <if test="po.batchCodeList !=null and po.batchCodeList.size() > 0 ">
            AND bbi.batch_code in
            <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            AND cgdH.receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.purchaseReceiptCodeList !=null and po.purchaseReceiptCodeList.size() > 0 ">
            AND cgdH.receipt_code in
            <foreach collection="po.purchaseReceiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.contractCode !=null and po.contractCode != ''" >
            AND cgdI.contract_code = #{po.contractCode}
        </if>
        <if test="po.contractCodeList !=null and po.contractCodeList.size() > 0 ">
            AND cgdI.contract_code in
            <foreach collection="po.contractCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.postStartTime !=null and po.postEndTime!=null">
            AND broi.posting_date between #{po.postStartTime} and #{po.postEndTime}
        </if>
        <if test="po.docDateStartTime !=null and po.docDateEndTime!=null">
            AND broi.doc_date between #{po.docDateStartTime} and #{po.docDateEndTime}
        </if>
    </select>

    <!--入库台账报表查询-->
    <select id="selectReturnNewLedgerRecord"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.ReturnNewLedgerVo">
        select a.*,shtzH.* from (
        SELECT
        df.id AS fty_id,
        df.fty_code,
        df.fty_name,
        dl.id AS location_id,
        dl.location_code,
        dl.location_name,
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        cgdH.receipt_code AS purchase_receipt_code,
        cgdI.rid AS purchase_receipt_rid,
        bi.spec_stock_code,
        bi.spec_stock_name,
        rkI.qty,
        bi.batch_code,
        <!-- 收获金额 -->
        bi.input_date,
        appH.receipt_code as apply_code,
        outD.create_time as approve_date,
        su.user_code AS user_code,
        su.user_name AS user_name,
        outD.receipt_code as output_code,
        outD.rid as output_rid,
        outD.mat_doc_code as output_mat_doc_code,
        outD.doc_date as output_doc_date,
        case
        WHEN rkI.pre_receipt_type = 2320 THEN
        rkI.pre_receipt_item_id
        WHEN rkI.pre_receipt_type = 315 THEN
        bfhxTZI.pre_receipt_item_id
        ELSE '' END as zjhq_item_id
        FROM
        biz_receipt_input_head rkH
        INNER JOIN biz_receipt_input_item rkI ON rkI.head_id = rkH.id
        INNER JOIN dic_material dm ON rkI.mat_id = dm.id
        INNER JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN dic_factory df ON rkI.fty_id = df.id
        INNER JOIN dic_stock_location dl ON rkI.location_id = dl.id
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        INNER JOIN biz_batch_info bi ON rkI.batch_id = bi.id
        INNER JOIN erp_purchase_receipt_head cgdH ON cgdH.id = bi.purchase_receipt_head_id
        LEFT JOIN erp_purchase_receipt_item cgdI ON cgdI.id = bi.purchase_receipt_item_id  AND cgdI.delete_tag = 0
        LEFT JOIN(
        select outI.pre_receipt_item_id, outI.pre_receipt_head_id,outI.mat_doc_code,outI.rid,outI.doc_date,outH.receipt_code,outB.batch_id,outH.create_time
        from biz_receipt_output_head outH
        LEFT JOIN biz_receipt_output_item outI ON outI.head_id = outH.id
        LEFT JOIN biz_receipt_output_bin outB ON outB.head_id = outH.id AND outB.item_id = outI.id
        where outH.receipt_type = 414) outD ON outD.batch_id = bi.id
        LEFT JOIN biz_receipt_apply_item appI ON appI.id = outD.pre_receipt_item_id
        LEFT JOIN biz_receipt_apply_head appH ON appH.id = appI.head_id and appH.receipt_type = 421
        LEFT JOIN biz_receipt_output_info outInfo ON outInfo.id = appH.out_info_id
        LEFT JOIN sys_user su ON appI.modify_user_id = su.id
        LEFT JOIN biz_receipt_inconformity_head bfhxH ON bfhxH.id = rkI.pre_receipt_head_id and bfhxH.receipt_type = 315
        LEFT JOIN biz_receipt_inconformity_item bfhxI ON bfhxI.id = rkI.pre_receipt_item_id -- 不符合项处置
        LEFT JOIN biz_receipt_inconformity_head bfhxTZH ON bfhxTZH.id = bfhxI.pre_receipt_head_id and bfhxTZH.receipt_type = 314
        LEFT JOIN biz_receipt_inconformity_item bfhxTZI ON bfhxTZI.id = bfhxI.pre_receipt_item_id -- 不符合项通知
        WHERE
        rkH.receipt_type = 214
        AND rkH.receipt_status IN (90)
        ) a
        INNER JOIN biz_receipt_inspect_item zjhqI ON zjhqI.id = a.zjhq_item_id -- 质检会签
        INNER JOIN biz_receipt_inspect_head zjhqH ON zjhqH.id = zjhqI.head_id and zjhqH.receipt_type = 2320
        INNER JOIN biz_receipt_inspect_head fpzjH ON fpzjH.id = zjhqI.pre_receipt_head_id and fpzjH.receipt_type = 2310
        INNER JOIN biz_receipt_inspect_item fpzjI ON zjhqI.pre_receipt_item_id = fpzjI.id -- 分配质检
        INNER JOIN biz_receipt_register_head dhdjH ON dhdjH.id = fpzjI.pre_receipt_head_id and dhdjH.receipt_type = 221
        INNER JOIN biz_receipt_register_item dhdjI ON dhdjI.id = fpzjI.pre_receipt_item_id -- 到货登记
        INNER JOIN biz_receipt_delivery_notice_head shtzH ON shtzH.id = dhdjI.pre_receipt_head_id
        where shtzH.is_direct_scene = 1
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            AND a.purchase_receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND a.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND a.location_id = #{po.locationId}
        </if>
        <if test="po.inputStartTime !=null and po.inputEndTime!=null">
            AND a.input_date between #{po.inputStartTime} and #{po.inputEndTime}
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND a.output_doc_date between #{po.startTime} and #{po.endTime}
        </if>
    </select>


    <!--入库台账报表查询-->
    <select id="selectToolLedgerRecord"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.ToolLedgerVo">
        select a.*,shtzH.* from (
        SELECT
        df.id AS fty_id,
        df.fty_code,
        df.fty_name,
        dl.id AS location_id,
        dl.location_code,
        dl.location_name,
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        cgdH.receipt_code AS purchase_receipt_code,
        cgdI.rid AS purchase_receipt_rid,
        bi.spec_stock_code,
        bi.spec_stock_name,
        rkI.qty,
        bi.batch_code,
        <!-- 收获金额 -->
        bi.input_date,
        appH.receipt_code as apply_code,
        outD.create_time as approve_date,
        su.user_code AS user_code,
        su.user_name AS user_name,
        outD.receipt_code as output_code,
        outD.rid as output_rid,
        outD.mat_doc_code as output_mat_doc_code,
        outD.doc_date as output_doc_date,
        case
        WHEN rkI.pre_receipt_type = 2320 THEN
        rkI.pre_receipt_item_id
        WHEN rkI.pre_receipt_type = 315 THEN
        bfhxTZI.pre_receipt_item_id
        ELSE '' END as zjhq_item_id
        FROM
        biz_receipt_input_head rkH
        INNER JOIN biz_receipt_input_item rkI ON rkI.head_id = rkH.id
        INNER JOIN dic_material dm ON rkI.mat_id = dm.id
        INNER JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN dic_factory df ON rkI.fty_id = df.id
        INNER JOIN dic_stock_location dl ON rkI.location_id = dl.id
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        INNER JOIN biz_batch_info bi ON rkI.batch_id = bi.id
        INNER JOIN erp_purchase_receipt_head cgdH ON cgdH.id = bi.purchase_receipt_head_id
        LEFT JOIN erp_purchase_receipt_item cgdI ON cgdI.id = bi.purchase_receipt_item_id  AND cgdI.delete_tag = 0
        LEFT JOIN(
        select outI.pre_receipt_item_id, outI.pre_receipt_head_id,outI.mat_doc_code,outI.rid,outI.doc_date,outH.receipt_code,outB.batch_id,outH.create_time
        from biz_receipt_output_head outH
        LEFT JOIN biz_receipt_output_item outI ON outI.head_id = outH.id
        LEFT JOIN biz_receipt_output_bin outB ON outB.head_id = outH.id AND outI.id = outB.item_id
        where outH.receipt_type = 414) outD ON outD.batch_id = bi.id
        LEFT JOIN biz_receipt_apply_item appI ON appI.id = outD.pre_receipt_item_id
        LEFT JOIN biz_receipt_apply_head appH ON appH.id = appI.head_id and appH.receipt_type = 421
        LEFT JOIN biz_receipt_output_info outInfo ON outInfo.id = appH.out_info_id
        LEFT JOIN sys_user su ON appI.modify_user_id = su.id
        LEFT JOIN biz_receipt_inconformity_head bfhxH ON bfhxH.id = rkI.pre_receipt_head_id and bfhxH.receipt_type = 315
        LEFT JOIN biz_receipt_inconformity_item bfhxI ON bfhxI.id = rkI.pre_receipt_item_id -- 不符合项处置
        LEFT JOIN biz_receipt_inconformity_head bfhxTZH ON bfhxTZH.id = bfhxI.pre_receipt_head_id and bfhxTZH.receipt_type = 314
        LEFT JOIN biz_receipt_inconformity_item bfhxTZI ON bfhxTZI.id = bfhxI.pre_receipt_item_id -- 不符合项通知
        WHERE
        rkH.receipt_type = 214
        AND rkH.receipt_status IN (90)
        ) a
        INNER JOIN biz_receipt_inspect_item zjhqI ON zjhqI.id = a.zjhq_item_id -- 质检会签
        INNER JOIN biz_receipt_inspect_head zjhqH ON zjhqH.id = zjhqI.head_id and zjhqH.receipt_type = 2320
        INNER JOIN biz_receipt_inspect_head fpzjH ON fpzjH.id = zjhqI.pre_receipt_head_id and fpzjH.receipt_type = 2310
        INNER JOIN biz_receipt_inspect_item fpzjI ON zjhqI.pre_receipt_item_id = fpzjI.id -- 分配质检
        INNER JOIN biz_receipt_register_head dhdjH ON dhdjH.id = fpzjI.pre_receipt_head_id and dhdjH.receipt_type = 221
        INNER JOIN biz_receipt_register_item dhdjI ON dhdjI.id = fpzjI.pre_receipt_item_id -- 到货登记
        INNER JOIN biz_receipt_delivery_notice_head shtzH ON shtzH.id = dhdjI.pre_receipt_head_id
        where shtzH.is_direct_scene = 1
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            AND a.purchase_receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND a.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND a.location_id = #{po.locationId}
        </if>
        <if test="po.inputStartTime !=null and po.inputEndTime!=null">
            AND a.input_date between #{po.inputStartTime} and #{po.inputEndTime}
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND a.output_doc_date between #{po.startTime} and #{po.endTime}
        </if>
    </select>

    <!--查询寿期台账-->
    <select id="selectLifetimeDetail"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.LifetimeVO">

        SELECT dm.mat_code mat_code,
               dm.mat_name mat_name,
               df.fty_code fty_code,
               dl.location_code location_code,
               bi.batch_code batch_code,
               brlhf.receipt_code receipt_code,
               brlhf.remark remark,
               brlhf.create_time create_time,
               suf.user_name submit_user_name,
               brlh.receipt_code receipt_code_result,
               brlh.remark remark_result,
               brlh.create_time create_time_result,
               su.user_name submit_user_name_result,
               li.qty qty,
               du.unit_name unit_name,
               dwb.bin_code bin_code,
               dmf.package_type package_type,
               dmf.deposit_type deposit_type,
               dmf.shelf_life_max shelf_life_max,
               li.inspect_user_name inspect_user_name,
               bi.apply_user_name apply_user_name,
               bi.apply_user_dept_name apply_user_dept_name,
               li.product_date product_date,
               li.expire_date expire_date,
               li.inspect_result inspect_result,
               li.delay_date delay_date,
               li.scrap_cause scrap_cause
        FROM biz_receipt_lifetime_head brlh
                 INNER JOIN biz_receipt_lifetime_item li ON li.head_id = brlh.id
                 INNER JOIN biz_receipt_lifetime_head brlhf ON brlhf.id = li.pre_receipt_head_id
                 INNER JOIN biz_receipt_lifetime_item lif ON li.pre_receipt_item_id = lif.id
                 INNER JOIN dic_material dm ON dm.id = li.mat_id
                 INNER JOIN biz_batch_info bi ON bi.id = li.batch_id
                 INNER JOIN dic_unit du ON li.unit_id = du.id
                 INNER JOIN dic_factory df ON li.fty_id = df.id
                 INNER JOIN dic_wh_storage_bin dwb ON li.bin_id = dwb.id
                 INNER JOIN dic_stock_location dl ON li.location_id = dl.id
                 LEFT JOIN dic_material_factory dmf ON li.fty_id = dmf.fty_id and dmf.mat_id = li.mat_id
                 LEFT JOIN sys_user su ON brlh.submit_user_id = su.id
                 LEFT JOIN sys_user suf ON brlhf.submit_user_id = suf.id
        WHERE brlh.is_delete = 0
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND li.fty_id = #{po.ftyId}
        </if>
        <if test="po.receiptType !=null and po.receiptType != ''">
            AND brlhf.receipt_type = #{po.receiptType}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND li.fty_id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND li.location_id = #{po.locationId}
        </if>
        <if test="po.matCode !=null and po.matCode != ''" >
            AND dm.mat_code  = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.batchCode !=null and po.batchCode != ''" >
            AND bi.batch_code  = #{po.batchCode}
        </if>
        <if test="po.batchCodeList != null and po.batchCodeList.size() > 0">
            AND bi.batch_code in
            <foreach collection="po.batchCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode !=null and po.receiptCode != ''" >
            AND brlhf.receipt_code  = #{po.receiptCode}
        </if>
        <if test="po.remark !=null and po.remark != ''" >
            AND brlhf.remark like concat( '%',#{po.remark}, '%')
        </if>
        <if test="po.receiptCodeResult !=null and po.receiptCodeResult != ''" >
            AND brlh.receipt_code  = #{po.receiptCodeResult}
        </if>
        <if test="po.inspectUserName !=null and po.inspectUserName != ''" >
            AND li.inspect_user_name  = #{po.inspectUserName}
        </if>
        <if test="po.applyUserName !=null and po.applyUserName != ''" >
            AND bi.apply_user_name  = #{po.applyUserName}
        </if>
        <if test="po.applyUserDeptCode !=null and po.applyUserDeptCode != ''" >
            AND bi.apply_user_dept_code  = #{po.applyUserDeptCode}
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND li.expire_date between #{po.startTime} and #{po.endTime}
        </if>
        <if test="po.docDateStartTime !=null and po.docDateEndTime!=null">
            AND li.delay_date between #{po.docDateStartTime} and #{po.docDateEndTime}
        </if>
        <if test="po.inspectResult !=null and po.inspectResult != ''">
            AND li.inspect_result  = #{po.inspectResult}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND li.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--查询维保台账-->
    <select id="selectMaintainDetail"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.MaintainVO">
        SELECT dm.mat_code mat_code,
               dm.mat_name mat_name,
               df.fty_code fty_code,
               dl.location_code location_code,
               bi.batch_code batch_code,
               brlhf.receipt_code receipt_code,
               brlhf.maintenance_type maintenance_type,
               brlhf.remark remark,
               brlhf.create_time create_time,
               brlhf.plan_complete_date plan_complete_date,
               suf.user_name submit_user_name,
               brlh.receipt_code receipt_code_result,
               brlh.create_time create_time_result,
               brlh.remark remark_result,
               brlh.submit_time submit_time_result,
               su.user_name submit_user_name_result,
               li.qty qty,
               du.unit_name unit_name,
               dwb.bin_code bin_code,
               dmf.package_type package_type,
               dmf.deposit_type deposit_type,
               li.maintenance_program maintenance_program,
               li.defect_describe defect_describe,
               li.state_desc state_desc,
               li.maintain_content maintain_content,
               li.maintenance_date maintenance_date,
               li.maintenance_date_normal maintenance_date_normal,
               li.maintenance_date_pro maintenance_date_pro,
               bi.input_date,
               bi.apply_user_name,
               bi.apply_user_dept_name,
               bi.maintenance_cycle
        FROM biz_receipt_maintain_head brlh
                 INNER JOIN biz_receipt_maintain_item li ON li.head_id = brlh.id
                 INNER JOIN biz_receipt_maintain_head brlhf ON brlhf.id = li.pre_receipt_head_id
                 INNER JOIN biz_receipt_maintain_item lif ON li.pre_receipt_item_id = lif.id
                 INNER JOIN dic_material dm ON dm.id = li.mat_id
                 INNER JOIN biz_batch_info bi ON bi.id = li.batch_id
                 INNER JOIN dic_unit du ON li.unit_id = du.id
                 INNER JOIN dic_factory df ON li.fty_id = df.id
                 INNER JOIN dic_wh_storage_bin dwb ON li.bin_id = dwb.id
                 INNER JOIN dic_stock_location dl ON li.location_id = dl.id
                 LEFT JOIN dic_material_factory dmf ON li.fty_id = dmf.fty_id and dmf.mat_id = li.mat_id
                 LEFT JOIN sys_user su ON brlh.submit_user_id = su.id
                 LEFT JOIN sys_user suf ON brlhf.submit_user_id = suf.id
        WHERE brlh.is_delete = 0
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND li.fty_id = #{po.ftyId}
        </if>
        <if test="po.ftyIdList != null and po.ftyIdList.size() > 0">
            AND li.fty_id in
            <foreach collection="po.ftyIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND li.location_id = #{po.locationId}
        </if>
        <if test="po.matCode !=null and po.matCode != ''" >
            AND dm.mat_code  = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.batchCode !=null and po.batchCode != ''" >
            AND bi.batch_code  = #{po.batchCode}
        </if>
        <if test="po.batchCodeList != null and po.batchCodeList.size() > 0">
            AND bi.batch_code in
            <foreach collection="po.batchCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode !=null and po.receiptCode != ''" >
            AND brlhf.receipt_code  = #{po.receiptCode}
        </if>
        <if test="po.maintenanceType !=null and po.maintenanceType != ''" >
            AND brlhf.maintenance_type  = #{po.maintenanceType}
        </if>
        <if test="po.remark !=null and po.remark != ''" >
            AND brlhf.remark like concat( '%',#{po.remark}, '%')
        </if>
        <if test="po.receiptCodeResult !=null and po.receiptCodeResult != ''" >
            AND brlh.receipt_code  = #{po.receiptCodeResult}
        </if>
        <if test="po.binCode !=null and po.binCode != ''" >
            AND dwb.bin_code  = #{po.binCode}
        </if>
        <if test="po.binCodeList != null and po.binCodeList.size() > 0">
            AND dwb.bin_code in
            <foreach collection="po.binCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND li.maintenance_date between #{po.startTime} and #{po.endTime}
        </if>
        <if test="po.docDateStartTime !=null and po.docDateEndTime!=null">
            AND ((li.maintenance_date_normal between #{po.docDateStartTime} and #{po.docDateEndTime} )or (li.maintenance_date_pro between #{po.docDateStartTime} and #{po.docDateEndTime} ) )
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND li.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectReturnOldDetail"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.ReturnOldVO">
        select dm.mat_code,
               dm.mat_name,
               ah.receipt_code                                        apply_receipt_code,
               ah.create_type,
               ai.return_old_type,
               ai.qty                                                 apply_qty,
               ih.receipt_code,
               ii.qty,
               oh.receipt_code                                        output_receipt_code,
               oi.qty                                                 output_qty,
               oai.return_old_type                                    estimate_return_old_type
        from biz_receipt_input_head ih
                 join biz_receipt_input_item ii on ii.head_id = ih.id and ii.is_delete = 0
                 join dic_material dm ON dm.id = ii.mat_id
                 join biz_receipt_apply_head ah on ah.id = ii.pre_receipt_head_id and ah.is_delete = 0
                 join biz_receipt_apply_item ai on ai.id = ii.pre_receipt_item_id and ai.is_delete = 0
                 left join biz_receipt_output_head oh on oh.id = ai.pre_receipt_head_id and oh.is_delete = 0
                 left join biz_receipt_output_item oi on oi.id = ai.pre_receipt_item_id and oi.is_delete = 0
                 left join biz_receipt_apply_item oai on oai.id = oi.pre_receipt_item_id and oi.is_delete = 0
        where ih.is_delete = 0
          and ih.receipt_type = 332
          and ih.receipt_status = 90
        <if test="po.matCode != null and po.matCode != ''" >
            AND dm.mat_code  = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0 ">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName != null and po.matName != ''" >
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.applyReceiptCode != null and po.applyReceiptCode != ''" >
            AND ah.receipt_code  = #{po.applyReceiptCode}
        </if>
        <if test="po.applyReceiptCodeList != null and po.applyReceiptCodeList.size() > 0 ">
            AND ah.receipt_code in
            <foreach collection="po.applyReceiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''" >
            AND ih.receipt_code  = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0 ">
            AND ih.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.outputReceiptCode != null and po.outputReceiptCode != ''" >
            AND oh.receipt_code  = #{po.outputReceiptCode}
        </if>
        <if test="po.outputReceiptCodeList != null and po.outputReceiptCodeList.size() > 0 ">
            AND oh.receipt_code in
            <foreach collection="po.outputReceiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.estimateReturnOldType != null and po.estimateReturnOldType != ''" >
            AND oai.return_old_type = #{po.estimateReturnOldType}
        </if>
        <if test="po.qty != null and po.qty != ''" >
            AND ii.qty = #{po.qty}
        </if>
        <if test="po.returnOldType != null and po.returnOldType != ''" >
            AND ai.return_old_type = #{po.returnOldType}
        </if>
    </select>

    <select id="getTimeInput" resultType="com.inossem.wms.common.model.bizdomain.report.vo.TimeInputVO">
        SELECT
        t.receipt_code,
        t.create_time,
        t.submit_time,
        DATEDIFF(ifnull(t.submit_time, NOW()), t.create_time) date_diff
        FROM
        biz_receipt_input_head t
        WHERE
        t.receipt_type = 106
        <if test="po.receiptCode != null and po.receiptCode != ''" >
            AND t.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0 ">
            AND t.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.createTimeStart !=null and po.createTimeEnd != null ">
            AND DATE_FORMAT(t.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.submitTimeStart !=null and po.submitTimeEnd != null ">
            AND DATE_FORMAT(t.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.submitTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.submitTimeEnd},'%Y-%m-%d')
        </if>
        ORDER BY t.create_time desc
    </select>

    <select id="getTimeOutput" resultType="com.inossem.wms.common.model.bizdomain.report.vo.TimeOutputVO">
        SELECT
            t.receipt_code,
            i.posting_date,
            t.submit_time,
            DATEDIFF(ifnull(i.posting_date, NOW()), t.submit_time) date_diff
        FROM
            biz_receipt_output_head t,
            biz_receipt_output_item i
        WHERE
            t.id = i.head_id
            AND t.receipt_type = 108
        <if test="po.receiptCode != null and po.receiptCode != ''" >
            AND t.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0 ">
            AND t.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.postingDateStart !=null and po.postingDateEnd != null ">
            AND DATE_FORMAT(i.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.postingDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.postingDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.submitTimeStart !=null and po.submitTimeEnd != null ">
            AND DATE_FORMAT(t.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.submitTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.submitTimeEnd},'%Y-%m-%d')
        </if>
        GROUP BY t.receipt_code
        ORDER BY t.create_time desc
    </select>

    <select id="getTimeUnload" resultType="com.inossem.wms.common.model.bizdomain.report.vo.TimeUnloadVO">
        SELECT
            h.receipt_code,
            max(i.modify_time) finish_time,
            t.submit_time,
            DATEDIFF(IF(i.item_status = 90, max(i.modify_time), NOW()), t.submit_time) date_diff
        FROM
            biz_receipt_output_head t,
            biz_receipt_output_item ti,
            biz_receipt_task_req_item i,
            biz_receipt_task_req_head h
        WHERE
            t.id = i.pre_receipt_head_id
            AND t.id = ti.head_id
            AND i.head_id = h.id
            AND t.receipt_type = 108
        <if test="po.outReceiptCode != null and po.outReceiptCode != ''" >
            AND t.receipt_code = #{po.outReceiptCode}
        </if>
        <if test="po.outReceiptCodeList != null and po.outReceiptCodeList.size() > 0 ">
            AND t.receipt_code in
            <foreach collection="po.outReceiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''" >
            AND h.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0 ">
            AND h.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.submitTimeStart !=null and po.submitTimeEnd != null ">
            AND DATE_FORMAT(t.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.submitTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.submitTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.postingDateStart !=null and po.postingDateEnd != null ">
            AND DATE_FORMAT(ti.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.postingDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.postingDateEnd},'%Y-%m-%d')
        </if>
        GROUP BY h.receipt_code
        <if test="po.finishTimeStart !=null and po.finishTimeEnd != null ">
            HAVING DATE_FORMAT(max(i.modify_time),'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.finishTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.finishTimeEnd},'%Y-%m-%d')
        </if>
        ORDER BY t.create_time desc
    </select>

    <select id="getTimeDirect" resultType="com.inossem.wms.common.model.bizdomain.report.vo.TimeDirectVO">
        SELECT
            m.mat_code,
            b.batch_code,
            b.extend29,
            b.extend20,
            b.extend2,
            b.functional_location_code,
            h.receipt_code,
            h.modify_time finish_time,
            b.input_date,
            max(oi.posting_date) posting_date,
            DATEDIFF(IF(sb.id is null, max(oi.posting_date), NOW()), b.input_date) date_diff
        FROM
            biz_receipt_delivery_notice_head h
            JOIN biz_receipt_waybill w ON w.delivery_notice_head_id = h.id
            JOIN biz_receipt_input_waybill iw ON iw.bill_id = w.id
            JOIN biz_receipt_input_item i ON iw.item_id = i.id
            JOIN biz_batch_info b ON b.id = iw.batch_id
            JOIN dic_material m ON m.id = b.mat_id
            LEFT JOIN stock_batch sb ON sb.batch_id = b.id
            LEFT JOIN biz_receipt_output_bin ob ON ob.batch_id = b.id
            LEFT JOIN biz_receipt_output_item oi ON oi.head_id = ob.head_id
        WHERE
            h.is_direct_scene = 1
            AND h.receipt_type = 101
        <if test="po.matCode != null and po.matCode != ''" >
            AND m.mat_code = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0 ">
            AND m.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.batchCode != null and po.batchCode != ''" >
            AND b.batch_code = #{po.batchCode}
        </if>
        <if test="po.batchCodeList != null and po.batchCodeList.size() > 0 ">
            AND b.batch_code in
            <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend29 != null and po.extend29 != ''" >
            AND b.extend29 = #{po.extend29}
        </if>
        <if test="po.extend29List != null and po.extend29List.size() > 0 ">
            AND b.extend29 in
            <foreach collection="po.extend29List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend20 != null and po.extend20 != ''" >
            AND b.extend20 = #{po.extend20}
        </if>
        <if test="po.extend20List != null and po.extend20List.size() > 0 ">
            AND b.extend20 in
            <foreach collection="po.extend20List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend2 != null and po.extend2 != ''" >
            AND b.extend2 = #{po.extend2}
        </if>
        <if test="po.extend2List != null and po.extend2List.size() > 0 ">
            AND b.extend2 in
            <foreach collection="po.extend2List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.functionalLocationCode != null and po.functionalLocationCode != ''" >
            AND b.functional_location_code = #{po.functionalLocationCode}
        </if>
        <if test="po.functionalLocationCodeList != null and po.functionalLocationCodeList.size() > 0 ">
            AND b.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''" >
            AND h.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0 ">
            AND h.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.finishTimeStart !=null and po.finishTimeEnd != null ">
            AND DATE_FORMAT(h.modify_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.finishTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.finishTimeEnd},'%Y-%m-%d')
        </if>
        GROUP BY b.id
        ORDER BY h.create_time desc
    </select>

    <select id="getOccupy" resultType="com.inossem.wms.common.model.bizdomain.report.vo.OccupyVO">
        SELECT
            sb.id,
            m.mat_code,
            m.mat_name,
            f.fty_code,
            f.fty_name,
            l.location_code,
            l.location_name,
            dlm.location_code main_location_code,
            dlm.location_name main_location_name,
            w.wh_code,
            w.wh_name,
            sb.qty,
            o.id occupy_id,
            o.qty occupy_qty,
            b.price,
            sb.qty * b.price money,
            pm.mat_code parent_mat_code,
            pm.mat_name parent_mat_name,
            b.batch_code,
            b.spec_stock_code,
            b.spec_stock_name,
            b.extend20,
            b.extend29,
            b.input_date,
            b.production_date,
            b.shelf_line,
            b.validity_date
        FROM
            stock_occupy o
            JOIN stock_batch sb ON o.stock_batch_id = sb.id
            JOIN biz_batch_info b ON sb.batch_id = b.id
            JOIN dic_material m ON sb.mat_id = m.id
            JOIN dic_material pm ON m.parent_mat_id = pm.id
            JOIN dic_factory f ON sb.fty_id = f.id
            JOIN dic_stock_location l ON sb.location_id = l.id
            JOIN dic_wh w ON l.wh_id = w.id
            LEFT JOIN biz_receipt_waybill waybill ON waybill.mat_id = sb.mat_id
            LEFT JOIN biz_receipt_delivery_notice_item brdni ON brdni.id = waybill.delivery_notice_item_id
            LEFT JOIN dic_stock_location dlm ON brdni.location_id = dlm.id
        WHERE
            f.fty_code = 'J047'
            AND b.validity_date <![CDATA[< ]]> NOW()
        <if test="po.ftyCode != null and po.ftyCode != ''" >
            AND f.fty_code = #{po.ftyCode}
        </if>
        <if test="po.locationCode != null and po.locationCode != ''" >
            AND l.location_code = #{po.locationCode}
        </if>
        <if test="po.whCode != null and po.whCode != ''" >
            AND w.wh_code = #{po.whCode}
        </if>
        <if test="po.inputDateStart !=null and po.inputDateEnd != null ">
            AND DATE_FORMAT(b.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.matName != null and po.matName != ''" >
            AND m.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.matCode != null and po.matCode != ''" >
            AND m.mat_code = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0 ">
            AND m.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.batchCode != null and po.batchCode != ''" >
            AND b.batch_code = #{po.batchCode}
        </if>
        <if test="po.batchCodeList != null and po.batchCodeList.size() > 0 ">
            AND b.batch_code in
            <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY sb.id
    </select>

    <select id="getOccupyDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.OccupyVO">
        SELECT
            ah.receipt_code,
            o.qty occupy_qty
        FROM
            stock_occupy o,
            biz_receipt_apply_head ah
        WHERE
            o.receipt_head_id = ah.id
        <if test="po.id != null" >
            AND o.stock_batch_id = #{po.id}
        </if>
    </select>

    <select id="getOver" resultType="com.inossem.wms.common.model.bizdomain.report.vo.OverVO">
        SELECT
            m.id,
            m.mat_code,
            m.mat_name,
            sum(if(o.debit_credit = 'S' , o.qty, 0)) over_qty,
            sum(if(o.debit_credit = 'H' , o.qty, 0)) subtract_qty,
            sum(if(o.debit_credit = 'L' , o.qty, 0)) lock_subtract_qty,
            sum(if(o.debit_credit = 'S' , o.qty, 0)) - sum(if(o.debit_credit = 'H' , o.qty, 0)) can_subtract_qty
        FROM
            stock_over o,
            dic_material m
        WHERE
            o.mat_id = m.id
        <if test="po.matCode != null and po.matCode != ''" >
            AND m.mat_code = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0 ">
            AND m.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY m.id
    </select>

    <select id="getOverDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.OverDetailVO">
        SELECT
            m.mat_code,
            m.mat_name,
            pm.mat_code parent_mat_code,
            b.extend29,
            ph.receipt_code paper_code,
            ph.island,
            ph.paper_system,
            ph.area,
            ph.factory_building,
            u.unit_name,
            ah.receipt_code,
            oh.receipt_code out_code,
            ah.used_dept_name,
            ab.qty,
            ab.task_qty,
            ab.task_qty - ab.qty over_qty
        FROM
            stock_over o
            JOIN dic_material m ON o.mat_id = m.id
            JOIN dic_unit u ON u.id = m.unit_id
            JOIN dic_material pm ON m.parent_mat_id = pm.id
            JOIN biz_batch_info b ON o.batch_id = b.id
            LEFT JOIN biz_receipt_apply_head ah ON ah.id = o.receipt_head_id
            LEFT JOIN biz_receipt_apply_item ai ON ai.head_id = ah.id
            LEFT JOIN biz_receipt_paper_head ph ON ph.id = ai.refer_receipt_head_id
            LEFT JOIN biz_receipt_apply_bin ab ON ab.id = o.receipt_bin_id
            LEFT JOIN biz_receipt_output_bin ob ON ob.apply_bin_id = o.receipt_bin_id
            LEFT JOIN biz_receipt_output_head oh ON oh.id = ob.head_id
        WHERE
            o.debit_credit = 'S'
        <if test="po.id != null" >
            AND m.id = #{po.id}
        </if>
    </select>

    <select id="getSubtractDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.SubtractDetailVO">
        SELECT
            m.mat_code,
            m.mat_name,
            pm.mat_code parent_mat_code,
            b.extend29,
            ph.receipt_code paper_code,
            ph.island,
            ph.paper_system,
            ph.area,
            ph.factory_building,
            u.unit_name,
            ah.receipt_code,
            oh.receipt_code out_code,
            ah.used_dept_name,
            ab.subtract_qty + ab.qty qty,
            ab.task_qty,
            ab.subtract_qty
        FROM
            stock_over o
            JOIN dic_material m ON o.mat_id = m.id
            JOIN dic_unit u ON u.id = m.unit_id
            JOIN dic_material pm ON m.parent_mat_id = pm.id
            LEFT JOIN biz_batch_info b ON o.batch_id = b.id
            LEFT JOIN biz_receipt_apply_head ah ON ah.id = o.receipt_head_id
            LEFT JOIN biz_receipt_apply_item ai ON ai.head_id = ah.id
            LEFT JOIN biz_receipt_paper_head ph ON ph.id = ai.refer_receipt_head_id
            LEFT JOIN biz_receipt_apply_bin ab ON ab.id = o.receipt_bin_id
            LEFT JOIN biz_receipt_output_bin ob ON ob.apply_bin_id = o.receipt_bin_id
            LEFT JOIN biz_receipt_output_head oh ON oh.id = ob.head_id
        WHERE
            o.debit_credit = 'H'
        <if test="po.id != null" >
            AND m.id = #{po.id}
        </if>
    </select>

    <select id="getInspectCaseRate" resultType="java.util.Map">
        SELECT
            ROUND(sum(CASE WHEN case_status='已开箱' and is_safe = 2 and case_cycle <![CDATA[ < ]]> 15 THEN 1 ELSE 0 END) / sum(1) * 100, 2) domestic15,
            ROUND(sum(CASE WHEN case_status='已开箱' and is_safe = 2 and case_cycle <![CDATA[ < ]]> 30 THEN 1 ELSE 0 END) / sum(1) * 100, 2) domestic30,
            ROUND(sum(CASE WHEN case_status='已开箱' and is_safe = 2 and case_cycle <![CDATA[ < ]]> 45 THEN 1 ELSE 0 END) / sum(1) * 100, 2) domestic45,
            ROUND(sum(CASE WHEN case_status='已开箱' and is_safe = 1 and case_cycle <![CDATA[ < ]]> 15 THEN 1 ELSE 0 END) / sum(1) * 100, 2) import15,
            ROUND(sum(CASE WHEN case_status='已开箱' and is_safe = 1 and case_cycle <![CDATA[ < ]]> 30 THEN 1 ELSE 0 END) / sum(1) * 100, 2) import30,
            ROUND(sum(CASE WHEN case_status='已开箱' and is_safe = 1 and case_cycle <![CDATA[ < ]]> 45 THEN 1 ELSE 0 END) / sum(1) * 100, 2) import45
        FROM (
        SELECT
            w.case_code,
            w.package_type,
            nh.is_safe,
            w.extend70,
            w.extend71,
            w.extend72,
            w.case_weight,
            rh.receipt_code,
            rh.submit_time,
            CASE WHEN count(ri.id) = count(ii.id) THEN max(ih.submit_time) ELSE '' END finish_time,
            CASE WHEN count(ii.id) = 0 THEN '未开箱' WHEN count(ri.id) > count(ii.id) THEN '部分开箱' WHEN count(ri.id) = count(ii.id) THEN '已开箱' ELSE '' END case_status,
            DATEDIFF(CASE WHEN count(ri.id) = count(ii.id) THEN max(ih.submit_time) ELSE NOW() END, rh.submit_time) case_cycle
        FROM
            biz_receipt_delivery_notice_head nh
            JOIN biz_receipt_delivery_notice_item ni ON ni.head_id = nh.id AND nh.receipt_type = 101
            JOIN biz_receipt_register_item ri ON ri.pre_receipt_item_id = ni.id
            JOIN biz_receipt_register_head rh ON ri.head_id = rh.id AND rh.receipt_status = 90
            JOIN biz_receipt_waybill w ON w.delivery_notice_item_id = ni.id
            LEFT JOIN biz_receipt_inspect_item ii ON ii.id = w.sign_inspect_item_id AND ii.item_status = 90
            LEFT JOIN biz_receipt_inspect_head ih ON ih.id = ii.head_id
        GROUP BY w.case_code, rh.receipt_code
        ORDER BY nh.create_time DESC
        ) t
    </select>

    <!--成套设备箱件维度 需要同步修改上述sql 括号内为相同的sql -->
    <select id="getInspectCase" resultType="com.inossem.wms.common.model.bizdomain.report.vo.InspectCaseVO">
    SELECT * FROM (
        SELECT
            w.case_code,
            w.package_form,
            nh.is_safe,
            w.extend70,
            w.extend71,
            w.extend72,
            w.case_weight,
            rh.receipt_code,
            rh.submit_time,
            CASE WHEN count(ri.id) = count(ii.id) THEN max(ih.submit_time) ELSE null END finish_time,
            CASE WHEN count(ii.id) = 0 THEN '未开箱' WHEN count(ri.id) > count(ii.id) THEN '部分开箱' WHEN count(ri.id) = count(ii.id) THEN '已开箱' ELSE '' END case_status,
            DATEDIFF(CASE WHEN count(ri.id) = count(ii.id) THEN max(ih.submit_time) ELSE NOW() END, rh.submit_time) case_cycle
        FROM
            biz_receipt_delivery_notice_head nh
            JOIN biz_receipt_delivery_notice_item ni ON ni.head_id = nh.id AND nh.receipt_type = 101
            JOIN biz_receipt_register_item ri ON ri.pre_receipt_item_id = ni.id
            JOIN biz_receipt_register_head rh ON ri.head_id = rh.id AND rh.receipt_status = 90
            JOIN biz_receipt_waybill w ON w.delivery_notice_item_id = ni.id
            LEFT JOIN biz_receipt_inspect_item ii ON ii.id = w.sign_inspect_item_id AND ii.item_status = 90
            LEFT JOIN biz_receipt_inspect_head ih ON ih.id = ii.head_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND ni.fty_id = #{po.ftyId}
        </if>
        GROUP BY w.case_code, rh.receipt_code
        ORDER BY nh.create_time DESC
    ) t
        WHERE 1=1
        <if test="po.caseCycle != null and po.caseCycle != ''" >
            AND t.case_cycle = #{po.caseCycle}
        </if>
        <if test="po.isSafe != null" >
            AND t.is_safe = #{po.isSafe}
        </if>
        <if test="po.caseStatus != null and po.caseStatus != ''" >
            <if test="po.caseStatus == '1'" >
                AND t.case_status = '未开箱'
            </if>
            <if test="po.caseStatus == '2'" >
                AND t.case_status = '部分开箱'
            </if>
            <if test="po.caseStatus == '3'" >
                AND t.case_status = '已开箱'
            </if>
        </if>
        <if test="po.submitTimeStart !=null and po.submitTimeEnd != null ">
            AND DATE_FORMAT(t.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.submitTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.submitTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.finishTimeStart !=null and po.finishTimeEnd != null ">
            AND DATE_FORMAT(t.finish_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.finishTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.finishTimeEnd},'%Y-%m-%d')
        </if>
    </select>

    <select id="getInspectMat" resultType="com.inossem.wms.common.model.bizdomain.report.vo.InspectMatVO">
    SELECT * FROM (
        SELECT
            m.mat_code,
            w.mat_name,
            u.unit_name,
            nh.is_safe,
            rh.receipt_code,
            rh.submit_time,
            w.arrival_qty,
            CASE WHEN count(ri.id) = count(ii.id) THEN w.arrival_qty ELSE 0 END inspect_qty,
            CASE WHEN count(ri.id) = count(ii.id) THEN 0 ELSE w.arrival_qty END un_inspect_qty,
            CASE WHEN count(ri.id) = count(ii.id) THEN max(ih.submit_time) ELSE null END finish_time,
            CASE WHEN count(ii.id) = 0 THEN '未质检' WHEN count(ri.id) > count(ii.id) THEN '部分质检' WHEN count(ri.id) = count(ii.id) THEN '已质检' ELSE '' END mat_status,
            DATEDIFF(CASE WHEN count(ri.id) = count(ii.id) THEN max(ih.submit_time) ELSE NOW() END, rh.submit_time) case_cycle,
            ifnull(nh.procurement_method, nh.purchase_type) procurement_method
        FROM
            biz_receipt_delivery_notice_head nh
        JOIN biz_receipt_delivery_notice_item ni ON ni.head_id = nh.id AND (nh.receipt_type = 101 or nh.purchase_type = 2)
        JOIN biz_receipt_register_item ri ON ri.pre_receipt_item_id = ni.id
        JOIN biz_receipt_register_head rh ON ri.head_id = rh.id AND rh.receipt_status = 90
        JOIN biz_receipt_waybill w ON w.delivery_notice_item_id = ni.id
        JOIN dic_material m ON w.mat_id = m.id
        JOIN dic_unit u ON u.id = m.unit_id
        LEFT JOIN biz_receipt_inspect_item ii ON ii.id = w.sign_inspect_item_id AND ii.item_status = 90
        LEFT JOIN biz_receipt_inspect_head ih ON ih.id = ii.head_id
        WHERE 1=1
        <if test="po.ftyId != null" >
            AND ni.fty_id = #{po.ftyId}
        </if>
        GROUP BY m.mat_code, rh.receipt_code
        ORDER BY nh.create_time DESC
    ) t
        WHERE 1=1
        <if test="po.procurementMethod != null" >
            AND t.procurement_method = #{po.procurementMethod}
        </if>
        <if test="po.caseCycle != null and po.caseCycle != ''" >
            AND t.case_cycle = #{po.caseCycle}
        </if>
        <if test="po.isSafe != null" >
            AND t.is_safe = #{po.isSafe}
        </if>
        <if test="po.caseStatus != null and po.caseStatus != ''" >
            <if test="po.caseStatus == '1'" >
                AND t.mat_status = '未质检'
            </if>
            <if test="po.caseStatus == '2'" >
                AND t.mat_status = '部分质检'
            </if>
            <if test="po.caseStatus == '3'" >
                AND t.mat_status = '已质检'
            </if>
        </if>
        <if test="po.submitTimeStart !=null and po.submitTimeEnd != null ">
            AND DATE_FORMAT(t.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.submitTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.submitTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.finishTimeStart !=null and po.finishTimeEnd != null ">
            AND DATE_FORMAT(t.finish_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.finishTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.finishTimeEnd},'%Y-%m-%d')
        </if>
    </select>

    <select id="getNcr" resultType="com.inossem.wms.common.model.bizdomain.report.vo.NcrVO">
        SELECT t.*, m.mat_code, u.unit_name
        FROM
            biz_receipt_waybill t,
            dic_material m,
            dic_unit u,
            dic_material pm
        WHERE
            ifnull(t.ncrbh, '') != ''
            AND t.mat_id = m.id
            AND m.unit_id = u.id
            AND m.parent_mat_id = pm.id
        <if test="po.ftyId != null" >
            AND t.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND t.location_id = #{po.locationId}
        </if>
        <if test="po.matCode != null and po.matCode != ''" >
            AND m.mat_code = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0 ">
            AND m.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matName != null and po.matName != ''" >
            AND t.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.parentMatCode != null and po.parentMatCode != ''" >
            AND pm.mat_code = #{po.parentMatCode}
        </if>
        <if test="po.parentMatCodeList != null and po.parentMatCodeList.size() > 0 ">
            AND pm.mat_code in
            <foreach collection="po.parentMatCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend20 != null and po.extend20 != ''" >
            AND t.extend20 = #{po.extend20}
        </if>
        <if test="po.extend20List != null and po.extend20List.size() > 0 ">
            AND t.extend20 in
            <foreach collection="po.extend20List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.functionalLocationCode != null and po.functionalLocationCode != ''" >
            AND t.functional_location_code = #{po.functionalLocationCode}
        </if>
        <if test="po.functionalLocationCodeList != null and po.functionalLocationCodeList.size() > 0 ">
            AND t.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend29 != null and po.extend29 != ''" >
            AND t.extend29 = #{po.extend29}
        </if>
        <if test="po.extend29List != null and po.extend29List.size() > 0 ">
            AND t.extend29 in
            <foreach collection="po.extend29List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.ncrbh != null and po.ncrbh != ''" >
            AND t.ncrbh = #{po.ncrbh}
        </if>
        <if test="po.ncrbhList != null and po.ncrbhList.size() > 0 ">
            AND t.ncrbh in
            <foreach collection="po.ncrbhList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <!--成套设备直抵现场报表查询 参考 selectDirectSceneRecord -->
    <select id="getInputDirect" resultType="com.inossem.wms.common.model.bizdomain.report.vo.DirectSceneVo">
        SELECT
            df.id AS fty_id,
            df.fty_code,
            df.fty_name,
            dl.id AS location_id,
            dl.location_code,
            dl.location_name,
            dm.mat_code,
            dm.mat_name,
            du.unit_code,
            du.unit_name,
            cgdH.receipt_code AS purchase_receipt_code,
            cgdI.rid AS purchase_receipt_rid,
            bi.spec_stock_code,
            bi.spec_stock_name,
            rkI.qty,
            bi.batch_code,
            <!-- 收获金额 -->
            rkI.dmbtr,
            bi.input_date,
            appH.receipt_code as apply_code,
            outH.create_time as approve_date,
            su.user_code AS user_code,
            su.user_name AS user_name,
            outH.receipt_code as output_code,
            outH.receipt_status as receipt_status,
            outI.rid as output_rid,
            outI.mat_doc_code as output_mat_doc_code,
            outI.doc_date as output_doc_date,
        ph.receipt_code paper_code,
        ph.island,
            ri.paper_system,
            ri.area,
            ri.factory_building,
            shtzH.*
        FROM
            biz_receipt_input_head rkH
            INNER JOIN biz_receipt_input_item rkI ON rkI.head_id = rkH.id
            INNER JOIN dic_material dm ON rkI.mat_id = dm.id
            INNER JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
            INNER JOIN dic_unit du ON dm.unit_id = du.id
            INNER JOIN dic_factory df ON rkI.fty_id = df.id
            INNER JOIN dic_stock_location dl ON rkI.location_id = dl.id
            INNER JOIN dic_wh dw ON dl.wh_id = dw.id
            INNER JOIN biz_receipt_input_waybill rkW on rkI.id = rkW.item_id
            INNER JOIN biz_batch_info bi ON rkW.batch_id = bi.id
            INNER JOIN erp_purchase_receipt_head cgdH ON cgdH.id = bi.purchase_receipt_head_id
            INNER JOIN biz_receipt_delivery_notice_head shtzH ON shtzH.id = rkI.delivery_notice_head_id
            LEFT JOIN erp_purchase_receipt_item cgdI ON cgdI.id = bi.purchase_receipt_item_id AND cgdI.delete_tag = 0
            LEFT JOIN biz_receipt_output_bin outB ON outB.batch_id = bi.id
            LEFT JOIN biz_receipt_output_item outI ON outI.head_id = outB.head_id
            LEFT JOIN biz_receipt_output_head outH ON outH.id = outI.head_id and outB.head_id = outH.id AND outH.receipt_type = 108
        LEFT JOIN biz_receipt_apply_bin appB ON appB.id = outB.apply_bin_id
        LEFT JOIN biz_receipt_apply_head appH ON appH.id = appB.head_id
        LEFT JOIN biz_receipt_apply_item appI ON appI.head_id = appH.id
        LEFT JOIN biz_receipt_require_item ri ON ri.id = appI.pre_receipt_item_id
        left join biz_receipt_paper_head ph on ph.id = ri.pre_receipt_head_id
            LEFT JOIN sys_user su ON appI.modify_user_id = su.id
        WHERE
            rkH.receipt_type = 106
            AND rkH.receipt_status IN (90)
            AND shtzH.is_direct_scene = 1
        <if test="po.purchaseReceiptCode !=null and po.purchaseReceiptCode != ''" >
            AND cgdH.receipt_code = #{po.purchaseReceiptCode}
        </if>
        <if test="po.ftyId !=null and po.ftyId != ''" >
            AND rkH.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId !=null and po.locationId != ''" >
            AND rkH.location_id = #{po.locationId}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND rkH.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.inputStartTime !=null and po.inputEndTime!=null">
            AND DATE_FORMAT(bi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputStartTime},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputEndTime},'%Y-%m-%d')
        </if>
        <if test="po.startTime !=null and po.endTime!=null">
            AND DATE_FORMAT(outI.doc_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.startTime},'%Y-%m-%d') AND DATE_FORMAT(#{po.endTime},'%Y-%m-%d')
        </if>
        ORDER BY bi.input_date desc
    </select>

    <select id="getMatDocDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.MatDocVO">
        select t.*, dm.mat_code, dm.mat_name, case when dm.is_ct_code = 1 then dm.parent_mat_id else NULL end parent_mat_id
        from (select sidb.debit_credit,
                     sidb.mat_id,
                     sidb.unit_id,
                     sidb.batch_id,
                     bbi.batch_code,
                     sidb.fty_id,
                     sidb.location_id,
                     bbi.spec_stock,
                     bbi.spec_stock_code,
                     bbi.spec_stock_name,
                     sidb.pre_receipt_type                                                            receipt_type,
                     case
                         when brii.mat_doc_code != '' then brii.mat_doc_code
                         when broi.mat_doc_code != '' then broi.mat_doc_code
                         when brri.mat_doc_code != '' then brri.mat_doc_code
                         when brti.mat_doc_code != '' then brti.mat_doc_code
                         else sidb.mat_doc_code
                         end                                                                          mat_doc_code,
                     case
                         when brii.mat_doc_rid != '' then brii.mat_doc_rid
                         when broi.mat_doc_rid != '' then broi.mat_doc_rid
                         when brri.mat_doc_rid != '' then brri.mat_doc_rid
                         when brti.mat_doc_rid != '' then brti.mat_doc_rid
                         else sidb.mat_doc_rid
                         end                                                                          mat_doc_rid,
                     sidb.move_qty                                                                    qty,
                     case
                         when brih.submit_time is not null then brih.submit_time
                         else sidb.create_time
                         end                                                                          submit_date,
                     case
                         when brih.submit_time is not null then brih.submit_time
                         else sidb.create_time
                         end                                                                          submit_time,
                     case
                         when brii.dmbtr is not null then brii.dmbtr
                         when briwoi.dmbtr is not null then briwoi.dmbtr
                         else bbi.price
                         end                                                                          dmbtr,
                     case when sidb.pre_receipt_type = 106 then bbi.purchase_receipt_code else '' end purchase_receipt_code,
                     case when sidb.pre_receipt_type = 106 then bbi.purchase_receipt_rid else '' end  purchase_receipt_rid,
                     sidb.doc_date,
                     sidb.posting_date,
                     errh.receipt_code                                                                refer_receipt_code,
                     broh.work_order,
                     sidb.modify_user_id
              from stock_ins_doc_batch sidb
                       join biz_batch_info bbi on bbi.id = sidb.batch_id
                       left join biz_receipt_input_head brih on brih.id = sidb.pre_receipt_head_id and brih.is_delete = 0
                       left join biz_receipt_input_item brii on brii.id = sidb.pre_receipt_item_id and brii.is_delete = 0
                       left join biz_receipt_input_write_off_item briwoi on briwoi.id = sidb.pre_receipt_item_id and briwoi.is_delete = 0
                       left join biz_receipt_output_head broh on broh.id = sidb.pre_receipt_head_id and broh.is_delete = 0
                       left join biz_receipt_output_item broi on broi.id = sidb.pre_receipt_item_id and broi.is_delete = 0
                       left join erp_reserve_receipt_head errh on errh.id = broi.refer_receipt_head_id
                       left join biz_receipt_return_item brri on brri.id = sidb.pre_receipt_item_id and brri.is_delete = 0
                       left join biz_receipt_transport_item brti on brti.id = sidb.pre_receipt_item_id and brti.is_delete = 0
              where brii.mat_doc_code != ''
                    or broi.mat_doc_code != ''
                    or brri.mat_doc_code != ''
                    or brti.mat_doc_code != ''
                    or sidb.mat_doc_code != ''

              union all

              select ''               debit_credit,
                     brii.mat_id,
                     brii.unit_id,
                     brii.batch_id,
                     bbi.batch_code,
                     brii.fty_id,
                     brii.location_id,
                     bbi.spec_stock,
                     bbi.spec_stock_code,
                     bbi.spec_stock_name,
                     brih.receipt_type,
                     brii.mat_doc_code,
                     brii.mat_doc_rid,
                     brii.qty,
                     brih.submit_time submit_date,
                     brih.submit_time,
                     brii.dmbtr,
                     ''               purchase_receipt_code,
                     ''               purchase_receipt_rid,
                     brii.doc_date,
                     brii.posting_date,
                     ''               refer_receipt_code,
                     ''               work_order,
                     brih.submit_user_id
              from biz_receipt_inconformity_item brii
                       join biz_receipt_inconformity_head brih on brih.id = brii.head_id and brii.mat_doc_code != '' and brih.is_delete = 0
                       join biz_batch_info bbi on bbi.id = brii.batch_id
              where brii.is_delete = 0

              union all

              select ''               debit_credit,
                     brii.mat_id,
                     brii.unit_id,
                     brii.batch_id,
                     bbi.batch_code,
                     brii.fty_id,
                     brii.location_id,
                     bbi.spec_stock,
                     bbi.spec_stock_code,
                     bbi.spec_stock_name,
                     brih.receipt_type,
                     brii.mat_doc_code,
                     brii.mat_doc_rid,
                     brii.qty,
                     brih.submit_time submit_date,
                     brih.submit_time,
                     null             dmbtr,
                     ''               purchase_receipt_code,
                     ''               purchase_receipt_rid,
                     brii.doc_date,
                     brii.posting_date,
                     ''               refer_receipt_code,
                     ''               work_order,
                     brih.modify_user_id
              from biz_receipt_inspect_item brii
                       join biz_receipt_inspect_head brih on brih.id = brii.head_id and brii.mat_doc_code != '' and brih.is_delete = 0
                       join biz_batch_info bbi on bbi.id = brii.batch_id
              where brii.is_delete = 0

              union all

              select ''               debit_credit,
                     brri.mat_id,
                     brri.unit_id,
                     brri.batch_id,
                     bbi.batch_code,
                     brri.fty_id,
                     brri.location_id,
                     bbi.spec_stock,
                     bbi.spec_stock_code,
                     bbi.spec_stock_name,
                     brrh.receipt_type,
                     brri.mat_doc_code,
                     brri.mat_doc_rid,
                     brri.qty,
                     brrh.submit_time submit_date,
                     brrh.submit_time,
                     brri.dmbtr,
                     ''               purchase_receipt_code,
                     ''               purchase_receipt_rid,
                     brri.doc_date,
                     brri.posting_date,
                     ''               refer_receipt_code,
                     ''               work_order,
                     brrh.modify_user_id
              from biz_receipt_register_item brri
                       join biz_receipt_register_head brrh on brrh.id = brri.head_id and brri.mat_doc_code != '' and brrh.is_delete = 0
                       join biz_batch_info bbi on bbi.id = brri.batch_id
              where brri.is_delete = 0
             ) t
        join dic_material dm on dm.id = t.mat_id
        <where>
            <if test="po.matCode != null and po.matCode != ''">
                AND dm.mat_code = #{po.matCode}
            </if>
            <if test="po.matCodeList != null and po.matCodeList.size() > 0">
                AND dm.mat_code in
                <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.ftyId != null" >
                AND t.fty_id = #{po.ftyId}
            </if>
            <if test="po.locationId != null" >
                AND t.location_id = #{po.locationId}
            </if>
            <if test="po.matDocCode != null and po.matDocCode != ''">
                AND t.mat_doc_code = #{po.matDocCode}
            </if>
            <if test="po.matDocCodeList != null and po.matDocCodeList.size() > 0">
                AND t.mat_doc_code in
                <foreach collection="po.matDocCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.batchCode !=null and po.batchCode != ''">
                AND t.batch_code = #{po.batchCode}
            </if>
            <if test="po.batchCodeList != null and po.batchCodeList.size() > 0">
                AND t.batch_code in
                <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.docDateStartTime != null and po.docDateEndTime != null ">
                AND DATE_FORMAT(t.doc_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.docDateStartTime},'%Y-%m-%d') AND DATE_FORMAT(#{po.docDateEndTime},'%Y-%m-%d')
            </if>
            <if test="po.postingDateStartTime != null and po.postingDateStartTime != null ">
                AND DATE_FORMAT(t.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.postingDateStartTime},'%Y-%m-%d') AND DATE_FORMAT(#{po.postingDateStartTime},'%Y-%m-%d')
            </if>
            <if test="po.specStock != null">
                AND t.spec_stock = #{po.specStock}
            </if>
            <if test="po.receiptType != null">
                AND t.receipt_type = #{po.receiptType}
            </if>
        </where>
    </select>

    <select id="getArrivalTrackCaseDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.ArrivalTrackVO">
        select t.*
        from (
              select brri.fty_id,
                     brrh.receipt_code,
                     brrh.create_time,
                     case when brrh.receipt_status != 10 then brrh.submit_time else NULL end submit_time,
                     case
                         when brrh.receipt_status = 10 then DATEDIFF(#{po.now}, brrh.create_time)
                         else DATEDIFF(brrh.submit_time, brrh.create_time)
                     end                                                                   delay_days,
                     brrh.is_safe,
                     case when brri.fty_id = 5 then 2 else NULL end                        procurement_method,
                     case when brrh.receipt_status = 10 then '未完成' else '已完成' end      receipt_status,
                     cr.case_code,
                     cr.package_type,
                     cr.case_size,
                     NULL                                                                  extend70,
                     NULL                                                                  extend71,
                     NULL                                                                  extend72,
                     cr.case_weight,
                     cr.visual_check,
                     NULL                                                                  unitized_visual_check,
                     NULL                                                                  waybill_remark
              from biz_receipt_delivery_notice_head brdnh
                       join biz_receipt_register_item brri on brri.pre_receipt_head_id = brdnh.id and brri.is_delete = 0
                       join biz_receipt_register_head brrh on brrh.id = brri.head_id and brrh.is_delete = 0
                       join biz_receipt_delivery_notice_case_rel cr on cr.receipt_head_id = brdnh.id and cr.is_delete = 0
              where brdnh.receipt_type = 220
                and brdnh.is_delete = 0
                and brrh.receipt_status in (10, 70, 90)
              group by cr.id

              union all

              select brw.fty_id,
                     brrh.receipt_code,
                     brrh.create_time,
                     case when brrh.receipt_status != 10 then brrh.submit_time else NULL end submit_time,
                     case
                         when brrh.receipt_status = 10 then DATEDIFF(#{po.now}, brrh.create_time)
                         else DATEDIFF(brrh.submit_time, brrh.create_time)
                     end                                                                   delay_days,
                     brrh.is_safe,
                     brdnh.procurement_method,
                     case when brrh.receipt_status = 10 then '未完成' else '已完成' end      receipt_status,
                     brw.case_code,
                     brw.package_form                                                      package_type,
                     brw.case_size,
                     brw.extend70,
                     brw.extend71,
                     brw.extend72,
                     brw.case_weight,
                     NULL                                                                  visual_check,
                     brw.unitized_visual_check,
                     brw.waybill_remark
              from biz_receipt_register_head brrh
                       join biz_receipt_waybill brw on brw.arrival_register_head_id = brrh.id and brw.is_delete = 0
                       join biz_receipt_delivery_notice_head brdnh on brdnh.id = brw.delivery_notice_head_id
              where brrh.receipt_type = 102
                and brrh.is_delete = 0
                and brrh.receipt_status in (10, 70, 90)
              group by brrh.id, brw.case_code
        ) t
        <where>
            <if test="po.ftyId != null" >
                AND t.fty_id = #{po.ftyId}
            </if>
            <if test="po.caseCode != null and po.caseCode != ''">
                AND t.case_code = #{po.caseCode}
            </if>
            <if test="po.caseCodeList != null and po.caseCodeList.size() > 0">
                AND t.case_code in
                <foreach collection="po.caseCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.receiptCode != null and po.receiptCode != ''">
                AND t.receipt_code = #{po.receiptCode}
            </if>
            <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0">
                AND t.receipt_code in
                <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.createTimeStart != null and po.createTimeEnd != null ">
                AND DATE_FORMAT(t.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
            </if>
            <if test="po.submitTimeStart != null and po.submitTimeEnd != null ">
                AND DATE_FORMAT(t.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.submitTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.submitTimeEnd},'%Y-%m-%d')
            </if>
            <if test="po.delayDays != null">
                AND t.delay_days = #{po.delayDays}
            </if>
            <if test="po.isSafe != null">
                AND t.is_safe = #{po.isSafe}
            </if>
            <if test="po.procurementMethod != null">
                AND t.procurement_method = #{po.procurementMethod}
            </if>
        </where>
    </select>

    <select id="getArrivalTrackReceiptDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.ArrivalTrackReceiptVO">
        select brri.fty_id,
               brrh.receipt_code,
               brrh.create_time,
               case when brrh.receipt_status != 10 then brrh.submit_time else NULL end submit_time,
               case
                   when brrh.receipt_status = 10 then DATEDIFF(#{po.now}, brrh.create_time)
                   else DATEDIFF(brrh.submit_time, brrh.create_time)
               end                                                                  delay_days,
               brrh.is_safe,
               case
                   when brdnh.receipt_type = brdnh.receipt_type then
                       case
                           when brri.fty_id = 5 then 2
                           else 0
                       end
                   else brdnh.procurement_method
               end                                                                   procurement_method,
               case when brrh.receipt_status = 10 then '未完成' else '已完成' end      receipt_status
        from biz_receipt_delivery_notice_head brdnh
                 join biz_receipt_register_item brri on brri.pre_receipt_head_id = brdnh.id and brri.is_delete = 0
                 join biz_receipt_register_head brrh on brrh.id = brri.head_id and brrh.is_delete = 0
        where brdnh.is_delete = 0
          and brdnh.receipt_type in (220, 102)
          and brrh.receipt_status in (10, 70, 90)
        <if test="po.ftyId != null" >
            AND t.fty_id = #{po.ftyId}
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brrh.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0">
            AND brrh.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.createTimeStart != null and po.createTimeEnd != null ">
            AND DATE_FORMAT(brrh.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.submitTimeStart != null and po.submitTimeEnd != null ">
            AND DATE_FORMAT(case when brrh.receipt_status != 10 then brrh.submit_time else NULL end,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.submitTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.submitTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.delayDays != null">
            AND case
                when brrh.receipt_status = 10 then DATEDIFF(#{po.now}, brrh.create_time)
                else DATEDIFF(brrh.submit_time, brrh.create_time)
            end = #{po.delayDays}
        </if>
        <if test="po.isSafe != null">
            AND brrh.is_safe = #{po.isSafe}
        </if>
        <if test="po.procurementMethod != null">
            AND case
                when brdnh.receipt_type = brdnh.receipt_type then
                    case
                        when brri.fty_id = 5 then 2
                    else 0
                end
                else brdnh.procurement_method
            end = #{po.procurementMethod}
        </if>
        group by brrh.id
    </select>
    <select id="getGvnInconformityDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.GvnInconformityVO">
        select t.*, dm.mat_code, dm.mat_name
        from (select item.mat_id,
                     NULL                                                               extend2,
                     NULL                                                               extend29,
                     NULL                                                               extend37,
                     item.unit_id,
                     brri.qty                                                           arrival_qty,
                     item.qty                                                           unqualified_qty,
                     NULL                                                               more_qty,
                     eprh.receipt_code                                                  erp_purchase_receipt_code,
                     epri.rid                                                           erp_purchase_receipt_rid,
                     brri.mat_doc_code,
                     brri.write_off_mat_doc_code,
                     item.inconformity_reason,
                     head.different_type,
                     item.solve_reason,
                     brih.receipt_code                                                  inspect_receipt_code,
                     head.receipt_code,
                     head.receipt_status,
                     item.create_time,
                     item.modify_time,
                     case when head.receipt_status = 10 then '未完成' else '已完成' end   maintain_status,
                     case
                         when head.receipt_status = 10 then DATEDIFF(#{po.now}, item.create_time)
                         else DATEDIFF(item.modify_time, item.create_time)
                         end                                                            maintain_days
              from biz_receipt_inconformity_head head
                       join biz_receipt_inconformity_item item on item.head_id = head.id and item.is_delete = 0
                       join biz_receipt_inspect_item brii on brii.id = item.sign_inspect_item_id
                       join biz_receipt_inspect_head brih on brih.id = item.sign_inspect_head_id
                       join biz_receipt_register_item brri on brri.id = brii.arrival_register_item_id
                       left join erp_purchase_receipt_head eprh on eprh.id = item.refer_receipt_head_id
                       left join erp_purchase_receipt_item epri on epri.id = item.refer_receipt_item_id
              where head.receipt_type = 315
                and head.is_delete = 0
                and head.different_type in (1, 5)

              union all

              select brw.mat_id,
                     brw.extend2,
                     brw.extend29,
                     brw.extend37,
                     brw.unit_id,
                     brw.arrival_qty,
                     brw.unqualified_qty,
                     brw.more_qty,
                     eprh.receipt_code                                                                                       erp_purchase_receipt_code,
                     epri.rid                                                                                                erp_purchase_receipt_rid,
                     case when brw.price = 0 then NULL else brri.mat_doc_code end                                            mat_doc_code,
                     case when brw.price = 0 then NULL else brri.write_off_mat_doc_code end                                  write_off_mat_doc_code,
                     case when head.different_type = 1 then brw.inconformity_reason else brw.inconformity_reason_quality end inconformity_reason,
                     head.different_type,
                     brw.solve_reason,
                     brih.receipt_code                                                                                       inspect_receipt_code,
                     head.receipt_code,
                     head.receipt_status,
                     item.create_time,
                     item.modify_time,
                     case when head.receipt_status = 10 then '未完成' else '已完成' end                                        maintain_status,
                     case
                         when head.receipt_status = 10 then DATEDIFF(#{po.now}, item.create_time)
                         else DATEDIFF(item.modify_time, item.create_time)
                         end                                                                                                 maintain_days
              from biz_receipt_inconformity_head head
                       join biz_receipt_inconformity_item item on item.head_id = head.id and item.is_delete = 0
                       join biz_receipt_waybill brw on (brw.more_number_inconformity_notice_head_id = item.pre_receipt_head_id and brw.more_number_inconformity_notice_item_rid = item.rid)
                  or (brw.number_inconformity_notice_item_id = item.pre_receipt_item_id and brw.number_inconformity_notice_item_rid = item.rid)
                       join biz_receipt_register_item brri on brri.id = brw.arrival_register_item_id
                       join biz_receipt_inspect_head brih on brih.id = brw.sign_inspect_head_id
                       left join erp_purchase_receipt_head eprh on eprh.id = item.refer_receipt_head_id
                       left join erp_purchase_receipt_item epri on epri.id = item.refer_receipt_item_id
              where head.receipt_type = 125
                and head.is_delete = 0) t
        join dic_material dm on dm.id = t.mat_id
        <where>
            <if test="po.receiptCode != null and po.receiptCode != ''">
                AND t.receipt_code = #{po.matCode}
            </if>
            <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0">
                AND t.receipt_code in
                <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.matCode != null and po.matCode != ''">
                AND dm.mat_code = #{po.matCode}
            </if>
            <if test="po.matCodeList != null and po.matCodeList.size() > 0">
                AND dm.mat_code in
                <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.createTimeStart != null and po.createTimeEnd != null ">
                AND DATE_FORMAT(t.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
            </if>
            <if test="po.modifyTimeStart != null and po.modifyTimeEnd != null ">
                AND DATE_FORMAT(t.modify_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.modifyTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.modifyTimeEnd},'%Y-%m-%d')
            </if>
            <if test="po.maintainDays != null">
                AND t.maintain_days = #{po.maintainDays}
            </if>
            <if test="po.maintainStatus != null and po.maintainStatus != ''">
                AND t.maintain_status = #{po.maintainStatus}
            </if>
        </where>
    </select>
    <select id="selectStockCompareDetail"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockCompareVO">
        SELECT dm2.mat_code,
               dm2.mat_name,
               df.fty_code,
               du.unit_code,
               du.unit_name,
               SUM((sb.qty + sb.qty_freeze) * bbi.price) wmsQty,
               0                                         sapQty
        FROM stock_batch sb
                 LEFT JOIN dic_material dm ON sb.mat_id = dm.id
                 LEFT JOIN dic_material dm2 ON dm.parent_mat_id = dm2.id
                 LEFT JOIN biz_batch_info bbi ON sb.batch_id = bbi.id
                 LEFT JOIN dic_unit du on du.id = dm2.unit_id
                 LEFT JOIN dic_factory df ON sb.fty_id = df.id
        WHERE dm.is_ct_code = 1
          AND bbi.price > 0
          AND df.fty_code = 'J047'
        GROUP BY dm.parent_mat_id,
                 df.id

    </select>

    <select id="getMaterialReturnDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.MaterialReturnVO">
        select brw.mat_id,
               dm.mat_code,
               dm.mat_name,
               brw.unit_id,
               brw.arrival_qty,
               bmrw.return_qty,
               erph.receipt_code                                                  erp_purchase_receipt_code,
               erpi.rid                                                           erp_purchase_receipt_rid,
               bmrw.status_desc,
               head.receipt_code,
               head.create_time,
               head.modify_time,
               case when head.receipt_status = 10 then '未完成' else '已完成' end receipt_status,
               case
                   when head.receipt_status = 10 then DATEDIFF(#{po.now}, head.create_time)
                   else DATEDIFF(head.modify_time, head.create_time)
               end                                                                maintain_days,
               brw.extend29,
               brw.extend37,
               brw.extend2,
               brw.extend20,
               brw.functional_location_code,
               brih.receipt_code inspect_receipt_code,
               preh.receipt_code notice_receipt_code,
               preh.create_time notice_create_time,
               preh.modify_time notice_modify_time
        from biz_material_return_head head
                 join biz_material_return_item item on item.head_id = head.id
                 join biz_material_return_waybill bmrw on bmrw.item_id = item.id
                 join biz_receipt_waybill brw on brw.id = bmrw.bill_id
                 join dic_material dm on dm.id = brw.mat_id
                 left join biz_receipt_inspect_head brih on brih.id = brw.sign_inspect_head_id
                 left join biz_material_return_head preh on preh.id = item.pre_receipt_head_id
                 left join erp_purchase_receipt_head erph on erph.id = item.refer_receipt_head_id
                 left join erp_purchase_receipt_item erpi on erpi.id = item.refer_receipt_item_id
        where head.receipt_type = 127
          and head.is_delete = 0
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND head.receipt_code = #{po.matCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0">
            AND head.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matCode != null and po.matCode != ''">
            AND dm.mat_code = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.createTimeStart != null and po.createTimeEnd != null ">
            AND DATE_FORMAT(head.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.modifyTimeStart != null and po.modifyTimeEnd != null ">
            AND DATE_FORMAT(head.modify_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.modifyTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.modifyTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.maintainDays != null">
            AND case
                    when head.receipt_status = 10 then DATEDIFF(NOW(), head.create_time)
                    else DATEDIFF(head.modify_time, head.create_time)
                end = #{po.maintainDays}
        </if>
        <if test="po.receiptStatus != null and po.receiptStatus != ''">
            AND case when head.receipt_status = 10 then '未完成' else '已完成' end = #{po.receiptStatus}
        </if>

        <if test="po.extend29 != null and po.extend29 != ''" >
            AND brw.extend29 = #{po.extend29}
        </if>
        <if test="po.extend29List != null and po.extend29List.size() > 0 ">
            AND brw.extend29 in
            <foreach collection="po.extend29List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend2 != null and po.extend2 != ''" >
            AND brw.extend2 = #{po.extend2}
        </if>
        <if test="po.extend2List != null and po.extend2List.size() > 0 ">
            AND brw.extend2 in
            <foreach collection="po.extend2List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend20 != null and po.extend20 != ''" >
            AND brw.extend20 = #{po.extend20}
        </if>
        <if test="po.extend20List != null and po.extend20List.size() > 0 ">
            AND brw.extend20 in
            <foreach collection="po.extend20List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.functionalLocationCode != null and po.functionalLocationCode != ''" >
            AND brw.functional_location_code = #{po.functionalLocationCode}
        </if>
        <if test="po.functionalLocationCodeList != null and po.functionalLocationCodeList.size() > 0 ">
            AND brw.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.inspectReceiptCode != null and po.inspectReceiptCode != ''">
            AND brih.receipt_code = #{po.matCode}
        </if>
        <if test="po.inspectReceiptCodeList != null and po.inspectReceiptCodeList.size() > 0">
            AND brih.receipt_code in
            <foreach collection="po.inspectReceiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.noticeReceiptCode != null and po.noticeReceiptCode != ''">
            AND preh.receipt_code = #{po.matCode}
        </if>
        <if test="po.noticeReceiptCodeList != null and po.noticeReceiptCodeList.size() > 0">
            AND preh.receipt_code in
            <foreach collection="po.noticeReceiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getUnitizedMaintainDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.UnitizedMaintainVO">
        select item.mat_id,
               dm.mat_code,
               dm.mat_name,
               item.fty_id,
               item.location_id,
               item.batch_id,
               bbi.batch_code,
               preh.receipt_code plan_receipt_code,
               preh.maintenance_type,
               preh.remark,
               preh.create_time plan_create_time,
               preh.plan_complete_date,
               preh.submit_user_id plan_submit_user_id,
               head.receipt_code,
               head.create_time,
               head.modify_time submit_time,
               head.submit_user_id,
               item.qty,
               item.unit_id,
               item.bin_id,
               prei.maintenance_program,
               item.state_desc,
               item.maintain_content,
               item.maintenance_date,
               case when head.maintenance_type = 2 then item.maintenance_date_pro else item.maintenance_date_normal end maintenance_date_next,
               item.item_remark,
               bbi.functional_location_code,
               bbi.extend2,
               bbi.extend20,
               bbi.extend24,
               bbi.extend25,
               bbi.extend26,
               bbi.extend27,
               bbi.extend28,
               bbi.extend29,
               bbi.extend61
        from biz_receipt_maintain_head head
                 join biz_receipt_maintain_item item on item.head_id = head.id and item.is_delete = 0
                 join dic_material dm on dm.id = item.mat_id
                 join biz_batch_info bbi on bbi.id = item.batch_id
                 join biz_receipt_maintain_head preh on preh.id = item.pre_receipt_head_id
                 join biz_receipt_maintain_item prei on prei.id = item.pre_receipt_item_id
        where head.receipt_type = 818
          and head.is_delete = 0
          and head.receipt_status = 90
        <if test="po.ftyId != null" >
            AND item.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null" >
            AND item.location_id = #{po.locationId}
        </if>
        <if test="po.matCode != null and po.matCode != ''">
            AND dm.mat_code = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.batchCode !=null and po.batchCode != ''" >
            AND bbi.batch_code = #{po.batchCode}
        </if>
        <if test="po.batchCodeList !=null and po.batchCodeList.size() > 0 ">
            AND bbi.batch_code in
            <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.planReceiptCode != null and po.planReceiptCode != ''" >
            AND preh.receipt_code = #{po.planReceiptCode}
        </if>
        <if test="po.maintenanceType !=null and po.maintenanceType != ''" >
            AND preh.maintenance_type  = #{po.maintenanceType}
        </if>
        <if test="po.remark !=null and po.remark != ''" >
            AND preh.remark like concat( '%',#{po.remark}, '%')
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND head.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptCodeList != null and po.receiptCodeList.size() > 0">
            AND head.receipt_code in
            <foreach collection="po.receiptCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.maintenanceDateStart != null and po.maintenanceDateEnd != null">
            AND DATE_FORMAT(item.maintenance_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.maintenanceDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.maintenanceDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.maintenanceDateNextStart != null and po.maintenanceDateNextEnd != null">
            AND DATE_FORMAT(case when head.maintenance_type = 2 then item.maintenance_date_pro else item.maintenance_date_normal end,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.maintenanceDateNextStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.maintenanceDateNextEnd},'%Y-%m-%d')
        </if>
        <if test="po.functionalLocationCode !=null and po.functionalLocationCode != ''" >
            AND bbi.functional_location_code = #{po.functionalLocationCode}
        </if>
        <if test="po.functionalLocationCodeList !=null and po.functionalLocationCodeList.size() > 0 ">
            AND bbi.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend2 !=null and po.extend2 != ''" >
            AND bbi.extend2 = #{po.extend2}
        </if>
        <if test="po.extend2List !=null and po.extend2List.size() > 0 ">
            AND bbi.extend2 in
            <foreach collection="po.extend2List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend20 !=null and po.extend20 != ''" >
            AND bbi.extend20 = #{po.extend20}
        </if>
        <if test="po.extend20List !=null and po.extend20List.size() > 0 ">
            AND bbi.extend20 in
            <foreach collection="po.extend20List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend24 !=null and po.extend24 != ''" >
            AND bbi.extend24 like concat( '%',#{po.extend24}, '%')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''" >
            AND bbi.extend28 = #{po.extend28}
        </if>
        <if test="po.extend29 !=null and po.extend29 != ''" >
            AND bbi.extend29 = #{po.extend29}
        </if>
        <if test="po.extend29List !=null and po.extend29List.size() > 0 ">
            AND bill.extend29 in
            <foreach collection="po.extend29List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.extend61 !=null and po.extend61 != ''" >
            AND bbi.extend61 = #{po.extend61}
        </if>
        <if test="po.extend61List !=null and po.extend61List.size() > 0 ">
            AND bbi.extend61 in
            <foreach collection="po.extend61List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectRegisterBatchCount" resultType="int">
        select count(1)
        from biz_receipt_register_head brrh
        inner join biz_receipt_waybill brw on brrh.id = brw.arrival_register_head_id
        where brrh.receipt_type = 102
        and brrh.receipt_status = 90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brrh.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectRegisterBoxCount" resultType="int">
        select count(1)from (
        select brw.case_code from biz_receipt_waybill brw
        inner join biz_receipt_register_head brrh on brrh.id = brw.arrival_register_head_id
        where brrh.receipt_type = 102 and brrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brrh.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
        group by brw.case_code ) tmp

    </select>

    <select id="selectRegisterUpCount" resultType="int">
        select count(brri.id) from biz_receipt_register_head brrh
        inner join biz_receipt_register_item brri on brrh.id=brri.head_id
        inner join biz_receipt_waybill brw on brri.id = brw.arrival_register_item_id
        where brrh.receipt_type = 102 and brrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brrh.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>


    <select id="selectInspectBatchCount" resultType="int">
        select count(1) from biz_receipt_inspect_head brih
        inner join biz_receipt_waybill brw on brih.id = brw.sign_inspect_head_id
        where brih.receipt_type = 104 and brih.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brih.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectInspectItemCount" resultType="int">
        select count(brii.id) from biz_receipt_inspect_head brih
        inner join biz_receipt_inspect_item brii on brih.id = brii.head_id
        inner join biz_receipt_waybill brw on brii.id = brw.sign_inspect_item_id
        where brih.receipt_type = 104 and brih.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brih.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectNcrCount" resultType="int">
        select count(brii.id) from biz_receipt_inconformity_head brih
        inner join biz_receipt_inconformity_item brii on brih.id =brii.head_id
        inner join biz_receipt_waybill brw on brii.id = brw.quality_inconformity_notice_item_id
        where brih.receipt_type = 126 and brih.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brih.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>


    <select id="selectGvnCount" resultType="int">
        select count(brii.id) from biz_receipt_inconformity_head brih
        inner join biz_receipt_inconformity_item brii on brih.id =brii.head_id
        inner join biz_receipt_waybill brw on brii.id = brw.number_inconformity_notice_item_id
        where brih.receipt_type = 124 and brih.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brih.submit_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectRequirementCount" resultType="int">
        select count(1) from biz_receipt_require_head brrh
        where brrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brrh.modify_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
    </select>


    <select id="selectRequirementItemCount" resultType="int">
        select count(brri.id) from biz_receipt_require_head brrh inner
        join biz_receipt_require_item brri on brrh.id=brri.head_id
        where brrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brrh.modify_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
    </select>

    <select id="selectApplyCount" resultType="int">
        select count(1) from biz_receipt_apply_head brah
        inner join biz_receipt_apply_item brai on brah.id= brai.head_id
        inner join biz_batch_info bbi on bbi.id = brai.batch_id
        where brah.receipt_type = 1077 and brah.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brah.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectApplyItemCount" resultType="int">
        select count(brai.id) from biz_receipt_apply_head brah
        inner join biz_receipt_apply_item brai on brah.id= brai.head_id
        inner join biz_batch_info bbi on bbi.id = brai.batch_id
        where brah.receipt_type = 1077 and brah.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brah.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>


    <select id="selectOutboundCount" resultType="int">
        select count(1) from biz_receipt_output_head broh
        inner join biz_receipt_output_item broi on broh.id=broi.head_id
        inner join biz_receipt_output_bin brob on brob.item_id = broi.id
        inner join biz_batch_info bbi on bbi.id = brob.batch_id
        where broh.receipt_type = 108 and broh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(broi.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectOutboundItemCount" resultType="int">
        select count(broi.id) from biz_receipt_output_head broh
        inner join biz_receipt_output_item broi on broh.id=broi.head_id
        inner join biz_receipt_output_bin brob on brob.item_id = broi.id
        inner join biz_batch_info bbi on bbi.id = brob.batch_id
        where broh.receipt_type = 108 and broh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(broi.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectNotOutboundCount" resultType="int">
        select count(1) from biz_receipt_output_head broh
        inner join biz_receipt_output_item broi on broh.id=broi.head_id
        inner join biz_receipt_output_bin brob on brob.item_id = broi.id
        inner join biz_batch_info bbi on bbi.id = brob.batch_id
        where broh.receipt_type = 108 and broh.receipt_status =10
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(broh.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectNotOutboundItemCount" resultType="int">
        select count(broi.id) from biz_receipt_output_head broh
        inner join biz_receipt_output_item broi on broh.id=broi.head_id
        inner join biz_receipt_output_bin brob on brob.item_id = broi.id
        inner join biz_batch_info bbi on bbi.id = brob.batch_id
        where broh.receipt_type = 108 and broh.receipt_status =10
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(broh.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>


    <select id="selectReturnCount" resultType="int">
        select count(1) from biz_receipt_return_head brrh
        inner join biz_receipt_return_item brri on brrh.id= brri.head_id
        inner join biz_receipt_return_bin brrb on brrb.item_id = brri.id
        inner join biz_batch_info bbi on brrb.batch_id = bbi.id
        where brrh.receipt_type = 139 and brrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brri.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectReturnItemCount" resultType="int">

        select count(brri.id) from biz_receipt_return_head brrh
        inner join biz_receipt_return_item brri on brrh.id= brri.head_id
        inner join biz_receipt_return_bin brrb on brrb.item_id = brri.id
        inner join biz_batch_info bbi on brrb.batch_id = bbi.id
        where brrh.receipt_type = 139 and brrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(brri.posting_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND bbi.extend28 = #{po.extend28}
        </if>
    </select>

    <select id="selectMaterialReturnBatchCount" resultType="int">
        select count(1) from biz_material_return_head bmrh
        inner join biz_material_return_item bmri on bmri.head_id =bmrh.id
        inner join biz_material_return_waybill bmrw on bmri.id = bmrw.item_id
        inner join biz_receipt_waybill brw on brw.id = bmrw.bill_id
        where bmrh.receipt_type = 127 and bmrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(bmrh.modify_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>


    <select id="selectMaterialReturnItemCount" resultType="int">
        select count(bmri.id) from biz_material_return_head bmrh
        inner join biz_material_return_item bmri on bmri.head_id =bmrh.id
        inner join biz_material_return_waybill bmrw on bmri.id = bmrw.item_id
        inner join biz_receipt_waybill brw on brw.id = bmrw.bill_id
        where bmrh.receipt_type = 127 and bmrh.receipt_status =90
        <if test="po.createTimeStart != null and po.createTimeEnd != null">
            AND DATE_FORMAT(bmrh.modify_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.createTimeStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND brw.extend28 = #{po.extend28}
        </if>
    </select>
    <select id="selectStockRetention" resultType="com.inossem.wms.common.model.bizdomain.report.dto.StockRetentionDTO">
        WITH RetentionDays AS (SELECT id                                         batchId,
                                      price,
        DATEDIFF(CURRENT_DATE, input_date) AS retention_days
                               FROM biz_batch_info
        WHERE input_date IS NOT NULL
        <if test="po.extend28 !=null and po.extend28 != ''">
            AND extend28 = #{po.extend28}
        </if>
        <if test="po.ftyId !=null and po.ftyId != ''">
            AND fty_id = #{po.ftyId}
        </if>

        <if test="po.procurementMethod !=null and po.procurementMethod != ''">
            AND procurement_method = #{po.procurementMethod}
        </if>
        )
        SELECT CASE
        WHEN retention_days BETWEEN 0 AND 180 THEN '0-6个月'
        WHEN retention_days BETWEEN 181 AND 360 THEN '6-12个月'
        WHEN retention_days BETWEEN 361 AND 720 THEN '12-24个月'
        ELSE '24个月以上'
                   END AS             retentionPeriod,
               COUNT(sb.id)           itemCount,
               SUM(sb.qty * rd.price) price
        FROM stock_batch sb
                 INNER JOIN RetentionDays rd ON rd.batchId = sb.batch_id
                 INNER JOIN dic_material dm ON dm.id = sb.mat_id
            AND dm.is_ct_code = 1
        <if test="po.locationId !=null and po.locationId != ''">
            AND sb.location_id = #{po.locationId}
        </if>
        GROUP BY retentionPeriod
    </select>

    <select id="selectMatRetentionStockDetail"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.MatRetentionStockDetailVO">
        SELECT dm.mat_code,
               dm.mat_name,
               bbi.extend20,
               bbi.extend33,
               bbi.extend2                          as purchasePackageCode,
               bbi.case_code,
               bbi.extend29,
               bbi.functional_location_code,
               sum(sb.qty)                          as stockQty,
               bbi.procurement_method,
               du.unit_code,
               bbi.input_date,
               datediff(CURRENT_DATE(), input_date) as retentionDays,
               bbi.extend28
        FROM stock_batch sb
                 INNER JOIN dic_material dm ON dm.id = sb.mat_id
                 INNER JOIN biz_batch_info bbi ON sb.batch_id = bbi.id
                 INNER JOIN dic_unit du ON dm.unit_id = du.id
        where dm.is_ct_code = 1
        group by dm.id, bbi.id
    </select>

    <select id="selectStockValidityPeriod"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockValidityPeriodWarningVO">
        select dm.mat_code,
        df.fty_code,
        dsl.location_code,
        bbi.batch_code,
        sum(sb.qty) as qty,
        bbi.production_date,
        bbi.lifetime_date,
        DATEDIFF(bbi.lifetime_date, production_date) as shelfLife,
        DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) as closeLife,
        CASE WHEN DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) >= 0 THEN '临期' ELSE '过期' END AS type,
        case when 0 > DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) then 1
        when DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) between 0 and 30 then 2
        when DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) between 31 and 90 then 3
        else 4 end as closeType,
        bbi.is_safe,
        bbi.purchase_receipt_code,
        bbi.purchase_receipt_rid,
        bbi.procurement_method,
        dm2.mat_code as mainMatCode,
        bbi.extend33,
        bbi.extend2,
        bbi.extend20,
        bbi.functional_location_code,
        bbi.extend29,
        bbi.extend24,
        bbi.extend25,
        bbi.extend26,
        bbi.extend27,
        du.unit_code,
        bbi.extend28,
        bbi.extend34,
        bbi.case_code
        from stock_batch sb
        inner join biz_batch_info bbi on sb.batch_id = bbi.id
        inner join dic_material dm on bbi.mat_id = dm.id
        inner join dic_factory df on sb.fty_id = df.id
        inner join dic_stock_location dsl on sb.location_id = dsl.id
        inner join dic_material dm2 on dm.parent_mat_id = dm2.id
        inner join dic_unit du on du.id = dm.unit_id
        where df.fty_code = 'J047'
        and dm.is_ct_code = 1
        <if test="po.mainMatCodeList != null and po.mainMatCodeList.size() > 0">
            AND dm2.mat_code in
            <foreach collection="po.mainMatCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.extend20List != null and po.extend20List.size() > 0">
            AND bbi.extend20 in
            <foreach collection="po.extend20List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.functionalLocationCodeList != null and po.functionalLocationCodeList.size() > 0">
            AND bbi.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.extend29List != null and po.extend29List.size() > 0">
            AND bbi.extend29 in
            <foreach collection="po.extend29List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.caseCodeList != null and po.caseCodeList.size() > 0">
            AND bbi.case_code in
            <foreach collection="po.caseCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.productionDateStart != null and po.productionDateEnd != null">
            AND DATE_FORMAT(bbi.production_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.productionDateStart},'%Y-%m-%d')
            AND
            DATE_FORMAT(#{po.productionDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.lifeTimeDateStart != null and po.lifeTimeDateEnd != null">
            AND DATE_FORMAT(bbi.lifetime_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.lifeTimeDateStart},'%Y-%m-%d') AND
            DATE_FORMAT(#{po.lifeTimeDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.closeLifeStart !=null and po.closeLifeEnd != ''">
            AND DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) between #{po.closeLifeStart} and #{po.closeLifeEnd}
        </if>
        <if test="po.closeLife !=null and po.closeLife != ''">
            AND DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) = #{po.closeLife}
        </if>
        <if test="po.extend24 !=null and po.extend24 != ''">
            AND bbi.extend24 = #{po.extend24}
        </if>
        <if test="po.closeType !=null and po.closeType == 1">
            AND 0 > DATEDIFF(bbi.lifetime_date, CURRENT_DATE())
        </if>
        <if test="po.closeType !=null and po.closeType == 2">
            AND DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) between 0 and 30
        </if>
        <if test="po.closeType !=null and po.closeType == 3">
            AND DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) between 31 and 180
        </if>
        <if test="po.closeType !=null and po.closeType == 4">
            AND DATEDIFF(bbi.lifetime_date, CURRENT_DATE()) > 180
        </if>
        group by dm.id
    </select>

    <select id="selectMatStateTrack" resultType="com.inossem.wms.common.model.bizdomain.report.vo.MatStateTrackVO">
        SELECT
        group_concat(brw.arrival_register_item_id) AS registerItemIds,
        group_concat(brw.sign_inspect_item_id) AS signInspectItemIds,
        group_concat(briw.item_id) as inputItemIds,
        group_concat(bmrw.item_id) as materialReturnItemIds,
        group_concat(brti.id) as transportItemIds,
        group_concat(so.receipt_bin_id) as reserveBinIds,
        group_concat(broi.id) as outputItemIds,
        group_concat(brri2.id) as returnItemIds,
        dm.mat_code,
        dm2.mat_code as mainMatCode,
        brrh.is_safe,
        brw.extend62 as purchaseReceiptCode,
        brw.extend63 as purchaseReceiptRid,
        brdnh.procurement_method,
        brw.extend33,
        brw.mat_name,
        brw.extend2,
        brw.extend20,
        brw.functional_location_code,
        brw.extend29,
        brw.extend37,
        brw.extend24,
        brw.extend25,
        brw.extend26,
        brw.extend27,
        du.unit_code,
        brw.extend28,
        brw.extend34,
        brw.case_code,
        SUM(brri.qty) AS arrivalQty,
        SUM(brw.arrival_qty) AS qtyPay,
        SUM(brw.qualified_qty) AS qualifiedQty,
        SUM(brw.unqualified_qty) AS unqualifiedQty,
        SUM(brw.unarrival_qty) AS unArrivalQty,
        SUM(briw.qty) AS inputQty,
        SUM(bmrw.return_qty) AS matReturnQty,
        SUM(sb.qty) AS qty,
        SUM(sb.qty_freeze) AS qtyFreeze,
        SUM(so.qty) AS qtyReserve,
        SUM(broi.qty) AS outputQty,
        SUM(brri2.qty) AS returnQty
        FROM
        biz_receipt_waybill brw
        INNER JOIN dic_material dm ON brw.mat_id = dm.id
        INNER JOIN dic_material dm2 on dm2.id = dm.parent_mat_id
        INNER JOIN biz_receipt_register_head brrh ON brw.arrival_register_head_id = brrh.id
        INNER JOIN biz_receipt_register_item brri ON brw.arrival_register_item_id = brri.id
        INNER JOIN biz_receipt_delivery_notice_head brdnh ON brw.delivery_notice_head_id = brdnh.id
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN dic_factory df ON brw.fty_id = df.id
        LEFT JOIN biz_receipt_input_waybill briw ON briw.bill_id = brw.id
        LEFT JOIN biz_material_return_waybill bmrw ON bmrw.bill_id = brw.id
        LEFT JOIN stock_batch sb ON sb.mat_id = dm.id
        LEFT JOIN stock_occupy so ON so.stock_batch_id = sb.id
        LEFT JOIN biz_receipt_output_item broi ON broi.mat_id = dm.id
        LEFT JOIN biz_receipt_return_item brri2 ON brri2.mat_id = dm.id
        LEFT JOIN biz_receipt_transport_item brti on brti.input_mat_id = dm.id
        WHERE
        brrh.receipt_status = 90
        AND dm.is_ct_code = 1
        AND brrh.receipt_type = 102
        AND df.fty_code = 'J047'
        <if test="po.extend2List != null and po.extend2List.size() > 0">
            AND brw.extend2 in
            <foreach collection="po.extend2List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="po.purchaseReceiptCodeList != null and po.purchaseReceiptCodeList.size() > 0">
            AND brw.extend62 in
            <foreach collection="po.purchaseReceiptCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.mainMatCodeList != null and po.mainMatCodeList.size() > 0">
            AND dm2.mat_code in
            <foreach collection="po.mainMatCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.extend20List != null and po.extend20List.size() > 0">
            AND brw.extend20 in
            <foreach collection="po.extend20List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.functionalLocationCodeList != null and po.functionalLocationCodeList.size() > 0">
            AND brw.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.extend29List != null and po.extend29List.size() > 0">
            AND brw.extend29 in
            <foreach collection="po.extend29List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.caseCodeList != null and po.caseCodeList.size() > 0">
            AND brw.case_code in
            <foreach collection="po.caseCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.isSafe !=null and po.isSafe != ''">
            AND brrh.is_safe = #{po.isSafe}
        </if>
        <if test="po.extend24 !=null and po.extend24 != ''">
            AND brw.extend24 like concat( '%',#{po.extend24}, '%')
        </if>
        GROUP BY dm.id
    </select>

    <select id="selectReservedStock" resultType="com.inossem.wms.common.model.bizdomain.report.vo.ReservedEmptyStockVO">
        SELECT sb.mat_id,
        brri.matnr as matCode,
        brri.desc_text as matName,
        brri.unit as unitCode,
        SUM(brri.qty) - SUM(brai.applied_qty) as reservedQty,
        SUM(sb.qty) -IFNULL(sum(so.qty),0) as stockQty
        FROM biz_receipt_require_item brri
        LEFT JOIN biz_receipt_apply_item brai ON brai.pre_receipt_item_id = brri.id
        LEFT JOIN biz_receipt_output_item broi ON broi.pre_receipt_item_id = brai.id
        LEFT JOIN biz_receipt_output_head broh ON broi.head_id = broh.id
        LEFT JOIN biz_receipt_output_bin brob ON brob.head_id = broh.id
        LEFT JOIN stock_batch sb ON brob.mat_id = sb.mat_id
        LEFT JOIN stock_occupy so ON so.stock_batch_id = sb.id
        WHERE brri.item_status = 90
        AND brai.item_status = 90
        <if test="po.matCode !=null and po.matCode != ''">
            AND brri.matnr = #{po.matCode}
        </if>
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND brri.matnr in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY brri.matnr
    </select>

    <select id="selectArrivedButNotInputQty" resultType="java.math.BigDecimal">
        SELECT
        ifnull(brw.arrival_qty,0)
        FROM
        biz_receipt_waybill brw
        INNER JOIN biz_receipt_delivery_notice_head brdnh ON brw.delivery_notice_head_id = brdnh.id
        INNER JOIN biz_receipt_input_waybill briw ON brw.id = briw.bill_id
        INNER JOIN biz_receipt_input_head brih ON briw.head_id = brih.id
        WHERE
        brdnh.receipt_status = 90
        AND brih.receipt_status != 90
        <if test="po.matId !=null and po.matId != ''">
            AND brw.mat_id = #{po.matId}
        </if>
    </select>
    <select id="selectToolBorrowReport"
            resultType="com.inossem.wms.common.model.bizdomain.report.vo.ToolBorrowStatisticsVO">
        select bbi.extend29,
        dm.mat_code ,
        dm.mat_name ,
        du.unit_code ,
        bbi.functional_location_code ,
        bbi.extend2 ,
        bbi.extend20 ,
        bbi.extend24,
        bbi.extend25,
        bbi.extend26,
        bbi.extend27,
        bbi.extend28,
        brtbah2.receipt_code as borrowReceiptCode ,
        brtbah2.create_user_id as borrowCreateUserId,
        brtbah2.description as borrowReceiptCodeDesc,
        brtbah2.submit_time as borrowReceiptSubmitTime,
        brtbah.receipt_code as returnReceiptCode,
        brtbah.submit_time as returnReceiptSubmitTime
        from biz_receipt_tool_borrow_apply_head brtbah
        join biz_receipt_tool_borrow_apply_item brtbai on brtbah.id =brtbai.head_id and brtbah.receipt_type =143
        left join biz_receipt_tool_borrow_apply_head brtbah2 on brtbai.pre_receipt_head_id = brtbah2.id and
        brtbah2.receipt_type =142
        left join biz_receipt_tool_borrow_apply_item brtbai2 on brtbai.pre_receipt_item_id = brtbai2.id
        join biz_batch_info bbi on brtbai.batch_id = bbi.id
        join dic_material dm on brtbai.mat_id =dm.id
        join dic_unit du on dm.unit_id = du.id
        where brtbah2.is_delete = 0
        <if test="po.matCodeList != null and po.matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.borrowUserIdList != null and po.borrowUserIdList.size() > 0">
            AND brtbah2.create_user_id in
            <foreach collection="po.borrowUserIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.matName !=null and po.matName != ''">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.extend20List != null and po.extend20List.size() > 0">
            AND bbi.extend20 in
            <foreach collection="po.extend20List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.functionalLocationCodeList != null and po.functionalLocationCodeList.size() > 0">
            AND bbi.functional_location_code in
            <foreach collection="po.functionalLocationCodeList" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.extend29List != null and po.extend29List.size() > 0">
            AND bbi.extend29 in
            <foreach collection="po.extend29List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.borrowReceiptCode !=null and po.borrowReceiptCode != ''">
            AND brtbah2.receipt_code =#{po.borrowReceiptCode}
        </if>
        <if test="po.borrowReceiptCodeDesc !=null and po.borrowReceiptCodeDesc != ''">
            AND brtbah2.description =#{po.borrowReceiptCodeDesc}
        </if>
        <if test="po.returnReceiptCode !=null and po.returnReceiptCode != ''">
            AND brtbah.receipt_code =#{po.returnReceiptCode}
        </if>
    </select>

    <select id="getUnitizedTempStoreDetail" resultType="com.inossem.wms.common.model.bizdomain.report.vo.TempStoreVO">
        select b.fty_id,
               df.fty_code,
               df.fty_name,
               b.location_id,
               b.mat_id,
               dm.mat_code,
               dm.mat_name,
               bbi.batch_code,
               bbi.temp_store_user,
               bbi.temp_store_dept_office_id,
               ddo.dept_office_code temp_store_dept_office_code,
               ddo.dept_office_name temp_store_dept_office_name,
               bbi.temp_store_dept_id,
               dd.dept_code temp_store_dept_code,
               dd.dept_name temp_store_dept_name,
               bbi.temp_store_reason,
               bbi.input_date,
               bbi.temp_store_expire_date,
               case when bbi.temp_store_expire_date is null then null when bbi.temp_store_expire_date &lt; #{po.now} then '已过期' else '未过期' end expire_type,
               ABS(DATEDIFF(bbi.temp_store_expire_date, #{po.now})) expire_days
        from stock_bin b
            join biz_batch_info bbi on bbi.id = b.batch_id and bbi.spec_stock in ('Z', 'Y')
            join dic_factory df on df.id = b.fty_id and df.fty_code in ('S046', 'S047')
            join dic_material dm on dm.id = b.mat_id
            left join dic_dept dd on dd.id = bbi.temp_store_dept_id
            left join dic_dept_office ddo on ddo.id = bbi.temp_store_dept_office_id
        <where>
            <if test="po.ftyId != null" >
                AND b.fty_id = #{po.ftyId}
            </if>
            <if test="po.locationId != null" >
                AND b.location_id = #{po.locationId}
            </if>
            <if test="po.matCode !=null and po.matCode != ''">
                AND dm.mat_code = #{po.matCode}
            </if>
            <if test="po.matName !=null and po.matName != '' ">
                AND dm.mat_name like concat( '%',#{po.matName}, '%')
            </if>
            <if test="po.matCodeList != null and po.matCodeList.size() > 0">
                AND dm.mat_code in
                <foreach collection="po.matCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="po.batchCode !=null and po.batchCode != ''" >
                AND bbi.batch_code = #{po.batchCode}
            </if>
            <if test="po.batchCodeList !=null and po.batchCodeList.size() > 0 ">
                AND bbi.batch_code in
                <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="po.tempStoreUser != null and po.tempStoreUser != '' ">
                AND bbi.temp_store_user like concat('%',#{po.tempStoreUser}, '%')
            </if>
            <if test="po.tempStoreDeptOfficeName !=null and po.tempStoreDeptOfficeName != ''" >
                AND ddo.dept_office_name like CONCAT('%', #{po.tempStoreDeptOfficeName} , '%')
            </if>
            <if test="po.tempStoreDeptName !=null and po.tempStoreDeptName != ''" >
                AND dd.dept_name like CONCAT('%', #{po.tempStoreDeptName} , '%')
            </if>
            <if test="po.tempStoreReason !=null and po.tempStoreReason != ''" >
                AND bbi.temp_store_reason like CONCAT('%', #{po.tempStoreReason} , '%')
            </if>
            <if test="po.inputDateStart != null and po.inputDateEnd != null ">
                AND DATE_FORMAT(bbi.input_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.inputDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.inputDateEnd},'%Y-%m-%d')
            </if>
            <if test="po.tempStoreExpireDateStart != null and po.tempStoreExpireDateEnd != null ">
                AND DATE_FORMAT(bbi.temp_store_expire_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.tempStoreExpireDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.tempStoreExpireDateEnd},'%Y-%m-%d')
            </if>
            <if test="po.expireType != null and po.expireType != ''" >
                AND case when bbi.temp_store_expire_date is null then null when bbi.temp_store_expire_date &lt; #{po.now} then '已过期' else '未过期' end = #{po.expireType}
            </if>
            <if test="po.expireDays != null" >
                AND ABS(DATEDIFF(bbi.temp_store_expire_date, #{po.now})) = #{po.expireDays}
            </if>
        </where>
    </select>

    <select id="getStockInsDocBinPage" resultType="com.inossem.wms.common.model.stock.vo.StockInsDocBinVO">
SELECT * from (
SELECT
	IFNULL(
		input_head_pre.receipt_code,
		IFNULL(
			output_head_pre.receipt_code,
			IFNULL(
				return_head_pre.receipt_code,
				IFNULL(
					transport_head_pre.receipt_code,
					IFNULL(
						arrange_head_pre.receipt_code,
						task_head_pre.receipt_code
					)
				)
			)
		)
	) pre_receipt_code,
	IFNULL(
		input_item_pre.rid,
		IFNULL(
			output_item_pre.rid,
			IFNULL(
				return_item_pre.rid,
				IFNULL(
					transport_item_pre.rid,
					IFNULL(
						arrange_item_pre.rid,
						task_item_pre.rid
					)
				)
			)
		)
	) pre_receipt_rid,
	IFNULL(
		input_bin_pre.bid,
		IFNULL(
			output_bin_pre.bid,
			IFNULL(
				return_bin_pre.bid,
				transport_bin_pre.bid
			)
		)
	) pre_receipt_bid,
	IFNULL(
		input_head_refer.receipt_code,
		IFNULL(
			output_head_refer.receipt_code,
			IFNULL(
				return_head_refer.receipt_code,
				IFNULL(
					transport_head_refer.receipt_code,
					IFNULL(
						apply_head_refer.receipt_code,
						IFNULL(
							purchase_head_refer.receipt_code,
							reserve_head_refer.receipt_code
						)
					)
				)
			)
		)
	) refer_receipt_code,
	IFNULL(
		input_item_refer.rid,
		IFNULL(
			output_item_refer.rid,
			IFNULL(
				return_item_refer.rid,
				IFNULL(
					transport_item_refer.rid,
					IFNULL(
						apply_item_refer.rid,
						IFNULL(
							purchase_item_refer.rid,
							reserve_item_refer.rid
						)
					)
				)
			)
		)
	) refer_receipt_rid,
	IFNULL(
		input_bin_refer.bid,
		IFNULL(
			output_bin_refer.bid,
			IFNULL(
				return_bin_refer.bid,
				IFNULL(
					transport_bin_refer.bid,
					apply_bin_refer.bid
				)
			)
		)
	) refer_receipt_bid,
	dic_material.mat_code,
	dic_material.mat_name,
	biz_batch_info.batch_code,
	biz_batch_info.spec_stock,
	biz_batch_info.spec_stock_code,
	dic_factory.fty_code,
	dic_stock_location.location_code,
	dic_wh.wh_code,
	dic_wh_storage_type.type_code,
	dic_wh_storage_bin.bin_code,
	dic_unit.unit_code,
	stock_ins_doc_bin.*
FROM
	stock_ins_doc_bin
LEFT JOIN dic_material ON dic_material.id = stock_ins_doc_bin.mat_id
LEFT JOIN biz_batch_info ON biz_batch_info.id = stock_ins_doc_bin.batch_id
LEFT JOIN dic_factory ON dic_factory.id = stock_ins_doc_bin.fty_id
LEFT JOIN dic_stock_location ON dic_stock_location.id = stock_ins_doc_bin.location_id
LEFT JOIN dic_wh ON dic_wh.id = stock_ins_doc_bin.wh_id
LEFT JOIN dic_wh_storage_type ON dic_wh_storage_type.id = stock_ins_doc_bin.type_id
LEFT JOIN dic_wh_storage_bin ON dic_wh_storage_bin.id = stock_ins_doc_bin.bin_id
LEFT JOIN dic_unit ON dic_unit.id = stock_ins_doc_bin.unit_id
LEFT JOIN biz_receipt_input_head input_head_pre ON input_head_pre.id = stock_ins_doc_bin.pre_receipt_head_id
LEFT JOIN biz_receipt_input_item input_item_pre ON input_item_pre.id = stock_ins_doc_bin.pre_receipt_item_id
LEFT JOIN biz_receipt_input_bin input_bin_pre ON input_bin_pre.id = stock_ins_doc_bin.pre_receipt_bin_id
LEFT JOIN biz_receipt_input_head input_head_refer ON input_head_refer.id = stock_ins_doc_bin.refer_receipt_head_id
LEFT JOIN biz_receipt_input_item input_item_refer ON input_item_refer.id = stock_ins_doc_bin.refer_receipt_item_id
LEFT JOIN biz_receipt_input_bin input_bin_refer ON input_bin_refer.id = stock_ins_doc_bin.refer_receipt_bin_id
LEFT JOIN biz_receipt_output_head output_head_pre ON output_head_pre.id = stock_ins_doc_bin.pre_receipt_head_id
LEFT JOIN biz_receipt_output_item output_item_pre ON output_item_pre.id = stock_ins_doc_bin.pre_receipt_item_id
LEFT JOIN biz_receipt_output_bin output_bin_pre ON output_bin_pre.id = stock_ins_doc_bin.pre_receipt_bin_id
LEFT JOIN biz_receipt_output_head output_head_refer ON output_head_refer.id = stock_ins_doc_bin.refer_receipt_head_id
LEFT JOIN biz_receipt_output_item output_item_refer ON output_item_refer.id = stock_ins_doc_bin.refer_receipt_item_id
LEFT JOIN biz_receipt_output_bin output_bin_refer ON output_bin_refer.id = stock_ins_doc_bin.refer_receipt_bin_id
LEFT JOIN biz_receipt_return_head return_head_pre ON return_head_pre.id = stock_ins_doc_bin.pre_receipt_head_id
LEFT JOIN biz_receipt_return_item return_item_pre ON return_item_pre.id = stock_ins_doc_bin.pre_receipt_item_id
LEFT JOIN biz_receipt_return_bin return_bin_pre ON return_bin_pre.id = stock_ins_doc_bin.pre_receipt_bin_id
LEFT JOIN biz_receipt_return_head return_head_refer ON return_head_refer.id = stock_ins_doc_bin.refer_receipt_head_id
LEFT JOIN biz_receipt_return_item return_item_refer ON return_item_refer.id = stock_ins_doc_bin.refer_receipt_item_id
LEFT JOIN biz_receipt_return_bin return_bin_refer ON return_bin_refer.id = stock_ins_doc_bin.refer_receipt_bin_id
LEFT JOIN biz_receipt_transport_head transport_head_pre ON transport_head_pre.id = stock_ins_doc_bin.pre_receipt_head_id
LEFT JOIN biz_receipt_transport_item transport_item_pre ON transport_item_pre.id = stock_ins_doc_bin.pre_receipt_item_id
LEFT JOIN biz_receipt_transport_bin transport_bin_pre ON transport_bin_pre.id = stock_ins_doc_bin.pre_receipt_bin_id
LEFT JOIN biz_receipt_transport_head transport_head_refer ON transport_head_refer.id = stock_ins_doc_bin.refer_receipt_head_id
LEFT JOIN biz_receipt_transport_item transport_item_refer ON transport_item_refer.id = stock_ins_doc_bin.refer_receipt_item_id
LEFT JOIN biz_receipt_transport_bin transport_bin_refer ON transport_bin_refer.id = stock_ins_doc_bin.refer_receipt_bin_id
LEFT JOIN biz_receipt_apply_head apply_head_refer ON apply_head_refer.id = stock_ins_doc_bin.refer_receipt_head_id
LEFT JOIN biz_receipt_apply_item apply_item_refer ON apply_item_refer.id = stock_ins_doc_bin.refer_receipt_item_id
LEFT JOIN biz_receipt_apply_bin apply_bin_refer ON apply_bin_refer.id = stock_ins_doc_bin.refer_receipt_bin_id
LEFT JOIN biz_receipt_arrange_head arrange_head_pre ON arrange_head_pre.id = stock_ins_doc_bin.pre_receipt_head_id
LEFT JOIN biz_receipt_arrange_item arrange_item_pre ON arrange_item_pre.id = stock_ins_doc_bin.pre_receipt_item_id
LEFT JOIN biz_receipt_task_head task_head_pre ON task_head_pre.id = stock_ins_doc_bin.pre_receipt_head_id
LEFT JOIN biz_receipt_task_item task_item_pre ON task_item_pre.id = stock_ins_doc_bin.pre_receipt_item_id
LEFT JOIN erp_purchase_receipt_head purchase_head_refer ON purchase_head_refer.id = stock_ins_doc_bin.refer_receipt_head_id
LEFT JOIN erp_purchase_receipt_item purchase_item_refer ON purchase_item_refer.id = stock_ins_doc_bin.refer_receipt_item_id
LEFT JOIN erp_reserve_receipt_head reserve_head_refer ON reserve_head_refer.id = stock_ins_doc_bin.refer_receipt_head_id
LEFT JOIN erp_reserve_receipt_item reserve_item_refer ON reserve_item_refer.id = stock_ins_doc_bin.refer_receipt_item_id
) stock_ins_doc_bin
        ${ew.customSqlSegment}
    </select>

    <select id="selectDeliveryWaybillLedgerPage" resultType="com.inossem.wms.common.model.bizdomain.report.vo.DeliveryWaybillLedgerVO">
        select waybill_head.id,
               waybill_head.delivery_notice_receipt_head_id,
               waybill_head.receipt_code,
               waybill_head.receipt_status,
               waybill_head.delivery_notice_receipt_code,
               waybill_head.delivery_type,
               waybill_head.batch_code,
               contract_head.receipt_code     contract_receipt_code,
               contract_head.create_user_name purchaser_name,
               waybill_head.supplier_name,
               waybill_head.trade_info,
               waybill_head.shipping_type,
               waybill_head.overview_of_goods_names,
               waybill_head.bl_no,
               (select count(1)
                from biz_receipt_delivery_notice_case_rel rel
                where rel.head_id = waybill_head.delivery_notice_receipt_head_id
                  and rel.is_delete = 0)      case_total_count,
               (select sum(ifnull(rel.case_weight, 0))
                from biz_receipt_delivery_notice_case_rel rel
                where rel.head_id = waybill_head.delivery_notice_receipt_head_id
                  and rel.is_delete = 0)      case_weight_sum,
               (select sum(ifnull(rel.volume_value, 0))
                from biz_receipt_delivery_notice_case_rel rel
                where rel.head_id = waybill_head.delivery_notice_receipt_head_id
                  and rel.is_delete = 0)      volume_value_sum,
               notice_head.po_no_tax_amount,
               waybill_head.create_user_name,
               waybill_head.create_time,
               waybill_head.booking_space_time,
               doc_head.receipt_code          submit_redemption_doc_receipt_code,
               doc_head.create_time           submit_redemption_doc_start_time,
               CASE WHEN doc_head.receipt_status = 90 THEN doc_head.modify_time ELSE null END submit_redemption_doc_finish_time,
               exemption_head.receipt_code    income_tax_exemption_receipt_code,
               exemption_head.create_time     income_tax_exemption_start_time,
               CASE WHEN exemption_head.receipt_status = 90 THEN exemption_head.modify_time ELSE null END income_tax_exemption_finish_time,
               waybill_head.estimated_shipping_time,
               waybill_head.actual_transport_vehicle_info,
               waybill_head.ship_id,
               waybill_head.export_customs_declaration_time,
               waybill_head.actual_departure_time,
               waybill_head.estimated_port_of_arrival_time,
               waybill_head.actual_port_of_arrival_time,
               case
                   when exemption_head.receipt_status != 90 then '所得税免税办理中;'
                   when exemption_head.receipt_status = 90 then concat(exemption_head.modify_time, '所得税免税获批;')
                   else ''
                   end                        exemption_status_info,
               case
                   when doc_head.receipt_status = 90 then concat(doc_head.modify_time, '托收已完成;')
                   else '托收办理中;'
                   end                        doc_status_info,
               waybill_head.customs_clearance_completion_time,
               waybill_head.destination_take_delivery_time,
               register_head.receive_date,
               doc_head.klq_back_doc_time,
               doc_head.klq_back_fi_approval_time
        from biz_receipt_delivery_waybill_head waybill_head
                 left join biz_receipt_contract_head contract_head on contract_head.id = waybill_head.contract_id and contract_head.is_delete = 0
                 left join biz_receipt_delivery_notice_head notice_head on notice_head.id = waybill_head.delivery_notice_receipt_head_id and notice_head.is_delete = 0
                 left join biz_receipt_submit_redemption_doc_head doc_head on doc_head.delivery_notice_receipt_head_id = waybill_head.delivery_notice_receipt_head_id and doc_head.is_delete = 0 and doc_head.receipt_type = 9401
                 left join biz_receipt_income_tax_exemption_head exemption_head on exemption_head.delivery_notice_receipt_head_id = waybill_head.delivery_notice_receipt_head_id and exemption_head.is_delete = 0 and exemption_head.receipt_type = 9402
                 left join biz_receipt_register_item register_item on register_item.pre_receipt_head_id = waybill_head.delivery_notice_receipt_head_id and register_item.is_delete = 0
                 left join biz_receipt_register_head register_head on register_head.id = register_item.head_id and register_head.is_delete = 0 and register_head.receipt_type = 221
        where waybill_head.is_delete = 0
          and waybill_head.receipt_type = 9403
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND waybill_head.receipt_code like concat( '%',#{po.receiptCode}, '%')
        </if>
        <if test="po.batchCode != null and po.batchCode != ''">
            AND waybill_head.batch_code like concat( '%',#{po.batchCode}, '%')
        </if>
        <if test="po.batchCodeList != null and po.batchCodeList.size() > 0 ">
            AND
            <foreach collection="po.batchCodeList" open="(" close=")" index="index" separator=" or " item="item">
                 waybill_head.batch_code like concat('%', #{item}, '%')
            </foreach>
        </if>
        <if test="po.supplierName != null and po.supplierName != ''">
            AND waybill_head.supplier_name like concat( '%',#{po.supplierName}, '%')
        </if>
        <if test="po.overviewOfGoodsNames != null and po.overviewOfGoodsNames != ''">
            AND waybill_head.overview_of_goods_names like concat( '%',#{po.overviewOfGoodsNames}, '%')
        </if>
        <if test="po.actualTransportVehicleInfo != null and po.actualTransportVehicleInfo != ''">
            AND waybill_head.actual_transport_vehicle_info like concat( '%',#{po.actualTransportVehicleInfo}, '%')
        </if>
        <if test="po.shippingType != null">
            AND waybill_head.shipping_type = #{po.shippingType}
        </if>
        <if test="po.actualDepartureTimeStart != null and po.actualDepartureTimeEnd != null ">
            AND waybill_head.actual_departure_time BETWEEN DATE_FORMAT(#{po.actualDepartureTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.actualDepartureTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.actualPortOfArrivalTimeStart != null and po.actualPortOfArrivalTimeEnd != null ">
            AND waybill_head.actual_port_of_arrival_time BETWEEN DATE_FORMAT(#{po.actualPortOfArrivalTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.actualPortOfArrivalTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.customsClearanceCompletionTimeStart != null and po.customsClearanceCompletionTimeEnd != null ">
            AND waybill_head.customs_clearance_completion_time BETWEEN DATE_FORMAT(#{po.customsClearanceCompletionTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.customsClearanceCompletionTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.destinationTakeDeliveryTimeStart != null and po.destinationTakeDeliveryTimeEnd != null ">
            AND waybill_head.destination_take_delivery_time BETWEEN DATE_FORMAT(#{po.destinationTakeDeliveryTimeStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.destinationTakeDeliveryTimeEnd},'%Y-%m-%d')
        </if>
        <if test="po.deliveryType != null">
            AND waybill_head.delivery_type = #{po.deliveryType}
        </if>
        group by waybill_head.id
    </select>

</mapper>
