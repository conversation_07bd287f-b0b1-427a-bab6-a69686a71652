<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.openapi.dao.OpenApiMapper">

    <!--查询化学品信息-->
    <select id="getChemicalMatList" resultType="com.inossem.wms.common.model.bizdomain.report.vo.StockBinVO">
        SELECT
        sb.mat_id,
        sb.fty_id,
        sb.location_id,
        sb.batch_id,
        sb.wh_id,
        sb.type_id,
        sb.bin_id,
        sb.cell_id,
        dc.corp_code,
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        dmg.mat_group_code,
        dmg.mat_group_name,
        sb.qty,
        IFNULL(occupy_temp.qty, 0) as occupy_qty,
        sb.qty_freeze,
        sb.qty_transfer,
        sb.qty_inspection,
        sb.qty_temp,
        sb.qty_haste,
        df.fty_code,
        df.fty_name,
        dl.location_code,
        dl.location_name,
        dw.wh_code,
        dw.wh_name,
        bi.batch_code,
        bi.batch_erp,
        bi.spec_stock,
        bi.spec_stock_code,
        bi.spec_stock_name,
        bi.purchase_receipt_code,
        bi.purchase_receipt_rid,
        bi.demand_dept,
        bi.apply_user_name,
        bi.apply_user_dept_name,
        bi.apply_user_office_name,
        dm.shelf_life_min,
        dm.shelf_life_max,
        DATE_ADD(bi.maintenance_date,
        INTERVAL
        (case
        when 0=dmf.package_type then 0
        when 1=dmf.package_type then 5
        when 2=dmf.package_type then 5
        when 3=dmf.package_type then 2
        when 4=dmf.package_type then 3
        when 5=dmf.package_type then 1
        when 6=dmf.package_type then 0
        when 7=dmf.package_type then 5
        when 8=dmf.package_type then 5
        end)
        YEAR) AS maintenance_in_date,
        bi.maintenance_date_pro,
        bi.lifetime_date,
        DATEDIFF(DATE_ADD(bi.production_date,INTERVAL dm.shelf_life_max MONTH),NOW()) AS remainder_days,
        bi.input_date,
        DATEDIFF(now(), input_date) + 1 stock_age,
        bi.production_date,
        bi.shelf_line,
        bi.main_requirement,
        bi.counter_sign_remark,
        ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END) price,
        (
        sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste
        ) * ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END) money,
        dwt.type_code,
        dwt.type_name,
        dwb.bin_code,
        dwb.group_bin_no,
        dwss.section_code,
        dwss.section_name,
        dwc.cell_code,
        dmf.package_type,
        dmf.deposit_type,
        dmf.stock_group,
        epri.contract_code
        FROM
        stock_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id

        INNER JOIN dic_unit du ON dm.unit_id = du.id

        INNER JOIN dic_factory df ON sb.fty_id = df.id

        INNER JOIN dic_corp dc  ON dc.id = df.corp_id

        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id

        INNER JOIN dic_wh dw ON dl.wh_id = dw.id

        INNER JOIN dic_wh_storage_type dwt ON sb.type_id = dwt.id

        INNER JOIN dic_wh_storage_bin dwb ON sb.bin_id = dwb.id

        INNER JOIN biz_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN erp_purchase_receipt_head eprh  ON bi.purchase_receipt_code = eprh.receipt_code
        LEFT JOIN erp_purchase_receipt_item epri ON bi.purchase_receipt_rid= epri.rid and eprh.id=epri.head_id AND epri.delete_tag = 0
        LEFT JOIN dic_wh_storage_section dwss ON dwb.section_id = dwss.id
        LEFT JOIN dic_wh_storage_cell dwc ON sb.cell_id = dwc.id

        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        left join dic_material_facotry_wbs dmfw on dmfw.mat_id = sb.mat_id
        and dmfw.fty_id = sb.fty_id and dmfw.spec_stock = bi.spec_stock and dmfw.spec_stock_code = bi.spec_stock_code
        LEFT JOIN (SELECT stock_bin_id, SUM(qty) as qty FROM stock_occupy GROUP BY stock_bin_id ) occupy_temp ON occupy_temp.stock_bin_id = sb.id
        where  1=1  and sb.qty>0
        <if test="companyCode !=null and companyCode != '' ">
            AND dc.corp_code=#{companyCode}
        </if>
        <if test="plantCode !=null and plantCode != '' ">
            AND df.fty_code=#{plantCode}
        </if>
        <if test="inventoryLocation !=null and inventoryLocation != '' ">
            AND  dl.location_code=#{inventoryLocation}
        </if>
        <if test="chemicalName !=null and chemicalName != '' ">
            AND dm.mat_name like concat( '%',#{chemicalName}, '%')
        </if>
        <if test="materialCode !=null and materialCode != '' ">
            AND  dm.mat_code=#{materialCode}
        </if>
    </select>
</mapper>
