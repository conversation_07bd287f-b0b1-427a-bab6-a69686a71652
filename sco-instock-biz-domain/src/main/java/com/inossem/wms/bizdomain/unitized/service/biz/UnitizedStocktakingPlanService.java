package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.unitized.service.component.UnitizedStocktakingPlanComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 库存盘点 业务实现层
 * </p>
 */

@Service
@Slf4j
public class UnitizedStocktakingPlanService {

    @Autowired
    protected UnitizedStocktakingPlanComponent stocktakingPlanComponent;

    /**
     * 查询盘点模式下拉
     *
     * @return 盘点模式下拉框
     */
    public void getStocktakingTypeDown(BizContext ctx) {

        // 查询盘点模式下拉
        stocktakingPlanComponent.getStocktakingTypeDown(ctx);
    }

    /**
     * 查询物资类型下拉
     *
     * @return 物资类型下拉框选项
     */
    public void getExtend28Down(BizContext ctx) {

        // 查询物资类型下拉
        stocktakingPlanComponent.getExtend28Down(ctx);
    }

    /**
     * 库存盘点-初始化
     *
     * @return 盘点单列表
     */
    @Entrance(call = {"stocktakingPlanComponent#setInit", "stocktakingPlanComponent#setExtendAttachment"})
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置盘点单【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交】
        // 3、设置扩展功能【无】
        stocktakingPlanComponent.setInit(ctx);

        // 开启附件
        stocktakingPlanComponent.setExtendAttachment(ctx);
    }

    /**
     * 获取盘点人列表
     *
     * @param ctx-po 查询用户列表入参
     * @return 用户列表
     */
    public void getUserList(BizContext ctx) {

        // 获取盘点人列表
        stocktakingPlanComponent.setUserList(ctx);

    }

    /**
     * 获取盘点人反显name
     *
     * @param ctx-po 查询用户列表入参
     * @return 用户name描述
     */
    public void getUserNameList(BizContext ctx) {

        // 获取盘点人name
        stocktakingPlanComponent.setUserNameList(ctx);

    }

    /**
     * 查询盘点单列表-分页
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点单列表
     */
    @Entrance(call = {"stocktakingPlanComponent#setPage"})
    public void getPage(BizContext ctx) {

        // 查询盘点单列表-分页
        stocktakingPlanComponent.setPage(ctx);
    }

    /**
     * 查询盘点单详情
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单详情
     */
    @Entrance(call = {"stocktakingPlanComponent#setInfo", "stocktakingPlanComponent#setBatchImg",
        "stocktakingPlanComponent#setExtendAttachment", "stocktakingPlanComponent#setInfoFirstCanBePosted"})
    public void getInfo(BizContext ctx) {

        // 【首盘可过账模式】查询盘点单详情
        stocktakingPlanComponent.setInfoFirstCanBePosted(ctx);

//        // 设置批次图片信息
//        stocktakingPlanComponent.setBatchImg(ctx);

        // 开启附件
        stocktakingPlanComponent.setExtendAttachment(ctx);
    }

    /**
     * 查询盘点单行项目详情
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单详情
     */
    @Entrance(call = {"stocktakingComponent#getItemInfo"})
    public void getItemInfo(BizContext ctx) {

        // 【首盘可过账模式】查询盘点单行项目详情
        stocktakingPlanComponent.getItemInfo(ctx);
    }

    /**
     * 查询盘点单详情
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单详情
     */
    @Entrance(call = {"stocktakingPlanComponent#setInfo", "stocktakingPlanComponent#setBatchImg",
            "stocktakingPlanComponent#setExtendAttachment", "stocktakingPlanComponent#setInfoFirstCanBePosted"})
    public void getStocktakingUserSign(BizContext ctx) {

        // 【首盘可过账模式】查询盘点单详情
      //  stocktakingPlanComponent.setInfoFirstCanBePosted(ctx);
        stocktakingPlanComponent.getStocktakingUserSign(ctx); //*防止沿用获取盘点单详情接口时 数据量过大造成卡顿*
        //签名重新设置按钮权限
        stocktakingPlanComponent.setButtonUserSign(ctx);

    }
    /**
     * 查询仓位库存列表 按照 仓库 存储类型 仓位分组
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点单行项目及物料明细列表
     */
    @Entrance(call = {"stocktakingPlanComponent#setStockBinList", "stocktakingPlanComponent#queryStockBinListFirstCanBePosted"})
    public void getStockBinList(BizContext ctx) {

        // 【首盘可过账模式】查询仓位库存列表 按照 仓库 存储类型 仓位 物料分组的物料信息
        stocktakingPlanComponent.queryStockBinListFirstCanBePosted(ctx);
    }

    /**
     * 查询仓位库存列表 按照 仓库 存储类型 仓位分组
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点单行项目及物料明细列表
     */
    @Entrance(call = {"stocktakingPlanComponent#queryStockBinList"})
    public void getStockBinListForSpecialStocktaking(BizContext ctx) {

        // 查询仓位库存列表 按照 仓库 存储类型 仓位 物料分组的物料信息
        stocktakingPlanComponent.queryStockBinListForSpecialStocktaking(ctx);
    }

    /**
     * 保存盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#saveInfo", "stocktakingPlanComponent#saveBizReceiptAttachment"})
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存盘点单
        stocktakingPlanComponent.saveInfo(ctx);

        // 保存附件
        stocktakingPlanComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 保存盘点人签名
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#saveStocktakingUser"})
    @Transactional(rollbackFor = Exception.class)
    public void saveStocktakingUser(BizContext ctx) {

        // 保存盘点人签名
        stocktakingPlanComponent.saveStocktakingUser(ctx);
    }

    /**
     * 保存盘点单复盘备注和复盘状态
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    public void saveBinRemarkAndReviewStatus(BizContext ctx) {
        // 保存盘点单手工录入的复盘状态和备注
        stocktakingPlanComponent.saveBinRemarkAndReviewStatus(ctx);
    }

    /**
     * 提交盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#checkBySubmit", "stocktakingPlanComponent#checkBySubmitFirstCanBePosted",
        "stocktakingPlanComponent#freezeStorageBin", "stocktakingPlanComponent#isAppointMaterial",
        "stocktakingPlanComponent#submitInfo", "stocktakingPlanComponent#saveBizReceiptAttachment",
        "stocktakingPlanComponent#submitInfoFirstCanBePosted", "stocktakingPlanComponent#saveReceiptFirstTree"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 【首盘可过账模式】提交盘点单效验
        stocktakingPlanComponent.checkBySubmitFirstCanBePosted(ctx);

        // 【首盘可过账】提交盘点单
        stocktakingPlanComponent.submitInfoFirstCanBePosted(ctx);

        // 保存附件
        stocktakingPlanComponent.saveBizReceiptAttachment(ctx);

        // 冻结仓位 2023-05-25 根据邓健要求，取消盘点表提交时仓位盘点冻结逻辑
//        stocktakingPlanComponent.freezeStorageBin(ctx);

        // 【首盘可过账】首盘保存单据流
        stocktakingPlanComponent.saveReceiptFirstTree(ctx);
    }

    /**
     * 提交盘点人签名
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitStocktakingUser(BizContext ctx) {

        // 提交盘点人签名
        stocktakingPlanComponent.submitStocktakingUser(ctx);
    }
    /**
     * 删除盘点单
     * 
     * @param ctx 入参上下文 {"headId":"抬头表主键"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#checkDeleteStocktaking", "stocktakingPlanComponent#deleteInfo"})
    public void delete(BizContext ctx) {

        // 删除校验
        stocktakingPlanComponent.checkDeleteStocktaking(ctx);

        // 刪除盘点单
        stocktakingPlanComponent.deleteInfo(ctx);
    }

    /**
     * 【首盘可过账模式】认领盘点仓位
     * 
     * @param ctx 入参上下文 {"id":"行项目表主键"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#checkBinIsGet", "stocktakingPlanComponent#getBin"})
    public void getBin(BizContext ctx) {

        // 【首盘可过账】仓位是否被认领效验
        stocktakingPlanComponent.checkBinIsGet(ctx);

        // 【首盘可过账】多人盘点，认领逻辑
        stocktakingPlanComponent.getBin(ctx);
    }

    /**
     * 添加仓位库存
     *
     * @param ctx 入参上下文 {"po":"盘点单添加仓位入参对象"}
     * @return 物料列表
     */
    @Entrance(call = {"stocktakingPlanComponent#checkBinIsExist", "stocktakingPlanComponent#setStockTakingReplay",
        "stocktakingPlanComponent#setStockBinList", "stocktakingPlanComponent#saveStockBinList",
        "stocktakingPlanComponent#setReplayFirstCanBePosted"})
    public void addStockBin(BizContext ctx) {

        // 仓位是否存在效验
        stocktakingPlanComponent.checkBinIsExist(ctx);

        // 【首盘可过账模式】设置盘点单类型
        stocktakingPlanComponent.setReplayFirstCanBePosted(ctx);

        // 查询仓位库存列表
        stocktakingPlanComponent.setStockBinList(ctx);

        // 插入仓位库存
        stocktakingPlanComponent.saveStockBinList(ctx);
    }

    /**
     * 添加物料
     *
     * @param ctx 入参上下文 {"po":"盘点单添加物料入参对象"}
     * @return 物料列表
     */
    @Entrance(call = {"stocktakingPlanComponent#checkCanAddMat", "stocktakingPlanComponent#getMatList",
        "stocktakingPlanComponent#setMatImgByBin"})
    public void addMaterial(BizContext ctx) {

        // 【首盘可过账模式】添加物料校验，只有按仓位盘才能添加物料
        stocktakingPlanComponent.checkCanAddMat(ctx);

        // 搜索物料
        stocktakingPlanComponent.getMatList(ctx);

        // 设置物料图片信息
        stocktakingPlanComponent.setMatImgByBin(ctx);
    }

    /**
     * 盘点计数
     *
     * @param ctx 入参上下文 {"po":"盘点单物料明细列表传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#checkCountedStocktaking", "stocktakingPlanComponent#saveCount",
        "stocktakingPlanComponent#unfreezeStorageBin"})
    public void saveCount(BizContext ctx) {

        // 盘点计数效验
        stocktakingPlanComponent.checkCountedStocktaking(ctx);

        // 盘点计数 保存物料和单据状态
        stocktakingPlanComponent.saveCount(ctx);

        // 仓位解冻
        stocktakingPlanComponent.unfreezeStorageBin(ctx);
    }

    /**
     * 获取差异列表
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点差异列表
     */
    @Entrance(call = {"stocktakingPlanComponent#setDifferenceList"})
    public void getDifferenceList(BizContext ctx) {

        // 保存复盘备注和结果
        stocktakingPlanComponent.saveBinRemarkAndReviewStatus(ctx);

        // 获取差异列表
        stocktakingPlanComponent.setDifferenceList(ctx);
    }

    /**
     * 重新盘点
     *
     * @param ctx 入参上下文 {"po":"复盘入参对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#checkReInventory", "stocktakingPlanComponent#saveReInventory",
        "stocktakingPlanComponent#updateReceiptStatusByReInventory", "stocktakingPlanComponent#saveReInventoryFirstCanBePosted",
        "stocktakingPlanComponent#saveReceiptOtherTree"})
    public void saveReInventory(BizContext ctx) {

        // 校验是否所有的行项目都是已提交状态
        stocktakingPlanComponent.checkReInventory(ctx);

        // 【首盘可过账模式】重新盘点 生成新的单号 新的单据 物料按照批次托盘分组
        stocktakingPlanComponent.saveReInventoryFirstCanBePosted(ctx);

        // 重新盘点 更新原单据状态 已完成
        stocktakingPlanComponent.updateReceiptStatusByReInventory(ctx);

        // 【首盘可过账】复盘保存单据流
        stocktakingPlanComponent.saveReceiptOtherTree(ctx);
    }

    /**
     * 修改单据已完成状态
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingPlanComponent#updateStockTakeStatus"})
    public void updateReceiptStatus(BizContext ctx) {

        // 保存复盘备注和结果
        stocktakingPlanComponent.saveBinRemarkAndReviewStatus(ctx);

        // 是否存在复盘差异，有复盘差异时需要发起审批，审批通过后修改为已完成
        if (stocktakingPlanComponent.isNeedApproval(ctx)) {
            // 发起审批
            stocktakingPlanComponent.startWorkflow(ctx);
        } else {
            // 更新单据状态 已完成
            stocktakingPlanComponent.updateStockTakeStatus(ctx);
        }
    }

    /**
     * PDA-获取盘点单行项目列表
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 行项目列表
     */
    @Entrance(call = {"stocktakingPlanComponent#setItemListbyHeadId"})
    public void getItemList(BizContext ctx) {

        // PDA-获取盘点单行项目列表
        stocktakingPlanComponent.setItemListbyHeadId(ctx);
    }

    /**
     * PDA-获取盘点单物料明细列表
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 物料明细列表
     */
    @Entrance(call = {"stocktakingPlanComponent#setBinListByItemId", "stocktakingPlanComponent#setBatchImgByBin"})
    public void getBinList(BizContext ctx) {

        // PDA-获取盘点单物料明细列表
        stocktakingPlanComponent.setBinListByItemId(ctx);

        // 设置批次图片信息
        stocktakingPlanComponent.setBatchImgByBin(ctx);
    }

    /**
     * 盘点过账
     *
     * @param ctx 入参上下文 {"po":"单据行项目级别操作通用入参对象"}
     */
    @Entrance(call = {"stocktakingPlanComponent#checkPost", "stocktakingPlanComponent#generateInsDocToPost",
        "stocktakingPlanComponent#postToSap", "stocktakingPlanComponent#postStocktakingToIns"})
    public void post(BizContext ctx) {

        // 过账效验
        stocktakingPlanComponent.checkPost(ctx);

        // 生成ins凭证
        stocktakingPlanComponent.generateInsDocToPost(ctx);

        // 盘点sap过账
        stocktakingPlanComponent.postToSap(ctx);

        // 盘点instock过账
        stocktakingPlanComponent.postStocktakingToIns(ctx);
    }

    /**
     * 盘点结果导出
     *
     * @param ctx 入参上下文 {"headId":"单据行项目级别操作通用入参对象"}
     */
    public void exportStocktakingDetail(BizContext ctx) {
        // 查询单据盘点结果并导出
        stocktakingPlanComponent.exportStocktakingDetail(ctx);
    }

    /**
     * 盘点创建导出
     *
     * @param ctx 入参上下文 {"headId":"单据行项目级别操作通用入参对象"}
     */
    public void exportStockBinCreateDetail(BizContext ctx) {
        // 查询单据盘点结果并导出
        stocktakingPlanComponent.exportStocktakingCreateDetail(ctx);
    }
}