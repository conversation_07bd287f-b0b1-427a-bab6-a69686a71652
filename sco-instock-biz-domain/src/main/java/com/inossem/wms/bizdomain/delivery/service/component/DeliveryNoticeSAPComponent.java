package com.inossem.wms.bizdomain.delivery.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.util.UtilCollection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class DeliveryNoticeSAPComponent {


    @Autowired
    private DeliveryNoticeComponent deliveryNoticeComponent;


    @Transactional
    public void submitBeforePurchase(BizContext ctx){


        // 提交-校验送货通知入参
        deliveryNoticeComponent.checkSubmitData(ctx);

        // 提交送货通知
        deliveryNoticeComponent.submitDeliveryNotice(ctx);

        // 保存操作日志
        deliveryNoticeComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        deliveryNoticeComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        deliveryNoticeComponent.saveReceiptTree(ctx);

        //更新合同已发货数量
        deliveryNoticeComponent.writeBackContract(ctx); 

        //回写供应商箱件已发货数量
        deliveryNoticeComponent.writeBackSupplierCase(ctx);

        // 开启审批
        // deliveryNoticeComponent.startWorkFlow(ctx);

    }


    public void  submitAfterPurchase(BizContext ctx){
        deliveryNoticeComponent.autoApproval(ctx);
    }


    @Autowired
    private BizReceiptRegisterItemDataWrap registerItemDataWrap;

    @Autowired
    private BizReceiptRegisterHeadDataWrap registerHeadDataWrap;
    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Transactional
    public void closeDeliveryAfterPurchase(BizContext ctx){


        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 关闭送货通知: [30546]【内贸/在岸/油品送货】内贸送货单，成功关闭单据后，单据状态仍显示已完成
        deliveryNoticeComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());

        // 删除草稿状态到货登记

        List<Long> deliveryItemIds = headDTO.getItemList().stream()
                .map(BizReceiptDeliveryNoticeItemDTO::getId)
                .collect(Collectors.toList());

        // 4. 查询并校验到货登记
        List<BizReceiptRegisterItem> registerItems = registerItemDataWrap.list(
                new LambdaQueryWrapper<BizReceiptRegisterItem>()
                        .eq(BizReceiptRegisterItem::getIsDelete, 0)
                        .in(BizReceiptRegisterItem::getPreReceiptItemId, deliveryItemIds));

        if (UtilCollection.isNotEmpty(registerItems)) {
            // 获取到货登记抬头ID
            List<Long> registerHeadIds = registerItems.stream().filter(e->e.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()))
                    .map(BizReceiptRegisterItem::getHeadId)
                    .distinct()
                    .collect(Collectors.toList());


            if(UtilCollection.isNotEmpty(registerHeadIds)){
                // 删除到货登记
                registerItemDataWrap.update(
                        new UpdateWrapper<BizReceiptRegisterItem>()
                                .lambda()
                                .set(BizReceiptRegisterItem::getIsDelete, 1)
                                .in(BizReceiptRegisterItem::getHeadId, registerHeadIds)
                                .eq(BizReceiptRegisterItem::getItemStatus,EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                );

                registerHeadDataWrap.update(
                        new UpdateWrapper<BizReceiptRegisterHead>()
                                .lambda()
                                .set(BizReceiptRegisterHead::getIsDelete, 1)
                                .in(BizReceiptRegisterHead::getId, registerHeadIds)
                                .eq(BizReceiptRegisterHead::getReceiptStatus,EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                );

                // 删除单据流
                receiptRelationService.deleteReceiptTree(registerHeadIds, EnumReceiptType.ARRIVAL_REGISTER.getValue());
            }



        }


        // 回写合同已发货数量
        deliveryNoticeComponent.writeBackContractByClosed(ctx);
    }   

}
