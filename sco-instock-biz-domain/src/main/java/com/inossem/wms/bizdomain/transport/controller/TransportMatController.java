package com.inossem.wms.bizdomain.transport.controller;

import com.inossem.wms.bizdomain.transport.service.biz.TransportMatService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.erp.dto.ErpWbs;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 物料转码
 */

@RestController
@Api(tags = "转码管理")
public class TransportMatController {

    @Autowired
    private TransportMatService transportMatService;

    /**
     * 移动类型列表
     *
     *
     * @out vo 移动类型列表
     */
    @ApiOperation(value = "移动类型列表", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/getMoveTypeList")
    public BaseResult getMoveTypeList(BizContext ctx) {
        transportMatService.getMoveTypeList(ctx);
        MultiResultVO<DicMoveType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 页面初始化
     *
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/init")
    public BaseResult init(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportMatService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询库存 - 只选到物料
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询库存", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/getStock")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportMatService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 转储物料导入
     */
    @ApiOperation(value = "转储物料导入", notes = "转储物料导入", tags = {"转码管理-转储物料导入"})
    @PostMapping(path = "/transport-mat/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importMaterial(@RequestPart("file") MultipartFile file,@RequestPart("po") String po,BizContext ctx) {
        transportMatService.importMaterial(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo =ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表 - 分页", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/results")
    public BaseResult getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportMatService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 获取盘点人列表
     *
     * @return 用户列表
     */
    @ApiOperation(value = "获取整理人列表", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/user-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SysUserDTO>> getUserList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportMatService.getUserList(ctx);
        MultiResultVO<SysUserDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"转码管理"})
    @GetMapping(value = "/transport-mat/{id}")
    public BaseResult getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportMatService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportMatService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 提交
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "提交", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportMatService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 过账
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "过账", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/post")
    public BaseResult post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportMatService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"转码管理"})
    @DeleteMapping("/transport-mat/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportMatService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 冲销
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "冲销", tags = {"转码管理"})
    @PostMapping("/transport-mat/write-off")
    public BaseResult writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transportMatService.writeOff(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 获取WBS集合
     */
    @ApiOperation(value = "获取WBS集合", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/wbs")
    public BaseResult getWbsList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportMatService.getWbsList(ctx);
        MultiResultVO<ErpWbs> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 转码-阅知
     */
    @ApiOperation(value = "转码管理-阅知", tags = {"转码管理-阅知"})
    @PostMapping(value = "/transport-mat/review", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> review (@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportMatService.review(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(receiptCode);
    }

    /**
     * 审批回调手动调用
     * 参数结构
     * {
     *     "receiptHeadId":358775381098000,
     *     "approveStatus":10
     * }
     * <b>不对外提供，不对页面提供</b>
     */
    @ApiOperation(value = "审批回调手动调用", tags = {"转码管理"})
    @PostMapping(value = "/transport-mat/manual/approval-callback")
    public BaseResult approvalCallback(@RequestBody BizApprovalReceiptInstanceRelDTO approvalReceiptInstanceRelDTO, BizContext ctx) {
        transportMatService.manualApprovalCallback(approvalReceiptInstanceRelDTO,  ctx);
        return BaseResult.success();
    }

}