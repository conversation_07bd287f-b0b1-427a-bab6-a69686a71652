<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.task.dao.BizReceiptTaskItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskItem">
        <id column="id" property="id"/>
        <result column="head_id" property="headId"/>
        <result column="rid" property="rid"/>
        <result column="item_status" property="itemStatus"/>
        <result column="task_req_head_id" property="taskReqHeadId"/>
        <result column="task_req_item_id" property="taskReqItemId"/>
        <result column="source_type_id" property="sourceTypeId"/>
        <result column="source_bin_id" property="sourceBinId"/>
        <result column="source_cell_id" property="sourceCellId"/>
        <result column="target_type_id" property="targetTypeId"/>
        <result column="target_bin_id" property="targetBinId"/>
        <result column="target_cell_id" property="targetCellId"/>
        <result column="batch_id" property="batchId"/>
        <result column="qty" property="qty"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="modify_user_id" property="modifyUserId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, head_id, rid, item_status, task_req_head_id, task_req_item_id, source_type_id, source_bin_id, source_cell_id,
        target_type_id, target_bin_id, target_cell_id, batch_id, qty, is_delete, create_time, modify_time,submit_time, create_user_id, modify_user_id,submit_user_id
    </sql>


    <select id="getTaskItemByPreReceiptItemIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            biz_receipt_task_item task
        where
            task.task_req_item_id in (
                select
                    req.id
                from
                    biz_receipt_task_req_item req
                where
                req.pre_receipt_item_id in
                <foreach collection="list" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            )
    </select>

    <select id="queryByTaskReqItemId"  resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
                biz_receipt_task_item
            WHERE
                is_delete = 0 and task_req_item_id IN
                ( SELECT r.id FROM biz_receipt_task_req_item r WHERE r.pre_receipt_item_id = #{taskReqItemId} )
    </select>
    <select id="selectExportItemList"
            resultType="com.inossem.wms.common.model.bizdomain.task.vo.BizReceiptTaskItemExportVO">
        select
            head.receipt_code,
            head.create_user_id,
            head.create_time,
            biz_receipt_task_head.receipt_code as taskReceiptCode,
            biz_batch_info.case_code,
            biz_batch_info.supplier_name,
            item.mat_id,
            dm.unit_id,
            task_item.batch_id,
            task_item.target_bin_id,
            task_item.submit_time,
            task_item.submit_user_id,
            biz_batch_info.description,
            task_item.qty
        from biz_receipt_task_item task_item
            inner join biz_receipt_task_head on biz_receipt_task_head.id = task_item.head_id
            inner join biz_receipt_task_req_item item on item.id = task_item.task_req_item_id and
                item.head_id = task_item.task_req_head_id
            inner join biz_receipt_task_req_head head on head.id = item.head_id
            inner join biz_batch_info on biz_batch_info.id = task_item.batch_id
            inner join sys_user su on head.create_user_id = su.id
            left join dic_material dm on dm.id = item.mat_id
            <if test="po.preReceiptCode !=null and po.preReceiptCode !=''">
                left join biz_receipt_input_head as input_head on  item.pre_receipt_head_id =  input_head.id
                left join biz_receipt_output_head as output_head on  item.pre_receipt_head_id =  output_head.id
                left join biz_receipt_transport_head as transport_head on  item.pre_receipt_head_id =  transport_head.id
                left join biz_receipt_return_head as return_head on  item.pre_receipt_head_id =  return_head.id
            </if>
            <if test="po.preApplyReceiptCode !=null and po.preApplyReceiptCode !=''">
                left join biz_receipt_output_item as output_item on  item.pre_receipt_item_id =  output_item.id
                left join biz_receipt_apply_head as apply_head on output_item.pre_receipt_head_id = apply_head.id
            </if>
        where task_item.is_delete = 0
            and biz_receipt_task_head.is_delete = 0
            and item.is_delete = 0
            and head.is_delete = 0
        <if test="po.statusList!=null and po.statusList.size>0">
            and head.receipt_status in
            <foreach collection="po.statusList" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <!-- 单据号 -->
        <if test="po.receiptCode!=null and po.receiptCode !=''">
            AND head.receipt_code like CONCAT('%', #{po.receiptCode})
        </if>
        <!-- 单据类型 -->
        <if test="po.receiptType!=null">
            and head.receipt_type = #{po.receiptType}
        </if>
        <!-- 前序单据号 -->
        <if test="po.preReceiptCode !=null and po.preReceiptCode !=''">
            and (
            input_head.receipt_code= #{po.preReceiptCode} or
            output_head.receipt_code= #{po.preReceiptCode} or
            transport_head.receipt_code= #{po.preReceiptCode} or
            return_head.receipt_code= #{po.preReceiptCode}
            )
        </if>
        <!-- 前序申请单号 -->
        <if test="po.preApplyReceiptCode !=null and po.preApplyReceiptCode !=''">
            and apply_head.receipt_code= #{po.preApplyReceiptCode}
        </if>
        <!-- 创建人 -->
        <if test="po.createUserId !=null and po.createUserId !=''">
            and su.id = #{po.createUserId}
        </if>
        <if test="po.matCode !=null and po.matCode !=''">
            and dm.mat_code like concat('%', #{po.matCode} , '%')
        </if>
        <if test="po.matId !=null and po.matId !=''">
            and dm.id = #{po.matId}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND item.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by task_item.create_time desc
    </select>

    <update id="deleteByItemId">
        update biz_receipt_task_item set is_delete=#{flag} where id=#{id}
    </update>
</mapper>
