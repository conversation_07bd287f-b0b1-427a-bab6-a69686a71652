<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.room.dao.BizRoomMapper">
    <!--房间主数据 - 分页-->
    <select id="selectPageVo" resultType="com.inossem.wms.common.model.bizdomain.room.vo.BizRoomPageVO">
        SELECT
            biz_room.*,
            biz_room_usage_head.check_in_count
        FROM
            biz_room
        LEFT JOIN biz_room_usage_head
        ON biz_room.current_room_usage_head_id = biz_room_usage_head.id AND biz_room_usage_head.is_delete = 0
        ${ew.customSqlSegment}
        order by biz_room.room_code
    </select>
    <!-- 查询房间主数据导出数据 -->
    <select id="selectExportData" resultType="com.inossem.wms.common.model.bizdomain.room.vo.BizRoomExportVO">
        SELECT
            biz_room.*,
            biz_room_usage_head.check_in_count,
            biz_room_usage_head.applicant_user_code,
            biz_room_usage_head.applicant_user_name,
            biz_room_usage_head.start_usage_time,
            biz_room_usage_item.check_in_user_name,
            biz_room_usage_item.check_in_user_sex,
            biz_room_usage_item.check_in_user_id_number,
            biz_room_usage_item.check_in_user_passport_number,
            biz_room_usage_item.check_in_time
        FROM
            biz_room
        LEFT JOIN biz_room_usage_head
        ON biz_room.current_room_usage_head_id = biz_room_usage_head.id AND biz_room_usage_head.is_delete = 0
        LEFT JOIN biz_room_usage_item
        ON biz_room_usage_head.id = biz_room_usage_item.head_id AND biz_room_usage_item.is_delete = 0
            ${ew.customSqlSegment}
        order by biz_room.room_code, biz_room_usage_head.id, biz_room_usage_item.id
    </select>
    <!-- 查询房间当前入住人员信息 -->
    <select id="selectRoomCheckInUser" resultType="com.inossem.wms.common.model.bizdomain.room.vo.BizRoomCheckInUserVO">
        SELECT
            biz_room.id room_id,
            biz_room.room_code,
            biz_room.room_no,
            biz_room.bed_count,
            biz_room.current_room_usage_head_id room_usage_head_id,
            biz_room_usage_item.id room_usage_item_id,
            biz_room_usage_item.check_in_user_name,
            biz_room_usage_item.check_in_user_sex,
            biz_room_usage_item.check_in_user_id_number,
            biz_room_usage_item.check_in_user_passport_number,
            biz_room_usage_item.check_in_time,
            biz_room_usage_head.contract_id,
            biz_room_usage_head.contract_code,
            biz_room_usage_head.contract_name
        FROM
            biz_room
        INNER JOIN biz_room_usage_head
                  ON biz_room.current_room_usage_head_id = biz_room_usage_head.id AND biz_room_usage_head.is_delete = 0 AND biz_room.is_delete = 0
        INNER JOIN biz_room_usage_item
                  ON biz_room_usage_head.id = biz_room_usage_item.head_id AND biz_room_usage_item.is_delete = 0
            ${ew.customSqlSegment}
        order by biz_room.room_code, biz_room_usage_item.id
    </select>
</mapper>
