package com.inossem.wms.bizdomain.contract.service.biz;

import com.inossem.wms.bizdomain.contract.service.component.ContractConfirmComponent;
import com.inossem.wms.bizdomain.contract.service.component.ContractReceivingComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 服务工程确认单 服务层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-09
 */
@Service
@Slf4j
public class ContractConfirmService {

    @Autowired
    private ContractConfirmComponent contractConfirmComponent;

    @Autowired
    private ContractReceivingComponent contractReceivingComponent;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {

        // 设置按钮
        contractConfirmComponent.setInit(ctx);

        // 开启附件
        contractConfirmComponent.setExtendAttachment(ctx);

        // 开启操作日志
        contractConfirmComponent.setExtendOperationLog(ctx);
    }

    /**
     * 服务工程确认-分页
     *
     * @param ctx context
     */
    public void getPage(BizContext ctx) {

        // 分页查询
        contractConfirmComponent.getPage(ctx);
    }

    /**
     * 服务工程确认-详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {

        // 获取详情
        contractConfirmComponent.getInfo(ctx);

        // 开启附件
        contractConfirmComponent.setExtendAttachment(ctx);

        // 开启操作日志
        contractConfirmComponent.setExtendOperationLog(ctx);

        // 开启单据流
        contractConfirmComponent.setExtendRelation(ctx);

        // 开启审批
        contractConfirmComponent.setExtendWf(ctx);

    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存-校验服务工程确认入参
        contractConfirmComponent.checkSaveData(ctx);

        // 保存-服务工程确认单
        contractConfirmComponent.save(ctx);

        // 保存操作日志
        contractConfirmComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        contractConfirmComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交-校验服务工程确认入参
        contractConfirmComponent.checkSaveData(ctx);

        // 提交服务工程确认
        contractConfirmComponent.submit(ctx);

        // 保存操作日志
        contractConfirmComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        contractConfirmComponent.saveBizReceiptAttachment(ctx);

        // 发起审批
        contractConfirmComponent.startWorkFlow(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_CONTRACT_CONFIRM)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {

        // 审批回调
        contractConfirmComponent.approvalCallback(wfReceiptCo);
    }

    /**
     * 撤销
     *
     * @param ctx ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {

        // 撤销
        contractConfirmComponent.revoke(ctx);
    }

    /**
     * 删除
     *
     * @param ctx ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 删除
        contractConfirmComponent.delete(ctx);

        // 逻辑删除附件
        contractConfirmComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 查询合同
     *
     * @param ctx ctx
     */
    public void getContract(BizContext ctx) {

        // 查询合同
        contractReceivingComponent.getContract(ctx);
    }

    /**
     * 查询送货通知
     *
     * @param ctx ctx
     */
    public void getDelivery(BizContext ctx) {

        // 查询送货通知
        contractReceivingComponent.getDelivery(ctx);
    }

}
