package com.inossem.wms.bizdomain.unitized.service.biz;

import com.inossem.wms.bizdomain.unitized.service.component.UnitizedDeliveryNoticeComponent;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 成套设备到货通知 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-05
 */
@Service
public class UnitizedDeliveryNoticeService {
    
    @Autowired
    protected UnitizedDeliveryNoticeComponent unitizedDeliveryNoticeComponent;

    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @return 库存地点下拉框
     */
    public void getLocationList(BizContext ctx) {

        // 查询库存地点-根据选中的工厂id获取
        unitizedDeliveryNoticeComponent.getLocationList(ctx);

    }

    /**
     * 送货通知-初始化
     *
     * @param ctx 入参上下文
     */
    public void init(BizContext ctx) {

        // 页面初始化
        unitizedDeliveryNoticeComponent.setInit(ctx);

        // 开启附件
        unitizedDeliveryNoticeComponent.setExtendAttachment(ctx);

        // 开启操作日志
        unitizedDeliveryNoticeComponent.setExtendOperationLog(ctx);

        // 开启操单据流
        unitizedDeliveryNoticeComponent.setExtendRelation(ctx);

    }

    /**
     * 送货通知-分页
     *
     * @param ctx 入参上下文
     */
    public void getPage(BizContext ctx) {

        // 送货通知单-分页
        unitizedDeliveryNoticeComponent.getPage(ctx);
    }

    /**
     * 送货通知-详情
     *
     * @param ctx 入参上下文 {"id":"送货通知主键"}
     */
    public void getInfo(BizContext ctx) {

        // 送货通知单-详情
        unitizedDeliveryNoticeComponent.getInfo(ctx);

        // 开启附件
        unitizedDeliveryNoticeComponent.setExtendAttachment(ctx);

        // 开启操作日志
        unitizedDeliveryNoticeComponent.setExtendOperationLog(ctx);

        // 开启操单据流
        unitizedDeliveryNoticeComponent.setExtendRelation(ctx);

        // 开启审批
        unitizedDeliveryNoticeComponent.setExtendWf(ctx);
    }

    /**
     * 送货通知-保存
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存-校验送货通知入参
        unitizedDeliveryNoticeComponent.checkSaveData(ctx);

        // 保存-送货通知单
        unitizedDeliveryNoticeComponent.saveDeliveryNotice(ctx);

        // 保存操作日志
        unitizedDeliveryNoticeComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        unitizedDeliveryNoticeComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        unitizedDeliveryNoticeComponent.saveReceiptTree(ctx);
    }

    /**
     * 送货通知-提交
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交-校验送货通知入参
        unitizedDeliveryNoticeComponent.checkSubmitData(ctx);

        // 提交送货通知
        unitizedDeliveryNoticeComponent.submitDeliveryNotice(ctx);

        // 保存操作日志
        unitizedDeliveryNoticeComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        unitizedDeliveryNoticeComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        unitizedDeliveryNoticeComponent.saveReceiptTree(ctx);

        // 生成到货登记单
        unitizedDeliveryNoticeComponent.genArrivalRegister(ctx);

        // 开启审批
        //unitizedDeliveryNoticeComponent.startWorkFlow(ctx);
    }

    /**
     * 送货通知-删除
     *
     * @param ctx 入参上下文{"id":"主表id"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 删除前校验
        unitizedDeliveryNoticeComponent.checkDeleteDeliveryNotice(ctx);

        // 删除送货通知单
        unitizedDeliveryNoticeComponent.deleteDeliveryNotice(ctx);

        // 删除单据流
        unitizedDeliveryNoticeComponent.deleteReceiptTree(ctx);

        // 删除单据附件
        unitizedDeliveryNoticeComponent.deleteReceiptAttachment(ctx);
    }

    /**
     * 送货通知-撤销
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {
        // 撤销前校验
        unitizedDeliveryNoticeComponent.checkRevokeDeliveryNotice(ctx);
        // 撤销送货通知单
        unitizedDeliveryNoticeComponent.revokeDeliveryNotice(ctx);
        // 删除单据流
        unitizedDeliveryNoticeComponent.deleteReceiptTree(ctx);
    }

    /**
     * 送货通知-前续单据
     *
     * @param ctx 入参上下文
     */
    public void getReferReceiptItemList(BizContext ctx) {

        // 添加物料查询-基于采购订单
        unitizedDeliveryNoticeComponent.purchaseReceipt(ctx);
    }

    /**
     * 送货通知-导出
     */
    public void export(BizContext ctx) {
        unitizedDeliveryNoticeComponent.export(ctx);
    }
    
}
