package com.inossem.wms.bizdomain.matview.service.biz;

import java.util.List;

import com.inossem.wms.bizdomain.matview.service.component.BizMaterialViewAuditComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditDTO;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditFactoryDTO;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditNuclearDTO;
import com.inossem.wms.common.model.bizdomain.matview.po.BizMaterialViewAuditSavePO;
import com.inossem.wms.common.model.bizdomain.matview.po.BizMaterialViewAuditSearchPO;
import com.inossem.wms.common.model.bizdomain.matview.vo.BizMaterialViewAuditPageVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 物料主数据视图审批 服务接口
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Slf4j
@Service
public class BizMaterialViewAuditService {

    @Autowired
    protected BizMaterialViewAuditComponent bizMaterialViewAuditComponent;
    @Autowired
    private BizMaterialViewAuditFactoryService bizMaterialViewAuditFactoryService;
    @Autowired
    private BizMaterialViewAuditNuclearService bizMaterialViewAuditNuclearService;

    /**
     * 获取物料主数据视图审批 - 分页列表
     */
    public PageObjectVO<BizMaterialViewAuditPageVO> getPage(BizMaterialViewAuditSearchPO po) {
        return bizMaterialViewAuditComponent.getPage(po);
    }

    /**
     * 获取物料主数据视图审批详情
     *
     * @param ctx 物料主数据视图审批id
     * @return 物料主数据视图审批详情
     */
    public void get(BizContext ctx) {
        // 获取单据详情
        bizMaterialViewAuditComponent.get(ctx);
        // 开启附件
        bizMaterialViewAuditComponent.setExtendAttachment(ctx);
        // 开启操作日志
        bizMaterialViewAuditComponent.setExtendOperationLog(ctx);
        // 开启审批流
        bizMaterialViewAuditComponent.setExtendWf(ctx);
    }

    /**
     * 新增或修改方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        bizMaterialViewAuditComponent.addOrUpdate(ctx);
        // 保存操作日志
        bizMaterialViewAuditComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 数据校验
     */
    public void checkSave(BizMaterialViewAuditSavePO po) {
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilCollection.isNotEmpty(po.getFactoryViewDTOList())) {
            bizMaterialViewAuditFactoryService.check(po.getFactoryViewDTOList());
        }
        if (UtilCollection.isNotEmpty(po.getNuclearViewDTOList())) {
            bizMaterialViewAuditNuclearService.check(po.getNuclearViewDTOList());
        }
    }

    /**
     * 删除方法
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String remove(Long id) {
        bizMaterialViewAuditComponent.remove(id);
        return String.valueOf(id);
    }

    /**
     * 提交
     */
    public void submit(BizContext ctx) {
        BizMaterialViewAuditSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizMaterialViewAuditComponent.checkSubmit(po);
        bizMaterialViewAuditComponent.addOrUpdate(ctx);
        // 设置上下文单据日志 - 提交
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存操作日志
        bizMaterialViewAuditComponent.saveBizReceiptOperationLog(ctx);
        // 发起审批
        bizMaterialViewAuditComponent.startWorkFlow(ctx);
    }

    /**
     * 工厂物料导入
     */
    public List<BizMaterialViewAuditFactoryDTO> importFtyMat(BizContext ctx) {
        return bizMaterialViewAuditFactoryService.importData(ctx);
    }

    /**
     * 工厂物料导出
     */
    public void exportFtyMat(BizContext ctx) {
        bizMaterialViewAuditFactoryService.exportFtyMat(ctx);
    }

    /**
     * 核电物料导入
     */
    public List<BizMaterialViewAuditNuclearDTO> importNucMat(BizContext ctx) {
        return bizMaterialViewAuditNuclearService.importNucMat(ctx);
    }

    /**
     * 核电物料导出
     */
    public void exportNucMat(BizContext ctx) {
        bizMaterialViewAuditNuclearService.exportNucMat(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_CALLBACK_MATERIAL_VIEW_AUDIT)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 审批回调
        bizMaterialViewAuditComponent.approvalCallback(wfReceiptCo);

    }

    public void init(BizContext ctx) {
        bizMaterialViewAuditComponent.init(ctx);
        // 开启附件
        bizMaterialViewAuditComponent.setExtendAttachment(ctx);
        // 开启操作日志
        bizMaterialViewAuditComponent.setExtendOperationLog(ctx);
    }
}
