package com.inossem.wms.bizdomain.apply.dao;

import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchMatPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptToolVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptSearchPrePO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 申请单行项目明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
public interface BizReceiptApplyItemMapper extends WmsBaseMapper<BizReceiptApplyItem> {

    List<BizReceiptToolVO> getToolList(@Param("po") BizReceiptApplySearchMatPO po);

    /**
     * 查询借用申请单行项目信息
     *
     * @param po 查新条件
     * @return List<BizReceiptOutputItemDTO>
     */
    List<BizReceiptApplyItemDTO> getApplyReceiptItemList(@Param("po") BizReceiptSearchPrePO po);

    void updateStatusById(@Param("dto") BizReceiptApplyItemDTO dto);
    void updateByIds(@Param("itemStatus") int itemStatus, @Param("ids") List<Long> ids);
}
