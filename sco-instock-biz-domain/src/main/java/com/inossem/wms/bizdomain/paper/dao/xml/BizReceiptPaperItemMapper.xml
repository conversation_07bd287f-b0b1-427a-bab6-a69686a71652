<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.paper.dao.BizReceiptPaperItemMapper">

    <select id="getPage" resultType="com.inossem.wms.common.model.bizdomain.paper.dto.BizReceiptPaperItemDTO">
        SELECT
(SELECT sum(qty) from biz_receipt_require_item s where s.pre_receipt_item_id = biz_receipt_paper_item.id and s.item_status = 90) required_qty,
            pre_item.plent_call_type margin_category_code,
            dic_margin_category.margin_factor,
            biz_receipt_paper_item.qty paper_require_qty,
            biz_receipt_paper_head.receipt_code,
            biz_receipt_paper_head.submitflg submitflg_paper,
            biz_receipt_paper_head.file_code file_code_paper,
            biz_receipt_paper_head.inner_code inner_code_paper,
            biz_receipt_paper_head.version version_paper,
            biz_receipt_paper_head.file_mark file_mark_paper,
            biz_receipt_paper_head.status_code status_code_paper,
            biz_receipt_paper_head.proj_code proj_code_paper,
            biz_receipt_paper_head.island island_paper,
            biz_receipt_paper_head.term term_paper,
            biz_receipt_paper_head.paper_system paper_system_paper,
            biz_receipt_paper_head.major major_paper,
            biz_receipt_paper_head.area area_paper,
            biz_receipt_paper_head.factory_building factory_building_paper,
            biz_receipt_paper_head. LEVEL level_paper,
            biz_receipt_paper_head.room room_paper,
            biz_receipt_paper_head.design_company design_company_paper,
            biz_receipt_paper_head.type type_paper,
            biz_receipt_paper_head.note note_paper,
            biz_receipt_paper_head.unit unit_paper,
            biz_receipt_paper_head.source source_paper,
            biz_receipt_paper_head.updater updater_paper,
            biz_receipt_paper_head.last_update_time last_update_time_paper,
            biz_receipt_paper_head.new_version new_version_paper,
            biz_receipt_paper_head.file_name file_name_paper,
            biz_receipt_paper_item.head_id pre_receipt_head_id,
            biz_receipt_paper_item.id pre_receipt_item_id,
            biz_receipt_paper_item.receipt_type pre_receipt_type,
            biz_receipt_paper_item.*
        FROM
            biz_receipt_paper_head
        JOIN biz_receipt_paper_item ON biz_receipt_paper_head.id = biz_receipt_paper_item.head_id
        JOIN sys_user ON sys_user.id = biz_receipt_paper_head.create_user_id
LEFT JOIN biz_receipt_paper_head pre_head ON pre_head.receipt_type = 97
AND pre_head.file_code = biz_receipt_paper_head.file_code AND pre_head.version = biz_receipt_paper_head.version AND pre_head.file_mark = biz_receipt_paper_head.file_mark
LEFT JOIN biz_receipt_paper_item pre_item ON pre_head.id = pre_item.head_id AND pre_item.matnr = biz_receipt_paper_item.matnr
        LEFT JOIN dic_margin_category ON dic_margin_category.margin_category_code = pre_item.plent_call_type
            ${ew.customSqlSegment}
    </select>
</mapper>
