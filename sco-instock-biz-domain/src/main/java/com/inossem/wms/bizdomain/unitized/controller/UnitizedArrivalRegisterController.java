package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedArrivalRegisterService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptRegisterSearchPO;
import com.inossem.wms.common.model.bizdomain.register.vo.BizRecieptRegisterPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.enums.CheckRejectResultMapVO;
import com.inossem.wms.common.model.common.enums.CheckResultMapVO;
import com.inossem.wms.common.model.common.enums.register.VisualCheckMapVO;
import com.inossem.wms.common.model.masterdata.car.entity.DicCar;
import com.inossem.wms.common.model.masterdata.car.entity.DicCarType;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成套设备到货登记 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-07
 */
@RestController
public class UnitizedArrivalRegisterController {
    
    @Autowired
    protected UnitizedArrivalRegisterService unitizedArrivalRegisterService;

    /**
     * 查询车辆类型下拉
     *
     * @return 车辆类型下拉框
     */
    @ApiOperation(value = "查询车辆类型下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/unitized/register/arrival-register/car-type-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicCarType>> getCarTypeDown(BizContext ctx) {
        unitizedArrivalRegisterService.getCarTypeDown(ctx);
        MultiResultVO<DicCarType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询车辆下拉
     *
     * @return 车辆下拉框
     */
    @ApiOperation(value = "查询车辆下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/unitized/register/arrival-register/car-down/{code}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicCar>> getCarDown(@PathVariable("code") String code, BizContext ctx) {
        unitizedArrivalRegisterService.getCarDown(ctx);
        MultiResultVO<DicCar> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询箱件状态下拉
     *
     * @return 箱件状态下拉框
     */
    @ApiOperation(value = "查询箱件状态下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/unitized/register/arrival-register/visual-check-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<VisualCheckMapVO>> getVisualCheckDown(BizContext ctx) {
        unitizedArrivalRegisterService.getVisualCheckDown(ctx);
        MultiResultVO<VisualCheckMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 查询检查结果下拉
     *
     * @return 检查结果下拉框
     */
    @ApiOperation(value = "查询检查结果下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/unitized/register/arrival-register/check_result-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<CheckResultMapVO>> getCheckResultDown(BizContext ctx) {
        unitizedArrivalRegisterService.getCheckResultDown(ctx);
        MultiResultVO<CheckResultMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 查询检查结果拒绝原因下拉
     *
     * @return 检查结果下拉框
     */
    @ApiOperation(value = "查询检查结果拒绝原因下拉", tags = {"送货管理-到货登记"})
    @GetMapping(path = "/unitized/register/arrival-register/check_reject_result-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<CheckRejectResultMapVO>> getCheckRejectResultDown(BizContext ctx) {
        unitizedArrivalRegisterService.getCheckRejectResultDown(ctx);
        MultiResultVO<CheckRejectResultMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 查询到货登记单列表-分页
     *
     * @param po 登记单分页查询入参
     * @return 到货登记单列表
     */
    @ApiOperation(value = "查询到货登记单列表-分页", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/unitized/register/arrival-register/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizRecieptRegisterPageVO>> getPage(@RequestBody BizReceiptRegisterSearchPO po, BizContext ctx) {
        unitizedArrivalRegisterService.getPage(ctx);
        PageObjectVO<BizRecieptRegisterPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询到货登记单详情
     *
     * @param id 到货登记单抬头表主键
     * @return 到货登记单详情
     */
    @ApiOperation(value = "查询到货登记单详情", tags = {"送货管理-到货登记"})
    @GetMapping(value = "/unitized/register/arrival-register/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRegisterHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedArrivalRegisterService.getInfo(ctx);
        BizResultVO<BizReceiptRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 到货登记-保存
     *
     * @param po 保存到货登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-保存", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/unitized/register/arrival-register/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        unitizedArrivalRegisterService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 到货登记-同步DTS
     *
     * @param po 保存到货登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-保存", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/unitized/register/arrival-register/syncDTS", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> syncDTS(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        unitizedArrivalRegisterService.syncDTS(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 到货登记-提交
     *
     * @param po 提交到货登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-提交", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/unitized/register/arrival-register/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        unitizedArrivalRegisterService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 到货登记-过账
     *
     * @param po 提交到货登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-过账", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/unitized/register/arrival-register/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult post(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        unitizedArrivalRegisterService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 到货登记-删除
     *
     * @param id 到货登记单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "到货登记-删除", tags = {"送货管理-到货登记"})
    @DeleteMapping(value = "/unitized/register/arrival-register/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        unitizedArrivalRegisterService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_DELETE_SUCCESS, receiptCode);
    }

    /**
     * 到货登记-冲销
     *
     * @param po 查询条件
     * @return 到货登记行项目
     */
    @ApiOperation(value = "到货登记-冲销", tags = {"送货管理-到货登记"})
    @PostMapping(value = "/unitized/register/arrival-register/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        unitizedArrivalRegisterService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

}
