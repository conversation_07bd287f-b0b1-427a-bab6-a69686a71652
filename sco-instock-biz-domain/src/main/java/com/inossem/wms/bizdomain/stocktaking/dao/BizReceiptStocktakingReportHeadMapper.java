package com.inossem.wms.bizdomain.stocktaking.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingReportHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingReportHead;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingReportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportExportVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportHeadPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘点报告抬头表 Mapper 接口
 * </p>
 */
public interface BizReceiptStocktakingReportHeadMapper extends WmsBaseMapper<BizReceiptStocktakingReportHead> {

    /**
     * 查询盘点报告抬头表分页列表
     * @param page 分页参数
     * @param po 条件
     * @return
     */
    List<BizReceiptStocktakingReportHeadPageVO> selectBizReceiptStocktakingReportHeadPageVOList(IPage<BizReceiptStocktakingReportHeadPageVO> page, @Param("po") BizReceiptStocktakingReportHeadSearchPO po);
    
    /**
     * 查询库存对比数据
     */
    List<BizReceiptStocktakingReportExportVO> queryStocktakingReportExcel(@Param("po") BizReceiptStocktakingReportHeadDTO po);

    /**
     * 查询盘点对比结果
     */
    List<BizReceiptStocktakingReportExportVO> queryStocktakingResultReportExcel(@Param("po") BizReceiptStocktakingReportHeadDTO po);
}
