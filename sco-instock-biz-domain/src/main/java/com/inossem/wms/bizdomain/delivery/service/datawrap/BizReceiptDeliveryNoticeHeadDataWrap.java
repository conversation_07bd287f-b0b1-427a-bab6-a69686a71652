package com.inossem.wms.bizdomain.delivery.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.delivery.dao.BizReceiptDeliveryNoticeHeadMapper;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.vo.BizReceiptDeliveryNoticeListVo;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 送货通知抬头表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Service
public class BizReceiptDeliveryNoticeHeadDataWrap extends BaseDataWrap<BizReceiptDeliveryNoticeHeadMapper, BizReceiptDeliveryNoticeHead> {

    /**
     * 送货通知单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 送货通知单
     */
    public IPage<BizReceiptDeliveryNoticeListVo> getDeliveryNoticePageVo(IPage<BizReceiptDeliveryNoticeListVo> pageData,
                                                                         WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> pageWrapper) {
        return pageData.setRecords(this.baseMapper.selectDeliveryNoticePageVo(pageData, pageWrapper));
    }
    /**
     * 送货通知单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 送货通知单
     */
    public IPage<BizReceiptDeliveryNoticeListVo> getDeliveryNoticePageVoUnitized(IPage<BizReceiptDeliveryNoticeListVo> pageData,
                                                                         WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> pageWrapper) {
        return pageData.setRecords(this.baseMapper.selectDeliveryNoticePageVoUnitized(pageData, pageWrapper));
    }
}
