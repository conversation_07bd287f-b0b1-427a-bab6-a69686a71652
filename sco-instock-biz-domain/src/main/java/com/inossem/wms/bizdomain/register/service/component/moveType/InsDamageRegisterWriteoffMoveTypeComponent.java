package com.inossem.wms.bizdomain.register.service.component.moveType;

import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 遗失登记 生成ins冲销凭证
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-14
 */
@Service
public class InsDamageRegisterWriteoffMoveTypeComponent {

    /**
     * 生成记账ins凭证
     *
     * @param headDTO 登记单
     * @return insMoveTypeVo 记账ins凭证
     */
    public StockInsMoveTypeDTO generatePostInsDoc(BizReceiptRegisterHeadDTO headDTO) {
        StockInsMoveTypeDTO insMoveTypeVo = new StockInsMoveTypeDTO();
        List<BizReceiptRegisterItemDTO> itemDTOList = headDTO.getItemList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            return insMoveTypeVo;
        }
        Integer insDocRid = 1;
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (BizReceiptRegisterItemDTO itemDTO : itemDTOList) {
            // 批次库存增加
            StockInsDocBatch insDocBatch = new StockInsDocBatch();
            insDocBatch.setInsDocRid(insDocRid.toString());
            insDocBatch.setMatId(itemDTO.getMatId());
            insDocBatch.setBatchId(itemDTO.getBatchId());
            insDocBatch.setFtyId(itemDTO.getFtyId());
            insDocBatch.setLocationId(itemDTO.getLocationId());
            insDocBatch.setUnitId(itemDTO.getUnitId());
            insDocBatch.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBatch.setDocDate(new Date());
            insDocBatch.setPostingDate(new Date());
            insDocBatch.setMoveTypeId(0L);
            insDocBatch.setMoveQty(itemDTO.getQty());
            insDocBatch.setDebitCredit(Const.DEBIT_S_ADD);
            insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBatch.setPreReceiptHeadId(headDTO.getId());
            insDocBatch.setPreReceiptItemId(itemDTO.getId());
            insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
            insDocBatch.setPreReceiptBinId(0L);
            insDocBatch.setPreReceiptType(EnumReceiptType.LOSE_REGISTER.getValue());
            insDocBatch.setReferReceiptHeadId(headDTO.getId());
            insDocBatch.setReferReceiptItemId(itemDTO.getId());
            insDocBatch.setReferReceiptType(EnumReceiptType.LOSE_REGISTER.getValue());
            insDocBatch.setMatDocCode(null);
            insDocBatch.setMatDocRid(null);
            insDocBatch.setMatDocYear(null);
            insDocBatch.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            insDocBatch.setCreateUserId(itemDTO.getCreateUserId());
            insDocBatchList.add(insDocBatch);

            // Z06类型仓位增加
            StockInsDocBin insDocBin = new StockInsDocBin();
            insDocBin.setInsDocRid(insDocRid.toString());
            insDocBin.setMatId(itemDTO.getMatId());
            insDocBin.setBatchId(itemDTO.getBatchId());
            insDocBin.setFtyId(itemDTO.getFtyId());
            insDocBin.setLocationId(itemDTO.getLocationId());
            insDocBin.setUnitId(itemDTO.getUnitId());
            insDocBin.setDecimalPlace(itemDTO.getDecimalPlace());
            insDocBin.setMoveQty(itemDTO.getQty());
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            insDocBin.setPreReceiptHeadId(headDTO.getId());
            insDocBin.setPreReceiptItemId(itemDTO.getId());
            insDocBin.setPreReceiptBinId(0L);
            insDocBin.setPreReceiptType(EnumReceiptType.LOSE_REGISTER.getValue());
            insDocBin.setReferReceiptHeadId(headDTO.getId());
            insDocBin.setReferReceiptItemId(itemDTO.getId());
            insDocBin.setReferReceiptType(EnumReceiptType.LOSE_REGISTER.getValue());
            insDocBin.setMatDocCode(null);
            insDocBin.setMatDocRid(null);
            insDocBin.setMatDocYear(null);
            insDocBin.setIsWriteOff(EnumRealYn.FALSE.getIntValue());
            insDocBin.setTypeId(itemDTO.getTypeId());
            insDocBin.setBinId(itemDTO.getBinId());
            insDocBin.setWhId(itemDTO.getWhId());
            insDocBin.setCellId(0L);
            insDocBin.setCreateUserId(itemDTO.getCreateUserId());
            insDocBinList.add(insDocBin);
            insDocRid++;
        }
        insMoveTypeVo.setInsDocBatchList(insDocBatchList);
        insMoveTypeVo.setInsDocBinList(insDocBinList);
        return insMoveTypeVo;
    }

}
