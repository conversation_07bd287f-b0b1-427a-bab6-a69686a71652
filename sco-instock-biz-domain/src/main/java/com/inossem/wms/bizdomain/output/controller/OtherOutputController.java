package com.inossem.wms.bizdomain.output.controller;

import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.inossem.wms.bizdomain.output.service.biz.OtherOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import io.swagger.annotations.ApiOperation;

/**
 * 其他出库controller
 *
 * <AUTHOR>
 * @date 2021/4/7 9:28
 */

@RestController
@Api(tags = "出库管理-其他出库")
public class OtherOutputController {

    @Autowired
    private OtherOutputService otherOutputService;

    @ApiOperation(value = "其他出库创建", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/init")
    public BaseResult init(BizContext ctx) {
        otherOutputService.init(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "其他出库查询列表（分页）", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptOutputQueryListPO po,
        BizContext ctx) {
        otherOutputService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "其他出库查询物料库存", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        otherOutputService.getMatStock(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @ApiOperation(value = "其他出库查询物料库存", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/mat-stock/list-same-time")
    public BaseResult<MatStockDTO> getMatStockSameTime(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        otherOutputService.getMatStockSameTime(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @ApiOperation(value = "其他出库获取配货信息", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        otherOutputService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }

    @ApiOperation(value = "其他出库详情", tags = {"出库管理-其他出库"})
    @GetMapping(value = "/outputs/other-output/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        otherOutputService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "其他出库保存", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/save")
    public BaseResult save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        otherOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "其他出库提交", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/submit")
    public BaseResult submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        otherOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "其他出库提交 同时模式", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/submitSameTime")
    public BaseResult submitSameTime(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        otherOutputService.submitSameTime(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "其他出库过账", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/post")
    public BaseResult post(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        otherOutputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_POST_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "其他出库冲销", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        otherOutputService.writeOff(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "其他出库删除", tags = {"出库管理-其他出库"})
    @DeleteMapping(value = "/outputs/other-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        otherOutputService.delete(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_DELETE_SUCCESS, headDTO.getReceiptCode());
    }


    @ApiOperation(value = "其他出库查所有仓位库存", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/all-item-info")
    public BaseResult getAllItemInfo(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        otherOutputService.getAllItemInfo(ctx);
        MultiResultVO<StockBinDTO> stockBinDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(stockBinDTOList);
    }


    @ApiOperation(value = "其他出库查询列表（分页）", tags = {"出库管理-其他出库"})
    @PostMapping(value = "/outputs/other-output/export")
    public BaseResult<?> export(@RequestBody BizReceiptOutputQueryListPO po,
                                                                    BizContext ctx) {
        otherOutputService.export(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }
}