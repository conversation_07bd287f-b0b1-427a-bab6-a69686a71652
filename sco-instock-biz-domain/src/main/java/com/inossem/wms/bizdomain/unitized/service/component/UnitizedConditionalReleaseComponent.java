package com.inossem.wms.bizdomain.unitized.service.component;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.inconformity.EnumDifferentType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityItem;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 成套设备-有条件放行
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UnitizedConditionalReleaseComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    protected BizReceiptInconformityHeadDataWrap bizReceiptInconformityHeadDataWrap;

    @Autowired
    protected BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected BizCommonService bizCommonService;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInconformityHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptInconformityHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_CONDITIONAL_RELEASE.getValue()).setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO().setRelationRequired(true).setAttachmentRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true)
        );
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 分页
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptInconformityPageVO> page = po.getPageObj(BizReceiptInconformityPageVO.class);
        CurrentUser user = ctx.getCurrentUser();
        bizReceiptInconformityHeadDataWrap.getPageVOListUnitized(page, po, user);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取不符合项通知单详情
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(bizReceiptInconformityHeadDataWrap.getById(headId), BizReceiptInconformityHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        List<BizReceiptWaybill> waybillList = bizReceiptWaybillDataWrap.list(new QueryWrapper<BizReceiptWaybill>().lambda().eq(BizReceiptWaybill::getConditionalReleaseHeadId, headDTO.getId()));
        List<BizReceiptWaybillDTO> waybillDTOList = UtilCollection.toList(waybillList, BizReceiptWaybillDTO.class);
        dataFillService.fillAttr(waybillDTOList);
        headDTO.setQuailtyNoticeWaybillDTOList(waybillDTOList);
        SysUser purchaseUser = dictionaryService.getSysUserCacheByuserCode(headDTO.getItemList().get(0).getPurchaseUserCode());
        String purchaseUserName = headDTO.getItemList().get(0).getPurchaseUserCode();
        if (!(purchaseUser == null || purchaseUser.getUserName() == null || purchaseUser.getUserName() == "")) {
            purchaseUserName = purchaseUser.getUserName();
        }
        // 属性填充
        headDTO.setPurchaseUserCode(headDTO.getItemList().get(0).getPurchaseUserCode())
                .setPurchaseUserName(purchaseUserName);
        // 差异通知填充固定资产物料描述属性
        List<Long> referReceiptItemIdList = headDTO.getItemList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).map(BizReceiptInconformityItemDTO::getReferReceiptItemId).collect(Collectors.toList());

        if (referReceiptItemIdList.size() > 0) {
            QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, referReceiptItemIdList);
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);

            Map<Long, ErpPurchaseReceiptItem> erpPurchaseMap = erpPurchaseReceiptItemList.stream()
                    .collect(Collectors.toMap(ErpPurchaseReceiptItem::getId, obj -> obj));

            headDTO.getItemList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(itemDTO -> {
                ErpPurchaseReceiptItem erpPurchaseList = erpPurchaseMap.get(itemDTO.getReferReceiptItemId());
                if (null != erpPurchaseList && "1".equals(erpPurchaseList.getSubjectType())) {
                    itemDTO.setMatName(erpPurchaseList.getMatNameBack());
                }
            });

        }
        if (headDTO.getQuailtyNoticeWaybillDTOList() != null) {
            headDTO.getQuailtyNoticeWaybillDTOList().forEach(p -> {
                p.setTotalPrice(p.getUnqualifiedQty().multiply(p.getPrice()));
            });
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置不符合项通知单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     */
    public ButtonVO setButton(BizReceiptInconformityHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        return buttonVO;
    }

    /**
     * 提交
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交寿维保单时赋值提交时间提交人
        po.setSubmitTime(UtilDate.getNow());
        po.setSubmitUserId(user.getId());
        if (po.getWriteOffPostingDate() == null) {
            po.setWriteOffPostingDate(po.getPostingDate());
        }
        po.getQuailtyNoticeWaybillDTOList().forEach(p -> p.setQWriteOffPostingDate(po.getWriteOffPostingDate()).setQWriteOffReason(po.getWriteOffReason()).setQWriteOffQty(p.getUnqualifiedQty()));
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存不符合项通知单
        this.save(ctx);
    }

    /**
     * 保存
     */
    public void save(BizContext ctx) {
        // 入参上下文 - 要保存的不符合项单
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        headDTO.setReceiptStatus(receiptStatus);
        headDTO.setCreateUserId(user.getId());
        headDTO.setModifyUserId(user.getId());
        headDTO.setCreateTime(null);
        headDTO.setModifyTime(UtilDate.getNow());
        if(UtilString.isNullOrEmpty(headDTO.getDeliveryNoticeDescribe())){
            headDTO.setDeliveryNoticeDescribe(headDTO.getQuailtyNoticeWaybillDTOList().get(0).getDeliveryNoticeDescribe());
        }
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新不符合项单
            bizReceiptInconformityHeadDataWrap.updateDtoById(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            receiptCode = "SK" + headDTO.getUnit() + "-CWH5" + DateUtil.thisYear() + "-APS-Y" + bizCommonService.getNextSequence(EnumSequenceCode.SEQUENCE_UNITIZED_CONDITIONAL_RELEASE.getValue());
            headDTO.setReceiptCode(receiptCode);
            bizReceiptInconformityHeadDataWrap.saveDto(headDTO);
            headDTO.getItemList().forEach(o->o.setId(null));
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        Map<Long, List<BizReceiptWaybillDTO>> waybillMap = headDTO.getQuailtyNoticeWaybillDTOList().stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getSignInspectItemId));
        for (BizReceiptInconformityItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            itemDTO.setItemStatus(receiptStatus);
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
            BigDecimal qty = new BigDecimal(0);
            Long preReceiptItemId = itemDTO.getPreReceiptItemId();
            List<BizReceiptWaybillDTO> waybillDTOListMain = waybillMap.get(preReceiptItemId);
            if (!CollectionUtils.isEmpty(waybillDTOListMain)) {
                for (BizReceiptWaybillDTO waybill : waybillDTOListMain) {
                    qty = qty.add(waybill.getUnqualifiedQty().multiply(waybill.getPrice()));
                }
            }
            itemDTO.setQty(qty);
        }
        bizReceiptInconformityItemDataWrap.saveOrUpdateBatchDto(headDTO.getItemList());
        /* ********************** item处理结束 *************************/
        /* ********************** waybill处理开始 *************************/
        // 行项目按行号分组
        Map<Long, List<BizReceiptInconformityItemDTO>> itemMap = headDTO.getItemList().stream().collect(Collectors.groupingBy(BizReceiptInconformityItemDTO::getPreReceiptItemId));
        for (BizReceiptWaybillDTO waybillDTO : headDTO.getQuailtyNoticeWaybillDTOList()) {
            if (UtilNumber.isEmpty(waybillDTO.getPreId())) {
                waybillDTO.setConditionalReleaseHeadId(headDTO.getId());
                waybillDTO.setConditionalReleaseItemId(itemMap.get(waybillDTO.getSignInspectItemId()).get(0).getId());
                waybillDTO.setConditionalReleaseItemRid(itemMap.get(waybillDTO.getSignInspectItemId()).get(0).getRid());
                waybillDTO.setPreId(waybillDTO.getId());
                waybillDTO.setId(null);
                // 前序单据headid处理
                waybillDTO.setDeliveryNoticeHeadId(null);
                waybillDTO.setArrivalRegisterHeadId(null);
                waybillDTO.setBoxPlanHeadId(null);
                waybillDTO.setDistributeInspectHeadId(null);
                waybillDTO.setSignInspectHeadId(null);
                waybillDTO.setNumberInconformityNoticeHeadId(null);
                waybillDTO.setNumberInconformityMaintainHeadId(null);
                waybillDTO.setMoreNumberInconformityNoticeHeadId(null);
                waybillDTO.setMoreNumberInconformityMaintainHeadId(null);
                waybillDTO.setInspectInputHeadId(null);
                waybillDTO.setInspectInputWriteOffHeadId(null);
                // 前序单据itemid处理
                waybillDTO.setDeliveryNoticeItemId(null);
                waybillDTO.setArrivalRegisterItemId(null);
                waybillDTO.setBoxPlanItemId(null);
                waybillDTO.setDistributeInspectItemId(null);
                waybillDTO.setSignInspectItemId(null);
                waybillDTO.setNumberInconformityNoticeItemId(null);
                waybillDTO.setNumberInconformityMaintainItemId(null);
                waybillDTO.setMoreNumberInconformityNoticeItemId(null);
                waybillDTO.setMoreNumberInconformityMaintainItemId(null);
                waybillDTO.setInspectInputItemId(null);
                waybillDTO.setInspectInputWriteOffItemId(null);
            }
        }
        bizReceiptWaybillDataWrap.saveOrUpdateBatchDto(headDTO.getQuailtyNoticeWaybillDTOList());

        /* ********************** waybill处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(headDTO);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的不符合项单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 根据解决方案处理单据
     */
    public boolean handleReceiptBySolveReasonNew(BizContext ctx) {
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 生成验收入库单(冻结)
        genInspectInput(po, po.getItemList(), po.getQuailtyNoticeWaybillDTOList(), ctx.getCurrentUser());
        return true;
    }

    /**
     * 原运单更新累计不合格数量(已生成质量差异)
     */
    public void updateWaybillByPreId(BizContext ctx) {
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptWaybillDTO> waybillDTOList = po.getQuailtyNoticeWaybillDTOList();
        List<Long> preIdList = waybillDTOList.stream().map(o -> o.getPreId()).collect(Collectors.toList());
        List<BizReceiptWaybill> preWaybillList = bizReceiptWaybillDataWrap.list(new QueryWrapper<BizReceiptWaybill>().lambda().in(BizReceiptWaybill::getId, preIdList));
        List<BizReceiptWaybill> updateList = new ArrayList<>();
        for (BizReceiptWaybillDTO waybillDTO : waybillDTOList) {
            for (BizReceiptWaybill preWaybill : preWaybillList) {
                if (waybillDTO.getPreId().equals(preWaybill.getId())) {
                    BizReceiptWaybill updateWaybill = new BizReceiptWaybill();
                    updateWaybill.setId(preWaybill.getId());
                    // 累计不合格数量 = 累计不合格数量 + 本次不合格数量
                    updateWaybill.setTotalUnqualifiedQty(preWaybill.getTotalUnqualifiedQty().add(waybillDTO.getUnqualifiedQty()));
                    updateList.add(updateWaybill);
                }
            }
        }
        bizReceiptWaybillDataWrap.updateBatchDtoById(updateList);
    }

    /**
     * 生成验收入库单(冻结)
     */
    private void genInspectInput(BizReceiptInconformityHeadDTO headDTO, List<BizReceiptInconformityItemDTO> itemList, List<BizReceiptWaybillDTO> quailtyWaybillDTOList, CurrentUser currentUser) {
        if (CollectionUtils.isEmpty(quailtyWaybillDTOList)) {
            return;
        }
        // 查询采购订单行项目
        QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, itemList.stream().map(p -> p.getReferReceiptItemId()).collect(Collectors.toList()));
        List<ErpPurchaseReceiptItem> purchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);
        // 装载验收入库单head
        BizReceiptInputHeadDTO inspectInputHead = new BizReceiptInputHeadDTO();
        // 装载验收入库单item
        List<BizReceiptInputItemDTO> inspectInputItemList = new ArrayList<>(itemList.size());
        /* ******** 入库单head设置 ******** */
        inspectInputHead.setReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue()).setWaybillDTOList(quailtyWaybillDTOList).setDeliveryNoticeDescribe(headDTO.getDeliveryNoticeDescribe())
                .setUnit(headDTO.getUnit());
        Map<Long, List<BizReceiptWaybillDTO>> waybillMapMain = quailtyWaybillDTOList.stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getConditionalReleaseItemId));
        /* ******** 入库单item设置 ******** */
        List<Long> idList = new ArrayList<>(quailtyWaybillDTOList.size());
        for (BizReceiptInconformityItemDTO itemDTO : itemList) {
            BizReceiptInputItemDTO inspectInputItem = new BizReceiptInputItemDTO();
            inspectInputItem = UtilBean.newInstance(itemDTO, inspectInputItem.getClass());
            inspectInputItem.setId(null);
            inspectInputItem.setHeadId(null);
            BigDecimal qty = new BigDecimal(0);
            Long preReceiptItemId = itemDTO.getId();
            List<BizReceiptWaybillDTO> waybillDTOListMain = waybillMapMain.get(preReceiptItemId);
            if (!CollectionUtils.isEmpty(waybillDTOListMain)) {
                for (BizReceiptWaybillDTO waybill : waybillDTOListMain) {
                    qty = qty.add(waybill.getUnqualifiedQty().multiply(waybill.getPrice()));
                }
            } else {
                continue;
            }
            inspectInputItem.setQty(qty);
            inspectInputItem.setPreReceiptHeadId(headDTO.getId());
            inspectInputItem.setPreReceiptItemId(preReceiptItemId);
            // 前置单据类型-有条件放行
            inspectInputItem.setPreReceiptType(EnumReceiptType.UNITIZED_CONDITIONAL_RELEASE.getValue());
            inspectInputItem.setPreMatDocCode(itemDTO.getMatDocCode());
            inspectInputItem.setPreMatDocRid(itemDTO.getMatDocRid());
            inspectInputItem.setPreMatDocYear(itemDTO.getMatDocYear());
            inspectInputItem.setMatDocCode(null);
            inspectInputItem.setMatDocRid(null);
            inspectInputItem.setMatDocYear(null);
            inspectInputItem.setPostingDate(null);
            inspectInputItem.setDocDate(null);
            inspectInputItem.setIsPost(null);
            /* ******** 设置批次信息 ******** */
            for (ErpPurchaseReceiptItem purchaseReceiptItem : purchaseReceiptItemList) {
                if (itemDTO.getReferReceiptItemId().equals(purchaseReceiptItem.getId())) {
                    for (BizReceiptWaybillDTO waybillDTO : quailtyWaybillDTOList) {
                        if (itemDTO.getId().equals(waybillDTO.getConditionalReleaseItemId())) {
                            BizReceiptWaybill preWaybill = bizReceiptWaybillDataWrap.getById(waybillDTO.getPreId());
                            inspectInputItem.setDeliveryNoticeHeadId(preWaybill.getDeliveryNoticeHeadId());
                            inspectInputItem.setDeliveryNoticeItemId(preWaybill.getDeliveryNoticeItemId());
                            inspectInputItem.setArrivalRegisterHeadId(preWaybill.getArrivalRegisterHeadId());
                            inspectInputItem.setArrivalRegisterItemId(preWaybill.getArrivalRegisterItemId());
                            BigDecimal remainder = waybillDTO.getRemainder();
                            if (remainder.compareTo(BigDecimal.ZERO) > 0) {
                                Long id = waybillDTO.getId();
                                idList.add(id);
                            }
                            BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                            batchInfoDTO = UtilBean.newInstance(purchaseReceiptItem, batchInfoDTO.getClass());
                            batchInfoDTO.setId(null);
                            batchInfoDTO.setPurchaseReceiptHeadId(inspectInputItem.getReferReceiptHeadId());
                            batchInfoDTO.setPurchaseReceiptItemId(inspectInputItem.getReferReceiptItemId());
                            batchInfoDTO.setPurchaseReceiptRid(inspectInputItem.getReferReceiptRid());
                            batchInfoDTO.setPurchaseReceiptCode(inspectInputItem.getReferReceiptCode());
                            batchInfoDTO.setProductionDate(waybillDTO.getProductDate());
                            batchInfoDTO.setIsSafe(headDTO.getIsSafe());
                            batchInfoDTO.setIsMainParts(waybillDTO.getIsMainParts());
                            batchInfoDTO.setFunctionalLocationCode(waybillDTO.getFunctionalLocationCode());
                            batchInfoDTO.setPrice(waybillDTO.getPrice());
                            batchInfoDTO.setRemainder(remainder);
                            batchInfoDTO.setUnitizedFtyId(itemDTO.getFtyId());
                            batchInfoDTO.setUnitizedLocationId(itemDTO.getLocationId());
//                            batchInfoDTO.setExtend1(waybillDTO.getExtend1());
                            batchInfoDTO.setExtend2(waybillDTO.getExtend2());
//                            batchInfoDTO.setExtend3(waybillDTO.getExtend3());
//                            batchInfoDTO.setExtend4(waybillDTO.getExtend4());
//                            batchInfoDTO.setExtend5(waybillDTO.getExtend5());
//                            batchInfoDTO.setExtend6(waybillDTO.getExtend6());
//                            batchInfoDTO.setExtend7(waybillDTO.getExtend7());
//                            batchInfoDTO.setExtend8(waybillDTO.getExtend8());
//                            batchInfoDTO.setExtend9(waybillDTO.getExtend9());
//                            batchInfoDTO.setExtend10(waybillDTO.getExtend10());
//                            batchInfoDTO.setExtend11(waybillDTO.getExtend11());
//                            batchInfoDTO.setExtend12(waybillDTO.getExtend12());
//                            batchInfoDTO.setExtend13(waybillDTO.getExtend13());
//                            batchInfoDTO.setExtend14(waybillDTO.getExtend14());
//                            batchInfoDTO.setExtend15(waybillDTO.getExtend15());
//                            batchInfoDTO.setExtend16(waybillDTO.getExtend16());
//                            batchInfoDTO.setExtend17(waybillDTO.getExtend17());
//                            batchInfoDTO.setExtend18(waybillDTO.getExtend18());
//                            batchInfoDTO.setExtend19(waybillDTO.getExtend19());
                            batchInfoDTO.setExtend20(waybillDTO.getExtend20());
//                            batchInfoDTO.setExtend21(waybillDTO.getExtend21());
//                            batchInfoDTO.setExtend22(waybillDTO.getExtend22());
//                            batchInfoDTO.setExtend23(waybillDTO.getExtend23());
                            batchInfoDTO.setExtend24(waybillDTO.getExtend24());
                            batchInfoDTO.setExtend25(waybillDTO.getExtend25());
                            batchInfoDTO.setExtend26(waybillDTO.getExtend26());
                            batchInfoDTO.setExtend27(waybillDTO.getExtend27());
                            batchInfoDTO.setExtend28(waybillDTO.getExtend28());
                            batchInfoDTO.setExtend29(waybillDTO.getExtend29());
//                            batchInfoDTO.setExtend30(waybillDTO.getExtend30());
                            batchInfoDTO.setExtend31(waybillDTO.getExtend31());
//                            batchInfoDTO.setExtend32(waybillDTO.getExtend32());
//                            batchInfoDTO.setExtend33(waybillDTO.getExtend33());
                            batchInfoDTO.setExtend34(waybillDTO.getExtend34());
                            batchInfoDTO.setExtend35(waybillDTO.getExtend35());
                            batchInfoDTO.setExtend36(waybillDTO.getExtend36());
                            batchInfoDTO.setExtend37(waybillDTO.getExtend37());
                            batchInfoDTO.setExtend38(waybillDTO.getExtend38());
//                            batchInfoDTO.setExtend39(waybillDTO.getExtend39());
                            batchInfoDTO.setExtend40(waybillDTO.getExtend40());
                            batchInfoDTO.setExtend41(waybillDTO.getExtend41());
                            batchInfoDTO.setExtend42(waybillDTO.getExtend42());
                            batchInfoDTO.setExtend43(waybillDTO.getExtend43());
                            batchInfoDTO.setExtend44(waybillDTO.getExtend44());
//                            batchInfoDTO.setExtend45(waybillDTO.getExtend45());
                            batchInfoDTO.setExtend46(waybillDTO.getExtend46());
                            batchInfoDTO.setExtend47(waybillDTO.getExtend47());
                            batchInfoDTO.setExtend48(waybillDTO.getExtend48());
                            batchInfoDTO.setExtend49(waybillDTO.getExtend49());
                            batchInfoDTO.setExtend50(waybillDTO.getExtend50());
//                            batchInfoDTO.setExtend51(waybillDTO.getExtend51());
//                            batchInfoDTO.setExtend52(waybillDTO.getExtend52());
//                            batchInfoDTO.setExtend53(waybillDTO.getExtend53());
//                            batchInfoDTO.setExtend54(waybillDTO.getExtend54());
//                            batchInfoDTO.setExtend55(waybillDTO.getExtend55());
//                            batchInfoDTO.setExtend56(waybillDTO.getExtend56());
//                            batchInfoDTO.setExtend57(waybillDTO.getExtend57());
//                            batchInfoDTO.setExtend58(waybillDTO.getExtend58());
//                            batchInfoDTO.setExtend59(waybillDTO.getExtend59());
                            batchInfoDTO.setExtend60(waybillDTO.getExtend60());
                            batchInfoDTO.setExtend61(waybillDTO.getExtend61());
                            batchInfoDTO.setExtend62(waybillDTO.getExtend62());
                            batchInfoDTO.setExtend63(waybillDTO.getExtend63());
                            batchInfoDTO.setExtend64(waybillDTO.getExtend64());
                            batchInfoDTO.setExtend65(waybillDTO.getExtend65());
                            batchInfoDTO.setExtend66(waybillDTO.getExtend66());
                            batchInfoDTO.setExtend67(waybillDTO.getExtend67());
                            batchInfoDTO.setExtend68(waybillDTO.getExtend68());
                            batchInfoDTO.setExtend69(waybillDTO.getExtend69());
                            batchInfoDTO.setExtend70(waybillDTO.getExtend70());
                            batchInfoDTO.setExtend71(waybillDTO.getExtend71());
                            batchInfoDTO.setExtend72(waybillDTO.getExtend72());
                            batchInfoDTO.setExtend73(waybillDTO.getExtend73());
                            batchInfoDTO.setExtend74(waybillDTO.getExtend74());
                            batchInfoDTO.setCaseCode(waybillDTO.getCaseCode());
                            batchInfoDTO.setCaseWeight(waybillDTO.getCaseWeight());
                            batchInfoDTO.setPackageForm(waybillDTO.getPackageForm());
                            batchInfoDTO.setArrivalQty(waybillDTO.getArrivalQty());
                            waybillDTO.setBizBatchInfoDTO(batchInfoDTO);
                        }
                    }
                }
            }
            inspectInputItemList.add(inspectInputItem);
        }
        if (!CollectionUtils.isEmpty(idList)) {
            bizReceiptWaybillDataWrap.updateUseRemainder(EnumRealYn.TRUE.getIntValue(), idList);
        }
        if (UtilCollection.isNotEmpty(inspectInputItemList)) {
            // MQ发生消息太大, 置空无用图片信息
            inspectInputHead.getWaybillDTOList().stream().forEach(o->o.getBizBatchImgDTOList().stream().forEach(b->b.setImgBase64(null)));
            inspectInputHead.setItemList(inspectInputItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, inspectInputHead);
            ctxInput.setCurrentUser(currentUser);
            // 推送MQ生成验收入库单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_INSPECT_INPUT_STOCK, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 更新单据状态
     */
    public void updateStatus(BizReceiptInconformityHeadDTO headDTO, List<BizReceiptInconformityItemDTO> itemDTOList, Integer receiptStatus) {
        if (UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptInconformityHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptInconformityHead::getId, headDTO.getId())
                    .set(BizReceiptInconformityHead::getReceiptStatus, receiptStatus);
            bizReceiptInconformityHeadDataWrap.update(headUpdateWrapper);
        }
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptInconformityItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptInconformityItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptInconformityItem::getItemStatus, receiptStatus);
            bizReceiptInconformityItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 保存单据流
     */
    public void saveReceiptTree(BizReceiptInconformityHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptInconformityItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 基于质检会签单创建
     */
    public void getPreReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();
        // 查询已完成的质检会签单信息
        List<BizReceiptWaybillDTO> waybillDTOList = bizReceiptInspectItemDataWrap.geUnitizedInspectItemListByInconformityNotice(po);
        if (UtilCollection.isNotEmpty(waybillDTOList)) {
            // 设置运单总价
            waybillDTOList.forEach(p -> p.setTotalPrice((p.getUnqualifiedQty().multiply(p.getPrice()))));
            dataFillService.fillAttr(waybillDTOList);
            headDTO.setQuailtyNoticeWaybillDTOList(waybillDTOList);
            headDTO.setUnit(waybillDTOList.get(0).getUnit());
        }
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO,
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true)));
    }

    /**
     * 前续单据-填充详细
     */
    public void fillEntity(BizContext ctx) {
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptWaybillDTO> waybillDTOList = headDTO.getQuailtyNoticeWaybillDTOList();
        if (UtilCollection.isNotEmpty(waybillDTOList)) {
            QueryWrapper<BizReceiptInspectItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().in(BizReceiptInspectItem::getId, waybillDTOList.stream().map(p -> p.getSignInspectItemId()).collect(Collectors.toList()));
            List<BizReceiptInspectItemDTO> inspectItemDTOList = UtilCollection.toList(bizReceiptInspectItemDataWrap.list(itemQueryWrapper), BizReceiptInspectItemDTO.class);
            // 数据封装
            List<BizReceiptInconformityItemDTO> itemDTOList = UtilCollection.toList(inspectItemDTOList, BizReceiptInconformityItemDTO.class);
            itemDTOList.forEach(p -> {
                p.setPreReceiptHeadId(p.getHeadId()).setPreReceiptItemId(p.getId()).setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue())
                        .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                inspectItemDTOList.forEach(q -> {
                    if (p.getId().equals(q.getId())) {
                        p.setPreReceiptQty(q.getUnqualifiedQty()).setQty(q.getUnqualifiedQty());
                    }
                });
            });
            dataFillService.fillRlatAttrDataList(itemDTOList);
            if (UtilCollection.isNotEmpty(itemDTOList)) {
                BizReceiptInspectHead bizReceiptInspectHead = new BizReceiptInspectHead();
                // 哥给你改bug了
                headDTO.setPurchaseUserCode(itemDTOList.get(0).getPurchaseUserCode())
                        .setPurchaseUserName(itemDTOList.get(0).getPurchaseUserName())
                        .setPurchaserManagerName(bizReceiptInspectHead.getPurchaserManagerName())
                        .setDifferentType(EnumDifferentType.QUALITY_DIFF.getValue())
                        .setReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                        .setDeliveryNoticeDescribe(itemDTOList.get(0).getDeliveryNoticeDescribe())
                        .setItemList(itemDTOList);
            }
        }
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO,
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true)));
    }

}
