package com.inossem.wms.bizdomain.output.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.inossem.wms.bizdomain.output.service.biz.PurchaseReturnService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPreVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.erp.po.PreReceiptQueryPO;
import io.swagger.annotations.ApiOperation;

/**
 * 采购退货controller
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-05-11
 */

@RestController
@Api(tags = "出库管理-采购退货")
public class PurchaseReturnController {

    @Autowired
    private PurchaseReturnService purchaseReturnService;

    @ApiOperation(value = "采购退货创建采购退货单", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/init")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> init(BizContext ctx) {
        purchaseReturnService.init(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "采购退货查询采购退货单列表（分页）", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptOutputQueryListPO po,
        BizContext ctx) {
        purchaseReturnService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "采购退货查询采购退货单列表（不分页）", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/list")
    public BaseResult<MultiResultVO<BizReceiptOutputHeadDTO>> getList(@RequestBody BizReceiptOutputQueryListPO po,
        BizContext ctx) {
        purchaseReturnService.getList(ctx);
        MultiResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "采购退货查询采购订单行项目列表", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/purchase-item/results")
    public BaseResult<MultiResultVO<BizReceiptOutputPreVO>>
        getPurchaseReceiptItemList(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        purchaseReturnService.getPurchaseReceiptItemList(ctx);
        MultiResultVO<BizReceiptOutputPreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "采购退货查询采购验收行项目列表", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/inspect-item/results")
    public BaseResult<MultiResultVO<BizReceiptOutputPreVO>>
        getPurchaseInspectItemList(@RequestBody PreReceiptQueryPO po, BizContext ctx) {
        purchaseReturnService.getPurchaseInspectItemList(ctx);
        MultiResultVO<BizReceiptOutputPreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "采购退货查询采购入库行项目列表", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/input-item/results")
    public BaseResult<MultiResultVO<BizReceiptOutputPreVO>> getPurchaseInputItemList(@RequestBody PreReceiptQueryPO po,
        BizContext ctx) {
        purchaseReturnService.getPurchaseInputItemList(ctx);
        MultiResultVO<BizReceiptOutputPreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "采购退货单详情", tags = {"出库管理-采购退货"})
    @GetMapping(value = "/outputs/purchase-return/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        purchaseReturnService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "采购退货单保存", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/save")
    public BaseResult save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        purchaseReturnService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, code);
    }

    @ApiOperation(value = "采购退货单提交", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/submit")
    public BaseResult submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        purchaseReturnService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "采购退货单获取配货信息", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        purchaseReturnService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }

    @ApiOperation(value = "采购退货单过账", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/post")
    public BaseResult post(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        purchaseReturnService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_POST_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "采购退货单冲销", tags = {"出库管理-采购退货"})
    @PostMapping(value = "/outputs/purchase-return/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        purchaseReturnService.writeOff(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "采购退货单删除", tags = {"出库管理-采购退货"})
    @DeleteMapping(value = "/outputs/purchase-return/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        purchaseReturnService.delete(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_DELETE_SUCCESS, headDTO.getReceiptCode());
    }

}