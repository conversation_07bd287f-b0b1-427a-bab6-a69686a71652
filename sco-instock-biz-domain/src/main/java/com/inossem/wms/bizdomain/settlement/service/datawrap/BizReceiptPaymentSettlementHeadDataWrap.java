package com.inossem.wms.bizdomain.settlement.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.settlement.dao.BizReceiptPaymentSettlementHeadMapper;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementInputItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentSettlementHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.DeliveryVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Service
public class BizReceiptPaymentSettlementHeadDataWrap extends BaseDataWrap<BizReceiptPaymentSettlementHeadMapper, BizReceiptPaymentSettlementHead> {

    /**
     * 分页列表查询
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查询条件
     * @return
     */
    public List<BizReceiptPaymentSettlementPageVO> getPageVo(IPage<BizReceiptPaymentSettlementPageVO> pageData, WmsLambdaQueryWrapper<BizReceiptPaymentSettlementSearchPO> pageWrapper) {
        return this.baseMapper.selectPageVo(pageData, pageWrapper);
    }

    public List<BizReceiptPaymentPlanVO> selectPaymentPlan(BizReceiptPaymentPlanSearchPO po) {
        return this.baseMapper.selectPaymentPlan(po);
    }

    public List<DeliveryVO> selectDelivery(BizReceiptDeliveryNoticeSearchPO po) {
        return this.baseMapper.selectDelivery(po);
    }

    public List<BizReceiptPaymentSettlementInputItemDTO> selectInput(BizReceiptInputSearchPO po) {
        return this.baseMapper.selectInput(po);
    }
}
