package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchImgDataWrap;
import com.inossem.wms.bizbasis.common.dao.SequenceMapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizCommonReceiptAttachmentDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpProductionReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReserveReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpSaleReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageBinService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizbasis.todo.service.datawrap.TemplateTaskDataWrap;
import com.inossem.wms.bizdomain.report.dao.BizReportMapper;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.constant.task.TaskConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.inconformity.EnumDisposalResult;
import com.inossem.wms.common.enums.inconformity.EnumSolveReason;
import com.inossem.wms.common.enums.lifetime.EnumInspectResult;
import com.inossem.wms.common.enums.maintain.EnumMaintenanceType;
import com.inossem.wms.common.enums.register.EnumUnitizedVisualCheck;
import com.inossem.wms.common.enums.report.EnumStockAgeType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.metadata.MetadataContext;
import com.inossem.wms.common.model.auth.todo.dto.TemplateTaskDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchImg;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.report.DtsFileSync;
import com.inossem.wms.common.model.bizdomain.report.DtsFileZip;
import com.inossem.wms.common.model.bizdomain.report.DtsRoot;
import com.inossem.wms.common.model.bizdomain.report.DtsSync;
import com.inossem.wms.common.model.bizdomain.report.Root;
import com.inossem.wms.common.model.bizdomain.report.UnitizedInputLedgerSync;
import com.inossem.wms.common.model.bizdomain.report.dto.InspectCaseDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.InspectMatDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeDirectDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeInputDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.OccupyDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeOutputDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.OverDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.OverDetailDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.SubtractDetailDTO;
import com.inossem.wms.common.model.bizdomain.report.dto.TimeUnloadDTO;
import com.inossem.wms.common.model.bizdomain.report.po.*;
import com.inossem.wms.common.model.bizdomain.report.vo.*;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.MoveTypeMapVO;
import com.inossem.wms.common.model.common.enums.ReceiptTypeMapVO;
import com.inossem.wms.common.model.common.enums.SpecStockMapVO;
import com.inossem.wms.common.model.common.enums.StockStatusMapVO;
import com.inossem.wms.common.model.erp.entity.ErpMatDoc;
import com.inossem.wms.common.model.erp.entity.ErpProductionReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpReserveReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpSaleReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpStockBatch;
import com.inossem.wms.common.model.erp.entity.ErpStockTurnover;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.sequence.vo.SysSequencePrefixVO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.file.service.datawrap.BizCommonFileDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 报表代码代码块
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-10
 */
@Service
@Slf4j
public class UnitizedReportComponent {
    @Autowired
    protected BizReportMapper bizReportMapper;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected SequenceMapper sequenceMapper;

    @Autowired
    protected ErpPurchaseReceiptHeadDataWrap erpPurchaseReceiptHeadDataWrap;
    @Autowired
    protected ErpProductionReceiptHeadDataWrap erpProductionReceiptHeadDataWrap;
    @Autowired
    protected ErpSaleReceiptHeadDataWrap erpSaleReceiptHeadDataWrap;
    @Autowired
    protected ErpReserveReceiptHeadDataWrap erpReserveReceiptHeadDataWrap;
    @Autowired
    protected DictionaryService dictionaryService;


    @Autowired
    private BatchImgService bizBatchImgService;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private BatchInfoService batchInfoService;
    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private WhStorageBinService whStorageBinService;
    @Autowired
    protected TemplateTaskDataWrap templateTaskDataWrap;

    @Autowired
    protected BizCommonFileDataWrap bizCommonFileDataWrap;
    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    private I18nTextCommonService i18nTextCommonService;
    @Autowired
    private BizBatchImgDataWrap bizBatchImgDataWrap;
    @Autowired
    private BizCommonReceiptAttachmentDataWrap bizCommonReceiptAttachmentDataWrap;

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

    /**
     * 获取库存状态下拉
     *
     * @param ctx ctx
     */
    public void getStockStatusList(BizContext ctx) {

        MultiResultVO<StockStatusMapVO> vo = new MultiResultVO<>(EnumStockStatus.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 获取特殊库存下拉
     *
     * @param ctx ctx
     */
    public void getSpecStockList(BizContext ctx) {
        MultiResultVO<SpecStockMapVO> vo = new MultiResultVO<>(EnumSpecStock.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 获取移动类型下拉
     *
     * @param ctx ctx
     */
    public void getMoveTypeList(BizContext ctx) {
        MultiResultVO<MoveTypeMapVO> vo = new MultiResultVO<>(EnumMoveType.toUnitizedList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }


    /**
     * 业务凭证获取单据类型下拉
     *
     * @param ctx ctx
     */
    public void getReceiptTypeList(BizContext ctx) {
        MultiResultVO<ReceiptTypeMapVO> vo = new MultiResultVO<>(EnumReceiptType.toInsDocBatchReceiptList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询批次库存详情
     *
     * @param ctx ctx
     */
    public void selectStockBatchDetail(BizContext ctx, boolean exportFlag) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (!EnumSpecStock.SPEC_STOCK_UNITIZED_BATCH_STATUS_TEMP_STORE.getValue().equals(po.getSpecStock())) {
            po.setIsUnitized(true);
        }
        List<UnitizedStockBatchVO> result;
        IPage<UnitizedStockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(UnitizedStockBatchVO.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        result = bizReportMapper.selectStockBatchDetailUnitized(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (!exportFlag && totalCount > 0) {
            // 非导出时，设置批次图片和附件信息

            Set<Long> batchIds = result.stream().map(UnitizedStockBatchVO::getBatchId).collect(Collectors.toSet());

            if (UtilCollection.isNotEmpty(batchIds)) {
                // 设置批次图片
                List<BizBatchImg> batchImgs = bizBatchImgDataWrap.list(new LambdaQueryWrapper<BizBatchImg>()
                        .in(BizBatchImg::getBatchId, batchIds)
                );
                Map<Long, List<BizBatchImg>> imgMap = batchImgs.stream().collect(Collectors.groupingBy(BizBatchImg::getBatchId));
                result.forEach(p -> p.setBizBatchImgDTOList(UtilCollection.toList(imgMap.get(p.getBatchId()), BizBatchImgDTO.class)));

                // 设置批次附件
                List<BizCommonReceiptAttachment> attachmentList = bizCommonReceiptAttachmentDataWrap.list(
                        new LambdaQueryWrapper<BizCommonReceiptAttachment>()
                                .in(BizCommonReceiptAttachment::getBatchId, batchIds));
                Map<Long, List<BizCommonReceiptAttachment>> attachmentFileMap = attachmentList.stream().collect(Collectors.groupingBy(BizCommonReceiptAttachment::getBatchId));

                result.forEach(p -> p.setBatchAttachmentFileList(attachmentFileMap.get(p.getBatchId())));
            }

        }
        dataFillService.fillAttr(result);
        for (UnitizedStockBatchVO batchVO : result) {
            String matCode = batchVO.getMatCode();
            // 判断是否成套物料
            if (StringUtils.isNotBlank(matCode)) {
                if (matCode.startsWith("CT")) {
                    BigDecimal batchPrice = batchVO.getBatchPrice();
                    BigDecimal money = batchVO.getQty().multiply(batchPrice);
                    batchVO.setMoney(money);
                    batchVO.setPrice(batchPrice);
                    Long parentMatId = batchVO.getParentMatId();
                    if (UtilNumber.isNotEmpty(parentMatId)) {
                        DicMaterialDTO materialDTO = dictionaryService.getMatCacheById(parentMatId);
                        if (materialDTO != null) {
                            batchVO.setParentMatCode(materialDTO.getMatCode());
                            batchVO.setParentMatName(materialDTO.getMatName());
                        }
                    }
                } else {
                    Integer lifeMax = batchVO.getShelfLifeMax();
                    if (lifeMax != null) {
                        batchVO.setExtend60(lifeMax.toString());
                    }
                    Integer pkgType = batchVO.getPackageType();
                    if (pkgType != null) {
                        int extend61 = 0;
                        // 维保周期映射
                        switch (pkgType.intValue()) {
                            case 1:
                            case 2:
                            case 7:
                            case 8:
                                extend61 = 5 * 12;
                                break;
                            case 3:
                                extend61 = 2 * 12;
                                break;
                            case 4:
                                extend61 = 3 * 12;
                                break;
                            case 5:
                                extend61 = 1 * 12;
                                break;
                            default:
                                extend61 = 0;
                                break;
                        }
                        batchVO.setExtend61(String.valueOf(extend61));
                    }
                }
            }
        }
        PageObjectVO<UnitizedStockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 导出批次库存Excel
     *
     * @param ctx 上下文
     */
    public void exportStockBatchDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("成套设备批次库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockBatchDetail(ctx, true);
        PageObjectVO<UnitizedStockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(UnitizedStockBatchVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询批次库存（库存金额）基于物料组分组
     *
     * @param ctx ctx
     */
    public void selectStockBatchGroupByMatGroup(BizContext ctx) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBatchVO> result;

        IPage<StockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBatchVO.class);
        }
        result = bizReportMapper.selectStockBatchGroupByMatGroup(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询批次库存（库存金额） 基于库存地点分组
     *
     * @param ctx ctx
     */
    public void selectStockBatchGroupByLocation(BizContext ctx) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBatchVO> result;

        IPage<StockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBatchVO.class);
        }
        result = bizReportMapper.selectStockBatchGroupByLocation(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询批次库存（库存金额）基于仓库号分组
     *
     * @param ctx ctx
     */
    public void selectStockBatchGroupByWh(BizContext ctx) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBatchVO> result;

        IPage<StockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBatchVO.class);
        }
        result = bizReportMapper.selectStockBatchGroupByWh(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    @Autowired
    protected BizLabelDataDataWrap labelDataDataWrap;

    /**
     * 查询仓位库存详情
     *
     * @param ctx ctx
     */
    public void selectStockBinDetail(BizContext ctx, boolean exportFlag) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (!EnumSpecStock.SPEC_STOCK_UNITIZED_BATCH_STATUS_TEMP_STORE.getValue().equals(po.getSpecStock())) {
            po.setIsUnitized(true);
        }
        List<UnitizedStockBinVO> result;

        IPage<UnitizedStockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(UnitizedStockBinVO.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        result = bizReportMapper.selectStockBinDetailUnitized(page, po);
        for (UnitizedStockBinVO stockBinVo : result) {
            String packageTypeI18n = EnumPackageType.getDescByValue(stockBinVo.getPackageType());
            stockBinVo.setPackageTypeI18n(packageTypeI18n);
        }
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (!exportFlag) {
            if(UtilCollection.isNotEmpty(result)){
                // 设置标签

                List<StockBinDTO> dtoList = UtilCollection.toList(result, StockBinDTO.class);

                List<BizLabelData> labelDataList = labelDataDataWrap.selectByStockBinList(dtoList);
                if (UtilCollection.isNotEmpty(labelDataList)) {
                    Map<String,
                            List<BizLabelData>> labelDataMap = labelDataList.stream()
                            .collect(Collectors.groupingBy(e -> e.getMatId() + Const.HYPHEN + e.getFtyId() + Const.HYPHEN + e.getLocationId()
                                    + Const.HYPHEN + e.getBatchId() + Const.HYPHEN + e.getWhId() + Const.HYPHEN + e.getTypeId() + Const.HYPHEN + e.getBinId()
                                    + Const.HYPHEN + e.getCellId()));
                    for (UnitizedStockBinVO stockBinDTO : result) {
                        String key = stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN + stockBinDTO.getLocationId()
                                + Const.HYPHEN + stockBinDTO.getBatchId() + Const.HYPHEN + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId()
                                + Const.HYPHEN + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                        List<BizLabelData> innerLabelDataList = labelDataMap.get(key);
                        stockBinDTO.setLabelDataList(innerLabelDataList);

                    }
                }
            }
        }
        if (!exportFlag && totalCount > 0) {
            // 非导出时，设置批次图片和附件信息

            Set<Long> batchIds = result.stream().map(UnitizedStockBinVO::getBatchId).collect(Collectors.toSet());

            if (UtilCollection.isNotEmpty(batchIds)) {
                // 设置批次图片
                List<BizBatchImg> batchImgs = bizBatchImgDataWrap.list(new LambdaQueryWrapper<BizBatchImg>()
                        .in(BizBatchImg::getBatchId, batchIds)
                );
                Map<Long, List<BizBatchImg>> imgMap = batchImgs.stream().collect(Collectors.groupingBy(BizBatchImg::getBatchId));
                result.forEach(p -> p.setBizBatchImgDTOList(UtilCollection.toList(imgMap.get(p.getBatchId()), BizBatchImgDTO.class)));

                // 设置批次附件
                List<BizCommonReceiptAttachment> attachmentList = bizCommonReceiptAttachmentDataWrap.list(
                        new LambdaQueryWrapper<BizCommonReceiptAttachment>()
                                .in(BizCommonReceiptAttachment::getBatchId, batchIds));
                Map<Long, List<BizCommonReceiptAttachment>> attachmentFileMap = attachmentList.stream().collect(Collectors.groupingBy(BizCommonReceiptAttachment::getBatchId));

                result.forEach(p -> p.setBatchAttachmentFileList(attachmentFileMap.get(p.getBatchId())));
            }

        }
        dataFillService.fillAttr(result);
        
        PageObjectVO<UnitizedStockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 导出仓位库存Excel
     *
     * @param ctx 上下文
     */
    public void exportStockBinDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("成套设备仓位库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockBinDetail(ctx, true);
        PageObjectVO<UnitizedStockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(UnitizedStockBinVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询仓位库存（库存金额） 基于仓库号分组
     *
     * @param ctx ctx
     */
    public void selectStockBinGroupByWh(BizContext ctx) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinVO> result;

        IPage<StockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinVO.class);
        }
        result = bizReportMapper.selectStockBinGroupByWh(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询仓位库存（库存金额）基于存储类型分组
     *
     * @param ctx ctx
     */
    public void selectStockBinGroupByType(BizContext ctx) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinVO> result;

        IPage<StockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinVO.class);
        }
        result = bizReportMapper.selectStockBinGroupByType(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询仓位库存（库存金额）基于存储区分组
     *
     * @param ctx ctx
     */
    public void selectStockBinGroupBySection(BizContext ctx) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinVO> result;

        IPage<StockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinVO.class);
        }
        result = bizReportMapper.selectStockBinGroupBySection(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 全系统的库存积压分析
     *
     * @param ctx ctx
     */
    public void selectStockAnalyse(BizContext ctx) {

        StockAgeAnalysePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockAgeAnalyseVO> result;

        LocalDate currentDate = LocalDate.now();
        List<EnumStockAgeType> ageTypeList = Arrays.asList(EnumStockAgeType.values());
        for (EnumStockAgeType ageType : ageTypeList) {
            if (ageType.getEndDay() != null) {
                ageType.setStartDate(currentDate.plusDays(-ageType.getEndDay()));

            }
            ageType.setEndDate(currentDate.plusDays(-ageType.getStartDay() + 1));

        }

        po.setAgeTypeList(ageTypeList);
        result = bizReportMapper.selectStockAnalyse(po);
        if (result != null) {
            BigDecimal totalMoney;
            totalMoney = result.stream().map(StockAgeAnalyseVO::getStockMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal currentPercent = BigDecimal.ZERO;
            for (int i = 0; i < result.size(); i++) {
                StockAgeAnalyseVO analyseVO = result.get(i);
                if (i == result.size() - 1) {
                    analyseVO.setPercent(BigDecimal.ONE.subtract(currentPercent));
                } else {
                    analyseVO.setPercent(analyseVO.getStockMoney().divide(totalMoney, 3, RoundingMode.HALF_UP));
                    currentPercent = currentPercent.add(analyseVO.getPercent());
                }
            }
        }

        MultiResultVO<StockAgeAnalyseVO> vo = new MultiResultVO<>(result);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 基于仓库维度 查询存在积压超过1年的数据
     *
     * @param ctx ctx
     */
    public void selectStockAnalyseGroupByWh(BizContext ctx) {

        StockAgeAnalysePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockAgeAnalyseVO> result;

        LocalDate currentDate = LocalDate.now();
        List<EnumStockAgeType> ageTypeList = new ArrayList<>();
        ageTypeList.add(EnumStockAgeType.ENUM_STOCK_AGE_TYPE_FOUR);
        for (EnumStockAgeType ageType : ageTypeList) {
            if (ageType.getEndDay() != null) {
                ageType.setStartDate(currentDate.plusDays(-ageType.getEndDay()));
            }
            ageType.setEndDate(currentDate.plusDays(-ageType.getStartDay() + 1));
        }

        po.setAgeTypeList(ageTypeList);
        result = bizReportMapper.selectStockAnalyseGroupByWh(po);
        result = result.stream().filter(e -> EnumStockAgeType.ENUM_STOCK_AGE_TYPE_FOUR.getStockAgeType().equals(e.getStockAgeType()))
                .collect(Collectors.toList());

        MultiResultVO<StockAgeAnalyseVO> vo = new MultiResultVO<>(result);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 基于库存地点维度的库存积压分析
     *
     * @param ctx ctx
     */
    public void selectStockAnalyseGroupByLocation(BizContext ctx) {

        StockAgeAnalysePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockAgeAnalyseVO> result;

        List<StockAgeAnalyseLocationVO> locationAnalyseResult = new ArrayList<>();

        LocalDate currentDate = LocalDate.now();
        List<EnumStockAgeType> ageTypeList = Arrays.asList(EnumStockAgeType.values());
        for (EnumStockAgeType ageType : ageTypeList) {
            if (ageType.getEndDay() != null) {
                ageType.setStartDate(currentDate.plusDays(-ageType.getEndDay()));
            }
            ageType.setEndDate(currentDate.plusDays(-ageType.getStartDay() + 1));
        }

        po.setAgeTypeList(ageTypeList);
        result = bizReportMapper.selectStockAnalyseGroupByLocation(po);
        // 根据库存地点id分组
        Map<Long, List<StockAgeAnalyseVO>> locationAnalyseMap = result.stream().collect(Collectors.groupingBy(StockAgeAnalyseVO::getLocationId));

        for (Long locationId : locationAnalyseMap.keySet()) {
            StockAgeAnalyseLocationVO stockAnalyseLocationVO = new StockAgeAnalyseLocationVO();
            stockAnalyseLocationVO.setStockAnalyseVOList(locationAnalyseMap.get(locationId));
            if (UtilCollection.isNotEmpty(stockAnalyseLocationVO.getStockAnalyseVOList())) {
                StockAgeAnalyseVO vo = stockAnalyseLocationVO.getStockAnalyseVOList().get(0);
                stockAnalyseLocationVO.setFtyCode(vo.getFtyCode());
                stockAnalyseLocationVO.setFtyName(vo.getFtyName());
                stockAnalyseLocationVO.setLocationCode(vo.getLocationCode());
                stockAnalyseLocationVO.setLocationName(vo.getLocationName());
                // 计算百分比
                BigDecimal totalMoney;
                totalMoney = stockAnalyseLocationVO.getStockAnalyseVOList().stream().map(StockAgeAnalyseVO::getStockMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                stockAnalyseLocationVO.setStockMoney(totalMoney);
                BigDecimal currentPercent = BigDecimal.ZERO;
                for (int i = 0; i < stockAnalyseLocationVO.getStockAnalyseVOList().size(); i++) {
                    StockAgeAnalyseVO analyseVO = stockAnalyseLocationVO.getStockAnalyseVOList().get(i);
                    if (i == stockAnalyseLocationVO.getStockAnalyseVOList().size() - 1) {
                        analyseVO.setPercent(BigDecimal.ONE.subtract(currentPercent));
                    } else {
                        analyseVO.setPercent(totalMoney.compareTo(BigDecimal.ZERO) == BigDecimal.ROUND_UP ? BigDecimal.ZERO : analyseVO.getStockMoney().divide(totalMoney, 3, RoundingMode.HALF_UP));
                        currentPercent = currentPercent.add(analyseVO.getPercent());
                    }
                }
                Map<String, StockAgeAnalyseVO> stockAgeAnalyseVOMap =
                        stockAnalyseLocationVO.getStockAnalyseVOList().stream().collect(Collectors.toMap(StockAgeAnalyseVO::getStockAgeType, e -> e));
                ArrayList<StockAgeAnalyseVO> fomatList = new ArrayList<>();
                for (EnumStockAgeType ageType : ageTypeList) {
                    if (stockAgeAnalyseVOMap.containsKey(ageType.getStockAgeType())) {
                        fomatList.add(stockAgeAnalyseVOMap.get(ageType.getStockAgeType()));
                    } else {
                        StockAgeAnalyseVO fomatVO = new StockAgeAnalyseVO();
                        fomatVO.setStockAgeType(ageType.getStockAgeType());

                        fomatVO.setPercent(BigDecimal.ZERO);
                        fomatVO.setFtyCode(stockAnalyseLocationVO.getFtyCode());
                        fomatVO.setFtyName(stockAnalyseLocationVO.getFtyName());
                        fomatVO.setLocationCode(stockAnalyseLocationVO.getLocationCode());
                        fomatVO.setLocationName(stockAnalyseLocationVO.getLocationName());
                        fomatVO.setLocationId(locationId);
                        fomatList.add(fomatVO);
                    }
                }
                stockAnalyseLocationVO.setStockAnalyseVOList(fomatList);

            }
            locationAnalyseResult.add(stockAnalyseLocationVO);

        }

        MultiResultVO<StockAgeAnalyseLocationVO> vo = new MultiResultVO<>(locationAnalyseResult);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 业务凭证查询
     *
     * @param ctx ctx
     */
    public void selectStockInsDocBatch(BizContext ctx) {

        StockInsDocBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 处理关联单据

        List<StockInsDocBatchVO> result = new ArrayList<>();
        IPage<StockInsDocBatchVO> page = null;
        Long totalCount = null;

        if (po == null) {
            PageObjectVO<StockInsDocBatchVO> vo = new PageObjectVO<>(result, totalCount);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
            return;
        }


        if (po.isPaging()) {
            page = po.getPageObj(StockInsDocBatchVO.class);
        }
        result = bizReportMapper.selectStockInsDocBatch(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        dataFillService.fillRlatAttrDataList(result);

        PageObjectVO<StockInsDocBatchVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 业务凭证查询导出Excel
     *
     * @param ctx 上下文
     */
    public void exportStockInsDocBatch(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("业务凭证"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockInsDocBatch(ctx);
        PageObjectVO<StockInsDocBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockInsDocBatchVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    /**
     * 将参考单据转换为id
     *
     * @param ctx ctx
     */
    public void referReceiptHandler(BizContext ctx) {
        Object po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po == null) {
            return;
        }

        String purchaseReceiptCode = UtilReflect.getValueByField("purchaseReceiptCode", po);
        String saleReceiptCode = UtilReflect.getValueByField("saleReceiptCode", po);
        String productionReceiptCode = UtilReflect.getValueByField("productionReceiptCode", po);
        String reserveReceiptCode = UtilReflect.getValueByField("reserveReceiptCode", po);

        Integer referReceiptType = null;
        Long referReceiptHeadId = null;
        int count = 0;
        if (UtilString.isNotNullOrEmpty(purchaseReceiptCode)) {
            count += 1;
        }
        if (UtilString.isNotNullOrEmpty(saleReceiptCode)) {
            count += 1;
        }
        if (UtilString.isNotNullOrEmpty(productionReceiptCode)) {
            count += 1;
        }
        if (UtilString.isNotNullOrEmpty(reserveReceiptCode)) {
            count += 1;
        }
        if (count > 1) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
        }

        // 采购订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(purchaseReceiptCode)) {
            QueryWrapper<ErpPurchaseReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpPurchaseReceiptHead::getReceiptCode, purchaseReceiptCode);
            ErpPurchaseReceiptHead head = erpPurchaseReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }

        // 销售订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(saleReceiptCode)) {
            QueryWrapper<ErpSaleReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpSaleReceiptHead::getReceiptCode, saleReceiptCode);
            ErpSaleReceiptHead head = erpSaleReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }
        // 预留订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(reserveReceiptCode)) {
            QueryWrapper<ErpReserveReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpReserveReceiptHead::getReceiptCode, reserveReceiptCode);
            ErpReserveReceiptHead head = erpReserveReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }
        // 生产订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(productionReceiptCode)) {
            QueryWrapper<ErpProductionReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpProductionReceiptHead::getReceiptCode, productionReceiptCode);
            ErpProductionReceiptHead head = erpProductionReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }
        UtilReflect.setValueByField(po, "referReceiptType", referReceiptType);
        UtilReflect.setValueByField(po, "referReceiptHeadId", referReceiptHeadId);

    }

    /**
     * 将前置单据号转为前置单据id
     *
     * @param ctx ctx
     */
    public void preReceiptHandler(BizContext ctx) {
        Object po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        String preReceiptCode = UtilReflect.getValueByField("preReceiptCode", po);

        if (UtilString.isNotNullOrEmpty(preReceiptCode)) {
            String tableName = null;
            // 前置单据号转 preReceiptHeadId
            List<SysSequencePrefixVO> prefixVOList = sequenceMapper.selectAllPrefix();

            for (SysSequencePrefixVO prefixVO : prefixVOList) {
                if (UtilString.isNotNullOrEmpty(prefixVO.getPrefix())) {
                    String comparStr = preReceiptCode.substring(0, prefixVO.getPrefix().length());
                    if (comparStr.equalsIgnoreCase(prefixVO.getPrefix())) {
                        tableName = prefixVO.getTableName();
                        break;
                    }
                }
            }
            if (UtilString.isNotNullOrEmpty(tableName)) {
                IService mPlusService = MetadataContext.getMplusService(tableName);

                if (mPlusService != null) {
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("receipt_code", preReceiptCode);
                    Object preObj = mPlusService.getOne(queryWrapper);
                    if (preObj != null) {

                        Long preReceiptHeadId = UtilReflect.getValueByField("id", preObj);

                        UtilReflect.setValueByField(po, "preReceiptHeadId", preReceiptHeadId);
                    } else {
                        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
                    }
                } else {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
                }

            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }

        }

    }

    /**
     * 作业量统计
     *
     * @param ctx ctx
     */
    public void selectTask(BizContext ctx) {

        TaskSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 处理关联单据

        List<TaskVO> result = new ArrayList<>();
        IPage<TaskVO> page = null;
        Long totalCount = null;

        if (po == null) {
            PageObjectVO<TaskVO> vo = new PageObjectVO<>(result, totalCount);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
            return;
        }


        if (po.isPaging()) {
            page = po.getPageObj(TaskVO.class);
        }
        result = bizReportMapper.selectTask(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        dataFillService.fillRlatAttrDataList(result);

        PageObjectVO<TaskVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 作业查询导出Excel
     *
     * @param ctx 上下文
     */
    public void exportTask(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("作业单"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectTask(ctx);
        PageObjectVO<TaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(TaskVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    /**
     * 库存对账 全局对账
     * 结果用于库存差异
     */
    public void stockDiff() {
        // 同步ERP库存到erp_stock_batch表中
        bizReportMapper.deleteInsStockToDiff();

        // 根据条件查询 erp库存 存入 erp_stock_diff 中
        bizReportMapper.insertErpStockToDiff(null);
        // 根据 fty_id, location_id, mat_id, spec_stock, spec_stock_code, batch_erp group by 查询 instock 库存 存入 erp_stock_diff 中

        // 查询仓位库存 插入到erp_stock_diff中 查询与插入分开 不建议使用 insert select ，防止锁stock_batch表
        Collection<DicStockLocationDTO> locationDTOS = dictionaryService.getAllLocationCache();

        for (DicStockLocationDTO locationDTO : locationDTOS) {
            List<StockDiffVO> insStockList = bizReportMapper.getInsStockByLocationId(locationDTO.getId());

            if (UtilCollection.isNotEmpty(insStockList)) {
                UtilDb.insertData(insStockList, bizReportMapper::insertInsStockToDiff, 5);
            }

        }

    }

    /**
     * 查询库存差异
     *
     * @param ctx ctx
     */
    public void selectStockDiff(BizContext ctx) {
        StockDiffSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockDiffVO> result = new ArrayList<>();
        IPage<StockDiffVO> page = null;
        Long totalCount = null;

        if (po == null) {
            PageObjectVO<StockDiffVO> vo = new PageObjectVO<>(result, totalCount);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
            return;
        }


        if (po.isPaging()) {
            page = po.getPageObj(StockDiffVO.class);
        }
        result = bizReportMapper.selectStockDiff(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }


        PageObjectVO<StockDiffVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 库存对账查询导出Excel
     *
     * @param ctx 上下文
     */
    public void exportStockDiff(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("库存对账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockDiff(ctx);
        PageObjectVO<StockDiffVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockDiffVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 库存金额分析 （erp库存）
     *
     * @param ctx ctx
     */
    public void erpStockMoneyAnalyse(BizContext ctx) {

        List<StockMoneyAnalyseVO> result = bizReportMapper.selectErpStockMoney();

        MultiResultVO<StockMoneyAnalyseVO> vo = new MultiResultVO<>(result);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询出入库记录 （最近7天）
     *
     * @param ctx ctx
     */
    public void selectInAndOut(BizContext ctx) {
        InAndOutAnalysePO po = new InAndOutAnalysePO();
        po.setEndDate(LocalDate.now());
        po.setStartDate(po.getEndDate().plusDays(-7));

        List<InAndOutAnalyseVO> result = bizReportMapper.selectInAndOut(po);

        // 将未查出的数据处理
        Map<String, InAndOutAnalyseVO> resultMap = result.stream().collect(Collectors.toMap(InAndOutAnalyseVO::getCreateDate, e -> e));
        List<InAndOutAnalyseVO> returnList = new ArrayList<>();

        for (int i = 0; i < 7; i++) {
            String date = po.getStartDate().plusDays(i).toString();
            if (resultMap.containsKey(date)) {
                returnList.add(resultMap.get(date));

            } else {
                InAndOutAnalyseVO inner = new InAndOutAnalyseVO();
                inner.setCreateDate(date);
                inner.setInNum(0);
                inner.setOutNum(0);
                returnList.add(inner);

            }
        }

        MultiResultVO<InAndOutAnalyseVO> vo = new MultiResultVO<>(returnList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 库存周转率报表 最近半年
     *
     * @param ctx ctx
     */
    public void selectStockTurnover(BizContext ctx) {
        StockTurnoverPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if (po == null) {
            po = new StockTurnoverPO();

        }
        LocalDate end = LocalDate.now().plusMonths(-1);

        LocalDate start = end.plusMonths(-5);


        po.setEndMonth(UtilLocalDateTime.getStringMonthForLocalDate(end));

        po.setStartMonth(UtilLocalDateTime.getStringMonthForLocalDate(start));

        List<StockTurnoverVO> result = bizReportMapper.selectStockTurnover(po);


        MultiResultVO<StockTurnoverVO> vo = new MultiResultVO<>(result);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 定时任务 每月统计 一次 期初库存 期末库存 月入金额 月出金额
     * 每月月底 执行一次
     * 项目上线时 导入一次期初库存
     */
    public void collectStockTurnover() {

        Collection<DicStockLocationDTO> locationDTOS = dictionaryService.getAllLocationCache();

        // 本月期末
        LocalDate endingMonth = LocalDate.now();

        // 下个月期初
        LocalDate beginningMonth = endingMonth.plusMonths(1);

        String year = endingMonth.getYear() + Const.STRING_EMPTY;
        String beginMonth = UtilLocalDateTime.getStringMonthForLocalDate(beginningMonth);
        String endMonth = UtilLocalDateTime.getStringMonthForLocalDate(endingMonth);

        for (DicStockLocationDTO locationDTO : locationDTOS) {
            List<ErpStockBatch> stockBatchList = bizReportMapper.selectStockByLocationId(locationDTO.getId());

            // erp_stock_batch计算期末库存 并插入下个月的期初库存入到erp_stock_turnover表中
            if (UtilCollection.isNotEmpty(stockBatchList)) {
                List<ErpStockTurnover> erpStockTurnoverList = new ArrayList<>();
                for (ErpStockBatch stockBatch : stockBatchList) {
                    ErpStockTurnover begin = new ErpStockTurnover();
                    begin.setId(UtilSequence.nextId());
                    begin.setYear(year);
                    begin.setMonth(beginMonth);
                    begin.setMatId(stockBatch.getMatId());
                    begin.setFtyId(stockBatch.getFtyId());
                    begin.setLocationId(stockBatch.getLocationId());
                    begin.setBeginningBalance(stockBatch.getMoney());
                    begin.setEndingBalance(BigDecimal.ZERO);
                    begin.setMonthInMoney(BigDecimal.ZERO);
                    begin.setMonthOutMoney(BigDecimal.ZERO);

                    ErpStockTurnover end = new ErpStockTurnover();
                    end.setId(UtilSequence.nextId());
                    end.setYear(year);
                    end.setMonth(endMonth);
                    end.setMatId(stockBatch.getMatId());
                    end.setFtyId(stockBatch.getFtyId());
                    end.setLocationId(stockBatch.getLocationId());
                    end.setBeginningBalance(BigDecimal.ZERO);
                    end.setEndingBalance(stockBatch.getMoney());
                    end.setMonthInMoney(BigDecimal.ZERO);
                    end.setMonthOutMoney(BigDecimal.ZERO);

                    erpStockTurnoverList.add(begin);
                    erpStockTurnoverList.add(end);

                }

                // 计算月入，月出 金额 插入到erp_stock_turnover表中
                List<ErpMatDoc> matDocList = bizReportMapper.selectInAndOutMoney(endMonth, locationDTO.getId());
                for (ErpMatDoc matDoc : matDocList) {
                    ErpStockTurnover stockTurnover = new ErpStockTurnover();
                    stockTurnover.setId(UtilSequence.nextId());
                    stockTurnover.setYear(year);
                    stockTurnover.setMonth(endMonth);
                    stockTurnover.setMatId(matDoc.getMatId());
                    stockTurnover.setFtyId(matDoc.getFtyId());
                    stockTurnover.setLocationId(matDoc.getLocationId());
                    stockTurnover.setBeginningBalance(BigDecimal.ZERO);
                    stockTurnover.setEndingBalance(BigDecimal.ZERO);
                    if (matDoc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT)) {
                        stockTurnover.setMonthOutMoney(matDoc.getMatDocMoney());
                        stockTurnover.setMonthInMoney(BigDecimal.ZERO);
                    } else if (matDoc.getDebitCredit().equals(Const.DEBIT_S_ADD)) {
                        stockTurnover.setMonthInMoney(matDoc.getMatDocMoney());
                        stockTurnover.setMonthOutMoney(BigDecimal.ZERO);
                    }

                    erpStockTurnoverList.add(stockTurnover);
                }

                UtilDb.insertData(erpStockTurnoverList, bizReportMapper::insertIntoStockTurnOver, 500);

            }
        }

    }

    /**
     * pda 查询仓位库存
     *
     * @param ctx ctx
     */
    public void getPdaStockBin(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        StockBinPdaSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po || UtilString.isNullOrEmpty(po.getLabelType()) || UtilString.isNullOrEmpty(po.getLabelSequence()) || UtilString
                .isNullOrEmpty(po.getResType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 库存搜索条件
        StockBinPO stockBinPo = new StockBinPO();
        // 查询非限制库存
        stockBinPo.setStockStatusSet(new HashSet<String>() {{
            add(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue().toString());
        }});
        //标签类型
        switch (po.getLabelType()) {
            // 物料标签
            case Const.LABEL_SORT_MAT:
                if (UtilString.isNullOrEmpty(po.getMaterialArgs())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
                /* ****************** 请求类型 ******************/
                // 扫描：01【按id查询】
                if (TaskConst.TYPE_SCAN.equals(po.getResType())) {
                    switch (po.getMaterialArgs()) {
                        case TaskConst.BATCH:
                            stockBinPo.setBatchId(Long.valueOf(po.getLabelSequence()));
                            break;
                        case TaskConst.MATERIAL:
                            stockBinPo.setMatId(Long.valueOf(po.getLabelSequence()));
                            break;
                        case TaskConst.LABEL:
                            List<BizLabelDataDTO> labelDataDTOList = labelDataService.listById(new ArrayList<Long>() {{
                                add(Long.valueOf(po.getLabelSequence()));
                            }});
                            if (UtilCollection.isEmpty(labelDataDTOList)) {
                                return;
                            }
                            if (labelDataDTOList.size() > 1) {
                                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                            }
                            BizLabelDataDTO labelDataDTO = labelDataDTOList.get(0);
                            stockBinPo.setFtyId(labelDataDTO.getFtyId());
                            stockBinPo.setLocationId(labelDataDTO.getLocationId());
                            stockBinPo.setWhId(labelDataDTO.getWhId());
                            stockBinPo.setMatId(labelDataDTO.getMatId());
                            stockBinPo.setTypeId(labelDataDTO.getTypeId());
                            stockBinPo.setBatchId(labelDataDTO.getBatchId());
                            stockBinPo.setBinId(labelDataDTO.getBinId());
                            break;
                    }
                }
                // 手输：02【按code或name查询】
                if (TaskConst.TYPE_MANUAL.equals(po.getResType())) {
                    switch (po.getMaterialArgs()) {
                        case TaskConst.BATCH:
                            // 按批次code查询批次
                            BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDtoByCode(po.getLabelSequence());
                            if (null == batchInfoDTO) {
                                return;
                            }
                            stockBinPo.setBatchId(batchInfoDTO.getId());
                            break;
                        case TaskConst.MATERIAL:
                            // 按物料code或name查询
                            QueryWrapper<DicMaterial> queryWrapper = new QueryWrapper<>();
                            queryWrapper.lambda().like(DicMaterial::getMatCode, po.getLabelSequence()).or()
                                    .like(DicMaterial::getMatName, po.getLabelSequence());
                            // 物料查询
                            List<DicMaterial> materialList = materialService.getMaterialList(queryWrapper);
                            if (UtilCollection.isNotEmpty(materialList)) {
                                stockBinPo
                                        .setMatIdList(new ArrayList<>(materialList.parallelStream().map(DicMaterial::getId).collect(Collectors.toSet())));
                            } else {
                                return;
                            }
                            break;
                        case TaskConst.LABEL:
                            // 按标签code查询
                            List<BizLabelDataDTO> labelDataDTOList = labelDataService.LabelDataListByCode(new ArrayList<String>() {{
                                add(po.getLabelSequence());
                            }});
                            if (UtilCollection.isEmpty(labelDataDTOList)) {
                                return;
                            }
                            if (labelDataDTOList.size() > 1) {
                                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                            }
                            BizLabelDataDTO labelDataDTO = labelDataDTOList.get(0);
                            stockBinPo.setFtyId(labelDataDTO.getFtyId());
                            stockBinPo.setLocationId(labelDataDTO.getLocationId());
                            stockBinPo.setWhId(labelDataDTO.getWhId());
                            stockBinPo.setMatId(labelDataDTO.getMatId());
                            stockBinPo.setTypeId(labelDataDTO.getTypeId());
                            stockBinPo.setBatchId(labelDataDTO.getBatchId());
                            stockBinPo.setBinId(labelDataDTO.getBinId());
                            break;
                    }
                }
                break;
            // 仓位标签
            case Const.LABEL_SORT_BIN:
                /* ****************** 请求类型 ******************/
                // 扫描：01【按id查询】
                if (TaskConst.TYPE_SCAN.equals(po.getResType())) {
                    stockBinPo.setBinId(Long.valueOf(po.getLabelSequence()));
                }
                // 手输：02【按code查询】
                if (TaskConst.TYPE_MANUAL.equals(po.getResType())) {
                    // 按仓位code查询
                    QueryWrapper<DicWhStorageBin> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().like(DicWhStorageBin::getBinCode, po.getLabelSequence());
                    // 物料查询
                    List<DicWhStorageBin> storageBinList = whStorageBinService.getWhStorageBinList(queryWrapper);
                    if (UtilCollection.isNotEmpty(storageBinList)) {
                        stockBinPo
                                .setBinIdList(new ArrayList<>(storageBinList.parallelStream().map(DicWhStorageBin::getId).collect(Collectors.toSet())));
                    } else {
                        return;
                    }
                }
                break;
        }

        List<StockBinDTO> stockBinDTOList = stockCommonService.getStockBinByStockBinPo(stockBinPo);
        log.info("获取扫描码的仓位库存-标签的库存信息 stockBinDTOList：{}", JSONObject.toJSONString(stockBinDTOList));
        if (UtilCollection.isNotEmpty(stockBinDTOList)) {
            // 仓位库存批次id集合
            Set<Long> batchIdSet = stockBinDTOList.stream().map(StockBinDTO::getBatchId).collect(Collectors.toSet());
            // 获取批次图片
            Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
            // 标签数据 根据batchId binId cellId 查询 对应标签
            List<BizLabelData> labelDataList = labelDataService.selectByStockBinList(stockBinDTOList);
            log.info("获取扫描码的仓位库存-标签的labelData信息 labelDataList：{}", JSONObject.toJSONString(labelDataList, SerializerFeature.IgnoreNonFieldGetter));
            Map<String, List<BizLabelData>> labelDataMap = null;
            if (UtilCollection.isNotEmpty(labelDataList)) {
                labelDataMap = labelDataList.stream().collect(Collectors.groupingBy(
                        label -> label.getMatId() + Const.HYPHEN + label.getFtyId() + Const.HYPHEN + label.getLocationId() + Const.HYPHEN + label
                                .getBatchId() + Const.HYPHEN + label.getWhId() + Const.HYPHEN + label.getTypeId() + Const.HYPHEN + label.getBinId()
                                + Const.HYPHEN + label.getCellId()));
            }

            // 遍历物料仓位库存
            for (StockBinDTO stockBinDTO : stockBinDTOList) {

                // 批次图片
                stockBinDTO.setBatchImgList(imgMap.get(stockBinDTO.getBatchId()));
                // 标签列表
                if (null != labelDataMap) {
                    String key =
                            stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN + stockBinDTO.getLocationId() + Const.HYPHEN
                                    + stockBinDTO.getBatchId() + Const.HYPHEN + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId() + Const.HYPHEN
                                    + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                    stockBinDTO.setLabelDataList(labelDataMap.get(key));
                    if (UtilCollection.isNotEmpty(stockBinDTO.getLabelDataList())) {
                        stockBinDTO
                                .setStockQty(stockBinDTO.getLabelDataList().stream().map(BizLabelData::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {

                        stockBinDTO.setStockQty(
                                stockBinDTO.getQty().add(stockBinDTO.getQtyTemp()).add(stockBinDTO.getQtyFreeze()).add(stockBinDTO.getQtyInspection())
                                        .add(stockBinDTO.getQtyTransfer()).add(stockBinDTO.getQtyHaste()));
                    }
                }

            }
        }

        MultiResultVO<StockBinDTO> vo = new MultiResultVO<>(stockBinDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);

        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询重量库存详情
     *
     * @param ctx ctx
     */
    public void selectStockBinWeightDetail(BizContext ctx) {
        StockBinWeightSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinWeightVo> result;

        IPage<StockBinWeightVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinWeightVo.class);
        }
        result = bizReportMapper.selectStockBinWeightDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinWeightVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 重量库存导出
     *
     * @param ctx
     */
    public void exportStockBinWeightDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("库存重量"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockBinWeightDetail(ctx);
        PageObjectVO<StockBinWeightVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockBinWeightVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 电子秤报表查询
     *
     * @param ctx 入参上下文 {@link ElectronicScalePO : 电子秤查询对象}
     */
    public void selectElectronicScaleRecord(BizContext ctx) {
        // 入参上下文
        ElectronicScalePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<ElectronicScaleVO> result;

        IPage<ElectronicScaleVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ElectronicScaleVO.class);
        }
        result = bizReportMapper.selectElectronicScaleRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<ElectronicScaleVO> vo = new PageObjectVO<>(result, totalCount);

        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }



    /**
     * 入库台账详情
     *
     * @param ctx ctx
     */
    public void selecInputLedgerRecord(BizContext ctx) {
        UnitizedInputLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 处理时间范围查询跨度问题 将查询条件中截至时间endTime天数+1
        this.convertStartTimeAndEndTime(po);

        List<UnitizedInputLedgerVo> result;

        IPage<UnitizedInputLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(UnitizedInputLedgerVo.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        result = bizReportMapper.selectInputLedgerRecordUnitized(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (UnitizedInputLedgerVo inputLedgerVo : result) {
                if(StringUtils.isEmpty(inputLedgerVo.getRegisterCode())){
                    inputLedgerVo.setUnitizedVisualCheck(null);
                }
                if(StringUtils.isEmpty(inputLedgerVo.getSignInspectCode())){
                    inputLedgerVo.setQualifiedQty(null);
                    inputLedgerVo.setUnqualifiedQty(null);
                    inputLedgerVo.setUnarrivalQty(null);
                }
                if(StringUtils.isEmpty(inputLedgerVo.getInputReceiptCode())){
                    inputLedgerVo.setInputQty(null);
                    inputLedgerVo.setInputPrice(null);
                }
                if(StringUtils.isEmpty(inputLedgerVo.getMatReturnReceiptCode())){
                    inputLedgerVo.setMatReturnQty(null);
                }

                String moveTypeKey =inputLedgerVo.getMoveType();
                Integer disposalResult=inputLedgerVo.getDisposalResult();
                Integer unitizedVisualCheck=inputLedgerVo.getUnitizedVisualCheck();
                String disposalResultI18n = EnumDisposalResult.getDescByValue(disposalResult);
                String unitizedVisualCheckI18n = EnumUnitizedVisualCheck.getDescByValue(unitizedVisualCheck);
                String isMainPartsI18n= EnumMainParts.getDescByValue(inputLedgerVo.getIsMainParts());
                inputLedgerVo.setDisposalResultI18n(disposalResultI18n);
                inputLedgerVo.setUnitizedVisualCheckI18n(unitizedVisualCheckI18n);
                inputLedgerVo.setIsMainPartsI18n(isMainPartsI18n);
                if(StringUtils.isNotEmpty(moveTypeKey)){
                    String moveTypeI18n = EnumMoveType.getDescByValue(moveTypeKey);
                    String moveTypeDesc=moveTypeKey+Const.HYPHEN+moveTypeI18n;
                    inputLedgerVo.setMoveTypeDesc(moveTypeDesc);
                }
            }
        }


        PageObjectVO<UnitizedInputLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    private void convertStartTimeAndEndTime(UnitizedInputLedgerPO po) {
        // 处理凭证日期中由于时间不为00:00:00导致查询存在时间范围跨度问题，将查询条件中截至时间endTime天数+1
        if (UtilObject.isNotNull(po.getRegisterDocDateEndTime())) {
            po.setRegisterDocDateEndTime(UtilDate.plusDays(po.getRegisterDocDateEndTime(), 1));
        }
        if (UtilObject.isNotNull(po.getInspectInputDocDateEndTime())) {
            po.setInspectInputDocDateEndTime(UtilDate.plusDays(po.getInspectInputDocDateEndTime(), 1));
        }
    }

    /**
     * 入库台账导出
     *
     * @param ctx
     */
    public void exportInputLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("成套设备入库台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selecInputLedgerRecord(ctx);
        PageObjectVO<UnitizedInputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(UnitizedInputLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    /**
     * 入库台账同步
     *
     * @param ctx
     */
    public void syncWarehousingAccount(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XML));
        bizCommonFile.setFileName(this.getFileName("成套设备入库台账"));
        bizCommonFile.setFileExt(Const.XML);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        this.selecInputLedgerRecord(ctx); //入库台账详情
        PageObjectVO<UnitizedInputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<UnitizedInputLedgerVo> resultList =  vo.getResultList();
        List<UnitizedInputLedgerSync> inputLedgerList =  UtilCollection.toList(resultList, UnitizedInputLedgerSync.class);
        Root root=new Root();
        root.setInputLedgerList(inputLedgerList);
        String xml=BeanXmlUtils.beanToXml(root,Root.class,true); //bean转xml
        FtpUtils.uploadInputLedger(xml,bizCommonFile); //上传文件 并ftp同步入库台账
    }


    /**
     * 直抵现场导出详情
     *
     * @param ctx ctx
     */
    public void selectDirectSceneRecord(BizContext ctx) {
        DirectScenePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<DirectSceneVo> result;

        IPage<DirectSceneVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(DirectSceneVo.class);
        }
        result = bizReportMapper.selectDirectSceneRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<DirectSceneVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }
    public void getInputDirect(BizContext ctx) {
        DirectScenePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<DirectSceneVo> result;

        IPage<DirectSceneVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(DirectSceneVo.class);
        }
        result = bizReportMapper.getInputDirect(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<DirectSceneVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 直抵现场导出
     *
     * @param ctx
     */
    public void exportDirectSceneRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("直抵现场台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectDirectSceneRecord(ctx);
        PageObjectVO<DirectSceneVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 由于导出数据在国际化处理之前封装，故此处手工对文案内容进行映射处理
        for (DirectSceneVo directSceneVo : vo.getResultList()) {
            if (UtilNumber.isNotEmpty(directSceneVo.getReceiptStatus())) {
                if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已完成");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("作业中");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已作业");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已提交");
                }
            }
        }

        UtilExcel.writeExcel(DirectSceneVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }
    public void getInputDirectExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("直抵现场台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getInputDirect(ctx);
        PageObjectVO<DirectSceneVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 由于导出数据在国际化处理之前封装，故此处手工对文案内容进行映射处理
        for (DirectSceneVo directSceneVo : vo.getResultList()) {
            if (UtilNumber.isNotEmpty(directSceneVo.getReceiptStatus())) {
                if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已完成");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("作业中");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已作业");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已提交");
                }
            }
        }

        UtilExcel.writeExcel(DirectSceneVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 入库物资跟踪表详情
     *
     * @param ctx ctx
     */
    public void selectDeliveryTrackRecord(BizContext ctx) {
        DeliveryTrackPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<DeliveryTrackVo> result;

        IPage<DeliveryTrackVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(DeliveryTrackVo.class);
        }
        result = bizReportMapper.selectDeliveryTrackRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<DeliveryTrackVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 入库物资跟踪表导出
     *
     * @param ctx
     */
    public void exportDeliveryTrackRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("直抵现场台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectDeliveryTrackRecord(ctx);
        PageObjectVO<InputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(DeliveryTrackVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    
    

    public void selectOutputLedgerRecord(BizContext ctx) {
        UnitizedOutputLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<UnitizedOutputLedgerVo> result;

        IPage<UnitizedOutputLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(UnitizedOutputLedgerVo.class);
        }
        result = bizReportMapper.selectOutputLedgerRecordUnitized(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (!CollectionUtils.isEmpty(result)) {
            result.forEach(i -> {
                Integer isWriterOff = i.getIsWriteOff();
                BigDecimal qty = i.getQty();
                BigDecimal outputPrice = i.getOutputPrice();
                if (UtilNumber.isNotEmpty(qty) && (isWriterOff != null) && isWriterOff.equals(EnumRealYn.FALSE.getIntValue())) {
                    if (qty.compareTo(BigDecimal.ZERO) > 0) {
                        i.setQty(qty.negate());
                        i.setOutputPrice(outputPrice.negate());
                    }
                }
            });
        }
        PageObjectVO<UnitizedOutputLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportOutputLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("成套设备出库台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectOutputLedgerRecord(ctx);
        PageObjectVO<UnitizedOutputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        for (UnitizedOutputLedgerVo unitizedOutputLedgerVo : vo.getResultList()) {
            if (UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getFtyCode()) && UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getFtyName())) {
                unitizedOutputLedgerVo.setFtyName(unitizedOutputLedgerVo.getFtyCode() + " - " + unitizedOutputLedgerVo.getFtyName());
            }
            if (UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getMainLocationCode()) && UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getMainLocationName())) {
                unitizedOutputLedgerVo.setMainLocationName(unitizedOutputLedgerVo.getMainLocationCode() + " - " + unitizedOutputLedgerVo.getMainLocationName());
            }
            if (UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getLocationCode()) && UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getLocationName())) {
                unitizedOutputLedgerVo.setLocationName(unitizedOutputLedgerVo.getLocationCode() + " - " + unitizedOutputLedgerVo.getLocationName());
            }
            if (UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getUnitCode()) && UtilString.isNotNullOrEmpty(unitizedOutputLedgerVo.getUnitName())) {
                unitizedOutputLedgerVo.setUnitName(unitizedOutputLedgerVo.getUnitCode() + " - " + unitizedOutputLedgerVo.getUnitName());
            }
        }

        UtilExcel.writeExcel(UnitizedOutputLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void selectReturnNewLedgerRecord(BizContext ctx) {
        ReturnNewLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<ReturnNewLedgerVo> result;

        IPage<ReturnNewLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ReturnNewLedgerVo.class);
        }
        result = bizReportMapper.selectReturnNewLedgerRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<ReturnNewLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportReturnNewLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("退旧换新台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectReturnNewLedgerRecord(ctx);
        PageObjectVO<ReturnNewLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ReturnNewLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void selectToolLedgerRecord(BizContext ctx) {
        ToolLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<ToolLedgerVo> result;

        IPage<ToolLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ToolLedgerVo.class);
        }
        result = bizReportMapper.selectToolLedgerRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<ToolLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportToolLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("工器具台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectToolLedgerRecord(ctx);
        PageObjectVO<ToolLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ToolLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询寿期台账详情
     *
     * @param ctx ctx
     */
    public void selectLifetimeDetail(BizContext ctx, boolean exportFlag) {

        LifetimeSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<LifetimeVO> result;

        IPage<LifetimeVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(LifetimeVO.class);
        }
        result = bizReportMapper.selectLifetimeDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (LifetimeVO lifetimeVO : result) {
                String packageTypeI18n = EnumPackageType.getDescByValue(lifetimeVO.getPackageType());
                String depositTypeI18n = EnumDepositType.getDescByValue(lifetimeVO.getDepositType());
                Integer inspectResult=lifetimeVO.getInspectResult();
                String inspectResultI18n = EnumInspectResult.getDescByValue(inspectResult);
                String scrapCauseI18n = EnumScrapFreezeCauseType.getDescByValue(lifetimeVO.getScrapCause());
                lifetimeVO.setPackageTypeI18n(packageTypeI18n);
                lifetimeVO.setDepositTypeI18n(depositTypeI18n);
                lifetimeVO.setInspectResultI18n(inspectResultI18n);
                lifetimeVO.setScrapCauseI18n(scrapCauseI18n);
                if(EnumInspectResult.DEMOTION.getValue().equals(inspectResult) || EnumInspectResult.SCRAP.getValue().equals(inspectResult)){
                    lifetimeVO.setDelayDate(null) ; //检定结果为“降级”或“报废”时，延期日期为空
                }
                if(EnumInspectResult.DELAY.getValue().equals(inspectResult) || EnumInspectResult.DEMOTION.getValue().equals(inspectResult)){
                    lifetimeVO.setScrapCause(null) ; //检定结果为“降级”或“延期”时，移动原因为空
                }
                String shelfLifeMax=lifetimeVO.getShelfLifeMax();
                if(StringUtils.isNotEmpty(shelfLifeMax)){
                    shelfLifeMax=shelfLifeMax+Const.MONTH_STR;
                }
                lifetimeVO.setShelfLifeMax(shelfLifeMax);

            }
        }

        PageObjectVO<LifetimeVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询寿期台账详情
     *
     * @param ctx ctx
     */
    public void selectLifetimeDetailNew(BizContext ctx, boolean exportFlag) {

        LifetimeSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<LifetimeVO> result;

        IPage<LifetimeVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(LifetimeVO.class);
        }
        po.setReceiptType(EnumReceiptType.UNITIZED_STOCK_LIFETIME_APPRIASAL.getValue());
        result = bizReportMapper.selectLifetimeDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (LifetimeVO lifetimeVO : result) {
                String packageTypeI18n = EnumPackageType.getDescByValue(lifetimeVO.getPackageType());
                String depositTypeI18n = EnumDepositType.getDescByValue(lifetimeVO.getDepositType());
                Integer inspectResult = lifetimeVO.getInspectResult();
                String inspectResultI18n = EnumInspectResult.getDescByValue(inspectResult);
                String scrapCauseI18n = EnumScrapFreezeCauseType.getDescByValue(lifetimeVO.getScrapCause());
                lifetimeVO.setPackageTypeI18n(packageTypeI18n);
                lifetimeVO.setDepositTypeI18n(depositTypeI18n);
                lifetimeVO.setInspectResultI18n(inspectResultI18n);
                lifetimeVO.setScrapCauseI18n(scrapCauseI18n);
                if (EnumInspectResult.DEMOTION.getValue().equals(inspectResult) || EnumInspectResult.SCRAP.getValue().equals(inspectResult)) {
                    lifetimeVO.setDelayDate(null); // 检定结果为“降级”或“报废”时，延期日期为空
                }
                if (EnumInspectResult.DELAY.getValue().equals(inspectResult) || EnumInspectResult.DEMOTION.getValue().equals(inspectResult)) {
                    lifetimeVO.setScrapCause(null); // 检定结果为“降级”或“延期”时，移动原因为空
                }
                String shelfLifeMax = lifetimeVO.getShelfLifeMax();
                if (StringUtils.isNotEmpty(shelfLifeMax)) {
                    shelfLifeMax = shelfLifeMax + Const.MONTH_STR;
                }
                lifetimeVO.setShelfLifeMax(shelfLifeMax);

            }
        }

        PageObjectVO<LifetimeNewVO> vo = new PageObjectVO<>(UtilCollection.toList(result, LifetimeNewVO.class), totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }


    /**
     * 导出寿期台账Excel
     *
     * @param ctx 上下文
     */
    public void exportLifetimeDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("寿期台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectLifetimeDetail(ctx, true);
        PageObjectVO<LifetimeVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(LifetimeVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void exportLifetimeDetailNew(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("库存寿期统计"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectLifetimeDetailNew(ctx, true);
        PageObjectVO<LifetimeNewVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(LifetimeNewVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询维保台账详情
     *
     * @param ctx ctx
     */
    public void selectMaintainDetail(BizContext ctx, boolean exportFlag) {

        MaintainSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<MaintainVO> result;

        IPage<MaintainVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(MaintainVO.class);
        }
        result = bizReportMapper.selectMaintainDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (MaintainVO maintainVO : result) {
                String packageTypeI18n = EnumPackageType.getDescByValue(maintainVO.getPackageType());
                String depositTypeI18n = EnumDepositType.getDescByValue(maintainVO.getDepositType());
                Integer maintenanceType=maintainVO.getMaintenanceType();
                String maintenanceTypeI18n = EnumMaintenanceType.getDescByValue(maintenanceType);
                maintainVO.setPackageTypeI18n(packageTypeI18n);
                maintainVO.setDepositTypeI18n(depositTypeI18n);
                maintainVO.setMaintenanceTypeI18n(maintenanceTypeI18n);
                Date maintenanceDateNormal=maintainVO.getMaintenanceDateNormal();
                Date maintenanceDatePro=maintainVO.getMaintenanceDatePro();
                Date maintenanceDateNext=null;
                String maintenanceProgram=""; //维护保养大纲  维保类型为“维护保养”时有值，其他两种类型为空
                String defectDescribe=""; //缺陷描述  维保类型为“库存状态检查”时有值，其他两种类型为空
                if(EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(maintenanceType) ){//包装更换
                    maintenanceDateNext=maintenanceDateNormal;
                }else if(EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(maintenanceType) ){//维护保养
                    maintenanceDateNext=maintenanceDatePro;
                    maintenanceProgram=maintainVO.getMaintenanceProgram();
                }else if(EnumMaintenanceType.DEFECT_MAINTAIN.getValue().equals(maintenanceType) ){//库存状态检查
                    defectDescribe=maintainVO.getDefectDescribe();
                }
                maintainVO.setMaintenanceDateNext(maintenanceDateNext);
                maintainVO.setMaintenanceProgram(maintenanceProgram);
                maintainVO.setDefectDescribe(defectDescribe);
            }
        }
        PageObjectVO<MaintainVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 导出维保台账Excel
     *
     * @param ctx 上下文
     */
    public void exportMaintainDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("维保台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectMaintainDetail(ctx, true);
        PageObjectVO<MaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(MaintainVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 定时任务同步DTS
     * @param ctx
     */
    public void syncDTS(BizContext ctx) {
        Object obj =  ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        if(obj!=null){
            Long templateTaskId=null;
            if(obj instanceof BizReceiptRegisterHeadDTO){
                BizReceiptRegisterHeadDTO dto=(BizReceiptRegisterHeadDTO)obj;
                templateTaskId=dto.getTemplateTaskId();
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,dto);
                this.syncDTSArrivalRegister(ctx);
            }else if (obj instanceof BizReceiptInspectHeadDTO){
                BizReceiptInspectHeadDTO dto=(BizReceiptInspectHeadDTO)obj;
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,dto);
                templateTaskId=dto.getTemplateTaskId();
                if(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue().equals(dto.getReceiptType())){ //成套设备质检会签
                    this.syncDTSSignInspect(ctx);
                }else if (EnumReceiptType.UNITIZED_DISTRIBUTE_INSPECTION.getValue().equals(dto.getReceiptType())){//成套设备分配质检
                    this.syncDTSDistributeInspect(ctx);
                }
            }else if (obj instanceof JSONObject){
                JSONObject Jobj=(JSONObject)obj;
                Integer receiptType= Jobj.getInteger("receiptType");
                templateTaskId= Jobj.getLong("templateTaskId");
                if(receiptType!=null){
                    if(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue().equals(receiptType)){ //成套设备质检会签
                        BizReceiptInspectHeadDTO dto= JSON.parseObject(Jobj.toJSONString(),BizReceiptInspectHeadDTO.class);
                        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,dto);
                        this.syncDTSSignInspect(ctx);
                    }else if (EnumReceiptType.UNITIZED_DISTRIBUTE_INSPECTION.getValue().equals(receiptType)){//成套设备分配质检
                        BizReceiptInspectHeadDTO dto=JSON.parseObject(Jobj.toJSONString(),BizReceiptInspectHeadDTO.class);
                        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,dto);
                        this.syncDTSDistributeInspect(ctx);
                    } else if (EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue().equals(receiptType)){//成套设备到货登记
                        BizReceiptRegisterHeadDTO dto= JSON.parseObject(Jobj.toJSONString(),BizReceiptRegisterHeadDTO.class);
                        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,dto);
                        this.syncDTSArrivalRegister(ctx);
                    }
                }
            }
            if(	UtilNumber.isNotNull(templateTaskId)){
                TemplateTaskDTO dto=new TemplateTaskDTO();
                dto.setId(templateTaskId);
                dto.setDealFlag(4);//4已处理
                dto.setDealTime(new Date());
                templateTaskDataWrap.updateDtoById(dto);
            }
        }
    }


    /**
     * 到货登记同步dts
     */
    public void syncDTSArrivalRegister( BizContext ctx) {
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(po==null ) {
            return;
        }
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XML));
        bizCommonFile.setFileName(this.getFileName("成套到货登记"));
        bizCommonFile.setFileExt(Const.XML);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        List<DtsSync> dtsSyncList=new ArrayList<>();
        for(BizReceiptWaybillDTO waybill: po.getWaybillDTOList()){
            DtsSync dtsSync=new DtsSync();
            dtsSync.setPKG_NO(waybill.getCaseCode());//箱号
            dtsSync.setRECEIPT_SITE(po.getDeliveryPoint());//收货地点
            dtsSync.setRECEIPT_DATE(UtilDate.getStringDateForDate(po.getReceiveDate(),Const.FORMATTER_DATE2));//收货时间
            dtsSync.setPKG_TYPE("CA");//包装
            dtsSync.setGOODS_STATUS(EnumUnitizedVisualCheck.getCodeByValue(waybill.getUnitizedVisualCheck()));//货物状况
            dtsSync.setDELI_AMOUNT("1");//交付数量
            dtsSync.setCHECK_DATE(UtilDate.getStringDateForDate(po.getReceiveDate(),Const.FORMATTER_DATE2));//检查日期
            dtsSync.setREGISTER_CODE(po.getReceiptCode());//到货登记单号
            dtsSyncList.add(dtsSync);
        }
        DtsRoot root=new DtsRoot();
        root.setDtsSyncList(dtsSyncList);
        String xml=BeanXmlUtils.beanToXml(root,DtsRoot.class,true); //bean转xml
        String pdfFileName=po.getReceiptCode()+".pdf";
        String syncZipFileNamePrefix=po.getReceiptCode() +"-"+ po.getReceiptType() +"-"+UtilDate.getStringDateForDate(new Date(),Const.FORMATTER_YYYYMMDDHHMMSS);
        DtsFileZip dtsFileZip =new DtsFileZip();
        List<DtsFileSync> dtsFileSyncList =new ArrayList<>();
        DtsFileSync dtsFileSync=new DtsFileSync();
        dtsFileSync.setXml(xml);
        dtsFileSync.setBizCommonFile(bizCommonFile);
        dtsFileSync.setSyncFileName(syncZipFileNamePrefix+".xml");
        dtsFileSyncList.add(dtsFileSync);
        BizCommonFile pdfCommonFile = bizCommonFileDataWrap.getByFileName(po.getId(),po.getReceiptType(),pdfFileName);
        if(pdfCommonFile!=null){
            String fileRootPath = UtilConst.getInstance().getFilePath();
            String filePath = pdfCommonFile.getPath();
            String fileCode = pdfCommonFile.getFileCode();
            File pdfFile = new File(fileRootPath + filePath + fileCode);
            DtsFileSync dtsPdfFileSync=new DtsFileSync();
            dtsPdfFileSync.setFile(pdfFile);
            dtsPdfFileSync.setBizCommonFile(pdfCommonFile);
            dtsPdfFileSync.setSyncFileName(syncZipFileNamePrefix+".pdf");
            dtsFileSyncList.add(dtsPdfFileSync);
        }
        String syncZipFileName =syncZipFileNamePrefix+".zip";
        String fileRootPath = UtilConst.getInstance().getLedgerFilePath();
        String filePath =  FtpUtils.getFilePath();
        String syncZipFilePath=fileRootPath + filePath;
        dtsFileZip.setSyncZipFileName(syncZipFileName);
        dtsFileZip.setSyncZipFilePath(syncZipFilePath);
        dtsFileZip.setDtsFileSyncList(dtsFileSyncList);
        FtpUtils.uploadDtsFileSync(dtsFileZip); //同步文件到dts 并打成zip包
    }

    /**
     * 分配质检同步dts
     */
    public void syncDTSDistributeInspect( BizContext ctx) {
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(po==null ) {
            return;
        }
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XML));
        bizCommonFile.setFileName(this.getFileName("成套设备分配质检"));
        bizCommonFile.setFileExt(Const.XML);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());

        List<DtsSync> dtsSyncList=new ArrayList<>();
        for(BizReceiptWaybillDTO waybill: po.getDistributeWaybillDTOList()){
            DtsSync dtsSync=new DtsSync();
            dtsSync.setPKG_NO(waybill.getCaseCode());//箱号
            dtsSync.setFORE_UNPKG_DATE(UtilDate.getStringDateForDate(po.getInspectTime(),Const.FORMATTER_DATE2));//预计开箱日期
            dtsSync.setASSIGN_QUALITY_CODE(po.getReceiptCode());//分配质检单号
            dtsSyncList.add(dtsSync);
        }
        String syncZipFileNamePrefix=po.getReceiptCode() +"-"+ po.getReceiptType() +"-"+UtilDate.getStringDateForDate(new Date(),Const.FORMATTER_YYYYMMDDHHMMSS);
        DtsRoot root=new DtsRoot();
        root.setDtsSyncList(dtsSyncList);
        String xml=BeanXmlUtils.beanToXml(root,DtsRoot.class,true); //bean转xml
        DtsFileZip dtsFileZip =new DtsFileZip();
        List<DtsFileSync> dtsFileSyncList =new ArrayList<>();
        DtsFileSync dtsFileSync=new DtsFileSync();
        dtsFileSync.setXml(xml);
        dtsFileSync.setSyncFileName(syncZipFileNamePrefix+".xml");
        dtsFileSync.setBizCommonFile(bizCommonFile);
        dtsFileSyncList.add(dtsFileSync);
        String syncZipFileName =syncZipFileNamePrefix+".zip";
        String fileRootPath = UtilConst.getInstance().getLedgerFilePath();
        String filePath =  FtpUtils.getFilePath();
        String syncZipFilePath=fileRootPath + filePath;
        dtsFileZip.setSyncZipFileName(syncZipFileName);
        dtsFileZip.setSyncZipFilePath(syncZipFilePath);
        dtsFileZip.setDtsFileSyncList(dtsFileSyncList);
        FtpUtils.uploadDtsFileSync(dtsFileZip); //同步文件到dts 并打成zip包
    }
    /**
     * 质检会签同步dts
     */
    public void syncDTSSignInspect( BizContext ctx) {
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(po==null ) {
            return;
        }
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XML));
        bizCommonFile.setFileName(this.getFileName("成套质检会签"));
        bizCommonFile.setFileExt(Const.XML);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        List<DtsSync> dtsSyncList=new ArrayList<>();
        List<UnitizedInputLedgerVo> syncList= bizReportMapper.selectDtsSyncRecordBySignInspect(po);
        for(UnitizedInputLedgerVo record: syncList){
            String gvnbh= record.getGvnbh(); //数量差异通知单号/GVN号
            String ncrbh= record.getNcrbh(); //不符合项通知单号/NCR号
            BigDecimal unqualifiedQty=record.getUnqualifiedQty();//不合格数量
            String QUALITY_STATUS="0"; //质量状态 现场检查录入0：合格  1：NCR  2：GVN   3：其他
            String GVN_FLAG="1"; //是否生成GVN 0：是   1：否
            if(StringUtils.isNotEmpty(ncrbh)){
                QUALITY_STATUS="1";
            }
            if(StringUtils.isNotEmpty(gvnbh)){
                GVN_FLAG="0";
                QUALITY_STATUS="2";
            }
            if(StringUtils.isNotEmpty(ncrbh) && StringUtils.isNotEmpty(gvnbh)){
                QUALITY_STATUS="1";
            }
            DtsSync dtsSync=new DtsSync();
            dtsSync.setPKG_NO(record.getCaseCode());//箱号
            dtsSync.setUP(record.getExtend29());//UP码
            dtsSync.setQUALITY_STATUS(QUALITY_STATUS);//质量状态 0：合格  1：NCR  2：GVN   3：其他
            dtsSync.setACCEP_AMOUNT(UtilObject.getStringOrEmpty(record.getArrivalQty()));//验收数量
            dtsSync.setSTANDARD_AMOUNT(UtilObject.getStringOrEmpty(record.getQualifiedQty()));//合格数量
            dtsSync.setNCR_AMOUNT(UtilObject.getStringOrEmpty(unqualifiedQty));//NCR数量
            dtsSync.setGVN_FLAG(GVN_FLAG);//是否生成GVN 0：是   1：否
            dtsSync.setGVN_NO(gvnbh);//GVN编码
            dtsSync.setNCR1(ncrbh);//NCR1编码
            dtsSync.setNCR2("");//NCR2编码
            dtsSync.setNCR3("");//NCR3编码
            dtsSync.setCHK_RESULT(EnumSignInspectResultType.getDtsFlagByValue(po.getSignInspectResult()));//检查结果
            dtsSync.setUNPKG_DATE(UtilDate.getStringDateForDate(po.getSubmitTime(),Const.FORMATTER_DATE2));//开箱日期
            dtsSync.setSIGN_INSPECT_CODE(po.getReceiptCode());//质检会签单号
            if(StringUtils.isNotEmpty(gvnbh)){
                Integer numberInconformityNoticeStatus=record.getNumberInconformityNoticeStatus();
                String GVN_STATUS="0"; //GVN关闭状态
                if(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(numberInconformityNoticeStatus)){
                    GVN_STATUS="1";
                    dtsSync.setCLOSE_OPERATOR(record.getCloseOperator());//GVN关闭人
                    dtsSync.setCLOSE_DATE(UtilDate.getStringDateForDate(record.getCloseDate(),Const.FORMATTER_DATE2));//GVN关闭时间
                }
                dtsSync.setGVN_STATUS(GVN_STATUS);//GVN关闭状态
                dtsSync.setGVN_DEAL_METHOD("2");//GVN处理方式  1:赠与 2:修改订单 3:增加订单行项目 4:退还
            }
            dtsSyncList.add(dtsSync);
        }
        DtsRoot root=new DtsRoot();
        root.setDtsSyncList(dtsSyncList);
        String xml=BeanXmlUtils.beanToXml(root,DtsRoot.class,true); //bean转xm
        String pdfFileName=po.getReceiptCode()+".pdf";
        String syncZipFileNamePrefix=po.getReceiptCode() +"-"+ po.getReceiptType() +"-"+UtilDate.getStringDateForDate(new Date(),Const.FORMATTER_YYYYMMDDHHMMSS);
        DtsFileZip dtsFileZip =new DtsFileZip();
        List<DtsFileSync> dtsFileSyncList =new ArrayList<>();
        DtsFileSync dtsFileSync=new DtsFileSync();
        dtsFileSync.setXml(xml);
        dtsFileSync.setSyncFileName(syncZipFileNamePrefix+".xml");
        dtsFileSync.setBizCommonFile(bizCommonFile);
        dtsFileSyncList.add(dtsFileSync);
        BizCommonFile pdfCommonFile = bizCommonFileDataWrap.getByFileName(po.getId(),po.getReceiptType(),pdfFileName);
        if(pdfCommonFile!=null){
            String fileRootPath = UtilConst.getInstance().getFilePath();
            String filePath = pdfCommonFile.getPath();
            String fileCode = pdfCommonFile.getFileCode();
            File pdfFile = new File(fileRootPath + filePath + fileCode);
            DtsFileSync dtsPdfFileSync=new DtsFileSync();
            dtsPdfFileSync.setFile(pdfFile);
            dtsPdfFileSync.setBizCommonFile(pdfCommonFile);
            dtsPdfFileSync.setSyncFileName(syncZipFileNamePrefix+".pdf");
            dtsFileSyncList.add(dtsPdfFileSync);
        }
        String syncZipFileName =syncZipFileNamePrefix+".zip";
        String fileRootPath = UtilConst.getInstance().getLedgerFilePath();
        String filePath =  FtpUtils.getFilePath();
        String syncZipFilePath=fileRootPath + filePath;
        dtsFileZip.setSyncZipFileName(syncZipFileName);
        dtsFileZip.setSyncZipFilePath(syncZipFilePath);
        dtsFileZip.setDtsFileSyncList(dtsFileSyncList);
        FtpUtils.uploadDtsFileSync(dtsFileZip); //同步文件到dts 并打成zip包
    }

    public void getTimeInput(BizContext ctx) {
        TimeInputDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode()) && po.getReceiptCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        List<TimeInputVO> result;
        IPage<TimeInputDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(TimeInputDTO.class);
        }
        result = bizReportMapper.getTimeInput(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<TimeInputVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getTimeInputExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("入库时效"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getTimeInput(ctx);
        PageObjectVO<TimeInputVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(TimeInputVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getTimeOutput(BizContext ctx) {
        TimeOutputDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode()) && po.getReceiptCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        List<TimeOutputVO> result;
        IPage<TimeOutputDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(TimeOutputDTO.class);
        }
        result = bizReportMapper.getTimeOutput(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<TimeOutputVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getTimeOutputExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("出库时效"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getTimeOutput(ctx);
        PageObjectVO<TimeOutputVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(TimeOutputVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getTimeUnload(BizContext ctx) {
        TimeUnloadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode()) && po.getReceiptCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        List<TimeUnloadVO> result;
        IPage<TimeUnloadDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(TimeUnloadDTO.class);
        }
        result = bizReportMapper.getTimeUnload(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<TimeUnloadVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getTimeUnloadExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("备料下架时效"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getTimeUnload(ctx);
        PageObjectVO<TimeUnloadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(TimeUnloadVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getTimeDirect(BizContext ctx) {
        TimeDirectDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getMatCode()) && po.getMatCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getMatCode().split(",")).collect(Collectors.toList());
            po.setMatCodeList(strList);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getBatchCode()) && po.getBatchCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getBatchCode().split(",")).collect(Collectors.toList());
            po.setBatchCodeList(strList);
            po.setBatchCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend29()) && po.getExtend29().contains(",")) {
            List<String> strList = Arrays.stream(po.getExtend29().split(",")).collect(Collectors.toList());
            po.setExtend29List(strList);
            po.setExtend29(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend20()) && po.getExtend20().contains(",")) {
            List<String> strList = Arrays.stream(po.getExtend20().split(",")).collect(Collectors.toList());
            po.setExtend20List(strList);
            po.setExtend20(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend2()) && po.getExtend2().contains(",")) {
            List<String> strList = Arrays.stream(po.getExtend2().split(",")).collect(Collectors.toList());
            po.setExtend2List(strList);
            po.setExtend2(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getFunctionalLocationCode()) && po.getFunctionalLocationCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getFunctionalLocationCode().split(",")).collect(Collectors.toList());
            po.setFunctionalLocationCodeList(strList);
            po.setFunctionalLocationCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode()) && po.getReceiptCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        List<TimeDirectVO> result;
        IPage<TimeDirectDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(TimeDirectDTO.class);
        }
        result = bizReportMapper.getTimeDirect(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<TimeDirectVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getTimeDirectExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("直抵现场时效"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getTimeDirect(ctx);
        PageObjectVO<TimeDirectVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(TimeDirectVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getOccupy(BizContext ctx) {
        OccupyDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getMatCode()) && po.getMatCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getMatCode().split(",")).collect(Collectors.toList());
            po.setMatCodeList(strList);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getBatchCode()) && po.getBatchCode().contains(",")) {
            List<String> strList = Arrays.stream(po.getBatchCode().split(",")).collect(Collectors.toList());
            po.setBatchCodeList(strList);
            po.setBatchCode(null);
        }
        List<OccupyVO> result;
        IPage<OccupyDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(OccupyDTO.class);
        }
        result = bizReportMapper.getOccupy(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<OccupyVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getOccupyDetail(BizContext ctx) {
        OccupyDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<OccupyVO> result;
        IPage<OccupyDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(OccupyDTO.class);
        }
        result = bizReportMapper.getOccupyDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<OccupyVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getOccupyExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("物资领用质量有效期监控报表"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getOccupy(ctx);
        PageObjectVO<OccupyVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(OccupyVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getOver(BizContext ctx) {
        OverDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<OverVO> result;
        IPage<OverDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(OverDTO.class);
        }
        result = bizReportMapper.getOver(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<OverVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getOverExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("领用多发量统计报表"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getOver(ctx);
        PageObjectVO<OverVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(OverVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getOverDetail(BizContext ctx) {
        OverDetailDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<OverDetailVO> result;
        IPage<OverDetailDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(OverDetailDTO.class);
        }
        result = bizReportMapper.getOverDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<OverDetailVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getOverDetailExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("领用多发量统计报表-超发"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getOverDetail(ctx);
        PageObjectVO<OverDetailVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(OverDetailVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getSubtractDetail(BizContext ctx) {
        SubtractDetailDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<SubtractDetailVO> result;
        IPage<SubtractDetailDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(SubtractDetailDTO.class);
        }
        result = bizReportMapper.getSubtractDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<SubtractDetailVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getSubtractDetailExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("领用多发量统计报表-冲减"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getSubtractDetail(ctx);
        PageObjectVO<SubtractDetailVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(SubtractDetailVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getInspectCaseRate(BizContext ctx) {
        InspectCaseDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Map result = bizReportMapper.getInspectCaseRate(po);
        SingleResultVO<Map> vo = new SingleResultVO<>(result);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getInspectCase(BizContext ctx) {
        InspectCaseDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<InspectCaseVO> result;
        IPage<InspectCaseDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(InspectCaseDTO.class);
        }
        result = bizReportMapper.getInspectCase(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        for (InspectCaseVO item : result) {
            // 是否进口核安全设备 1是2否
            item.setIsSafeStr(EnumRealYn.TRUE.getIntValue().equals(item.getIsSafe()) ? EnumIsYesOrNo.YES.getValue() : EnumIsYesOrNo.NO.getValue());
        }
        PageObjectVO<InspectCaseVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getInspectCaseExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("开箱验收跟踪表-箱件维度"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getInspectCase(ctx);
        PageObjectVO<InspectCaseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(InspectCaseVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getInspectMat(BizContext ctx) {
        InspectMatDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<InspectMatVO> result;
        IPage<InspectMatDTO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(InspectMatDTO.class);
        }
        result = bizReportMapper.getInspectMat(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        for (InspectMatVO item : result) {
            // 是否进口核安全设备 1是2否
            item.setIsSafeStr(EnumRealYn.TRUE.getIntValue().equals(item.getIsSafe()) ? EnumIsYesOrNo.YES.getValue() : EnumIsYesOrNo.NO.getValue());
            // 采购类型 1联合采购2自主采购
            item.setProcurementMethodStr(EnumRealYn.TRUE.getIntValue().equals(item.getProcurementMethod()) ? "联合采购" : "自主采购");
        }
        PageObjectVO<InspectMatVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getInspectMatExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("开箱验收跟踪表-物料维度"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getInspectMat(ctx);
        PageObjectVO<InspectMatVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(InspectMatVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getNcr(BizContext ctx) {
        NcrPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<NcrVO> result;
        IPage<NcrPO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(NcrPO.class);
        }
        result = bizReportMapper.getNcr(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        for (NcrVO item : result) {
            // 设备主体标识 1是
            item.setIsMainPartsStr(EnumRealYn.TRUE.getIntValue().equals(item.getIsMainParts()) ? EnumIsYesOrNo.YES.getValue() : EnumIsYesOrNo.NO.getValue());
        }
        PageObjectVO<NcrVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getNcrExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("NCR统计报表"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getNcr(ctx);
        PageObjectVO<NcrVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(NcrVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getMatDocDetail(BizContext ctx) {
        MatDocSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<MatDocVO> result;
        IPage<MatDocVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(MatDocVO.class);
            if (page.orders().isEmpty()) {
                page.orders().add(OrderItem.desc("submit_date"));
            } else {
                page.orders().forEach(obj -> {
                    obj.setColumn(obj.getColumn().replace("unit", "unit_id")
                            .replace("fty_code", "fty_id")
                            .replace("location_code", "location_id")
                            .replace("modify_user_name", "modify_user_id")
                            .replace("parent_mat_code", "parent_mat_id"));
                });
            }
        }
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            po.setMatCode(po.getMatCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getMatCode().split(",")).collect(Collectors.toList());
            po.setMatCodeList(strList);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getMatDocCode())) {
            po.setMatDocCode(po.getMatDocCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getMatDocCode().split(",")).collect(Collectors.toList());
            po.setMatDocCodeList(strList);
            po.setMatDocCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getBatchCode())) {
            po.setBatchCode(po.getBatchCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getBatchCode().split(",")).collect(Collectors.toList());
            po.setBatchCodeList(strList);
            po.setBatchCode(null);
        }
        result = bizReportMapper.getMatDocDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        for (MatDocVO matDocVo : result) {
            if ("H".equals(matDocVo.getDebitCredit())) {
                matDocVo.setQty(matDocVo.getQty().negate());
            }
        }
        dataFillService.fillAttr(result);
        PageObjectVO<MatDocVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getMatDocDetailExcel(BizContext ctx) {
        MatDocSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("物料明细查询"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        po.setPaging(Boolean.FALSE);
        this.getMatDocDetail(ctx);
        PageObjectVO<MatDocVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        for (MatDocVO matDocVo : vo.getResultList()) {
            if (UtilNumber.isNotEmpty(matDocVo.getReceiptType())) {
                String receiptTypeI18n = i18nTextCommonService.getNameMessage(Const.DEFAULT_LANG_CODE, Const.RECEIPT_TYPE, matDocVo.getReceiptType().toString());
                matDocVo.setReceiptTypeI18n(receiptTypeI18n);
            }
        }
        UtilExcel.writeExcel(MatDocVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getArrivalTrackCaseDetail(BizContext ctx) {
        ArrivalTrackSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<ArrivalTrackVO> result;
        IPage<ArrivalTrackVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ArrivalTrackVO.class);
            if (page.orders().isEmpty()) {
                page.orders().add(OrderItem.desc("create_time"));
            } else {
                page.orders().forEach(obj -> {
                    obj.setColumn(obj.getColumn().replace("is_safe_str", "is_safe")
                            .replace("procurement_method_str", "procurement_method")
                            .replace("visual_check_str", "visual_check"));
                });
            }
        }

        if (UtilString.isNotNullOrEmpty(po.getCaseCode())) {
            po.setCaseCode(po.getCaseCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getCaseCode().split(",")).collect(Collectors.toList());
            po.setCaseCodeList(strList);
            po.setCaseCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode())) {
            po.setReceiptCode(po.getReceiptCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        po.setNow(UtilDate.getNow());
        result = bizReportMapper.getArrivalTrackCaseDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        for (ArrivalTrackVO vo : result) {
            // 是否进口核安全设备 1是2否
            vo.setIsSafeStr(EnumRealYn.TRUE.getIntValue().equals(vo.getIsSafe()) ? EnumIsYesOrNo.YES.getValue() : EnumIsYesOrNo.NO.getValue());
            // 箱件状态
            if (UtilNumber.isNotNull(vo.getVisualCheck())) {
                vo.setVisualCheckStr(i18nTextCommonService.getNameMessage(Const.DEFAULT_LANG_CODE, "visualCheck", vo.getVisualCheck().toString()));
            } else if (UtilNumber.isNotNull(vo.getUnitizedVisualCheck())) {
                vo.setVisualCheckStr(EnumUnitizedVisualCheck.getDescByValue(vo.getUnitizedVisualCheck()));
            }
            // 采购类型 1联合采购2自主采购
            vo.setProcurementMethodStr(EnumRealYn.TRUE.getIntValue().equals(vo.getProcurementMethod()) ? "联合采购" : "自主采购");
        }
        PageObjectVO<ArrivalTrackVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getArrivalTrackDetailCaseExcel(BizContext ctx) {
        ArrivalTrackSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("到货接收跟踪-箱件维度"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        po.setPaging(Boolean.FALSE);
        this.getArrivalTrackCaseDetail(ctx);
        PageObjectVO<ArrivalTrackVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(ArrivalTrackVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getArrivalTrackReceiptDetail(BizContext ctx) {
        ArrivalTrackSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<ArrivalTrackReceiptVO> result;
        IPage<ArrivalTrackReceiptVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ArrivalTrackReceiptVO.class);
            if (page.orders().isEmpty()) {
                page.orders().add(OrderItem.desc("create_time"));
            } else {
                page.orders().forEach(obj -> {
                    obj.setColumn(obj.getColumn().replace("is_safe_str", "is_safe")
                            .replace("procurement_method_str", "procurement_method"));
                });
            }
        }

        if (UtilString.isNotNullOrEmpty(po.getCaseCode())) {
            po.setCaseCode(po.getCaseCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getCaseCode().split(",")).collect(Collectors.toList());
            po.setCaseCodeList(strList);
            po.setCaseCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode())) {
            po.setReceiptCode(po.getReceiptCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        po.setNow(UtilDate.getNow());
        result = bizReportMapper.getArrivalTrackReceiptDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        for (ArrivalTrackReceiptVO vo : result) {
            // 是否进口核安全设备 1是2否
            vo.setIsSafeStr(EnumRealYn.TRUE.getIntValue().equals(vo.getIsSafe()) ? EnumIsYesOrNo.YES.getValue() : EnumIsYesOrNo.NO.getValue());
            // 采购类型 1联合采购2自主采购
            vo.setProcurementMethodStr(EnumRealYn.TRUE.getIntValue().equals(vo.getProcurementMethod()) ? "联合采购" : "自主采购");
        }
        PageObjectVO<ArrivalTrackReceiptVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getArrivalTrackReceiptDetailExcel(BizContext ctx) {
        ArrivalTrackSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("到货接收跟踪-批次维度"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        po.setPaging(Boolean.FALSE);
        this.getArrivalTrackReceiptDetail(ctx);
        PageObjectVO<ArrivalTrackReceiptVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(ArrivalTrackReceiptVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getGvnInconformityDetail(BizContext ctx) {
        GvnInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<GvnInconformityVO> result;
        IPage<GvnInconformityVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(GvnInconformityVO.class);
            if (page.orders().isEmpty()) {
                page.orders().add(OrderItem.desc("create_time"));
            } else {
                page.orders().forEach(obj -> {
                    obj.setColumn(obj.getColumn().replace("unit_name", "unit_id"));
                });
            }
        }

        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            po.setMatCode(po.getMatCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getMatCode().split(",")).collect(Collectors.toList());
            po.setMatCodeList(strList);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode())) {
            po.setReceiptCode(po.getReceiptCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        po.setNow(UtilDate.getNow());
        result = bizReportMapper.getGvnInconformityDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }

        dataFillService.fillAttr(result);
        PageObjectVO<GvnInconformityVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getGvnInconformityDetailExcel(BizContext ctx) {
        GvnInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("GVN统计"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        po.setPaging(Boolean.FALSE);
        this.getGvnInconformityDetail(ctx);
        PageObjectVO<GvnInconformityVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        for (GvnInconformityVO result : vo.getResultList()) {
            if (UtilString.isNotNullOrEmpty(result.getSolveReason())) {
                String solveReasonDesc = EnumSolveReason.getDescByValue(result.getSolveReason());
                if (UtilString.isNotNullOrEmpty(solveReasonDesc)) {
                    result.setSolveReason(solveReasonDesc);
                }
            }
        }
        UtilExcel.writeExcel(GvnInconformityVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getMaterialReturnDetail(BizContext ctx) {
        MaterialReturnSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<MaterialReturnVO> result;
        IPage<MaterialReturnVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(MaterialReturnVO.class);
            if (page.orders().isEmpty()) {
                page.orders().add(OrderItem.desc("create_time"));
            } else {
                page.orders().forEach(obj -> {
                    obj.setColumn(obj.getColumn().replace("unit_name", "unit_id"));
                });
            }
        }

        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            po.setMatCode(po.getMatCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getMatCode().split(",")).collect(Collectors.toList());
            po.setMatCodeList(strList);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getReceiptCode())) {
            po.setReceiptCode(po.getReceiptCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getReceiptCode().split(",")).collect(Collectors.toList());
            po.setReceiptCodeList(strList);
            po.setReceiptCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend29())) {
            po.setExtend29(po.getExtend29().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getExtend29().split(",")).collect(Collectors.toList());
            po.setExtend29List(strList);
            po.setExtend29(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend2())) {
            po.setExtend2(po.getExtend2().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getExtend2().split(",")).collect(Collectors.toList());
            po.setExtend2List(strList);
            po.setExtend2(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend20())) {
            po.setExtend20(po.getExtend20().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getExtend20().split(",")).collect(Collectors.toList());
            po.setExtend20List(strList);
            po.setExtend20(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getFunctionalLocationCode())) {
            po.setFunctionalLocationCode(po.getFunctionalLocationCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getFunctionalLocationCode().split(",")).collect(Collectors.toList());
            po.setFunctionalLocationCodeList(strList);
            po.setFunctionalLocationCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getInspectReceiptCode())) {
            po.setInspectReceiptCode(po.getInspectReceiptCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getInspectReceiptCode().split(",")).collect(Collectors.toList());
            po.setInspectReceiptCodeList(strList);
            po.setInspectReceiptCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getNoticeReceiptCode())) {
            po.setNoticeReceiptCode(po.getNoticeReceiptCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getNoticeReceiptCode().split(",")).collect(Collectors.toList());
            po.setNoticeReceiptCodeList(strList);
            po.setNoticeReceiptCode(null);
        }
        po.setNow(UtilDate.getNow());
        result = bizReportMapper.getMaterialReturnDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }

        dataFillService.fillAttr(result);
        PageObjectVO<MaterialReturnVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getMaterialReturnDetailExcel(BizContext ctx) {
        MaterialReturnSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("物资返运统计"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        po.setPaging(Boolean.FALSE);
        this.getMaterialReturnDetail(ctx);
        PageObjectVO<MaterialReturnVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(MaterialReturnVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getExtend28Down(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumMatType.toList()));
    }

    public void getUnitizedMaintainDetail(BizContext ctx) {
        MaintainSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<UnitizedMaintainVO> result;
        IPage<UnitizedMaintainVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(UnitizedMaintainVO.class);
            if (page.orders().isEmpty()) {
                page.orders().add(OrderItem.desc("head.create_time"));
            } else {
                page.orders().forEach(obj -> {
                    obj.setColumn(obj.getColumn().replace("fty_code", "fty_id"));
                    if ("location_code".equals(obj.getColumn())) {
                        obj.setColumn(obj.getColumn().replace("location_code", "location_id"));
                    }
                    obj.setColumn(obj.getColumn().replace("plan_submit_user_name", "preh.submit_user_id"));
                    obj.setColumn(obj.getColumn().replace("submit_user_name", "head.submit_user_id"));
                    obj.setColumn(obj.getColumn().replace("unit_name", "item.unit_id"));
                    obj.setColumn(obj.getColumn().replace("bin_code", "item.bin_id"));
                });
            }
        }
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            po.setMatCode(po.getMatCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getMatCode().split(",")).collect(Collectors.toList());
            po.setMatCodeList(strList);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getBatchCode())) {
            po.setBatchCode(po.getBatchCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getBatchCode().split(",")).collect(Collectors.toList());
            po.setBatchCodeList(strList);
            po.setBatchCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getFunctionalLocationCode())) {
            po.setFunctionalLocationCode(po.getFunctionalLocationCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getFunctionalLocationCode().split(",")).collect(Collectors.toList());
            po.setFunctionalLocationCodeList(strList);
            po.setFunctionalLocationCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend2())) {
            po.setExtend2(po.getExtend2().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getExtend2().split(",")).collect(Collectors.toList());
            po.setExtend2List(strList);
            po.setExtend2(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend20())) {
            po.setExtend20(po.getExtend20().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getExtend20().split(",")).collect(Collectors.toList());
            po.setExtend20List(strList);
            po.setExtend20(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend29())) {
            po.setExtend29(po.getExtend29().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getExtend29().split(",")).collect(Collectors.toList());
            po.setExtend29List(strList);
            po.setExtend29(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getExtend61())) {
            po.setExtend61(po.getExtend61().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getExtend61().split(",")).collect(Collectors.toList());
            po.setExtend61List(strList);
            po.setExtend61(null);
        }
        result = bizReportMapper.getUnitizedMaintainDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }

        dataFillService.fillAttr(result);
        PageObjectVO<UnitizedMaintainVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getUnitizedMaintainDetailExcel(BizContext ctx) {
        MaintainSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("维护保养统计"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        po.setPaging(Boolean.FALSE);
        this.getUnitizedMaintainDetail(ctx);
        PageObjectVO<UnitizedMaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(UnitizedMaintainVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void getUnitizedTempStoreDetail(BizContext ctx) {
        TempStoreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<TempStoreVO> result;
        IPage<TempStoreVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(TempStoreVO.class);
            if (page.orders().isEmpty()) {
                page.orders().add(OrderItem.desc("bbi.input_date"));
            } else {
                page.orders().forEach(obj -> {
                    obj.setColumn(obj.getColumn().replace("location_code", "location_id"));
                });
            }
        }
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            po.setMatCode(po.getMatCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getMatCode().split(",")).collect(Collectors.toList());
            po.setMatCodeList(strList);
            po.setMatCode(null);
        }
        if (UtilString.isNotNullOrEmpty(po.getBatchCode())) {
            po.setBatchCode(po.getBatchCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getBatchCode().split(",")).collect(Collectors.toList());
            po.setBatchCodeList(strList);
            po.setBatchCode(null);
        }
        po.setNow(UtilDate.getNow());
        result = bizReportMapper.getUnitizedTempStoreDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }

        dataFillService.fillAttr(result);
        PageObjectVO<TempStoreVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void getUnitizedTempStoreExcel(BizContext ctx) {
        TempStoreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("暂存物资库存统计"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        po.setPaging(Boolean.FALSE);
        this.getUnitizedTempStoreDetail(ctx);
        PageObjectVO<TempStoreVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        UtilExcel.writeExcel(TempStoreVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

}
