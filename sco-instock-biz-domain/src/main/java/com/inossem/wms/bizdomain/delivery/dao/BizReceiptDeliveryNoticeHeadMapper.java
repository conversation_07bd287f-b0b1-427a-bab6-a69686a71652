package com.inossem.wms.bizdomain.delivery.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.vo.BizReceiptDeliveryNoticeListVo;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 送货通知抬头表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
public interface BizReceiptDeliveryNoticeHeadMapper extends WmsBaseMapper<BizReceiptDeliveryNoticeHead> {


    /**
     * 送货通知单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 送货通知单
     */
    List<BizReceiptDeliveryNoticeListVo> selectDeliveryNoticePageVo(IPage<BizReceiptDeliveryNoticeListVo> pageData,
                                                                    @Param(Constants.WRAPPER) WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> pageWrapper);

    /**
     * 送货通知单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 送货通知单
     */
    List<BizReceiptDeliveryNoticeListVo> selectDeliveryNoticePageVoUnitized(IPage<BizReceiptDeliveryNoticeListVo> pageData,
                                                                    @Param(Constants.WRAPPER) WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> pageWrapper);



    List<BizReceiptDeliveryNoticeHead> selectDeliveryNotice(BizReceiptDeliveryNoticeSearchPO po);

}
