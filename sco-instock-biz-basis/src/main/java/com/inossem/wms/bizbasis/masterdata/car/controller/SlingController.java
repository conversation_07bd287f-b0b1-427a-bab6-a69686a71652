package com.inossem.wms.bizbasis.masterdata.car.controller;

import com.inossem.wms.bizbasis.masterdata.car.service.biz.SlingService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.car.dto.DicSlingDTO;
import com.inossem.wms.common.model.masterdata.car.po.DicSlingSavePO;
import com.inossem.wms.common.model.masterdata.car.po.DicSlingSearchPO;
import com.inossem.wms.common.model.masterdata.car.vo.DicSlingPageVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 吊带管理 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-23
 */
@RestController
public class SlingController {
    
    @Autowired
    protected SlingService slingService;

    /**
     * 吊带列表分页查询
     *
     * @param po 吊带管理分页列表查询入参
     *
     */
    @ApiOperation(value = "吊带列表分页查询", tags = {"主数据管理-吊带管理"})
    @PostMapping(path = "/masterdata/sling/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicSlingPageVO>> getPage(@RequestBody DicSlingSearchPO po) {
        return BaseResult.success(slingService.getPage(po));
    }

    /**
     * 查询吊带详情信息
     *
     * @param id 吊带ID
     *
     */
    @ApiOperation(value = "查询吊带详情信息", tags = {"主数据管理-吊带管理"})
    @GetMapping(path = "/masterdata/sling/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicSlingDTO>> getInfo(@PathVariable("id") Long id) {
        return BaseResult.success(slingService.getInfo(id));
    }

    /**
     * 保存吊带信息
     *
     * @param po 吊带管理保存/修改入参
     *
     */
    @ApiOperation(value = "保存吊带信息", tags = {"主数据管理-吊带管理"})
    @PostMapping(path = "/masterdata/sling", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody DicSlingSavePO po, BizContext ctx) {
        slingService.save(po, ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SLING_SAVE_SUCCESS, po.getSlingDTO().getSlingCode());
    }

    /**
     * 修改吊带信息
     *
     * @param po 吊带管理保存/修改入参
     *
     */
    @ApiOperation(value = "修改吊带信息", tags = {"主数据管理-吊带管理"})
    @PutMapping(path = "/masterdata/sling", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicSlingSavePO po, BizContext ctx) {
        slingService.update(po, ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SLING_SAVE_SUCCESS, po.getSlingDTO().getSlingCode());
    }

    /**
     * 删除吊带信息
     *
     * @param id 吊带ID
     *
     */
    @ApiOperation(value = "删除吊带信息", tags = {"主数据管理-吊带管理"})
    @DeleteMapping(path = "/masterdata/sling/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id) {
        String code = slingService.delete(id);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SLING_DELETE_SUCCESS, code);
    }
    
}

