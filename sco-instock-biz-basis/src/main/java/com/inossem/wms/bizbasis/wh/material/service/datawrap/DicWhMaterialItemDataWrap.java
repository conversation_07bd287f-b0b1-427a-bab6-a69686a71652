package com.inossem.wms.bizbasis.wh.material.service.datawrap;

import com.inossem.wms.bizbasis.wh.material.dao.DicWhMaterialItemMapper;
import com.inossem.wms.common.model.wh.material.DicWhMaterialItem;
import com.inossem.wms.common.model.wh.material.dto.DicWhMaterialDTO;
import com.inossem.wms.common.model.wh.material.vo.DicWhMaterialPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 * <AUTHOR>
 */
@Service
public class DicWhMaterialItemDataWrap extends BaseDataWrap<DicWhMaterialItemMapper, DicWhMaterialItem> {

    public List<DicWhMaterialDTO> getStorageTypes(Long key, List<Long> matIdList) {
        return this.baseMapper.getStorageTypes(key,matIdList);
    }
}
