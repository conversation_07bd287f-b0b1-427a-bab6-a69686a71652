package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicDeptOfficeMapper;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptOfficeDTO;
import com.inossem.wms.common.model.masterdata.base.entity.DicDeptOffice;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 科室表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-19
 */
@Service
public class DicDeptOfficeDataWrap extends BaseDataWrap<DicDeptOfficeMapper, DicDeptOffice> {

    @Autowired
    private DataFillService dataFillService;

    /**
     * 获取科室
     *
     * @param pageData     pageData
     * @param pageWrapper  pageWrapper
     * @param dataFillType dataFillType
     */
    public void getDeptOfficePageList(IPage<DicDeptOfficeDTO> pageData,
                                      WmsQueryWrapper<MetaDataDeptOfficePO> pageWrapper, EnumDataFillType dataFillType) {
        List<DicDeptOfficeDTO> inputList = this.baseMapper.getDicDeptOfficeList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        pageData.setRecords(inputList);

    }

}
