package com.inossem.wms.bizbasis.rfid.service.biz;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelExceptionLogDataWrap;
import com.inossem.wms.common.model.label.entity.BizLabelExceptionLog;

/**
 * 标签日志
 * 
 * <AUTHOR>
 */
@Service
public class LabelExceptionLogService {

    @Autowired
    protected BizLabelExceptionLogDataWrap bizLabelExceptionLogDataWrap;

    /**
     * 批量保存过门时异常日志
     */
    public void saveBatch(List<BizLabelExceptionLog> bizLabelExceptionLogList) {
        bizLabelExceptionLogDataWrap.saveBatch(bizLabelExceptionLogList);
    }
}
