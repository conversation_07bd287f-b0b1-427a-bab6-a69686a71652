package com.inossem.wms.bizbasis.rfid.service.datawrap;

import com.inossem.wms.bizbasis.rfid.dao.BizStockdocLabelDataMapper;
import com.inossem.wms.common.model.label.entity.BizStockdocLabelData;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 盘点凭证标签表 服务实现类
 * </p>
 */
@Service
public class BizStockdocLabelDataDataWrap extends BaseDataWrap<BizStockdocLabelDataMapper, BizStockdocLabelData> {

    /**
     * 根据仓位库存查询 所对应的标签
     * @param stockBin
     * @return
     */
    public List<BizStockdocLabelData> selectByStockBin(StockBinDTO stockBin) {
        List<BizStockdocLabelData> labelDataList = this.baseMapper.selectByStockBin(stockBin);
        return labelDataList;
    }

    public List<BizStockdocLabelData> selectByStockBinList(List<StockBinDTO> stockBinList) {
        List<BizStockdocLabelData> labelDataList = this.baseMapper.selectByStockBinList(stockBinList);
        return labelDataList;
    }


    public List<BizStockdocLabelData> getListByTaskReqItemId(Long taskReqItemId) {
        return this.baseMapper.getListByTaskReqItemId(taskReqItemId);
    }

    public List<BizStockdocLabelData> selectByStockBinNonCellList(List<StockBinDTO> stockBinList) {
        List<BizStockdocLabelData> labelDataList = this.baseMapper.selectByStockBinNonCellList(stockBinList);
        return labelDataList;
    }

    public void deleteByIds(List<Long> idList, int deleteFlag) {
        this.baseMapper.deleteByIds(idList, deleteFlag);
    }
}
