package com.inossem.wms.bizbasis.spec.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.spec.dao.BizSpecClassifyMapper;
import com.inossem.wms.common.model.masterdata.spec.entity.BizSpecClassify;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecClassifySearchPO;
import com.inossem.wms.common.model.masterdata.spec.vo.BizSpecClassifyPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 分类表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-03
 */
@Service
public class BizSpecClassifyDataWrap extends BaseDataWrap<BizSpecClassifyMapper, BizSpecClassify> {

    /**
     * 分类列表分页
     *
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<BizSpecClassifyPageVO> getBizSpecClassifyPageVOList(IPage<BizSpecClassifyPageVO> page, QueryWrapper<BizSpecClassifySearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectBizSpecClassifyPageVOList(page, wrapper));
    }
}
