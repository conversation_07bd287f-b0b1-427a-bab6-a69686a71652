package com.inossem.wms.bizbasis.masterdata.wcs.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.org.section.entity.DicWhStorageSection;
import com.inossem.wms.common.model.wcs.dto.DicWcsTunnelDTO;
import com.inossem.wms.common.model.wcs.po.DicWcsTunnelSavePO;
import com.inossem.wms.common.model.wcs.po.DicWcsTunnelSearchPO;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageSectionService;
import com.inossem.wms.bizbasis.masterdata.wcs.service.biz.WcsTunnelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * WCS巷道主数据 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-31
 */
@RestController
@Slf4j
@Api(tags = "WCS巷道管理")
public class WcsTunnelController {

    @Autowired
    protected WcsTunnelService wcsTunnelService;

    @Autowired
    protected WhStorageSectionService whStorageSectionService;

    /**
     * 查询巷道列表
     *
     * @param po 查询条件
     * @return 巷道列表
     *
     */
    @ApiOperation(value = "查询巷道列表", tags = {"WCS巷道管理"})
    @PostMapping(path = "/master-data/wcs-tunnel/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicWcsTunnelDTO>> getList(@RequestBody DicWcsTunnelSearchPO po, BizContext ctx) {
        return BaseResult.success(wcsTunnelService.getList(ctx));
    }

    /**
     * 查看巷道详情
     *
     * @param id 主键id
     * @return 巷道详情
     *
     */
    @ApiOperation(value = "查看巷道详情", tags = {"WCS巷道管理"})
    @GetMapping(path = "/master-data/wcs-tunnel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicWcsTunnelDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(wcsTunnelService.getInfo(ctx));
    }

    /**
     * 查询存储区列表---配置巷道存储区关系时使用
     *
     * @return 存储区列表
     *
     */
    @ApiOperation(value = "存储区列表", tags = {"WCS巷道管理"})
    @PostMapping(path = "/master-data/wcs-tunnel/section/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicWhStorageSection>> getSectionList(BizContext ctx) {
        MultiResultVO<DicWhStorageSection> multiResultVO=whStorageSectionService.getList(ctx);
        return BaseResult.success(multiResultVO);
    }

    /**
     * 新增巷道信息
     *
     * @param po 巷道信息
     *
     */
    @ApiOperation(value = "新增巷道信息", tags = {"WCS巷道管理"})
    @PostMapping(path = "/master-data/wcs-tunnel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicWcsTunnelSavePO po, BizContext ctx) {
        wcsTunnelService.add(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WCS_TUNNEL_SAVE_SUCCESS, po.getCusDicTunnelInfo().getTunnelCode());
    }

    /**
     * 修改巷道信息
     *
     * @param po 巷道信息
     *
     */
    @ApiOperation(value = "修改巷道信息", tags = {"WCS巷道管理"})
    @PutMapping(path = "/master-data/wcs-tunnel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicWcsTunnelSavePO po, BizContext ctx) {
        wcsTunnelService.update(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WCS_TUNNEL_SAVE_SUCCESS, po.getCusDicTunnelInfo().getTunnelCode());
    }

    /**
     * 删除巷道信息
     *
     * @param id 主键id
     *
     */
    @ApiOperation(value = "删除巷道信息", tags = {"WCS巷道管理"})
    @DeleteMapping(path = "/master-data/wcs-tunnel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        String tunnelCode = wcsTunnelService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WCS_TUNNEL_DELETE_SUCCESS, tunnelCode);
    }
}
