<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.spec.dao.BizSpecFeatureValueMapper">

    <select id="getSpecValueList" resultType="com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureValueDTO">
        SELECT
        sfv.id,
        sfv.mat_id,
        sfv.fty_id,
        sfv.batch_id,
        sfv.classify_id,
        sfv.feature_id,
        sfv.feature_value,
        sfv.is_delete,
        sfv.create_time,
        sfv.modify_time,
        sfv.create_user_id,
        sfv.modify_user_id,
        sc.spec_classify_code,
        sc.spec_classify_name,
        sc.spec_classify_type,
        sf.spec_feature_name,
        sf.spec_feature_type,
        sf.info
        FROM biz_spec_feature_value sfv
        INNER JOIN (
        <foreach collection="newSearchSpecList" item="item" index="index" separator="UNION ALL">
            select #{item.matId} mat_id,
            #{item.ftyId} fty_id,
            #{item.batchId} batch_id,
            #{item.specClassifyType} spec_classify_type
        </foreach>
        ) t ON sfv.mat_id = t.mat_id AND sfv.fty_Id = t.fty_Id AND sfv.batch_id = t.batch_id
        INNER JOIN biz_spec_classify sc ON sfv.classify_id = sc.id AND sc.spec_classify_type = t.spec_classify_type
        INNER JOIN biz_spec_feature sf ON sfv.feature_id = sf.id
    </select>

</mapper>
