package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.org.wh.po.DicWhSearchPO;
import com.inossem.wms.common.model.org.wh.vo.DicWhPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicWhMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仓库主数据表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Service
public class DicWhDataWrap extends BaseDataWrap<DicWhMapper, DicWh> {

    /**
     * 仓库列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicWhPageVO> getDicWhPageVOList(IPage<DicWhPageVO> page, QueryWrapper<DicWhSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicWhPageVOList(page, wrapper));
    }
}
