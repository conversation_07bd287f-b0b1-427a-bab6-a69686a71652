<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.org.dao.DicDeptMapper">

    <resultMap id="deptOfficeTree" type="com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO">
        <id column="id" property="id"/>
        <result column="dept_code" property="deptCode"/>
        <result column="dept_name" property="deptName"/>
        <result column="dept_is_checked" property="deptIsChecked"/>
        <result column="job_level" property="jobLevel"/>

        <collection property="officeDTOList" ofType="com.inossem.wms.common.model.masterdata.base.dto.DicDeptOfficeDTO">
            <id column="office_id" property="id"/>
            <result column="dept_office_code" property="deptOfficeCode"/>
            <result column="dept_office_name" property="deptOfficeName"/>
            <result column="office_is_checked" property="officeIsChecked"/>
        </collection>
    </resultMap>


    <!--部门查询-->
    <select id="getDicDeptList" resultType="com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO">
        SELECT `id`,
               `dept_code`,
               `corp_id`,
               `dept_name`,
               `dept_type`,
               `is_delete`,
               `create_user_id`,
               `modify_user_id`,
               `create_time`,
               `modify_time`
        FROM `dic_dept` ${ew.customSqlSegment}
        ORDER BY `create_time` DESC
    </select>

    <!--部门查询-->
    <select id="getDeptOfficeTree" resultMap="deptOfficeTree">
        SELECT
        DD.id,
        DD.dept_code,
        DD.dept_name,
        DDO.id AS office_id,
        DDO.dept_office_code,
        DDO.dept_office_name,
        SUDOR1.dept_id,
        SUDOR2.office_id,
        SUDOR1.job_level,
        CASE WHEN SUDOR1.dept_id IS NOT NULL THEN '1' ELSE '0' END AS dept_is_checked,
        CASE WHEN SUDOR2.office_id IS NOT NULL THEN '1' ELSE '0' END AS office_is_checked
        FROM
        dic_dept DD
        LEFT JOIN dic_dept_office DDO ON DD.id = DDO.dept_id
        LEFT JOIN sys_user_dept_office_rel SUDOR1 ON DD.id = SUDOR1.dept_id AND SUDOR1.user_id = #{userId}
        LEFT JOIN sys_user_dept_office_rel SUDOR2 ON DDO.id = SUDOR2.office_id AND SUDOR2.user_id = #{userId}
        <where>
            <if test="corpId != null and corpId != '' ">
                AND DD.corp_id = #{corpId}
            </if>
            <if test="alreadySetting!=null and alreadySetting!=''">
                AND SUDOR1.user_id = #{userId}
            </if>
        </where>
    </select>

    <!--用户所属部门查询-->
    <select id="getUserDeptOfficeTree" resultMap="deptOfficeTree">
        SELECT
        DD.id,
        DD.dept_code,
        DD.dept_name,
        DDO.id AS office_id,
        DDO.dept_office_code,
        DDO.dept_office_name,
        SUDOR.job_level
        FROM
        sys_user_dept_office_rel SUDOR
        LEFT JOIN dic_dept DD ON SUDOR.dept_id = DD.id
        LEFT JOIN dic_dept_office DDO ON SUDOR.office_id = DDO.id
        <where>
            <if test="userId != null and userId != '' ">
                AND SUDOR.user_id = #{userId}
            </if>
        </where>
    </select>



</mapper>
