package com.inossem.wms.bizbasis.masterdata.material.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.component.MaterialReturnComponent;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizMaterialReturnHeadDataWrap;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumIsYesOrNo;
import com.inossem.wms.common.enums.EnumUserLabel;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnHeadDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnItemDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnUserDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnHead;
import com.inossem.wms.common.util.UtilBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/25 19:15
 */
@Service
@Slf4j
public class MaterialReturnService {

    @Autowired
    private MaterialReturnComponent materialReturnComponent;

    @Autowired
    private BizMaterialReturnHeadDataWrap bizMaterialReturnHeadDataWrap;


    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    /**
     * 初始化
     * @param ctx
     */
    public void init(BizContext ctx) {
        // 初始化设置
        materialReturnComponent.setInit(ctx);
    }

    /**
     * 获取物资返运列表
     * @param ctx
     * @return
     */
    public void getPage(BizContext ctx) {
        materialReturnComponent.getPage(ctx);
    }


    /**
     * 物资返运详情
     * @param ctx
     */
    public void get(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        BizMaterialReturnHead bizMaterialReturnHead = bizMaterialReturnHeadDataWrap.getById(headId);
        BizMaterialReturnHeadDTO dto = UtilBean.newInstance(bizMaterialReturnHead, BizMaterialReturnHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(dto);
//        dataFillService.fillRlatAttrForDataObj(dto);
        // 分物资返运填充固定资产物料描述属性
        List<Long> referReceiptItemIdList = dto.getItemDTOList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).map(BizMaterialReturnItemDTO::getReferReceiptItemId).collect(Collectors.toList());

        if (referReceiptItemIdList.size() > 0){
            QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, referReceiptItemIdList);
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);

            Map<Long, ErpPurchaseReceiptItem> erpPurchaseMap = erpPurchaseReceiptItemList.stream()
                    .collect(Collectors.toMap(ErpPurchaseReceiptItem::getId, obj -> obj));

            dto.getItemDTOList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(itemDTO -> {
                ErpPurchaseReceiptItem erpPurchaseList = erpPurchaseMap.get(itemDTO.getReferReceiptItemId());
                if (null != erpPurchaseList && "1".equals(erpPurchaseList.getSubjectType())){
                    itemDTO.setMatName(erpPurchaseList.getMatNameBack());
                }
            });

        }
        
        

        // 设置按钮组
        ButtonVO buttonVO = materialReturnComponent.setButton(dto);

        // 设置不符合项处置单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(dto, new ExtendVO().setAttachmentRequired(true), buttonVO));
        // 开启附件
        materialReturnComponent.setExtendAttachment(ctx);
//        return new SingleResultVO<>(dto);
    }

    /**
     * 删除操作
     * @param ctx
     */
    public void delete(BizContext ctx) {

        // 删除其他入库单
        materialReturnComponent.deleteReturnInput(ctx);
    }


    /**
     * 新增物料返运
     * @param ctx
     */
    @WmsMQListener(tags = TagConst.GEN_MAT_RETURN)
    public void save(BizContext ctx) {
        // 数据校验
        materialReturnComponent.check(ctx);
        // 保存入库单
        materialReturnComponent.saveInput(ctx);

        // 保存批次图片
        materialReturnComponent.saveBizBatchImg(ctx);
    }

    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 数据校验
        materialReturnComponent.check(ctx);
        // 保存入库单
        materialReturnComponent.saveInput(ctx);
        // 保存批次图片
        materialReturnComponent.saveBizBatchImg(ctx);
        // 更新状态 - 已完成
        materialReturnComponent.updateStatusCompleted(ctx);
        // 库存物项返运 冲销
        materialReturnComponent.writeOff(ctx);
    }


    /**
     * 得到信息打印
     *
     * @param ctx ctx
     */
    public void getInfoPrint(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        BizMaterialReturnHead bizMaterialReturnHead = bizMaterialReturnHeadDataWrap.getById(headId);
        BizMaterialReturnHeadDTO dto = UtilBean.newInstance(bizMaterialReturnHead, BizMaterialReturnHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(dto);
//        dataFillService.fillRlatAttrForDataObj(dto);
        // 分物资返运填充固定资产物料描述属性
        List<Long> referReceiptItemIdList = dto.getItemDTOList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).map(BizMaterialReturnItemDTO::getReferReceiptItemId).collect(Collectors.toList());

        if (referReceiptItemIdList.size() > 0){
            QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, referReceiptItemIdList);
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);

            Map<Long, ErpPurchaseReceiptItem> erpPurchaseMap = erpPurchaseReceiptItemList.stream()
                    .collect(Collectors.toMap(ErpPurchaseReceiptItem::getId, obj -> obj));

            dto.getItemDTOList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(itemDTO -> {
                ErpPurchaseReceiptItem erpPurchaseList = erpPurchaseMap.get(itemDTO.getReferReceiptItemId());
                if (null != erpPurchaseList && "1".equals(erpPurchaseList.getSubjectType())){
                    itemDTO.setMatName(erpPurchaseList.getMatNameBack());
                }
            });

        }

        if (dto.getItemDTOList().size() > 0){
            dto.setReferReceiptCode(dto.getItemDTOList().get(0).getReferReceiptCode());
        }

        List<BizMaterialReturnUserDTO> userDTOList = dto.getInspectUserList();
        if (!CollectionUtils.isEmpty(userDTOList)){
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            userDTOList.forEach(userDto -> {

                if (EnumUserLabel.HANDED_OVER_TO_PEOPLE.getValue().equals(userDto.getLabel())){
                    dto.setCompanyHandOverUser(userDto.getInspectCompany());
                    dto.setUserNameHandOverUser(userDto.getInspectUserName());
                    dto.setCreateTimeStrHandOverUser(format.format(userDto.getCreateTime()));
                }

                if (EnumUserLabel.RECIPIENT.getValue().equals(userDto.getLabel())){
                    dto.setCompanyRecipient(userDto.getInspectCompany());
                    dto.setUserNameRecipient(userDto.getInspectUserName());
                    dto.setCreateTimeStrRecipient(format.format(userDto.getCreateTime()));
                }

                if (EnumUserLabel.PARTICIPANTSIN.getValue().equals(userDto.getLabel())){
                    dto.setCompanyParticipantsIn(userDto.getInspectCompany());
                    dto.setUserNameParticipantsIn(userDto.getInspectUserName());
                    dto.setCreateTimeStrParticipantsIn(format.format(userDto.getCreateTime()));
                }
            });
        }
        
        if (dto.getReturnReason() == 0){
            dto.setIsReturn(EnumIsYesOrNo.YES.getValue());
            dto.setIsRepair(EnumIsYesOrNo.NO.getValue());
            dto.setIsExcessSupply(EnumIsYesOrNo.NO.getValue());
        } else if (dto.getReturnReason() == 1){
            dto.setIsReturn(EnumIsYesOrNo.NO.getValue());
            dto.setIsRepair(EnumIsYesOrNo.YES.getValue());
            dto.setIsExcessSupply(EnumIsYesOrNo.NO.getValue());
        } else if (dto.getReturnReason() == 2){
            dto.setIsReturn(EnumIsYesOrNo.NO.getValue());
            dto.setIsRepair(EnumIsYesOrNo.NO.getValue());
            dto.setIsExcessSupply(EnumIsYesOrNo.YES.getValue());
        } else {
            dto.setIsReturn(EnumIsYesOrNo.NO.getValue());
            dto.setIsRepair(EnumIsYesOrNo.NO.getValue());
            dto.setIsExcessSupply(EnumIsYesOrNo.NO.getValue());
        }
        
        // 设置不符合项处置单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(dto, new ExtendVO().setAttachmentRequired(true), new ButtonVO()));
    }
}
