package com.inossem.wms.bizbasis.wh.material.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.wh.material.dao.DicWhMaterialMapper;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.wh.material.DicWhMaterial;
import com.inossem.wms.common.model.wh.material.vo.DicWhMaterialPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仓库物料主数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class DicWhMaterialDataWrap extends BaseDataWrap<DicWhMaterialMapper, DicWhMaterial> {

    public IPage<DicWhMaterialPageVO> getPage(IPage<DicWhMaterialPageVO> page, DicMaterialSearchPO po) {
        return page.setRecords(this.baseMapper.getPage(page,po));
    }
}
