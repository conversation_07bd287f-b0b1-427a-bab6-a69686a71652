package com.inossem.wms.bizbasis.masterdata.material.controller;


import com.inossem.wms.bizbasis.masterdata.material.service.biz.LogMaterialNetWeightRecordService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.po.LogMaterialNetWeightRecordSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.LogMaterialNetWeightRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  物料净重变更记录 controller
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-17
 */
@RestController
@Api(tags = "物料净重变更记录")
public class LogMaterialNetWeightRecordController {
    @Autowired
    private LogMaterialNetWeightRecordService logMaterialNetWeightRecordService;

    @ApiOperation(value = "物料净重变更记录-分页", tags = {"物料净重变更记录"})
    @PostMapping(path = "/log/material-net-weight-record/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<LogMaterialNetWeightRecordVO>> getPage(@RequestBody LogMaterialNetWeightRecordSearchPO po, BizContext ctx) {
        logMaterialNetWeightRecordService.getPage(ctx);
        PageObjectVO<LogMaterialNetWeightRecordVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "验证物料主数据重量容差", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/check-weight-tolerance", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<DicMaterialDTO> checkWeightTolerance(@RequestBody DicMaterialSearchPO po, BizContext ctx) {
        logMaterialNetWeightRecordService.checkWeightTolerance(ctx);
        DicMaterialDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "更新物料主数据净重(标准重量)", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/update-net-weight", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> updateNetWeight(@RequestBody DicMaterialDTO po, BizContext ctx) {
        logMaterialNetWeightRecordService.updateNetWeight(ctx);
        return BaseResult.success();
    }
}

