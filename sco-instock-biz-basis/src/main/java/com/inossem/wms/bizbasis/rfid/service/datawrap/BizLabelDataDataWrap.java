package com.inossem.wms.bizbasis.rfid.service.datawrap;

import com.inossem.wms.bizbasis.rfid.dao.BizLabelDataMapper;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.po.LabelStockBinQueryPO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 标签表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Service
public class BizLabelDataDataWrap extends BaseDataWrap<BizLabelDataMapper, BizLabelData> {

    /**
     * 根据仓位库存查询 所对应的标签
     * @param stockBin
     * @return
     */
    public List<BizLabelData> selectByStockBin(StockBinDTO stockBin) {
        List<BizLabelData> labelDataList = this.baseMapper.selectByStockBin(stockBin);
        return labelDataList;
    }

    public List<BizLabelData> selectByStockBinList(List<StockBinDTO> stockBinList) {
        List<BizLabelData> labelDataList = this.baseMapper.selectByStockBinList(stockBinList);
        return labelDataList;
    }


    public List<BizLabelData> getListByTaskReqItemId(Long taskReqItemId) {
        return this.baseMapper.getListByTaskReqItemId(taskReqItemId);
    }

    public List<BizLabelData> selectByStockBinNonCellList(List<StockBinDTO> stockBinList) {
        List<BizLabelData> labelDataList = this.baseMapper.selectByStockBinNonCellList(stockBinList);
        return labelDataList;
    }

    public void deleteByIds(List<Long> idList, int deleteFlag) {
        this.baseMapper.deleteByIds(idList, deleteFlag);
    }

    public LabelStockBinQueryPO searchMatOrBin(String text) {
        LabelStockBinQueryPO labelStockBinQueryPO = this.baseMapper.searchMatOrBin(text);
        return labelStockBinQueryPO;
    }

    /**
     * 标签数据恢复补丁
     * @param receiptCode 盘点单号
     */
    public void insertTempLabelPatchForStocktaking(String receiptCode) {
        this.baseMapper.insertTempLabelPatchForStocktaking(receiptCode);
    }
    public void updateTempLabelPatchForStocktaking(String receiptCode) {
        this.baseMapper.updateTempLabelPatchForStocktaking(receiptCode);
    }
}
