<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.material.dao.DicMaterialFacotryWbsMapper">

    <select id="getInputMatCodeSetFromInput" parameterType="java.util.Date" resultType="java.lang.String">
        SELECT
            m.mat_code
        FROM
            biz_receipt_input_item i
        INNER JOIN dic_material m ON i.mat_id = m.id
        WHERE
        i.posting_date <![CDATA[ <= ]]> #{date}
        AND i.posting_date > DATE_ADD(#{date}, INTERVAL - 1 DAY)
    </select>

</mapper>
