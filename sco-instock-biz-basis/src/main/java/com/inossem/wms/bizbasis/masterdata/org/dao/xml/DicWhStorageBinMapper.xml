<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.org.dao.DicWhStorageBinMapper">

    <select id="selectDicWhStorageBinPageVOList"
            resultType="com.inossem.wms.common.model.masterdata.storagebin.vo.DicWhStorageBinPageVO">
        select
            dic_wh_storage_bin.id, dic_wh_storage_bin.wh_id, dic_wh_storage_bin.type_id, dic_wh_storage_bin.bin_code, dic_wh_storage_bin.section_id,
            dic_wh_storage_bin.freeze_input, dic_wh_storage_bin.freeze_output, dic_wh_storage_bin.freeze_reason, dic_wh_storage_bin.is_empty, dic_wh_storage_bin.is_delete,
            dic_wh_storage_bin.is_default, dic_wh_storage_bin.create_time, dic_wh_storage_bin.modify_time, dic_wh_storage_bin.create_user_id, dic_wh_storage_bin.modify_user_id,
            dic_wh_storage_bin.is_nuclear_island_tool_room,
            dic_wh.wh_code, dic_wh.wh_name,
            dic_wh_storage_type.type_code, dic_wh_storage_type.type_name,
            dic_wh_storage_section.section_code, dic_wh_storage_section.section_name,
            dic_wh_storage_bin.over_weight
        from dic_wh_storage_bin
        inner join dic_wh on dic_wh_storage_bin.wh_id = dic_wh.id and dic_wh_storage_bin.is_delete = 0 and dic_wh_storage_bin.is_default = 0
        inner join dic_wh_storage_type on dic_wh_storage_bin.type_id = dic_wh_storage_type.id
        left join dic_wh_storage_section on dic_wh_storage_bin.section_id = dic_wh_storage_section.id ${ew.customSqlSegment}
    </select>

    <!--获取全部仓位-->
    <select id="getAll" resultType="com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO">
        SELECT
            b.id,
            b.wh_id,
            b.type_id,
            b.bin_code,
            b.section_id,
            b.freeze_input,
            b.freeze_output,
            b.freeze_reason,
            b.is_empty,
            b.is_default,
            b.is_delete,
            b.create_time,
            b.modify_time,
            b.create_user_id,
            b.modify_user_id,
            w.wh_code,
            w.wh_name,
            t.type_code,
            t.type_name
        FROM
            dic_wh_storage_bin b
                INNER JOIN dic_wh w ON b.wh_id = w.id
                INNER JOIN dic_wh_storage_type t ON b.type_id = t.id
    </select>
</mapper>
