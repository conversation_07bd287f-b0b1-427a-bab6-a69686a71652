package com.inossem.wms.bizbasis.wh.material.service.datawrap.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.dictionary.dao.DicDictionaryMapper;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageSectionDataWrap;
import com.inossem.wms.bizbasis.wh.material.service.datawrap.DicWhMaterialDataWrap;
import com.inossem.wms.bizbasis.wh.material.service.datawrap.DicWhMaterialItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.wh.material.EnumDelistingPolicy;
import com.inossem.wms.common.enums.wh.material.EnumListingStrategy;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.dictionary.entity.DicDictionary;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.org.section.dto.DicWhStorageSectionDTO;
import com.inossem.wms.common.model.org.section.entity.DicWhStorageSection;
import com.inossem.wms.common.model.wh.material.DicWhMaterial;
import com.inossem.wms.common.model.wh.material.DicWhMaterialItem;
import com.inossem.wms.common.model.wh.material.dto.DicWhMaterialDTO;
import com.inossem.wms.common.model.wh.material.po.DicWhMaterialSavePO;
import com.inossem.wms.common.model.wh.material.vo.DicWhMaterialPageVO;
import com.inossem.wms.common.model.wh.material.vo.WhMaterialUploadLoadInitVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DicWhMaterialService {

    @Autowired
    private DicWhMaterialDataWrap dicWhMaterialDataWrap;
    @Autowired
    private DicWhMaterialItemDataWrap dicWhMaterialItemDataWrap;
    @Autowired
    private DicWhStorageSectionDataWrap dicWhStorageSectionDataWrap;

    @Autowired
    private DicDictionaryMapper dicDictionaryMapper;


    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 新增
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(BizContext ctx) {
        DicWhMaterialSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        saveBill(po,ctx);

    }

    /**
     * 保存单据
     *
     * @param po
     * @param ctx
     */
    private void saveBill(DicWhMaterialSavePO po, BizContext ctx) {
        try{
            DicWhMaterial dicWhMaterial = UtilBean.newInstance(po, DicWhMaterial.class);
            Long userId = ctx.getCurrentUser().getId();
            Long matId = po.getMatId();
            if(UtilNumber.isEmpty(matId)){
                // 物料不存在
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }
            dicWhMaterial.setCreateUserId(userId);
            try {
                dicWhMaterialDataWrap.saveOrUpdate(dicWhMaterial);
            }catch (DuplicateKeyException e){
                // 已存在此仓库和物料的仓库物料信息
                throw new WmsException(EnumReturnMsg.RETURN_CODE_WH_MAT_EXISTED);
            }
            List<DicWhMaterialItem> dicWhMaterialItemList = po.getDicWhMaterialItemList();
            if (UtilCollection.isNotEmpty(dicWhMaterialItemList)) {
                dicWhMaterialItemList.forEach(u -> u.setHeadId(dicWhMaterial.getId()));
                for(int i = 0; i<dicWhMaterialItemList.size(); i++){
                    dicWhMaterialItemList.get(i).setSort(i+1);
                }
                try {
                    dicWhMaterialItemDataWrap.saveOrUpdateBatch(dicWhMaterialItemList);
                }catch (DuplicateKeyException e){
                    // 同仓库物料下，不能增加相同的存储类型
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE);
                }
            }
        }catch (Exception e){
            throw e;
        }
    }


    /**
     * 修改
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(BizContext ctx) {
        DicWhMaterialSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(UtilNumber.isEmpty(po.getId())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        UpdateWrapper<DicWhMaterialItem> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(DicWhMaterialItem::getHeadId, po.getId());
        dicWhMaterialItemDataWrap.remove(updateWrapper);
        saveBill(po,ctx);
    }

    /**
     * 查看
     * @param ctx
     */
    public void info(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        DicWhMaterial byId = dicWhMaterialDataWrap.getById(id);
        DicWhMaterialDTO dicWhMaterialDTO = UtilBean.newInstance(byId, DicWhMaterialDTO.class);
        dataFillService.fillAttr(dicWhMaterialDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_BIN_VO,dicWhMaterialDTO);

    }




    /**
     * 删除
     * @param ctx
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        UpdateWrapper<DicWhMaterialItem> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(DicWhMaterialItem::getHeadId, id);
//        dicWhMaterialItemDataWrap.physicalDelete(updateWrapper); 物理删除
        dicWhMaterialItemDataWrap.removeById(id);
        dicWhMaterialDataWrap.removeById(id);
    }



    /**
     * 上下架策略查询
     * @return
     */
    public WhMaterialUploadLoadInitVO init() {
        WhMaterialUploadLoadInitVO vo = new WhMaterialUploadLoadInitVO();

        QueryWrapper<DicDictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicDictionary::getType, 3);
        dicDictionaryMapper.selectList(queryWrapper);
        vo.setLoadList(dicDictionaryMapper.selectList(queryWrapper));

        // 上架策略 枚举的
        List<DicDictionary> loadList = EnumListingStrategy.toList();
        // 下架策略 枚举的
        List<DicDictionary> unLoadList = EnumDelistingPolicy.toList();


        QueryWrapper<DicDictionary> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(DicDictionary::getType, 4);
        dicDictionaryMapper.selectList(queryWrapper1);
        vo.setUnloadList(dicDictionaryMapper.selectList(queryWrapper1));
        return vo;

    }


    /**
     * 分页查询物料仓库
     * @param ctx
     * @return
     */
    public PageObjectVO<DicWhMaterialPageVO> getPage(BizContext ctx) {
        DicMaterialSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        IPage<DicWhMaterialPageVO> page = po.getPageObj(DicWhMaterialPageVO.class);
        dicWhMaterialDataWrap.getPage(page,po);
        long totalCount = page.getTotal();
        return new PageObjectVO(page.getRecords(), totalCount);
    }

    /**
     * 字典字段下拉列表查询
     * @return
     * @param key
     */
    public List<DicDictionary> dictionaryList(long key) {
        QueryWrapper<DicDictionary> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(DicDictionary::getType, key);
        return dicDictionaryMapper.selectList(queryWrapper1);

    }

    /**
     * 获取物料信息
     * @param ctx
     * @return
     */
    public DicMaterialDTO getMatInfo(BizContext ctx) {
        DicMaterialSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long matIdByMatCode = dictionaryService.getMatIdByMatCode(po.getMatCode());
        DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(matIdByMatCode);
        return dicMaterialDTO;
    }

    /**
     * 根据存储类型id获取存储区
     * @param typeId 存储类型id
     * @return
     */
    public MultiResultVO<DicWhStorageSectionDTO> getSectionByTypeId(long typeId) {
        // 该方法暂时不可用有问题  先查库
        Collection<DicWhStorageSectionDTO> allStorageSectionCache = dictionaryService.getAllStorageSectionCache();
        List<DicWhStorageSectionDTO> dtoList = allStorageSectionCache.stream().filter(dicWhStorageSectionDTO -> dicWhStorageSectionDTO.getTypeId().equals(typeId))
                .collect(Collectors.toList());
//        List<DicWhStorageSection>  dicWhStorageSectionList = dicWhStorageSectionDataWrap
//                .selectSectionByTypeId(typeId);
        return new MultiResultVO<>(dtoList);
    }
}
