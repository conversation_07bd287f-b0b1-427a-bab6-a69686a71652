package com.inossem.wms.bizbasis.batch.dao;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchImg;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;

/**
 * <p>
 * 批次图片表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Mapper
public interface BizBatchImgMapper extends WmsBaseMapper<BizBatchImg> {

    /**
     * 按照物料列表查询图片 并显示每条图片的数量
     *
     * @param matIdList 物料id集合
     * @param limit     显示个数
     * @return BizBatchImgDTO
     * @date 2021/3/24 14:53
     * <AUTHOR>
     */
    List<BizBatchImgDTO> selectBatchImgListByMatIdList(@Param("matIdList") Set<Long> matIdList, @Param("limit") int limit);

    /**
     * 按照批次列表查询图片 并显示每条图片的数量
     *
     * @param batchIdList 批次id集合
     * @param limit       显示个数
     * @return BizBatchImgDTO
     * @date 2021/3/24 14:53
     * <AUTHOR>
     */
    List<BizBatchImgDTO> selectBatchImgListByBatchIdList(@Param("batchIdList") Set<Long> batchIdList, @Param("limit") int limit);

}
