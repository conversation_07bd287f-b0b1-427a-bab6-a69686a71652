package com.inossem.wms.bizbasis.stock.service.datawrap;

import java.util.List;

import org.springframework.stereotype.Service;

import com.inossem.wms.bizbasis.stock.dao.StockHistoryBinMapper;
import com.inossem.wms.common.model.stock.entity.StockHistoryBin;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;

/**
 * 仓位库存历史表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class StockHistoryBinDataWrap extends BaseDataWrap<StockHistoryBinMapper, StockHistoryBin> {

    /**
     * 根据物料凭证code查询批次库存 2020.11.9 libin 由于添加库存修改历史记录是异步操作，需要事先查询出修改完的库存数量，新增此查询方法
     * 
     * @param insDocCode 凭证code
     * @return 历史批次库存对象
     */
    public List<StockHistoryBin> selectStockBinByInsDocCode(String insDocCode) {
        return this.getBaseMapper().selectStockBinByInsDocCode(insDocCode);
    }
}
