<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.account.dao.DicAccountPeriodMapper">

    <select id="selectDicAccountPeriodPageVOList"
            resultType="com.inossem.wms.common.model.account.vo.DicAccountPeriodPageVO">
        select
            dic_account_period.id, dic_account_period.corp_id, dic_account_period.account_year, dic_account_period.account_month, dic_account_period.account_begin_date, dic_account_period.account_end_date, dic_account_period.account_fact_date,
            dic_account_period.is_delete, dic_account_period.create_time, dic_account_period.modify_time, dic_account_period.create_user_id, dic_account_period.modify_user_id,
            dic_corp.corp_code, dic_corp.corp_name,
            sys_user.user_code create_user_code,
            sys_user.user_name create_user_name
        from dic_account_period
        inner join dic_corp on dic_account_period.corp_id = dic_corp.id and dic_account_period.is_delete = 0
        inner join sys_user on dic_account_period.create_user_id = sys_user.id ${ew.customSqlSegment}
    </select>
</mapper>
