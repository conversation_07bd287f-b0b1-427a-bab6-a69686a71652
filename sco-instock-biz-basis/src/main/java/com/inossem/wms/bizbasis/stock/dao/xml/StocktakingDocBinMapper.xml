<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.stock.dao.StocktakingDocBinMapper">
    <!--根据batchId 修改 qty_freeze-->
    <update id="updateByBatchId">
        UPDATE
            `stock_bin`
        SET
            `stock_bin`.`qty_freeze` =
                (SELECT
                     t1.`qty_freeze` + #{freezeNum}
                 FROM
                     (SELECT
                          `qty_freeze`
                      FROM
                          `stock_bin`
                      WHERE `batch_id` = #{batchId}) AS t1),
            `stock_bin`.`qty` =
                (SELECT
                     t2.`qty` - #{freezeNum}
                 FROM
                     (SELECT
                          `qty`
                      FROM
                          `stock_bin`
                      WHERE `batch_id` = #{batchId}) AS t2)
        WHERE `batch_id` = #{batchId}
    </update>

    <!-- 根据ins凭证查询所有相关仓位库存 -->
    <select id="selectNegativeStockBatchAfterModifyStockBatch" resultType="com.inossem.wms.common.model.stock.dto.StockBinDTO">
        SELECT a.id,
               a.mat_id,
               a.batch_id,
               a.fty_id,
               a.location_id,
               a.wh_id,
               a.type_id,
               a.bin_id,
               a.cell_id,
               a.qty,
               a.qty_transfer,
               a.qty_inspection,
               a.qty_freeze,
               a.qty_haste,
               a.qty_temp,
               a.create_time,
               a.modify_time,
               a.create_user_id,
               a.modify_user_id
        FROM stock_bin a
                 INNER JOIN biz_batch_info bm ON a.batch_id = bm.id
                 INNER JOIN stock_ins_doc_bin b
                            ON b.ins_doc_code = #{insDocCode}
                                AND a.mat_id = b.mat_id
                                AND a.fty_id = b.fty_id
                                AND a.location_id = b.location_id
                                AND a.wh_id = b.wh_id
                                AND a.type_id = b.type_id
                                AND a.bin_id = b.bin_id
                                AND a.cell_id = b.cell_id
                                AND bm.id = b.batch_id
                                AND (a.qty <![CDATA[<]]> 0
                                    OR a.qty_transfer <![CDATA[<]]> 0
                                    OR a.qty_inspection <![CDATA[<]]> 0
                                    OR a.qty_freeze <![CDATA[<]]> 0
                                    OR a.qty_haste + a.qty <![CDATA[<]]> 0
                                    OR a.qty_temp + a.qty <![CDATA[<]]> 0
                                    OR a.qty + a.qty_haste + a.qty_temp <![CDATA[<]]> 0)
    </select>

    <!-- 根据唯一索引查询所有相关仓位库存 -->
    <select id="selectStockBinByStockBinKeyList" parameterType="com.inossem.wms.common.model.stock.key.StockBinKey" resultType="com.inossem.wms.common.model.stock.dto.StockBinDTO">
        SELECT
        s.mat_id, s.batch_id, s.fty_id, s.location_id,
        s.wh_id, s.type_id, s.bin_id, s.cell_id,
        bm.spec_stock, bm.spec_stock_code,
        s.qty, s.qty_transfer, s.qty_inspection,
        s.qty_freeze, s.qty_haste, s.qty_temp
        FROM stock_bin s
        INNER JOIN biz_batch_info bm ON s.batch_id = bm.id AND bm.is_delete = 0
        INNER JOIN
        <foreach collection="list" item="item" open="(" separator=" UNION ALL" close=")" index="index">
            SELECT #{item.ftyId} f,#{item.locationId} l,#{item.matId} m,#{item.batchId} b,
            #{item.whId} w,#{item.typeId} t,#{item.binId} i,#{item.cellId, jdbcType=VARCHAR} c
        </foreach>
        k
        ON s.mat_id = k.m AND s.batch_id = k.b AND s.fty_id = k.f AND s.location_id = k.l
        AND s.wh_id = k.w AND s.type_id = k.t AND s.bin_id = k.i AND s.cell_id = k.c
    </select>

    <!-- 查询库存通用方法 -->
    <select id="selectStockBinByStockBinPo" parameterType="com.inossem.wms.common.model.stock.po.StockBinPO" resultType="com.inossem.wms.common.model.stock.dto.StockBinDTO">
        SELECT
        <!-- 库存主键 -->
        stock.id,
        <if test="joinMode != null and joinMode == 1">
            sbatch.id stock_batch_id,
        </if>
        <!-- 库存八主键 -->
        stock.fty_id,stock.location_id,stock.batch_id,stock.mat_id,
        stock.wh_id,stock.type_id,stock.bin_id,stock.cell_id,
        <!-- 库存六数量 -->
        stock.qty,stock.qty_transfer,stock.qty_inspection,stock.qty_freeze,stock.qty_haste,stock.qty_temp,
        <!-- 库存信息 -->
        stock.create_time create_time,
        <!-- 工厂配置 -->
        fty.fty_name,fty.fty_code,
        <!-- 库存地点配置 -->
        location.location_name,location.location_code,
        <!-- 批次信息 -->
        bm.id,bm.spec_stock,bm.spec_stock_code,bm.spec_stock_name,
        bm.batch_erp,bm.input_date,
        bm.supplier_code,bm.supplier_name,
        <!-- 物料信息 -->
        m.mat_name,m.mat_type_id,m.mat_code,
        <!-- 单位信息 -->
        m.unit_id,u.unit_code,u.unit_name,u.decimal_place,
        dwst.type_name,dwst.type_code,
        <!-- 可用库存 -->
        stock.qty + stock.qty_temp + stock.qty_haste qty_usable
        FROM stock_bin stock
        INNER JOIN dic_material m ON m.id = stock.mat_id AND m.is_delete = 0
        <if test="stockBinConditionDTOList != null and stockBinConditionDTOList.size()>0">
            AND
            <foreach collection="stockBinConditionDTOList" open="(" separator="or" close=")" index="index" item="item">
                ( 1=1
                <if test="item.ftyId != null">
                    AND stock.fty_id = #{item.ftyId}
                </if>
                <if test="item.locationId != null">
                    AND stock.location_id = #{item.locationId}
                </if>
                <if test="item.matId != null">
                    AND stock.mat_id = #{item.matId}
                </if>
                )
            </foreach>
        </if>
        <if test="ftyId != null">
            AND stock.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock.mat_id = #{matId}
        </if>
        <if test="matIdList != null">
            AND (stock.mat_id IN
            <foreach collection="matIdList" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="whId != null">
            AND stock.wh_id = #{whId}
        </if>
        <if test="typeId != null">
            AND stock.type_id = #{typeId}
        </if>
        <if test="binId != null">
            AND stock.bin_id = #{binId}
        </if>
        <if test="binIdList != null">
            AND (stock.bin_id IN
            <foreach collection="binIdList" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="cellId != null and cellId !=''">
            AND binary stock.cell_id = #{cellId,jdbcType=VARCHAR}
        </if>
        <if test="cellSet != null">
            AND (stock.cell_id IN
            <foreach collection="cellSet" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="stockStatusSet != null">
            <foreach collection="stockStatusSet" open="" separator="" close="" index="index" item="item">
                <choose>
                    <when test="item == 10">
                        AND stock.qty > 0
                    </when>
                    <when test="item == 20">
                        AND stock.qty_transfer > 0
                    </when>
                    <when test="item == 30">
                        AND stock.qty_inspection > 0
                    </when>
                    <when test="item == 40">
                        AND stock.qty_freeze > 0
                    </when>
                    <when test="item == 50">
                        AND stock.qty_haste > 0
                    </when>
                    <when test="item == 60">
                        AND stock.qty_temp > 0
                    </when>
                    <when test="item == 110">
                        AND stock.qty + stock.qty_haste > 0
                    </when>
                    <when test="item == 120">
                        AND stock.qty + stock.qty_temp > 0
                    </when>
                    <when test="item == 127">
                        AND stock.qty + stock.qty_haste + stock.qty_temp > 0
                    </when>
                    <otherwise>

                    </otherwise>
                </choose>
            </foreach>
            <if test="joinMode != null and joinMode == 1">
                INNER JOIN stock_batch sbatch
                ON stock.mat_id = sbatch.mat_id
                AND stock.batch_id = sbatch.batch_id
                AND stock.fty_id = sbatch.fty_id
                AND stock.location_id = sbatch.location_id
                <foreach collection="stockStatusSet" open="" separator="" close="" index="index" item="item">
                    <choose>
                        <when test="item == 10">
                            AND sbatch.qty > 0
                        </when>
                        <when test="item == 20">
                            AND sbatch.qty_transfer > 0
                        </when>
                        <when test="item == 30">
                            AND sbatch.qty_inspection > 0
                        </when>
                        <when test="item == 40">
                            AND sbatch.qty_freeze > 0
                        </when>
                        <when test="item == 50">
                            AND sbatch.qty_haste > 0
                        </when>
                        <when test="item == 60">
                            AND sbatch.qty_temp > 0
                        </when>
                        <when test="item == 110">
                            AND sbatch.qty + stock.qty_haste > 0
                        </when>
                        <when test="item == 120">
                            AND sbatch.qty + stock.qty_temp > 0
                        </when>
                        <when test="item == 127">
                            AND sbatch.qty + stock.qty_haste + stock.qty_temp > 0
                        </when>
                        <otherwise>

                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </if>

        INNER JOIN biz_batch_info bm ON stock.batch_id = bm.id
        <if test="batchId != null and batchId !=''">
            AND bm.id = #{batchId}
        </if>
        <if test="specStock != null">
            AND bm.spec_stock = #{specStock,jdbcType=VARCHAR}
            <if test="specStockCode != null">
                AND bm.spec_stock_code = #{specStockCode,jdbcType=VARCHAR}
            </if>
        </if>
        <if test="batchErp != null">
            AND bm.batch_erp = #{batchErp}
        </if>
        INNER JOIN dic_unit u ON u.id = m.unit_id AND u.is_delete = 0
        INNER JOIN dic_factory fty ON fty.id = stock.fty_id AND fty.is_delete = 0
        INNER JOIN dic_stock_location location ON stock.location_id = location.id AND stock.fty_id = location.fty_id AND location.is_delete = 0
        INNER JOIN dic_wh_storage_type dwst ON dwst.wh_id = stock.wh_id AND dwst.id = stock.type_id AND dwst.is_delete = 0
        AND  dwst.is_default = 0
        <if test="typeSet != null and typeSet.size() > 0 ">
            AND (dwst.type_code IN
            <foreach collection="typeSet" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            OR dwst.type_code NOT IN
            <foreach collection="allTypeSet" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        INNER JOIN dic_wh wh ON wh.id = stock.wh_id AND wh.is_delete = 0
        ORDER BY stock.create_time
    </select>

    <select id="selectStockBinBySpecFeature" parameterType="com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO" resultType="com.inossem.wms.common.model.stock.dto.StockBinDTO">
        SELECT
            biz_batch_info.input_date,
            stock_bin.fty_id,
            stock_bin.location_id,
            stock_bin.mat_id,
            stock_bin.batch_id,
            stock_bin.wh_id,
            stock_bin.type_id,
            stock_bin.bin_id,
            stock_bin.cell_id,
            stock_bin.qty,
            stock_bin.qty_freeze,
            stock_bin.qty_inspection,
            stock_bin.qty_transfer,
            stock_bin.qty_haste,
            stock_bin.qty_temp,
            case    when 10=#{stockStatus} then stock_bin.qty+stock_bin.qty_temp
                    when 20=#{stockStatus} then stock_bin.qty_transfer
                    when 30=#{stockStatus} then stock_bin.qty_inspection
                    when 40=#{stockStatus} then stock_bin.qty_freeze
                    when 50=#{stockStatus} then stock_bin.qty_haste
                    when 60=#{stockStatus} then stock_bin.qty_temp
            end stock_qty

        FROM stock_bin
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
            AND dic_wh_storage_type.is_delete = 0 AND dic_wh_storage_type.is_default = 0
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="stockStatus == 10">
            AND stock_bin.qty > 0
            AND stock_bin.qty_temp = 0
        </if>
        <if test="stockStatus == 20">
            AND stock_bin.qty_transfer > 0
        </if>
        <if test="stockStatus == 30">
            AND stock_bin.qty_inspection > 0
        </if>
        <if test="stockStatus == 40">
            AND stock_bin.qty_freeze > 0
        </if>
        <if test="stockStatus == 50">
            AND stock_bin.qty_haste > 0
        </if>
        <if test="stockStatus == 60">
            AND stock_bin.qty_temp > 0
        </if>
        <if test="specStock != null">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
    </select>

    <!-- 根据特性批量查询库存 -->
    <select id="selectStockBinByAssembleList" parameterType="com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO" resultType="com.inossem.wms.common.model.stock.dto.StockBinDTO">
        SELECT
        <!-- 与上面的区别s -->
        stock_bin.id,
        stock_batch.id stock_batch_id,
        <if test="featureCode != null and featureCode !=''">
            CONCAT(${featureCode}) spec_value,
        </if>
        <!-- 与上面的区别e -->
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id,
        stock_bin.batch_id,
        stock_bin.wh_id,
        stock_bin.type_id,
        stock_bin.bin_id,
        stock_bin.cell_id,
        stock_bin.qty,
        stock_bin.qty_freeze,
        stock_bin.qty_inspection,
        stock_bin.qty_transfer,
        stock_bin.qty_haste,
        stock_bin.qty_temp,
        case    when 10=#{stockStatus} then stock_bin.qty +stock_bin.qty_temp
        when 20=#{stockStatus} then stock_bin.qty_transfer
        when 30=#{stockStatus} then stock_bin.qty_inspection
        when 40=#{stockStatus} then stock_bin.qty_freeze
        when 50=#{stockStatus} then stock_bin.qty_haste
        when 60=#{stockStatus} then stock_bin.qty_temp
        end
        <!-- 与上面的区别s -->
        - ifnull(stock_occupy.qty,0)
        <!-- 与上面的区别e -->
        stock_qty

        FROM stock_bin
        INNER JOIN stock_batch ON stock_bin.batch_id = stock_batch.batch_id AND stock_bin.fty_id = stock_batch.fty_id AND stock_bin.location_id = stock_batch.location_id AND stock_bin.mat_id = stock_batch.mat_id
        <!-- 与上面的区别s -->
        LEFT JOIN
        (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
        ON stock_occupy.stock_bin_id = stock_bin.id
        <!-- 与上面的区别e -->
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0 AND dic_wh_storage_type.is_default = 0
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="stockStatus == 10">
            AND stock_bin.qty > 0
            AND stock_bin.qty_temp = 0
        </if>
        <if test="stockStatus == 20">
            AND stock_bin.qty_transfer > 0
        </if>
        <if test="stockStatus == 30">
            AND stock_bin.qty_inspection > 0
        </if>
        <if test="stockStatus == 40">
            AND stock_bin.qty_freeze > 0
        </if>
        <if test="stockStatus == 50">
            AND stock_bin.qty_haste > 0
        </if>
        <if test="stockStatus == 60">
            AND stock_bin.qty_temp > 0
        </if>
        <if test="specStock != null">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>

        <if test="assembleList != null and assembleList.size()>0">
            AND
            <foreach collection="assembleList" open="(" separator="or" close=")" index="index" item="item">
                <!-- 与上面的区别s -->
                ( 1=1
                <if test="item.specCode != null and item.specCode !=''">
                    AND CONCAT(${item.specCode}) = #{item.specValue}
                </if>
                <if test="item.ftyId != null">
                    AND stock_bin.fty_id = #{item.ftyId}
                </if>
                <if test="item.locationId != null">
                    AND stock_bin.location_id = #{item.locationId}
                </if>
                <if test="item.matId != null">
                    AND stock_bin.mat_id = #{item.matId}
                </if>
                )
                <!-- 与上面的区别e -->
            </foreach>
        </if>
    </select>

</mapper>
