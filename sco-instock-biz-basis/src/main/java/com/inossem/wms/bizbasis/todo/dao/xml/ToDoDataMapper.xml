<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.todo.dao.ToDoMapper">

    <resultMap id="todolistResultMap" type="com.inossem.wms.common.model.auth.todo.dto.TodolistCountDTO">
        <id column="resources_code" jdbcType="VARCHAR" property="resourcesCode" />
        <result column="count_num" jdbcType="INTEGER" property="countNum" />
        <result column="receipt_type" jdbcType="INTEGER" property="receiptType" />
    </resultMap>



    <!-- 获取配置菜单 -->
    <select id="selectResourcesList" resultType="com.inossem.wms.common.model.auth.rel.entity.SysResources">
        SELECT
  		t2.id id,
		t2.resources_name resourcesName,
		t2.resources_code resourcesCode,
		t2.resources_url resourcesUrl,
		t2.parent_id parentId,
		t2.display_index displayIndex,
		t2.todolist_receipt_status todolistReceiptStatus,
		t2.todolist_item_status todolistItemStatus,
		t2.enabled FROM sys_user_todo_resources_rel t1 join sys_resources t2 on t1.resources_id=t2.id
		WHERE t1.user_id=#{userId} and t2.resources_type=0
    </select>

    <!-- 根据当前登录人查询首页代办数量 -->
    <select id="selectTodolistCount"  parameterType="java.util.List" resultMap="todolistResultMap">
        select
        t.count_num,
        t.resources_code,
        t.receipt_type
        from
        <foreach collection="list" open="(" close=")" index="index" item="item" separator=" union all ">
            <choose>
                <!--1 审批   -->
                <when test="item.resourcesCode == 'approval_todo'.toString() ">
                    SELECT
                    count(1) count_num,
                    'approval_todo' resources_code,
                    null receipt_type
                    FROM
                    (
                    SELECT DISTINCT
                    RES.*
                    FROM
                    ACT_RU_TASK RES
                    INNER JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES.ID_
                    WHERE
                    RES.ASSIGNEE_ IS NULL
                    AND I.TYPE_ = 'candidate'
                    AND (
                    I.USER_ID_ = #{item.currentUserCode}
                    OR I.GROUP_ID_ IN ('ADMIN')
                    )
                    ) temp
                </when>
                <!-- 物流清关 -->
                <when test="item.resourcesCode == 'htgr_arrival_notice_logistics'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_arrival_notice_logistics' resources_code,
                    223 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_logistics_head t1
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_logistics_item t2 ON t1.id = t2.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t2.item_status IN ${item.todolistItemStatus}
                        </if>
                        INNER JOIN(
                        SELECT
                        fty_id,
                        location_id
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        UNION ALL
                        SELECT DISTINCT
                        fty_id,
                        ''
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        ) rsl ON rsl.fty_id = t2.fty_id AND rsl.location_id =
                        t2.location_id
                    </if>
                    WHERE t1.is_delete = 0 and t1.receipt_type = 223
                    <if test="item.userType == 20">
                        AND t1.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t1.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t1.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	采购收货	到货登记  -->
                <when test="item.resourcesCode == 'pwr_arrival_registration'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_arrival_registration' resources_code,
                    102 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_register_head t1
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_register_item t2 ON t1.id = t2.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t2.item_status IN ${item.todolistItemStatus}
                        </if>
                        INNER JOIN(
                        SELECT
                        fty_id,
                        location_id
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        UNION ALL
                        SELECT DISTINCT
                        fty_id,
                        ''
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        ) rsl ON rsl.fty_id = t2.fty_id AND rsl.location_id =
                        t2.location_id
                    </if>
                    WHERE t1.is_delete = 0 and t1.receipt_type = 102
                    <if test="item.userType == 20">
                        AND t1.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t1.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t1.receipt_code
                    ) temp
                </when>
                <!--压水堆仓储管理	采购收货	质检通知  -->
                <when test="item.resourcesCode == 'pwr_assign_quality_acceptance'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_assign_quality_acceptance' resources_code,
                    103 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t3
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t4 ON t3.id =
                        t4.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t4.item_status IN ${item.todolistItemStatus}
                        </if>
                        INNER JOIN(
                        SELECT
                        fty_id,
                        location_id
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        UNION ALL
                        SELECT DISTINCT
                        fty_id,
                        ''
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        ) rsl ON rsl.fty_id = t4.fty_id AND rsl.location_id =
                        t4.location_id
                    </if>
                    WHERE t3.is_delete = 0 and t3.receipt_type = 103
                    <if test="item.userType == 20">
                        AND t3.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t3.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t3.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	采购收货	质检会签  -->
                <when test="item.resourcesCode == 'pwr_online_translation'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_online_translation' resources_code,
                    104 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t5
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t6 ON t5.id =
                        t6.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t6.item_status IN ${item.todolistItemStatus}
                        </if>
                        INNER JOIN(
                        SELECT
                        fty_id,
                        location_id
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        UNION ALL
                        SELECT DISTINCT
                        fty_id,
                        ''
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        ) rsl ON rsl.fty_id = t6.fty_id AND rsl.location_id =
                        t6.location_id
                    </if>
                    WHERE t5.is_delete = 0 and t5.receipt_type = 104
                    <if test="item.userType == 20">
                        AND t5.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t5.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t5.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	采购收货	数量差异通知  -->
                <when test="item.resourcesCode == 'pwr_inconformity_number_notice'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_inconformity_number_notice' resources_code,
                    124 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inconformity_head t7
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inconformity_item t8 ON t7.id =
                        t8.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t8.item_status IN ${item.todolistItemStatus}
                        </if>
                        INNER JOIN(
                        SELECT
                        fty_id,
                        location_id
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        UNION ALL
                        SELECT DISTINCT
                        fty_id,
                        ''
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        ) rsl ON rsl.fty_id = t8.fty_id AND rsl.location_id =
                        t8.location_id
                    </if>
                    WHERE t7.is_delete = 0 and t7.receipt_type = 124
                    <if test="item.userType == 20">
                        AND t7.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t7.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t7.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	采购收货	数量差异处置  -->
                <when test="item.resourcesCode == 'pwr_inconformity_number_maintain'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_inconformity_number_maintain' resources_code,
                    125 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inconformity_head t9
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inconformity_item t10 ON t9.id =
                        t10.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t10.item_status IN ${item.todolistItemStatus}
                        </if>
                        INNER JOIN(
                        SELECT
                        fty_id,
                        location_id
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        UNION ALL
                        SELECT DISTINCT
                        fty_id,
                        ''
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        ) rsl ON rsl.fty_id = t10.fty_id AND rsl.location_id =
                        t10.location_id
                    </if>
                    WHERE t9.is_delete = 0 and t9.receipt_type = 125
                    <if test="item.userType == 20">
                        AND t9.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t9.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t9.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	采购收货	验收入库  -->
                <when test="item.resourcesCode == 'pwr_inspect_inbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_inspect_inbound' resources_code,
                    106 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_input_head t11
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_input_item t12 ON t11.id =
                        t12.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t12.item_status IN ${item.todolistItemStatus}
                        </if>
                        INNER JOIN(
                        SELECT
                        fty_id,
                        location_id
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        UNION ALL
                        SELECT DISTINCT
                        fty_id,
                        ''
                        FROM
                        sys_user_stock_location_rel
                        WHERE
                        user_id = #{item.currentUserId}
                        AND is_delete = 0
                        ) rsl ON rsl.fty_id = t12.fty_id AND rsl.location_id =
                        t12.location_id
                    </if>
                    WHERE t11.is_delete = 0 and t11.receipt_type = 106
                    <if test="item.userType == 20">
                        AND t11.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t11.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t11.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	采购收货	物资返运  -->
                <when test="item.resourcesCode == 'pwr_material_return'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_material_return' resources_code,
                    127 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_material_return_head t13
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_material_return_item t14 ON t13.id =
                        t14.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t14.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t13.is_delete = 0 and t13.receipt_type = 127
                    <if test="item.userType == 20">
                        AND t13.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t13.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t13.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	退库管理	退库质检分配  -->
                <when test="item.resourcesCode == 'pwr_returns_inspection_assign'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_returns_inspection_assign' resources_code,
                    136 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t15
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t16 ON t15.id =
                        t16.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t16.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t15.is_delete = 0 and t15.receipt_type = 136
                    <if test="item.userType == 20">
                        AND t15.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t15.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t15.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	退库管理	退库质检会签  -->
                <when test="item.resourcesCode == 'pwr_returns_countersign_inspection'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_returns_countersign_inspection' resources_code,
                    137 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t17
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t18 ON t17.id =
                        t18.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t18.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t17.is_delete = 0 and t17.receipt_type = 137
                    <if test="item.userType == 20">
                        AND t17.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t17.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t17.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	退库管理	退库不合格项维护  -->
                <when test="item.resourcesCode == 'pwr_returns_unqualified'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_returns_unqualified' resources_code,
                    138 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inconformity_head t19
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inconformity_item t20 ON t19.id =
                        t20.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t20.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t19.is_delete = 0 and t19.receipt_type = 138
                    <if test="item.userType == 20">
                        AND t19.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t19.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t19.receipt_code
                    ) temp
                </when>
                <!--压水堆仓储管理	退库管理	领料退库  -->
                <when test="item.resourcesCode == 'pwr_gi_returns'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_gi_returns' resources_code,
                    139 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_return_head t21
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_return_item t22 ON t21.id =
                        t22.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t22.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t21.is_delete = 0 and t21.receipt_type = 139
                    <if test="item.userType == 20">
                        AND t21.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t21.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t21.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	出库管理	领用出库  -->
                <when test="item.resourcesCode == 'pwr_collect_outbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_collect_outbound' resources_code,
                    108 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_output_head t23
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_output_item t24 ON t23.id =
                        t24.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t24.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t23.is_delete = 0 and t23.receipt_type = 108
                    <if test="item.userType == 20">
                        AND t23.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t23.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t23.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	盘点管理	计划盘点管理	盘点表结果  -->
                <when test="item.resourcesCode == 'pwr_count_diff'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_count_diff' resources_code,
                    676 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_stocktaking_head t25
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_stocktaking_item t26 ON t25.id =
                        t26.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t26.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t25.is_delete = 0 and t25.receipt_type = 676
                    <if test="item.userType == 20">
                        AND t25.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t25.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t25.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	盘点管理	月度盘点管理	盘点表结果  -->
                <when test="item.resourcesCode == 'pwr_monthly_count_diff'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_monthly_count_diff' resources_code,
                    679 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_stocktaking_head t27
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_stocktaking_item t28 ON t27.id =
                        t28.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t28.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t27.is_delete = 0 and t27.receipt_type = 679
                    <if test="item.userType == 20">
                        AND t27.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t27.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t27.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	维保管理	维保结果维护 -->
                <when test="item.resourcesCode == 'pwr_maintenance_maintain'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_maintenance_maintain' resources_code,
                    818 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_maintain_head t29
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_maintain_item t30 ON t29.id =
                        t30.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t30.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t29.is_delete = 0 and t29.receipt_type = 818
                    <if test="item.userType == 20">
                        AND t29.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t29.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t29.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	寿期管理	寿期鉴定结果维护 -->
                <when test="item.resourcesCode == 'pwr_lifetime_maintain'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_lifetime_maintain' resources_code,
                    823 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_lifetime_head t31
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_lifetime_item t32 ON t31.id =
                        t32.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t32.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t31.is_delete = 0 and t31.receipt_type = 823
                    <if test="item.userType == 20">
                        AND t31.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t31.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t31.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	仓库管理	上架管理 -->
                <when test="item.resourcesCode == 'pwr_put_away'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_put_away' resources_code,
                    109 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_task_req_head t33
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_task_req_item t34 ON t33.id =
                        t34.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t34.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t33.is_delete = 0 and t33.receipt_type = 109
                    <if test="item.userType == 20">
                        AND t33.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t33.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t33.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	仓库管理	下架管理 -->
                <when test="item.resourcesCode == 'pwr_removal'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_removal' resources_code,
                    110 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_task_req_head t35
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_task_req_item t36 ON t35.id =
                        t36.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t36.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t35.is_delete = 0 and t35.receipt_type = 110
                    <if test="item.userType == 20">
                        AND t35.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t35.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t35.receipt_code
                    ) temp
                </when>
                <!--压水堆仓储管理	冻结管理	物资冻结 -->
                <when test="item.resourcesCode == 'pwr_frozen'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_frozen' resources_code,
                    4101 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_transport_head t37
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_transport_item t38 ON t37.id =
                        t38.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t38.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t37.is_delete = 0 and t37.receipt_type = 4101
                    <if test="item.userType == 20">
                        AND t37.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t37.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t37.receipt_code
                    ) temp
                </when>

                <!--压水堆仓储管理	冻结管理	物资解冻 -->
                <when test="item.resourcesCode == 'pwr_unfreeze'.toString() ">
                    SELECT
                    count(1) count_num,
                    'pwr_unfreeze' resources_code,
                    4102 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_transport_head t39
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_transport_item t40 ON t39.id =
                        t40.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t40.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t39.is_delete = 0 and t39.receipt_type = 4102
                    <if test="item.userType == 20">
                        AND t39.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t39.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t39.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	采购收货	到货通知（已驳回） -->
                <when test="item.resourcesCode == 'htgr_arrival_notice'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_arrival_notice' resources_code,
                    220 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_delivery_notice_head t41
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_delivery_notice_item t42 ON t41.id =
                        t42.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t42.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t41.is_delete = 0 and t41.receipt_type = 220
                    <if test="item.userType == 20">
                        AND t41.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t41.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t41.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	采购收货	到货登记 -->
                <when test="item.resourcesCode == 'htgr_arrival_registration'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_arrival_registration' resources_code,
                    221 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_register_head t43
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_register_item t44 ON t43.id =
                        t44.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t44.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t43.is_delete = 0 and t43.receipt_type = 221
                    <if test="item.userType == 20">
                        AND t43.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t43.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t43.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	采购收货	质检分配 -->
                <when test="item.resourcesCode == 'htgr_assign_quality_acceptance'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_assign_quality_acceptance' resources_code,
                    2310 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t45
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t46 ON t45.id =
                        t46.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t46.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t45.is_delete = 0 and t45.receipt_type = 2310
                    <if test="item.userType == 20">
                        AND t45.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t45.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t45.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	采购收货	质检会签 -->
                <when test="item.resourcesCode == 'htgr_online_translation_acceptance'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_online_translation_acceptance' resources_code,
                    2320 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t47
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t48 ON t47.id =
                        t48.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t48.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t47.is_delete = 0 and t47.receipt_type = 2320
                    <if test="item.userType == 20">
                        AND t47.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t47.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t47.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理	采购收货	差异通知（已驳回） -->
                <when test="item.resourcesCode == 'htgr_inconformity_notice_acceptance'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_inconformity_notice_acceptance' resources_code,
                    314 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inconformity_head t49
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inconformity_item t50 ON t49.id =
                        t50.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t50.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t49.is_delete = 0 and t49.receipt_type = 314

                    AND t49.purchaser_manager_name = #{item.currentUserName}

                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t49.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t49.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	采购收货	差异处置 -->
                <when test="item.resourcesCode == 'htgr_inconformity_maintain_acceptance'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_inconformity_maintain_acceptance' resources_code,
                    315 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inconformity_head t51
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inconformity_item t52 ON t51.id =
                        t52.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t52.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t51.is_delete = 0 and t51.receipt_type = 315
                    <if test="item.userType == 20">
                        AND t51.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t51.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t51.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	采购收货	验收入库 -->
                <when test="item.resourcesCode == 'htgr_inspect_inbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_inspect_inbound' resources_code,
                    214 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_input_head t53
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_input_item t54 ON t53.id =
                        t54.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t54.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t53.is_delete = 0 and t53.receipt_type = 214
                    <if test="item.userType == 20">
                        AND t53.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t53.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t53.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	采购收货	物资返运 -->
                <when test="item.resourcesCode == 'htgr_material_return'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_material_return' resources_code,
                    413 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_material_return_head t55
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_material_return_item t56 ON t55.id =
                        t56.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t56.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t55.is_delete = 0 and t55.receipt_type = 413
                    <if test="item.userType == 20">
                        AND t55.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t55.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t55.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	仓库管理	上架管理 -->
                <when test="item.resourcesCode == 'htgr_put_away'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_put_away' resources_code,
                    610 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_task_req_head t57
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_task_req_item t58 ON t57.id =
                        t58.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t58.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t57.is_delete = 0 and t57.receipt_type = 610
                    <if test="item.userType == 20">
                        AND t57.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t57.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t57.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理	仓库管理	下架管理 -->
                <when test="item.resourcesCode == 'htgr_removal'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_removal' resources_code,
                    620 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_task_req_head t59
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_task_req_item t60 ON t59.id =
                        t60.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t60.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t59.is_delete = 0 and t59.receipt_type = 620
                    <if test="item.userType == 20">
                        AND t59.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t59.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t59.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	领料管理	紧急领用出库（已驳回） -->
                <when test="item.resourcesCode == 'htgr_urgency_outbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_urgency_outbound' resources_code,
                    423 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_apply_head t61
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_apply_item t62 ON t61.id =
                        t62.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t62.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t61.is_delete = 0 and t61.receipt_type = 423
                    <if test="item.userType == 20">
                        AND t61.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t61.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t61.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	领料管理	领料通知（已驳回） -->
                <when test="item.resourcesCode == 'htgr_gi_apply'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_gi_apply' resources_code,
                    421 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_apply_head t63
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_apply_item t64 ON t63.id =
                        t64.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t64.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t63.is_delete = 0 and t63.receipt_type = 421
                    <if test="item.userType == 20">
                        AND t63.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t63.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t63.receipt_code
                    ) temp
                </when>


                <!--高温堆仓储管理	领料管理	领料出库 -->
                <when test="item.resourcesCode == 'htgr_gi_outbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_gi_outbound' resources_code,
                    414 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_output_head t65
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_output_item t66 ON t65.id =
                        t66.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t66.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t65.is_delete = 0 and t65.receipt_type = 414
                    <if test="item.userType == 20">
                        AND t65.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t65.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t65.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	退库管理	质检分配 -->
                <when test="item.resourcesCode == 'htgr_returns_inspection_assign'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_returns_inspection_assign' resources_code,
                    2311 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t67
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t68 ON t67.id =
                        t68.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t68.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t67.is_delete = 0 and t67.receipt_type = 2311
                    <if test="item.userType == 20">
                        AND t67.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t67.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t67.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	退库管理	质检会签 -->
                <when test="item.resourcesCode == 'htgr_returns_countersign_inspection'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_returns_countersign_inspection' resources_code,
                    2321 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t69
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t70 ON t69.id =
                        t70.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t70.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t69.is_delete = 0 and t69.receipt_type = 2321
                    <if test="item.userType == 20">
                        AND t69.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t69.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t69.receipt_code
                    ) temp
                </when>


                <!--高温堆仓储管理	退库管理	退库不合格项维护 -->
                <when test="item.resourcesCode == 'htgr_returns_unqualified'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_returns_unqualified' resources_code,
                    310 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inconformity_head t71
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inconformity_item t72 ON t71.id =
                        t72.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t72.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t71.is_delete = 0 and t71.receipt_type = 310
                    <if test="item.userType == 20">
                        AND t71.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t71.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t71.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	退库管理	领料退库 -->
                <when test="item.resourcesCode == 'htgr_gi_returns'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_gi_returns' resources_code,
                    312 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_return_head t73
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_return_item t74 ON t73.id =
                        t74.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t74.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t73.is_delete = 0 and t73.receipt_type = 312
                    <if test="item.userType == 20">
                        AND t73.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t73.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t73.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理	退转库管理	分配质检 -->
                <when test="item.resourcesCode == 'htgr_returns_transfer_inspection_assign'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_returns_transfer_inspection_assign' resources_code,
                    2312 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t75
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t76 ON t75.id =
                        t76.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t76.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t75.is_delete = 0 and t75.receipt_type = 2312
                    <if test="item.userType == 20">
                        AND t75.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t75.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t75.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理	退转库管理	质检会签 -->
                <when test="item.resourcesCode == 'htgr_returns_transfer_countersign_inspection'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_returns_transfer_countersign_inspection' resources_code,
                    2322 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inspect_head t77
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inspect_item t78 ON t77.id =
                        t78.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t78.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t77.is_delete = 0 and t77.receipt_type = 2322
                    <if test="item.userType == 20">
                        AND t77.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t77.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t77.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理	退转库管理	退转库不合格项维护 -->
                <when test="item.resourcesCode == 'htgr_return_to_warehouse_maintain'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_return_to_warehouse_maintain' resources_code,
                    320 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_inconformity_head t79
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_inconformity_item t80 ON t79.id =
                        t80.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t80.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t79.is_delete = 0 and t79.receipt_type = 320
                    <if test="item.userType == 20">
                        AND t79.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t79.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t79.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理	退转库管理	退转库入库 -->
                <when test="item.resourcesCode == 'htgr_return_to_warehouse_inbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_return_to_warehouse_inbound' resources_code,
                    321 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_return_head t81
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_return_item t82 ON t81.id =
                        t82.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t82.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t81.is_delete = 0 and t81.receipt_type = 321
                    <if test="item.userType == 20">
                        AND t81.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t81.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t81.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理	退转库管理	退转库出库 -->
                <when test="item.resourcesCode == 'htgr_return_to_warehouse_gi_outbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_return_to_warehouse_gi_outbound' resources_code,
                    323 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_output_head t83
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_output_item t84 ON t83.id =
                        t84.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t84.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t83.is_delete = 0 and t83.receipt_type = 323
                    <if test="item.userType == 20">
                        AND t83.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t83.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t83.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理 计划盘点	盘点表结果-->
                <when test="item.resourcesCode == 'htgr_plan_count_diff'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_plan_count_diff' resources_code,
                    672 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_stocktaking_head t85
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_stocktaking_item t86 ON t85.id =
                        t86.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t86.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t85.is_delete = 0 and t85.receipt_type = 672
                    <if test="item.userType == 20">
                        AND t85.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t85.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t85.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理 盘点管理	盘点表创建-->
                <when test="item.resourcesCode == 'htgr_count_create'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_count_create' resources_code,
                    670 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_stocktaking_head t87
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_stocktaking_item t88 ON t87.id =
                        t88.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t88.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t87.is_delete = 0 and t87.receipt_type = 670
                    <if test="item.userType == 20">
                        AND t87.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t87.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t87.receipt_code
                    ) temp
                </when>

                <!--高温堆仓储管理 盘点管理	盘点表结果-->
                <when test="item.resourcesCode == 'htgr_count_diff'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_count_diff' resources_code,
                    670 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_stocktaking_head t87
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_stocktaking_item t88 ON t87.id =
                        t88.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t88.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t87.is_delete = 0 and t87.receipt_type = 670
                    <if test="item.userType == 20">
                        AND t87.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t87.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t87.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理 寿期管理	寿期鉴定单创建（已驳回）-->
                <when test="item.resourcesCode == 'htgr_lifetime_appraisal'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_lifetime_appraisal' resources_code,
                    812 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_lifetime_head t89
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_lifetime_item t90 ON t89.id =
                        t90.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t90.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t89.is_delete = 0 and t89.receipt_type = 812
                    <if test="item.userType == 20">
                        AND t89.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t89.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t89.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理 寿期管理	寿期鉴定结果维护-->
                <when test="item.resourcesCode == 'htgr_lifetime_maintain'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_lifetime_maintain' resources_code,
                    813 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_lifetime_head t91
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_lifetime_item t92 ON t91.id =
                        t92.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t92.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t91.is_delete = 0 and t91.receipt_type = 813
                    <if test="item.userType == 20">
                        AND t91.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t91.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t91.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理 维保管理	维保结果维护-->
                <when test="item.resourcesCode == 'htgr_maintenance_maintain'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_maintenance_maintain' resources_code,
                    811 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_maintain_head t93
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_maintain_item t94 ON t93.id =
                        t94.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t94.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t93.is_delete = 0 and t93.receipt_type = 811
                    <if test="item.userType == 20">
                        AND t93.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t93.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t93.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理 暂存物资管理	暂存物项入库-->
                <when test="item.resourcesCode == 'htgr_temporary_inbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_temporary_inbound' resources_code,
                    815 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_input_head t95
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_input_item t96 ON t95.id =
                        t96.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t96.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t95.is_delete = 0 and t95.receipt_type = 815
                    <if test="item.userType == 20">
                        AND t95.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t95.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t95.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理 暂存物资管理	暂存物项出库-->
                <when test="item.resourcesCode == 'htgr_temporary_outbound'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_temporary_outbound' resources_code,
                    817 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_output_head t97
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_output_item t98 ON t97.id =
                        t98.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t98.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t97.is_delete = 0 and t97.receipt_type = 817
                    <if test="item.userType == 20">
                        AND t97.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t97.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t97.receipt_code
                    ) temp
                </when>
                <!--高温堆仓储管理 巡检管理	巡检结果维护-->
                <when test="item.resourcesCode == 'htgr_inspection_result'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_inspection_result' resources_code,
                    150 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_checkup_head t99
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_checkup_item t100 ON t99.id =
                        t100.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t100.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t99.is_delete = 0 and t99.receipt_type = 150
                    <if test="item.userType == 20">
                        AND t99.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t99.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t99.receipt_code
                    ) temp
                </when>
                <!--供应链管理 物流信息管理 运单管理 -->
                <when test="item.resourcesCode == 'waybill-mgt'.toString() ">
                    SELECT
                    count(1) count_num,
                    'waybill-mgt' resources_code,
                    9403 receipt_type
                    FROM
                    ( SELECT 1 FROM biz_receipt_delivery_waybill_head t101
                    join dic_supplier_user_rel t102 on t101.supplier_id = t102.supplier_id
                    WHERE t101.is_delete = 0 and t101.receipt_type = 9403
                    <!-- <if test="item.userType == 20"> -->
                        AND t102.user_id = #{item.currentUserId}
                    <!-- </if> -->
                <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                    AND t101.receipt_status IN ${item.todolistReceiptStatus}
                </if>
                GROUP BY t101.receipt_code
                ) temp
            </when>
            <!--供应链管理 物流信息管理 托收单管理 -->
                <when test="item.resourcesCode == 'submission-redemption'.toString() ">
                    SELECT
                    count(1) count_num,
                    'submission-redemption' resources_code,
                    9401 receipt_type
                    FROM
                    (SELECT 1
                    FROM biz_receipt_submit_redemption_doc_head t103
                    WHERE t103.is_delete = 0
                    and t103.receipt_type = 9401
                    <!-- <if test="item.userType == 20"> -->
                        AND t103.handle_user_id = #{item.currentUserId}
                    <!-- </if> -->
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t103.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t103.receipt_code
                    ) temp
                </when>
                <!--供应链管理 物流信息管理 所得税免税 -->
                <when test="item.resourcesCode == 'income-tax-exemption'.toString() ">
                    SELECT
                    count(1) count_num,
                    'income-tax-exemption' resources_code,
                    9402 receipt_type
                    FROM
                    (SELECT 1
                    FROM biz_receipt_income_tax_exemption_head t105
                    WHERE t105.is_delete = 0
                    and t105.receipt_type = 9402
                    <!-- <if test="item.userType == 20"> -->
                        AND t105.handle_user_id = #{item.currentUserId}
                    <!-- </if> -->
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t105.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t105.receipt_code
                    ) temp
                </when>
                <!--供应链管理 物流信息管理 合同收货 -->
                <when test="item.resourcesCode == 'htgr_contract_receipt'.toString() ">
                    SELECT
                    count(1) count_num,
                    'htgr_contract_receipt' resources_code,
                    190 receipt_type
                    FROM
                    (SELECT 1
                    FROM biz_receipt_contract_receiving_head t107
                    <if test="item.currentUserId != null and item.currentUserId != '' ">
                        INNER JOIN biz_receipt_contract_receiving_item t108 ON t107.id =
                        t108.head_id
                        <if test="item.todolistItemStatus != null and item.todolistItemStatus != '' ">
                            AND t108.item_status IN ${item.todolistItemStatus}
                        </if>
                    </if>
                    WHERE t107.is_delete = 0
                    and t107.receipt_type = 190
                    <if test="item.userType == 20">
                        AND t107.create_user_id = #{item.currentUserId}
                    </if>
                    <if test="item.todolistReceiptStatus != null and item.todolistReceiptStatus != '' ">
                        AND t107.receipt_status IN ${item.todolistReceiptStatus}
                    </if>
                    GROUP BY t107.receipt_code
                    ) temp
                </when>
                <when test="item.resourcesCode == 'htgr_demand_plan'.toString() ">
                    select
                        count(count_num) as count_num,
                        'htgr_demand_plan'  resources_code,
                        400                 receipt_type
                    from (select ifnull(count(distinct biz_receipt_demand_plan_head.id), 0) count_num
                    from biz_receipt_demand_plan_head
                    inner join biz_receipt_demand_plan_item
                    on biz_receipt_demand_plan_item.head_id = biz_receipt_demand_plan_head.id
                    where biz_receipt_demand_plan_head.receipt_status = 90
                    and biz_receipt_demand_plan_head.is_delete = 0
                    and biz_receipt_demand_plan_item.item_status != 92
                    and biz_receipt_demand_plan_item.is_delete = 0
                    and biz_receipt_demand_plan_head.handle_User_Id = #{item.currentUserId}
                    group by biz_receipt_demand_plan_head.id) t
                </when>

                <!-- 无匹配项  -->
                <otherwise>
                    (select
                    null count_num,
                    null resources_code,
                    null receipt_type
                    LIMIT 0 )
                </otherwise>
            </choose>

        </foreach>
        t order by t.receipt_type
    </select>


    <!--获取代办中用户选中以及未选中的总集合-->
    <select id="selectCurrentUserToDoResources" parameterType="com.inossem.wms.common.model.auth.todo.po.ToDoResourcePO" resultType="com.inossem.wms.common.model.auth.todo.dto.ToDoResourceDTO">
        SELECT
        t.id resourcesId,
        t.resources_code resourcesCode,
        t.parent_id parentId,
        t.display_index displayIndex,
        t.enabled,
        t.resources_name resourcesName,
        CASE
        WHEN t3.resources_id IS NULL THEN
        0
        ELSE
        1
        END isChecked
        FROM
        (
        SELECT
        t2.resources_id
        FROM
        sys_user_role_rel t1
        JOIN sys_role_resources_rel t2 ON t1.role_id = t2.role_id
        JOIN sys_resources t3 ON t3.id = t2.resources_id
        WHERE
        t1.user_id = #{userId}
        GROUP BY
        t2.resources_id
        ) st
        JOIN sys_resources t ON st.resources_id = t.id
        LEFT JOIN sys_user_todo_resources_rel t3 ON t.id = t3.resources_id and t3.user_id = #{userId}
        where t.enabled = 1 and t.resources_type= #{resourcesType}
        and t.todolist = 1
        AND t.is_delete = 0
        ORDER BY t.display_index
    </select>

</mapper>
