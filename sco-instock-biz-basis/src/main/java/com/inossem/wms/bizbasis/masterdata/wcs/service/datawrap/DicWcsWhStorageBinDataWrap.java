package com.inossem.wms.bizbasis.masterdata.wcs.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.wcs.entity.DicWcsWhStorageBin;
import com.inossem.wms.common.model.wcs.po.DicWcsWhStorageBinSearchPO;
import com.inossem.wms.common.model.wcs.vo.DicWcsWhStorageBinPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.wcs.dao.DicWcsWhStorageBinMapper;
import org.springframework.stereotype.Service;

/***
 * <AUTHOR>
 * @date 2021-04-07 15:10:51
 **/
@Service
public class DicWcsWhStorageBinDataWrap extends BaseDataWrap<DicWcsWhStorageBinMapper, DicWcsWhStorageBin> {

    /**
     * 查询WCS仓位主数据分页列表
     *
     * @param page 分页参数
     * @param po 条件
     * @return
     */
    public IPage<DicWcsWhStorageBinPageVO> getDicWcsWhStorageBinPageVOList(IPage<DicWcsWhStorageBinPageVO> page, DicWcsWhStorageBinSearchPO po) {
        return page.setRecords(this.baseMapper.getDicWcsWhStorageBinPageVOList(page, po));
    }
}
