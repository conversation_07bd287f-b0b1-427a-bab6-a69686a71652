package com.inossem.wms.bizbasis.masterdata.car.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.auth.role.entity.SysRole;
import com.inossem.wms.common.model.auth.role.po.SysRoleSearchPO;
import com.inossem.wms.common.model.auth.role.vo.SysRolePageVO;
import com.inossem.wms.common.model.masterdata.car.entity.DicCarType;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Mapper
public interface DicCarTypeMapper extends WmsBaseMapper<DicCarType> {

}
