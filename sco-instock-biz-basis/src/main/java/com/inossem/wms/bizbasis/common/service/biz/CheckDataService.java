package com.inossem.wms.bizbasis.common.service.biz;

import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageCellDataWrap;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.common.DicDeleteCheckPO;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.cell.entity.DicWhStorageCell;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.org.corp.entity.DicCorp;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.storagetype.dto.DicWhStorageTypeDTO;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.bizbasis.common.dao.CheckDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CheckDataService {

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected CheckDataMapper checkDataDao;

    @Autowired
    protected DicWhStorageCellDataWrap dicWhStorageCellDataWrap;

    public void dicDeleteCheck(DicDeleteCheckPO deleteCheckPo) {

        /* **************************************   公司    **************************************************************/
        if (UtilNumber.isNotEmpty(deleteCheckPo.getCorpId())) {

            DicCorp dicCorp = dictionaryService.getCorpCacheById(deleteCheckPo.getCorpId());
            if (dicCorp == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_CORP_NOT_EXIST);
            }

            // 公司判断是否已经被分配了用户
            List<Byte> userList = checkDataDao.selectSysUserByCorpId(deleteCheckPo.getCorpId());
            if (userList != null && userList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_DUE_TO_USED_USER, dicCorp.getCorpCode());
            }

            // 公司判断是否已经被分配了工厂
            List<Byte> factoryList = checkDataDao.selectFactoryByCorpId(deleteCheckPo.getCorpId());
            if (factoryList != null && factoryList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_DUE_TO_USED_FACTORY, dicCorp.getCorpCode());
            }

            // 公司判断是否已经被分配了部门
            List<Byte> deptList = checkDataDao.selectDeptByCorpId(deleteCheckPo.getCorpId());
            if (deptList != null && deptList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicCorp.getCorpCode());
            }

            /* **************************************   工厂    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getFtyId())) {

            DicFactoryDTO dicFactory = dictionaryService.getFtyCacheById(deleteCheckPo.getFtyId());
            if (dicFactory == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
            }

            // 工厂判断是否被用户库存地点权限占用
            /*List<Byte> relUserStockLocationList = checkDataDao.selectRelLocationByDicDeleteCheckPo(deleteCheckPo);
            if (relUserStockLocationList != null && relUserStockLocationList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }*/

            // 工厂判断是否被库存地点占用
            List<Byte> locationList = checkDataDao.selectLocationByDicDeleteCheckPo(deleteCheckPo);
            if (locationList != null && locationList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_DUE_TO_USED_STOCK_LOCATION, dicFactory.getFtyCode());
            }

            // 工厂判断是否被单位换算关系占用
            /*List<Byte> relUnitList = checkDataDao.selectRelUnitByDicDeleteCheckPo(deleteCheckPo);
            if (relUnitList != null && relUnitList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }*/

            // 工厂判断是否被批次库存占用
            List<Byte> stockBatchList = checkDataDao.selectStockBatchByDicDeleteCheckPo(deleteCheckPo);
            if (stockBatchList != null && stockBatchList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被仓位库存占用
            List<Byte> stockBinList = checkDataDao.selectStockBinByDicDeleteCheckPo(deleteCheckPo);
            if (stockBinList != null && stockBinList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被验收单占用
            List<Byte> inspectList = checkDataDao.selectInspectByDicDeleteCheckItemPo(deleteCheckPo);
            if (inspectList != null && inspectList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被入库单占用
            List<Byte> inputList = checkDataDao.selectInputByDicDeleteCheckItemPo(deleteCheckPo);
            if (inputList != null && inputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被出库单占用
            List<Byte> outputList = checkDataDao.selectOutputByDicDeleteCheckItemPo(deleteCheckPo);
            if (outputList != null && outputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被退库单占用
            List<Byte> returnList = checkDataDao.selectReturnByDicDeleteCheckItemPo(deleteCheckPo);
            if (returnList != null && returnList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被转储单占用
            List<Byte> transportList = checkDataDao.selectTransportByDicDeleteCheckItemPo(deleteCheckPo);
            if (transportList != null && transportList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被作业单占用
            List<Byte> taskList = checkDataDao.selectTaskByDicDeleteCheckHeadPo(deleteCheckPo);
            if (taskList != null && taskList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被请求单占用
            List<Byte> taskReqList = checkDataDao.selectTaskReqByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskReqList != null && taskReqList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            // 工厂判断是否被盘点单占用
            List<Byte> takeMatList = checkDataDao.selectStockTakeByDicDeleteCheckBinPo(deleteCheckPo);
            if (takeMatList != null && takeMatList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicFactory.getFtyCode());
            }

            /* **************************************   库存地点    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getLocationId())) {

            DicStockLocationDTO dicStockLocation = dictionaryService.getLocationCacheById(deleteCheckPo.getLocationId());
            if (dicStockLocation == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_LOCATION_NOT_EXIST);
            }

            // 库存地点判断是否被用户库存地点权限占用
            /*List<Byte> relUserStockLocationList = checkDataDao.selectRelLocationByDicDeleteCheckPo(deleteCheckPo);
            if (relUserStockLocationList != null && relUserStockLocationList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }*/

            // 库存地点判断是否被批次库存占用
            List<Byte> stockBatchList = checkDataDao.selectStockBatchByDicDeleteCheckPo(deleteCheckPo);
            if (stockBatchList != null && stockBatchList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被仓位库存占用
            List<Byte> stockBinList = checkDataDao.selectStockBinByDicDeleteCheckPo(deleteCheckPo);
            if (stockBinList != null && stockBinList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被验收单占用
            List<Byte> inspectList = checkDataDao.selectInspectByDicDeleteCheckItemPo(deleteCheckPo);
            if (inspectList != null && inspectList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被入库单占用
            List<Byte> inputList = checkDataDao.selectInputByDicDeleteCheckItemPo(deleteCheckPo);
            if (inputList != null && inputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被出库单占用
            List<Byte> outputList = checkDataDao.selectOutputByDicDeleteCheckItemPo(deleteCheckPo);
            if (outputList != null && outputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被退库单占用
            List<Byte> returnList = checkDataDao.selectReturnByDicDeleteCheckItemPo(deleteCheckPo);
            if (returnList != null && returnList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被转储单占用
            List<Byte> transportList = checkDataDao.selectTransportByDicDeleteCheckItemPo(deleteCheckPo);
            if (transportList != null && transportList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被请求单占用
            List<Byte> taskReqList = checkDataDao.selectTaskReqByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskReqList != null && taskReqList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            // 库存地点判断是否被盘点单占用
            List<Byte> takeMatList = checkDataDao.selectStockTakeByDicDeleteCheckBinPo(deleteCheckPo);
            if (takeMatList != null && takeMatList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicStockLocation.getLocationCode());
            }

            /* **************************************   仓库    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getWhId())) {

            DicWh dicWh = dictionaryService.getWhCacheById(deleteCheckPo.getWhId());
            if (dicWh == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_WAREHOUSE_NOT_EXIST);
            }

            // 仓库判断是否分配库存地点
            List<Byte> locationList = checkDataDao.selectLocationByDicDeleteCheckPo(deleteCheckPo);
            if (locationList != null && locationList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否分配存储类型
            List<Byte> typeList = checkDataDao.selectDicWhStorageTypeByWhId(deleteCheckPo.getWhId());
            if (typeList != null && typeList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否分配仓位
            List<Byte> binList = checkDataDao.selectDicWhStorageBinByDicDeleteCheckPo(deleteCheckPo);
            if (binList != null && binList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被仓位库存占用
            List<Byte> stockBinList = checkDataDao.selectStockBinByDicDeleteCheckPo(deleteCheckPo);
            if (stockBinList != null && stockBinList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被验收单占用
            List<Byte> inspectList = checkDataDao.selectInspectByDicDeleteCheckItemPo(deleteCheckPo);
            if (inspectList != null && inspectList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被入库单占用
            List<Byte> inputList = checkDataDao.selectInputByDicDeleteCheckItemPo(deleteCheckPo);
            if (inputList != null && inputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被出库单占用
            List<Byte> outputList = checkDataDao.selectOutputByDicDeleteCheckItemPo(deleteCheckPo);
            if (outputList != null && outputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被退库单占用
            List<Byte> returnList = checkDataDao.selectReturnByDicDeleteCheckItemPo(deleteCheckPo);
            if (returnList != null && returnList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被转储单占用
            List<Byte> transportList = checkDataDao.selectTransportByDicDeleteCheckItemPo(deleteCheckPo);
            if (transportList != null && transportList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被请求单占用
            List<Byte> taskReqList = checkDataDao.selectTaskReqByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskReqList != null && taskReqList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被盘点单占用
            List<Byte> takeMatList = checkDataDao.selectStockTakeByDicDeleteCheckBinPo(deleteCheckPo);
            if (takeMatList != null && takeMatList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            // 仓库判断是否被盘点单Item占用
            List<Byte> takeMatItemList = checkDataDao.selectStockTakeByDicDeleteCheckItemPo(deleteCheckPo);
            if (takeMatItemList != null && takeMatItemList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWh.getWhCode());
            }

            /* **************************************   存储类型    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getTypeId())) {

            DicWhStorageTypeDTO dicWhStorageType = dictionaryService.getStorageTypeCacheById(deleteCheckPo.getTypeId());
            if (dicWhStorageType == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_TYPE_NOT_EXIST);
            }

            // 存储类型判断是否分配仓位
            List<Byte> binList = checkDataDao.selectDicWhStorageBinByDicDeleteCheckPo(deleteCheckPo);
            if (binList != null && binList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 存储类型判断是否被仓位库存占用
            List<Byte> stockBinList = checkDataDao.selectStockBinByDicDeleteCheckPo(deleteCheckPo);
            if (stockBinList != null && stockBinList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 存储类型判断是否被入库单占用
            List<Byte> inputList = checkDataDao.selectInputByDicDeleteCheckBinPo(deleteCheckPo);
            if (inputList != null && inputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 存储类型判断是否被出库单占用
            List<Byte> outputList = checkDataDao.selectOutputByDicDeleteCheckBinPo(deleteCheckPo);
            if (outputList != null && outputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 存储类型判断是否被退库单占用
            List<Byte> returnList = checkDataDao.selectReturnByDicDeleteCheckBinPo(deleteCheckPo);
            if (returnList != null && returnList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 存储类型判断是否被转储单占用
            List<Byte> transportList = checkDataDao.selectTransportByDicDeleteCheckBinPo(deleteCheckPo);
            if (transportList != null && transportList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 存储类型判断是否被作业单占用
            List<Byte> taskList = checkDataDao.selectTaskByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskList != null && taskList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 存储类型判断是否被盘点单占用
            List<Byte> takeMatList = checkDataDao.selectStockTakeByDicDeleteCheckBinPo(deleteCheckPo);
            if (takeMatList != null && takeMatList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            // 仓库判断是否被盘点单Item占用
            List<Byte> takeMatItemList = checkDataDao.selectStockTakeByDicDeleteCheckItemPo(deleteCheckPo);
            if (takeMatItemList != null && takeMatItemList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageType.getTypeCode());
            }

            /* **************************************   仓位    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getBinId())) {

            DicWhStorageBinDTO dicWhStorageBin = dictionaryService.getBinCacheById(deleteCheckPo.getBinId());
            if (dicWhStorageBin == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_NOT_EXIST);
            }

            // 仓位判断是否被仓位库存占用
            List<Byte> stockBinList = checkDataDao.selectStockBinByDicDeleteCheckPo(deleteCheckPo);
            if (stockBinList != null && stockBinList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            // 仓位判断是否被入库单占用
            List<Byte> inputList = checkDataDao.selectInputByDicDeleteCheckBinPo(deleteCheckPo);
            if (inputList != null && inputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            // 仓位判断是否被出库单占用
            List<Byte> outputList = checkDataDao.selectOutputByDicDeleteCheckBinPo(deleteCheckPo);
            if (outputList != null && outputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            // 仓位判断是否被退库单占用
            List<Byte> returnList = checkDataDao.selectReturnByDicDeleteCheckBinPo(deleteCheckPo);
            if (returnList != null && returnList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            // 仓位判断是否被转储单占用
            List<Byte> transportList = checkDataDao.selectTransportByDicDeleteCheckBinPo(deleteCheckPo);
            if (transportList != null && transportList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            // 仓位判断是否被作业单占用
            List<Byte> taskList = checkDataDao.selectTaskByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskList != null && taskList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            // 仓位判断是否被盘点单占用
            List<Byte> takeMatList = checkDataDao.selectStockTakeByDicDeleteCheckBinPo(deleteCheckPo);
            if (takeMatList != null && takeMatList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            // 仓库判断是否被盘点单Item占用
            List<Byte> takeMatItemList = checkDataDao.selectStockTakeByDicDeleteCheckItemPo(deleteCheckPo);
            if (takeMatItemList != null && takeMatItemList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageBin.getBinCode());
            }

            /* **************************************   存储单元    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getCellId())) {

            DicWhStorageCell dicWhStorageCell = dicWhStorageCellDataWrap.getById(deleteCheckPo.getCellId());

            // 存储单元(电子秤)判断是否生成采集任务
            List<Byte> collectionList = checkDataDao.selectCollectionDeleteCheckPo(deleteCheckPo);
            if (collectionList != null && collectionList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_COLLECTION_TASK_GEN_NOT_DELETE, dicWhStorageCell.getCellCode());
            }

            // 存储单元(电子秤)判断是否被作业单占用
            List<Byte> taskList = checkDataDao.selectTaskByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskList != null && taskList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageCell.getCellCode());
            }

            // 存储单元(电子秤)判断是否被重量库存占用
            List<Byte> weightList = checkDataDao.selectStockBinWeightByDicDeleteCheckPo(deleteCheckPo);
            if (weightList != null && weightList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicWhStorageCell.getCellCode());
            }
            /* **************************************   物料    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getMatId())) {

            DicMaterialDTO dicMaterial = dictionaryService.getMatCacheById(deleteCheckPo.getMatId());
            if (dicMaterial == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }

            // 物料判断是否被单位换算关系占用
            /*List<Byte> relUnitList = checkDataDao.selectRelUnitByDicDeleteCheckPo(deleteCheckPo);
            if (relUnitList != null && relUnitList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }*/

            // 物料判断是否被批次库存占用
            List<Byte> stockBatchList = checkDataDao.selectStockBatchByDicDeleteCheckPo(deleteCheckPo);
            if (stockBatchList != null && stockBatchList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被仓位库存占用
            List<Byte> stockBinList = checkDataDao.selectStockBinByDicDeleteCheckPo(deleteCheckPo);
            if (stockBinList != null && stockBinList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被验收单占用
            List<Byte> inspectList = checkDataDao.selectInspectByDicDeleteCheckItemPo(deleteCheckPo);
            if (inspectList != null && inspectList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被入库单占用
            List<Byte> inputList = checkDataDao.selectInputByDicDeleteCheckItemPo(deleteCheckPo);
            if (inputList != null && inputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被出库单占用
            List<Byte> outputList = checkDataDao.selectOutputByDicDeleteCheckItemPo(deleteCheckPo);
            if (outputList != null && outputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被退库单占用
            List<Byte> returnList = checkDataDao.selectReturnByDicDeleteCheckItemPo(deleteCheckPo);
            if (returnList != null && returnList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被转储单占用
            List<Byte> transportList = checkDataDao.selectTransportByDicDeleteCheckItemPo(deleteCheckPo);
            if (transportList != null && transportList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被请求单占用
            List<Byte> taskReqList = checkDataDao.selectTaskReqByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskReqList != null && taskReqList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            // 物料判断是否被盘点单占用
            List<Byte> takeMatList = checkDataDao.selectStockTakeByDicDeleteCheckBinPo(deleteCheckPo);
            if (takeMatList != null && takeMatList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicMaterial.getMatCode());
            }

            /* **************************************   单位    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getUnitId())) {

            DicUnit dicUnit = dictionaryService.getUnitCacheById(deleteCheckPo.getUnitId());
            if (dicUnit == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_NOT_EXIST);
            }

            // 单位判断是否被单位换算关系占用
            /*List<Byte> relUnitList = checkDataDao.selectRelUnitByDicDeleteCheckPo(deleteCheckPo);
            if (relUnitList != null && relUnitList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }*/

            // 单位判断是否被验收单占用
            List<Byte> inspectList = checkDataDao.selectInspectByDicDeleteCheckItemPo(deleteCheckPo);
            if (inspectList != null && inspectList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }

            // 单位判断是否被入库单占用
            List<Byte> inputList = checkDataDao.selectInputByDicDeleteCheckItemPo(deleteCheckPo);
            if (inputList != null && inputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }

            // 单位判断是否被出库单占用
            List<Byte> outputList = checkDataDao.selectOutputByDicDeleteCheckItemPo(deleteCheckPo);
            if (outputList != null && outputList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }

            // 单位判断是否被退库单占用
            List<Byte> returnList = checkDataDao.selectReturnByDicDeleteCheckItemPo(deleteCheckPo);
            if (returnList != null && returnList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }

            // 单位判断是否被转储单占用
            List<Byte> transportList = checkDataDao.selectTransportByDicDeleteCheckItemPo(deleteCheckPo);
            if (transportList != null && transportList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }

            // 单位判断是否被请求单占用
            List<Byte> taskReqList = checkDataDao.selectTaskReqByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskReqList != null && taskReqList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }

            // 单位判断是否被盘点单占用
            List<Byte> takeMatList = checkDataDao.selectStockTakeByDicDeleteCheckBinPo(deleteCheckPo);
            if (takeMatList != null && takeMatList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DELETE_FAIL_IN_USE, dicUnit.getUnitCode());
            }

            /* **************************************   用户    **************************************************************/
        } else if (UtilNumber.isNotEmpty(deleteCheckPo.getUserId())) {

            // 送货通知单
            if (!checkDataDao.selectInspectByModifyUserId(deleteCheckPo.getUserId()).isEmpty()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM);
                // 入库单
            } else if (!checkDataDao.selectInputByModifyUserId(deleteCheckPo.getUserId()).isEmpty()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM);
                // 出库单
            } else if (!checkDataDao.selectOutputByModifyUserId(deleteCheckPo.getUserId()).isEmpty()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM);
                // 退库单
            } else if (!checkDataDao.selectReturnByModifyUserId(deleteCheckPo.getUserId()).isEmpty()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM);
                // 作业单
            } else if (!checkDataDao.selectTaskByModifyUserId(deleteCheckPo.getUserId()).isEmpty()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM);
                // 仓储作业申请明细单
            } else if (!checkDataDao.selectTaskReqByModifyUserId(deleteCheckPo.getUserId()).isEmpty()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM);
                // 转储单
            } else if (!checkDataDao.selectTransportByModifyUserId(deleteCheckPo.getUserId()).isEmpty()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM);
            }
        }
    }

    public void dicUpdateCheck(DicDeleteCheckPO deleteCheckPo) {
         /* **************************************   存储单元    **************************************************************/
        if (UtilNumber.isNotEmpty(deleteCheckPo.getCellId())) {
            DicWhStorageCell dicWhStorageCell = dicWhStorageCellDataWrap.getById(deleteCheckPo.getCellId());

            // 存储单元(电子秤)判断是否生成采集任务
            List<Byte> collectionList = checkDataDao.selectCollectionDeleteCheckPo(deleteCheckPo);
            if (collectionList != null && collectionList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_COLLECTION_TASK_GEN_NOT_UPDATE, dicWhStorageCell.getCellCode());
            }

            // 存储单元(电子秤)判断是否被作业单占用
            List<Byte> taskList = checkDataDao.selectTaskByDicDeleteCheckItemPo(deleteCheckPo);
            if (taskList != null && taskList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_UPDATE_FAIL_IN_USE, dicWhStorageCell.getCellCode());
            }

            // 存储单元(电子秤)判断是否被重量库存占用
            List<Byte> weightList = checkDataDao.selectStockBinWeightByDicDeleteCheckPo(deleteCheckPo);
            if (weightList != null && weightList.size() > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_UPDATE_FAIL_IN_USE, dicWhStorageCell.getCellCode());
            }
        }
    }
}
