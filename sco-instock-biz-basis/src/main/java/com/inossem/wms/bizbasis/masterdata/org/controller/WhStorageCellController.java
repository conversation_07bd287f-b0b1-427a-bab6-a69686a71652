package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.masterdata.cell.po.DicWhStorageCellPushPO;
import com.inossem.wms.common.model.masterdata.cell.vo.DicWhStorageCellPushVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageCellService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.cell.dto.DicWhStorageCellDTO;
import com.inossem.wms.common.model.masterdata.cell.po.DicWhStorageCellSavePO;
import com.inossem.wms.common.model.masterdata.cell.po.DicWhStorageCellSearchPO;
import com.inossem.wms.common.model.masterdata.cell.vo.DicWhStorageCellPageVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 存储单元主数据 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@RestController
@Slf4j
@Api(tags = "存储单元管理")
public class WhStorageCellController {

    @Autowired
    protected WhStorageCellService whStorageCellService;

    /**
     * 查询存储单元列表
     *
     * @param po 查询条件
     * @return 存储单元列表
     *
     */
    @ApiOperation(value = "查询存储单元列表", tags = {"存储单元管理"})
    @PostMapping(path = "/master-data/cell/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicWhStorageCellPageVO>> getPage(@RequestBody DicWhStorageCellSearchPO po, BizContext ctx) {
        return BaseResult.success(whStorageCellService.getPage(ctx));
    }

    /**
     * 查看存储单元详情
     *
     * @param id 主键id
     * @return 存储单元详情
     *
     */
    @ApiOperation(value = "查看存储单元详情", tags = {"存储单元管理"})
    @GetMapping(path = "/master-data/cell/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicWhStorageCellDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(whStorageCellService.getInfo(ctx));
    }

    /**
     * 新增存储单元信息
     *
     * @param po 存储单元信息
     */
    @ApiOperation(value = "新增存储单元信息", tags = {"存储单元管理"})
    @PostMapping(path = "/master-data/cell", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicWhStorageCellSavePO po, BizContext ctx) {
        whStorageCellService.add(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_CELL_SAVE_SUCCESS, po.getDicWhStorageCellInfo().getCellCode());
    }

    /**
     * 修改存储单元信息
     *
     * @param po 存储单元信息
     *
     */
    @ApiOperation(value = "修改存储单元信息", tags = {"存储单元管理"})
    @PutMapping(path = "/master-data/cell", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicWhStorageCellSavePO po, BizContext ctx) {
        whStorageCellService.update(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_CELL_SAVE_SUCCESS, po.getDicWhStorageCellInfo().getCellCode());
    }

    /**
     * 删除存储单元信息
     *
     * @param id 主键id
     */
    @ApiOperation(value = "删除存储单元信息", tags = {"存储单元管理"})
    @DeleteMapping(path = "/master-data/cell/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        String cellCode = whStorageCellService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_CELL_DELETE_SUCCESS, cellCode);
    }

    @ApiOperation(value = "存储单元导入", notes = "存储单元导入", tags = {"存储单元管理"})
    @PostMapping(path = "/master-data/cell/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importCellImport(@RequestPart("file") MultipartFile file, BizContext ctx) {
        whStorageCellService.importCellImport(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "电子秤-生成采集任务", tags = {"存储单元管理"})
    @PostMapping(path = "/master-data/cell/gen-collection-task", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> genCollectionTask(@RequestBody DicWhStorageCellDTO po, BizContext ctx) {
        whStorageCellService.genCollectionTask(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_COLLECTION_TASK_GEN_SUCCESS);
    }

    @ApiOperation(value = "电子秤-推送电子秤水墨屏显示信息", tags = {"存储单元管理"})
    @PostMapping(path = "/master-data/cell/push-electric-info", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicWhStorageCellDTO>> pushElectricInfo(@RequestBody DicWhStorageCellSearchPO po, BizContext ctx) {
        whStorageCellService.pushElectricInfo(ctx);
        SingleResultVO<DicWhStorageCellDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "电子秤-推送设备状态", tags = {"存储单元管理"})
    @PostMapping(path = "/master-data/cell/push-electric-status", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<DicWhStorageCellPushVO>> pushElectricStatus(@RequestBody DicWhStorageCellPushPO po, BizContext ctx) {
        whStorageCellService.pushElectricStatus(ctx);
        List<DicWhStorageCellPushVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "电子秤-推送设备电量", tags = {"存储单元管理"})
    @PostMapping(path = "/master-data/cell/push-electric-quantity", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<DicWhStorageCellPushVO>> pushElectricQuantity(@RequestBody DicWhStorageCellPushPO po, BizContext ctx) {
        whStorageCellService.pushElectricQuantity(ctx);
        List<DicWhStorageCellPushVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}
