package com.inossem.wms.bizbasis.rfid.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizStockdocLabelDataDataWrap;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizStockdocLabelReceiptRelDataWrap;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumDeleteFlag;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.label.dto.BizStockdocLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizStockdocLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizStockdocLabelData;
import com.inossem.wms.common.model.label.entity.BizStockdocLabelReceiptRel;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 盘点凭证标签关联记录表
 */
@Service
public class LabelStockdocReceiptRelService {

    @Autowired
    protected BizStockdocLabelReceiptRelDataWrap bizLabelReceiptRelDataWrap;
    @Autowired
    protected BizStockdocLabelDataDataWrap bizLabelDataDataWrap;
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 根据状态,单据类型,单据itemIdList查询RFID标签与各单据的关联信息并分组
     */
    public Map<String, List<BizStockdocLabelDataDTO>> getLabelReceiptRefBatch(Integer status, Integer receiptType, List<Long> itemIdList) {
        // 根据状态,单据类型,单据itemIdList查询码盘关联信息
        QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(null != status, BizStockdocLabelReceiptRel::getStatus, status)
                .eq(null != receiptType, BizStockdocLabelReceiptRel::getReceiptType, receiptType).in(BizStockdocLabelReceiptRel::getReceiptItemId, itemIdList);
        List<BizStockdocLabelReceiptRel> bizLabelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        // 根据labelid填充code
        List<BizStockdocLabelReceiptRelDTO> labelReceiptRelDTOList = UtilCollection.toList(bizLabelReceiptRelList, BizStockdocLabelReceiptRelDTO.class);
//        dataFillService.fillAttr(labelReceiptRelDTOList);
        // 转换返回labelData对象
        List<BizStockdocLabelDataDTO> labelDataDTList = UtilCollection.toList(labelReceiptRelDTOList, BizStockdocLabelDataDTO.class);
        dataFillService.fillAttr(labelDataDTList);
        Map<String, List<BizStockdocLabelDataDTO>> resultMap = new HashMap<>(labelReceiptRelDTOList.size());
        if (labelDataDTList.size() > 0) {
            resultMap = labelDataDTList.stream().collect(Collectors.groupingBy(vo -> vo.getReceiptHeadId().toString().concat("-")
                    .concat(vo.getReceiptItemId().toString().concat("-").concat(vo.getReceiptType().toString()))));
        }
        return resultMap;
    }

    /**
     * 根据条件获取RFID标签相关信息(DTO)
     *
     * @param headIdList      单据id/码盘id列表
     * @param labelIdList     标签id列表
     * @param gtReceiptStatus 大于等于状态
     * @param labelReceiptRel 要查询的关联单据信息
     */
    public List<BizStockdocLabelReceiptRelDTO> getDTOList(List<Long> headIdList, List<Long> labelIdList, EnumReceiptStatus gtReceiptStatus,
                                                  BizStockdocLabelReceiptRel labelReceiptRel) {
        List<BizStockdocLabelReceiptRel> labelReceiptRelList = this.getList(headIdList, labelIdList, gtReceiptStatus, labelReceiptRel);
        // 根据labelid填充code
        List<BizStockdocLabelReceiptRelDTO> labelReceiptRelDTOList = UtilCollection.toList(labelReceiptRelList, BizStockdocLabelReceiptRelDTO.class);
        dataFillService.fillAttr(labelReceiptRelDTOList);
        return labelReceiptRelDTOList;
    }

    /**
     * 更新标签关联关系
     */
    public void updateBatchList(List<BizStockdocLabelReceiptRel> bizLabelReceiptRelList) {
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            bizLabelReceiptRelDataWrap.updateBatchById(bizLabelReceiptRelList);
        }
    }


    /**
     * 根据条件获取RFID标签相关信息(Entity)
     *
     * @param headIdList      单据id/码盘id列表
     * @param labelIdList     标签id列表
     * @param gtReceiptStatus 大于等于状态
     * @param labelReceiptRel 要查询的关联单据信息
     */
    public List<BizStockdocLabelReceiptRel> getList(List<Long> headIdList, List<Long> labelIdList, EnumReceiptStatus gtReceiptStatus,
                                            BizStockdocLabelReceiptRel labelReceiptRel) {
        QueryWrapper<BizStockdocLabelReceiptRel> queryWrapper = new QueryWrapper<>();
        if (headIdList != null) {
            // 单据id/码盘id
            queryWrapper.lambda().in(BizStockdocLabelReceiptRel::getReceiptHeadId, headIdList);
        }
        if (labelIdList != null) {
            // 标签id列表
            queryWrapper.lambda().in(BizStockdocLabelReceiptRel::getLabelId, labelIdList);
        }
        if (gtReceiptStatus != null) {
            // 大于等于状态
            queryWrapper.lambda().gt(BizStockdocLabelReceiptRel::getStatus, gtReceiptStatus.getValue());
        }
        if (labelReceiptRel != null && labelReceiptRel.getStatus() != null) {
            // 等于状态
            queryWrapper.lambda().eq(BizStockdocLabelReceiptRel::getStatus, labelReceiptRel.getStatus());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptType() != null) {
            // 单据类型
            queryWrapper.lambda().eq(BizStockdocLabelReceiptRel::getReceiptType, labelReceiptRel.getReceiptType());
        }
        if (labelReceiptRel != null && labelReceiptRel.getPreReceiptHeadId() != null) {
            // 前置单据id
            queryWrapper.lambda().eq(BizStockdocLabelReceiptRel::getPreReceiptHeadId, labelReceiptRel.getPreReceiptHeadId());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptHeadId() != null) {
            // 单据head表id
            queryWrapper.lambda().eq(BizStockdocLabelReceiptRel::getReceiptHeadId, labelReceiptRel.getReceiptHeadId());
        }
        return bizLabelReceiptRelDataWrap.list(queryWrapper);
    }

    /**
     * 批量保存RFID标签与各单据之间的关联信息
     */
    public void saveBatch(List<BizStockdocLabelReceiptRel> bizLabelReceiptRelList) {

    	if(UtilCollection.isEmpty(bizLabelReceiptRelList)){
    		return;
		}

		// 填充默认值
    	for(BizStockdocLabelReceiptRel bizLabelReceiptRel : bizLabelReceiptRelList){
			bizLabelReceiptRel.setStatus(bizLabelReceiptRel.getStatus() == null ? EnumDbDefaultValueInteger.BIZ_LABEL_RECEIPT_REL_STATUS.getValue() : bizLabelReceiptRel.getStatus());
		}

        bizLabelReceiptRelDataWrap.saveBatch(bizLabelReceiptRelList);
    }

    /**
     * 复制标签关联关系
     * @param labelDataList
     */
    public void copyRel(List<BizStockdocLabelData> labelDataList){

        if(UtilCollection.isNotEmpty(labelDataList)){
            return;
        }
        List<Long> sourceLabelList = labelDataList.stream().map(e->e.getSourceLabelId()).collect(Collectors.toList());
        QueryWrapper<BizStockdocLabelReceiptRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BizStockdocLabelReceiptRel::getLabelId, sourceLabelList);
        List<BizStockdocLabelReceiptRel> soureRelList = bizLabelReceiptRelDataWrap.list(queryWrapper);
        if(UtilCollection.isNotEmpty(sourceLabelList)){
            Map<Long, List<BizStockdocLabelReceiptRel>> sourceRelMap = soureRelList.stream().collect(Collectors.groupingBy(e->e.getLabelId()));
            List<BizStockdocLabelReceiptRel> newList = new ArrayList<>();

            for(BizStockdocLabelData labelData : labelDataList){
                List<BizStockdocLabelReceiptRel> sourceList = sourceRelMap.get(labelData.getSourceLabelId());
                if(UtilCollection.isNotEmpty(sourceList)){
                    List<BizStockdocLabelReceiptRel> innerList = UtilCollection.toList(sourceList,BizStockdocLabelReceiptRel.class);
                    innerList.forEach(e->{
                        e.setId(null);
                        e.setLabelId(labelData.getId());
                    });
                    newList.addAll(innerList);
                }
            }
            if(UtilCollection.isNotEmpty(newList)){
                bizLabelReceiptRelDataWrap.saveBatchOptimize(newList, 100);
            }
        }

    }

    /**
     * 更新RFID标签与各单据关联表状态
     *
     * @param idList          主键id列表
     * @param labelIdList     标签id列表
     * @param toStatus        修改后状态
     * @param labelReceiptRel 要修改的对象
     */
    public void updateStatus(List<Long> idList, List<Long> labelIdList, EnumReceiptStatus toStatus, BizStockdocLabelReceiptRel labelReceiptRel) {
        UpdateWrapper<BizStockdocLabelReceiptRel> updateWrapper = new UpdateWrapper<>();
        // 更新状态
        updateWrapper.lambda().set(BizStockdocLabelReceiptRel::getStatus, toStatus.getValue());
        if (idList != null) {
            // 主键id列表
            updateWrapper.lambda().in(BizStockdocLabelReceiptRel::getId, idList);
        }
        if (labelIdList != null) {
            // 标签id列表
            updateWrapper.lambda().in(BizStockdocLabelReceiptRel::getLabelId, labelIdList);
        }
        if (labelReceiptRel != null && labelReceiptRel.getStatus() != null) {
            // 现在状态
            updateWrapper.lambda().eq(BizStockdocLabelReceiptRel::getStatus, labelReceiptRel.getStatus());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptType() != null) {
            // 单据类型
            updateWrapper.lambda().eq(BizStockdocLabelReceiptRel::getReceiptType, labelReceiptRel.getReceiptType());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptHeadId() != null) {
            // 关联单据head表id
            updateWrapper.lambda().in(BizStockdocLabelReceiptRel::getReceiptHeadId, labelReceiptRel.getReceiptHeadId());
        }

        bizLabelReceiptRelDataWrap.update(updateWrapper);
    }

    /**
     * 删除标签数据及标签关联数据
     *
     * @param receiptHeadId     单据head主键
     */
    public void multiRemoveLabel(Long receiptHeadId) {
        // 根据标签主键逻辑删除关联数据
        QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();

        wrapper.lambda()
                // 单据head主键
                .eq(UtilNumber.isNotEmpty(receiptHeadId), BizStockdocLabelReceiptRel::getReceiptHeadId, receiptHeadId);
        // 获取标签关联数据
        List<BizStockdocLabelReceiptRel> LabelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        if (UtilCollection.isNotEmpty(LabelReceiptRelList)) {
            // 转DTO
            List<BizStockdocLabelReceiptRelDTO> labelReceiptRelDTOList = UtilCollection.toList(LabelReceiptRelList, BizStockdocLabelReceiptRelDTO.class);

            /* **** 删除标签数据 **** */
            bizLabelDataDataWrap.multiPhysicalDeleteByIdList(labelReceiptRelDTOList.stream().map(BizStockdocLabelReceiptRelDTO::getLabelId).collect(Collectors.toList()));

            /* **** 删除标签关联数据 **** */
            bizLabelReceiptRelDataWrap.multiPhysicalDeleteByIdList(labelReceiptRelDTOList.stream().map(BizStockdocLabelReceiptRelDTO::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 删除RFID标签与各单据关联信息
     *
     * @param labelRelIdList 标签与各单据关联表主键
     */
    public void removeByIds(List<Long> labelRelIdList) {
        bizLabelReceiptRelDataWrap.removeByIds(labelRelIdList);
    }

    /**
     * 查询RFID标签关联结果
     *
     * @param receiptItemId 单据id/码盘id列表
     * @param status        状态
     * @param receiptType   单据类型
     * @return 结果行数
     */
    public List<BizStockdocLabelReceiptRelDTO> getLabelReceiptRel(Long receiptItemId, Integer status, Integer receiptType) {
        QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                // 状态
                .ge(null != status, BizStockdocLabelReceiptRel::getStatus, status)
                // 单据类型
                .eq(null != receiptType, BizStockdocLabelReceiptRel::getReceiptType, receiptType)
                // 单据id/码盘id列表
                .in(BizStockdocLabelReceiptRel::getReceiptItemId, receiptItemId);

        List<BizStockdocLabelReceiptRel> labelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);

        return UtilCollection.toList(labelReceiptRelList, BizStockdocLabelReceiptRelDTO.class);
    }

    /**
     * 批量删除RFID标签关联数据
     *
     * @param labelIds 标签主键
     */
    public void multiDeleteLabelReceiptRel(List<Long> labelIds) {
        if (UtilCollection.isNotEmpty(labelIds)) {
            // 根据标签主键逻辑删除关联数据
            QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();

            wrapper.lambda().in(BizStockdocLabelReceiptRel::getLabelId, labelIds);
            bizLabelReceiptRelDataWrap.remove(wrapper);
        }
    }

    /**
     * 物理删除RFID标签关联数据
     *
     * @param receiptHeadId 单据id
     */
    public void physicalDeleteLabelReceiptRelByHeadId(Long receiptHeadId) {
        // 根据标签主键逻辑删除关联数据
        QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();

        wrapper.lambda().in(BizStockdocLabelReceiptRel::getReceiptHeadId, receiptHeadId);
        bizLabelReceiptRelDataWrap.physicalDelete(wrapper);
    }

    /**
     * 物理删除RFID标签关联数据
     *
     * @param receiptHeadId 单据id
     */
    public void physicalDeleteLabelReceiptRelByHeadIdAndStatus(Long receiptHeadId, Integer status) {
        // 根据标签主键逻辑删除关联数据
        QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(BizStockdocLabelReceiptRel::getReceiptHeadId, receiptHeadId)
                .eq(Objects.nonNull(status), BizStockdocLabelReceiptRel::getStatus, status);
        bizLabelReceiptRelDataWrap.physicalDelete(wrapper);
    }

    /**
     * 查询单据的关联信息
     *
     * @param receiptHeadId
     * @return
     */
    public List<BizStockdocLabelReceiptRel> findByReceiptItemId(Long receiptHeadId, Long itemId) {
        QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizStockdocLabelReceiptRel::getReceiptHeadId, receiptHeadId).eq(BizStockdocLabelReceiptRel::getReceiptItemId, itemId);
        List<BizStockdocLabelReceiptRel> labelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        return labelReceiptRelList;
    }

    /**
     * 查询标签的关联信息
     *
     * @param labelIdList
     * @return
     */
    public List<BizStockdocLabelReceiptRel> findByLabelIds(List<Long> labelIdList) {
        QueryWrapper<BizStockdocLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(BizStockdocLabelReceiptRel::getLabelId, labelIdList);
        List<BizStockdocLabelReceiptRel> labelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        return labelReceiptRelList;
    }

    /**
     * 撤销关联关系
     * @param idList
     */
    public void rollbackByIdList(List<Long> idList) {
        bizLabelReceiptRelDataWrap.deleteByIdList(idList, EnumDeleteFlag.ROLLBACK.getIntValue());
    }
}
