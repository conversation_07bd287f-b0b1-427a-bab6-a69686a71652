package com.inossem.wms.bizbasis.masterdata.user.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.entity.SysUserImg;
import com.inossem.wms.common.model.auth.user.po.SysUserSearchPO;
import com.inossem.wms.common.model.auth.user.vo.SysUserPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.dao.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Slf4j
@Service
public class SysUserDataWrap extends BaseDataWrap<SysUserMapper, SysUser> {

    /**
     * 未绑定该角色的用户
     *
     * @param condition 查询条件
     * @param roleId 角色ID
     * @return SysUser
     * @date 2021/3/3 10:32
     * <AUTHOR>
     */
    public List<SysUser> getSysUserListDidNotBindThisRole(String condition, Long roleId) {
        return baseMapper.getSysUserListDidNotBindThisRole(condition, roleId);
    }

    /**
     * 根據用户id获取 用户菜单权限id列表
     *
     * @param userId 用户ID
     * @return Long
     * @date 2021/3/3 10:30
     * <AUTHOR>
     */
    public List<Long> getUserResourceIdList(Long userId) {
        return baseMapper.getUserResourceIdList(userId);
    }

    /**
     * 用户列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public List<SysUserPageVO> getSysUserPageVOList(IPage<SysUserPageVO> page, QueryWrapper<SysUserSearchPO> wrapper) {
        return this.baseMapper.selectSysUserPageVOList(page, wrapper);
    }

    public SysUserImg findImg(Long imgId) {
        return this.baseMapper.findImg(imgId);
    }

    /**
     * 根據用户id，用户角色获取 用户菜单权限Code列表
     */
    public List<String> getUserResourceCode(Long userId, Set<String> roleCodes) {
        return baseMapper.getUserResourceCodeList(userId,roleCodes);
    }

    /**
     * 根据角色编码查询对应用户列表
     */
    public List<SysUser> getUserByRoleCode(String roleCode) {
        return baseMapper.getUserByRoleCode(roleCode);
    }

}
