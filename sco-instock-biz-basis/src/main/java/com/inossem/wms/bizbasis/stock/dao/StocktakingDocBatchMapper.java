package com.inossem.wms.bizbasis.stock.dao;

import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleLifetimeDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleMaintainDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.stock.dto.StockBatchDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockBatch;
import com.inossem.wms.common.model.stock.entity.StocktakingDocBatch;
import com.inossem.wms.common.model.stock.key.StockBatchKey;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 批次库存表 Mapper 接口
 */
@Mapper
public interface StocktakingDocBatchMapper extends WmsBaseMapper<StocktakingDocBatch> {

    /**
     * 根据ins凭证查询所有相关批次库存
     */
    List<StockBatchDTO> selectNegativeStockBatchAfterModifyStockBatch(String insDocCode);

    /**
     * 根据唯一索引批量查询所有相关批次库存
     */
    List<StockBatchDTO> selectStockBatchByStockBatchKeyList(@Param("list") List<StockBatchKey> list);

    /**
     * type=1 根据工厂+物料查询库存 type=2 根据工厂+物料+库存地点查询库存
     */
    List<StockBatchDTO> selectStockBatchByPoListAndType(@Param("list") List<StockBinPO> stockBatchCoList, @Param("type") Byte type);

    /**
     * 查询库存通用方法
     */
    List<StockBatchDTO> selectStockBatchByStockBatchPo(StockBinPO stockBinPo);

    /**
     * 批量查询所有相关批次库存
     */
    List<StockBatchDTO> selectStockBatchByStockBatchPoList(@Param("list") List<StockBinPO> stockBatchPoList);

    /**
     * 根据特性查询库存
     */
    List<BizReceiptAssembleDTO> getStockBySpecFeature(BizReceiptAssembleRuleSearchPO po);

    /**
     * 根据特性工器具查询库存
     */
    List<BizReceiptAssembleDTO> getToolStockBySpecFeature(BizReceiptAssembleRuleSearchPO po);

    /**
     * 根据特性工器具查询库存（石岛湾）
     */
    List<BizReceiptAssembleDTO> getStockBySpecFeatureBySdw(BizReceiptAssembleRuleSearchPO po);

    /**
     * 根据唯一索引批量查询所有相关工器具批次库存
     */
    List<StockBatchDTO> selectToolStockBatchByStockBatchKeyList(@Param("list") List<StockBatchKey> list);

    /**
     * 根据batchId 修改 qty_freeze
     * @param batchId
     * @param freezeNum
     */
    void updateByBatchId(@Param("batchId") Long batchId,@Param("freezeNum") Long freezeNum);



    /**
     * 新增 领料批量查库存使用 涉及占用
     */
    List<StockBinDTO> getOutputStockByBatchList(@Param("list") List<StockBinPO> stockBatchPoList);


    /**
     * 新增 领料批量查库存使用 涉及占用
     */
    List<StockBinDTO> getPlotStockByBatchList(@Param("list") List<StockBinPO> stockBatchPoList);

    List<BizReceiptAssembleDTO> getStockBySpecFeatureBySdw4Unitized(BizReceiptAssembleRuleSearchPO po);

    List<BizReceiptAssembleLifetimeDTO> getStockByPkgTypeBySdw(BizReceiptAssembleRuleSearchPO po);

    List<BizReceiptAssembleMaintainDTO> getMaintainStockBySdw(BizReceiptAssembleRuleSearchPO po);

    List<BizReceiptAssembleDTO> getStockBySpecFeatureBySdwAll(BizReceiptAssembleRuleSearchPO po);
}
