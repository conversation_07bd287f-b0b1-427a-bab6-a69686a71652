package com.inossem.wms.bizbasis.masterdata.material.service.biz.component;

import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizReceiptTypeDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.masterdata.receipt.dto.BizReceiptTypeDTO;
import com.inossem.wms.common.model.masterdata.receipt.entity.BizReceiptType;
import com.inossem.wms.common.model.masterdata.receipt.po.BizReceiptTypeSavePO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/27 14:20
 */
@Service
@Slf4j
public class BizReceiptTypeComponent {

    @Autowired
    private BizReceiptTypeDataWrap bizReceiptTypeDataWrap;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    protected DataFillService dataFillService;



    /**
     *  获取单据类型对应存储区列表-详情
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long receiptTypeId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(receiptTypeId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取获取单据类型对应存储区列表-详情
        BizReceiptType bizReceiptType = bizReceiptTypeDataWrap.getById(receiptTypeId);
        // 转DTO
        BizReceiptTypeDTO bizReceiptTypeDTO = UtilBean.newInstance(bizReceiptType, BizReceiptTypeDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizReceiptTypeDTO);

        // 设置单据类型对应存储区列表详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, bizReceiptTypeDTO);
    }


    /**
     * 新增、更新 单据类型对应存储区数据
     */
    public void saveOrUpdate(BizContext ctx) {
        // 入参上下文 - 要保存的入库单
        BizReceiptTypeSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptType bizReceiptType = UtilBean.newInstance(po, BizReceiptType.class);
        String stockInputCode = po.getReceiptCode();
        if (!UtilNumber.isNotEmpty(po.getId())){
            stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.RECEIPT_STORAGE_SAVE.getValue());
        }
        bizReceiptType.setReceiptType(po.getReceiptType());
        bizReceiptType.setReceiptTypeName(po.getReceiptTypeI18n());
        bizReceiptType.setCreateTime(UtilDate.getNow());
        bizReceiptType.setModifyTime(UtilDate.getNow());
        bizReceiptType.setCreateUserId(user.getCreateUserId());
        bizReceiptType.setModifyUserId(user.getModifyUserId());
        bizReceiptTypeDataWrap.saveOrUpdate(bizReceiptType);
    }

    /**
     * 逻辑删除单据类型对应存储区主数据-批量
     * @param ctx
     */
    public void deleteLogical(List<BizReceiptTypeSavePO> poList,BizContext ctx) {
        List<Long> idList = poList.stream().map(BizReceiptTypeSavePO::getId).collect(Collectors.toList());
        bizReceiptTypeDataWrap.multiPhysicalDeleteByIdList(idList);
//        bizReceiptTypeDataWrap.removeByIds(idList);
    }


}
