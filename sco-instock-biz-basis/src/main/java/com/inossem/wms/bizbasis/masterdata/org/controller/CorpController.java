package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.org.corp.dto.DicCorpDTO;
import com.inossem.wms.common.model.org.corp.po.DicCorpSavePO;
import com.inossem.wms.common.model.org.corp.po.DicCorpSearchPO;
import com.inossem.wms.common.model.org.corp.vo.DicCorpPageVO;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.CorpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 公司主数据表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@RestController
@Api(tags = "公司管理")
public class CorpController {

    /**
     * 公司BizService
     */
    @Autowired
    protected CorpService corpService;

    /**
     * 获取公司列表
     *
     * @param po 查询条件参数
     * @return 公司列表集合
     */
    @ApiOperation(value = "获取公司列表", tags = {"公司管理"})
    @PostMapping(path = "/org/corp/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicCorpPageVO>> getPage(@RequestBody DicCorpSearchPO po, BizContext ctx) {
        return BaseResult.success(corpService.getPage(ctx));
    }

    /**
     * 查看公司详情
     * 
     * @param id 公司Id
     * @return 公司详情
     */
    @ApiOperation(value = "按照公司id查找公司", tags = {"公司管理"})
    @GetMapping(path = "/org/corp/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicCorpDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(corpService.get(ctx));
    }

    /**
     * 新增公司
     * 
     * @param po 公司入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增公司信息", notes = "对公司信息进行添加", tags = {"公司管理"})
    @PostMapping(path = "/org/corp", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicCorpSavePO po, BizContext ctx) {
        // 储存公司信息
        corpService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_CORP_SAVE_SUCCESS, po.getCorpInfo().getCorpCode());
    }

    /**
     * 修改公司
     *
     * @param po 公司入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改公司信息", notes = "对公司信息进行添加", tags = {"公司管理"})
    @PutMapping(path = "/org/corp", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicCorpSavePO po, BizContext ctx) {
        // 储存公司信息
        corpService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_CORP_SAVE_SUCCESS, po.getCorpInfo().getCorpCode());
    }

    /**
     * 删除公司方法
     * 
     * @param id 公司Id
     * @return 删除结果
     */
    @ApiOperation(value = "按照公司编码删除公司", notes = "逻辑删除，只有公司下面没有工厂才能删除", tags = {"公司管理"})
    @DeleteMapping(path = "/org/corp/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除公司信息
        String corpCode = corpService.remove(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_CORP_DELETE_SUCCESS, corpCode);
    }

    /**
     * 公司导入
     * @param file 公司excel
     * @param ctx 上下文对象
     * <AUTHOR>
     */
    @ApiOperation(value = "公司导入", notes = "公司导入", tags = {"公司管理"})
    @PostMapping(path = "/org/corp/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestPart("file") MultipartFile file, BizContext ctx) {
        // 导入公司信息
        corpService.importCorp(file, ctx);
        return BaseResult.success();
    }

}
