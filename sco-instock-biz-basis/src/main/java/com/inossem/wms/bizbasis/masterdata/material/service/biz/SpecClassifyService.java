package com.inossem.wms.bizbasis.masterdata.material.service.biz;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.spec.service.datawrap.BizSpecClassifyDataWrap;
import com.inossem.wms.bizbasis.spec.service.datawrap.BizSpecClassifyFeatureRelDataWrap;
import com.inossem.wms.bizbasis.spec.service.datawrap.BizSpecFeatureDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.spec.EnumSpecClassifyType;
import com.inossem.wms.common.enums.spec.EnumSpecFeatureType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.spec.SpecClassifyMapVo;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecClassifyDTO;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureDTO;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureValueDTO;
import com.inossem.wms.common.model.masterdata.spec.entity.BizSpecClassify;
import com.inossem.wms.common.model.masterdata.spec.entity.BizSpecClassifyFeatureRel;
import com.inossem.wms.common.model.masterdata.spec.entity.BizSpecFeature;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecClassifySavePO;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecClassifySearchPO;
import com.inossem.wms.common.model.masterdata.spec.vo.BizSpecClassifyPageVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 分类表 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Service
@Slf4j
public class SpecClassifyService {

    @Autowired
    protected BizSpecClassifyDataWrap bizSpecClassifyDataWrap;

    @Autowired
    protected BizSpecFeatureDataWrap bizSpecFeatureDataWrap;

    @Autowired
    protected BizSpecClassifyFeatureRelDataWrap bizSpecClassifyFeatureRelDataWrap;

    /**
     * 数据填充实现类
     */
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 查询分类列表
     *
     * @param ctx-po 查询条件
     * @return 分类列表
     *
     */
    public PageObjectVO<BizSpecClassifyPageVO> getPage(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        BizSpecClassifySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 设置查询条件 ******** */
        QueryWrapper<BizSpecClassifySearchPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getSpecClassifyCode()), BizSpecClassifySearchPO::getSpecClassifyCode, po.getSpecClassifyCode())
                .like(UtilString.isNotNullOrEmpty(po.getSpecClassifyName()), BizSpecClassifySearchPO::getSpecClassifyName, po.getSpecClassifyName());
        IPage<BizSpecClassifyPageVO> page = po.getPageObj(BizSpecClassifyPageVO.class);
        bizSpecClassifyDataWrap.getBizSpecClassifyPageVOList(page, wrapper);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 查询分类详情
     * 
     * @param ctx-id 主键id
     * @return 分类详情
     *
     */
    public SingleResultVO<BizSpecClassifyDTO> getInfo(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询分类详情 ******** */
        BizSpecClassify bizSpecClassify = bizSpecClassifyDataWrap.getById(id);
        /* ********* 分类数据泛型转换 ******** */
        BizSpecClassifyDTO bizSpecClassifyDto = UtilBean.newInstance(bizSpecClassify, BizSpecClassifyDTO.class);
        /* ********* 设置分类类型描述 ******** */
        // specClassifyDto.setSpecClassifyTypeName(EnumSpecClassifyType.getNameByValue(UtilObject.getStringOrEmpty(specClassifyDto.getSpecClassifyType())));
        /* ********* 设置查询分类特性中间表条件 ******** */
        QueryWrapper<BizSpecClassifyFeatureRel> relWrapper = new QueryWrapper<>();
        relWrapper.lambda().eq(BizSpecClassifyFeatureRel::getClassifyId, bizSpecClassifyDto.getId());
        /* ********* 查询分类特性中间表详情 ******** */
        List<BizSpecClassifyFeatureRel> bizSpecClassifyFeatureRelList = bizSpecClassifyFeatureRelDataWrap.list(relWrapper);
        /* ********* 查询特性详情 ******** */
        if(!CollectionUtils.isEmpty(bizSpecClassifyFeatureRelList)){
            QueryWrapper<BizSpecFeature> featureWrapper = new QueryWrapper<>();
            featureWrapper.lambda().in(BizSpecFeature::getId, bizSpecClassifyFeatureRelList.stream().map(rel -> rel.getFeatureId()).collect(Collectors.toList()));
            List<BizSpecFeature> bizSpecFeatureList = bizSpecFeatureDataWrap.list(featureWrapper);
            /* ********* 特性数据泛型转换 ******** */
            List<BizSpecFeatureDTO> bizSpecFeatureDTOList = UtilCollection.toList(bizSpecFeatureList, BizSpecFeatureDTO.class);
            /* ********* 设置特性数据类型描述 ******** */
            // specFeatureDTOList.stream().forEach(spec ->
            // spec.setSpecFeatureTypeName(EnumSpecFeatureType.getNameByValue(UtilObject.getStringOrEmpty(spec.getSpecFeatureType()))));
            /* ********* 特性值转换 ******** */
            for (BizSpecFeatureDTO bizSpecFeatureDto : bizSpecFeatureDTOList) {
                if (bizSpecFeatureDto.getSpecFeatureType().equals(EnumSpecFeatureType.FIELD_PULL.getValue())) {
                    bizSpecFeatureDto.setBizSpecFeatureValueDTOList(JSON.parseArray(bizSpecFeatureDto.getInfo(), BizSpecFeatureValueDTO.class));
                }
            }
            bizSpecClassifyDto.setBizSpecFeatureDTOList(bizSpecFeatureDTOList);
        }
        return new SingleResultVO<>(bizSpecClassifyDto);
    }

    /**
     * 新增或修改分类信息
     *
     * @param ctx-po 分类信息
     * @param ctx-cUser 当前登录人信息
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        BizSpecClassifySavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 入参非空效验 ******** */
        BizSpecClassifyDTO bizSpecClassifyDto = po.getBizSpecClassifyInfo();
        if (bizSpecClassifyDto == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 特性非空效验 ******** */
        if (UtilString.isNullOrEmpty(bizSpecClassifyDto.getSpecClassifyCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        /* ********* 特性描述非空效验 ******** */
        if (UtilString.isNullOrEmpty(bizSpecClassifyDto.getSpecClassifyName())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        /* ********* 分类类型非空效验 ******** */
        if (UtilNumber.isEmpty(bizSpecClassifyDto.getSpecClassifyType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        /* ********* 判断id是否为空 ******** */
        if (UtilNumber.isEmpty(bizSpecClassifyDto.getId())) {
            bizSpecClassifyDto.setCreateUserId(cUser.getId());
        }
        /* ********* id是否有对应分类信息效验 ******** */
        else if (null == bizSpecClassifyDataWrap.getById(bizSpecClassifyDto.getId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        bizSpecClassifyDto.setModifyUserId(cUser.getId());
        /* ********* 分类数据泛型转换 ******** */
        BizSpecClassify bizSpecClassify = UtilBean.newInstance(bizSpecClassifyDto, BizSpecClassify.class);
        /* ********* 新增或修改分类信息 ******** */
        bizSpecClassifyDataWrap.saveOrUpdate(bizSpecClassify);
        /* ********* 删除分类特性中间表信息 ******** */
        bizSpecClassifyFeatureRelDataWrap.deleteByClassifyId(bizSpecClassifyDto.getId());
        /* ********* 设置新增特性中间表信息 ******** */
        List<BizSpecClassifyFeatureRel> bizSpecClassifyFeatureRelList = bizSpecClassifyDto.getBizSpecFeatureDTOList().stream().map(spec -> {
            BizSpecClassifyFeatureRel bizSpecClassifyFeatureRel = new BizSpecClassifyFeatureRel();
            bizSpecClassifyFeatureRel.setClassifyId(bizSpecClassifyDto.getId());
            bizSpecClassifyFeatureRel.setFeatureId(spec.getId());
            bizSpecClassifyFeatureRel.setCreateUserId(cUser.getId());
            bizSpecClassifyFeatureRel.setModifyUserId(cUser.getId());
            bizSpecClassifyFeatureRel.setIsDelete(0);
            return bizSpecClassifyFeatureRel;
        }).collect(Collectors.toList());
        /* ********* 批量新增特性中间表信息 ******** */
        bizSpecClassifyFeatureRelDataWrap.saveBatch(bizSpecClassifyFeatureRelList);
    }

    /**
     * 删除分类信息
     *
     * @param ctx-id 主键Id
     *
     */
    public String remove(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 设置分类Code ******** */
        String classifyCode = bizSpecClassifyDataWrap.getById(id).getSpecClassifyCode();
        /* ********* 删除分类信息 ******** */
        if(bizSpecClassifyDataWrap.removeById(id)){
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SPEC_CLASSIFY_DELETE_FAILURE, classifyCode);
        }
        /* ********* 删除分类特性中间表信息 ******** */
        bizSpecClassifyFeatureRelDataWrap.deleteByClassifyId(id);
        return classifyCode;
    }

    /**
     * 查询分类类型下拉
     *
     * @return 分类类型下拉框
     *
     */
    public MultiResultVO<SpecClassifyMapVo> getDown() {
        return new MultiResultVO<>(EnumSpecClassifyType.toList());
    }

    /**
     * 查询特性列表
     *
     * @return 特性列表
     *
     */
    public MultiResultVO<BizSpecFeatureDTO> getFeaturesList() {
        /* ********* 查询特性列表 ******** */
        List<BizSpecFeature> bizSpecFeatureList = bizSpecFeatureDataWrap.list();
        /* ********* 数据泛型转换 ******** */
        List<BizSpecFeatureDTO> bizSpecFeatureDTOList = UtilCollection.toList(bizSpecFeatureList, BizSpecFeatureDTO.class);
        /* ********* 设置特性数据类型描述 ******** */
        // specFeatureDTOList.stream().forEach(spec ->
        // spec.setSpecFeatureTypeName(EnumSpecFeatureType.getNameByValue(UtilObject.getStringOrEmpty(spec.getSpecFeatureType()))));
        /* ********* 数据填充 创建人 ******** */
        dataFillService.fillRlatAttrDataList(bizSpecFeatureDTOList);
        return new MultiResultVO<>(bizSpecFeatureDTOList);
    }
}
