package com.inossem.wms.bizbasis.stock.service.datawrap;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.stock.dao.StockBatchMapper;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleLifetimeDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleMaintainDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.stock.dto.StockBatchDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockBatch;
import com.inossem.wms.common.model.stock.key.StockBatchKey;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.util.UtilObject;

/**
 * 批次库存表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class StockBatchDataWrap extends BaseDataWrap<StockBatchMapper, StockBatch> {

    @Autowired
    private DataFillService dataFillService;

    /**
     * 根据ins凭证查询所有相关批次库存
     * 
     * @param insDocCode 凭证code
     * @return 批次库存列表
     */
    public List<StockBatchDTO> selectNegativeStockBatchAfterModifyStockBatch(String insDocCode) {
        List<StockBatchDTO> returnList = this.getBaseMapper().selectNegativeStockBatchAfterModifyStockBatch(insDocCode);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 根据唯一索引批量查询所有相关批次库存
     * 
     * @param list 唯一索引列表
     * @return 批次库存列表
     */
    public List<StockBatchDTO> selectStockBatchByStockBatchKeyList(List<StockBatchKey> list) {
        List<StockBatchDTO> returnList = this.getBaseMapper().selectStockBatchByStockBatchKeyList(list);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * type=1 根据工厂+物料查询库存 type=2 根据工厂+物料+库存地点查询库存
     * 
     * @param stockBatchPoList 批次库存po列表
     * @param type 类型
     * @return 批次库存列表
     */
    public List<StockBatchDTO> selectStockBatchByPoListAndType(List<StockBinPO> stockBatchPoList, Byte type) {
        List<StockBatchDTO> returnList = this.getBaseMapper().selectStockBatchByPoListAndType(stockBatchPoList, type);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 查询库存通用方法
     * 
     * @param stockBinPo 批次库存po
     * @return 批次库存列表
     */
    public List<StockBatchDTO> selectStockBatchByStockBatchPo(StockBinPO stockBinPo) {
        List<StockBatchDTO> returnList = this.getBaseMapper().selectStockBatchByStockBatchPo(stockBinPo);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 批量查询所有相关批次库存
     * 
     * @param stockBatchPoList 批次库存po列表
     * @return 批次库存列表
     */
    public List<StockBatchDTO> selectStockBatchByStockBatchPoList(List<StockBinPO> stockBatchPoList) {
        List<StockBatchDTO> returnList = this.getBaseMapper().selectStockBatchByStockBatchPoList(stockBatchPoList);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 批量查询所有相关批次库存
     *
     * @param stockBatchPoList 批次库存po列表
     * @return 批次库存列表
     */
    public List<StockBinDTO> getOutputStockByBatchList(List<StockBinPO> stockBatchPoList) {
        List<StockBinDTO> returnList = this.getBaseMapper().getOutputStockByBatchList(stockBatchPoList);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 批量查询所有相关批次库存
     *
     * @param stockBatchPoList 批次库存po列表
     * @return 批次库存列表
     */
    public List<StockBinDTO> getPlotStockByBatchList(List<StockBinPO> stockBatchPoList) {
        List<StockBinDTO> returnList = this.getBaseMapper().getPlotStockByBatchList(stockBatchPoList);
        dataFillService.fillAttr(returnList);
        return returnList;
    }



    /**
     * 根据特性查询库存
     *
     * @param po 仓位库存查询条件
     * @return 已有特性库存
     */
    public List<BizReceiptAssembleDTO> getStockBySpecFeature(BizReceiptAssembleRuleSearchPO po) {
        List<BizReceiptAssembleDTO> returnList = this.getBaseMapper().getStockBySpecFeature(po);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 根据特性查询工器具库存
     *
     * @param po 仓位库存查询条件
     * @return 已有特性库存
     */
    public List<BizReceiptAssembleDTO> getToolStockBySpecFeature(BizReceiptAssembleRuleSearchPO po) {
        List<BizReceiptAssembleDTO> returnList = this.getBaseMapper().getToolStockBySpecFeature(po);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 根据特性查询库存（石岛湾）
     *
     * @param po 仓位库存查询条件
     * @return 已有特性库存
     */
    public List<BizReceiptAssembleDTO> getStockBySpecFeatureBySdw(BizReceiptAssembleRuleSearchPO po) {

        // 处理查询条件中，物料描述的转义字符串问题
        String matName = po.getMatName();
        if (UtilObject.isNotEmpty(matName)) {
            // 模糊匹配时 "\"转义
            matName = matName.replaceAll("\\\\", "\\\\\\\\");
            // 模糊匹配时 "%"转义
            matName = matName.replaceAll("%", "\\\\%");
            po.setMatName(matName);
        }
        IPage<BizReceiptAssembleDTO> page = null;
        if (po.isPaging()) {
            page = po.getPageObj(BizReceiptAssembleDTO.class);
        }
        List<BizReceiptAssembleDTO> returnList = this.getBaseMapper().getStockBySpecFeatureBySdw(page, po);
        dataFillService.fillAttr(returnList);
        if (page != null) {
            po.setTotalCount((int) page.getTotal());
        }
        return returnList;
    }

    /**
     * 根据唯一索引批量查询所有相关工器具批次库存
     *
     * @param list 唯一索引列表
     * @return 批次库存列表
     */
    public List<StockBatchDTO> selectToolStockBatchByStockBatchKeyList(List<StockBatchKey> list) {
        List<StockBatchDTO> returnList = this.getBaseMapper().selectToolStockBatchByStockBatchKeyList(list);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    /**
     * 根据batchId 修改 qty_freeze
     * @param batchId
     * @param freezeNum
     */
    public void updateByBatchId(Long batchId, Long freezeNum) {
        this.baseMapper.updateByBatchId(batchId,freezeNum);
    }

    /**
     * 根据唯一索引批量查询所有相关批次库存的主键
     *
     * @param c 唯一索引集合
     * @return 批次库存Id列表
     * <AUTHOR> <<EMAIL>>
     */
    public List<Long> selectStockBatchIdByStockBatchKeys(Collection<StockBatchKey> c) {
        return this.getBaseMapper().selectStockBatchIdByStockBatchKeys(c);
    }

    /**
     * 根据主键查询批次库存数量，并加X锁
     *
     * @param c 主键集合
     * @return 批次库存列表
     * <AUTHOR>
     */
    public List<StockBatch> selectStockBatchQtyByIdForUpdate(Collection<Long> c) {
        return this.getBaseMapper().selectStockBatchQtyByIdForUpdate(c);
    }

    public List<BizReceiptAssembleDTO> getStockBySpecFeatureBySdw4Unitized(BizReceiptAssembleRuleSearchPO po) {
        List<BizReceiptAssembleDTO> returnList = this.getBaseMapper().getStockBySpecFeatureBySdw4Unitized(po);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    public List<BizReceiptAssembleLifetimeDTO> getStockByPkgTypeBySdw(BizReceiptAssembleRuleSearchPO po) {
        List<BizReceiptAssembleLifetimeDTO> returnList = this.getBaseMapper().getStockByPkgTypeBySdw(po);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    public List<BizReceiptAssembleMaintainDTO> getMaintainStockBySdw(BizReceiptAssembleRuleSearchPO po) {
        List<BizReceiptAssembleMaintainDTO> returnList = this.getBaseMapper().getMaintainStockBySdw(po);
        dataFillService.fillAttr(returnList);
        return returnList;
    }
    public List<BizReceiptAssembleMaintainDTO> getMaintainStockBySdwUnitized(BizReceiptAssembleRuleSearchPO po) {
        List<BizReceiptAssembleMaintainDTO> returnList = this.getBaseMapper().getMaintainStockBySdwUnitized(po);
        dataFillService.fillAttr(returnList);
        return returnList;
    }


    public List<BizReceiptAssembleDTO> getStockBySpecFeatureBySdwByBatchCode(BizReceiptAssembleRuleSearchPO po) {
        // 处理查询条件中，物料描述的转义字符串问题
        String matName = po.getMatName();
        if (UtilObject.isNotEmpty(matName)) {
            // 模糊匹配时 "\"转义
            matName = matName.replaceAll("\\\\", "\\\\\\\\");
            // 模糊匹配时 "%"转义
            matName = matName.replaceAll("%", "\\\\%");
            po.setMatName(matName);
        }

        List<BizReceiptAssembleDTO> returnList = this.getBaseMapper().getStockBySpecFeatureBySdwAll(po);
        dataFillService.fillAttr(returnList);
        return returnList;
    }

    public List<StockBatchDTO> getStockByPurchase(List<ErpPurchaseReceiptItemDTO> itemDTOList) {
        return this.getBaseMapper().getStockByPurchase(itemDTOList);
    }
}
