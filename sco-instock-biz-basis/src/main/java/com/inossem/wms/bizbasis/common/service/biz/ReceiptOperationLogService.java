package com.inossem.wms.bizbasis.common.service.biz;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.common.model.bizbasis.po.BizCommonReceiptOperationLogSearchPO;
import com.inossem.wms.common.model.bizbasis.vo.BizCommonReceiptOperationLogVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.datawrap.BizCommonReceiptOperationLogDataWrap;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptOperationLog;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;

/**
 * 单据日志服务类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReceiptOperationLogService {

    @Autowired
    protected BizCommonReceiptOperationLogDataWrap bizCommonReceiptOperationLogDataWrap;
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 获取单据日志记录
     *
     * @param receiptId 单据id
     * @param receiptType 单据类型
     * @return BizReceiptOperationLog
     *
     */
    public List<BizCommonReceiptOperationLogDTO> getBizReceiptOperationLogList(Long receiptId, Integer receiptType) {
        if (UtilNumber.isEmpty(receiptType)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
        }
        if (UtilNumber.isEmpty(receiptId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_CODE_CAN_NOT_BE_EMPTY);
        }

        QueryWrapper<BizCommonReceiptOperationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizCommonReceiptOperationLog::getReceiptHeadId, receiptId).eq(BizCommonReceiptOperationLog::getReceiptType,
            receiptType);
        List<BizCommonReceiptOperationLog> list = bizCommonReceiptOperationLogDataWrap.list(queryWrapper);
        List<BizCommonReceiptOperationLogDTO> listDto = UtilCollection.toList(list, BizCommonReceiptOperationLogDTO.class);
        dataFillService.fillRlatAttrDataList(listDto);

        return listDto;
    }

    /**
     * 保存单据日志记录
     * 
     * @param receiptId 单据id
     * @param receiptType 单据类型
     * @param receiptOperationType 状态枚举
     * @param logRemark 备注
     * @param userId 创建人
     *
     */
    public void saveBizReceiptOperationLogList(Long receiptId, Integer receiptType, EnumReceiptOperationType receiptOperationType, String logRemark,
        Long userId) {
        if (UtilNumber.isEmpty(receiptType)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
        }
        if (UtilNumber.isEmpty(receiptId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_CODE_CAN_NOT_BE_EMPTY);
        }

        BizCommonReceiptOperationLog receiptOperationLog = new BizCommonReceiptOperationLog();
        receiptOperationLog.setReceiptHeadId(receiptId);
        receiptOperationLog.setReceiptType(receiptType);
        receiptOperationLog.setReceiptOperationType(receiptOperationType.getValue());
        receiptOperationLog.setRemark(logRemark);
        receiptOperationLog.setCreateUserId(userId);
        receiptOperationLog.setModifyUserId(userId);
        bizCommonReceiptOperationLogDataWrap.save(receiptOperationLog);
        log.info("单据日志记录保存成功 : " + receiptId);
    }

    /**
     * 逻辑删除单据日志记录
     * 
     * @param receiptId 单据id
     * @param receiptType 单据类型
     *
     */
    public void deleteBizReceiptOperationLog(Long receiptId, Integer receiptType) {
        UpdateWrapper<BizCommonReceiptOperationLog> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(BizCommonReceiptOperationLog::getReceiptHeadId, receiptId).eq(BizCommonReceiptOperationLog::getReceiptType, receiptType);

        bizCommonReceiptOperationLogDataWrap.remove(wrapper);
        log.info("单据日志记录删除成功 : " + receiptId);
    }

    /**
     * 分页查询操作日志
     * @param po : 时间段参数
     */
    public PageObjectVO<BizCommonReceiptOperationLogVO> getLogsPage(BizCommonReceiptOperationLogSearchPO po) {
        Page<BizCommonReceiptOperationLog> page = new Page<>(po.getPageIndex(), po.getPageSize());
        QueryWrapper<BizCommonReceiptOperationLog> queryWrapper = new QueryWrapper<>();
        // 判断日期是否为空
        if (UtilObject.isNotEmpty(po.getCreateTimeStart()) && UtilObject.isNotEmpty(po.getCreateTimeEnd())) {
            Date startTime = UtilLocalDateTime.getStartTime(po.getCreateTimeStart());
            Date endTime = UtilLocalDateTime.getEndTime(po.getCreateTimeEnd());
            // 取日期范围内
            queryWrapper.lambda().between(BizCommonReceiptOperationLog::getCreateTime, startTime,endTime);
        }
        queryWrapper.lambda().orderByDesc(BizCommonReceiptOperationLog::getCreateTime);
        bizCommonReceiptOperationLogDataWrap.page(page, queryWrapper);
        if (UtilCollection.isEmpty(page.getRecords())) {
            return new PageObjectVO<>(null,0L);
        }
        List<BizCommonReceiptOperationLogVO> list = UtilCollection.toList(page.getRecords(), BizCommonReceiptOperationLogVO.class);
        for (BizCommonReceiptOperationLogVO bizCommonReceiptOperationLogVO : list) {
            bizCommonReceiptOperationLogVO.setPreReceiptType(bizCommonReceiptOperationLogVO.getReceiptType());
        }
        dataFillService.fillRlatAttrDataList(list);
        return new PageObjectVO<>(list, page.getTotal());
    }
}
