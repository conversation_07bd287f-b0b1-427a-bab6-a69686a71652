package com.inossem.wms.bizbasis.masterdata.material.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.material.dao.DicMaterialFactoryMapper;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFactory;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySearchPO;
import com.inossem.wms.common.model.masterdata.mat.fty.vo.DicMaterialFactoryPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 物料工厂数据表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Service
public class DicMaterialFactoryDataWrap extends BaseDataWrap<DicMaterialFactoryMapper, DicMaterialFactory> {

    /**
     * 物料工厂列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicMaterialFactoryPageVO> getDicMaterialFactoryPageVOList(IPage<DicMaterialFactoryPageVO> page,
        QueryWrapper<DicMaterialFactorySearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicMaterialFactoryPageVOList(page, wrapper));
    }

    public void updateUnitizedFlag(Integer unitizedFlag, Long ftyId, Long matId) {
        this.baseMapper.updateUnitizedFlag(unitizedFlag, ftyId, matId);
    }
}
