package com.inossem.wms.bizbasis.erp.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.sap.SapConst;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.util.UtilErp;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSpring;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;
import java.util.List;

/**
 * 调用sap冲销
 */
@Slf4j
public class SapInterfaceWriteOffUtil {
    /**
     * 获取请求报文  模拟接口  真实接口通用
     * @param postingItem
     * @return
     */
   public static JSONObject getRequestParams (String postingItem){
       JSONObject params = new JSONObject();
       // json转list
       List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
       JSONObject firstObject =jsonObjectList.get(0);
       if(UtilNumber.isNotEmpty(firstObject.getInteger("receiptType"))){
           Integer callReceiptType= firstObject.getInteger("receiptType");
           String  callReceiptCode=firstObject.getString("receiptCode");
           params.put("callerReceiptType", callReceiptType);
           params.put("callReceiptCode", callReceiptCode);
       }else{

       }
       // 定义入参
       BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
       // ********  I_IMPORT 接口通用输入参数 ********
       CurrentUser user = bizCommonService.getUser();
       String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
       JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), SapConst.TYPE_ONE);

       // ********  I_ITEM 行项目参数  ********
       JSONArray paramsOfItem = new JSONArray();
       // 验收入库 item表赋值
       if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
           if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue())) {
               jsonObjectList.forEach(item -> {
                   JSONObject sapItem = new JSONObject();
                   sapItem.put("MBLPO", item.getIntValue("matDocRid")); // 物料凭证行项目
                   sapItem.put("ZDJBH", item.getString("receiptCode")); // 单据号
                   sapItem.put("ZDJXM", item.getString("rid")); // 单据行项目号
                   sapItem.put("ZHXH", item.getString("rid")); // 单据配货号
                   paramsOfItem.add(sapItem);
               });
           }
       }
       // 成套设备-验收入库
       else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("inspectInputItem"))
               && jsonObjectList.get(0).getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())) {
           jsonObjectList.forEach(waybill -> {
               JSONObject sapItem = new JSONObject();
               sapItem.put("MBLPO", waybill.getIntValue("iMatDocRid")); // 物料凭证行项目
               sapItem.put("ZDJBH", waybill.getJSONObject("inspectInputItem").getString("receiptCode")); // 单据号
               sapItem.put("ZDJXM", waybill.getString("inspectInputItemRid")); // 单据行项目号
               sapItem.put("ZHXH", waybill.getString("bid")); // 单据配货号
               paramsOfItem.add(sapItem);
           });
       }
       // 采购退货/领料退库/退转库入库 bin表匹配
       if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
           if (jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                   || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                   || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())
                   || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                   || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                   || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
               jsonObjectList.forEach(item -> {
                   JSONArray jsonArray = new JSONArray();
                   if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                           || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                           || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                           || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                       jsonArray = item.getJSONArray("binDTOList");
                   }else if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                           || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())) {
                       jsonArray = item.getJSONArray("itemInfoList");
                   }
                   for(int i = 0; i < jsonArray.size(); i++) {
                       JSONObject jsonObject = jsonArray.getJSONObject(i);
                       JSONObject sapItem = new JSONObject();
                       sapItem.put("MBLPO", item.getIntValue("matDocRid")); // 物料凭证行项目
                       sapItem.put("ZDJBH", item.getString("receiptCode")); // 单据号
                       sapItem.put("ZDJXM", item.getString("rid")); // 单据行项目号
                       sapItem.put("ZHXH", jsonObject.getString("bid")); // 单据配货号
                       paramsOfItem.add(sapItem);
                   }
               });
           }
       }

       // 组装参数
       params.put("IS_IMPORT", paramsOfImport);
       params.put("IV_MBLNR", jsonObjectList.get(0).getString("matDocCode"));
       params.put("IV_MJAHR", jsonObjectList.get(0).getIntValue("matDocYear"));
       params.put("IV_DATE", jsonObjectList.get(0).getString("writeOffPostingDate"));
       // 成套设备-验收入库
       if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("inspectInputItem"))
               && jsonObjectList.get(0).getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())) {
           params.put("IV_MBLNR", jsonObjectList.get(0).getString("iMatDocCode"));
           params.put("IV_MJAHR", jsonObjectList.get(0).getIntValue("iMatDocYear"));
           params.put("IV_DATE", jsonObjectList.get(0).getString("iWriteOffPostingDate"));
       }
       params.put("IT_ITEM", paramsOfItem);
       return params;
   }
    /**
     * 解析返回结果
     * @param postingItem
     * @param erpReturnObj
     */
    public static void delRespondObject (String postingItem, ErpReturnObject erpReturnObj){
        String matDocCode = "matDocCode";
        int year = Calendar.getInstance().get(Calendar.YEAR);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);

        // 验收入库 item表赋值
        if(UtilNumber.isNotEmpty(firstObject.getInteger("receiptType"))){
            if(firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())) {
                jsonObjectList.forEach(e -> {
                    ErpReturnObjectItem item = new ErpReturnObjectItem();
                    item.setReceiptCode(e.getString("receiptCode"));
                    item.setReceiptRid(e.getString("rid"));
                    item.setReceiptBid(e.getString("rid"));
                    item.setMatDocCode("matDocCode");
                    item.setMatDocYear(Integer.toString(year));
                    item.setMatDocRid(Const.DOC_RID_PRE  + e.getString("rid"));
                    returnItemList.add(item);
                });
            }
        }
        // 成套设备-验收入库
        else if(UtilObject.isNotNull(firstObject.getJSONObject("inspectInputItem"))
                && firstObject.getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())) {
            jsonObjectList.forEach(e -> {
                ErpReturnObjectItem item = new ErpReturnObjectItem();
                item.setReceiptCode(e.getJSONObject("inspectInputItem").getString("receiptCode"));
                item.setReceiptRid(e.getString("inspectInputItemRid"));
                item.setReceiptBid(e.getString("bid"));
                item.setMatDocCode(matDocCode);
                item.setMatDocYear(Integer.toString(year));
                item.setMatDocRid(Const.DOC_RID_PRE  + e.getString("inspectInputItemRid"));
                returnItemList.add(item);
            });
        }
        // 采购退货/领料退库/退转库入库/领料出库/报废出库冲销 bin表匹配
        if(UtilNumber.isNotEmpty(firstObject.getInteger("receiptType"))){
            if (firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                    || firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                    || firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())
                    || firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())
                    || firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                    || firstObject.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())) {
                jsonObjectList.forEach(e -> {
                    JSONArray jsonArray = new JSONArray();
                    if(firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                            || firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())
                            || firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                            || firstObject.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())) {
                        jsonArray = e.getJSONArray("binDTOList");
                    }else if(firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                            || firstObject.getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())) {
                        jsonArray = e.getJSONArray("itemInfoList");
                    }
                    for(int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        ErpReturnObjectItem item = new ErpReturnObjectItem();
                        item.setReceiptCode(e.getString("receiptCode"));
                        item.setReceiptRid(e.getString("rid"));
                        item.setReceiptBid(jsonObject.getString("bid"));
                        item.setMatDocCode(matDocCode);
                        item.setMatDocYear(Integer.toString(year));
                        item.setMatDocRid(Const.DOC_RID_PRE  + e.getString("rid"));
                        returnItemList.add(item);
                    }
                });
            }
        }
        erpReturnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        erpReturnObj.setReturnItemList(returnItemList);
        erpReturnObj.setMatDocCode(matDocCode);
        erpReturnObj.setMatDocYear(year);
    }
}
