package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.bizbasis.masterdata.org.service.biz.OfficeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptOfficeDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 科室主数据Controller
 *
 * <AUTHOR>
 * @date 2022/05/07 10:08
 **/
@RestController
@Api(tags = "主数据-科室管理")
public class OfficeController {

    @Autowired
    private OfficeService officeService;

    /**
     * 科室主数据-查询
     *
     * @param po  保存工器具入库表单参数
     * @param ctx 入参上下文 {"po":"保存工器具入库表单参数"}
     */
    @ApiOperation(value = "科室管理-查询", tags = {"主数据管理-科室管理"})
    @PostMapping(value = "/office-master-data/office/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicDeptOfficeDTO>> getResult(@RequestBody MetaDataDeptOfficePO po, BizContext ctx) {
        officeService.getResult(ctx);
        PageObjectVO<DicDeptOfficeDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 科室主数据-新增
     *
     * @param po  MetaDataDeptOfficePO
     * @param ctx 入参上下文 {"po":"MetaDataDeptOfficePO"}
     */
    @ApiOperation(value = "科室管理-提交创建", tags = {"主数据管理-科室管理"})
    @PostMapping(value = "/office-master-data/office", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody MetaDataDeptOfficePO po, BizContext ctx) {
        officeService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DEPT_OFFICE_SAVE_SUCCESS, po.getDeptOfficeCode());
    }

    /**
     * 科室主数据-详情
     *
     * @param id 公司Id
     * @return 公司详情
     */
    @ApiOperation(value = "按照科室id查找科室", tags = {"科室主数据管理"})
    @GetMapping(path = "/office-master-data/office/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicDeptOfficeDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(officeService.get(ctx));
    }

    /**
     * 修改科室
     *
     * @param po 仓位入参类
     * @return 处理结果
     * <AUTHOR>
     */
    @ApiOperation(value = "科室管理-修改科室", notes = "对科室信息进行修改", tags = {"主数据管理-科室管理"})
    @PutMapping(path = "/office-master-data/office", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody MetaDataDeptOfficePO po, BizContext ctx) {
        officeService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DEPT_OFFICE_SAVE_SUCCESS, po.getDeptOfficeCode());
    }


}
