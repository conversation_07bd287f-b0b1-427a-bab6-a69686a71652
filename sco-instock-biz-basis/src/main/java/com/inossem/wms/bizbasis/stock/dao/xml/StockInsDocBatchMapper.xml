<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.stock.dao.StockInsDocBatchMapper">

    <!-- 根据物料凭证号修改库存 -->
    <update id="modifyStockBatch">
        INSERT INTO stock_batch (
        mat_id,
        batch_id,
        fty_id,
        location_id,
        <!-- 六库存 -->
        qty,
        qty_transfer,
        qty_inspection,
        qty_freeze,
        qty_haste,
        qty_temp
        ) SELECT
        sidb.mat_id,
        sidb.batch_id,
        sidb.fty_id,
        sidb.location_id,
        <!-- 六库存类型行转列 -->
        SUM(CASE sidb.stock_status WHEN 10 THEN CASE sidb.debit_credit WHEN 's' THEN 1 ELSE - 1 END * sidb.actual_qty ELSE 0 END),
        SUM(CASE sidb.stock_status WHEN 20 THEN CASE sidb.debit_credit WHEN 's' THEN 1 ELSE - 1 END * sidb.actual_qty ELSE 0 END),
        SUM(CASE sidb.stock_status WHEN 30 THEN CASE sidb.debit_credit WHEN 's' THEN 1 ELSE - 1 END * sidb.actual_qty ELSE 0 END),
        SUM(CASE sidb.stock_status WHEN 40 THEN CASE sidb.debit_credit WHEN 's' THEN 1 ELSE - 1 END * sidb.actual_qty ELSE 0 END),
        SUM(CASE sidb.stock_status WHEN 50 THEN CASE sidb.debit_credit WHEN 's' THEN 1 ELSE - 1 END * sidb.actual_qty ELSE 0 END),
        SUM(CASE sidb.stock_status WHEN 60 THEN CASE sidb.debit_credit WHEN 's' THEN 1 ELSE - 1 END * sidb.actual_qty ELSE 0 END)
        FROM
        stock_ins_doc_batch sidb
        WHERE
        sidb.ins_doc_code = #{insDocCode}
        GROUP BY
        sidb.mat_id,
        sidb.batch_id,
        sidb.fty_id,
        sidb.location_id
        ON DUPLICATE KEY UPDATE
        qty = qty + VALUES(qty),
        qty_transfer = qty_transfer + VALUES(qty_transfer),
        qty_inspection = qty_inspection + VALUES(qty_inspection),
        qty_freeze = qty_freeze + VALUES(qty_freeze),
        qty_haste = qty_haste + VALUES(qty_haste),
        qty_temp = qty_temp + VALUES(qty_temp);
    </update>

    <!-- 删除所有库存数量都为0的数据 -->
    <delete id="deleteStockBatch">
        DELETE b
        FROM (
                 SELECT mat_id,
                        batch_id,
                        fty_id,
                        location_id
                 FROM stock_ins_doc_batch
                 WHERE ins_doc_code = #{insDocCode}
                 GROUP BY mat_id,
                          batch_id,
                          fty_id,
                          location_id
             ) a
                 INNER JOIN stock_batch b ON a.mat_id = b.mat_id
            AND a.fty_id = b.fty_id
            AND a.location_id = b.location_id
            AND b.qty = 0
            AND b.qty_transfer = 0
            AND b.qty_inspection = 0
            AND b.qty_freeze = 0
            AND b.qty_haste = 0
            AND b.qty_temp = 0
                 INNER JOIN biz_batch_info bm ON bm.id = b.batch_id
    </delete>

</mapper>
