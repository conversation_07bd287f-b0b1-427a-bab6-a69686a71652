package com.inossem.wms.bizbasis.erp.service.biz;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicUnitDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptHeadDTO;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.org.corp.entity.DicCorp;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * ERP采购订单查询接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Service
@Slf4j
public class PurchaseReceiptService {

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    private ErpPurchaseReceiptHeadDataWrap erpPurchaseReceiptHeadDataWrap;
    @Autowired
    private ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;
    @Autowired
    private SapInterfaceService sapInterfaceService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private DicUnitDataWrap dicUnitDataWrap;
    @Autowired
    private EditCacheService editCacheService;

    /**
     * 获取采购订单
     *
     * @param po   采购验收查询表单
     * @param user 操作用户
     * @return 采购订单数据
     */
    public List<ErpPurchaseReceiptItemDTO> getErpPurchaseReceiptItemList(BizReceiptPreSearchPO po, CurrentUser user) {
        // 装载SAP查询结果
        List<ErpPurchaseReceiptItemDTO> resultList = new ArrayList<>();
        // if (UtilConst.getInstance().isErpSyncMode()) {
        //     // 调用SAP
        //     this.erpPurchaseReceiptSynBySap(resultList, po, user);
        //     if (UtilCollection.isEmpty(resultList)) {
        //         return resultList;
        //     }
        // }
        // 装载数据库查询结果
        List<ErpPurchaseReceiptItemDTO> receiptItemDTOList = new ArrayList<>();
        this.getErpPurchaseReceipt(receiptItemDTOList, po);
        return receiptItemDTOList;
    }

    /**
     * 调用sap查询采购订单
     *
     * @param po         查询条件
     * @param resultList 采购订单行项目信息
     */
    private void erpPurchaseReceiptSynBySap(List<ErpPurchaseReceiptItemDTO> resultList, BizReceiptPreSearchPO po, CurrentUser user) {
        // 调用(采购订单同步)的方法, 返回值 ERP对象
        JSONObject returnObj = sapInterfaceService.getPurchaseReceiptInfo(po, user);
        if (UtilObject.isNull(returnObj)) {
            log.error("SAP采购订单API返回对象为空:{}", returnObj);
            return;
        }
        if (!returnObj.getJSONObject("I_RETURN").get("CODE").equals("S")) {
            log.info("SAP获取采购订单信息异常:{}", returnObj);
            return;
        }
        // 行项目信息
        JSONArray erpPurchaseReceiptItemArray = returnObj.getJSONArray("T_EKPO");
        // 抬头信息
        JSONArray erpPurchaseReceiptHeadArray = returnObj.getJSONArray("T_EKKO");
        // 采购凭证中的账户设置
        JSONArray erpPurchaseReceiptPurchDocArray = returnObj.getJSONArray("T_EKKN");
        // 解析SAP抬头信息
        List<ErpPurchaseReceiptHeadDTO> purchaseReceiptHeadList = this.getErpPurchaseReceiptHeadByJSONArray(erpPurchaseReceiptHeadArray);
        //解析SAP采购订单行项目
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemList = this.getErpPurchaseReceiptItemByJSONArray(erpPurchaseReceiptItemArray);
        // 解析SAP采购订单中的账户设置
        List<Map<String, String>> purchaseReceiptPurchDocList = this.getErpPurchaseReceiptPurchDocByJSONArray(erpPurchaseReceiptPurchDocArray);
        List<Long> headIdList = null;
        if (UtilCollection.isNotEmpty(purchaseReceiptHeadList)) {
            // 处理解析行项目和头信息 进行退货标识属性填充
            purchaseReceiptItemList.forEach(erpPurchaseReceiptItemDTO -> {
                purchaseReceiptHeadList.forEach(erpPurchaseReceiptHeadDTO -> {
                    if (Objects.equals(erpPurchaseReceiptHeadDTO.getReceiptCode(), erpPurchaseReceiptItemDTO.getReceiptCode())) {
                        // 填充退货标识
                        erpPurchaseReceiptHeadDTO.setIsReturnFlag(erpPurchaseReceiptItemDTO.getIsReturnFlag());
                    }
                });
            });
            // 保存erp采购订单头信息
            headIdList = savaOrUpdateErpPurchaseReceiptHead(purchaseReceiptHeadList);
        }
        // 判断SAP行项目不为空
        if (UtilCollection.isNotEmpty(purchaseReceiptItemList)) {
            List<ErpPurchaseReceiptItemDTO> finalPurchaseReceiptItemList = purchaseReceiptItemList;
            // 当SAP账户设置不为空的时候进行WBS编码属性填充到行项目上
            if (UtilCollection.isNotEmpty(purchaseReceiptPurchDocList)) {
                // 处理解析行项目和采购凭证中的账户设置 进行属性填充
                purchaseReceiptItemList=purchaseReceiptItemList.stream().map(erpPurchaseReceiptItemDTO -> purchaseReceiptPurchDocList.stream()
                        .filter(erpPurchaseReceiptPurchDoc->Objects.equals(erpPurchaseReceiptPurchDoc.get("EBELN"), erpPurchaseReceiptItemDTO.getReceiptCode())
                                && Objects.equals(erpPurchaseReceiptPurchDoc.get("EBELP"), erpPurchaseReceiptItemDTO.getReceiptProjectCode()))
                        .findFirst().map(item->{
                            // 配置wbs内码
                            erpPurchaseReceiptItemDTO.setSpecStockCode(item.get("POSID"));
                            // 配置WBS外码
                            erpPurchaseReceiptItemDTO.setWhCodeOut(item.get("PS_PSP_PNR"));
                            // 配置WBS描述
                            erpPurchaseReceiptItemDTO.setSpecStockName(item.get("POST1"));
                            // 配置总账科目文本
                            erpPurchaseReceiptItemDTO.setGeneralLedgerAccountText(item.get("TXT20"));
                            return erpPurchaseReceiptItemDTO;
                        }).orElse(erpPurchaseReceiptItemDTO))
                        .collect(Collectors.toList());
            }
            // 处理解析行项目和头信息 进行属性填充
            purchaseReceiptItemList.forEach(erpPurchaseReceiptItemDTO -> {
                purchaseReceiptHeadList.forEach(erpPurchaseReceiptHeadDTO -> {
                    if (Objects.equals(erpPurchaseReceiptHeadDTO.getReceiptCode(), erpPurchaseReceiptItemDTO.getReceiptCode())) {
                        /*   将头信息填充到行项目信息中  没有SAP单据备注 remark */
                        erpPurchaseReceiptItemDTO.setReceiptCode(erpPurchaseReceiptHeadDTO.getReceiptCode());
                        erpPurchaseReceiptItemDTO.setErpReceiptType(erpPurchaseReceiptHeadDTO.getErpReceiptType());
                        erpPurchaseReceiptItemDTO.setErpReceiptTypeName(erpPurchaseReceiptHeadDTO.getErpReceiptTypeName());
                        erpPurchaseReceiptItemDTO.setIsReturnFlag(erpPurchaseReceiptHeadDTO.getIsReturnFlag());
                        erpPurchaseReceiptItemDTO.setErpCreateUserCode(erpPurchaseReceiptHeadDTO.getErpCreateUserCode());
                        erpPurchaseReceiptItemDTO.setErpCreateUserName(erpPurchaseReceiptHeadDTO.getErpCreateUserName());
                        erpPurchaseReceiptItemDTO.setErpCreateTime(erpPurchaseReceiptHeadDTO.getErpCreateTime());
                        erpPurchaseReceiptItemDTO.setCreateTime(erpPurchaseReceiptHeadDTO.getCreateTime());
                        erpPurchaseReceiptItemDTO.setModifyTime(erpPurchaseReceiptHeadDTO.getModifyTime());
                        /*   将头信息填充到行项目信息中   */

                        // 采购人
                        erpPurchaseReceiptItemDTO.setPurchaseUserCode(erpPurchaseReceiptHeadDTO.getErpCreateUserCode());
                        erpPurchaseReceiptItemDTO.setPurchaseUserName(erpPurchaseReceiptHeadDTO.getErpCreateUserCode());
                        // 公司
                        erpPurchaseReceiptItemDTO.setCorpCode(erpPurchaseReceiptHeadDTO.getCorpCode());
                        erpPurchaseReceiptItemDTO.setCorpName(erpPurchaseReceiptHeadDTO.getCorpName());
                        Long corpIdCache = dictionaryService.getCorpIdCacheByCode(erpPurchaseReceiptHeadDTO.getCorpCode());
                        DicCorp corpCache = dictionaryService.getCorpCacheById(corpIdCache);
                        if (UtilObject.isNull(corpCache)) {
                            log.error("缓存中公司信息不存在：{}");
                        }
                        Long corpId = corpCache==null?null:corpCache.getId();
                        erpPurchaseReceiptItemDTO.setCorpId(corpId);
                        // 采购组织
                        erpPurchaseReceiptItemDTO.setPurchaseOrganizationCode(erpPurchaseReceiptHeadDTO.getPurchaseOrganizationCode());
                        erpPurchaseReceiptItemDTO.setPurchaseOrganizationName(erpPurchaseReceiptHeadDTO.getPurchaseOrganizationName());
                        // 采购组
                        erpPurchaseReceiptItemDTO.setPurchaseGroupCode(erpPurchaseReceiptHeadDTO.getPurchaseGroupCode());
                        erpPurchaseReceiptItemDTO.setPurchaseGroupName(erpPurchaseReceiptHeadDTO.getPurchaseGroupName());
                        erpPurchaseReceiptItemDTO.setSupplierCode(erpPurchaseReceiptHeadDTO.getSupplierCode());
                        erpPurchaseReceiptItemDTO.setSupplierName(erpPurchaseReceiptHeadDTO.getSupplierName());
                        erpPurchaseReceiptItemDTO.setGuaranteePeriod(erpPurchaseReceiptHeadDTO.getGuaranteePeriod());
                        // 头信息 id
                        erpPurchaseReceiptItemDTO.setHeadId(erpPurchaseReceiptHeadDTO.getId());
                    }
                });
            });
        }
        // 保存行项目信息
        saveOrUpdateErpPurchaseReceiptItem(purchaseReceiptItemList, headIdList);
        if (UtilCollection.isNotEmpty(purchaseReceiptItemList)) {
            resultList.addAll(purchaseReceiptItemList);
        }

    }

    /**
     * 保存采购订单行项目信息
     *
     * @param purchaseReceiptItemListFromSap
     * @param headIdList              已经存在的头信息 id
     */
    public void saveOrUpdateErpPurchaseReceiptItem(List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemListFromSap, List<Long> headIdList) {
        if (UtilCollection.isEmpty(purchaseReceiptItemListFromSap)) {
            return;
        }
        // 获取已经存在的行项目
        List<ErpPurchaseReceiptItem> itemList = erpPurchaseReceiptItemDataWrap.list(new QueryWrapper<ErpPurchaseReceiptItem>()
                .in("head_id", purchaseReceiptItemListFromSap.stream().map(ErpPurchaseReceiptItemDTO::getHeadId).collect(Collectors.toList())));
        // 将已经存在的行项目的数据id赋值给SAP返回的行项目
        itemList.forEach(erpPurchaseReceiptItem -> {
            purchaseReceiptItemListFromSap.forEach(erpPurchaseReceiptItemDTO -> {
                if (Objects.equals(erpPurchaseReceiptItemDTO.getHeadId(), erpPurchaseReceiptItem.getHeadId()) && Objects.equals(erpPurchaseReceiptItemDTO.getRid(), erpPurchaseReceiptItem.getRid())) {
                    erpPurchaseReceiptItemDTO.setId(erpPurchaseReceiptItem.getId());
                }
            });
        });

//        erpPurchaseReceiptItemDataWrap.saveOrUpdateByHeadIdRId(purchaseReceiptItemList);
        List<Long> erpPurchaseItemAlreadyExistedIds = purchaseReceiptItemListFromSap.stream().map(ErpPurchaseReceiptItemDTO::getId).collect(Collectors.toList());
        List<ErpPurchaseReceiptItem> shouldBeDeleteItems = itemList.stream().filter(e -> !erpPurchaseItemAlreadyExistedIds.contains(e.getId())).collect(Collectors.toList());
        List<Long> deletedItemIds = new ArrayList<>();
        if (UtilCollection.isNotEmpty(shouldBeDeleteItems)) {
            deletedItemIds = shouldBeDeleteItems.stream().map(ErpPurchaseReceiptItem::getId).collect(Collectors.toList());
        }

        /* ================ 删除已经存在的采购订单行项目信息 ================*/
        UpdateWrapper<ErpPurchaseReceiptItem> itemUpdateWapper = new UpdateWrapper<>();
        if (UtilCollection.isNotEmpty(headIdList) && UtilCollection.isNotEmpty(deletedItemIds)) {
            itemUpdateWapper.lambda()
                    .in(Boolean.TRUE, ErpPurchaseReceiptItem::getHeadId, headIdList)
                    .in(ErpPurchaseReceiptItem::getId, deletedItemIds)
                    .set(ErpPurchaseReceiptItem::getDeleteTag, null)
            ;
            erpPurchaseReceiptItemDataWrap.update(itemUpdateWapper);
        }
        /* ================ 保存采购订单行项目信息 ================*/
        erpPurchaseReceiptItemDataWrap.saveOrUpdateBatchDto(purchaseReceiptItemListFromSap);
    }

    /**
     * 保存erp采购订单头信息
     *
     * @param purchaseReceiptHeadList
     * @return 返回已经存在的头信息id
     */
    public List<Long> savaOrUpdateErpPurchaseReceiptHead(List<ErpPurchaseReceiptHeadDTO> purchaseReceiptHeadList) {
        /* ================ 查询存在的采购订单信息id ================*/
        WmsQueryWrapper<ErpPurchaseReceiptHead> wrapper = new WmsQueryWrapper<>();
        List<String> receiptCodeList = purchaseReceiptHeadList.stream().map(ErpPurchaseReceiptHeadDTO::getReceiptCode).collect(Collectors.toList());
        wrapper.lambda()
                .in(Boolean.TRUE, ErpPurchaseReceiptHead::getReceiptCode, receiptCodeList);
        List<ErpPurchaseReceiptHead> erpPurchaseReceiptHeadList = erpPurchaseReceiptHeadDataWrap.list(wrapper);
        // 已经存在的headId
        List<Long> headIdList = erpPurchaseReceiptHeadList.stream().map(ErpPurchaseReceiptHead::getId).collect(Collectors.toList());
        // 已经存在的receiptCode exist
        List<String> existReceiptCodeList = erpPurchaseReceiptHeadList.stream().map(ErpPurchaseReceiptHead::getReceiptCode).collect(Collectors.toList());


        /*================ 根据 receiptCode 更新采购订单头信息 ================*/

        // 如果存在headIdList则更新
        if (UtilCollection.isNotEmpty(existReceiptCodeList)) {
            purchaseReceiptHeadList.stream().forEach(erpPurchaseReceiptHeadDTO -> {
                existReceiptCodeList.stream().forEach(receiptCode -> {
                    if (receiptCode.equals(erpPurchaseReceiptHeadDTO.getReceiptCode())){
                        erpPurchaseReceiptHeadDataWrap.updateDto(erpPurchaseReceiptHeadDTO,
                                new UpdateWrapper<ErpPurchaseReceiptHead>().eq("receipt_code", receiptCode));
                        // 获取已经存在头信息的id
                        ErpPurchaseReceiptHead headInfoByReceiptCode = erpPurchaseReceiptHeadDataWrap.getOne(new QueryWrapper<ErpPurchaseReceiptHead>().eq("receipt_code", receiptCode));
                        erpPurchaseReceiptHeadDTO.setId(headInfoByReceiptCode.getId());
                    }
                });
            });
        } else {
            // 如果没有headId直接新增
            erpPurchaseReceiptHeadDataWrap.saveBatchDto(purchaseReceiptHeadList);
        }
        return headIdList;
    }


    /**
     * 查询采购订单假接口
     *
     * @param po         查询条件
     * @param resultList 采购订单行项目信息
     */
    private void getErpPurchaseReceipt(List<ErpPurchaseReceiptItemDTO> resultList, BizReceiptPreSearchPO po) {
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.getPurchaseReceiptItemList(po);
        if (UtilCollection.isNotEmpty(purchaseReceiptItemList)) {
            //  优先取对应工厂物料主数据中库存确定组
            purchaseReceiptItemList.forEach(item -> {
                Long ftyId = item.getFtyId();
                Long maraId = item.getMatId();
                DicMaterialFactoryDTO maraFtyDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(maraId, ftyId);
                if (maraFtyDTO != null) {
                    Long stockGroupId = maraFtyDTO.getStockGroupId();
                    if (UtilNumber.isNotEmpty(stockGroupId)) {
                        item.setLocationId(stockGroupId);
                        item.setLocationCode(maraFtyDTO.getStockGroup());
                    }
                }

                if (item.getMatId() == 0L && "1".equals(item.getSubjectType())){
                    item.setMatName(item.getMatNameBack());
                }
            });
            resultList.addAll(purchaseReceiptItemList);
        }
    }

    /**
     * 获取erp采购订单
     *
     * @param itemIdList 行项目id集合
     * @return List<ErpPurchaseReceiptItemDTO>
     */

    public List<ErpPurchaseReceiptItemDTO> getErpPurchaseReceiptItem(List<Long> itemIdList) {
        if (UtilCollection.isNotEmpty(itemIdList)) {
            return UtilCollection.toList(erpPurchaseReceiptItemDataWrap.listByIds(itemIdList), ErpPurchaseReceiptItemDTO.class);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * （批量）修改erp采购订单行项目
     *
     * @param purchaseDTOList erp采购订单行项目
     */
    public void multiUpdateBatchInfo(List<ErpPurchaseReceiptItemDTO> purchaseDTOList) {
        if (UtilCollection.isNotEmpty(purchaseDTOList)) {
            erpPurchaseReceiptItemDataWrap.updateBatchDtoById(purchaseDTOList);
        }
    }

    /**
     * 根据单据号获取id
     *
     * @param receiptCode
     * @return
     */
    public Long getIdByReceiptCode(String receiptCode) {
        QueryWrapper<ErpPurchaseReceiptHead> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ErpPurchaseReceiptHead::getReceiptCode, receiptCode);
        ErpPurchaseReceiptHead head = erpPurchaseReceiptHeadDataWrap.getOne(wrapper);
        if (Objects.isNull(head)) {
            return Long.valueOf(0);
        } else {
            return head.getId();
        }
    }

    /**
     * 解析SAP行项目集合
     *
     * @param erpPurchaseReceiptItemArray ERP采购订单行项目集合
     * @return ErpPurchaseReceiptItemDTO
     */
    public List<ErpPurchaseReceiptItemDTO> getErpPurchaseReceiptItemByJSONArray(JSONArray erpPurchaseReceiptItemArray) {
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemList = new ArrayList<>();
        List<DicUnit> unitList = new ArrayList<>();
        // 新增物料集合
        List<String> matCodeList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(erpPurchaseReceiptItemArray)) {
            erpPurchaseReceiptItemArray.forEach(erpPurchaseReceiptItem -> {
                JSONObject obj = (JSONObject) erpPurchaseReceiptItem;
                ErpPurchaseReceiptItemDTO erpPurchaseReceiptItemDto = new ErpPurchaseReceiptItemDTO();
                // 采购凭证号 订单号
                erpPurchaseReceiptItemDto.setReceiptCode(UtilObject.getStringOrEmpty(obj.get("EBELN")));
                erpPurchaseReceiptItemDto.setReceiptProjectCode(UtilObject.getStringOrEmpty(obj.get("EBELP")));
                // 采购订单行号
                erpPurchaseReceiptItemDto.setRid(UtilObject.getStringOrEmpty(obj.get("EBELP")));
                // 合同号
                erpPurchaseReceiptItemDto.setContractCode(UtilObject.getStringOrEmpty(obj.get("ZZOTNUM")));
                // 合同描述
                erpPurchaseReceiptItemDto.setContractName(UtilObject.getStringOrEmpty(obj.get("HTNAME")));
                // 合同创建人
                String afnamStr = UtilObject.getStringOrEmpty(obj.get("AFNAM"));
                SysUser afnam = dictionaryService.getSysUserCacheByuserCode(afnamStr);
                if (Objects.isNull(afnam)) {
                    // 如果没有找到对应的用户，则使用 AFNAM 作合同创建人
                    erpPurchaseReceiptItemDto.setContractCreateUserName(afnamStr);
                } else {
                    // 如果找到了对应的用户，则使用该用户的用户名
                    erpPurchaseReceiptItemDto.setContractCreateUserName(afnam.getUserName());
                }
                erpPurchaseReceiptItemDto.setContractCreateUserName(UtilObject.getStringOrEmpty(obj.get("AFNAM")));
                // 客户编号
                erpPurchaseReceiptItemDto.setCustomerCode(UtilObject.getStringOrEmpty(obj.get("KUNNR")));
                // 客户名称
                erpPurchaseReceiptItemDto.setCustomerName(UtilObject.getStringOrEmpty(obj.get("KUNNR")));
                // 采购申请号
                erpPurchaseReceiptItemDto.setPurchaseApplicationCode(UtilObject.getStringOrEmpty(obj.get("BANFN")));
                // 采购申请行号
                erpPurchaseReceiptItemDto.setPurchaseApplicationRid(UtilObject.getStringOrEmpty(obj.get("BNFPO")));
                // 已采购数量
                erpPurchaseReceiptItemDto.setSubmitQty(BigDecimal.ZERO);
                // 需求部门编码
                // 订购数量 订单数量
                erpPurchaseReceiptItemDto.setReceiptQty(new BigDecimal(UtilObject.getStringOrEmpty(obj.get("MENGE"))));
                // 订单单位 id
                dicUnitOpt(obj, erpPurchaseReceiptItemDto, unitList);
                // 公司主键id - 头信息中
                // 工厂id 工厂
                erpPurchaseReceiptItemDto.setFtyCode(UtilObject.getStringOrEmpty(obj.get("WERKS")));
                Long ftyId = dictionaryService.getFtyIdCacheByCode(UtilObject.getStringOrEmpty(obj.get("WERKS")));
                Long locationId = dictionaryService.getLocationIdCacheByCode(UtilObject.getStringOrEmpty(obj.get("WERKS")), UtilObject.getStringOrEmpty(obj.get("LGORT")));
                erpPurchaseReceiptItemDto.setFtyId(ftyId);
                erpPurchaseReceiptItemDto.setLocationId(locationId == null ? 0 : locationId);
                // 仓库号
                DicStockLocationDTO dicStockLocationDTO = dictionaryService.getLocationCacheById(locationId);
                erpPurchaseReceiptItemDto.setWhId(dicStockLocationDTO == null ? 0 : dicStockLocationDTO.getWhId());
                // 库存地点
                erpPurchaseReceiptItemDto.setLocationCode(UtilObject.getStringOrEmpty(obj.get("LGORT")));
                // 项目类别
                erpPurchaseReceiptItemDto.setProjectType(UtilObject.getStringOrEmpty(obj.get("PTEXT")));
                // 科目类别
                erpPurchaseReceiptItemDto.setSubjectType(UtilObject.getStringOrEmpty(obj.get("KNTTP")));
                // 根据物料code获取物料信息 - 支持科目分配类别为1，项目类型为空，物料编码可能为空的零星消耗采购订单
                if(UtilString.isNotNullOrEmpty(obj.getString("MATNR"))) {
                    // 物料编码
                    erpPurchaseReceiptItemDto.setMatCode(obj.getString("MATNR"));
                    erpPurchaseReceiptItemDto.setMatName(UtilObject.getStringOrEmpty(obj.get("TXZ01")));
                    Long matId = dictionaryService.getMatIdByMatCode(obj.getString("MATNR"));
                    if (UtilNumber.isEmpty(matId)) {
                        log.error("物料信息不存在:{}", UtilObject.getStringOrEmpty(obj.get("MATNR")));
                        matCodeList.add(UtilObject.getStringOrEmpty(obj.get("MATNR")));
                    } else {
                        // 物料id
                        erpPurchaseReceiptItemDto.setMatId(matId);
                    }
                }else {
                    erpPurchaseReceiptItemDto.setMatNameBack(UtilObject.getStringOrEmpty(obj.get("TXZ01")));
                    // 物料id
                    erpPurchaseReceiptItemDto.setMatId(0L);
                }
                // 需求人编码
                erpPurchaseReceiptItemDto.setApplyUserCode(UtilObject.getStringOrEmpty(obj.get("SQNAM")));
                // 需求人描述
                erpPurchaseReceiptItemDto.setApplyUserName(UtilObject.getStringOrEmpty(obj.get("NAME_TEXT")));
                // 需求人部门编码
                erpPurchaseReceiptItemDto.setApplyUserDeptCode(UtilObject.getStringOrEmpty(obj.get("ZDPTSD")));
                // 需求人部门描述
                erpPurchaseReceiptItemDto.setApplyUserDeptName(UtilObject.getStringOrEmpty(obj.get("INNAM")));
                // 需求人科室编码
                erpPurchaseReceiptItemDto.setApplyUserOfficeCode(UtilObject.getStringOrEmpty(obj.get("ZSUBSTID")));
                // 需求人科室描述
                erpPurchaseReceiptItemDto.setApplyUserOfficeName(UtilObject.getStringOrEmpty(obj.get("ZSUBSTTX")));
                // 特殊库存类型
                erpPurchaseReceiptItemDto.setSpecStock(UtilObject.getStringOrEmpty(obj.get("SOBKZ")));
                // 创建人
                erpPurchaseReceiptItemDto.setErpCreateUserName(UtilObject.getStringOrEmpty(obj.get("ERNAM")));
                // 创建日期
                erpPurchaseReceiptItemDto.setErpCreateTime(DateUtil.parseDate(UtilObject.getStringOrEmpty(obj.get("AEDAT"))));

                // 采购退货标识
                if (UtilObject.getStringOrEmpty(obj.get("RETPO")).toUpperCase().equals("X")) {
                    // 可出库数量
                    erpPurchaseReceiptItemDto.setCanOutputQty(new BigDecimal(UtilObject.getStringOrEmpty(obj.get("BPWES"))));
                    erpPurchaseReceiptItemDto.setIsReturnFlag(1);
                } else {
                    // 可送货数量
                    erpPurchaseReceiptItemDto.setCanDeliveryQty(new BigDecimal(UtilObject.getStringOrEmpty(obj.get("BPWES"))));
                    erpPurchaseReceiptItemDto.setIsReturnFlag(0);
                }

//                // 品牌信息
//                erpPurchaseReceiptItemDto.setBrandmodel(" ");
//                // 计划配送日期
//                // 送货日期
//                erpPurchaseReceiptItemDto.setDeliveryTime(new DateTime());
//                // 特殊库存代码-（存储wbs外码） 在采购凭证中的账户设置中
////                erpPurchaseReceiptItemDto.setSpecStockCode(" ");
//                // 特殊库存描述
//                erpPurchaseReceiptItemDto.setSpecStockName(" ");
//                // 供应商编码 头信息中
//                // 供应商名称 头信息中
//                // SAP领用单位
//                erpPurchaseReceiptItemDto.setApplyCompany(" ");
//                // 采购员编号 头信息中
//                // 采购员名称 头信息中
//                // 采购员组编号 头信息中
//                // 采购员组描述 头信息中
//                // 采购组织编号 头信息中
//                // 采购组织描述 头信息中
//                // 移动类型id
//                erpPurchaseReceiptItemDto.setMoveTypeId(0L);
//
//                // 已质检数量
//                erpPurchaseReceiptItemDto.setInspectQty(BigDecimal.ZERO);
//                // erp批次
//                erpPurchaseReceiptItemDto.setBatchErp(" ");
//                // 单价

                if(UtilString.isNotNullOrEmpty(obj.getString("NETPR"))) {
                    erpPurchaseReceiptItemDto.setPrice(new BigDecimal(UtilObject.getStringOrEmpty(obj.get("NETPR"))));
                }
//                // 金额
//                erpPurchaseReceiptItemDto.setMoney(BigDecimal.ZERO);
//                // 需求单号
//                erpPurchaseReceiptItemDto.setRequireApplicationCode(" ");
//                // 需求申请行号
//                erpPurchaseReceiptItemDto.setRequireApplicationRid(" ");
                purchaseReceiptItemList.add(erpPurchaseReceiptItemDto);
                //TODO 完善采购订单行项目信息
            });
            // 更新unit
            if (!unitList.isEmpty()) {

                // 判断数据库中已经存在的unitCode
                List<DicUnit> unitCodeList = dicUnitDataWrap.list(
                        new QueryWrapper<DicUnit>()
                                .in("unit_code", unitList.stream().map(DicUnit::getUnitCode).collect(Collectors.toList())));
                // 过滤掉已经存在的单位
                List<DicUnit> insertUnitList = unitList.stream().filter(dicUnit -> unitCodeList.stream()
                        .noneMatch(unit -> Objects.equals(dicUnit.getUnitCode(), unit.getUnitCode()))).collect(Collectors.toList());
                // 根据unitCode去重
                insertUnitList = insertUnitList.stream().collect(
                        Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DicUnit::getUnitCode))), ArrayList::new));
                dicUnitDataWrap.saveOrUpdateBatchDto(insertUnitList);
                // 刷新单位缓存
                editCacheService.refreshUnitCache();
            }
        }
        // 新增物料主数据
        if(UtilCollection.isNotEmpty(matCodeList)) {
            sapInterfaceService.synMaterialInfoNew(null, matCodeList, null, null);
        }
        // 回填行项目物料id
        purchaseReceiptItemList.forEach(p -> {
            if(UtilString.isNotNullOrEmpty(p.getMatCode())) {
                p.setMatId(dictionaryService.getMatIdByMatCode(p.getMatCode()));
            }
        });
        return purchaseReceiptItemList;
    }

    /**
     * 解析SAP抬头信息
     *
     * @param erpPurchaseReceiptHeadArray
     * @return
     */
    private List<ErpPurchaseReceiptHeadDTO> getErpPurchaseReceiptHeadByJSONArray(JSONArray erpPurchaseReceiptHeadArray) {
        List<ErpPurchaseReceiptHeadDTO> purchaseReceiptHeadList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(erpPurchaseReceiptHeadArray)) {
            erpPurchaseReceiptHeadArray.forEach(erpPurchaseReceiptHead -> {
                JSONObject obj = (JSONObject) erpPurchaseReceiptHead;
                ErpPurchaseReceiptHeadDTO erpPurchaseReceiptHeadDto = new ErpPurchaseReceiptHeadDTO();

                // 采购订单号
                erpPurchaseReceiptHeadDto.setReceiptCode(UtilObject.getStringOrEmpty(obj.get("EBELN")));
                // 描述
//                erpPurchaseReceiptHeadDto.setRemark(UtilObject.getStringOrEmpty(obj.get("TEXT")));
                // 采购订单类型
                erpPurchaseReceiptHeadDto.setErpReceiptType(UtilObject.getStringOrEmpty(obj.get("BSART")));
                // 采购订单描述
                erpPurchaseReceiptHeadDto.setErpReceiptTypeName(UtilObject.getStringOrEmpty(obj.get("BATXT")));
                // 采购人
                erpPurchaseReceiptHeadDto.setErpCreateUserName(UtilObject.getStringOrEmpty(obj.get("ERNAM")));
                erpPurchaseReceiptHeadDto.setErpCreateUserCode(UtilObject.getStringOrEmpty(obj.get("ERNAM")));
                // 创建日期
                erpPurchaseReceiptHeadDto.setErpCreateTime(DateUtil.parseDate(UtilObject.getStringOrEmpty(obj.get("AEDAT"))));
                // 公司
                erpPurchaseReceiptHeadDto.setCorpCode(UtilObject.getStringOrEmpty(obj.get("BUKRS")));
                erpPurchaseReceiptHeadDto.setCorpName(UtilObject.getStringOrEmpty(obj.get("BUTXT")));
                // 采购组织信息
//                erpPurchaseReceiptHeadDto.setPurchaseOrganizationCode(UtilObject.getStringOrEmpty(obj.get("EKORG")));
//                erpPurchaseReceiptHeadDto.setPurchaseOrganizationName(UtilObject.getStringOrEmpty(obj.get("EKOTX")));
                // 采购组信息
//                erpPurchaseReceiptHeadDto.setPurchaseGroupCode(UtilObject.getStringOrEmpty(obj.get("EKGRP")));
//                erpPurchaseReceiptHeadDto.setPurchaseGroupName(UtilObject.getStringOrEmpty(obj.get("EKNAM")));
                // 供应商信息
                erpPurchaseReceiptHeadDto.setSupplierCode(UtilObject.getStringOrEmpty(obj.get("LIFNR")));
                erpPurchaseReceiptHeadDto.setSupplierName(UtilObject.getStringOrEmpty(obj.get("NAME1")));
                erpPurchaseReceiptHeadDto.setGuaranteePeriod(UtilObject.getStringOrEmpty(obj.get("EDATE1")));
                purchaseReceiptHeadList.add(erpPurchaseReceiptHeadDto);
            });
        }
        return purchaseReceiptHeadList;
    }


    /**
     * 解析采购订单中的账户设置
     *
     * @param erpPurchaseReceiptPurchDocArray
     * @return
     */
    private List<Map<String, String>> getErpPurchaseReceiptPurchDocByJSONArray(JSONArray erpPurchaseReceiptPurchDocArray) {
        List<Map<String, String>> erpPurchaseReceiptPurchDocList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(erpPurchaseReceiptPurchDocArray)) {
            erpPurchaseReceiptPurchDocArray.forEach(erpPurchaseReceiptPurchDoc -> {
                JSONObject obj = (JSONObject) erpPurchaseReceiptPurchDoc;
                Map<String, String> map = new ConcurrentHashMap<>();
                // SAP-采购凭证号
                map.put("EBELN", UtilObject.getStringOrEmpty(obj.get("EBELN")));
                // SAP-采购凭证的项目编号
                map.put("EBELP", UtilObject.getStringOrEmpty(obj.get("EBELP")));
                // SAP-工作分解结构元素 (WBS 元素)
                map.put("POSID", UtilObject.getStringOrEmpty(obj.get("POSID")));
                // SAP-工作分解结构元素 (WBS 元素) WBS外码
                map.put("PS_PSP_PNR", UtilObject.getStringOrEmpty(obj.get("PS_PSP_PNR")));
                // WBS描述
                map.put("POST1", UtilObject.getStringOrEmpty(obj.get("POST1")));
                // 总账科目文本
                map.put("TXT20", UtilObject.getStringOrEmpty(obj.get("TXT20")));
                erpPurchaseReceiptPurchDocList.add(map);
            });
        }
        return erpPurchaseReceiptPurchDocList;
    }

    /**
     * 单位的处理
     *
     * @param obj
     * @param erpPurchaseReceiptItemDto
     * @param unitList
     */
    private void dicUnitOpt(JSONObject obj, ErpPurchaseReceiptItemDTO erpPurchaseReceiptItemDto, List<DicUnit> unitList) {
        erpPurchaseReceiptItemDto.setUnitCode(UtilObject.getStringOrEmpty(obj.get("MEINS")).toUpperCase());
        erpPurchaseReceiptItemDto.setUnitName(UtilObject.getStringOrEmpty(obj.get("MSEHL")));
        Long meinsId = dictionaryService.getUnitIdCacheByCode(UtilObject.getStringOrEmpty(obj.get("MEINS")).toUpperCase());
        DicUnit dicUnit = dictionaryService.getUnitCacheById(meinsId);
        erpPurchaseReceiptItemDto.setUnitId(dicUnit == null ? 0 : dicUnit.getId());
        if (UtilNumber.isNull(meinsId) || UtilObject.isNull(dicUnit)) {
            DicUnit dicUnitInfo = new DicUnit();
            String unitCode = UtilObject.getStringOrEmpty(obj.get("MEINS")).toUpperCase();
            String unitName = UtilObject.getStringOrEmpty(obj.get("MSEHL"));
            dicUnitInfo.setDecimalPlace(3);
            dicUnitInfo.setUnitName(unitName);
            dicUnitInfo.setUnitCode(unitCode);
            unitList.add(dicUnitInfo);
        }
    }

}
