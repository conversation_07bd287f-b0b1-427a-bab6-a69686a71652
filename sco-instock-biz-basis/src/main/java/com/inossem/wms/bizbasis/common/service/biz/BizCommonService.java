package com.inossem.wms.bizbasis.common.service.biz;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonObject;
import com.inossem.wms.bizbasis.common.dao.BizCommonMapper;
import com.inossem.wms.bizbasis.common.dao.SequenceMapper;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageBinDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumFreezeType;
import com.inossem.wms.common.enums.EnumHspncCode;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.po.StartProcessInstancePO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.log.po.ReceiptPO;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySearchSpecPO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.model.org.location.po.DicStockLocationFreeSearchPO;
import com.inossem.wms.common.model.sequence.vo.SysSequencePrefixVO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSpring;
import com.inossem.wms.common.util.UtilString;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 通用方法 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Service
@Slf4j
public class BizCommonService {

    @Autowired
    protected SequenceMapper sequenceMapper;

    @Autowired
    protected BizCommonMapper bizCommonMapper;

    @Autowired
    private DicWhStorageBinDataWrap dicWhStorageBinDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private DicStockLocationDataWrap dicStockLocationDataWrap;

    private static I18nTextCommonService i18nTextCommonService;

    @Autowired
    public void setService(I18nTextCommonService i18nTextCommonService) {
        BizCommonService.i18nTextCommonService = i18nTextCommonService;
    }

    /* *****************************  流水号获取  ************************************ */

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSequenceValue(String sequenceCode) {
        return sequenceMapper.selectNextValue(sequenceCode);
    }

    public String getNextSequenceValueDaily(String sequenceCode) {
        return sequenceMapper.selectNextValueDaily(sequenceCode);
    }

    /* *****************************  流水号获取结束  ************************************ */

    /* *****************************  业务单据通用方法  ************************************ */

    /**
     * 获取当前登录用户
     *
     * @return CurrentUser 当前用户
     */
    public CurrentUser getUser() {
        // 修复未登录时（如定时任务等），调用方法导致空指针异常。
        if (SecurityContextHolder.getContext().getAuthentication() == null) {
            UserService userService= UtilSpring.getBean("userService");
            CurrentUser currentUser=userService.getCurrentUserByUserCode(Const.ADMIN_USER_CODE);
            return currentUser;
        }
        return (CurrentUser) SecurityContextHolder.getContext().getAuthentication().getDetails();
    }

    /**
     * 根据当前登录人获取库存地点信息
     *
     * @param userId
     * @return
     */
    public List<DicStockLocationDTO> getLocationDTOList(Long userId) {
        List<DicStockLocation> dicStockLocationList = dicStockLocationDataWrap.getLocationByUserId(userId);
        List<DicStockLocationDTO> locationDTOList = UtilCollection.toList(dicStockLocationList, DicStockLocationDTO.class);

        return locationDTOList;
    }

    /**
     * 根据选择工厂获取当前登录人库存地点信息
     *
     * @param po
     * @return
     */
    public List<DicStockLocationDTO> getLocationDTOListByFtyId(DicStockLocationFreeSearchPO po) {
        CurrentUser user = this.getUser();
        po.setUserId(user.getId());
        po.setInputFree(po.getInputFree() == null ? EnumFreezeType.INVENTORY_FREEZE.getValue() : po.getInputFree());
        po.setOutputFree(po.getOutputFree() == null ? EnumFreezeType.INVENTORY_FREEZE.getValue() : po.getOutputFree());
        List<DicStockLocation> dicStockLocationList = dicStockLocationDataWrap.getLocationByFtyId(po);
        List<DicStockLocationDTO> locationDTOList = UtilCollection.toList(dicStockLocationList, DicStockLocationDTO.class);

        return locationDTOList;
    }

    /**
     * 根据库存地点获取仓库Id
     *
     * @param locationId
     * @return
     */
    public DicStockLocationDTO getWhIdByLocId(Long locationId) {
        QueryWrapper<DicStockLocation> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DicStockLocation::getId, locationId);
        DicStockLocation dicStockLocation = dicStockLocationDataWrap.getOne(wrapper);
        DicStockLocationDTO locationDTO = UtilBean.newInstance(dicStockLocation, DicStockLocationDTO.class);
        dataFillService.fillRlatAttrForDataObj(locationDTO);
        return locationDTO;
    }

    /**
     * 设置ins凭证code
     *
     * @param insMoveTypeDTO ins凭证
     */
    public void setInsDocCode(StockInsMoveTypeDTO insMoveTypeDTO) {
        if (UtilObject.isNotNull(insMoveTypeDTO)) {
            /* AopContext.currentProxy()获取当前的代理对象
             * 用来解决事务方法本类内部调用时，声明事务不生效的问题
             * 使用instance.getNextSequenceValue() 来替代 this.getNextSequenceValue() */
            BizCommonService instance = ((BizCommonService) AopContext.currentProxy());
            String insDocCode = instance.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
            if (UtilCollection.isNotEmpty(insMoveTypeDTO.getInsDocBatchList())) {
                for (StockInsDocBatch docBatch : insMoveTypeDTO.getInsDocBatchList()) {
                    docBatch.setInsDocCode(insDocCode);
                }
            }
            if (UtilCollection.isNotEmpty(insMoveTypeDTO.getInsDocBinList())) {
                for (StockInsDocBin docbin : insMoveTypeDTO.getInsDocBinList()) {
                    docbin.setInsDocCode(insDocCode);
                }
            }
        }
    }

    /**
     * 发起审批
     *
     * @param receiptCode 单据号
     * @param receiptType 单据类型
     * @param map         变量
     */
    public void startWorkFlow(Long receiptId, String receiptCode, Integer receiptType, Map<String, Object> map) {
        CurrentUser currentUser = this.getUser();
        String procId = UtilConst.getInstance().getWfProcIdByReceiptType(receiptType);
        StartProcessInstancePO startPo = new StartProcessInstancePO();

        startPo.setUserCode(currentUser.getUserCode()).setUserId(currentUser.getId()).setReceiptId(receiptId).setBusinessKey(receiptCode)
                .setProcessDefinitionKey(procId).setReceiptCode(receiptCode).setReceiptType(receiptType).setVariables(map);

        // 推送MQ edit by ChangBaoLong 避免消息丢失，采用同步机制发送消息
        ProducerMessageContent message = ProducerMessageContent.messageContent(Const.START_APPROVAL_REQUEST_CODE, startPo);
        SendResult sendResult = RocketMQProducerProcessor.getInstance().SyncMQSendWithResult(message);
        if (sendResult == null || !SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION, "工作流启动失败，MQ消息发送失败。");
        }
    }

    /**
     * 发起审批
     *
     * @param receiptCode 单据号
     * @param receiptType 单据类型
     * @param map         变量
     */
    public void revokeWorkFlow(Long receiptId, String receiptCode, Integer receiptType, Map<String, Object> map) {
        CurrentUser currentUser = this.getUser();
        String procId = UtilConst.getInstance().getWfProcIdByReceiptType(receiptType);
        StartProcessInstancePO startPo = new StartProcessInstancePO();

        startPo.setUserCode(currentUser.getUserCode()).setUserId(currentUser.getId()).setReceiptId(receiptId).setBusinessKey(receiptCode)
                .setProcessDefinitionKey(procId).setReceiptCode(receiptCode).setReceiptType(receiptType).setVariables(map);

        // 推送MQ
        ProducerMessageContent message = ProducerMessageContent.messageContent(Const.REVOKE_APPROVAL_REQUEST_CODE, startPo);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }


    /**
     * 获取单据状态为已完成时,行项目状态应该属于什么状态的set
     *
     * @return Integer
     */
    public Set<Integer> getCompletedItemStatusSet() {
        Set<Integer> itemStatusSet = new HashSet<>();
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_WRITING_OFF.getValue());
        return itemStatusSet;
    }
    public Set<Integer> getWritedOffItemStatusSet() {
        Set<Integer> itemStatusSet = new HashSet<>();
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
        return itemStatusSet;
    }


    /**
     * 入库模式为先过账 包含已作业
     * @return
     */
    public Set<Integer> getInputCompletedItemStatusSet() {
        Set<Integer> itemStatusSet = new HashSet<>();
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_WRITING_OFF.getValue());
        itemStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        return itemStatusSet;
    }

    /**
     * 获取仓库下的空仓位id列表
     *
     * @return Long
     */
    public Set<Long> getEmptyBinSet(Long whId) {
        return bizCommonMapper.getEmptyBinSet(whId);
    }

    /**
     * 获取仓库下的空仓位id列表
     *
     * @return Long
     */
    public List<StockBinDTO> getEmptyBinDTO(Long whId) {
        return bizCommonMapper.getEmptyBinDTO(whId);
    }

    /**
     * 判断过账日期是否在帐期内
     *
     * @param postingDate 过账日期
     * @param userId      用户id
     * @return Date
     */
    public Date checkAndUpdateInPostDate(Date postingDate, Long userId) {
        return postingDate;
    }
    // public Date checkAndUpdateInPostDate(Date postingDate, Integer userId) {
    // postingDate = postingDate == null ? new Date() : postingDate;
    // DicAccountPeriodDTO accountPeriod = accountPeriodDao.selectByUserId(userId);
    // if (accountPeriod == null) {
    // log.debug("过账日期返回当前时间{}", postingDate);
    // return postingDate;
    // }
    // accountPeriod.setAccountPeriodList(accountPeriodDao.selectByCo(accountPeriod.getBoardCode(),
    // accountPeriod.getCorpCode()));
    //
    // long docDate = Long.parseLong(UtilDate.getStringDateForDate(postingDate).trim().replaceAll("-", "").substring(0,
    // 8));
    // if (accountPeriod.getEnabled() == EnumEnable.ENABLE.getValue()) {
    // List<DicAccountPeriod> accountPeriodList = accountPeriod.getAccountPeriodList();
    // if (accountPeriodList.size() == 0) {
    // log.debug("过账日期返回当前时间{}", postingDate);
    // return postingDate;
    // }
    //
    // for (DicAccountPeriod accoutPeriod : accountPeriodList) {
    // long beginDate =
    // Long.parseLong(UtilDate.getStringDateForDate(accoutPeriod.getAccountBeginDate()).trim().replaceAll("-",
    // "").substring(0, 8));
    // long endDate =
    // Long.parseLong(UtilDate.getStringDateForDate(accoutPeriod.getAccountEndDate()).trim().replaceAll("-",
    // "").substring(0, 8));
    // if (docDate <= endDate && docDate >= beginDate) {
    // return accoutPeriod.getAccountFactDate();
    // }
    // }
    // }
    //
    // return postingDate;
    // }

    /**
     * 根据物料id 工厂id 获取分类id
     *
     * @param searchSpecList 入参
     * @return 分类id列表
     */
    public List<DicMaterialFactoryDTO> getClassifyList(List<DicMaterialFactorySearchSpecPO> searchSpecList) {
        return bizCommonMapper.getClassifyList(searchSpecList);
    }

    /**
     * 修改冻结状态，同时会更新缓存
     *
     * @param dicWhStorageBin 仓位
     */
    public void updateFreeze(List<DicWhStorageBin> dicWhStorageBin) {
        log.info("WCS-修改冻结仓位 ctx：{}", JSONObject.toJSONString(dicWhStorageBin));
        dicWhStorageBinDataWrap.saveOrUpdateBatchDto(dicWhStorageBin);
        // 刷新缓存
        dictionaryService.initBinCache();
    }

    /* *****************************  业务单据通用方法结束  ************************************ */


    /* ============================ 华能单据编号的编码获取开始 ============================ */


    /**
     * 物项返运交接单的编码
     *
     * @param sequenceCode delivery_num
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqDelivery(CurrentUser user, String sequenceCode) {
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = EnumHspncCode.PRE_SEQ_HS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_REC.getCode() + "-"
                + nextValue;
        return seqCode;
    }

    /**
     * 产生记录的编码
     *
     * @param sequenceCode generate_record
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqGenerateRecords(CurrentUser user, String sequenceCode) {
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValue(sequenceCode);
        // 组装编码
        String seqCode = EnumHspncCode.PRE_SEQ_HS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_HCCD.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_IR.getCode() + "/" + EnumHspncCode.PRE_SEQ_APL.getCode() + "-"
                + nextValue;
        return seqCode;
    }

    /**
     * 物项维护保养记录编码
     *
     * @param sequenceCode maintenance_record
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqMaintenanceRecord(CurrentUser user, String sequenceCode) {
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String deptCode = userDeptList.size() == 0 ? "XXX" : userDeptList.get(0).getDeptCode();
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValue(sequenceCode);
        // 组装编码
        String seqCode = EnumHspncCode.PRE_SEQ_HS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_DEPT_PRE.getCode() + deptCode + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_RC.getCode() + "-"
                + nextValue;
        return seqCode;
    }

    /**
     * 物项特殊维护保养要求编码
     *
     * @param sequenceCode special_maintenance
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqSpecialMaintenance(CurrentUser user, String sequenceCode,String ftyCode) {
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValue(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_HCCD.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_RQ.getCode() + "-"
                + nextValue;
        return seqCode;
    }

    /**
     * 物项暂存申请单编码
     *
     * @param sequenceCode item_storage_apply
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqItemStorage(CurrentUser user, String sequenceCode,String ftyCode) {
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String deptCode = userDeptList.size() == 0 ? "XXX" : userDeptList.get(0).getDeptCode();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
//                + EnumHspncCode.PRE_SEQ_DEPT_PRE.getCode() + deptCode + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_APS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_ZC.getCode()
                + nextValue;
        return seqCode;
    }

    /**
     * 获取暂存物项下一个批次
     *
     * @return String
     */
    public String getNextSeqTempStoreBatch() {
        // 获取流水号
        String nextValue = this.getNextSequenceValueDaily(EnumSequenceCode.TEMP_STORE_BATCH.getValue());
        String ymd = getYMD();
        String seqCode = EnumSpecStock.SPEC_STOCK_BATCH_STATUS_TEMP_STORE+ymd+nextValue;
        return seqCode;
    }

    /**
     * 暂存物项领用单编码
     *
     * @param sequenceCode temporary_item_req
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqTemporaryItemReq(CurrentUser user, String sequenceCode,String ftyCode) {
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String deptCode = userDeptList.size() == 0 ? "XXX" : userDeptList.get(0).getDeptCode();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
//                + EnumHspncCode.PRE_SEQ_DEPT_PRE.getCode() + deptCode + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_DRS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_ZC.getCode() + nextValue;
        return seqCode;
    }

    /**
     * 领料申请的编号
     *
     * @param sequenceCode material_req
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqMaterialReq(CurrentUser user, String sequenceCode,String ftyCode) {
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String deptCode = userDeptList.size() == 0 ? "XXX" : userDeptList.get(0).getDeptCode();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
//                + EnumHspncCode.PRE_SEQ_DEPT_PRE.getCode() + deptCode + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_APS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_NORMAL.getCode() + nextValue;
        return seqCode;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqEmergencyOrder(String sequenceCode, String ftyCode) {
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_APS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_J.getCode() + nextValue;
        return seqCode;
    }
    /**
     * 领料申请的编号 退转库
     *
     * @param sequenceCode material_req
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqMaterialReqTransferReturn(CurrentUser user, String sequenceCode,String ftyCode) {
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String deptCode = userDeptList.size() == 0 ? "XXX" : userDeptList.get(0).getDeptCode();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
//                + EnumHspncCode.PRE_SEQ_DEPT_PRE.getCode() + deptCode + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_APS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_Z.getCode() + nextValue;
        return seqCode;
    }
    /**
     * 领料出库单的编号
     *
     * @param sequenceCode material_out
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqMaterialOut(CurrentUser user, String sequenceCode,String ftyCode) {
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String deptCode = userDeptList.size() == 0 ? "XXX" : userDeptList.get(0).getDeptCode();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
//                + EnumHspncCode.PRE_SEQ_DEPT_PRE.getCode() + deptCode + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_DRS.getCode() + "-"
                + nextValue;
        return seqCode;
    }

    /**
     * 领料出库单的编号 退转库
     *
     * @param sequenceCode material_out
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqMaterialOutTransferReturn(CurrentUser user, String sequenceCode,String ftyCode) {
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String deptCode = userDeptList.size() == 0 ? "XXX" : userDeptList.get(0).getDeptCode();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
//                + EnumHspncCode.PRE_SEQ_DEPT_PRE.getCode() + deptCode + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_DRS.getCode() + "-"
                + EnumHspncCode.PRE_SEQ_Z.getCode() 
                + nextValue;
        return seqCode;
    }

    /**
     * 采购入库质检会签单的编号 inspect_order_purchase
     * @param user 当前用户
     * @param sequenceCode 编号的标识
     * @param ftyCode 工厂
     * @param receiptCode 订单号
     * @return 质检会签的单据号
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqInspectOrder(CurrentUser user, String sequenceCode,String ftyCode,String receiptCode){
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);

        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_UIR.getCode() + "-"
//                2023-05-27 跟单据号规则管理要求，移除质检会签和验收入库的采购订单号，由列表页面增加采购订单字段展示
//                + receiptCode + "-"
                + nextValue;
        return seqCode;
    }

    /**
     * 退库质检会签单的编号 inspect_order_return
     * @param user 当前用户
     * @param sequenceCode 编号的标识
     * @param ftyCode 工厂
     * @param receiptCode 订单号
     * @return 质检会签的单据号
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqReturnInspectOrder(CurrentUser user, String sequenceCode,String ftyCode,String receiptCode){
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);

        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_IRI.getCode() + "-"
//                2023-05-27 跟单据号规则管理要求，移除质检会签和验收入库的采购订单号，由列表页面增加采购订单字段展示
//                + receiptCode + "-"
                + EnumHspncCode.PRE_SEQ_T.getCode()
                + nextValue;
        return seqCode;
    }

    /**
     * 退转库质检会签单的编号 inspect_order_return_transfer
     * @param user 当前用户
     * @param sequenceCode 编号的标识
     * @param ftyCode 工厂
     * @param receiptCode 订单号
     * @return 质检会签的单据号
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextReturnTransferSeqInspectOrder(CurrentUser user, String sequenceCode,String ftyCode,String receiptCode){
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);

        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_IRI.getCode() + "-"
//                2023-05-27 跟单据号规则管理要求，移除质检会签和验收入库的采购订单号，由列表页面增加采购订单字段展示
//                + receiptCode + "-"
                + EnumHspncCode.PRE_SEQ_Z.getCode()
                + nextValue;
        return seqCode;
    }

    /**
     * 验收入库单编号 inspect_input
     * @param user
     * @param sequenceCode 单据类型code
     * @param ftyCode 工厂编号
     * @param receiptCode 采购订单号
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSeqInspectInput(CurrentUser user, String sequenceCode,String ftyCode,String receiptCode){
        // 部门编码
        List<MetaDataDeptOfficePO> userDeptList = user.getUserDeptList();
        String proType = getProType(ftyCode);
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_IIS.getCode() + "-"
//                2023-05-27 跟单据号规则管理要求，移除质检会签和验收入库的采购订单号，由列表页面增加采购订单字段展示
//                + receiptCode + "-"
                + nextValue;
        return seqCode;
    }


    /**
     * 按规则获取年月
     *
     * @return
     */
    private String getYM() {
        // 获取年份的后两文
        String year = new SimpleDateFormat("yy", Locale.CHINESE).format(new Date());
        // 获取月份
        LocalDateTime currentDate = LocalDateTime.now();
        int monthValue = currentDate.getMonthValue();
        String month = (monthValue < 10) ? "0" + monthValue : String.valueOf(monthValue);
        String ym = year + month;
        return ym;
    }

    /**
     * 按规则获取年月日
     *
     * @return
     */
    private String getYMD() {
        Date date=new Date();
        // 获取年份的后两文
        String year = new SimpleDateFormat("yy", Locale.CHINESE).format(date);
        String monthDay = new SimpleDateFormat("MMdd", Locale.CHINESE).format(date);
        String ymd = year + monthDay;
        return ymd;
    }
    /**
     * 根据工厂去判断高温堆、压水堆
     * @param ftyCode
     * @return
     */
    private String getProType(String ftyCode){
        String type = "";
        switch (ftyCode){
            case "J046":
            case "W046":
            case "Y046":
            case "S046":
            case "Z046":
                type = EnumHspncCode.PRE_SEQ_HS_TYPE_HIGH.getCode();
                break;
            case "J047":
            case "W047":
            case "Y047":
            case "S047":
            case "Z047":
                type = EnumHspncCode.PRE_SEQ_HS_TYPE_PRESSURIZED.getCode();
                break;
            default:
                type = EnumHspncCode.PRE_SEQ_HS_PRE.getCode() + RandomUtil.randomInt(2);
        }
        return type;
    }

    /* ============================ 华能单据编号的编码获取结束 ============================ */


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<String> getNextNValue(String sequenceCode,Integer num) {
        List<String> codeList = new ArrayList<>();
        String code = sequenceMapper.selectNextNValue(sequenceCode,num);
        SysSequencePrefixVO pre = sequenceMapper.getPrefix(sequenceCode);
        String prefix = pre.getPrefix();
        String intCode = code.substring(prefix.length(), code.length());

        Integer intValue = UtilObject.getIntOrZero(intCode);

        for(int a = intValue-num+1;a<=intValue;a++){

            int zeroLength = pre.getCodeLen()-prefix.length();
            String nextCode = prefix + String.format(String.format("%%0%dd",zeroLength),a);
            codeList.add(nextCode);
        }

        return codeList;
    }

    /**
     *  到货通知单
     * @param sequenceCode 编号的标识
     * @return 质检会签的单据号
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSequenceNoticeValue(String sequenceCode, String ftyCode){
        // 部门编码
        String ym = getYM();
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);
        String proType = getProType(ftyCode);
        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_AAN.getCode() + "-"
                + nextValue;
        return seqCode;
    }

    /**
     *  到货登记单
     * @param sequenceCode 编号的标识
     * @return 质检会签的单据号
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSequenceRegisterValue(String sequenceCode, String ftyCode){
        // 部门编码
        String ym = getYM();
        String proType = getProType(ftyCode);
        // 获取流水号
        String nextValue = sequenceMapper.selectNextValueCodeMonth(sequenceCode);

        // 组装编码
        String seqCode = proType + "-"
                + EnumHspncCode.PRE_SEQ_DATE_PRE_FIVE.getCode() + ym + "-"
                + EnumHspncCode.PRE_SEQ_PIR.getCode() + "-"
                + nextValue;
        return seqCode;
    }

    /**
     * 获取流水号-昌江项目编码
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSequenceYear(String sequenceCode){
        String nextValue = sequenceMapper.selectNextValueCodeYear(sequenceCode);
        // 组装编码
        String year = new SimpleDateFormat("yy", Locale.CHINESE).format(new Date());
        String seqCode = year + nextValue;
        return seqCode;
    }

    /**
     * 获取4位流水号
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSequence(String sequenceCode){
        return sequenceMapper.selectNextValueCodeYear(sequenceCode);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextSequenceCodeMonth(String sequenceCode) {
        return sequenceMapper.selectNextValueCodeMonth(sequenceCode);
    }


    /**
     * 设置业务参数
     */
    public static void setReceiptParam(JSONObject targeJsonObject, JSONObject sourceJsonObject) {
        if (UtilObject.isNull(targeJsonObject) || UtilObject.isNull(sourceJsonObject)) {
            return;
        }
        String receiptId = sourceJsonObject.getString("receiptId");
        String receiptCode = sourceJsonObject.getString("receiptCode");
        String receiptType = sourceJsonObject.getString("receiptType");
        String receiptTypeName = sourceJsonObject.getString("receiptTypeName");
        Integer interfaceType = sourceJsonObject.getInteger("interfaceType");

        ReceiptPO receipt = new ReceiptPO();
        receipt.setReceiptId(receiptId);
        receipt.setReceiptCode(receiptCode);
        receipt.setReceiptType(receiptType);
        receipt.setReceiptTypeName(receiptTypeName);
        receipt.setInterfaceType(interfaceType);
        if (UtilString.isNotNullOrEmpty(receiptType) && UtilString.isNullOrEmpty(receiptTypeName)) {
            receipt.setReceiptTypeName(
                    i18nTextCommonService.getNameMessage(Const.DEFAULT_LANG_CODE, "receiptType", receiptType));
        }
        targeJsonObject.put("RECEIPT_PO", receipt);
    }
    
    /**
     * 设置业务参数
     */
    public static void setReceiptParam(JsonObject targeJsonObject, JsonObject sourceJsonObject) {
        if (UtilObject.isNull(targeJsonObject) || UtilObject.isNull(sourceJsonObject)) {
            return;
        }
        String receiptId = sourceJsonObject.get("receiptId").getAsString();
        String receiptCode = sourceJsonObject.get("receiptCode").getAsString();
        String receiptType = sourceJsonObject.get("receiptType").getAsString();
        String receiptTypeName = Const.STRING_EMPTY;
        if (sourceJsonObject.has("receiptTypeName")) {
            receiptTypeName = sourceJsonObject.get("receiptTypeName").getAsString();
        }
        Integer interfaceType = sourceJsonObject.get("interfaceType").getAsInt();

        JsonObject receipt = new JsonObject();
        receipt.addProperty("receiptId", receiptId);
        receipt.addProperty("receiptCode", receiptCode);
        receipt.addProperty("receiptType", receiptType);
        receipt.addProperty("receiptTypeName", receiptTypeName);
        receipt.addProperty("interfaceType", interfaceType);
        if (UtilString.isNotNullOrEmpty(receiptType) && UtilString.isNullOrEmpty(receiptTypeName)) {
            receipt.addProperty("receiptTypeName", i18nTextCommonService.getNameMessage(Const.DEFAULT_LANG_CODE, "receiptType", receiptType));
        }
        targeJsonObject.add("RECEIPT_PO", receipt);
    }

    /**
     * 设置业务参数
     */
    public static ReceiptPO setReceiptParam(Long receiptId, String receiptCode, Integer receiptType, String receiptTypeName,Integer interfaceType) {
        ReceiptPO receipt = new ReceiptPO();
        receipt.setReceiptId(UtilNumber.isNotEmpty(receiptId) ? String.valueOf(receiptId) : Const.STRING_EMPTY);
        receipt.setReceiptCode(receiptCode);
        receipt.setReceiptType(UtilNumber.isNotEmpty(receiptType) ? String.valueOf(receiptType) : Const.STRING_EMPTY);
        receipt.setReceiptTypeName(receiptTypeName);
        receipt.setInterfaceType(interfaceType);

        if (UtilNumber.isNotEmpty(receiptType) && UtilString.isNullOrEmpty(receipt.getReceiptTypeName())) {
            receipt.setReceiptTypeName(i18nTextCommonService.getNameMessage(Const.DEFAULT_LANG_CODE, "receiptType", receipt.getReceiptType()));
        }

        return receipt;
    }
}
