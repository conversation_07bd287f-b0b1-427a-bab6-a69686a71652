package com.inossem.wms.bizbasis.sys.controller;


import com.inossem.wms.bizbasis.sys.service.biz.SysNativeSqlOmDbDataUrdService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.sys.om.dto.SysNativeSqlDbDataUrdDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 系统运维-数据库原生sql  数据urd(修改，读取，删除) 可以在页面实现任意表的urd操作，方便生产环境运维工作 前端控制器
 * </p>
 */
@RestController
@Api(tags = "系统运维-数据库原生sql urd操作")
public class SysNativeSqlDbDataUrdConfigController {

    @Autowired
    SysNativeSqlOmDbDataUrdService sysNativeSqlOmDbDataUrdService;

    /**
     * 原生sql查询
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "原生sql查询", tags = {"原生sql查询"})
    @PostMapping(path = "/sys-na-sql/db-data/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<SysNativeSqlDbDataUrdDTO>> getDataList(@RequestBody SysNativeSqlDbDataUrdDTO po, BizContext ctx) {

        sysNativeSqlOmDbDataUrdService.getDataList(ctx);

        return BaseResult.success(new SingleResultVO<>( ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO)));
    }

    /**
     * 原生sql执行
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "原生sql执行", tags = {"原生sql执行"})
    @PostMapping(path = "/sys-na-sql/db-data/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult updateData(@RequestBody SysNativeSqlDbDataUrdDTO po, BizContext ctx) {

        sysNativeSqlOmDbDataUrdService.updateData(ctx);

        return BaseResult.success("执行成功");
    }

    /**
     * 原生sql导出
     * @param po
     * @param ctx
     */
    @ApiOperation(value = "原生sql导出", tags = {"原生sql导出"})
    @PostMapping(path = "/sys-na-sql/db-data/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public void exportExcel(@RequestBody SysNativeSqlDbDataUrdDTO po, BizContext ctx) {
        sysNativeSqlOmDbDataUrdService.exportExcel(ctx);
    }

}

