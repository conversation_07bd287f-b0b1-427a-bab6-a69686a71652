package com.inossem.wms.bizbasis.account.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.account.dto.DicAccountPeriodDTO;
import com.inossem.wms.common.model.account.po.DicAccountPeriodSavePO;
import com.inossem.wms.common.model.account.po.DicAccountPeriodSearchPO;
import com.inossem.wms.common.model.account.vo.DicAccountPeriodPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.bizbasis.account.service.biz.AccountPeriodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 账期管理 controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "账期管理")
public class AccountPeriodController {

    @Autowired
    private AccountPeriodService accountPeriodService;

    /**
     * 获取账期列表
     *
     * @param po 账期查询入参类
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 账期集合列表
     */
    @ApiOperation(value = "获取账期列表", tags = {"账期管理"})
    @PostMapping(path = "/account-period/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicAccountPeriodPageVO>> getPage(@RequestBody DicAccountPeriodSearchPO po, BizContext ctx) {
        return BaseResult.success(accountPeriodService.getPage(ctx));
    }

    /**
     * 查看账期详情
     *
     * @param po 账期查询入参类
     * @param ctx 上下文对象
     * <AUTHOR>
     * @return 账期详情
     */
    @ApiOperation(value = "按照账期id查找账期", tags = {"账期管理"})
    @PostMapping(path = "/account-period/ids", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicAccountPeriodDTO>> query(@RequestBody DicAccountPeriodSearchPO po, BizContext ctx) {
        return BaseResult.success(accountPeriodService.get(ctx));
    }

    /**
     * 新增账期
     *
     * <AUTHOR>
     * @param po 账期保存入参类
     * @param ctx 上下文对象
     * @return 处理结果
     */
    @ApiOperation(value = "新增账期信息", notes = "对账期信息进行添加", tags = {"账期管理"})
    @PostMapping(path = "/account-period", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicAccountPeriodSavePO po, BizContext ctx) {
        // 储存存地点信息
        accountPeriodService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_ACCOUNT_PERIOD_SAVE_SUCCESS);
    }

    /**
     * 修改账期
     *
     * <AUTHOR>
     * @param po 账期保存入参类
     * @param ctx 上下文对象
     * @return 处理结果
     */
    @ApiOperation(value = "修改账期信息", notes = "对账期信息进行修改", tags = {"账期管理"})
    @PutMapping(path = "/account-period", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicAccountPeriodSavePO po, BizContext ctx) {
        // 储存存地点信息
        accountPeriodService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_ACCOUNT_PERIOD_SAVE_SUCCESS);
    }

    /**
     * 删除账期
     *
     * @param po 账期查询入参类
     * @param ctx 上下文对象
     * <AUTHOR>
     * @return 删除结果
     */
    @ApiOperation(value = "按照账期id删除账期", notes = "逻辑删除", tags = {"账期管理"})
    @DeleteMapping(path = "/account-period/ids", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@RequestBody DicAccountPeriodSearchPO po, BizContext ctx) {
        // 删除账期信息
        accountPeriodService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_ACCOUNT_PERIOD_DELETE_SUCCESS);
    }

}
