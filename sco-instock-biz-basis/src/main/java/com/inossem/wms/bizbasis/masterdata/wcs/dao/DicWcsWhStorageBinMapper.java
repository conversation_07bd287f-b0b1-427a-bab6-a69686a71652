package com.inossem.wms.bizbasis.masterdata.wcs.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.wcs.entity.DicWcsWhStorageBin;
import com.inossem.wms.common.model.wcs.po.DicWcsWhStorageBinSearchPO;
import com.inossem.wms.common.model.wcs.vo.DicWcsWhStorageBinPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***
 * <AUTHOR>
 * @date 2021-04-07 15:10:51
 **/
@Mapper
public interface DicWcsWhStorageBinMapper extends WmsBaseMapper<DicWcsWhStorageBin> {

    /**
     * 查询WCS仓位主数据分页列表
     *
     * @param page 分页参数
     * @param po 条件
     * @return
     */
    List<DicWcsWhStorageBinPageVO> getDicWcsWhStorageBinPageVOList(IPage<DicWcsWhStorageBinPageVO> page, @Param("po") DicWcsWhStorageBinSearchPO po);
}
