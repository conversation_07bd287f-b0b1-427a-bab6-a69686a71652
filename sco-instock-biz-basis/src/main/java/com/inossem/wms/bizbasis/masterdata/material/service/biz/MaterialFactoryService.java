package com.inossem.wms.bizbasis.masterdata.material.service.biz;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.common.service.biz.CheckDataService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryMaintainDataWrap;
import com.inossem.wms.bizbasis.spec.service.datawrap.BizSpecClassifyDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchImg;
import com.inossem.wms.common.model.common.DicDeleteCheckPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.tag.TagMapVo;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryUniqueKey;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFactory;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFactoryMaintain;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactoryImport;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySavePO;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySearchPO;
import com.inossem.wms.common.model.masterdata.mat.fty.vo.DicMaterialFactoryPageVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryDataWrap;
import com.inossem.wms.common.util.excel.UtilExcel;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MaterialFactoryService {
    @Autowired
    protected CheckDataService checkDataService;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected BizSpecClassifyDataWrap bizSpecClassifyDataWrap;
    @Autowired
    private DicMaterialFactoryDataWrap dicMaterialFactoryDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private EditCacheService editCacheService;
    @Autowired
    private BatchImgService batchImgService;
    @Autowired
    private DicMaterialFactoryMaintainDataWrap dicMaterialFactoryMaintainDataWrap;

    /**
     * 获取物料工厂列表
     *
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 物料工厂详情列表
     */
    public PageObjectVO<DicMaterialFactoryPageVO> getPage(BizContext ctx) {
        DicMaterialFactorySearchPO po = ctx.getContextData("po");
        log.info("获取物料工厂列表 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new DicMaterialFactorySearchPO();
        }

        // 查询条件设置
        QueryWrapper<DicMaterialFactorySearchPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatCode()), DicMaterialFactorySearchPO::getMatCode, po.getMatCode());
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getFtyId()), DicMaterialFactorySearchPO::getFtyId, po.getFtyId());
        IPage<DicMaterialFactoryPageVO> page = po.getPageObj(DicMaterialFactoryPageVO.class);
        dicMaterialFactoryDataWrap.getDicMaterialFactoryPageVOList(page, wrapper);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 获取物料工厂详情
     *
     * @param ctx 上下文对象
     * @return 物料工厂详情
     *
     */
    public SingleResultVO<DicMaterialFactoryDTO> get(BizContext ctx) {
        Long matFactoryId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("物料工厂详情查询 matFactoryId：{}", matFactoryId);
        if (UtilNumber.isEmpty(matFactoryId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        DicMaterialFactory materialFactory = dicMaterialFactoryDataWrap.getById(matFactoryId);
        log.info("物料工厂id：{}，详情：{}", matFactoryId, JSONObject.toJSONString(matFactoryId));
        DicMaterialFactoryDTO dto = UtilBean.newInstance(materialFactory, DicMaterialFactoryDTO.class);
        dataFillService.fillAttr(dto);
        // 填充图片
        this.fillImage(dto);
        return new SingleResultVO<>(dto);
    }

    private void fillImage(DicMaterialFactoryDTO dto) {
        List<BizBatchImg> batchImages = batchImgService.getBatchImgListByMatIdAndFtyId(dto.getMatId(), dto.getFtyId());
        List<BizBatchImgDTO> bizBatchImgDTOS = UtilCollection.toList(batchImages, BizBatchImgDTO.class);
        dataFillService.fillAttr(bizBatchImgDTOS);
        dto.setImgList(bizBatchImgDTOS);
    }

    /**
     * 根据物料id，工厂id获取工厂物料主数据
     * @param matId
     * @param ftyId
     * @return
     */
    public DicMaterialFactoryDTO getByUniqueKey(Long matId, Long ftyId) {
        DicMaterialFactory mf = dicMaterialFactoryDataWrap.getOne(new QueryWrapper<DicMaterialFactory>()
                .lambda()
                .eq(DicMaterialFactory::getMatId, matId)
                .eq(DicMaterialFactory::getFtyId, ftyId));
        DicMaterialFactoryDTO mdfDTO = UtilBean.newInstance(mf, DicMaterialFactoryDTO.class);
        dataFillService.fillAttr(mdfDTO);
        return mdfDTO;
    }

    /**
     * 根据物料id，工厂id获取工厂物料主数据
     * @param keys
     * @return
     */
    public Map<DicMaterialFactoryUniqueKey, DicMaterialFactoryDTO> getByUniqueKeys(Collection<DicMaterialFactoryUniqueKey> keys) {

        // 拼装形式如：select * from dic_material_factory where (mat_id = xx1 and fty_id = yy1) or (mat_id = xx2 and fty_id = yy2) or ..
        List<DicMaterialFactory> mfList = dicMaterialFactoryDataWrap.list(new QueryWrapper<DicMaterialFactory>()
                .lambda()
                .and(qw -> {
                    keys.forEach(key -> {
                        // 内部先拼接 (mat_id = xx and fty_id = yy) 再通过or组合
                        qw.or(innerQw -> innerQw.eq(DicMaterialFactory::getMatId, key.getMatId()).eq(DicMaterialFactory::getFtyId, key.getFtyId()));
                    });
                }));
        List<DicMaterialFactoryDTO> mfDTOs = UtilCollection.toList(mfList, DicMaterialFactoryDTO.class);
        dataFillService.fillAttr(mfDTOs);

        // 组装成key-value的Map结构
        Map<DicMaterialFactoryUniqueKey, DicMaterialFactoryDTO> resultMap = new HashMap<>();
        for (DicMaterialFactoryDTO mfDTO : mfDTOs) {
            DicMaterialFactoryUniqueKey key = new DicMaterialFactoryUniqueKey();
            key.setMatId(mfDTO.getMatId()).setFtyId(mfDTO.getFtyId());
            resultMap.put(key, mfDTO);
        }
        return resultMap;
    }

    /**
     * 新增或修改方法
     *
     * @param ctx 上下文对象
     *
     */
    public void addOrUpdate(BizContext ctx) {
        DicMaterialFactorySavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        log.info("新增/修改物料工厂信息 po：{}", JSONObject.toJSONString(po));
        if (null == po.getMaterialFactoryInfo()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilCollection.isNotEmpty(po.getMaterialFactoryInfo().getMaintainList())) {
            for (DicMaterialFactoryMaintain maintain : po.getMaterialFactoryInfo().getMaintainList()) {
                if (EnumRealYn.TRUE.getIntValue().equals(maintain.getIsMaintenanceEnable())
                        && UtilNumber.isEmpty(maintain.getMaintenanceCycle())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
            }
        }
        DicMaterialFactoryDTO dto = po.getMaterialFactoryInfo();
        // 根据是否存在ID判断是否为新增
        if (UtilNumber.isEmpty(dto.getId())) {
            // 新增
            dto.setCreateUserId(currentUser.getId());
        }
        // 修改
        else {
            DicMaterialFactory materialFactory = dicMaterialFactoryDataWrap.getById(dto.getId());
            if (Objects.isNull(materialFactory)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
            }
            dto.setMatId(materialFactory.getMatId());
            dto.setFtyId(materialFactory.getFtyId());
        }
        dto.setModifyUserId(currentUser.getId());

        DicMaterialFactory matFactory = UtilBean.newInstance(dto, DicMaterialFactory.class);
        // 更新图片(只更新图片导致dicMaterialFactoryDataWrap.saveOrUpdate(matFactory) 实际返回false ,因为数据库字段没有任何更新，所以提到上面来)
        this.saveImage(dto, currentUser);

        boolean successFlag = dicMaterialFactoryDataWrap.saveOrUpdate(matFactory);
        // 更新缓存
        List<DicMaterialFactoryDTO> list =new ArrayList<>();

        // 更新物料工厂维保信息
        if (UtilCollection.isNotEmpty(dto.getMaintainList())) {

            // 逻辑删除不在当前列表里的数据
            QueryWrapper<DicMaterialFactoryMaintain> removeWrapper = new QueryWrapper<>();
            removeWrapper.lambda().eq(DicMaterialFactoryMaintain::getMatFtyId, matFactory.getId());
            removeWrapper.lambda().notIn(DicMaterialFactoryMaintain::getId, dto.getMaintainList().stream().map(DicMaterialFactoryMaintain::getId).collect(Collectors.toList()));
            dicMaterialFactoryMaintainDataWrap.remove(removeWrapper);

            dto.getMaintainList().forEach(dicMaterialFactoryMaintain -> {
                dicMaterialFactoryMaintain.setMatFtyId(matFactory.getId());
            });
            dicMaterialFactoryMaintainDataWrap.saveOrUpdateBatchOptimize(dto.getMaintainList());
        }

        DicMaterialFactory matupdate = dicMaterialFactoryDataWrap.getById(dto.getId());
        DicMaterialFactoryDTO dto1 = UtilBean.newInstance(matupdate, DicMaterialFactoryDTO.class);
        dataFillService.fillAttr(dto1);
        list.add(dto1);
        dictionaryService.refreshDicMaterialFactoryByUniqueKey(list);

        log.info("物料工厂：{}，保存成功", dto.getFtyCode());
    }

    private void saveImage(DicMaterialFactoryDTO dto, CurrentUser currentUser) {
        if (UtilCollection.isNotEmpty(dto.getImgList())) {
            for (BizBatchImgDTO bizBatchImgDTO : dto.getImgList()) {
                bizBatchImgDTO.setId(null);
                bizBatchImgDTO.setMatId(dto.getMatId());
                bizBatchImgDTO.setFtyId(dto.getFtyId());
                bizBatchImgDTO.setImgBizType(EnumImageBizType.FACTORY_MATERIAL.getValue());
                bizBatchImgDTO.setCreateUserId(currentUser.getId());
            }
        }
        batchImgService.multiSaveBizBatchImg(dto.getImgList());
    }

    /**
     * 删除方法
     *
     *
     */
    public void remove(BizContext ctx) {
        Long matFactoryId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询物料工厂信息 ******** */
        log.info("删除物料工厂 matFactoryId：{}", matFactoryId);
        if (UtilNumber.isEmpty(matFactoryId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验是否可删除
        checkDataService.dicDeleteCheck(new DicDeleteCheckPO(EnumCheckType.FACTORY.getValue(), matFactoryId));
        // 逻辑删除
        if (dicMaterialFactoryDataWrap.removeById(matFactoryId)) {
            log.info("物料工厂：{}，删除成功", matFactoryId);
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_FACTORY_DELETE_FAILURE);
        }
    }


    /**
     * 查询标签类型下拉
     *
     * @return 标签类型下拉框
     *
     */
    public MultiResultVO<TagMapVo> getDown() {
        return new MultiResultVO<>(EnumTagType.toList());
    }

    /**
     * 物料工厂EXCEL导入
     * @param ctx
     */
    public void importMaterialFactory(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        CurrentUser user = ctx.getCurrentUser();

        try {
            //获取EXCEL数据
            List<DicMaterialFactoryImport> materialFactoryList = (List<DicMaterialFactoryImport>) UtilExcel.readExcelData(file.getInputStream(), DicMaterialFactoryImport.class);
            //判断EXCEL中主键重复的值
            Map<String, List<DicMaterialFactoryImport>> checkMap = materialFactoryList.stream().collect(Collectors.groupingBy(item -> item.getFtyCode() + "-" + item.getMatCode()));
            for (String key : checkMap.keySet()) {
                List<DicMaterialFactoryImport> checkList = checkMap.get(key);
                if(checkList.size() > 1){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE,key);
                }
            }

            materialFactoryList.forEach(
                    materialFactory -> {
                        //工厂校验
                        Long ftyId = dictionaryService.getFtyIdCacheByCode(materialFactory.getFtyCode());
                        if(UtilNumber.isEmpty(ftyId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST,materialFactory.getFtyCode());
                        }
                        //物料校验
                        Long matId = dictionaryService.getMatIdByMatCode(materialFactory.getMatCode());
                        if(UtilNumber.isEmpty(matId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_ALERT_NOT_EXIST,materialFactory.getMatCode());
                        }

                        //物料仓库已存在校验 ： 根据工厂、物料
                        DicMaterialFactoryDTO  dictionaryMatFty = dictionaryService.getDicMaterialFactoryByUniqueKey(matId,ftyId);
                        if(dictionaryMatFty != null){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_EXIST,dictionaryMatFty.getFtyCode() + "-" + dictionaryMatFty.getMatCode());
                        }
                        materialFactory.setMatId(matId);
                        materialFactory.setFtyId(ftyId);
                        materialFactory.setCreateUserId(user.getId());
                        materialFactory.setModifyUserId(user.getId());
                    }
            );
            //批量插入数据
            dicMaterialFactoryDataWrap.saveBatchDto(materialFactoryList);
            //刷新缓存
            editCacheService.refreshDicMaterialFactoryCache();
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

}
