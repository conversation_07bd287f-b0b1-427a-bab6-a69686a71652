<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.org.dao.DicFactoryMapper">

    <select id="selectDicFactoryPageVOList"
            resultType="com.inossem.wms.common.model.org.factory.vo.DicFactoryPageVO">
        select df.id,
               df.fty_code,
               df.corp_id,
               df.fty_name,
               df.address,
               df.is_delete,
               df.create_time,
               df.modify_time,
               df.create_user_id,
               df.modify_user_id,
               dc.corp_code, dc.corp_name,
               su.user_code create_user_code,
               su.user_name create_user_name
        from dic_factory df
                 inner join dic_corp dc on df.corp_id = dc.id and df.is_delete = 0
                 inner join sys_user su on df.create_user_id = su.id ${ew.customSqlSegment}
    </select>
</mapper>
