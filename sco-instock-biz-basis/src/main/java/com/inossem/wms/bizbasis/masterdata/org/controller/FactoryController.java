package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.factory.po.DicFactorySavePO;
import com.inossem.wms.common.model.org.factory.po.DicFactorySearchPO;
import com.inossem.wms.common.model.org.factory.vo.DicFactoryPageVO;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.FactoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 工厂主数据表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@RestController
@Api(tags = "工厂管理")
public class FactoryController {

    @Autowired
    private FactoryService factoryService;

    /**
     * 获取工厂列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 工厂集合列表
     */
    @ApiOperation(value = "获取工厂列表", tags = {"工厂管理"})
    @PostMapping(path = "/org/factory/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicFactoryPageVO>> getPage(@RequestBody DicFactorySearchPO po, BizContext ctx) {
        return BaseResult.success(factoryService.getPage(ctx));
    }

    /**
     * 查看工厂详情
     * 
     * <AUTHOR>
     * @param id 工厂Id
     * @return 工厂详情
     */
    @ApiOperation(value = "按照工厂id查找工厂", tags = {"工厂管理"})
    @GetMapping(path = "/org/factory/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicFactoryDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(factoryService.get(ctx));
    }

    /**
     * 新增工厂
     * 
     * <AUTHOR>
     * @param po 工厂入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增工厂信息", notes = "对工厂信息进行添加、修改", tags = {"工厂管理"})
    @PostMapping(path = "/org/factory", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicFactorySavePO po, BizContext ctx) {
        // 储存工厂信息
        factoryService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_FACTORY_SAVE_SUCCESS, po.getFactoryInfo().getFtyCode());
    }

    /**
     * 修改工厂
     *
     * <AUTHOR>
     * @param po 工厂入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改工厂信息", notes = "对工厂信息进行添加、修改", tags = {"工厂管理"})
    @PutMapping(path = "/org/factory", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicFactorySavePO po, BizContext ctx) {
        // 储存工厂信息
        factoryService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_FACTORY_SAVE_SUCCESS, po.getFactoryInfo().getFtyCode());
    }

    /**
     * 删除工厂
     * 
     * @param id 工厂Id
     * <AUTHOR>
     * @param ctx 上下文对象
     * @return 删除结果
     */
    @ApiOperation(value = "按照工厂编码删除工厂", notes = "逻辑删除", tags = {"工厂管理"})
    @DeleteMapping(path = "/org/factory/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除工厂信息
        String ftyCode = factoryService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_FACTORY_DELETE_SUCCESS, ftyCode);
    }

    /**
     * 工厂导入
     * @param file 工厂excel
     * @param ctx 上下文对象
     * <AUTHOR>
     */
    @ApiOperation(value = "工厂导入", notes = "工厂导入", tags = {"工厂管理"})
    @PostMapping(path = "/org/factory/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestPart("file") MultipartFile file, BizContext ctx) {
        // 导入库存地点信息
        factoryService.importFactory(file, ctx);
        return BaseResult.success();
    }

}
