package com.inossem.wms.bizbasis.masterdata.org.service.biz;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.CheckDataService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicFactoryDataWrap;
import com.inossem.wms.common.cache.ICacheService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumCheckType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.DicDeleteCheckPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.factory.entity.DicFactory;
import com.inossem.wms.common.model.org.factory.po.DicFactoryImport;
import com.inossem.wms.common.model.org.factory.po.DicFactorySavePO;
import com.inossem.wms.common.model.org.factory.po.DicFactorySearchPO;
import com.inossem.wms.common.model.org.factory.vo.DicFactoryPageVO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FactoryService {

    @Autowired
    protected CheckDataService checkDataService;
    @Autowired
    private DicFactoryDataWrap dicFactoryDataWrap;
    @Autowired
    private EditCacheService editCacheService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    protected ICacheService cacheServiceImpl;

    /**
     * 获取工厂列表
     *
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1 15:41
     * @return 工厂详情列表
     */
    public PageObjectVO<DicFactoryPageVO> getPage(BizContext ctx) {
        DicFactorySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("工厂列表查询 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new DicFactorySearchPO();
        }
        // 查询条件设置
        QueryWrapper<DicFactorySearchPO> wrapper = new QueryWrapper<>();
        // 拼装参数(boot根据配置，自动生成查询条件)
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getFtyCode()), DicFactorySearchPO::getFtyCode, po.getFtyCode())
            .like(UtilString.isNotNullOrEmpty(po.getFtyName()), DicFactorySearchPO::getFtyName, po.getFtyName());
        IPage<DicFactoryPageVO> page = po.getPageObj(DicFactoryPageVO.class);
        dicFactoryDataWrap.getDicFactoryPageVOList(page, wrapper);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 获取工厂详情
     * 
     * @param ctx 上下文对象
     * @return 工厂详情
     *
     */
    public SingleResultVO<DicFactoryDTO> get(BizContext ctx) {
        Long factoryId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("工厂详情 factoryId：{}", factoryId);
        if (UtilNumber.isEmpty(factoryId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        DicFactory dicFactory = dicFactoryDataWrap.getById(factoryId);
        log.info("工厂id：{}，详情：{}", factoryId, JSONObject.toJSONString(dicFactory));
        DicFactoryDTO dto = UtilBean.newInstance(dicFactory, DicFactoryDTO.class);
        // 填充数据
        dataFillService.fillAttr(dto);
        return new SingleResultVO<>(dto);
    }

    /**
     * 新增或修改方法
     * 
     * @param ctx 上下文对象
     *
     */
    public void addOrUpdate(BizContext ctx) {
        DicFactorySavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        log.info("工厂新增/修改 po：{}", JSONObject.toJSONString(po));
        if (null == po.getFactoryInfo()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicFactoryDTO dto = po.getFactoryInfo();

        if (UtilString.isNullOrEmpty(dto.getFtyCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 根据是否存在ID判断是否为新增
        if (UtilNumber.isEmpty(dto.getId())) {
            // 新增
            dto.setCreateUserId(currentUser.getId());
            // 校验编码是否已存在
            DicFactory dicFactory = this.getFactoryByCode(dto.getFtyCode());
            if (dicFactory != null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_ALREADY_EXIST);
            }
        }
        // 修改【校验id是否存在】
        else if (null == dicFactoryDataWrap.getById(dto.getId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_SUCH_FACTORY);
        }
        dto.setModifyUserId(currentUser.getId());

        DicFactory dicFactory = UtilBean.newInstance(dto, DicFactory.class);

        if (dicFactoryDataWrap.saveOrUpdate(dicFactory)) {
            log.info("工厂：{}，保存成功", dicFactory.getFtyCode());
            // 刷新缓存
            editCacheService.refreshAllFtyCache();
        }
    }

    /**
     * 根据工厂编码查工厂对象
     * 
     * @param ftyCode 工厂编码
     * @return DicFactory
     */
    public DicFactory getFactoryByCode(String ftyCode) {
        QueryWrapper<DicFactory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicFactory::getFtyCode, ftyCode);
        return dicFactoryDataWrap.getOne(queryWrapper);
    }

    /**
     * 删除方法
     * 
     * @param ctx 上下文对象
     *
     */
    public String remove(BizContext ctx) {
        Long factoryId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("工厂删除 factoryId：{}", factoryId);
        if (UtilNumber.isEmpty(factoryId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验是否可删除
        checkDataService.dicDeleteCheck(new DicDeleteCheckPO(EnumCheckType.FACTORY.getValue(), factoryId));
        String ftyCode = dicFactoryDataWrap.getById(factoryId).getFtyCode();
        // 逻辑删除
        if (dicFactoryDataWrap.removeById(factoryId)) {
            log.info("工厂：{}，删除成功", factoryId);
            // 从缓存中删除
            editCacheService.deleteFtyCacheById(factoryId);
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_DELETE_FAILURE, ftyCode);
        }
        return ftyCode;
    }

    /**
     * 库存地点初始化导入
     *
     * @param ctx 上下文对象
     */
    public void importFactory(MultipartFile file, BizContext ctx) {
        //获取当前用户
        CurrentUser user = ctx.getCurrentUser();
        Map<String,String> factoryMap = new HashMap<String,String>();
        try {
            List<DicFactoryImport> factoryImportList = (List<DicFactoryImport>) UtilExcel.readExcelData(file.getInputStream(), DicFactoryImport.class);
            factoryImportList.forEach(
                    factory -> {
                        Long corpId = dictionaryService.getCorpIdCacheByCode(factory.getCorpCode());
                        if(UtilNumber.isEmpty(corpId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_CORP_NOT_EXIST,factory.getCorpCode());
                        }
                        // 判断EXCEL中是否存在相同的工厂编码
                        if(UtilString.isNotNullOrEmpty(factoryMap.get(factory.getFtyCode()))){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE,factory.getFtyCode());
                        }else{
                            factoryMap.put(factory.getFtyCode(),factory.getFtyCode());
                        }
                        // 判断EXCEL中公司编码是否在缓存中存在
                        Long ftyId = dictionaryService.getFtyIdCacheByCode(factory.getFtyCode());
                        if(UtilNumber.isNotEmpty(ftyId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_ALREADY_EXIST,factory.getFtyCode());
                        }
                        factory.setCorpId(corpId);
                        factory.setCreateUserId(user.getId());
                        factory.setModifyUserId(user.getId());
                    }
            );
            if (dicFactoryDataWrap.saveBatchDto(factoryImportList)) {
                // 放入缓存
                List<Long> ftyIdList = factoryImportList.stream().map(DicFactoryImport::getId).collect(Collectors.toList());
                List<DicFactory> ftyList = dicFactoryDataWrap.listByIds(ftyIdList);
                List<DicFactoryDTO> ftyDTOList = UtilCollection.toList(ftyList,DicFactoryDTO.class);
                for (DicFactoryDTO factory : ftyDTOList) {
                    cacheServiceImpl.put(Const.CACHE_FACTORY, factory.getId().toString(), factory);
                    cacheServiceImpl.put(Const.CACHE_FACTORY_ID, factory.getFtyCode().toUpperCase(), factory.getId());
                }
            }
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }
}
