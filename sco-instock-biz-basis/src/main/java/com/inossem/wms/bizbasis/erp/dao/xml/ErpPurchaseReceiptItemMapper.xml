<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.erp.dao.ErpPurchaseReceiptItemMapper">

    <insert id="saveOrUpdateByHeadIdRId">
        INSERT INTO `erp_purchase_receipt_item` (
        `id`,
        `head_id`,
        `rid`,
        `contract_code`,
        `contract_rid`,
        `contract_name`,
        `customer_code`,
        `customer_name`,
        `require_application_code`,
        `require_application_rid`,
        `purchase_application_code`,
        `purchase_application_rid`,
        `brandmodel`,
        `delivery_date_plan`,
        `need_dept_code`,
        `receipt_qty`,
        `submit_qty`,
        `inspect_qty`,
        `can_delivery_qty`,
        `batch_erp`,
        `price`,
        `money`,
        `unit_id`,
        `corp_id`,
        `fty_id`,
        `wh_id`,
        `location_id`,
        `mat_id`,
        `move_type_id`,
        `apply_user_code`,
        `apply_user_name`,
        `apply_user_dept_code`,
        `apply_user_dept_name`,
        `apply_user_office_code`,
        `apply_user_office_name`,
        `apply_company`,
        `purchase_user_code`,
        `purchase_user_name`,
        `purchase_group_code`,
        `purchase_group_name`,
        `purchase_organization_code`,
        `purchase_organization_name`,
        `spec_stock`,
        `spec_stock_code`,
        `spec_stock_name`,
        `supplier_code`,
        `supplier_name`,
        `create_time`,
        `modify_time`
        )
        VALUES
        <foreach collection="list" item="item" separator="," >
            (
            #{item.`id`},
            #{item.`headId`},
            #{item.`rId`},
            #{item.`contractCode`},
            #{item.`contractRid`},
            #{item.`contractName`},
            #{item.`customerCode`},
            #{item.`customerName`},
            #{item.`requireApplicationCode`},
            #{item.`requireApplicationRid`},
            #{item.`purchaseApplicationCode`},
            #{item.`purchaseApplicationRid`},
            #{item.`brandmodel`},
            #{item.`deliveryDatePlan`},
            #{item.`needDeptCode`},
            #{item.`receiptQty`},
            #{item.`submitQty`},
            #{item.`inspectQty`},
            #{item.`canDeliveryQty`},
            #{item.`batchErp`},
            #{item.`price`},
            #{item.`money`},
            #{item.`unitId`},
            #{item.`corId`},
            #{item.`ftyId`},
            #{item.`whId`},
            #{item.`locationId`},
            #{item.`matId`},
            #{item.`moveTypeId`},
            #{item.`applyUserCode`},
            #{item.`applyUserName`},
            #{item.`applyUserDeptCode`},
            #{item.`applyUserDeptName`},
            #{item.`applyUserOfficeCode`},
            #{item.`applyUserOfficeName`},
            #{item.`applyCompany`},
            #{item.`purchaseUserCode`},
            #{item.`purchaseUserName`},
            #{item.`purchaseGroupCode`},
            #{item.`purchaseGroupName`},
            #{item.`purchaseOrganizationCode`},
            #{item.`purchaseOrganizationName`},
            #{item.`specStock`},
            #{item.`specStockCode`},
            #{item.`specStockName`},
            #{item.`supplierCode`},
            #{item.`supplierName`},
            #{item.`createTime`},
            #{item.`modifyTime`}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        <if test="head_id != null and head_id != '' ">
            `head_id` =  VALUES(`head_id`),
        </if>

        <if test="rid != null and rid != '' ">
            `rid`= VALUES(`rid`) ,
        </if>

        <if test="contract_code != null and contract_code != '' ">
            `contract_code` = VALUES(`contract_code`),
        </if>

        <if test="contract_rid != null and contract_rid != '' ">
            `contract_rid` = VALUES(`contract_rid`),
        </if>

        <if test="contract_name != null and contract_name != '' ">
            `contract_name` = VALUES(`contract_name`),
        </if>

        <if test="customer_code != null and customer_code != '' ">
            `customer_code` = VALUES(`customer_code`),
        </if>

        <if test="customer_name != null and customer_name != '' ">
            `customer_name` = VALUES(`customer_name`),
        </if>

        <if test="require_application_code != null and require_application_code != '' ">
            `require_application_code` = VALUES(`require_application_code`),
        </if>

        <if test="require_application_rid != null and require_application_rid != '' ">
            `require_application_rid` = VALUES(`require_application_rid`),
        </if>

        <if test="purchase_application_code != null and purchase_application_code != '' ">
            `purchase_application_code` = VALUES(`purchase_application_code`),
        </if>

        <if test="purchase_application_rid != null and purchase_application_rid != '' ">
            `purchase_application_rid` = VALUES(`purchase_application_rid`),
        </if>

        <if test="brandmodel != null and brandmodel != '' ">
            `brandmodel` = VALUES(`brandmodel`),
        </if>

        <if test="delivery_date_plan != null and delivery_date_plan != '' ">
            `delivery_date_plan` = VALUES(`delivery_date_plan`),
        </if>

        <if test="need_dept_code != null and need_dept_code != '' ">
            `need_dept_code` = VALUES(`need_dept_code`),
        </if>

        <if test="receipt_qty != null and receipt_qty != '' ">
            `receipt_qty` = VALUES(`receipt_qty`),
        </if>

        <if test="submit_qty != null and submit_qty != '' ">
            `submit_qty` = VALUES(`submit_qty`),
        </if>

        <if test="inspect_qty != null and inspect_qty != '' ">
            `inspect_qty` = VALUES(`inspect_qty`),
        </if>

        <if test="can_delivery_qty != null and can_delivery_qty != '' ">
            `can_delivery_qty` = VALUES(`can_delivery_qty`),
        </if>

        <if test="batch_erp != null and batch_erp != '' ">
            `batch_erp` = VALUES(`batch_erp`),
        </if>

        <if test="price != null and price != '' ">
            `price` = VALUES(`price`),
        </if>

        <if test="money != null and money != '' ">
            `money` = VALUES(`money`),
        </if>

        <if test="unit_id != null and unit_id != '' ">
            `unit_id` = VALUES(`unit_id`),
        </if>

        <if test="corp_id != null and corp_id != '' ">
            `corp_id` = VALUES(`corp_id`),
        </if>

        <if test="fty_id != null and fty_id != '' ">
            `fty_id` = VALUES(`fty_id`),
        </if>

        <if test="wh_id != null and wh_id != '' ">
            `wh_id` = VALUES(`wh_id`),
        </if>

        <if test="location_id != null and location_id != '' ">
            `location_id` = VALUES(`location_id`),
        </if>

        <if test="mat_id != null and mat_id != '' ">
            `mat_id` = VALUES(`mat_id`),
        </if>

        <if test="move_type_id != null and move_type_id != '' ">
            `move_type_id` = VALUES(`move_type_id`),
        </if>

        <if test="apply_user_code != null and apply_user_code != '' ">
            `apply_user_code` = VALUES(`apply_user_code`),
        </if>

        <if test="apply_user_name != null and apply_user_name != '' ">
            `apply_user_name` = VALUES(`apply_user_name`),
        </if>

        <if test="apply_user_dept_code != null and apply_user_dept_code != '' ">
            `apply_user_dept_code` = VALUES(`apply_user_dept_code`),
        </if>

        <if test="apply_user_dept_name != null and apply_user_dept_name != '' ">
            `apply_user_dept_name` = VALUES(`apply_user_dept_name`),
        </if>

        <if test="apply_user_office_code != null and apply_user_office_code != '' ">
            `apply_user_office_code` = VALUES(`apply_user_office_code`),
        </if>

        <if test="apply_user_office_name != null and apply_user_office_name != '' ">
            `apply_user_office_name` = VALUES(`apply_user_office_name`),
        </if>

        <if test="apply_company != null and apply_company != '' ">
            `apply_company` = VALUES(`apply_company`),
        </if>

        <if test="purchase_user_code != null and purchase_user_code != '' ">
            `purchase_user_code` = VALUES(`purchase_user_code`),
        </if>

        <if test="purchase_user_name != null and purchase_user_name != '' ">
            `purchase_user_name` = VALUES(`purchase_user_name`),
        </if>

        <if test="purchase_group_code != null and purchase_group_code != '' ">
            `purchase_group_code` = VALUES(`purchase_group_code`),
        </if>

        <if test="purchase_group_name != null and purchase_group_name != '' ">
            `purchase_group_name` = VALUES(`purchase_group_name`),
        </if>

        <if test="purchase_organization_code != null and purchase_organization_code != '' ">
            `purchase_organization_code` = VALUES(`purchase_organization_code`),
        </if>

        <if test="purchase_organization_name != null and purchase_organization_name != '' ">
            `purchase_organization_name` = VALUES(`purchase_organization_name`),
        </if>

        <if test="spec_stock != null and spec_stock != '' ">
            `spec_stock` = VALUES(`spec_stock`),
        </if>

        <if test="spec_stock_code != null and spec_stock_code != '' ">
            `spec_stock_code` = VALUES(`spec_stock_code`),
        </if>

        <if test="spec_stock_name != null and spec_stock_name != '' ">
            `spec_stock_name` = VALUES(`spec_stock_name`),
        </if>

        <if test="supplier_code != null and supplier_code != '' ">
            `supplier_code` = VALUES(`supplier_code`),
        </if>

        <if test="supplier_name != null and supplier_name != '' ">
            `supplier_name` = VALUES(`supplier_name`),
        </if>

        <if test="create_time != null and create_time != '' ">
            `create_time` = VALUES(`create_time`),
        </if>

        <if test="create_time != null and create_time != '' ">
            `modify_time` = VALUES(`modify_time`)
        </if>
    </insert>


    <!--获取采购验收单行项目-->
    <select id="getPurchaseReceiptItemList" resultType="com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO">
        SELECT
        head.receipt_code,
        head.erp_receipt_type,
        head.erp_receipt_type_name,
        head.erp_create_user_code,
        head.erp_create_user_name,
        head.erp_create_time,
        item.id,
        item.head_id,
        item.rid,
        item.contract_code,
        item.contract_rid,
        item.contract_name,
        item.contract_create_user_name,
        item.customer_code,
        item.customer_name,
        item.require_application_code,
        item.require_application_rid,
        item.purchase_application_code,
        item.purchase_application_rid,
        item.brandmodel,
        item.delivery_date_plan,
        item.delivery_time,
        item.need_dept_code,
        item.receipt_qty,
        item.submit_qty,
        item.inspect_qty,
        item.can_delivery_qty,
        item.can_output_qty,
        item.batch_erp,
        item.price,
        item.money,
        item.unit_id,
        item.corp_id,
        item.fty_id,
        item.wh_id,
        item.location_id,
        item.mat_id,
        item.move_type_id,
        item.apply_user_code,
        item.apply_user_name,
        item.apply_company,
        item.purchase_user_code,
        item.purchase_user_name,
        item.purchase_group_code,
        item.purchase_group_name,
        item.purchase_organization_code,
        item.purchase_organization_name,
        item.supplier_code,
        item.supplier_name,
        item.spec_stock,
        item.spec_stock_code,
        item.spec_stock_name,
        item.mat_name_back,
        item.subject_type
        FROM
        erp_purchase_receipt_head head INNER JOIN erp_purchase_receipt_item item ON head.id = item.head_id AND item.delete_tag = 0
        <where>
            <if test="po.purchaseReceiptCode != null and po.purchaseReceiptCode != '' ">
                AND head.receipt_code = #{po.purchaseReceiptCode}
            </if>
            <if test="po.contractCode != null and po.contractCode != '' ">
                AND item.contract_code = #{po.contractCode}
            </if>
            <if test="po.contractName != null and po.contractName != '' ">
                AND item.contract_name LIKE CONCAT('%', #{po.contractName}, '%')
            </if>
            <if test="po.erpCreateStartTime != null ">
                AND date_format(head.erp_create_time,'%Y%m%d') <![CDATA[ >= ]]> date_format(#{po.erpCreateStartTime},'%Y%m%d')
            </if>
            <if test="po.erpCreateEndTime != null ">
                AND date_format(head.erp_create_time,'%Y%m%d') <![CDATA[ <= ]]> date_format(#{po.erpCreateEndTime},'%Y%m%d')
            </if>
            <if test="po.ftyId != null ">
                AND item.fty_id = #{po.ftyId}
            </if>
            <if test="po.locationId != null ">
                AND item.location_id = #{po.locationId}
            </if>
            <if test="po.matId != null ">
                AND item.mat_id = #{po.matId}
            </if>
            <if test="po.isReturnFlag != null ">
                AND head.is_return_flag = #{po.isReturnFlag}
            </if>
        </where>
    </select>
</mapper>
