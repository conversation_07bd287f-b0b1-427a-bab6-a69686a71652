package com.inossem.wms.bizbasis.stock.service.biz;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleRuleDataWrap;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockBatchDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockBinDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockHistoryBatchDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockHistoryBinDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockInsDocBatchDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockInsDocBinDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.metadata.MetadataContext;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleLifetimeDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleMaintainDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssembleRule;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.unit.dto.DicUnitRelDTO;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.SearchStockDTO;
import com.inossem.wms.common.model.stock.dto.StockBatchDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsDocBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBatch;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockHistoryBatch;
import com.inossem.wms.common.model.stock.entity.StockHistoryBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.key.StockBatchKey;
import com.inossem.wms.common.model.stock.key.StockBinKey;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPo;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilMetadata;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilReflect;
import com.inossem.wms.common.util.UtilSequence;
import com.inossem.wms.common.util.UtilString;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 库存更改通用方法
 *
 * <AUTHOR>
 */
@Service
public class StockCommonService {
    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    protected StockCommonComponent stockCommonComponent;
    @Autowired
    protected StockInsDocBatchDataWrap stockInsDocBatchDataWrap;
    @Autowired
    protected StockInsDocBinDataWrap stockInsDocBinDataWrap;
    @Autowired
    protected StockBatchDataWrap stockBatchDataWrap;
    @Autowired
    protected StockBinDataWrap stockBinDataWrap;
    @Autowired
    protected BizLabelDataDataWrap labelDataDataWrap;
    @Autowired
    protected StockHistoryBatchDataWrap stockHistoryBatchDataWrap;
    @Autowired
    protected StockHistoryBinDataWrap stockHistoryBinDataWrap;
    @Autowired
    protected BizReceiptAssembleRuleDataWrap bizReceiptAssembleRuleDataWrap;
    @Autowired
    protected BizBatchInfoDataWrap bizBatchInfoDataWrap;
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 过账前的校验和数量计算
     *
     * @param insMoveTypeVo 库存凭证
     *
     */
    public void checkAndComputeForModifyStock(StockInsMoveTypeDTO insMoveTypeVo) {
        List<StockInsDocBin> insDocBinList = insMoveTypeVo.getInsDocBinList();
        if (insDocBinList == null || insDocBinList.size() == 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 校验仓位冻结
        this.checkFreezeBin(insMoveTypeVo);
        // 校验物料冻结
        this.checkFreezeMaterial(insMoveTypeVo);
        // 校验库存地点冻结
        this.checkFreezeLocation(insMoveTypeVo);
        // 循环分前续单据rid，bid检验物料、单位、状态、数量和,计算实际数量
        this.checkAndComputeForPreReceipt(insMoveTypeVo);
        // 校验库存是否充足
        this.checkStock(insMoveTypeVo);
        // 校验存储单元是否放在多个仓位上
        // 放在这里的原因是希望使用actual_qty来校验，这样库存和凭证的单位已经统一
        this.checkCellOnlyBin(insMoveTypeVo);
    }

    /**
     * 库存更改通用方法
     *
     * 实现逻辑：
     * 1、保存物料凭证
     * 2、根据物料凭证，获取到影响的库存记录，按照记录主键id，批量新增和批量更新库存记录
     * 3、异步保存库存修改历史记录，用于收发存统计
     * 4、删除库存数量为0的无效库存，保持库存记录数维持在最低水平
     * 5、校验负库存确保修改正确
     *
     * @param insMoveTypeVo 库存凭证
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void modifyStock(StockInsMoveTypeDTO insMoveTypeVo) {
        // 使用actual数量修改批次库存
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        for (StockInsDocBatch insDocBatch : insMoveTypeVo.getInsDocBatchList()) {
            if (insDocBatch.getActualQty() == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_ACTUAL_QTY_ERROR,
                        bizBatchInfoDataWrap.getById(insDocBatch.getBatchId()).getBatchCode());
            }
            if(insDocBatch.getDocDate()==null){
                insDocBatch.setDocDate(UtilDate.getNow());
            }
            if(insDocBatch.getPostingDate()==null){
                insDocBatch.setPostingDate(UtilDate.getNow());
            }
            insDocBatchList.add(insDocBatch);
        }
        // 使用actual数量修改仓位库存
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        for (StockInsDocBin insDocBin : insMoveTypeVo.getInsDocBinList()) {
            if (insDocBin.getActualQty() == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_ACTUAL_QTY_ERROR,
                        bizBatchInfoDataWrap.getById(insDocBin.getBatchId()).getBatchCode());
            }
            insDocBinList.add(insDocBin);
        }

        // 批次凭证有数据，根据批次凭证移动批次库存
        if (insDocBatchList.size() > 0) {
            // 设置凭证过账时间
            insDocBatchList.forEach(u -> u.setDocDate(new Date()));
            insDocBatchList.forEach(u -> u.setPostingDate(new Date()));

            // 批量保存凭证
            stockInsDocBatchDataWrap.saveBatch(insDocBatchList);

            // 合并凭证，减少库存记录修改次数
            Map<StockBatchKey, StockBatch> mergedStockBatch = this.mergeInsDocBatch(insDocBatchList);

            // 获取所有的库存记录
            List<StockBatch> allStockRecords = this.getAllStockRecordByInsDocBatchList(mergedStockBatch);

            // 批次库存非负数校验(批次库存的非限制、质检、在途、冻结数量必须为正数)
            this.stockBatchCheckQty(allStockRecords);

            // 删除零库存的记录
            this.removeStockBatchZeroStockRecords(allStockRecords);

            // 修改批次库存记录
            this.updateStockBatchRecords(allStockRecords);

            // 插入批次库存记录
            this.insertStockBatchRecords(allStockRecords);

            // 保存批次库存历史信息
            this.saveStockBatchHistory(allStockRecords, insDocBatchList);
        }

        // 仓位凭证有数据，根据仓位凭证移动仓位库存
        if (insDocBinList.size() > 0) {
            // 批量保存仓位库存凭证
            stockInsDocBinDataWrap.saveBatch(insDocBinList);

            // 合并凭证，减少库存记录修改次数
            Map<StockBinKey, StockBin> mergedStockBin = this.mergeInsDocBin(insDocBinList);

            // 获取所有的库存记录
            List<StockBin> allStockRecords = this.getAllStockRecordByInsDocBinList(mergedStockBin);

            // 校验六库存数量非负（系统默认存储类型不校验）
            this.stockBinCheckQty(allStockRecords);

            // 删除零库存的记录
            this.removeStockBinZeroStockRecords(allStockRecords);

            // 修改仓位库存记录
            this.updateStockBinRecords(allStockRecords);

            // 插入仓位库存记录
            this.insertStockBinRecords(allStockRecords);

            // 保存仓位库存历史信息
            this.saveStockBinHistory(allStockRecords, insDocBinList);

            // 修改实际仓位使用状态及仓位库存移动时间,存储单元状态（过滤默认存储类型的默认仓位）
            List<Long> binIdList = new ArrayList<>();
            for (StockInsDocBin insDocBin : insDocBinList) {
                if (!EnumDefaultStorageType.getAllDefaultStorageTypeCode().contains(dictionaryService.getStorageTypeCacheById(insDocBin.getTypeId()).getTypeCode())) {
                    binIdList.add(insDocBin.getBinId());
                }
            }
            if (binIdList.size() > 0) {
                // 异步刷新仓位缓存
                ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.UPDATE_BIN_EMPTY_STATUS, binIdList);
                RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
            }
        }

        // 更新标签对应的库存移动信息
        if (UtilCollection.isNotEmpty(insMoveTypeVo.getInsDocBinPoList())) {
            List<BizLabelData> labelDataList = new ArrayList<>();
            for (StockInsDocBinPo insDocBinPo : insMoveTypeVo.getInsDocBinPoList()) {
                if (UtilCollection.isNotEmpty(insDocBinPo.getLabelIdList())) {
                    if (Const.DEBIT_S_ADD.equalsIgnoreCase(insDocBinPo.getDebitCredit())) {
                        for (Long id : insDocBinPo.getLabelIdList()) {
                            BizLabelData bizLabelData = new BizLabelData();
                            bizLabelData.setId(id);
                            bizLabelData.setMatId(insDocBinPo.getMatId());
                            bizLabelData.setBatchId(insDocBinPo.getBatchId());
                            bizLabelData.setFtyId(insDocBinPo.getFtyId());
                            bizLabelData.setLocationId(insDocBinPo.getLocationId());
                            bizLabelData.setWhId(insDocBinPo.getWhId());
                            bizLabelData.setTypeId(insDocBinPo.getTypeId());
                            bizLabelData.setBinId(insDocBinPo.getBinId());
                            bizLabelData.setCellId(insDocBinPo.getCellId());
                            labelDataList.add(bizLabelData);
                        }
                    }
                }
            }
            if (UtilCollection.isNotEmpty(labelDataList)) {
                labelDataDataWrap.updateBatchDtoById(labelDataList);
            }
        }
    }

    /**
     * 校验前四个库存 必须非负 (非限制、质检、在途、冻结)
     *
     * @param stockBatchList 批次库存数据列表
     */
    private void stockBatchCheckQty(List<StockBatch> stockBatchList) {

        for(StockBatch stockBatch : stockBatchList){
            // 校验前四个库存 必须非负 (非限制、质检、在途、冻结)
            if (stockBatch.getQty().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_UNRESTRICTED, this.getCheckQtyErrorMsgArgs(stockBatch));
            } else if (stockBatch.getQtyTransfer().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_ON_THE_WAY, this.getCheckQtyErrorMsgArgs(stockBatch));
            } else if (stockBatch.getQtyInspection().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_QUALITY_INSPECTION, this.getCheckQtyErrorMsgArgs(stockBatch));
            } else if (stockBatch.getQtyFreeze().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_FREEZE, this.getCheckQtyErrorMsgArgs(stockBatch));
            }
        }
    }

    /**
     * <AUTHOR>
     *
     * 获取校验数量异常消息的参数
     *
     * @param stockBatch
     * @return
     */
    private String[] getCheckQtyErrorMsgArgs(StockBatch stockBatch){
        StockBatchDTO dto = UtilBean.newInstance(stockBatch, StockBatchDTO.class);
        dataFillService.fillAttr(dto);

        String[] args = new String[4];
        args[0] = dto.getFtyCode();
        args[1] = dto.getLocationCode();
        args[2] = dto.getMatCode();
        args[3] = dto.getBatchCode();

        return args;
    }

    /**
     * 删除零库存的记录
     *
     * @param stockBatchList 批次库存
     */
    private void removeStockBatchZeroStockRecords(List<StockBatch> stockBatchList) {

        List<Long> removeIdList = stockBatchList.stream().filter(obj -> {
            if(BigDecimal.ZERO.compareTo(obj.getQty()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyTransfer()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyInspection()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyFreeze()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyHaste()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyTemp()) != 0){
                return false;
            }
            return true;
        }).map(StockBatch::getId).collect(Collectors.toList());

        if(UtilCollection.isEmpty(removeIdList)){
            return;
        }

        stockBatchDataWrap.removeByIds(removeIdList);
    }

    /**
     * 修改批次库存记录
     *
     * @param stockBatchList 批次库存
     */
    private void updateStockBatchRecords(List<StockBatch> stockBatchList) {

        List<StockBatch> updateRecordList = stockBatchList.stream().filter(obj -> {
            // 过滤掉待新增记录
            if(UtilNumber.isEmpty(obj.getId())){
                return false;
            }

            // 过滤掉零库存记录
            if(BigDecimal.ZERO.compareTo(obj.getQty()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyTransfer()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyInspection()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyFreeze()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyHaste()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyTemp()) == 0){
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if(UtilCollection.isEmpty(updateRecordList)){
            return;
        }

        updateRecordList.forEach(obj -> obj.setModifyTime(new Date()));

        stockBatchDataWrap.updateBatchById(updateRecordList);
    }

    /**
     * 插入批次库存记录
     *
     * @param stockBatchList 批次库存
     */
    private void insertStockBatchRecords(List<StockBatch> stockBatchList) {

        List<StockBatch> insertRecordList = stockBatchList.stream().filter(obj -> UtilNumber.isEmpty(obj.getId())).collect(Collectors.toList());

        if(UtilCollection.isEmpty(insertRecordList)){
            return;
        }

        stockBatchDataWrap.saveBatch(insertRecordList);
    }

    /**
     * 保存批次库存历史信息
     *
     * @param stockBatchList 批次库存
     */
    private void saveStockBatchHistory(List<StockBatch> stockBatchList, List<StockInsDocBatch> insDocBatchList) {

        // 按照唯一键分组，未进行唯一键重复的处理，如果唯一键重复，将抛出异常，证明前序代码有bug，需要及时修正
        Map<StockBatchKey, StockBatch> unKeyStockBatchMap = stockBatchList.stream().collect(Collectors.toMap(obj -> new StockBatchKey(obj.getMatId(), obj.getBatchId(), obj.getFtyId(), obj.getLocationId()), obj -> obj));

        List<StockHistoryBatch> hisList = new LinkedList<>();

        for(StockInsDocBatch stockInsDocBatch : insDocBatchList){
            StockBatch stockBatch = unKeyStockBatchMap.get(new StockBatchKey(stockInsDocBatch.getMatId(), stockInsDocBatch.getBatchId(), stockInsDocBatch.getFtyId(), stockInsDocBatch.getLocationId()));

            StockHistoryBatch stockHistoryBatch = new StockHistoryBatch();

            stockHistoryBatch.setId(UtilSequence.nextId());

            stockHistoryBatch.setMatId(stockBatch.getMatId());
            stockHistoryBatch.setBatchId(stockBatch.getBatchId());
            stockHistoryBatch.setFtyId(stockBatch.getFtyId());
            stockHistoryBatch.setLocationId(stockBatch.getLocationId());
            stockHistoryBatch.setQty(stockBatch.getQty());
            stockHistoryBatch.setQtyTransfer(stockBatch.getQtyTransfer());
            stockHistoryBatch.setQtyInspection(stockBatch.getQtyInspection());
            stockHistoryBatch.setQtyFreeze(stockBatch.getQtyFreeze());
            stockHistoryBatch.setQtyHaste(stockBatch.getQtyHaste());
            stockHistoryBatch.setQtyTemp(stockBatch.getQtyTemp());
            stockHistoryBatch.setInsDocBatchId(stockInsDocBatch.getId());
            stockHistoryBatch.setCreateTime(new Date());
            stockHistoryBatch.setModifyTime(new Date());

            hisList.add(stockHistoryBatch);
        }

        stockHistoryBatchDataWrap.saveBatch(hisList);
    }

    /**
     * 校验六库存数量非负（系统默认存储类型不校验）
     *
     * @param stockBinList 仓位库存数据列表
     */
    private void stockBinCheckQty(List<StockBin> stockBinList) {

        for(StockBin stockBin : stockBinList){

            // 系统默认存储类型下的仓位库存不进行非负校验
            if(EnumDefaultStorageType.getAllDefaultStorageTypeCode().contains(dictionaryService.getStorageTypeCacheById(stockBin.getTypeId()).getTypeCode())){
                continue;
            }

            if (stockBin.getQty().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_UNRESTRICTED, this.getCheckQtyErrorMsgArgs(stockBin));
            } else if (stockBin.getQtyTransfer().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_ON_THE_WAY, this.getCheckQtyErrorMsgArgs(stockBin));
            } else if (stockBin.getQtyInspection().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_QUALITY_INSPECTION, this.getCheckQtyErrorMsgArgs(stockBin));
            } else if (stockBin.getQtyFreeze().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_FREEZE, this.getCheckQtyErrorMsgArgs(stockBin));
            } else if (stockBin.getQtyHaste().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_HASTE, this.getCheckQtyErrorMsgArgs(stockBin));
            } else if (stockBin.getQtyTemp().compareTo(BigDecimal.ZERO) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_TEMP, this.getCheckQtyErrorMsgArgs(stockBin));
            }
        }
    }

    /**
     * <AUTHOR>
     *
     * 获取校验数量异常消息的参数
     *
     * @param stockBatch
     * @return
     */
    private String[] getCheckQtyErrorMsgArgs(StockBin stockBatch){
        StockBinDTO dto = UtilBean.newInstance(stockBatch, StockBinDTO.class);
        dataFillService.fillAttr(dto);

        String[] args = new String[4];
        args[0] = dto.getFtyCode();
        args[1] = dto.getLocationCode();
        args[2] = dto.getMatCode();
        args[3] = dto.getBatchInfo().getBatchCode();
        args[4] = dto.getWhCode();
        args[5] = dto.getTypeCode();
        args[6] = dto.getBinCode();

        return args;
    }

    /**
     * 删除零库存的记录
     *
     * @param stockBinList 仓位库存
     */
    private void removeStockBinZeroStockRecords(List<StockBin> stockBinList) {

        List<Long> removeIdList = stockBinList.stream().filter(obj -> {
            if(BigDecimal.ZERO.compareTo(obj.getQty()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyTransfer()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyInspection()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyFreeze()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyHaste()) != 0
                    || BigDecimal.ZERO.compareTo(obj.getQtyTemp()) != 0){
                return false;
            }
            return true;
        }).map(StockBin::getId).collect(Collectors.toList());

        if(UtilCollection.isEmpty(removeIdList)){
            return;
        }

        stockBinDataWrap.removeByIds(removeIdList);
    }

    /**
     * 修改仓位库存记录
     *
     * @param stockBinList 仓位库存
     */
    private void updateStockBinRecords(List<StockBin> stockBinList) {

        List<StockBin> updateRecordList = stockBinList.stream().filter(obj -> {

            // 过滤掉待新增记录
            if(UtilNumber.isEmpty(obj.getId())){
                return false;
            }

            // 过滤掉零库存记录
            if(BigDecimal.ZERO.compareTo(obj.getQty()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyTransfer()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyInspection()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyFreeze()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyHaste()) == 0
                    && BigDecimal.ZERO.compareTo(obj.getQtyTemp()) == 0){
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if(UtilCollection.isEmpty(updateRecordList)){
            return;
        }

        updateRecordList.forEach(obj -> obj.setModifyTime(new Date()));

        stockBinDataWrap.updateBatchById(updateRecordList);
    }

    /**
     * 插入仓位库存记录
     *
     * @param stockBinList 仓位库存
     */
    private void insertStockBinRecords(List<StockBin> stockBinList) {

        List<StockBin> insertRecordList = stockBinList.stream().filter(obj -> UtilNumber.isEmpty(obj.getId())).collect(Collectors.toList());

        if(UtilCollection.isEmpty(insertRecordList)){
            return;
        }

        stockBinDataWrap.saveBatch(insertRecordList);
    }

    /**
     * 保存仓位库存历史信息
     *
     * @param stockBinList 仓位库存
     */
    private void saveStockBinHistory(List<StockBin> stockBinList, List<StockInsDocBin> insDocBinList) {

        // 按照唯一键分组，未进行唯一键重复的处理，如果唯一键重复，将抛出异常，证明前序代码有bug，需要及时修正
        Map<StockBinKey, StockBin> unKeyStockBinMap = stockBinList.stream().collect(Collectors.toMap(obj -> new StockBinKey(obj.getMatId(), obj.getBatchId(), obj.getFtyId(), obj.getLocationId(), obj.getWhId(), obj.getTypeId(), obj.getBinId(), obj.getCellId()), obj -> obj));

        List<StockHistoryBin> hisList = new LinkedList<>();

        for(StockInsDocBin stockInsDocBin : insDocBinList){
            if(stockInsDocBin.getMoveQty().compareTo(BigDecimal.ZERO)==0){
                // 库存不错修改的数据跳过
                continue;
            }
            StockBin stockBin = unKeyStockBinMap.get(new StockBinKey(stockInsDocBin.getMatId(), stockInsDocBin.getBatchId(), stockInsDocBin.getFtyId(), stockInsDocBin.getLocationId(), stockInsDocBin.getWhId(), stockInsDocBin.getTypeId(), stockInsDocBin.getBinId(), stockInsDocBin.getCellId()));

            StockHistoryBin stockHistoryBin = new StockHistoryBin();

            stockHistoryBin.setId(UtilSequence.nextId());

            stockHistoryBin.setMatId(stockBin.getMatId());
            stockHistoryBin.setBatchId(stockBin.getBatchId());
            stockHistoryBin.setFtyId(stockBin.getFtyId());
            stockHistoryBin.setLocationId(stockBin.getLocationId());
            stockHistoryBin.setWhId(stockBin.getWhId());
            stockHistoryBin.setTypeId(stockBin.getTypeId());
            stockHistoryBin.setBinId(stockBin.getBinId());
            stockHistoryBin.setCellId(stockBin.getCellId());
            stockHistoryBin.setQty(stockBin.getQty());
            stockHistoryBin.setQtyTransfer(stockBin.getQtyTransfer());
            stockHistoryBin.setQtyInspection(stockBin.getQtyInspection());
            stockHistoryBin.setQtyFreeze(stockBin.getQtyFreeze());
            stockHistoryBin.setQtyHaste(stockBin.getQtyHaste());
            stockHistoryBin.setQtyTemp(stockBin.getQtyTemp());
            stockHistoryBin.setInsDocBinId(stockInsDocBin.getId());
            stockHistoryBin.setCreateTime(new Date());
            stockHistoryBin.setModifyTime(new Date());

            hisList.add(stockHistoryBin);
        }

        stockHistoryBinDataWrap.saveBatch(hisList);
    }

    /**
     * 循环分前续单据rid，bid检验物料、单位、状态、数量和,计算实际数量
     *
     * @param insMoveTypeVo 库存凭证
     */
    private void checkAndComputeForPreReceipt(StockInsMoveTypeDTO insMoveTypeVo) {
        List<StockInsDocBatch> insDocBatchList = insMoveTypeVo.getInsDocBatchList();
        List<StockInsDocBin> insDocBinList = insMoveTypeVo.getInsDocBinList();

        // 相关物料map
        Map<Long, DicMaterialDTO> matMap =
            dictionaryService.getMatMapCacheByMatIdList(insDocBinList.stream().map(StockInsDocBin::getMatId).distinct().collect(Collectors.toList()));

        // 所有单位关系map
        Map<String, DicUnitRelDTO> relUnitMap = dictionaryService.getAllRelUnitCache();

        // 以前续单据itemId分组批次物料凭证
        Map<PreReceiptObj, List<StockInsDocBatch>> batchMap =
            insDocBatchList.stream().collect(Collectors.groupingBy(e-> new PreReceiptObj(e.getPreReceiptItemId(), e.getPreReceiptBinId()), Collectors.toList()));
        // 以前续单据Rid分组仓位物料凭证
        Map<PreReceiptObj, List<StockInsDocBin>> binMap =
            insDocBinList.stream().collect(Collectors.groupingBy(e-> new PreReceiptObj(e.getPreReceiptItemId(), e.getPreReceiptBinId()), Collectors.toList()));

        // 查询所有的前续单据ItemId
        List<PreReceiptObj> referReceiptItemIds = insDocBinList.stream().map(e-> new PreReceiptObj(e.getPreReceiptItemId(), e.getPreReceiptBinId())).distinct().collect(Collectors.toList());

        // 分前续单据Rid检验和计算 单位和数量
        for (PreReceiptObj preReceiptObj : referReceiptItemIds) {
            List<StockInsDocBatch> batchList = batchMap.get(preReceiptObj);
            List<StockInsDocBin> binList = binMap.get(preReceiptObj);
            if (binList == null || binList.size() == 0) {
                if (batchList == null || batchList.size() == 0) {
                    continue;
                } else {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_REFER_RECEIPT_INS_DOC_ERROR, insDocBinList.get(0).getInsDocCode());
                }
            }
            // 分前续单据ItemId检验和计算 物料、单位、状态、实际数量、每组数量和
            this.checkAndComputeForPreReceiptItemId(matMap, relUnitMap, batchList, binList);
        }
    }

    /**
     * 分前续单据ItemId检验和计算 物料、单位、状态、实际数量、每组数量和
     *
     * @param matMap 物料map
     * @param relUnitMap 单位换算map
     * @param insDocBatchList 批次库存凭证
     * @param insDocBinList 仓位库存凭证
     */
    private void checkAndComputeForPreReceiptItemId(Map<Long, DicMaterialDTO> matMap, Map<String, DicUnitRelDTO> relUnitMap,
        List<StockInsDocBatch> insDocBatchList, List<StockInsDocBin> insDocBinList) {
        StockInsDocBin insDocBin = insDocBinList.get(0);
        Long matId = insDocBin.getMatId();
        Long unitId = insDocBin.getUnitId();
        Long ftyId = insDocBin.getFtyId();
        if (UtilNumber.isEmpty(ftyId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_MISS_FACTORY_CODE);
        } else if (UtilNumber.isEmpty(matId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_MISS_MATERIAL_CODE);
        } else if (UtilNumber.isEmpty(unitId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_MISS_UNIT_CODE);
        } else if (UtilNumber.isEmpty(insDocBin.getStockStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_MISS_STOCK_STATUS);
        }

        // 物料单位校验
        DicMaterialDTO matObject = matMap.get(matId);
        if (matObject == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
        } else if (matObject.getUnitId() == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_UNIT_NOT_EXIST, matObject.getMatCode());
        }

        // 单据单位转物料主数据单位
        RelUnitFactor relUnitFactor = this.getRelUnitFactor(ftyId, matId, unitId, matObject.getUnitId(), relUnitMap);

        // 计算前续单据Rid数量及实际数量，同时校验【物料】【单位】【库存状态】是否统一
        BigDecimal qtyBatch = stockCommonComponent.sumBatchQtyForPreReceiptItemId(matId, unitId, insDocBatchList,
            relUnitFactor.getMultiplicationFactor(), relUnitFactor.getDivisionFactor());
        // 计算前续单据Rid数量及实际数量，同时校验【物料】【单位】【库存状态】是否统一
        BigDecimal qtyBin = stockCommonComponent.sumBinQtyForPreReceiptItemId(matId, unitId, insDocBinList, relUnitFactor.getMultiplicationFactor(),
            relUnitFactor.getDivisionFactor());

        // 批次库存仓位库存修改数量不平
        if (qtyBatch.compareTo(qtyBin) != 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_STOCK_UNEQUAL);
        }
    }

    /**
     * 设置 单位转换系数
     *
     * @param ftyId 工厂id
     * @param matId 物料id
     * @param sourceUnitId 原单位id
     * @param targetUnitId 目标单位id
     * @param relUnitMap 单位换算关系map
     * @return 换算关系系数
     * <AUTHOR> <<EMAIL>>
     */
    private RelUnitFactor getRelUnitFactor(Long ftyId, Long matId, Long sourceUnitId, Long targetUnitId, Map<String, DicUnitRelDTO> relUnitMap) {
        RelUnitFactor relUnitFactor = new RelUnitFactor();
        BigDecimal multiplicationFactor = null, divisionFactor = null;
        // 物料单位和单据单位对比，单位不同，需要单位换算，存储乘系数和除系数
        if (!targetUnitId.equals(sourceUnitId)) {
            DicUnitRelDTO relUnit = relUnitMap.get(ftyId + "-" + matId + "-" + sourceUnitId + "-" + targetUnitId);
            if (relUnit == null) {
                relUnit = relUnitMap.get(ftyId + "-" + matId + "-" + targetUnitId + "-" + sourceUnitId);
                if (relUnit == null) {
                    DicFactoryDTO ftyObj = dictionaryService.getFtyCacheById(ftyId);
                    if (null == ftyObj) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_SUCH_FACTORY, UtilObject.getStringOrEmpty(ftyId));
                    }
                    DicUnit targetUnitObject = dictionaryService.getUnitCacheById(targetUnitId);
                    if (targetUnitObject == null) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_NOT_EXIST, UtilObject.getStringOrEmpty(targetUnitId));
                    }
                    DicUnit sourceUnitObject = dictionaryService.getUnitCacheById(sourceUnitId);
                    if (sourceUnitObject == null) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_NOT_EXIST, UtilObject.getStringOrEmpty(sourceUnitId));
                    }
                    DicMaterialDTO matObject = dictionaryService.getMatCacheById(matId);
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_REL_UNIT_NOT_EXIST, ftyObj.getFtyCode(), matObject.getMatCode(),
                        targetUnitObject.getUnitCode(), sourceUnitObject.getUnitCode());
                } else {

                    multiplicationFactor = relUnit.getSourceQty();
                    divisionFactor = relUnit.getTargetQty();
                }
            } else {
                multiplicationFactor = relUnit.getTargetQty();
                divisionFactor = relUnit.getSourceQty();
            }
        }
        relUnitFactor.setMultiplicationFactor(multiplicationFactor);
        relUnitFactor.setDivisionFactor(divisionFactor);
        return relUnitFactor;
    }

    /**
     * 校验库存是否充足
     *
     * @param insMoveTypeVo 库存凭证
     *
     */
    private void checkStock(StockInsMoveTypeDTO insMoveTypeVo) {
        // 校验批次ins凭证actual数量是否存在
        for (StockInsDocBatch insDocBatch : insMoveTypeVo.getInsDocBatchList()) {
            if (insDocBatch.getActualQty() == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_ACTUAL_QTY_ERROR,
                    bizBatchInfoDataWrap.getById(insDocBatch.getBatchId()).getBatchCode());
            }
        }

        // 校验仓位ins凭证actual数量是否存在
        for (StockInsDocBin insDocBin : insMoveTypeVo.getInsDocBinList()) {
            if (insDocBin.getActualQty() == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_ACTUAL_QTY_ERROR,
                    bizBatchInfoDataWrap.getById(insDocBin.getBatchId()).getBatchCode());
            }
        }

        // 查询批次ins凭证相关批次库存
        Map<StockBatchKey, StockBatchDTO> stockBatchMap = stockCommonComponent.getStockBatchByInsMoveTypeVo(insMoveTypeVo);
        // 查询仓位ins凭证相关仓位库存
        Map<StockBinKey, StockBinDTO> stockBinMap = stockCommonComponent.getStockBinByInsMoveTypeVo(insMoveTypeVo);

        // 模拟修改批次库存
        for (StockInsDocBatch insDocBatch : insMoveTypeVo.getInsDocBatchList()) {
            stockCommonComponent.simulateModifyStockForCheck(stockBatchMap, insDocBatch);
        }

        // 模拟修改仓位库存
        for (StockInsDocBin insDocBin : insMoveTypeVo.getInsDocBinList()) {
            stockCommonComponent.simulateModifyStockForCheck(stockBinMap, insDocBin);
        }

        // 校验批次库存七条件
        for (StockBatchKey key : stockBatchMap.keySet()) {
            stockCommonComponent.checkStockBatch(stockBatchMap.get(key));
        }

        // 校验仓位库存七条件
        for (StockBinKey key : stockBinMap.keySet()) {
            String typeCode = dictionaryService.getStorageTypeCacheById(key.getTypeId()).getTypeCode();
            if (UtilConst.getInstance().getDefaultStorageTypeCodeSet().contains(typeCode)) {
                // 默认存储区，只校验非负的
                if (EnumDefaultStorageType.UNREAL.getTypeCode().equals(typeCode)) {
                    stockCommonComponent.checkStockBin(stockBinMap.get(key));
                }
            } else {
                // 普通存储区校验
                stockCommonComponent.checkStockBin(stockBinMap.get(key));
            }
        }
    }

    /**
     * 使用actual_qty来校验存储单元是否放在多个仓位上
     *
     * @param insMoveTypeVo 库存凭证
     *
     */
    private void checkCellOnlyBin(StockInsMoveTypeDTO insMoveTypeVo) {
        // 查询有效的存储单元的insDocBin列表
        List<StockInsDocBin> insDocBinList = insMoveTypeVo.getInsDocBinList().stream()
            .filter(insDocBin -> UtilNumber.isNotEmpty(insDocBin.getCellId())).collect(Collectors.toList());

        if (insDocBinList.size() <= 0) {
            return;
        }

        HashSet<Long> cellSet = new HashSet<>();

        // 辅助用的map集合，先以cellCode为key，再以bin三主键为key，分解所有insDocBin仓位凭证
        HashMap<Long, HashMap<Long, ArrayList<StockInsDocBin>>> cellMap = new HashMap<>(insDocBinList.size());

        // insDocBin凭证存储，并放置在辅助map集合中
        for (StockInsDocBin insDocBin : insDocBinList) {
            this.setInsDocBinForCellOnlyBin(cellMap, insDocBin);
            cellSet.add(insDocBin.getCellId());
        }

        // 查询有效存储单元的已有库存
        StockBinPO stockBinPo = new StockBinPO();
        stockBinPo.setCellSet(cellSet);
        List<StockBinDTO> stockBinList = this.getStockBinByStockBinPo(stockBinPo);

        // 把已有库存改为insDocBin凭证
        for (StockBinDTO stockBinDto : stockBinList) {
            this.setStockBinForCellOnlyBin(cellMap, stockBinDto);
        }

        // 辅助map中第三级的insDocBin仓位凭证列表List计算改仓位是否剩余库存
        for (Long cellId : cellMap.keySet()) {
            // 空托盘不校验
            if (cellId == 0) {
                continue;
            }
            HashMap<Long, ArrayList<StockInsDocBin>> binMap = cellMap.get(cellId);
            // 如果该存储单元只在一个仓位上，不需要校验
            if (binMap.size() <= 1) {
                continue;
            }
            // 如果该存储单元在多个仓位上，计算每个仓位是数量，去掉数量是0的仓位
            for (Long binKey : new ArrayList<>(binMap.keySet())) {
                HashMap<String, BigDecimal> mergeMap = new HashMap<>(binMap.get(binKey).size());
                // 计算该仓位的数量,以InsDocBin的合并键为准InsDocBinMergeKey
                for (StockInsDocBin insDocBin : binMap.get(binKey)) {
                    String mergeKey =
                        insDocBin.getMatId() + "-" + insDocBin.getFtyId() + "-" + insDocBin.getLocationId() + "-" + insDocBin.getBatchId() + "-"
                            + insDocBin.getWhId() + "-" + insDocBin.getTypeId() + "-" + insDocBin.getBinId() + "-" + insDocBin.getStockStatus();
                    if (mergeMap.containsKey(mergeKey)) {
                        BigDecimal qty = mergeMap.get(mergeKey);
                        if (insDocBin.getDebitCredit().equals(Const.DEBIT_S_ADD)) {
                            qty = qty.add(insDocBin.getActualQty());
                        } else {
                            qty = qty.subtract(insDocBin.getActualQty());
                        }

                        if (qty.compareTo(BigDecimal.ZERO) == 0) {
                            mergeMap.remove(mergeKey);
                        } else {
                            mergeMap.put(mergeKey, qty);
                        }
                    } else {
                        if (insDocBin.getActualQty().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }

                        if (insDocBin.getDebitCredit().equals(Const.DEBIT_S_ADD)) {
                            mergeMap.put(mergeKey, insDocBin.getActualQty());
                        } else {
                            mergeMap.put(mergeKey, insDocBin.getActualQty().negate());
                        }
                    }
                }

                if (mergeMap.size() <= 0) {
                    binMap.remove(binKey);
                }
            }

            if (binMap.size() > 1) {
                StringBuilder stringBuilder = new StringBuilder();
                for (Long binKey : binMap.keySet()) {
                    stringBuilder.append(binKey.toString());
                }
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MODIFY_STOCK_CELL_BIN, cellId.toString(), stringBuilder.toString());
            }
        }
    }

    /**
     * 库存转为insDocBin放在map中
     */
    private void setStockBinForCellOnlyBin(HashMap<Long, HashMap<Long, ArrayList<StockInsDocBin>>> cellMap, StockBinDTO stockBinDto) {
        BigDecimal qty = stockBinDto.getQty();
        int compare = qty.compareTo(BigDecimal.ZERO);
        if (compare != 0) {
            StockInsDocBin insDocBin = this.newStockInsDocBinDto(stockBinDto, compare, qty);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            this.setInsDocBinForCellOnlyBin(cellMap, insDocBin);
        }

        qty = stockBinDto.getQtyTransfer();
        compare = qty.compareTo(BigDecimal.ZERO);
        if (compare != 0) {
            StockInsDocBin insDocBin = this.newStockInsDocBinDto(stockBinDto, compare, qty);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_ON_THE_WAY.getValue());
            this.setInsDocBinForCellOnlyBin(cellMap, insDocBin);
        }

        qty = stockBinDto.getQtyInspection();
        compare = qty.compareTo(BigDecimal.ZERO);
        if (compare != 0) {
            StockInsDocBin insDocBin = this.newStockInsDocBinDto(stockBinDto, compare, qty);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_QUALITY_INSPECTION.getValue());
            this.setInsDocBinForCellOnlyBin(cellMap, insDocBin);
        }

        qty = stockBinDto.getQtyFreeze();
        compare = qty.compareTo(BigDecimal.ZERO);
        if (compare != 0) {
            StockInsDocBin insDocBin = this.newStockInsDocBinDto(stockBinDto, compare, qty);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue());
            this.setInsDocBinForCellOnlyBin(cellMap, insDocBin);
        }

        qty = stockBinDto.getQtyHaste();
        compare = qty.compareTo(BigDecimal.ZERO);
        if (compare != 0) {
            StockInsDocBin insDocBin = this.newStockInsDocBinDto(stockBinDto, compare, qty);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_HASTE.getValue());
            this.setInsDocBinForCellOnlyBin(cellMap, insDocBin);
        }

        qty = stockBinDto.getQtyTemp();
        compare = qty.compareTo(BigDecimal.ZERO);
        if (compare != 0) {
            StockInsDocBin insDocBin = this.newStockInsDocBinDto(stockBinDto, compare, qty);
            insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_TEMP.getValue());
            this.setInsDocBinForCellOnlyBin(cellMap, insDocBin);
        }
    }

    /**
     * insDocBin凭证放在map中
     */
    private void setInsDocBinForCellOnlyBin(HashMap<Long, HashMap<Long, ArrayList<StockInsDocBin>>> cellMap, StockInsDocBin insDocBin) {
        if (!cellMap.containsKey(insDocBin.getCellId())) {
            cellMap.put(insDocBin.getCellId(), new HashMap<>(1));
        }
        HashMap<Long, ArrayList<StockInsDocBin>> binMap = cellMap.get(insDocBin.getCellId());
        if (!binMap.containsKey(insDocBin.getBinId())) {
            binMap.put(insDocBin.getBinId(), new ArrayList<>());
        }
        ArrayList<StockInsDocBin> list = binMap.get(insDocBin.getBinId());
        list.add(insDocBin);
    }

    /**
     * 根据借贷标识查询 冻结仓位 H时 出库冻结 S时 入库冻结
     */
    private void checkFreezeBin(StockInsMoveTypeDTO insMoveTypeVo) {
        List<StockInsDocBin> insDocBinList = insMoveTypeVo.getInsDocBinList();
        if (insDocBinList == null || insDocBinList.size() == 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        Set<String> freezeBinSet = new HashSet<>();
        for (StockInsDocBin dto : insDocBinList) {
            DicWhStorageBinDTO binDto = dictionaryService.getBinCacheById(dto.getBinId());
            if (null == binDto) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR, UtilObject.getStringOrEmpty(dto.getBinId()));
            }
            boolean flag = (Const.DEBIT_S_ADD.equals(dto.getDebitCredit()) && binDto.getFreezeInput() > 0)
                || (Const.CREDIT_H_SUBTRACT.equals(dto.getDebitCredit()) && binDto.getFreezeOutput() > 0);
            if (flag) {
                freezeBinSet.add(binDto.getWhCode() + binDto.getTypeCode() + binDto.getBinCode());
            }
        }
        if (freezeBinSet.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_FREEZING, freezeBinSet.toString());
        }
    }

    /**
     * 根据借贷标识查询 冻结库存地点 H时 出库冻结 S时 入库冻结
     */
    private void checkFreezeLocation(StockInsMoveTypeDTO insMoveTypeVo) {
        List<StockInsDocBatch> insDocBatchList = insMoveTypeVo.getInsDocBatchList();
        List<StockInsDocBin> insDocBinList = insMoveTypeVo.getInsDocBinList();
        Set<String> freezeLocationSet = new HashSet<>();
        if (UtilCollection.isNotEmpty(insDocBatchList)) {
            for (StockInsDocBatch stockInsDocBatch : insDocBatchList) {
                DicStockLocationDTO locationDto = dictionaryService.getLocationCacheById(stockInsDocBatch.getLocationId());
                if (null == locationDto) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_SUCH_STOCK_LOCATION);
                }
                boolean flag = Boolean.FALSE;
                if (Const.DEBIT_S_ADD.equals(stockInsDocBatch.getDebitCredit()) && locationDto.getFreezeInput() > 0){
                    flag = Boolean.TRUE;
                }else if (Const.CREDIT_H_SUBTRACT.equals(stockInsDocBatch.getDebitCredit()) && locationDto.getFreezeOutput() > 0){
                    flag = Boolean.TRUE;
                }
                if (flag) {
                    String freezeLocation = locationDto.getFtyCode() + "-" + locationDto.getLocationCode();
                    freezeLocationSet.add(freezeLocation);
                }
            }
        }
//        if (UtilCollection.isNotEmpty(insDocBinList)) {
//            for (StockInsDocBin stockInsDocBin : insDocBinList) {
//                DicStockLocationDTO locationDto = dictionaryService.getLocationCacheById(stockInsDocBin.getLocationId());
//                if (null == locationDto) {
//                    throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_SUCH_STOCK_LOCATION);
//                }
//
//                boolean flag = (Const.DEBIT_S_ADD.equals(stockInsDocBin.getDebitCredit()) && locationDto.getFreezeInput() > 0) || (Const.CREDIT_H_SUBTRACT.equals(stockInsDocBin.getDebitCredit()) && locationDto.getFreezeOutput() > 0);
//                if (flag) {
//                    String freezeLocation = locationDto.getFtyCode() + "-" + locationDto.getLocationCode();
//                    freezeLocationSet.add(freezeLocation);
//                }
//            }
//        }
        if (freezeLocationSet.size() > 0) {
            // 冻结库存地点
            throw new WmsException(EnumReturnMsg.RETURN_CODE_LOCATION_FREEZING, freezeLocationSet.toString());
        }
    }

    /**
     * 查询冻结物料
     */
    private void checkFreezeMaterial(StockInsMoveTypeDTO insMoveTypeVo) {
        List<StockInsDocBatch> insDocBatchList = insMoveTypeVo.getInsDocBatchList();
        List<StockInsDocBin> insDocBinList = insMoveTypeVo.getInsDocBinList();
        if (insDocBinList == null || insDocBinList.size() == 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        Set<Long> matIdSet = new HashSet<>();
        if (insDocBatchList != null && insDocBatchList.size() > 0) {
            for (StockInsDocBatch insDocBatch : insDocBatchList) {
                matIdSet.add(insDocBatch.getMatId());
            }
        }
        if (insDocBinList.size() > 0) {
            for (StockInsDocBin insDocBin : insDocBinList) {
                matIdSet.add(insDocBin.getMatId());
            }
        }
        if (matIdSet.size() > 0) {
            List<Long> matIdList = new ArrayList<>(matIdSet);
            Set<String> freezeMatCodeSet = new HashSet<>();
            Collection<DicMaterialDTO> materialDtoList = dictionaryService.getMatListCacheByMatIdList(matIdList);
            for (DicMaterialDTO dto : materialDtoList) {
                if (dto.getIsFreeze() > 0) {
                    freezeMatCodeSet.add(dto.getMatCode());
                }
            }
            if (freezeMatCodeSet.size() > 0) {
                // 冻结物料
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_FREEZING, freezeMatCodeSet.toString());
            }
        }

    }

    /**
     * 根据stockBinPo查询已有库存
     *
     * @param stockBinPo 仓位库存查询条件
     * @return 已有仓位库存
     *
     */
    public List<StockBinDTO> getStockBinByStockBinPo(StockBinPO stockBinPo) {
        // 检查仓位库存查询入参，校验查询条件是否有效，并提前准备相关通用数据
        SearchStockDTO searchStockDto = stockCommonComponent.checkStockBatchPo(stockBinPo);

        // 临时存储类型限制时，自动添加全部临时存储类型
        if (stockBinPo.getTypeSet() != null && stockBinPo.getTypeSet().size() > 0) {
            stockBinPo.setAllTypeSet(UtilConst.getInstance().getDefaultStorageTypeCodeSet());
        }

        // 查询库存
        List<StockBinDTO> list = stockBinDataWrap.selectStockBinByStockBinPo(stockBinPo);

        // 查询条件是否有单位
        if (stockBinPo.getUnitCode() != null) {
            if (searchStockDto.getMultiplicationFactor() != null && searchStockDto.getDivisionFactor() != null) {
                for (StockBinDTO stockBinDto : list) {
                    // 设置数量
                    stockCommonComponent.setStockBinQty(stockBinDto, searchStockDto.getMultiplicationFactor(), searchStockDto.getDivisionFactor());
                }
            } else {
                for (StockBinDTO stockBinDto : list) {
                    // 乘系数 除系数
                    SearchStockDTO factorVo = new SearchStockDTO();
                    // 单位肯定存在
                    factorVo.setDicUnit(searchStockDto.getDicUnit());

                    // 工厂存在直接使用，不存在直接查询
                    if (stockBinPo.getFtyCode() == null) {
                        factorVo.setDicFactoryDto(dictionaryService.getFtyCacheById(stockBinDto.getFtyId()));
                    } else {
                        factorVo.setDicFactoryDto(searchStockDto.getDicFactoryDto());
                    }
                    // 物料存在直接使用，不存在直接查询
                    // 物料单位存在直接使用，不存在直接查询
                    if (stockBinPo.getMatCode() == null) {
                        factorVo.setDicMaterialDto(dictionaryService.getMatCacheById(stockBinDto.getMatId()));
                        factorVo.setMatUnit(dictionaryService.getUnitCacheById(factorVo.getDicMaterialDto().getUnitId()));
                    } else {
                        factorVo.setDicMaterialDto(searchStockDto.getDicMaterialDto());
                        factorVo.setMatUnit(searchStockDto.getMatUnit());
                    }

                    // 查询单位和物料单位不相同，需要数量转换
                    if (!stockBinPo.getUnitCode().equalsIgnoreCase(factorVo.getMatUnit().getUnitCode())) {
                        // 获取系数
                        stockCommonComponent.setFactorForStock(factorVo);
                        // 设置数量
                        stockCommonComponent.setStockBinQty(stockBinDto, factorVo.getMultiplicationFactor(), factorVo.getDivisionFactor());
                    }
                }
            }
        }
        return list;
    }

    /**
     * 根据stockBinPo中的批次信息查询已有库存
     *
     * @param stockBinPo 仓位库存查询条件
     * @return 已有批次库存
     *
     */
    public List<StockBatchDTO> getStockBatchByStockBatchCo(StockBinPO stockBinPo) {
        // 检查批次库存查询入参，校验查询条件是否有效，并提前准备相关通用数据
        SearchStockDTO searchStockVo = stockCommonComponent.checkStockBatchPo(stockBinPo);

        // 查询库存
        List<StockBatchDTO> list = stockBatchDataWrap.selectStockBatchByStockBatchPo(stockBinPo);

        // 查询条件是否有单位
        if (stockBinPo.getUnitCode() != null) {
            if (searchStockVo.getMultiplicationFactor() != null && searchStockVo.getDivisionFactor() != null) {
                for (StockBatchDTO stockBatchDto : list) {
                    // 设置数量
                    stockCommonComponent.setStockBatchQty(stockBatchDto, searchStockVo.getMultiplicationFactor(), searchStockVo.getDivisionFactor(),
                        searchStockVo.getDicUnit().getUnitCode(), searchStockVo.getDicUnit().getUnitName());
                }
            } else {
                for (StockBatchDTO stockBatchDto : list) {
                    // 乘系数 除系数
                    SearchStockDTO factorVo = new SearchStockDTO();
                    // 单位肯定存在
                    factorVo.setDicUnit(searchStockVo.getDicUnit());

                    // 工厂存在直接使用，不存在直接查询
                    if (stockBinPo.getFtyCode() == null) {
                        factorVo.setDicFactoryDto(dictionaryService.getFtyCacheById(stockBatchDto.getFtyId()));
                    } else {
                        factorVo.setDicFactoryDto(searchStockVo.getDicFactoryDto());
                    }
                    // 物料存在直接使用，不存在直接查询
                    // 物料单位存在直接使用，不存在直接查询
                    if (stockBinPo.getMatCode() == null) {
                        factorVo.setDicMaterialDto(dictionaryService.getMatCacheById(stockBatchDto.getMatId()));
                        factorVo.setMatUnit(dictionaryService.getUnitCacheById(factorVo.getDicMaterialDto().getUnitId()));
                    } else {
                        factorVo.setDicMaterialDto(searchStockVo.getDicMaterialDto());
                        factorVo.setMatUnit(searchStockVo.getMatUnit());
                    }

                    // 查询单位和物料单位不相同，需要数量转换
                    if (!stockBinPo.getUnitCode().equalsIgnoreCase(factorVo.getMatUnit().getUnitCode())) {

                        // 获取系数
                        stockCommonComponent.setFactorForStock(factorVo);

                        // 设置数量
                        stockCommonComponent.setStockBatchQty(stockBatchDto, factorVo.getMultiplicationFactor(), factorVo.getDivisionFactor(),
                            factorVo.getDicUnit().getUnitCode(), factorVo.getDicUnit().getUnitName());
                    }
                }
            }
        }
        return list;
    }

    /**
     * 根据stockBinPo中的批次信息批量查询已有库存
     *
     * @param stockBatchPoList 批次库存查询条件列表
     * @return 已有批次库存
     *
     */
    public List<StockBatchDTO> getStockBatchByStockBatchPoList(List<StockBinPO> stockBatchPoList) {
        // 批量查询库存
        List<StockBatchDTO> list = stockBatchDataWrap.selectStockBatchByStockBatchPoList(stockBatchPoList);
        // 处理后返回结果
        List<StockBatchDTO> resultList = new ArrayList<>();
        for (StockBinPO stockBinPo : stockBatchPoList) {
            // 检查批次库存查询入参，校验查询条件是否有效，并提前准备相关通用数据
            SearchStockDTO searchStockDto = stockCommonComponent.checkStockBatchPo(stockBinPo);
            // 查询条件是否有单位
            if (stockBinPo.getUnitCode() != null) {
                if (searchStockDto.getMultiplicationFactor() != null && searchStockDto.getDivisionFactor() != null) {
                    for (StockBatchDTO stockBatchDto : list) {
                        // 设置数量
                        stockCommonComponent.setStockBatchQty(stockBatchDto, searchStockDto.getMultiplicationFactor(),
                            searchStockDto.getDivisionFactor(), searchStockDto.getDicUnit().getUnitCode(), searchStockDto.getDicUnit().getUnitName());
                    }
                } else {
                    for (StockBatchDTO stockBatchDto : list) {
                        // 乘系数 除系数
                        SearchStockDTO factorVo = new SearchStockDTO();
                        // 单位肯定存在
                        factorVo.setDicUnit(searchStockDto.getDicUnit());

                        // 工厂存在直接使用，不存在直接查询
                        if (stockBinPo.getFtyCode() == null) {
                            factorVo.setDicFactoryDto(dictionaryService.getFtyCacheById(stockBatchDto.getFtyId()));
                        } else {
                            factorVo.setDicFactoryDto(searchStockDto.getDicFactoryDto());
                        }
                        // 物料存在直接使用，不存在直接查询
                        // 物料单位存在直接使用，不存在直接查询
                        if (stockBinPo.getMatCode() == null) {
                            factorVo.setDicMaterialDto(dictionaryService.getMatCacheById(stockBatchDto.getMatId()));
                            factorVo.setMatUnit(dictionaryService.getUnitCacheById(factorVo.getDicMaterialDto().getUnitId()));
                        } else {
                            factorVo.setDicMaterialDto(searchStockDto.getDicMaterialDto());
                            factorVo.setMatUnit(searchStockDto.getMatUnit());
                        }

                        // 查询单位和物料单位不相同，需要数量转换
                        if (!stockBinPo.getUnitCode().equalsIgnoreCase(factorVo.getMatUnit().getUnitCode())) {

                            // 获取系数
                            stockCommonComponent.setFactorForStock(factorVo);

                            // 设置数量
                            stockCommonComponent.setStockBatchQty(stockBatchDto, factorVo.getMultiplicationFactor(), factorVo.getDivisionFactor(),
                                factorVo.getDicUnit().getUnitCode(), factorVo.getDicUnit().getUnitName());
                        }
                    }
                }
            }
            resultList.addAll(list);
        }

        return resultList;
    }

    /**
     * 据仓位库存唯一索引批量查询仓位库存
     * @param keyList
     * @return
     */
    public List<StockBinDTO> getStockBinByStockBinKeyList(List<StockBinKey> keyList) {
        return stockBinDataWrap.selectStockBinByStockBinKeyList(keyList);
    }

    /**
     * 据仓位库存唯一索引批量查询盘点凭证仓位库存
     *
     * @param keyList 主键列表
     * @param headId 盘点凭证单主键
     * @return 仓位库存明细
     */
    public List<StockBinDTO> getStocktakingDocBinByStockBinKeyList(List<StockBinKey> keyList, Long headId) {
        return stockBinDataWrap.selectStocktakingDocBinByStockBinKeyList(keyList, headId);
    }


    /**
     * type=1 根据工厂+物料查询库存 type=2 根据工厂+物料+库存地点查询库存
     */
    public List<StockBatchDTO> getStockBatchByPoListAndType(List<StockBinPO> stockBatchPoList, Byte type) {
        List<StockBatchDTO> list = stockBatchDataWrap.selectStockBatchByPoListAndType(stockBatchPoList, type);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list;
        }
    }

    /**
     * 根据仓位库存创建凭证
     *
     * @param stockBinDto 仓位库存对象
     * @return 仓位凭证
     */
    private StockInsDocBin newStockInsDocBinDto(StockBinDTO stockBinDto, int compare, BigDecimal qty) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        insDocBin.setMatId(stockBinDto.getMatId());
        insDocBin.setBatchId(stockBinDto.getBatchId());
        insDocBin.setFtyId(stockBinDto.getFtyId());
        insDocBin.setLocationId(stockBinDto.getLocationId());
        insDocBin.setWhId(stockBinDto.getWhId());
        insDocBin.setTypeId(stockBinDto.getTypeId());
        insDocBin.setBinId(stockBinDto.getBinId());
        insDocBin.setCellId(stockBinDto.getCellId());
        if (compare > 0) {
            insDocBin.setActualQty(qty);
            insDocBin.setDebitCredit(Const.DEBIT_S_ADD);
        } else {
            insDocBin.setActualQty(qty.negate());
            insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        }
        return insDocBin;
    }

    /**
     * 根据特性查询库存
     *
     * @param receiptType 单据类型
     * @param ftyId 工厂id
     * @param locationId 库存地点id
     * @param matId 物料id
     * @param stockStatus 库存类型 EnumStockStatus
     * @param specStock 特殊库存标识
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getStockByFeatureCode(Integer receiptType, Long ftyId, Long locationId, Long matId, Integer stockStatus,
        String specStock) {
        return this.getStockByFeatureCodeAndValue(null, null, receiptType, ftyId, locationId, matId, stockStatus, specStock);
    }

    /**
     * 根据特性查询库存（工器具）
     *
     * @param receiptType 单据类型
     * @param po 库存特性查询PO
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getToolStockByFeatureCode(Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        return this.getToolStockByFeatureCodeAndValue(null, receiptType, po);
    }

    /**
     * 根据特性查询库存（石岛湾）
     *
     * @param receiptType 单据类型
     * @param po 库存特性查询PO
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getStockByFeatureCodeBySdw(Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        return this.getStockByFeatureCodeAndValueBySdw(null, null, receiptType, po);
    }

    /**
     * 根据特性查询库存
     * 可以在配货规则表biz_receipt_assemble_role配置规则，如果不配置则按照默认查询
     * 默认查询情况是不启用临时仓位进行库存计算的，也就是说默认情况查询到的库存是真实仓位的库存
     * 如果配置了规则，可以设置是否启用临时仓位进行库存计算
     *
     * @param valueObj 包含单个特性值的obj
     * @param valueObjList 包含多个特性值的obj集合
     * @param receiptType 单据类型
     * @param ftyId 工厂id
     * @param locationId 库存地点id
     * @param matId 物料id
     * @param stockStatus 库存类型 EnumStockStatus
     * @param specStock 特殊库存标识
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getStockByFeatureCodeAndValue(Object valueObj, List<StockBinDTO> valueObjList, Integer receiptType, Long ftyId, Long locationId, Long matId,
                                                                   Integer stockStatus, String specStock) {
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);

        BizReceiptAssembleRuleSearchPO po = new BizReceiptAssembleRuleSearchPO();
        po.setFtyId(ftyId);
        po.setLocationId(locationId);
        po.setMatId(matId);
        po.setStockStatus(stockStatus);
        po.setSpecStock(specStock);
        if (null != specFeature) {
            // 未配置特性, 则按照默认查询
            po.setFeatureCode(specFeature.getFeatureCode());
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }

        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }
        // 特性值处理
        if (UtilObject.isNotNull(valueObj)) {

            List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
            for (String specCode : po.getFeatureCodeList()) {
                // 去表名
                String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
                // 转驼峰
                fieldName = UtilMetadata.underlineToHump(fieldName);
                // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
                String specValue = UtilReflect.getValueByFieldNullReturnNull(fieldName, valueObj);
                if (UtilString.isNullOrEmpty(specValue)) {
                    // 特性值为空 则不参加特性匹配
                    continue;
                }
                // 拼装特性值列表
                BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                bizReceiptAssemble.setSpecCode(specCode);
                bizReceiptAssemble.setSpecValue(specValue);
                bizReceiptAssembleList.add(bizReceiptAssemble);
            }
            po.setAssembleList(bizReceiptAssembleList);
        }
        // 多个特性值处理
        if (UtilCollection.isNotEmpty(valueObjList)) {
            List<List<BizReceiptAssemble>> bizReceiptAssembleListList = new ArrayList<>();
            for (StockBinDTO stockBinDTO : valueObjList) {
                List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
                for (String specCode : po.getFeatureCodeList()) {
                    // 去表名
                    String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
                    // 转驼峰
                    fieldName = UtilMetadata.underlineToHump(fieldName);
                    // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
                    Object specValue = UtilReflect.getValueByFieldNullReturnNull(fieldName, stockBinDTO);
                    if (UtilString.isNullOrEmpty(String.valueOf(specValue))) {
                        // 特性值为空 则不参加特性匹配
                        continue;
                    }
                    // 拼装特性值列表
                    BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                    bizReceiptAssemble.setSpecCode(specCode);
                    bizReceiptAssemble.setSpecValue(String.valueOf(specValue));
                    bizReceiptAssembleList.add(bizReceiptAssemble);
                }
                bizReceiptAssembleListList.add(bizReceiptAssembleList);
            }
            po.setAssembleListList(bizReceiptAssembleListList);
        }
        List<BizReceiptAssembleDTO> assembleDTOList = stockBatchDataWrap.getStockBySpecFeature(po);
        assembleDTOList = assembleDTOList.stream().filter(p -> p.getStockQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(assembleDTOList) && UtilObject.isNotNull(valueObj)) {
            // 获取需要转换的单位id
            Long unitId = UtilReflect.getValueByFieldNullReturnNull(Const.UNIT_ID_FILED_NAME, valueObj);
            if (unitId != null) {
                // 单位换算处理
                Map<String, DicUnitRelDTO> relUnitMap = dictionaryService.getAllRelUnitCache();
                DicMaterialDTO matObj = dictionaryService.getMatCacheById(matId);
                RelUnitFactor relUnitFactor = this.getRelUnitFactor(ftyId, matId, matObj.getUnitId(), unitId, relUnitMap);
                assembleDTOList.forEach(e -> {
                    if (relUnitFactor.getMultiplicationFactor() != null && relUnitFactor.getDivisionFactor() != null) {
                        e.setStockQty(e.getStockQty().multiply(relUnitFactor.getMultiplicationFactor()).divide(relUnitFactor.getDivisionFactor(), 3,
                                RoundingMode.HALF_UP));
                    }
                });
            }
        }
        // 2021/5/14 根据specCode中的分组id填充specDisplayValue页面显示字段
        this.fillSpecDisplayValue(assembleDTOList,specFeature);

        BizReceiptAssembleRuleDTO assembleRuleDTO = UtilBean.newInstance(specFeature, BizReceiptAssembleRuleDTO.class);

        assembleRuleDTO.setAssembleDTOList(assembleDTOList);
        return assembleRuleDTO;
    }

    /**
     * 根据特性查询库存（工器具）
     * 可以在配货规则表biz_receipt_assemble_role配置规则，如果不配置则按照默认查询
     * 默认查询情况是不启用临时仓位进行库存计算的，也就是说默认情况查询到的库存是真实仓位的库存
     * 如果配置了规则，可以设置是否启用临时仓位进行库存计算
     *
     * @param valueObjList 包含多个特性值的obj集合
     * @param receiptType 单据类型
     * @param po 库存特性查询PO
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getToolStockByFeatureCodeAndValue(List<StockBinDTO> valueObjList, Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);
        if (null != specFeature) {
            // 未配置特性, 则按照默认查询
            po.setFeatureCode(specFeature.getFeatureCode());
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }

        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }
        // 多个特性值处理
        if (UtilCollection.isNotEmpty(valueObjList)) {
            List<List<BizReceiptAssemble>> bizReceiptAssembleListList = new ArrayList<>();
            for (StockBinDTO stockBinDTO : valueObjList) {
                List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
                for (String specCode : po.getFeatureCodeList()) {
                    // 去表名
                    String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
                    // 转驼峰
                    fieldName = UtilMetadata.underlineToHump(fieldName);
                    // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
                    Object specValue = UtilReflect.getValueByFieldNullReturnNull(fieldName, stockBinDTO);
                    if (UtilString.isNullOrEmpty(String.valueOf(specValue))) {
                        // 特性值为空 则不参加特性匹配
                        continue;
                    }
                    // 拼装特性值列表
                    BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                    bizReceiptAssemble.setSpecCode(specCode);
                    bizReceiptAssemble.setSpecValue(String.valueOf(specValue));
                    bizReceiptAssembleList.add(bizReceiptAssemble);
                }
                bizReceiptAssembleListList.add(bizReceiptAssembleList);
            }
            po.setAssembleListList(bizReceiptAssembleListList);
        }
        List<BizReceiptAssembleDTO> assembleDTOList = stockBatchDataWrap.getToolStockBySpecFeature(po);
        // 2021/5/14 根据specCode中的分组id填充specDisplayValue页面显示字段
        this.fillSpecDisplayValue(assembleDTOList,specFeature);

        BizReceiptAssembleRuleDTO assembleRuleDTO = UtilBean.newInstance(specFeature, BizReceiptAssembleRuleDTO.class);

        assembleRuleDTO.setAssembleDTOList(assembleDTOList);
        return assembleRuleDTO;
    }

    /**
     * 根据特性查询库存（石岛湾）
     * 可以在配货规则表biz_receipt_assemble_role配置规则，如果不配置则按照默认查询
     * 默认查询情况是不启用临时仓位进行库存计算的，也就是说默认情况查询到的库存是真实仓位的库存
     * 如果配置了规则，可以设置是否启用临时仓位进行库存计算
     *
     * @param valueObj 包含单个特性值的obj
     * @param valueObjList 包含多个特性值的obj集合
     * @param receiptType 单据类型
     * @param po 库存特性查询PO
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getStockByFeatureCodeAndValueBySdw(Object valueObj, List<StockBinDTO> valueObjList, Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        // 装载单据类型
        po.setReceiptType(receiptType);
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);

        if (null != specFeature) {
            // 未配置特性, 则按照默认查询
            po.setFeatureCode(specFeature.getFeatureCode());
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }

        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }
        // 特性值处理
        if (UtilObject.isNotNull(valueObj)) {

            List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
            for (String specCode : po.getFeatureCodeList()) {
                // 去表名
                String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
                // 转驼峰
                fieldName = UtilMetadata.underlineToHump(fieldName);
                // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
                String specValue = UtilObject.getStringOrEmpty(UtilReflect.getValueByFieldNullReturnNull(fieldName, valueObj));
                if (UtilString.isNullOrEmpty(specValue)) {
                    // 特性值为空 则不参加特性匹配
                    continue;
                }
                // 拼装特性值列表
                BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                bizReceiptAssemble.setSpecCode(specCode);
                bizReceiptAssemble.setSpecValue(specValue);
                bizReceiptAssembleList.add(bizReceiptAssemble);
            }
            po.setAssembleList(bizReceiptAssembleList);
        }
        // 多个特性值处理
        if (UtilCollection.isNotEmpty(valueObjList)) {
            List<List<BizReceiptAssemble>> bizReceiptAssembleListList = new ArrayList<>();
            for (StockBinDTO stockBinDTO : valueObjList) {
                List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
                for (String specCode : po.getFeatureCodeList()) {
                    // 去表名
                    String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
                    // 转驼峰
                    fieldName = UtilMetadata.underlineToHump(fieldName);
                    // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
                    Object specValue = UtilReflect.getValueByFieldNullReturnNull(fieldName, stockBinDTO);
                    if (UtilString.isNullOrEmpty(String.valueOf(specValue))) {
                        // 特性值为空 则不参加特性匹配
                        continue;
                    }
                    // 拼装特性值列表
                    BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                    bizReceiptAssemble.setSpecCode(specCode);
                    bizReceiptAssemble.setSpecValue(String.valueOf(specValue));
                    bizReceiptAssembleList.add(bizReceiptAssemble);
                }
                bizReceiptAssembleListList.add(bizReceiptAssembleList);
            }
            po.setAssembleListList(bizReceiptAssembleListList);
        }
        List<BizReceiptAssembleDTO> assembleDTOList = stockBatchDataWrap.getStockBySpecFeatureBySdw(po);
        assembleDTOList = assembleDTOList.stream().filter(p -> p.getStockQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(assembleDTOList) && UtilObject.isNotNull(valueObj) && UtilNumber.isNotEmpty(po.getMatId())) {
            // 获取需要转换的单位id
            Long unitId = UtilReflect.getValueByFieldNullReturnNull(Const.UNIT_ID_FILED_NAME, valueObj);
            if (unitId != null) {
                // 单位换算处理
                Map<String, DicUnitRelDTO> relUnitMap = dictionaryService.getAllRelUnitCache();
                DicMaterialDTO matObj = dictionaryService.getMatCacheById(po.getMatId());
                RelUnitFactor relUnitFactor = this.getRelUnitFactor(po.getFtyId(), po.getMatId(), matObj.getUnitId(), unitId, relUnitMap);
                assembleDTOList.forEach(e -> {
                    if (relUnitFactor.getMultiplicationFactor() != null && relUnitFactor.getDivisionFactor() != null) {
                        e.setStockQty(e.getStockQty().multiply(relUnitFactor.getMultiplicationFactor()).divide(relUnitFactor.getDivisionFactor(), 3,
                                RoundingMode.HALF_UP));
                    }
                });
            }
        }else {
            Set<Long> matIdSet = po.getMatIdSet();
            if(UtilCollection.isNotEmpty(assembleDTOList) && UtilObject.isNotNull(valueObj) && UtilCollection.isNotEmpty(matIdSet)){
                // 获取需要转换的单位id
                Long unitId = UtilReflect.getValueByFieldNullReturnNull(Const.UNIT_ID_FILED_NAME, valueObj);
                if (unitId != null) {
                    // 单位换算处理
                    Map<String, DicUnitRelDTO> relUnitMap = dictionaryService.getAllRelUnitCache();
                    for (Long matId : matIdSet) {
                        DicMaterialDTO matObj = dictionaryService.getMatCacheById(matId);
                        RelUnitFactor relUnitFactor = this.getRelUnitFactor(po.getFtyId(), po.getMatId(), matObj.getUnitId(), unitId, relUnitMap);
                        assembleDTOList.forEach(e -> {
                            if (relUnitFactor.getMultiplicationFactor() != null && relUnitFactor.getDivisionFactor() != null) {
                                e.setStockQty(e.getStockQty().multiply(relUnitFactor.getMultiplicationFactor()).divide(relUnitFactor.getDivisionFactor(), 3,
                                        RoundingMode.HALF_UP));
                            }
                        });
                    }
                }
            }
        }
        // 2021/5/14 根据specCode中的分组id填充specDisplayValue页面显示字段
        this.fillSpecDisplayValue(assembleDTOList,specFeature);

        BizReceiptAssembleRuleDTO assembleRuleDTO = UtilBean.newInstance(specFeature, BizReceiptAssembleRuleDTO.class);

        assembleRuleDTO.setAssembleDTOList(assembleDTOList);
        return assembleRuleDTO;
    }

    /**
    * 将多个特性值，封装成StockBinDTO集合对象
    **/
    public List<StockBinDTO> fillSpecCodeAndValue(List<BizReceiptAssembleDTO> assembleDTOList) {
        List<String> codeListComma = assembleDTOList.stream().map(BizReceiptAssembleDTO::getSpecCode).collect(Collectors.toList());
        List<String> valueListComma = assembleDTOList.stream().map(BizReceiptAssembleDTO::getSpecValue).collect(Collectors.toList());
        String tableName = StockBin.class.getAnnotation(TableName.class).value();
        String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
        String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
        String tableFieldNameBinId = tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
        List<StockBinDTO> stockBinDTOList = new ArrayList<>();
        for (int j = 0; j < codeListComma.size(); j++) {
            List<String> codeList = UtilString.split(codeListComma.get(j), Const.COMMA_CHAR);
            List<String> valueList = UtilString.split(valueListComma.get(j), Const.COMMA_CHAR);
            StockBinDTO stockBinDTO = new StockBinDTO();
            for (int i = 0; i < codeList.size(); i++) {
                if (codeList.get(i).equals(tableFieldNameBatchId)) {
                    // 批次
                    Long batchId = Long.parseLong(valueList.get(i));
                    stockBinDTO.setBatchId(batchId);
                } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                    // 存储类型
                    stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                    // 存储单元
                    stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                    // 仓位
                    stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                }
            }
            stockBinDTOList.add(stockBinDTO);
        }
        return stockBinDTOList;
    }

    private void fillSpecDisplayValue(List<BizReceiptAssembleDTO> assembleDTOList, BizReceiptAssembleRule specFeature) {
        if(UtilCollection.isEmpty(assembleDTOList)){
            return;
        }

        if (null == specFeature) {
            return;
        }
        String featureDisplayCode = specFeature.getFeatureDisplayCode();

        if(UtilString.isNullOrEmpty(featureDisplayCode)){
            // 不需要转换
            assembleDTOList.forEach(e->e.setSpecDisplayValue(e.getSpecValue()));
            return;
        }

        List<String> fillCodeList = Arrays.asList(featureDisplayCode.split(Const.COMMA));

        String[][] fillValueArray = new String[assembleDTOList.size()][fillCodeList.size()];

        for (int i = 0; i < fillCodeList.size(); i++) {
            String fillCode = fillCodeList.get(i);
            if (fillCode.equalsIgnoreCase(Const.NULL)) {
                // 不需要 填充 直接取 原值
                for (int j = 0; j < fillValueArray.length; j++) {
                    List<String> valueList = UtilString.split(assembleDTOList.get(j).getSpecValue(), Const.COMMA_CHAR);
                    fillValueArray[j][i] = valueList.get(i);
                }
            } else {
                String tableName = fillCode.split(Const.POINT_ESCAPE)[0];
                String tableColum = fillCode.split(Const.POINT_ESCAPE)[1];
                int columnIndex = i;
                List idList = assembleDTOList.stream().map(e -> {
                    List<String> valueList = UtilString.split(e.getSpecValue(), Const.COMMA_CHAR);
                    return UtilObject.getLongOrNull(valueList.get(columnIndex));
                }).collect(Collectors.toList());
                IService mPlusService = MetadataContext.getMplusService(tableName);

                if (mPlusService != null) {
                    List<Object> relationDataList = mPlusService.listByIds(idList);
                    Map<Long, Object> relationDataMap = relationDataList.stream().collect(Collectors.toMap(obj -> {
                        return UtilReflect.getValueByField("id", obj);
                    }, obj -> obj));

                    for (int j = 0; j < fillValueArray.length; j++) {
                        List<String> valueList = UtilString.split(assembleDTOList.get(j).getSpecValue(), Const.COMMA_CHAR);
                        Long id = UtilObject.getLongOrNull(valueList.get(columnIndex));
                        Object relationData = relationDataMap.get(id);
                        if (relationData != null) {
                            fillValueArray[j][i] = UtilReflect.getValueByField(UtilMetadata.underlineToHump(tableColum), relationData);
                        } else {
                            fillValueArray[j][i] = Const.STRING_EMPTY;
                        }

                    }
                }

            }
        }
        for (int i = 0; i < assembleDTOList.size(); i++) {

            String specDisplayValue = String.join(Const.COMMA, fillValueArray[i]);
            assembleDTOList.get(i).setSpecDisplayValue(specDisplayValue);

        }

    }


    /**
     * 根据特性查询仓位库存 排除临时区
     *
     * @param valueObj 包含特性值的obj
     * @param ftyId 工厂id
     * @param locationId 库存地点id
     * @param matId 物料id
     * @param stockStatus 库存类型 EnumStockStatus
     * @param specStock 特殊库存标识
     * @return 根据特性值查询出的库存
     */
    public List<StockBinDTO> getStockBinByFeatureCodeAndValue(Object valueObj, Long ftyId, Long locationId, Long matId, Integer stockStatus,String specStock) {

        BizReceiptAssembleRuleSearchPO po = new BizReceiptAssembleRuleSearchPO();
        po.setFtyId(ftyId);
        po.setLocationId(locationId);
        po.setMatId(matId);
        po.setStockStatus(stockStatus);
        po.setSpecStock(specStock);
        // 特性值处理
        if (UtilObject.isNotNull(valueObj)) {

            List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
            String specCodes = UtilReflect.getValueByFieldNullReturnNull(Const.SPEC_CODE_FILED_NAME, valueObj);
            String specValues = UtilReflect.getValueByFieldNullReturnNull(Const.SPEC_VALUE_FILED_NAME, valueObj);
            if (UtilString.isNotNullOrEmpty(specCodes) && UtilString.isNotNullOrEmpty(specValues)) {
                List<String> specCodeList = Arrays.asList(specCodes.split(Const.COMMA,-1));
                List<String> specValueList = Arrays.asList(specValues.split(Const.COMMA,-1));
                for (int i = 0; i < specCodeList.size(); i++) {
                    BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                    bizReceiptAssemble.setSpecCode(specCodeList.get(i));
                    bizReceiptAssemble.setSpecValue(specValueList.get(i));
                    bizReceiptAssembleList.add(bizReceiptAssemble);
                }
            }

            po.setAssembleList(bizReceiptAssembleList);
        }
        List<StockBinDTO> stockBinDTOList = stockBinDataWrap.selectStockBinBySpecFeature(po);

        if (UtilCollection.isNotEmpty(stockBinDTOList) && UtilObject.isNotNull(valueObj)) {
            // 单位转换
            // 获取需要转换的单位id
            Long unitId = UtilReflect.getValueByFieldNullReturnNull(Const.UNIT_ID_FILED_NAME, valueObj);
            if (unitId != null) {
                // 单位换算处理
                Map<String, DicUnitRelDTO> relUnitMap = dictionaryService.getAllRelUnitCache();
                DicMaterialDTO matObj = dictionaryService.getMatCacheById(matId);
                RelUnitFactor relUnitFactor = this.getRelUnitFactor(ftyId, matId, matObj == null ? null : matObj.getUnitId(), unitId, relUnitMap);
                stockBinDTOList.forEach(e -> {
                    if (relUnitFactor.getMultiplicationFactor() != null && relUnitFactor.getDivisionFactor() != null) {
                        e.setStockQty(e.getStockQty().multiply(relUnitFactor.getMultiplicationFactor()).divide(relUnitFactor.getDivisionFactor(), 3,
                            RoundingMode.HALF_UP));

                    }
                });
            }
            // 设置标签 根据batchId binId cellId 查询 对应标签
            List<BizLabelData> labelDataList = labelDataDataWrap.selectByStockBinList(stockBinDTOList);
            if (UtilCollection.isNotEmpty(labelDataList)) {
                Map<String,
                    List<BizLabelData>> labelDataMap = labelDataList.stream()
                        .collect(Collectors.groupingBy(e -> e.getMatId() + Const.HYPHEN + e.getFtyId() + Const.HYPHEN + e.getLocationId()
                            + Const.HYPHEN + e.getBatchId() + Const.HYPHEN + e.getWhId() + Const.HYPHEN + e.getTypeId() + Const.HYPHEN + e.getBinId()
                            + Const.HYPHEN + e.getCellId()));
                for (StockBinDTO stockBinDTO : stockBinDTOList) {
                    String key = stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN + stockBinDTO.getLocationId()
                        + Const.HYPHEN + stockBinDTO.getBatchId() + Const.HYPHEN + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId()
                        + Const.HYPHEN + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                    List<BizLabelData> innerLabelDataList = labelDataMap.get(key);
                    stockBinDTO.setLabelDataList(innerLabelDataList);

                }
            }
        }

        return stockBinDTOList;
    }

    public List<StockBinDTO> getStockBinByApply(BizReceiptTaskReqItemDTO reqItem) {
        List<StockBinDTO> stockBinDTOList = stockBinDataWrap.getStockBinByApply(reqItem);
        // 设置标签 根据batchId binId cellId 查询 对应标签
        List<BizLabelData> labelDataList = labelDataDataWrap.selectByStockBinList(stockBinDTOList);
        if (UtilCollection.isNotEmpty(labelDataList)) {
            Map<String,
                    List<BizLabelData>> labelDataMap = labelDataList.stream()
                    .collect(Collectors.groupingBy(e -> e.getMatId() + Const.HYPHEN + e.getFtyId() + Const.HYPHEN + e.getLocationId()
                            + Const.HYPHEN + e.getBatchId() + Const.HYPHEN + e.getWhId() + Const.HYPHEN + e.getTypeId() + Const.HYPHEN + e.getBinId()
                            + Const.HYPHEN + e.getCellId()));
            for (StockBinDTO stockBinDTO : stockBinDTOList) {
                String key = stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN + stockBinDTO.getLocationId()
                        + Const.HYPHEN + stockBinDTO.getBatchId() + Const.HYPHEN + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId()
                        + Const.HYPHEN + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                List<BizLabelData> innerLabelDataList = labelDataMap.get(key);
                stockBinDTO.setLabelDataList(innerLabelDataList);

            }
        }
        return stockBinDTOList;
    }

    /**
     * 根据特征批量查询库存(占用库存使用) 排除临时区
     *
     * @param po 特征po
     * @return 根据特性值查询出的库存
     */
    public List<StockBinDTO> selectStockBinByAssembleList(BizReceiptAssembleRuleSearchPO po) {
        if (UtilString.isNotNullOrEmpty(po.getFeatureCode())) {
            // 特征code处理,sql中使用
            po.setFeatureCode(po.getFeatureCode().replaceAll(Const.COMMA, ",',',"));
        }
        List<BizReceiptAssemble> assembleList = po.getAssembleList();
        for (BizReceiptAssemble assembleDTO : assembleList) {
            // 特征code处理,sql中使用
            if (UtilString.isNotNullOrEmpty(assembleDTO.getSpecCode())) {
                assembleDTO.setSpecCode(assembleDTO.getSpecCode().replaceAll(Const.COMMA, ",',',"));
            }
        }
        return stockBinDataWrap.selectStockBinByAssembleList(po);
    }

    /**
     * 根据单据类型获取库存状态： 10-非限制库存 ,20-在途库存,30-质量检验库存 ,40-冻结的库存,50-紧急的库存,60-临时的库存
     *
     * @param receiptType 单据类型
     * @return 库存状态 默认类型非限制库存
     */
    public Integer getStockStatus(Integer receiptType) {
        // 默认非限制库存
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 临时库存
        if (EnumReceiptType.STOCK_INPUT_TEMP.getValue().equals(receiptType) ||
                EnumReceiptType.STOCK_OUTPUT_TEMP.getValue().equals(receiptType)) {
            stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_TEMP.getValue();
        }
        // TODO 根据单据实际类型添加库存状态
        return stockStatus;

    }

    /**
     * 根据单据类型获取库存状态： 10-非限制库存 ,20-在途库存,30-质量检验库存 ,40-冻结的库存,50-紧急的库存,60-临时的库存
     *
     * @param receiptType 单据类型
     * @return 库存状态 默认类型非限制库存
     */
    public Integer getStockStatus(Integer receiptType,Integer referReceiptType) {
        // 默认非限制库存
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 临时库存
        if (EnumReceiptType.STOCK_INPUT_TEMP.getValue().equals(receiptType) ||
                EnumReceiptType.STOCK_OUTPUT_TEMP.getValue().equals(receiptType)) {
            stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_TEMP.getValue();
        }
        if(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ.getValue().equals(receiptType)){
            if (UtilNumber.isNotEmpty(referReceiptType) && referReceiptType.equals(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_INCONFORMITY.getValue())) {
                stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue();
            }
        }
        // TODO 根据单据实际类型添加库存状态
        return stockStatus;

    }

    /**
     * 获取仓位库存凭证信息
     * @param wrapper
     * @return
     */
    public List<StockInsDocBinDTO> getStockInsDocBinListByWrapper(WmsQueryWrapper<StockInsDocBin> wrapper, boolean isUnitized){
        List<StockInsDocBin> stockInsDocBinList = stockInsDocBinDataWrap.list(wrapper,isUnitized);
        return  UtilCollection.toList(stockInsDocBinList, StockInsDocBinDTO.class);
    }

    /**
     * 根据特性，批次号等查询库存
     *
     * @param receiptType 单据类型
     * @param po 库存特性查询PO
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getStockByFeatureCodeBySdwByBatchCode(Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        return this.getStockByFeatureCodeAndValueBySdwByBatchCode(receiptType, po);
    }


    /**
     * 根据特性，批次号等查询库存
     * 由getStockByFeatureCodeAndValueBySdw方法复制过来去掉一些用不到的地方
     * 查询库存信息时去掉存储类型判断, 所有存储类型的物资都可以导入为闲置
     * @param receiptType 单据类型
     * @param po 库存特性查询PO
     * @return 已有特性库存
     */
    public BizReceiptAssembleRuleDTO getStockByFeatureCodeAndValueBySdwByBatchCode(Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        // 装载单据类型
        po.setReceiptType(receiptType);
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);

        if (null != specFeature) {
            // 未配置特性, 则按照默认查询
            po.setFeatureCode(specFeature.getFeatureCode());
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }

        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }

        List<BizReceiptAssembleDTO> assembleDTOList = stockBatchDataWrap.getStockBySpecFeatureBySdwByBatchCode(po);
        assembleDTOList = assembleDTOList.stream().filter(p -> p.getStockQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        Set<Long> matIdSet = po.getMatIdSet();

        // 2021/5/14 根据specCode中的分组id填充specDisplayValue页面显示字段
        this.fillSpecDisplayValue(assembleDTOList,specFeature);

        BizReceiptAssembleRuleDTO assembleRuleDTO = UtilBean.newInstance(specFeature, BizReceiptAssembleRuleDTO.class);

        assembleRuleDTO.setAssembleDTOList(assembleDTOList);
        return assembleRuleDTO;
    }

    static class RelUnitFactor {

        private BigDecimal multiplicationFactor;
        private BigDecimal divisionFactor;

        public BigDecimal getMultiplicationFactor() {
            return multiplicationFactor;
        }

        public void setMultiplicationFactor(BigDecimal multiplicationFactor) {
            this.multiplicationFactor = multiplicationFactor;
        }

        public BigDecimal getDivisionFactor() {
            return divisionFactor;
        }

        public void setDivisionFactor(BigDecimal divisionFactor) {
            this.divisionFactor = divisionFactor;
        }
    }

    @Data
    @AllArgsConstructor
    static class PreReceiptObj{
        private Long preReceiptItemId;
        private Long preReceiptBinId;
    }

    public BizReceiptAssembleRuleDTO getStockByFeatureCodeAndValueBySdw4Unitized(Object valueObj, List<StockBinDTO> valueObjList, Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        // 装载单据类型
        po.setReceiptType(receiptType);
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);

        if (null != specFeature) {
            // 未配置特性, 则按照默认查询
            po.setFeatureCode(specFeature.getFeatureCode());
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }

        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }
        // 特性值处理
        if (UtilObject.isNotNull(valueObj)) {

            List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
            for (String specCode : po.getFeatureCodeList()) {
                // 去表名
                String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
                // 转驼峰
                fieldName = UtilMetadata.underlineToHump(fieldName);
                // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
                String specValue = UtilObject.getStringOrEmpty(UtilReflect.getValueByFieldNullReturnNull(fieldName, valueObj));
                if (UtilString.isNullOrEmpty(specValue)) {
                    // 特性值为空 则不参加特性匹配
                    continue;
                }
                // 拼装特性值列表
                BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                bizReceiptAssemble.setSpecCode(specCode);
                bizReceiptAssemble.setSpecValue(specValue);
                bizReceiptAssembleList.add(bizReceiptAssemble);
            }
            po.setAssembleList(bizReceiptAssembleList);
        }
        // 多个特性值处理
        if (UtilCollection.isNotEmpty(valueObjList)) {
            List<List<BizReceiptAssemble>> bizReceiptAssembleListList = new ArrayList<>();
            for (StockBinDTO stockBinDTO : valueObjList) {
                List<BizReceiptAssemble> bizReceiptAssembleList = new ArrayList<>();
                for (String specCode : po.getFeatureCodeList()) {
                    // 去表名
                    String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
                    // 转驼峰
                    fieldName = UtilMetadata.underlineToHump(fieldName);
                    // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
                    Object specValue = UtilReflect.getValueByFieldNullReturnNull(fieldName, stockBinDTO);
                    if (UtilString.isNullOrEmpty(String.valueOf(specValue))) {
                        // 特性值为空 则不参加特性匹配
                        continue;
                    }
                    // 拼装特性值列表
                    BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble();
                    bizReceiptAssemble.setSpecCode(specCode);
                    bizReceiptAssemble.setSpecValue(String.valueOf(specValue));
                    bizReceiptAssembleList.add(bizReceiptAssemble);
                }
                bizReceiptAssembleListList.add(bizReceiptAssembleList);
            }
            po.setAssembleListList(bizReceiptAssembleListList);
        }
        List<BizReceiptAssembleDTO> assembleDTOList = stockBatchDataWrap.getStockBySpecFeatureBySdw4Unitized(po);
        assembleDTOList = assembleDTOList.stream().filter(p -> p.getStockQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(assembleDTOList) && UtilObject.isNotNull(valueObj) && UtilNumber.isNotEmpty(po.getMatId())) {
            // 获取需要转换的单位id
            Long unitId = UtilReflect.getValueByFieldNullReturnNull(Const.UNIT_ID_FILED_NAME, valueObj);
            if (unitId != null) {
                // 单位换算处理
                Map<String, DicUnitRelDTO> relUnitMap = dictionaryService.getAllRelUnitCache();
                DicMaterialDTO matObj = dictionaryService.getMatCacheById(po.getMatId());
                RelUnitFactor relUnitFactor = this.getRelUnitFactor(po.getFtyId(), po.getMatId(), matObj.getUnitId(), unitId, relUnitMap);
                assembleDTOList.forEach(e -> {
                    if (relUnitFactor.getMultiplicationFactor() != null && relUnitFactor.getDivisionFactor() != null) {
                        e.setStockQty(e.getStockQty().multiply(relUnitFactor.getMultiplicationFactor()).divide(relUnitFactor.getDivisionFactor(), 3,
                                RoundingMode.HALF_UP));
                    }
                });
            }
        }else {
            Set<Long> matIdSet = po.getMatIdSet();
            if(UtilCollection.isNotEmpty(assembleDTOList) && UtilObject.isNotNull(valueObj) && UtilCollection.isNotEmpty(matIdSet)){
                // 获取需要转换的单位id
                Long unitId = UtilReflect.getValueByFieldNullReturnNull(Const.UNIT_ID_FILED_NAME, valueObj);
                if (unitId != null) {
                    // 单位换算处理
                    Map<String, DicUnitRelDTO> relUnitMap = dictionaryService.getAllRelUnitCache();
                    for (Long matId : matIdSet) {
                        DicMaterialDTO matObj = dictionaryService.getMatCacheById(matId);
                        RelUnitFactor relUnitFactor = this.getRelUnitFactor(po.getFtyId(), po.getMatId(), matObj.getUnitId(), unitId, relUnitMap);
                        assembleDTOList.forEach(e -> {
                            if (relUnitFactor.getMultiplicationFactor() != null && relUnitFactor.getDivisionFactor() != null) {
                                e.setStockQty(e.getStockQty().multiply(relUnitFactor.getMultiplicationFactor()).divide(relUnitFactor.getDivisionFactor(), 3,
                                        RoundingMode.HALF_UP));
                            }
                        });
                    }
                }
            }
        }
        // 2021/5/14 根据specCode中的分组id填充specDisplayValue页面显示字段
        this.fillSpecDisplayValue(assembleDTOList,specFeature);

        BizReceiptAssembleRuleDTO assembleRuleDTO = UtilBean.newInstance(specFeature, BizReceiptAssembleRuleDTO.class);

        assembleRuleDTO.setAssembleDTOList(assembleDTOList);
        return assembleRuleDTO;
    }

    public List<BizReceiptAssembleLifetimeDTO> getStockByPkgTypeBySdw(Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        // 装载单据类型
        po.setReceiptType(receiptType);
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);
        String featureCode = null;
        String featureName = null;
        if (null != specFeature) {
            featureCode = specFeature.getFeatureCode();
            featureName = specFeature.getFeatureName();
            // 未配置特性, 则按照默认查询
            po.setFeatureCode(featureCode);
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }

        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }
        List<BizReceiptAssembleLifetimeDTO> assembleDTOList = stockBatchDataWrap.getStockByPkgTypeBySdw(po);
        List<BizReceiptAssembleLifetimeDTO> assembleList = new ArrayList<>();
        for (BizReceiptAssembleLifetimeDTO item : assembleDTOList) {
            if (item.getStockQty().compareTo(BigDecimal.ZERO) > 0) {
                item.setRuleFeatureCode(featureCode);
                item.setRuleFeatureName(featureName);
                assembleList.add(item);
            }
        }
        return assembleList;
    }

    public List<BizReceiptAssembleMaintainDTO> getMaintainStockBySdw(Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        // 装载单据类型
        po.setReceiptType(receiptType);
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);
        String featureCode = null;
        String featureName = null;
        if (null != specFeature) {
            // 未配置特性, 则按照默认查询
            featureCode = specFeature.getFeatureCode();
            featureName = specFeature.getFeatureName();
            po.setFeatureCode(featureCode);
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }
        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }
        List<BizReceiptAssembleMaintainDTO> assembleDTOList = stockBatchDataWrap.getMaintainStockBySdw(po);
        List<BizReceiptAssembleMaintainDTO> assembleList = new ArrayList<>();
        for (BizReceiptAssembleMaintainDTO item : assembleDTOList) {
            if (item.getStockQty().compareTo(BigDecimal.ZERO) > 0) {
                item.setRuleFeatureCode(featureCode);
                item.setRuleFeatureName(featureName);
                assembleList.add(item);
            }
        }
        return assembleList;
    }


    /**
     * 根据特性code查询特性库存 成套设备
     * @param receiptType
     * @param po
     * @return
     */
    public List<BizReceiptAssembleMaintainDTO> getMaintainStockBySdwUnitized(Integer receiptType, BizReceiptAssembleRuleSearchPO po) {
        // 装载单据类型
        po.setReceiptType(receiptType);
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, receiptType);
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);
        String featureCode = null;
        String featureName = null;
        if (null != specFeature) {
            // 未配置特性, 则按照默认查询
            featureCode = specFeature.getFeatureCode();
            featureName = specFeature.getFeatureName();
            po.setFeatureCode(featureCode);
            po.setIsUseTemporaryBin(specFeature.getIsUseTemporaryBin());
            if (UtilString.hasText(po.getFeatureCode())) {
                List<String> featureCodeList = Arrays.asList(po.getFeatureCode().split(Const.COMMA));
                po.setFeatureCodeList(featureCodeList);
            } else {
                po.setFeatureCodeList(new ArrayList<>());
            }
        } else {
            po.setFeatureCodeList(new ArrayList<>());
        }
        List<BizReceiptAssembleMaintainDTO> assembleDTOList = stockBatchDataWrap.getMaintainStockBySdwUnitized(po);
        List<BizReceiptAssembleMaintainDTO> assembleList = new ArrayList<>();
        for (BizReceiptAssembleMaintainDTO item : assembleDTOList) {
            if (item.getStockQty().compareTo(BigDecimal.ZERO) > 0) {
                item.setRuleFeatureCode(featureCode);
                item.setRuleFeatureName(featureName);
                assembleList.add(item);
            }
        }
        return assembleList;
    }

    /**
     * 根据出库凭证时间范围，查询关联的INS仓位库存凭证列表
     * @param wrapper
     * @return
     */
    public List<StockInsDocBinDTO> getRelatedStockBinDocByOutputDocDate(WmsQueryWrapper<StockInsDocBin> wrapper, Boolean isUnitized) {
        return stockInsDocBinDataWrap.selectRelatedStockBinDocByOutputDocDate(wrapper, isUnitized);
    }


    /**
     * 按照批次库存唯一键，合并凭证中对于统一个库存记录的数量修改
     *
     * @param insDocBatchList 批次库存移动凭证列表
     * @return 返回按照批次凭证唯一键合并后的库存修改对象
     * <AUTHOR> <<EMAIL>>
     */
    private Map<StockBatchKey, StockBatch> mergeInsDocBatch(List<StockInsDocBatch> insDocBatchList) {
        // 按照批次库存唯一键，合并凭证中对于统一个库存记录的数量修改
        Map<StockBatchKey, StockBatch> uniqueKeyMap = new HashMap<>();

        for (StockInsDocBatch insDocBatch : insDocBatchList) {

            // 如果有凭证移动数量为零的数据，认为不需要修改库存，直接跳过
            if(UtilNumber.isEmpty(insDocBatch.getActualQty())){
                continue;
            }

            // 从凭证中拿出唯一键所需的物料id、批次id、工厂id、库存地点id
            StockBatchKey uniqueKey = new StockBatchKey(insDocBatch.getMatId(), insDocBatch.getBatchId(), insDocBatch.getFtyId(), insDocBatch.getLocationId());

            StockBatch stockBatch;
            if (uniqueKeyMap.containsKey(uniqueKey)) {
                stockBatch = uniqueKeyMap.get(uniqueKey);
            } else {
                stockBatch = new StockBatch();
                stockBatch.setMatId(insDocBatch.getMatId());
                stockBatch.setBatchId(insDocBatch.getBatchId());
                stockBatch.setFtyId(insDocBatch.getFtyId());
                stockBatch.setLocationId(insDocBatch.getLocationId());
                stockBatch.setCreateUserId(insDocBatch.getCreateUserId());
                stockBatch.setModifyUserId(insDocBatch.getModifyUserId());

                // 库存数量信息初始化
                stockBatch.setQty(BigDecimal.ZERO);
                stockBatch.setQtyTransfer(BigDecimal.ZERO);
                stockBatch.setQtyInspection(BigDecimal.ZERO);
                stockBatch.setQtyFreeze(BigDecimal.ZERO);
                stockBatch.setQtyHaste(BigDecimal.ZERO);
                stockBatch.setQtyTemp(BigDecimal.ZERO);
            }

            // 根据借贷标识，计算出要加总的数量
            BigDecimal qty = Const.DEBIT_S_ADD.equals(insDocBatch.getDebitCredit()) ? insDocBatch.getActualQty() : BigDecimal.ZERO.subtract(insDocBatch.getActualQty());
            if(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue().equals(insDocBatch.getStockStatus())){
                stockBatch.setQty(stockBatch.getQty().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_ON_THE_WAY.getValue().equals(insDocBatch.getStockStatus())){
                stockBatch.setQtyTransfer(stockBatch.getQtyTransfer().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_QUALITY_INSPECTION.getValue().equals(insDocBatch.getStockStatus())){
                stockBatch.setQtyInspection(stockBatch.getQtyInspection().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue().equals(insDocBatch.getStockStatus())){
                stockBatch.setQtyFreeze(stockBatch.getQtyFreeze().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_HASTE.getValue().equals(insDocBatch.getStockStatus())){
                stockBatch.setQtyHaste(stockBatch.getQtyHaste().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_TEMP.getValue().equals(insDocBatch.getStockStatus())){
                stockBatch.setQtyTemp(stockBatch.getQtyTemp().add(qty));
            }
            uniqueKeyMap.put(uniqueKey, stockBatch);

        }

        return uniqueKeyMap;
    }


    /**
     * 按照仓位库存唯一键，合并凭证中对于统一个库存记录的数量修改
     *
     * @param insDocBinList 仓位库存移动凭证列表
     * @return 返回按照仓位凭证唯一键合并后的库存修改对象
     * <AUTHOR> <<EMAIL>>
     */
    private Map<StockBinKey, StockBin> mergeInsDocBin(List<StockInsDocBin> insDocBinList) {
        // 按照批次库存唯一键，合并凭证中对于统一个库存记录的数量修改
        Map<StockBinKey, StockBin> uniqueKeyMap = new HashMap<>();

        for (StockInsDocBin insDocBin : insDocBinList) {

            // 如果有凭证移动数量为零的数据，认为不需要修改库存，直接跳过
            if(UtilNumber.isEmpty(insDocBin.getActualQty())){
                continue;
            }

            // 从凭证中拿出唯一键所需的物料id、批次id、工厂id、库存地点id
            StockBinKey uniqueKey = new StockBinKey(insDocBin.getMatId(), insDocBin.getBatchId(), insDocBin.getFtyId(), insDocBin.getLocationId(), insDocBin.getWhId(), insDocBin.getTypeId(), insDocBin.getBinId(), insDocBin.getCellId());

            StockBin stockBin;
            if (uniqueKeyMap.containsKey(uniqueKey)) {
                stockBin = uniqueKeyMap.get(uniqueKey);
            } else {
                stockBin = new StockBin();
                stockBin.setMatId(insDocBin.getMatId());
                stockBin.setBatchId(insDocBin.getBatchId());
                stockBin.setFtyId(insDocBin.getFtyId());
                stockBin.setLocationId(insDocBin.getLocationId());
                stockBin.setWhId(insDocBin.getWhId());
                stockBin.setTypeId(insDocBin.getTypeId());
                stockBin.setBinId(insDocBin.getBinId());
                stockBin.setCellId(insDocBin.getCellId());
                stockBin.setCreateUserId(insDocBin.getCreateUserId());
                stockBin.setModifyUserId(insDocBin.getModifyUserId());

                // 库存数量信息初始化
                stockBin.setQty(BigDecimal.ZERO);
                stockBin.setQtyTransfer(BigDecimal.ZERO);
                stockBin.setQtyInspection(BigDecimal.ZERO);
                stockBin.setQtyFreeze(BigDecimal.ZERO);
                stockBin.setQtyHaste(BigDecimal.ZERO);
                stockBin.setQtyTemp(BigDecimal.ZERO);
            }

            // 根据借贷标识，计算出要加总的数量
            BigDecimal qty = Const.DEBIT_S_ADD.equals(insDocBin.getDebitCredit()) ? insDocBin.getActualQty() : BigDecimal.ZERO.subtract(insDocBin.getActualQty());
            if(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue().equals(insDocBin.getStockStatus())){
                stockBin.setQty(stockBin.getQty().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_ON_THE_WAY.getValue().equals(insDocBin.getStockStatus())){
                stockBin.setQtyTransfer(stockBin.getQtyTransfer().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_QUALITY_INSPECTION.getValue().equals(insDocBin.getStockStatus())){
                stockBin.setQtyInspection(stockBin.getQtyInspection().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue().equals(insDocBin.getStockStatus())){
                stockBin.setQtyFreeze(stockBin.getQtyFreeze().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_HASTE.getValue().equals(insDocBin.getStockStatus())){
                stockBin.setQtyHaste(stockBin.getQtyHaste().add(qty));
            } else if(EnumStockStatus.STOCK_BATCH_STATUS_TEMP.getValue().equals(insDocBin.getStockStatus())){
                stockBin.setQtyTemp(stockBin.getQtyTemp().add(qty));
            }
            uniqueKeyMap.put(uniqueKey, stockBin);

        }

        return uniqueKeyMap;
    }

    /**
     * 1.根据唯一键查询出数据库中已存在的批次库存记录id
     * 2.根据记录id查询出批次库存记录，并对这些记录加X锁
     * 3.将已有库存记录的数量与本次修改的数量合并
     * 4.将本次要新增插入的记录添加到返回结果集中（新增记录的id为null）
     * @param mergedStockBatch 按照批次库存唯一键合并的批次库存对象
     * @return 本次库存移动涉及到的所有的批次库存记录列表
     * <AUTHOR>
     */
    private List<StockBatch> getAllStockRecordByInsDocBatchList(Map<StockBatchKey, StockBatch> mergedStockBatch) {
        // 按照唯一键，查询数据库中已存在的批次库存记录id列表
        List<Long> existStockBatchIdList = stockBatchDataWrap.selectStockBatchIdByStockBatchKeys(mergedStockBatch.keySet());

        if(UtilCollection.isEmpty(existStockBatchIdList)){
            return new LinkedList<>(mergedStockBatch.values());
        }

        // 根据主键查询批次库存数量，并加X锁
        List<StockBatch> existStockBatchList = stockBatchDataWrap.selectStockBatchQtyByIdForUpdate(existStockBatchIdList);

        if(UtilCollection.isEmpty(existStockBatchList)){
            return new LinkedList<>(mergedStockBatch.values());
        }

        // 按照唯一键为key的map
        Map<StockBatchKey, StockBatch> existStockBatchMap = existStockBatchList.stream().collect(Collectors.toMap(obj -> new StockBatchKey(obj.getMatId(), obj.getBatchId(), obj.getFtyId(), obj.getLocationId()), obj -> obj));

        // 遍历本次要处理的批次库存记录，把数据库中已经存在的记录的数量合并进去
        for(Map.Entry<StockBatchKey, StockBatch> entry : mergedStockBatch.entrySet()){

            if(!existStockBatchMap.containsKey(entry.getKey())){
                continue;
            }

            StockBatch existStockBatch = existStockBatchMap.get(entry.getKey());

            entry.getValue().setId(existStockBatch.getId());

            // 将已有库存记录的数量与本次修改的数量合并
            entry.getValue().setQty(entry.getValue().getQty().add(existStockBatch.getQty()));
            entry.getValue().setQtyInspection(entry.getValue().getQtyInspection().add(existStockBatch.getQtyInspection()));
            entry.getValue().setQtyTransfer(entry.getValue().getQtyTransfer().add(existStockBatch.getQtyTransfer()));
            entry.getValue().setQtyFreeze(entry.getValue().getQtyFreeze().add(existStockBatch.getQtyFreeze()));
            entry.getValue().setQtyHaste(entry.getValue().getQtyHaste().add(existStockBatch.getQtyHaste()));
            entry.getValue().setQtyTemp(entry.getValue().getQtyTemp().add(existStockBatch.getQtyTemp()));
        }

        return new LinkedList<>(mergedStockBatch.values());
    }

    /**
     * 1.根据唯一键查询出数据库中已存在的仓位库存记录id
     * 2.根据记录id查询出仓位库存记录，并对这些记录加X锁
     * 3.将已有库存记录的数量与本次修改的数量合并
     * 4.将本次要新增插入的记录添加到返回结果集中（新增记录的id为null）
     * @param mergedStockBin 按照仓位库存唯一键合并的仓位库存对象
     * @return 本次库存移动涉及到的所有的仓位库存记录列表
     * <AUTHOR>
     */
    private List<StockBin> getAllStockRecordByInsDocBinList(Map<StockBinKey, StockBin> mergedStockBin) {
        // 按照唯一键，查询数据库中已存在的仓位库存记录id列表
        List<Long> existStockBinIdList = stockBinDataWrap.selectStockBinIdByStockBinKeys(mergedStockBin.keySet());

        if(UtilCollection.isEmpty(existStockBinIdList)){
            return new LinkedList<>(mergedStockBin.values());
        }

        // 根据主键查询批次库存数量，并加X锁
        List<StockBin> existStockBinRecords = stockBinDataWrap.selectStockBinQtyByIdForUpdate(existStockBinIdList);

        if(UtilCollection.isEmpty(existStockBinRecords)){
            return new LinkedList<>(mergedStockBin.values());
        }

        // 按照唯一键为key的map
        Map<StockBinKey, StockBin> existStockBinMap = existStockBinRecords.stream().collect(Collectors.toMap(obj -> new StockBinKey(obj.getMatId(), obj.getBatchId(), obj.getFtyId(), obj.getLocationId(), obj.getWhId(), obj.getTypeId(), obj.getBinId(), obj.getCellId()), obj -> obj));

        // 遍历本次要处理的批次库存记录，把数据库中已经存在的记录的数量合并进去
        for(Map.Entry<StockBinKey, StockBin> entry : mergedStockBin.entrySet()){

            if(!existStockBinMap.containsKey(entry.getKey())){
                continue;
            }

            StockBin existStockBin = existStockBinMap.get(entry.getKey());

            entry.getValue().setId(existStockBin.getId());

            // 将已有库存记录的数量与本次修改的数量合并
            entry.getValue().setQty(entry.getValue().getQty().add(existStockBin.getQty()));
            entry.getValue().setQtyInspection(entry.getValue().getQtyInspection().add(existStockBin.getQtyInspection()));
            entry.getValue().setQtyTransfer(entry.getValue().getQtyTransfer().add(existStockBin.getQtyTransfer()));
            entry.getValue().setQtyFreeze(entry.getValue().getQtyFreeze().add(existStockBin.getQtyFreeze()));
            entry.getValue().setQtyHaste(entry.getValue().getQtyHaste().add(existStockBin.getQtyHaste()));
            entry.getValue().setQtyTemp(entry.getValue().getQtyTemp().add(existStockBin.getQtyTemp()));
        }

        return new LinkedList<>(mergedStockBin.values());
    }

    /**
     * 根据采购订单行项目号查询批次库存
     * @param itemDTOList
     * @return
     */
    public List<StockBatchDTO> getStockByPurchase(List<ErpPurchaseReceiptItemDTO> itemDTOList) {
        return stockBatchDataWrap.getStockByPurchase(itemDTOList);
    }

}
