package com.inossem.wms.bizbasis.masterdata.org.service.biz;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptOfficeDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptOfficeDTO;
import com.inossem.wms.common.model.masterdata.base.entity.DicDeptOffice;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 科室主数据Service
 *
 * <AUTHOR>
 * @date 2022/05/07 13:43
 **/
@Service
@Slf4j
public class OfficeService {

    @Autowired
    private DicDeptOfficeDataWrap dicDeptOfficeDataWrap;

    @Autowired
    private DataFillService dataFillService;

    /**
     * 查询科室-分页
     *
     * @param ctx BizContext
     */
    public void getResult(BizContext ctx) {
        // 上下文入参
        MetaDataDeptOfficePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装查询条件
        WmsQueryWrapper<MetaDataDeptOfficePO> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .like(UtilString.isNotNullOrEmpty(po.getDeptOfficeName()), MetaDataDeptOfficePO::getDeptOfficeName, po.getDeptOfficeName());
        wrapper.lambda()
                .eq(UtilString.isNotNullOrEmpty(po.getDeptOfficeCode()), MetaDataDeptOfficePO::getDeptOfficeCode, po.getDeptOfficeCode());
        // 分页处理
        IPage<DicDeptOfficeDTO> page = po.getPageObj(DicDeptOfficeDTO.class);
        // 获取科室信息
        dicDeptOfficeDataWrap.getDeptOfficePageList(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 获取详情
     *
     * @param ctx 上下文对象
     * @return 公司详情
     */
    public SingleResultVO<DicDeptOfficeDTO> get(BizContext ctx) {
        Long officeId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("科室详情 deptId：{}", officeId);
        if (UtilNumber.isEmpty(officeId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicDeptOffice dicDeptOffice = dicDeptOfficeDataWrap.getById(officeId);
        log.info("科室id：{}，详情：{}", officeId, JSONObject.toJSONString(dicDeptOffice));

        DicDeptOfficeDTO dto = UtilBean.newInstance(dicDeptOffice, DicDeptOfficeDTO.class);
        dataFillService.fillAttr(dto);
        return new SingleResultVO<>(dto);
    }

    /**
     * 借用部门确认提交
     *
     * @param ctx BizContext
     */
    public void addOrUpdate(BizContext ctx) {
        // 获取入参上下文
        MetaDataDeptOfficePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotEmpty(po)) {
            if (UtilNumber.isEmpty(po.getDeptId())
                    || UtilString.isNullOrEmpty(po.getDeptOfficeCode())
                    || UtilString.isNullOrEmpty(po.getDeptOfficeName())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        }
        CurrentUser currentUser = ctx.getCurrentUser();
        log.info("新增/修改信息 po：{}", JSONObject.toJSONString(po));
        DicDeptOffice entity = UtilBean.newInstance(po, DicDeptOffice.class);
        // 根据是否存在ID判断是否为新增
        if (UtilNumber.isEmpty(po.getId())) {
            // 新增
            Date now = UtilDate.getNow();
            entity.setCreateTime(now);
            entity.setCreateUserId(currentUser.getId());
        } else {
            // 修改
            entity.setModifyUserId(currentUser.getId());
        }
        if (dicDeptOfficeDataWrap.saveOrUpdate(entity)) {
            log.info("科室：{}，保存成功", entity.getDeptOfficeCode());
        }
    }

    public DicDeptOfficeDTO getDeptOfficeByCode(Long deptId,String deptOfficeCode){
        WmsQueryWrapper<DicDeptOffice> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .eq(DicDeptOffice::getDeptOfficeCode, deptOfficeCode)
                .eq(DicDeptOffice::getDeptId,deptId);
        List<DicDeptOffice> list = dicDeptOfficeDataWrap.list(wrapper);
        if (UtilCollection.isEmpty(list)){
            return null;
        }
        DicDeptOffice dicDeptOffice = list.get(0);
        return UtilBean.newInstance(dicDeptOffice,DicDeptOfficeDTO.class);
    }


}
