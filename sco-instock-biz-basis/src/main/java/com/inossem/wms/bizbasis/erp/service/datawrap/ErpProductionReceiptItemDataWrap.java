package com.inossem.wms.bizbasis.erp.service.datawrap;

import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.erp.dao.ErpProductionReceiptItemMapper;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.erp.dto.ErpProductionReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpProductionReceiptItem;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 生产订单行项目明细表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-10
 */
@Service
public class ErpProductionReceiptItemDataWrap extends BaseDataWrap<ErpProductionReceiptItemMapper, ErpProductionReceiptItem> {

    @Autowired
    private DataFillService dataFillService;

    /**
     * 获取生产订单行项目
     *
     * @param po 查询条件
     * @return List<ErpProductionReceiptItemDTO>
     */
    public List<ErpProductionReceiptItemDTO> getProductionReceiptItemList(BizReceiptPreSearchPO po) {
        List<ErpProductionReceiptItemDTO> productionReceiptItemList = this.getBaseMapper().getProductionReceiptItemList(po);
        dataFillService.fillRlatAttrDataList(productionReceiptItemList);
        return productionReceiptItemList;
    }
}
