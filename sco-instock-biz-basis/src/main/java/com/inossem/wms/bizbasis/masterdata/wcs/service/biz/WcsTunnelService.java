package com.inossem.wms.bizbasis.masterdata.wcs.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.wcs.dto.DicWcsTunnelDTO;
import com.inossem.wms.common.model.wcs.entity.DicWcsTunnel;
import com.inossem.wms.common.model.wcs.po.DicWcsTunnelSavePO;
import com.inossem.wms.common.model.wcs.po.DicWcsTunnelSearchPO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.bizbasis.masterdata.wcs.service.datawrap.DicWcsTunnelDataWrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WcsTunnelService {

    @Autowired
    protected DicWcsTunnelDataWrap dicWcsTunnelDataWrap;

    /**
     * 数据填充实现类
     */
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 查询巷道列表
     *
     * @param ctx-po 查询条件
     * @return 巷道列表
     *
     */
    public PageObjectVO<DicWcsTunnelDTO> getList(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        DicWcsTunnelSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 设置查询条件 ******** */
        QueryWrapper<DicWcsTunnel> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getTunnelCode()), DicWcsTunnel::getTunnelCode, po.getTunnelCode())
            .like(UtilString.isNotNullOrEmpty(po.getTunnelName()), DicWcsTunnel::getTunnelName, po.getTunnelName());
        wrapper.orderBy(UtilString.isNotNullOrEmpty(po.getDescSortColumn()), false, UtilString.getSnakeList(po.getDescSortColumn().split(",")));
        wrapper.orderBy(UtilString.isNotNullOrEmpty(po.getAscSortColumn()), true, UtilString.getSnakeList(po.getAscSortColumn().split(",")));
        /* ********* 查询巷道列表 ******** */
        Page<DicWcsTunnel> page = new Page<>(po.getPageIndex(), po.getPageSize());
        dicWcsTunnelDataWrap.page(page, wrapper);
        List<DicWcsTunnel> dicWcsTunnelList = page.getRecords();
        Long totalCount = page.getTotal();
        /* ********* 数据泛型转换 ******** */
        List<DicWcsTunnelDTO> dicWcsTunnelDTOList = UtilCollection.toList(dicWcsTunnelList, DicWcsTunnelDTO.class);
        /*数据填充*/
        dataFillService.fillRlatAttrDataList(dicWcsTunnelDTOList);
        return new PageObjectVO<>(dicWcsTunnelDTOList, totalCount);
    }

    /**
     * 查询巷道详情
     * 
     * @param ctx-id 主键id
     * @return 巷道详情
     *
     */
    public SingleResultVO<DicWcsTunnelDTO> getInfo(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询巷道详情 ******** */
        DicWcsTunnel dicWcsTunnel = dicWcsTunnelDataWrap.getById(id);
        /* ********* 数据泛型转换 ******** */
        DicWcsTunnelDTO dicWcsTunnelDTO = UtilBean.newInstance(dicWcsTunnel, DicWcsTunnelDTO.class);
        /*数据填充*/
        dataFillService.fillAttr(dicWcsTunnelDTO);
        return new SingleResultVO<>(dicWcsTunnelDTO);
    }

    /**
     * 新增巷道信息
     *
     * @param ctx-po 巷道信息
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        DicWcsTunnelSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 入参非空效验 ******** */
        DicWcsTunnelDTO dicWcsTunnelDTO = po.getCusDicTunnelInfo();
        if (dicWcsTunnelDTO == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 数据泛型转换 ******** */
        DicWcsTunnel dicWcsTunnel = UtilBean.newInstance(dicWcsTunnelDTO, DicWcsTunnel.class);
        /* ********* 新增巷道信息 ******** */
        dicWcsTunnelDataWrap.save(dicWcsTunnel);
    }

    /**
     * 修改巷道信息
     *
     * @param ctx-po 巷道信息
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        DicWcsTunnelSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 入参非空效验 ******** */
        DicWcsTunnelDTO dicWcsTunnelDTO = po.getCusDicTunnelInfo();
        if (dicWcsTunnelDTO == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 数据泛型转换 ******** */
        DicWcsTunnel dicWcsTunnel = UtilBean.newInstance(dicWcsTunnelDTO, DicWcsTunnel.class);
        /* ********* 修改设备信息 ******** */
        dicWcsTunnelDataWrap.updateById(dicWcsTunnel);
    }

    /**
     * 删除巷道信息
     *
     * @param ctx-id 主键id
     *
     */
    public String remove(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 设置巷道code ******** */
        String tunnelCode = dicWcsTunnelDataWrap.getById(id).getTunnelCode();
        /* ********* 删除巷道信息 ******** */
        if(dicWcsTunnelDataWrap.removeById(id)){
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_WCS_TUNNEL_DELETE_FAILURE, tunnelCode);
        }
        return tunnelCode;
    }
}
