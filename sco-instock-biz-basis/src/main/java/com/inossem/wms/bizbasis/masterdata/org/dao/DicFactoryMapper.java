package com.inossem.wms.bizbasis.masterdata.org.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.org.factory.entity.DicFactory;
import com.inossem.wms.common.model.org.factory.po.DicFactorySearchPO;
import com.inossem.wms.common.model.org.factory.vo.DicFactoryPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工厂主数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Mapper
public interface DicFactoryMapper extends WmsBaseMapper<DicFactory> {
    /**
     * 工厂列表分页结果集查询
     * 
     * @param page
     * @param wrapper
     * <AUTHOR>
     * @return
     */
    List<DicFactoryPageVO> selectDicFactoryPageVOList(IPage<DicFactoryPageVO> page,
        @Param(Constants.WRAPPER) QueryWrapper<DicFactorySearchPO> wrapper);
}
