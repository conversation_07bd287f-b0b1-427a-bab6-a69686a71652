package com.inossem.wms.bizbasis.masterdata.user.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.entity.SysUserImg;
import com.inossem.wms.common.model.auth.user.po.SysUserSearchPO;
import com.inossem.wms.common.model.auth.user.vo.SysUserPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */

@Mapper
public interface SysUserMapper extends WmsBaseMapper<SysUser> {

    /**
     * 未绑定该角色的用户
     * 
     * @date 2021/3/2 16:15
     * <AUTHOR>
     * @param condition 查询条件
     * @param roleId 角色ID
     * @return SysUser
     */
    List<SysUser> getSysUserListDidNotBindThisRole(@Param("condition") String condition, @Param("roleId") Long roleId);

    /**
     * 根據用户id获取 用户菜单权限id列表
     * 
     * @date 2021/3/3 10:28
     * <AUTHOR>
     * @param userId 用户ID
     * @return Long
     */
    List<Long> getUserResourceIdList(Long userId);

    /**
     * 用户列表分页结果集查询
     * 
     * @param page
     * @param wrapper
     * @return
     */
    List<SysUserPageVO> selectSysUserPageVOList(IPage<SysUserPageVO> page, @Param(Constants.WRAPPER) QueryWrapper<SysUserSearchPO> wrapper);

    /**
     * 查询签名
     *
     * @param imgId
     * @return
     */
    SysUserImg findImg(@Param("imgId") Long imgId);

    List<String> getUserResourceCodeList(@Param("userId") Long userId, @Param("roleCodes") Set<String> roleCodes);

    /**
     * 根据角色编码查询对应用户列表
     */
    List<SysUser> getUserByRoleCode(@Param("roleCode") String roleCode);

}
