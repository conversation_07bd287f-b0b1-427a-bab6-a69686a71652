package com.inossem.wms.bizbasis.common.service.biz;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFacotryWbsDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageBinDataWrap;
import com.inossem.wms.common.cache.ICacheService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.bizdomain.threedimensional.entity.BizStorageHumiture;
import com.inossem.wms.common.model.i18n.entity.SysIl8nText;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.cgn.mat.dto.DicMaterialCgnDTO;
import com.inossem.wms.common.model.masterdata.margin.entity.DicMarginCategory;
import com.inossem.wms.common.model.masterdata.mat.base.dto.DicMaterialGroupDTO;
import com.inossem.wms.common.model.masterdata.mat.base.dto.DicMaterialTypeDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFacotryWbs;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.purchasepackage.dto.DicPurchasePackageDTO;
import com.inossem.wms.common.model.masterdata.purchasepackage.entity.DicPurchasePackage;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.masterdata.unit.dto.DicUnitRelDTO;
import com.inossem.wms.common.model.org.corp.entity.DicCorp;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.section.dto.DicWhStorageSectionDTO;
import com.inossem.wms.common.model.org.storagetype.dto.DicWhStorageTypeDTO;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.tool.entity.DicToolType;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据缓存查询类 提供各主数据的缓存查询和更新方法，默认使用redis的缓存实现 1. 公司 2. 工厂 3. 库存地点 4. 仓库 5. 存储类型 6. 仓位 7. 计量单位 8. 计量单位换算关系 9. 物料 10. 物料工厂
 * 11. 物料类型 12. 物料组 13. 国际化语言 14. 移动类型
 *
 * <AUTHOR> 重构时间: 2020年2月19日
 * <AUTHOR> 重构时间: 2021年2月28日
 */
@Component
@Slf4j
public class DictionaryService {

    @Autowired
    protected ICacheService cacheServiceImpl;

    @Autowired
    private DicWhStorageBinDataWrap whStorageBinDataWrap;

    @Autowired
    @Lazy
    protected DataFillService dataFillService;

    @Autowired
    protected DicMaterialFacotryWbsDataWrap dicMaterialFacotryWbsDataWrap;

    /**
     * 国际化缓存
     */ 
    protected Map<String, String> languageMap = new HashMap<>();


    public void initLanguageMap(){
        languageMap.clear();
    }   
    public void setLanguageMap(String key, String value){
        languageMap.put(key, value);
    }   
    public String getLanguageMap(String key){
        return languageMap.get(key);
    }   

    /* ******************** 公司主数据缓存 ************************ */
    /**
     * 从缓存中获取全部公司数据
     *
     * @return 公司集合
     */
    @SuppressWarnings("unchecked")
    public Collection<DicCorp> getAllCorpCache() {
        Map<Long, DicCorp> map = (Map<Long, DicCorp>)cacheServiceImpl.get(Const.CACHE_CORP);
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 根据公司ID从缓存中获取公司数据
     *
     * @param corpId 公司ID
     * @return 公司对象
     */
    public DicCorp getCorpCacheById(Long corpId) {
        if (UtilNumber.isEmpty(corpId)) {
            return null;
        }
        return (DicCorp)(cacheServiceImpl.get(Const.CACHE_CORP, corpId.toString()));
    }

    /**
     * 根据公司code从缓存中获取公司ID
     *
     * @param corpCode 公司code
     * @return 公司ID
     */
    public Long getCorpIdCacheByCode(String corpCode) {
        if (UtilString.isNullOrEmpty(corpCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_CORP_ID, corpCode.toUpperCase()));
    }

    /* ******************** 工厂主数据缓存 ************************ */

    /**
     * 从缓存中获取全部工厂数据
     *
     * @return 工厂集合
     */
    @SuppressWarnings("unchecked")
    public Collection<DicFactoryDTO> getAllFtyCache() {
        Map<Long, DicFactoryDTO> map = (Map<Long, DicFactoryDTO>)(cacheServiceImpl.get(Const.CACHE_FACTORY));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }
    

    /**
     * 根据ID从缓存中获取工厂数据
     *
     * @param factoryId 工厂ID
     * @return 工厂对象
     */
    public DicFactoryDTO getFtyCacheById(Long factoryId) {
        if (UtilNumber.isEmpty(factoryId)) {
            return null;
        }
        return (DicFactoryDTO)(cacheServiceImpl.get(Const.CACHE_FACTORY, factoryId.toString()));
    }

    /**
     * 根据工厂code从缓存中获取工厂ID
     *
     * @param factoryCode 工厂code
     * @return 工厂ID
     */
    public Long getFtyIdCacheByCode(String factoryCode) {
        if (UtilString.isNullOrEmpty(factoryCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_FACTORY_ID, factoryCode.toUpperCase()));
    }

    /**
     * 从缓存中获取全部工厂数据
     *
     * @return 工厂集合
     */
    @SuppressWarnings("unchecked")
    public Collection<DicFactoryDTO> getFtyCacheByIds(List<Long> ftyiIds) {
        if (UtilCollection.isEmpty(ftyiIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:ftyiIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_FACTORY, ids,DicFactoryDTO.class));
    }

    /* ******************** 库存地点主数据缓存 ************************ */

    /**
     * 从缓存中获取全部库存地点数据
     *
     * @return 库存地点集合
     */

    @SuppressWarnings("unchecked")
    public Collection<DicStockLocationDTO> getAllLocationCache() {
        Map<Long, DicStockLocationDTO> map = (Map<Long, DicStockLocationDTO>)(cacheServiceImpl.get(Const.CACHE_LOCATION));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 根据库存地点ID获取库存地点数据
     *
     * @param locationId 库存地点ID
     * @return 库存地点对象
     */
    public DicStockLocationDTO getLocationCacheById(Long locationId) {
        if (UtilNumber.isEmpty(locationId)) {
            return null;
        }
        return (DicStockLocationDTO)(cacheServiceImpl.get(Const.CACHE_LOCATION, locationId.toString()));
    }

    /**
     * 根据工厂库存地点code获取库存地点ID
     *
     * @param ftyCode 工厂code
     * @param locationCode 库存地点code
     * @return 库存地点ID
     */
    public Long getLocationIdCacheByCode(String ftyCode, String locationCode) {
        if (UtilString.isNullOrEmpty(ftyCode) || UtilString.isNullOrEmpty(locationCode)) {
            return null;
        }
        String key = ftyCode.toUpperCase() + "-" + locationCode.toUpperCase();
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_LOCATION_ID, key));
    }

    /**
     * 根据库存地点ID获取库存地点数据
     *
     * @param locationIds 库存地点ID
     * @return 库存地点对象
     */
    public Collection<DicStockLocationDTO> getLocationCacheByIds(List<Long> locationIds) {
        if (UtilCollection.isEmpty(locationIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:locationIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_LOCATION, ids,DicStockLocationDTO.class));
    }

    /* ******************** 仓库号主数据缓存 ************************ */

    /**
     * 获取全部仓库缓存数据
     *
     * @return 仓库集合
     */
    @SuppressWarnings("unchecked")
    public Collection<DicWh> getAllWhCache() {
        Map<Long, DicWh> map = (Map<Long, DicWh>)(cacheServiceImpl.get(Const.CACHE_WH));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 根据仓库ID获取缓存数据
     *
     * @param whId 仓库ID
     * @return 仓库对象
     */
    public DicWh getWhCacheById(Long whId) {
        if (UtilNumber.isEmpty(whId)) {
            return null;
        }
        return (DicWh)(cacheServiceImpl.get(Const.CACHE_WH, whId.toString()));
    }

    /**
     * 根据仓库code获取仓库ID
     *
     * @param whCode 仓库code
     * @return 仓库ID
     */
    public Long getWhIdCacheByCode(String whCode) {
        if (UtilString.isNullOrEmpty(whCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_WH_ID, whCode.toUpperCase()));
    }

    /**
     * 根据仓库ID获取缓存数据
     *
     * @param whIds 仓库ID
     * @return 仓库对象
     */
    public Collection<DicWh> getWhCacheByIds(List<Long> whIds) {
        if (UtilCollection.isEmpty(whIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:whIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_WH, ids,DicWh.class));
    }

    /* ******************** 存储类型主数据缓存 ************************ */

    /**
     * 获取全部存储类型缓存数据
     *
     * @return 存储类型集合
     */
    @SuppressWarnings("unchecked")
    public Collection<DicWhStorageTypeDTO> getAllStorageTypeCache() {
        Map<Long, DicWhStorageTypeDTO> map = (Map<Long, DicWhStorageTypeDTO>)(cacheServiceImpl.get(Const.CACHE_STORAGE_TYPE));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 根据存储类型ID获取存储类型数据
     *
     * @param typeId 存储类型ID
     * @return 存储类型对象
     */
    public DicWhStorageTypeDTO getStorageTypeCacheById(Long typeId) {
        if (UtilNumber.isEmpty(typeId)) {
            return null;
        }
        return (DicWhStorageTypeDTO)(cacheServiceImpl.get(Const.CACHE_STORAGE_TYPE, typeId.toString()));
    }

    /**
     * 根据仓库存储类型code获取存储类型ID
     *
     * @param whCode 仓库code
     * @param typeCode 存储类型code
     * @return 存储类型ID
     */
    public Long getStorageTypeIdCacheByCode(String whCode, String typeCode) {
        if (UtilString.isNullOrEmpty(whCode) || UtilString.isNullOrEmpty(typeCode)) {
            return null;
        }
        String key = whCode.toUpperCase() + "-" + typeCode.toUpperCase();
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_STORAGE_TYPE_ID, key));
    }

    /**
     * 根据存储类型ID获取存储类型数据
     *
     * @param typeIds 存储类型ID
     * @return 存储类型对象
     */
    public Collection<DicWhStorageTypeDTO> getStorageTypeCacheByIds(List<Long> typeIds) {
        if (UtilCollection.isEmpty(typeIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:typeIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_STORAGE_TYPE, ids,DicWhStorageTypeDTO.class));
    }

    /*================================ 存储区主数据缓存 ================================*/

    /**
     * 获取全部存储区缓存数据
     *
     * @return 存储区集合
     */
    @SuppressWarnings("unchecked")
    public Collection<DicWhStorageSectionDTO> getAllStorageSectionCache() {
        Map<Long, DicWhStorageSectionDTO> map = (Map<Long, DicWhStorageSectionDTO>)(cacheServiceImpl.get(Const.CACHE_STORAGE_SECTION));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }


    /**
     * 根据存储区ID获取全部存储区缓存数据
     *
     * @param sectionIds 存储区ID
     * @return 存储类型对象
     */
    public Collection<DicWhStorageTypeDTO> getStorageSectionCacheByIds(List<Long> sectionIds) {
        if (UtilCollection.isEmpty(sectionIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:sectionIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_STORAGE_SECTION, ids,DicWhStorageTypeDTO.class));
    }


    /**
     * 根据仓库存储类型code获取存储类型ID
     *
     * @return 存储类型ID
     */
    public Long getStorageSectionIdCacheByCode(Long typeId, String sectionCode) {
        if (UtilNumber.isEmpty(typeId) || UtilString.isNullOrEmpty(sectionCode)) {
            return null;
        }

        String key = typeId.toString() + "-" + sectionCode;
        return  UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_STORAGE_SECTION_ID, key));
    }

    /* ******************** 仓位主数据缓存 ************************ */

    /**
     * 获取全部仓位缓存数据
     *
     * @return 仓位集合
     */
    @SuppressWarnings("unchecked")
    public Collection<DicWhStorageBinDTO> getAllBinCache() {
        Map<String, DicWhStorageBinDTO> map = (Map<String, DicWhStorageBinDTO>)(cacheServiceImpl.get(Const.CACHE_BIN));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 初始化仓位缓存数据
     */
    public void initBinCache() {
        cacheServiceImpl.delete(Const.CACHE_BIN);
        cacheServiceImpl.delete(Const.CACHE_BIN_ID);
        /*============== 仓位缓存增加条件 whCode+binCode ============== */
        cacheServiceImpl.delete(Const.CACHE_BIN_BY_BIN_CODE);
        /*============== 仓位缓存增加条件 whCode+binCode ============== */
        List<DicWhStorageBin> binList = whStorageBinDataWrap.list();
        if (UtilCollection.isNotEmpty(binList)) {
            List<DicWhStorageBinDTO> dtoList = UtilCollection.toList(binList, DicWhStorageBinDTO.class);
            dataFillService.fillRlatAttrDataList(dtoList);
            Map<Long, List<DicWhStorageBinDTO>> binMap = new HashMap<>();
            for (DicWhStorageBinDTO bin : dtoList) {
                if(binMap.containsKey(bin.getTypeId())){
                    binMap.get(bin.getTypeId()).add(bin);
                }else{
                    List<DicWhStorageBinDTO> binDTOList = new ArrayList<>();
                    binDTOList.add(bin);
                    binMap.put(bin.getTypeId(), binDTOList);
                }
                cacheServiceImpl.put(Const.CACHE_BIN, bin.getId().toString(), bin);
                String key = bin.getWhCode().toUpperCase() + "-" + bin.getTypeCode().toUpperCase() + "-" + bin.getBinCode().toUpperCase();
                cacheServiceImpl.put(Const.CACHE_BIN_ID, key, bin.getId());
                /*============== 仓位缓存增加条件 whCode+binCode ============== */
                cacheServiceImpl.put(Const.CACHE_BIN_BY_BIN_CODE, StringUtils.join(bin.getWhCode(), "-", bin.getBinCode()), bin);
                /*============== 仓位缓存增加条件 whCode+binCode ============== */
            }
            for(Long key : binMap.keySet()){
                cacheServiceImpl.put(Const.CACHE_BIN_TYPE_ID, key.toString(), binMap.get(key));
            }
        }
    }

    /**
     * 根据仓位ID获取仓位数据
     *
     * @param binId 仓位ID
     * @return 仓位对象
     */
    public DicWhStorageBinDTO getBinCacheById(Long binId) {
        if (UtilNumber.isEmpty(binId)) {
            return null;
        }
        return (DicWhStorageBinDTO)cacheServiceImpl.get(Const.CACHE_BIN, binId.toString());
    }

    /**
     * 根据仓位binCode获取仓位数据
     *
     * @param binCode 仓位binCode
     * @return 仓位对象
     */
    public DicWhStorageBinDTO getBinCacheByCode(String binCode) {
        if (UtilString.isNullOrEmpty(binCode)) {
            return null;
        }

        LinkedHashMap binMap = (LinkedHashMap) cacheServiceImpl.get(Const.CACHE_BIN);
        Iterator iter = binMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry entry = (Map.Entry) iter.next();
            DicWhStorageBinDTO binDto = (DicWhStorageBinDTO) entry.getValue();
            if(binCode.equals(binDto.getBinCode())){
                return binDto;
            }
        }

        return  null;
    }


    /**
     * 根据仓位code获取仓位数据
     *
     * @param whCode 仓库code
     * @param binCode 仓位code
     * @return 仓位对象
     */
    public DicWhStorageBinDTO getBinCacheByBinCode(String whCode, String binCode) {
        if (UtilString.isNullOrEmpty(binCode) || UtilString.isNullOrEmpty(whCode)) {
            return null;
        }
        return (DicWhStorageBinDTO)cacheServiceImpl.get(Const.CACHE_BIN_BY_BIN_CODE, StringUtils.join(whCode, "-", binCode));
    }


    /**
     * 根据联合主键code获取仓位ID
     *
     * @param whCode 仓库code
     * @param typeCode 存储类型code
     * @param binCode 仓位code
     * @return 仓位ID
     */
    public Long getBinIdCacheByCode(String whCode, String typeCode, String binCode) {
        if (UtilString.isNullOrEmpty(whCode) || UtilString.isNullOrEmpty(typeCode) || UtilString.isNullOrEmpty(binCode)) {
            return null;
        }
        String key = whCode.toUpperCase() + "-" + typeCode.toUpperCase() + "-" + binCode.toUpperCase();
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_BIN_ID, key));
    }

    /**
     * 根据存储类型ID获取仓位数据
     *
     * @param typeId 存储类型ID
     * @return 仓位对象
     */
    public Collection<DicWhStorageBinDTO> getBinCacheByTypeId(Long typeId) {
        if (UtilNumber.isEmpty(typeId)) {
            return null;
        }
        return (Collection<DicWhStorageBinDTO>)cacheServiceImpl.get(Const.CACHE_BIN_TYPE_ID, typeId.toString());
    }

    /**
     * 根据存储类型ID获取仓位数据
     *
     * @param typeIdS 存储类型ID
     * @return 仓位对象
     */
    public List<DicWhStorageBinDTO> getBinCacheByTypeIds(List<Long> typeIdS) {
        List<DicWhStorageBinDTO> binDTOList = new ArrayList<>();
        if (UtilCollection.isEmpty(typeIdS)) {
            return null;
        }
        for(Long id:typeIdS){
            binDTOList.addAll(this.getBinCacheByTypeId(id));
        }
        return binDTOList;
    }

    /**
         * 根据存储类型ID获取仓位数据---未冻结仓位集合
         *
         * @param typeId 存储类型ID
         * @return 仓位对象
         */
    public List<DicWhStorageBinDTO> getBinCacheByTypeIdUnfrozen(Long typeId) {
        if (UtilNumber.isEmpty(typeId)) {
            return null;
        }
        Collection<DicWhStorageBinDTO> collection= (Collection<DicWhStorageBinDTO>) cacheServiceImpl.get(Const.CACHE_BIN_TYPE_ID, typeId.toString());
        if(!CollectionUtils.isEmpty(collection)){
            List<DicWhStorageBinDTO> dicWhStorageBinDTOList = new ArrayList<>(collection);
            dicWhStorageBinDTOList=dicWhStorageBinDTOList.stream().filter(bin -> bin.getFreezeInput()==0&&bin.getFreezeOutput()==0).collect(Collectors.toList());
            return dicWhStorageBinDTOList;
        }
        return null;
    }

    /**
     * 根据仓位ID获取仓位数据
     *
     * @param binIds 仓位ID
     * @return 仓位对象
     */
    public Collection<DicWhStorageBinDTO> getBinCacheByIds(List<Long> binIds) {
        if (UtilCollection.isEmpty(binIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:binIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return cacheServiceImpl.multiGet(Const.CACHE_BIN, ids,DicWhStorageBinDTO.class);
    }

    /* ******************** 计量单位主数据缓存 ************************ */

    /**
     * 获取全部单位缓存数据
     *
     * @return 单位集合
     */
    @SuppressWarnings("unchecked")
    public Map<Long, DicUnit> getAllUnitCache() {
        Map<Long, DicUnit> map = (Map<Long, DicUnit>)(cacheServiceImpl.get(Const.CACHE_UNIT));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map;
    }

    /**
     * 根据单位ID获取单位数据
     *
     * @param unitId 单位ID
     * @return 单位对象
     */
    public DicUnit getUnitCacheById(Long unitId) {
        if (UtilNumber.isEmpty(unitId)) {
            return null;
        }
        return (DicUnit)(cacheServiceImpl.get(Const.CACHE_UNIT, unitId.toString()));
    }

    /**
     * 根据单位code获取单位ID
     *
     * @param unitCode 单位code
     * @return 单位ID
     */
    public Long getUnitIdCacheByCode(String unitCode) {
        if (UtilString.isNullOrEmpty(unitCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_UNIT_ID, unitCode.toUpperCase()));
    }

    /**
     * 根据单位name获取单位ID
     *
     * @param unitName 单位name
     * @return 单位ID
     */
    public Long getUnitIdCacheByName(String unitName) {
        if (UtilString.isNullOrEmpty(unitName)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_UNIT_NAME, unitName));
    }

    /**
     * 根据单位ID获取单位数据
     *
     * @param unitIds 单位ID
     * @return 单位对象
     */
    public Collection<DicUnit> getUnitCacheByIds(List<Long> unitIds) {
        if (UtilCollection.isEmpty(unitIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:unitIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_UNIT, ids,DicUnit.class));
    }

    /* ******************** 计量单位换算关系缓存 ************************ */

    /**
     * 获取全部单位换算缓存数据
     *
     * @return 单位换算集合
     */
    @SuppressWarnings("unchecked")
    public Map<String, DicUnitRelDTO> getAllRelUnitCache() {
        Map<String, DicUnitRelDTO> map = (Map<String, DicUnitRelDTO>)(cacheServiceImpl.get(Const.CACHE_UNIT_REL));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map;
    }

    /**
     * 根据ID获取单位换算数据
     *
     * @param ftyId 工厂id
     * @param matId 物料id
     * @param sourceUnitId 源单位id
     * @param targetUnitId 目标单位id
     * @return 单位换算对象
     */
    public DicUnitRelDTO getRelUnitCacheById(Long ftyId, Long matId, Long sourceUnitId, Long targetUnitId) {
        if (UtilNumber.isNotEmpty(ftyId) && UtilNumber.isNotEmpty(matId) && UtilNumber.isNotEmpty(sourceUnitId)
            && UtilNumber.isNotEmpty(targetUnitId)) {
            String key = ftyId + "-" + matId + "-" + sourceUnitId + "-" + targetUnitId;

            return (DicUnitRelDTO)(cacheServiceImpl.get(Const.CACHE_UNIT_REL, key));
        }
        return null;
    }

    /* ******************** 物料主数据缓存 ************************ */

    /**
     * 根据物料code集合获取物料ID集合
     *
     * @param matCodeList 物料code集合
     * @return 物料id集合
     */
    public Collection<Long> getMatIdListByMatCodeList(List<String> matCodeList) {
        if (UtilCollection.isNotEmpty(matCodeList)) {
            return cacheServiceImpl.multiGet(Const.CACHE_MATERIAL_ID, matCodeList, Long.class);
        }
        return null;
    }

    /**
     * 根据物料ID集合获取物料主数据集合
     *
     * @param matIdList 物料ID集合
     * @return 物料主数据集合
     */
    public Collection<DicMaterialDTO> getMatListCacheByMatIdList(List<Long> matIdList) {
        if (UtilCollection.isNotEmpty(matIdList)) {
            List<String> matIdStrList = new ArrayList<>();
            for (Long matId : matIdList) {
                matIdStrList.add(matId.toString());
            }
            return cacheServiceImpl.multiGet(Const.CACHE_MATERIAL, matIdStrList, DicMaterialDTO.class);
        }
        return null;
    }

    /**
     * 根据物料ID集合获取物料主数据集合
     *
     * @param matIdList 物料ID集合
     * @return 物料主数据集合
     */
    public Map<Long, DicMaterialDTO> getMatMapCacheByMatIdList(List<Long> matIdList) {
        if (UtilCollection.isNotEmpty(matIdList)) {
            List<String> matIdStrList = new ArrayList<>();
            for (Long matId : matIdList) {
                matIdStrList.add(matId.toString());
            }
            List<DicMaterialDTO> cachedList = cacheServiceImpl.multiGet(Const.CACHE_MATERIAL, matIdStrList, DicMaterialDTO.class);
            Map<Long, DicMaterialDTO> map = new HashMap<>();
            for (DicMaterialDTO mat : cachedList) {
                map.put(mat.getId(), mat);
            }
            return map;
        }
        return null;
    }

    /**
     * 根据物料code获取物料id
     *
     * @param matCode 物料code
     * @return 物料id
     */
    public Long getMatIdByMatCode(String matCode) {
        if (UtilString.isNullOrEmpty(matCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_MATERIAL_ID, matCode));

    }

    /**
     * 根据物料id获取物料数据
     *
     * @param matId 物料id
     * @return 物料对象
     */
    public DicMaterialDTO getMatCacheById(Long matId) {
        if (UtilNumber.isEmpty(matId)) {
            return null;
        }
        return (DicMaterialDTO)cacheServiceImpl.get(Const.CACHE_MATERIAL, matId.toString());
    }

    /* ******************** 物料工厂主数据缓存 ************************ */

    /**
     * 获取全部工厂物料缓存数据
     *
     * @return DicMaterialFactoryDto
     */
    @SuppressWarnings("unchecked")
    public Collection<DicMaterialFactoryDTO> getAllMaterialFactoryCache() {
        Map<String, DicMaterialFactoryDTO> map = (Map<String, DicMaterialFactoryDTO>)(cacheServiceImpl.get(Const.CACHE_MATERIAL_FACTORY));
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 根据id获取工厂物料缓存数据
     *
     * @param matId 物料id
     * @param ftyId 工厂id
     * @return DicMaterialFactoryDto
     */
    public DicMaterialFactoryDTO getDicMaterialFactoryByUniqueKey(Long matId, Long ftyId) {
        if (UtilNumber.isNotEmpty(matId) && UtilNumber.isNotEmpty(ftyId)) {
            String key = matId + "-" + ftyId;
            return (DicMaterialFactoryDTO)cacheServiceImpl.get(Const.CACHE_MATERIAL_FACTORY, key);
        }
        return null;
    }

    public void refreshDicMaterialFactoryByUniqueKey(List<DicMaterialFactoryDTO> list){
        if(UtilCollection.isNotEmpty(list)){
            for(DicMaterialFactoryDTO dto:list){
                String key = dto.getMatId() + "-" + dto.getFtyId();
                cacheServiceImpl.put(Const.CACHE_MATERIAL_FACTORY, key, dto);
            }
        }

    }

    /* ******************** 物料类型主数据缓存 ************************ */

    /**
     * 根据物料类型编码获取物料类型
     *
     * @param matTypeCode 物料类型编码
     * @return DicMaterialTypeDTO
     */
    public DicMaterialTypeDTO getMatTypeByMatTypeCode(String matTypeCode) {
        if (UtilString.isNullOrEmpty(matTypeCode)) {
            return null;
        }
        return (DicMaterialTypeDTO)cacheServiceImpl.get(Const.CACHE_MATERIAL_TYPE, matTypeCode);
    }

    /**
     * 根据物料类型编码获取物料类型id
     *
     * @param matTypeCode 物料类型编码
     * @return 物料类型id
     */
    public Long getMatTypeIdByMatTypeCode(String matTypeCode) {
        if (UtilString.isNullOrEmpty(matTypeCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_MATERIAL_TYPE_ID, matTypeCode));
    }

    /**
     * 根据物料类型描述获取物料类型id
     *
     * @param matTypeName 物料类型描述
     * @return 物料类型id
     */
    public Long getMatTypeIdByMatTypeName(String matTypeName) {
        if (UtilString.isNullOrEmpty(matTypeName)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_MATERIAL_TYPE_NAME, matTypeName));
    }

    /* ******************** 物料组主数据缓存 ************************ */
    /**
     * 根据物料组编码获取物料组
     *
     * @param matGroupCode 物料组编码
     * @return DicMaterialGroupDTO
     */
    public DicMaterialGroupDTO getMatGroupByMatGroupCode(String matGroupCode) {
        if (UtilString.isNullOrEmpty(matGroupCode)) {
            return null;
        }
        return (DicMaterialGroupDTO)cacheServiceImpl.get(Const.CACHE_MATERIAL_GROUP, matGroupCode);
    }

    /**
     * 根据物料组编码获取物料组id
     *
     * @param matGroupCode 物料组编码
     * @return 物料组id
     */
    public Long getMatGroupIdByMatGroupCode(String matGroupCode) {
        if (UtilString.isNullOrEmpty(matGroupCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_MATERIAL_GROUP_ID, matGroupCode));
    }

    /**
     * 根据物料组描述获取物料组
     *
     * @param matGroupName 物料组描述
     * @return DicMaterialGroupDTO
     */
    public Long getMatGroupIdByMatGroupName(String matGroupName) {
        if (UtilString.isNullOrEmpty(matGroupName)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_MATERIAL_GROUP_NAME, matGroupName));
    }

    /**
     * 获取物料组缓存列表
     * 从缓存中获取全量物料组数据
     * 
     * @return 物料组DTO列表,如果缓存中没有数据则返回空列表
     */
    public List<DicMaterialGroupDTO> getMatGroupListCache() {
        List<DicMaterialGroupDTO> list = new ArrayList<>();
        
        // 从缓存中获取所有物料组数据
        Map<String, DicMaterialGroupDTO> matGroupMap = 
            (Map<String, DicMaterialGroupDTO>) cacheServiceImpl.get(Const.CACHE_MATERIAL_GROUP);
        
        if (matGroupMap != null && !matGroupMap.isEmpty()) {
            // 将Map中的值转换为List返回
            list.addAll(matGroupMap.values());
            
            // 按物料组编码排序
            list.sort((a, b) -> {
                if (a.getMatGroupCode() == null) {
                    return -1;
                }
                if (b.getMatGroupCode() == null) {
                    return 1; 
                }
                return a.getMatGroupCode().compareTo(b.getMatGroupCode());
            });
        }
        
        return list;
    }

    /* ******************** 移动类型主数据缓存 ************************ */

    /**
     * 根据单据类型获取移动类型ID集合
     *
     * @param receiptType 单据类型
     * @return List<Long> 移动类型id列表
     */
    public List<Long> getMoveTypeIDListCacheByReceiptType(Integer receiptType) {
        if (UtilNumber.isEmpty(receiptType)) {
            return null;
        }
        List<DicMoveType> moveTypeList = (List<DicMoveType>)cacheServiceImpl.get(Const.CACHE_MOVE_TYPE, receiptType.toString());
        List<Long> moveTypeIdList = new ArrayList<>(moveTypeList.size());
        if (moveTypeList.size() > 0) {
            for (DicMoveType moveType : moveTypeList) {
                moveTypeIdList.add(moveType.getId());
            }
        }
        return moveTypeIdList;
    }

    /**
     * 获取全部移动类型ID集合
     *
     * @return List<Long> 移动类型id列表
     */
    public List<Long> getAllMoveTypeIDListCache(){
        Map<Long, List<DicMoveType>> map = (Map<Long, List<DicMoveType>>)cacheServiceImpl.get(Const.CACHE_MOVE_TYPE);
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        List<DicMoveType> moveTypeIdList = new ArrayList<>();
        List<Long> moveTypeIds = new ArrayList<>();
        for (List<DicMoveType> list:map.values()) {
            moveTypeIdList.addAll(list);
        }
        for(DicMoveType moveType:moveTypeIdList){
            moveTypeIds.add(moveType.getId());
        }
        return moveTypeIds;
    }
    /**
     * 根据单据类型获取移动类型集合
     *
     * @param receiptType 单据类型
     * @return List<DicMoveType>
     */
    public List<DicMoveType> getMoveTypeListCacheByReceiptType(Integer receiptType) {
        if (UtilNumber.isEmpty(receiptType)) {
            return null;
        }
        return (List<DicMoveType>)cacheServiceImpl.get(Const.CACHE_MOVE_TYPE, receiptType.toString());
    }

    /**
     * 根据移动类型编码取出ID
     *
     * @param moveTypeCode 移动类型编码
     * @param specStock 特殊库存标识
     * @return Long
     */
    public Long getMoveTypeIdCacheByCode(String moveTypeCode, String specStock) {
        if (UtilString.isNullOrEmpty(moveTypeCode)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_MOVE_TYPE_CODE, moveTypeCode.toUpperCase() + specStock.toUpperCase()));
    }

    /**
     * 根据移动类型ID取出移动类型
     *
     * @param moveTypeId 移动类型ID
     * @return DicMoveType 移动类型
     */
    public DicMoveType getMoveCacheById(Long moveTypeId) {
        if (UtilNumber.isEmpty(moveTypeId)) {
            return null;
        }
        return (DicMoveType) cacheServiceImpl.get(Const.CACHE_MOVE_TYPE_ID, moveTypeId.toString());
    }

    /* ******************** 国际化主数据缓存 ************************ */

    /**
     * 获取全部WEB国际化数据
     * @return Collection<SysIl8nText>
     */
    @SuppressWarnings("unchecked")
    public Collection<SysIl8nText> getAllTextWebLanguageCache() {
        Map<Long, SysIl8nText> map = (Map<Long, SysIl8nText>)cacheServiceImpl.get(Const.CACHE_TEXT_WEB_MESSAGES);
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 获取全部PDA国际化数据
     * @return Collection<SysIl8nText>
     */
    @SuppressWarnings("unchecked")
    public Collection<SysIl8nText> getAllTextPdaLanguageCache() {
        Map<Long, SysIl8nText> map = (Map<Long, SysIl8nText>)cacheServiceImpl.get(Const.CACHE_TEXT_PDA_MESSAGES);
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return map.values();
    }

    /**
     * 获取JAVA国际化信息
     * @param messageType 国际化类型
     * @param key KEY值
     * @return SysIl8nText
     */
    public SysIl8nText getLanguageByKey(String messageType, String key) {
        if (UtilString.isNullOrEmpty(messageType) || UtilString.isNullOrEmpty(key)) {
            return null;
        }
        return (SysIl8nText)cacheServiceImpl.get(messageType, key);
    }

    /**
     * 获取JAVA国际化信息
     * @param messageType 国际化类型
     * @param hashKeyList hashKey列表
     * @return 国际化信息列表
     */
    public List<SysIl8nText> getLanguageByKeyList(String messageType, List<String> hashKeyList) {
        if (UtilString.isNullOrEmpty(messageType) || UtilCollection.isEmpty(hashKeyList)) {
            return null;
        }
        return cacheServiceImpl.multiGet(messageType, hashKeyList, SysIl8nText.class);
    }

    /**
     * 获取用户主数据
     *
     * @param userIds 用户主键集合
     * @return SysUser
     */
    public Collection<SysUser> getSysUserCacheByIds(List<Long> userIds){

        if (UtilCollection.isEmpty(userIds)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:userIds){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_USER_ID, ids,SysUser.class));
    }

    /**
     * 通过用户编码获取用户主数据
     *
     * @param userCode 用户编码
     * @return SysUser
     */
    public SysUser getSysUserCacheByuserCode(String userCode){

        if (UtilString.isNullOrEmpty(userCode)) {
            return null;
        }
        return (SysUser) cacheServiceImpl.get(Const.CACHE_USER_CODE, userCode);
    }

    /**
     * 通过用户id获取用户主数据
     *
     * @param id 用户id
     * @return SysUser
     */
    public SysUser getSysUserCacheById(Long id){

        if (UtilNumber.isNull(id)) {
            return null;
        }
        return (SysUser) cacheServiceImpl.get(Const.CACHE_USER_ID, id.toString());
    }
    /* ******************** 工器具模块缓存 ************************ */
    /**
     * 从缓存中获取全部工具类型
     *
     * @return 工具类型集合
     */
    public List<DicToolType> getAllToolTypeCache() {
        Map<Long, DicToolType> map = (Map<Long, DicToolType>) cacheServiceImpl.get(Const.CACHE_TOOL_TYPE);
        if (UtilCollection.isEmpty(Collections.singleton(map))) {
            return null;
        }
        return new ArrayList<>(map.values());
    }

    /**
     * 从缓存中获取全部工具类型
     *
     * @return 工具类型
     */
    public DicToolType getToolTypeCacheById(Long toolTypeId) {
        if (UtilNumber.isEmpty(toolTypeId)) {
            return null;
        }
        return (DicToolType) cacheServiceImpl.get(Const.CACHE_TOOL_TYPE_ID, toolTypeId.toString());
    }


    /**
     * 获取温湿度数据缓存
     * @param deviceId 设备id
     * @return BizStorageHumiture
     */
    public BizStorageHumiture getStorageHumitureByDeviceId(String deviceId) {
        if (UtilString.isNullOrEmpty(deviceId)) {
            return null;
        }
        return (BizStorageHumiture) cacheServiceImpl.get(Const.CACHE_3D_STORAGE_HUMITURE, deviceId);
    }

    /**
     * 查询项目工厂物料信息缓存
     *
     * 方法名和变量名中的 Facotry 有拼写错误，此处保持与其他位置一致，暂不更正
     * @param key 缓存查找key = "${matId}##${ftyId}##${stockSpec}##${stockSpecCode}"
     *            例：key = "283796526535619##1##Q##J-5460201.1502010107"
     * @return
     */
    public DicMaterialFacotryWbs getMaterialFacotryWbsByKey(String key) {
        if (UtilString.isNullOrEmpty(key)) {
            return null;
        }
        DicMaterialFacotryWbs result = (DicMaterialFacotryWbs) cacheServiceImpl.get(Const.CACHE_MATERIAL_FACTORY_WBS_CACHE, key);
        if (result == null) {
            String[] params = key.split("##");
            if (params.length != 4) {
                return null;
            }
            result = dicMaterialFacotryWbsDataWrap.getOne(new QueryWrapper<DicMaterialFacotryWbs>()
                    .lambda()
                    .eq(DicMaterialFacotryWbs::getMatId, params[0])
                    .eq(DicMaterialFacotryWbs::getFtyId, params[1])
                    .eq(DicMaterialFacotryWbs::getSpecStock, params[2])
                    .eq(DicMaterialFacotryWbs::getSpecStockCode, params[3])
                    .last("LIMIT 1")
            );
        }
        return result;
    }

    /**
     * 获取采购包主数据
     *
     * @param idList 采购包主键集合
     * @return 采购包主数据
     */
    public Collection<DicPurchasePackage> getPurchasePackageCacheByIds(List<Long> idList) {
        if (UtilCollection.isEmpty(idList)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:idList){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_PURCHASE_PACKAGE_ID, ids,DicPurchasePackage.class));
    }



    /**
     * 从缓存中获取采购包主数据
     *
     * @return 采购包主数据
     */
    public DicPurchasePackageDTO getPurchasePackageCacheById(Long id) {
        if (UtilNumber.isEmpty(id)) {
            return null;
        }
        return (DicPurchasePackageDTO) cacheServiceImpl.get(Const.CACHE_PURCHASE_PACKAGE_ID, id.toString());
    }

    /**
     * 根据采购包号从缓存中获取采购包主数据id
     *
     * @return 采购包主数据id
     */
    public Long getPurchasePackageIdCacheByCode(String purchasePackageCode) {
        if (UtilString.isNullOrEmpty(purchasePackageCode)) {
            return null;
        }
        return (Long) cacheServiceImpl.get(Const.CACHE_PURCHASE_PACKAGE_CODE, purchasePackageCode.toLowerCase());
    }

    /**
     * 从缓存中获取裕量分类主数据
     *
     * @return 裕量分类主数据
     */
    public DicMarginCategory getMarginCategoryCacheById(Long id) {
        if (UtilNumber.isEmpty(id)) {
            return null;
        }
        return (DicMarginCategory) cacheServiceImpl.get(Const.CACHE_MARGIN_CATEGORY_ID, id.toString());
    }

    /**
     * 根据裕量分类编码从缓存中获取采购包主数据id
     *
     * @return 裕量分类主数据id
     */
    public Long getMarginCategoryIdCacheByCode(String marginCategoryCode) {
        if (UtilString.isNullOrEmpty(marginCategoryCode)) {
            return null;
        }
        return (Long) cacheServiceImpl.get(Const.CACHE_MARGIN_CATEGORY_CODE, marginCategoryCode.toLowerCase());
    }

    /**
     * 获取裕量分类主数据
     *
     * @param idList 裕量分类主键集合
     * @return 裕量分类主数据
     */
    public Collection<DicMarginCategory> getMarginCategoryCacheByIds(List<Long> idList) {
        if (UtilCollection.isEmpty(idList)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:idList){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_MARGIN_CATEGORY_ID, ids,DicMarginCategory.class));
    }

    /**
     * 从缓存中获取项目物料码管理主数据
     *
     * @return 项目物料码管理主数据
     */
    public DicMaterialCgnDTO getMaterialCgnCacheById(Long id) {
        if (UtilNumber.isEmpty(id)) {
            return null;
        }
        return (DicMaterialCgnDTO) cacheServiceImpl.get(Const.CACHE_MATERIAL_CGN_ID, id.toString());
    }

    /**
     * 根据项目物料码管理编码从缓存中获取采购包主数据id
     *
     * @return 项目物料码管理主数据id
     */
    public Long getMaterialCgnIdCacheByCode(String materialCgnCode) {
        if (UtilString.isNullOrEmpty(materialCgnCode)) {
            return null;
        }
        return (Long) cacheServiceImpl.get(Const.CACHE_MATERIAL_CGN_CODE, materialCgnCode.toLowerCase());
    }

    /**
     * 获取项目物料码管理主数据
     *
     * @param idList 项目物料码管理键集合
     * @return 项目物料码管理主数据
     */
    public Collection<DicMaterialCgnDTO> getMaterialCgnCacheByIds(List<Long> idList) {
        if (UtilCollection.isEmpty(idList)) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for(Long id:idList){
            ids.add(UtilObject.getStringOrEmpty(id));
        }
        return (cacheServiceImpl.multiGet(Const.CACHE_MATERIAL_CGN_ID, ids,DicMaterialCgnDTO.class));
    }

    /**
     * 获取国际化类型动态配置
     */
    public Long getI18nDynamicType(String type) {
        if (UtilString.isNullOrEmpty(type)) {
            return null;
        }
        return UtilObject.getLongOrNull(cacheServiceImpl.get(Const.CACHE_I18N_DYNAMIC_TYPE, type));
    }


    /**
     * 获取国际化文本动态配置
     */
    public String getI18nDynamicText(String type) {
        if (UtilString.isNullOrEmpty(type)) {
            return null;
        }
        return UtilObject.getStringOrEmpty(cacheServiceImpl.get(Const.CACHE_I18N_DYNAMIC_TEXT, type));
    }


    /**
     * 批量获取物料信息Map
     * @param matCodeSet 物料编码集合
     * @return Map<物料编码, 物料DTO>
     */
    public Map<String, DicMaterialDTO> getMatMapByMatCodes(Set<String> matCodeSet) {
        if (UtilCollection.isEmpty(matCodeSet)) {
            return new HashMap<>();
        }
        Map<String, DicMaterialDTO> resultMap = new HashMap<>();
        for (String matCode : matCodeSet) {
            Long matId = getMatIdByMatCode(matCode);
            if (matId != null) {
                DicMaterialDTO material = getMatCacheById(matId);
                if (material != null) {
                    resultMap.put(matCode, material);
                }
            }
            
        }
        return resultMap;
    }

    /**
     * 批量获取工厂信息Map
     * @param ftyCodeSet 工厂编码集合
     * @return Map<工厂编码, 工厂DTO>
     */
    public Map<String, DicFactoryDTO> getFtyMapByCodes(Set<String> ftyCodeSet) {
        if (UtilCollection.isEmpty(ftyCodeSet)) {
            return new HashMap<>();
        }
        Map<String, DicFactoryDTO> resultMap = new HashMap<>();
        for (String ftyCode : ftyCodeSet) {
            Long ftyId = getFtyIdCacheByCode(ftyCode);
            if (ftyId != null) {
                DicFactoryDTO factory = getFtyCacheById(ftyId);
                if (factory != null) {
                    resultMap.put(ftyCode, factory);
                }
            }
        }
        return resultMap;
    }

    /**
     * 批量获取物料组信息Map
     * @param matGroupCodeSet 物料组编码集合
     * @return Map<物料组编码, 物料组DTO>
     */
    public Map<String, DicMaterialGroupDTO> getMatGroupMapByCodes(Set<String> matGroupCodeSet) {
        if (UtilCollection.isEmpty(matGroupCodeSet)) {
            return new HashMap<>();
        }
        Map<String, DicMaterialGroupDTO> resultMap = new HashMap<>();
        for (String matGroupCode : matGroupCodeSet) {
            DicMaterialGroupDTO matGroup = getMatGroupByMatGroupCode(matGroupCode);
            if (matGroup != null) {
                resultMap.put(matGroupCode, matGroup);
            }
            
        }
        return resultMap;
    }

    /**
     * 批量获取单位信息Map
     * @param unitCodeSet 单位编码集合
     * @return Map<单位编码, DicUnit>
     */
    public Map<String, DicUnit> getUnitMapByCodes(Set<String> unitCodeSet) {
        if (UtilCollection.isEmpty(unitCodeSet)) {
            return new HashMap<>();
        }
        Map<String, DicUnit> resultMap = new HashMap<>();
        for (String unitCode : unitCodeSet) {
            Long unitId = getUnitIdCacheByCode(unitCode);
            if (unitId != null) {
                DicUnit unit = getUnitCacheById(unitId);
                if (unit != null) {
                    resultMap.put(unitCode, unit);
                }
            }
        }
        return resultMap;
    }
}
