package com.inossem.wms.bizbasis.erp.service.datawrap;

import org.springframework.stereotype.Service;

import com.inossem.wms.bizbasis.erp.dao.ErpPurchaseReceiptHeadMapper;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;

/**
 * <p>
 * 采购订单行 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-10
 */
@Service
public class ErpPurchaseReceiptHeadDataWrap extends BaseDataWrap<ErpPurchaseReceiptHeadMapper, ErpPurchaseReceiptHead> {

}
