<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.user.dao.SysUserMapper">

    <select id="getSysUserListDidNotBindThisRole" resultType="com.inossem.wms.common.model.auth.user.entity.SysUser">
        SELECT su.id, su.user_code, su.user_name
        FROM sys_user su
        WHERE su.is_delete = 0
        <if test="condition != null and condition != '' ">
            AND (su.user_code LIKE CONCAT('%',#{condition},'%' ) OR su.user_name LIKE CONCAT('%',#{condition},'%' ))
        </if>
        and NOT Exists
        ( SELECT 1 FROM sys_user_role_rel role WHERE role.role_id = #{roleId} and su.id = role.user_id ) ORDER BY su.user_code
    </select>

    <select id="getUserResourceIdList" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT t1.resources_id
        FROM sys_role_resources_rel t1
                 inner JOIN sys_user_role_rel t2 ON t1.role_id = t2.role_id
                 inner JOIN sys_user t4 on t2.user_id = t4.id
        WHERE t4.id = #{userId}
    </select>

    <select id="selectSysUserPageVOList" resultType="com.inossem.wms.common.model.auth.user.vo.SysUserPageVO">
        SELECT su.id,
               su.user_code,
               su.user_name,
               su.password,
               su.user_type,
               su.is_locked,
               su.is_freeze,
               su.last_login,
               su.fail_attempts,
               su.last_fail_attempt,
               su.validity_begin_date,
               su.validity_end_date,
               su.org_code,
               su.employee_code,
               su.user_post,
               su.corp_id,
               GROUP_CONCAT(DISTINCT dic_dept.dept_name SEPARATOR ',') dept_name,
               dic_dept.id dept_id,
--                dic_dept.dept_name,
                dic_dept.dept_code,
               su.phone_number,
               su.email,
               su.is_syn,
               su.is_delete,
               su.last_password_modify_time,
               su.create_time,
               su.modify_time,
               su.create_user_id,
               su.modify_user_id,
               dc.corp_code, dc.corp_name
        FROM sys_user su
                 INNER JOIN dic_corp dc ON su.corp_id = dc.id and su.is_delete = 0
                 LEFT JOIN sys_user_dept_office_rel ON su.id = sys_user_dept_office_rel.user_id
                 LEFT JOIN dic_dept ON sys_user_dept_office_rel.dept_id = dic_dept.id
                 LEFT JOIN dic_dept_office ON sys_user_dept_office_rel.office_id = dic_dept_office.id
        ${ew.customSqlSegment}
        group by su.id
    </select>

    <select id="findImg" resultType="com.inossem.wms.common.model.auth.user.entity.SysUserImg">
        select path,img_name,img_web_path,create_time from biz_common_img where id=#{imgId}
    </select>

    <select id="getUserResourceCodeList" resultType="java.lang.String">
        SELECT t5.resources_code
        FROM sys_role_resources_rel t1
        inner JOIN sys_user_role_rel t2 ON t1.role_id = t2.role_id
        inner JOIN sys_user t4 on t2.user_id = t4.id
        INNER JOIN sys_resources t5 on t5.id = t1.resources_id
        WHERE t4.id = #{userId} 
          and t2.role_code in  
        <foreach collection="roleCodes" item="roleCode" open="(" separator="," close=")">
            #{roleCode}
        </foreach>
    </select>

    <select id="getUserByRoleCode" resultType="com.inossem.wms.common.model.auth.user.entity.SysUser">
        select su.*
        from sys_user su
                 join sys_user_role_rel rel on rel.user_id = su.id
                 join sys_role sr on sr.id = rel.role_id
        where su.is_delete = 0
          and sr.role_code = #{roleCode}
        group by su.id
    </select>

</mapper>
