package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.org.wh.dto.DicWhDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageSectionService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.org.section.dto.DicWhStorageSectionDTO;
import com.inossem.wms.common.model.org.section.po.DicWhStorageSectionSavePO;
import com.inossem.wms.common.model.org.section.po.DicWhStorageSectionSearchPO;
import com.inossem.wms.common.model.org.section.vo.DicWhStorageSectionPageVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 存储区表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@RestController
@Slf4j
@Api(tags = "存储区管理")
public class WhStorageSectionController {

    @Autowired
    protected WhStorageSectionService whStorageSectionService;


    /**
     * 查询存储区列表
     *
     * @param po 查询条件
     * @return 存储区列表
     *
     */
    @ApiOperation(value = "查询存储区列表", tags = {"存储区管理"})
    @PostMapping(path = "/org/section/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicWhStorageSectionPageVO>> getPage(@RequestBody DicWhStorageSectionSearchPO po, BizContext ctx) {
        return BaseResult.success(whStorageSectionService.getPage(ctx));
    }

    /**
     * 查看存储区详情
     *
     * @param id 主键id
     * @return 存储区详情
     *
     */
    @ApiOperation(value = "查看存储区详情", tags = {"存储区管理"})
    @GetMapping(path = "/org/section/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicWhStorageSectionDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(whStorageSectionService.getInfo(ctx));
    }

    /**
     * 新增存储区信息
     *
     * @param po 存储区信息
     *
     */
    @ApiOperation(value = "新增存储区信息", tags = {"存储区管理"})
    @PostMapping(path = "/org/section", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicWhStorageSectionSavePO po, BizContext ctx) {
        whStorageSectionService.add(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SECTION_SAVE_SUCCESS, po.getDicWhStorageSectionInfo().getSectionCode());
    }

    /**
     * 修改存储区信息
     *
     * @param po 存储区信息
     *
     */
    @ApiOperation(value = "修改存储区信息", tags = {"存储区管理"})
    @PutMapping(path = "/org/section", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicWhStorageSectionSavePO po, BizContext ctx) {
        whStorageSectionService.update(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SECTION_SAVE_SUCCESS, po.getDicWhStorageSectionInfo().getSectionCode());
    }

    /**
     * 删除存储区信息
     *
     * @param id 主键id
     *
     */
    @ApiOperation(value = "删除存储区信息", tags = {"存储区管理"})
    @DeleteMapping(path = "/org/section/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        String sectionCode = whStorageSectionService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SECTION_DELETE_SUCCESS, sectionCode);
    }

    /**
     * 存储区导入
     * @param file 存储区excel
     * @param ctx 上下文对象
     * <AUTHOR>
     */
    @ApiOperation(value = "存储区导入", notes = "存储区导入", tags = {"存储区管理"})
    @PostMapping(path = "/org/section/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestPart("file") MultipartFile file, BizContext ctx) {
        // 导入存储区信息
        whStorageSectionService.importSection(file, ctx);
        return BaseResult.success();
    }


    /**
     * 存储区下拉-全量
     * @param ctx 上下文对象
     */
    @ApiOperation(value = "存储区下拉", notes = "存储区下拉", tags = {"存储区管理"})
    @PostMapping(path = "/org/section/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicWhStorageSectionPageVO>> list(BizContext ctx) {
        return BaseResult.success(whStorageSectionService.getSectionList(ctx));
    }




}
