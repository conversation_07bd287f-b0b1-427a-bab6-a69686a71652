<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.car.dao.DicSlingMapper">

    <select id="selectPageVOList" resultType="com.inossem.wms.common.model.masterdata.car.vo.DicSlingPageVO">
        SELECT
        ds.id,
        ds.sling_code,
        ds.remark,
        ds.create_time,
        su.user_name createUserName
        FROM
        dic_sling ds
        LEFT JOIN sys_user su ON ds.create_user_id = su.id AND su.is_delete = 0
        <if test="po.slingCode != null and po.slingCode != ''">
            AND ds.sling_code = #{po.slingCode}
        </if>
        WHERE ds.is_delete = 0
    </select>

</mapper>
