package com.inossem.wms.bizbasis.todo.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.todo.service.datawrap.StWftaskDataWrap;
import com.inossem.wms.common.enums.EnumFactory;
import com.inossem.wms.common.enums.EnumTaskType;
import com.inossem.wms.common.model.auth.todo.entity.StWftask;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.ums.UmsMessage;
import com.inossem.wms.common.model.ums.UmsMessageUpdate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StWftaskService {

    @Autowired
    protected StWftaskDataWrap stWftaskDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    /**
     * 生成ums待办
     */
    public void genUmsTask(UmsMessage umsMessage) {
        StWftask stWftask=new StWftask();
        // ST_WFTASKID
        stWftask.setStWftaskid(umsMessage.getStWftaskid().longValue());
        // DATA_ID
        stWftask.setDataId(umsMessage.getDataId());
        // APP
        stWftask.setApp(umsMessage.getApp());
        // APPDESC
        stWftask.setAppdesc(umsMessage.getAppdesc());
        // WFASSNAME
        stWftask.setWfassname(umsMessage.getWfassname());
        // TASK_STATUS
        stWftask.setTaskStatus(umsMessage.getTaskStatus());
        // ASSIGNCODE
        stWftask.setAssigncode(umsMessage.getAssigncode());
        stWftask.setAssignname(umsMessage.getAssignname());
        // STARTDATE
        stWftask.setStartdate(umsMessage.getStartdate());
        stWftask.setRespersonid(umsMessage.getRespersonid());
        // WFDESC
        stWftask.setWfdesc(umsMessage.getWfdesc());
        // URL 配置文件
        stWftask.setUrl(umsMessage.getUrl());
        stWftask.setTaskType(umsMessage.getTaskType());
        stWftaskDataWrap.save(stWftask);
    }
    /**
     * ums阅知
     */
    public void completeUmsTaskOther(UmsMessageUpdate umsMessage) {
        StWftask stWftask=new StWftask();
        stWftask.setDataId(umsMessage.getDataId());
        stWftask.setAssigncode(umsMessage.getAssigneeUser());
        stWftask.setEnddate(umsMessage.getProcessDate());
        stWftask.setStWftaskid(umsMessage.getStWftaskid());
        stWftask.setTaskStatus(umsMessage.getTaskStatus());
        stWftaskDataWrap.saveOrUpdate(stWftask);
    }

    /**
     * 审批完成
     */
    public void completeUmsTask(UmsMessageUpdate umsMessage) {
        StWftask stWftask=new StWftask();
        stWftask.setDataId(umsMessage.getDataId());
        stWftask.setAssigncode(umsMessage.getAssigneeUser());
        stWftask.setEnddate(umsMessage.getProcessDate());
        stWftask.setStWftaskid(umsMessage.getStWftaskid());
        stWftask.setTaskStatus(umsMessage.getTaskStatus());
        stWftaskDataWrap.saveOrUpdate(stWftask);

        QueryWrapper<StWftask> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StWftask::getDataId ,umsMessage.getDataId()).ne(StWftask::getAssigncode,umsMessage.getAssigneeUser());
        stWftaskDataWrap.physicalDelete(wrapper);
    }

    /**
     * 审批转办
     * @param umsMessage
     */
    public void transferTask(UmsMessageUpdate umsMessage) {
        StWftask stWftask=new StWftask();
        stWftask.setDataId(umsMessage.getDataId());
        stWftask.setAssigncode(umsMessage.getAssigneeUser());
        stWftask.setAssignname(umsMessage.getAssigneeUserName());
        stWftask.setRespersonid(umsMessage.getOriginalAssigneeUser());
        stWftask.setStWftaskid(umsMessage.getStWftaskid());
        stWftaskDataWrap.saveOrUpdate(stWftask);
    }


    /**
     * 根据工厂编码获取任务类别
     * @param ftyId
     * @param ftyCode
     * @return
     */
    public String getTaskType(Long ftyId,String ftyCode) {
        String taskType=EnumTaskType.UMS.getValue();
        if(StringUtils.isEmpty(ftyCode)){
            if(ftyId!=null){
                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(ftyId);
                if(factoryDTO!=null){
                    ftyCode=factoryDTO.getFtyCode();
                }
            }
        }
        if(StringUtils.isEmpty(ftyCode)){
            taskType=EnumTaskType.UMS.getValue();
        }else{
            if(EnumFactory.J358.getFtyCode().equals(ftyCode) ||
               EnumFactory.W358.getFtyCode().equals(ftyCode) ||
               EnumFactory.Y358.getFtyCode().equals(ftyCode) ){
                taskType=EnumTaskType.CJ.getValue();
            }
        }
        return taskType;
    }

}
