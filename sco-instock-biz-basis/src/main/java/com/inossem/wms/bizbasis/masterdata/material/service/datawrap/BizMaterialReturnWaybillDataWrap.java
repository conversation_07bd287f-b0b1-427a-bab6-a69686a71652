package com.inossem.wms.bizbasis.masterdata.material.service.datawrap;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.masterdata.material.dao.BizMaterialReturnWaybillMapper;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnWaybillDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnWaybill;
import com.inossem.wms.common.model.masterdata.mat.info.po.BizMaterialReturnWayBillSearchPO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;

/**
 * <p>
 * 物资返运单抬头表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@Service
public class BizMaterialReturnWaybillDataWrap extends BaseDataWrap<BizMaterialReturnWaybillMapper, BizMaterialReturnWaybill> {


    @Autowired
    private DataFillService dataFillService;


    public List<BizMaterialReturnWaybillDTO> getWaybillList(WmsQueryWrapper<BizMaterialReturnWayBillSearchPO> queryWrapper) {
        return this.baseMapper.selectWaybillList(queryWrapper);
    }
}
