package com.inossem.wms.bizbasis.electronicscale.controller;


import com.inossem.wms.bizbasis.electronicscale.service.biz.LogElectronicScaleRecordService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.collection.vo.BizReceiptCollectionTaskVO;
import com.inossem.wms.common.model.bizdomain.electronicscale.dto.LogElectronicScaleRecordDTO;
import com.inossem.wms.common.model.bizdomain.electronicscale.po.LogElectronicScaleRecordSearchPO;
import com.inossem.wms.common.model.bizdomain.electronicscale.vo.LogElectronicScaleRecordVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  电子秤移动记录 controller
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-09
 */
@RestController
@Api(tags = "电子秤管理-电子秤移动记录")
public class LogElectronicScaleRecordController {
    @Autowired
    private LogElectronicScaleRecordService logElectronicScaleRecordService;

    @ApiOperation(value = "电子秤移动记录-分页", tags = {"电子秤管理-电子秤移动记录"})
    @PostMapping(path = "/log/electronic-scale-record/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<LogElectronicScaleRecordVO>> getPage(@RequestBody LogElectronicScaleRecordSearchPO po, BizContext ctx) {
        logElectronicScaleRecordService.getPage(ctx);
        PageObjectVO<LogElectronicScaleRecordVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "电子秤移动记录-移动记录上报", tags = {"电子秤管理-电子秤移动记录"})
    @PostMapping(path = "/electronic-scale-record/report", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> electronicScaleRecordReport(@RequestBody LogElectronicScaleRecordDTO po, BizContext ctx) {
        logElectronicScaleRecordService.electronicScaleRecordReport(ctx);
        return BaseResult.success();
    }
}

