<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.material.dao.DicUnitRelMapper">

    <select id="selectDicUnitRelPageVOList"
            resultType="com.inossem.wms.common.model.masterdata.unit.vo.DicUnitRelPageVO">
        select
        dur.id, dur.fty_id, dur.mat_id, dur.source_unit_id, dur.target_unit_id, dur.source_qty, dur.target_qty,
        dur.is_delete, dur.create_time, dur.modify_time, dur.create_user_id, dur.modify_user_id,
        dm.mat_code, dm.mat_name,
        dus.unit_code source_unit_code, dus.unit_name source_unit_name,
        dut.unit_code target_unit_code, dut.unit_name target_unit_name
        from dic_unit_rel dur
        inner join dic_material dm on dur.mat_id = dm.id and dur.is_delete = 0
        inner join dic_unit dus on dur.source_unit_id = dus.id
        inner join dic_unit dut on dur.target_unit_id = dut.id ${ew.customSqlSegment}
    </select>
</mapper>
