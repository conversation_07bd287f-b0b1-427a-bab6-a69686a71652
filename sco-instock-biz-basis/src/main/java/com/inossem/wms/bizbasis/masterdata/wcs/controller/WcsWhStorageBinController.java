package com.inossem.wms.bizbasis.masterdata.wcs.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.bizbasis.masterdata.wcs.service.biz.WcsWhStorageBinService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.wcs.po.DicWcsWhStorageBinSavePO;
import com.inossem.wms.common.model.wcs.po.DicWcsWhStorageBinSearchPO;
import com.inossem.wms.common.model.wcs.vo.DicWcsWhStorageBinPageVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * WCS仓位 视图controller层
 * 
 * <AUTHOR>
 */
@RestController
@Api(tags = "WCS仓位管理")
public class WcsWhStorageBinController {

    @Autowired
    private WcsWhStorageBinService wcsWhStorageBinService;

    /**
     * 查询 WCS仓位 列表
     *
     * @param po 查询条件
     * @return XXX列表
     *
     */
    @ApiOperation(value = "查询WCS仓位列表", tags = {"WCS仓位管理"})
    @PostMapping(path = "/wcs-storage-bin/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicWcsWhStorageBinPageVO>> getList(@RequestBody DicWcsWhStorageBinSearchPO po, BizContext ctx) {
        return BaseResult.success(wcsWhStorageBinService.getPage(ctx));
    }

    /**
     * 新增 WCS仓位信息
     *
     * @param po WCS仓位信息
     *
     */
    @ApiOperation(value = "新增WCS仓位信息", tags = {"WCS仓位管理"})
    @PostMapping(path = "/wcs-storage-bin", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicWcsWhStorageBinSavePO po, BizContext ctx) {
        wcsWhStorageBinService.add(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WCS_STORAGE_BIN_SAVE_SUCCESS, po.getDicWcsWhStorageBinDTO().getBinCode());
    }

    /**
     * 修改 WCS仓位信息
     *
     * @param po WCS仓位信息
     *
     */
    @ApiOperation(value = "修改WCS仓位信息", tags = {"WCS仓位管理"})
    @PutMapping(path = "/wcs-storage-bin", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicWcsWhStorageBinSavePO po, BizContext ctx) {
        wcsWhStorageBinService.update(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WCS_STORAGE_BIN_SAVE_SUCCESS, po.getDicWcsWhStorageBinDTO().getBinCode());
    }

    /**
     * 删除 WCS仓位信息
     *
     * @param id 主键id
     *
     */
    @ApiOperation(value = "删除WCS仓位信息", tags = {"WCS仓位管理"})
    @DeleteMapping(path = "/wcs-storage-bin/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        String binCode = wcsWhStorageBinService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WCS_STORGAE_BIN_DELETE_SUCCESS, binCode);
    }

}
