<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.material.dao.DicMaterialFactoryMapper">

    <select id="selectDicMaterialFactoryPageVOList"
            resultType="com.inossem.wms.common.model.masterdata.mat.fty.vo.DicMaterialFactoryPageVO">
        select dmf.id,
               dmf.mat_id,
               dmf.fty_id,
               dmf.inspect_classify_id,
               dmf.material_classify_id,
               dmf.tag_type,
               dmf.is_single,
               dmf.move_avg_price,
               dmf.price_unit,
               dmf.price_method,
               dmf.standard_price,
               dmf.shelf_life_max,
               dmf.shelf_life_min,
               dmf.package_type,
               dmf.deposit_type,
               dmf.shelf_life,
               dmf.remind_day,
               dmf.security_qty,
               dmf.order_point_qty,
               dmf.is_batch_erp_enabled,
               dmf.is_batch_product_enabled,
               dmf.is_package_enabled,
               dmf.create_time,
               dmf.modify_time,
               dmf.create_user_id,
               dmf.modify_user_id,
               dmf.unitized_flag,
               dmf.stock_group,
               dm.mat_code, dm.mat_name,
               dmt.mat_type_code, dmt.mat_type_name,
               dmg.mat_group_code, dmg.mat_group_name,
               df.fty_code, df.fty_name,
               du.unit_code, du.unit_name,
               dut.unit_code price_unit_code, dut.unit_name price_unit_name
        from dic_material_factory dmf
        inner join dic_material dm on dmf.mat_id = dm.id
        inner join dic_material_type dmt on dm.mat_type_id = dmt.id
        inner join dic_material_group dmg on dm.mat_group_id = dmg.id
        inner join dic_factory df on dmf.fty_id = df.id
        inner join dic_unit du on dm.unit_id = du.id
        left join dic_unit dut on dmf.price_unit = dut.id ${ew.customSqlSegment}
    </select>

    <update id="updateUnitizedFlag">
        update dic_material_factory set unitized_flag=#{unitizedFlag} where fty_id=#{ftyId} and mat_id=#{matId}
    </update>
</mapper>
