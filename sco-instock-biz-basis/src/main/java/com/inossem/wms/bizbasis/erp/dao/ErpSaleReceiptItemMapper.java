package com.inossem.wms.bizbasis.erp.dao;

import com.inossem.wms.common.model.erp.dto.ErpSaleReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpSaleReceiptItem;
import com.inossem.wms.common.model.erp.po.SaleReceiptQueryPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-11
 */
public interface ErpSaleReceiptItemMapper extends WmsBaseMapper<ErpSaleReceiptItem> {

    /**
     * 获取销售单行项目
     *
     * @param po 查询条件
     * @return List<ErpSaleReceiptItemDTO>
     */
    List<ErpSaleReceiptItemDTO> getSaleReceiptItemList(@Param("po") SaleReceiptQueryPO po);
}
