package com.inossem.wms.bizbasis.masterdata.car.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.car.dao.DicSlingMapper;
import com.inossem.wms.common.model.masterdata.car.entity.DicSling;
import com.inossem.wms.common.model.masterdata.car.po.DicSlingSearchPO;
import com.inossem.wms.common.model.masterdata.car.vo.DicSlingPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 吊带管理 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-23
 */
@Service
public class DicSlingDataWrap extends BaseDataWrap<DicSlingMapper, DicSling> {


    /**
     * 查询吊带列表-分页
     *
     * @param page 分页参数
     * @param po 条件
     * @return 吊带列表
     *
     */
    public IPage<DicSlingPageVO> getPageVOList(IPage<DicSlingPageVO> page, DicSlingSearchPO po) {
        List<DicSlingPageVO> pageVOList = this.baseMapper.selectPageVOList(page, po);
        return page.setRecords(pageVOList);
    }
}
