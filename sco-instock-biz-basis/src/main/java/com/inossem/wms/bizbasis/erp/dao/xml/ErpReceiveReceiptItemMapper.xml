<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.erp.dao.ErpReceiveReceiptItemMapper">

    <!--获取领料单行项目-->
    <select id="getReceiveReceiptItemList" resultType="com.inossem.wms.common.model.erp.dto.ErpReceiveReceiptItemDTO">
        SELECT
        head.receipt_code,
        head.erp_create_user_code,
        head.erp_create_time,
        item.*
        FROM
        erp_receive_receipt_head head INNER JOIN erp_receive_receipt_item item ON head.id = item.head_id AND item.is_delete = 0
        AND head.is_delete = 0
        <where>
            <if test="po.receiptCode != null and po.receiptCode != '' ">
                AND head.receipt_code = #{po.receiptCode}
            </if>
            <if test="po.remark != null and po.remark != '' ">
                AND head.remark   LIKE CONCAT('%', #{po.remark}, '%')
            </if>
            <if test="po.erpCreateUserCode != null and po.erpCreateUserCode != '' ">
                AND head.erp_create_user_code = #{po.erpCreateUserCode}
            </if>
            <if test="po.matId != null">
                AND item.mat_id = #{po.matId}
            </if>
            <if test="po.workOrder != null and po.workOrder != '' ">
                AND item.work_order = #{po.workOrder}
            </if>
        </where>
    </select>

    <!--获取领料单行项目-->
    <select id="getReceiveReceiptItemAttrList" resultType="com.inossem.wms.common.model.erp.dto.ErpReceiveReceiptItemDTO">
        SELECT
        head.receipt_code,
        head.remark receipt_remark,
        head.erp_create_user_code,
        head.erp_create_time,
        item.* ,
        bi.id batch_id,
        (select sum(qty) from stock_bin sb where sb.batch_id=bi.id and sb.mat_id=item.mat_id and  sb.fty_id=item.fty_id  AND sb.location_id = item.location_id ) as stock_qty,
        (select sum(broi.finish_qty - ifnull(biz_receipt_return_item.qty,0)) from biz_receipt_output_head broh
        join biz_receipt_output_item broi on broh.id=broi.head_id
        join biz_receipt_apply_item brai on brai.id=broi.pre_receipt_item_id
        left join biz_receipt_return_item on biz_receipt_return_item.refer_receipt_item_id = broi.id and biz_receipt_return_item.refer_receipt_type = 414 and biz_receipt_return_item.item_status = 90
        where broh.is_delete = 0 and broi.is_delete = 0  and brai.is_delete = 0
        and broh.receipt_status=90 and broh.receipt_type=414 and broi.item_status = 90
        and brai.pre_receipt_item_id=item.id ) as send_qty
        FROM
        erp_receive_receipt_head head
        INNER JOIN erp_receive_receipt_item item ON head.id = item.head_id
        AND item.is_delete = 0
        AND head.is_delete = 0
        left JOIN biz_batch_info bi on bi.mat_id=item.mat_id and bi.spec_stock=item.spec_stock
        and bi.fty_id=item.fty_id and bi.spec_stock_code=item.spec_stock_code
        INNER JOIN dic_factory df ON item.fty_id = df.id
        <where>
            item.id in
            <foreach collection="po.itemIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="po.ftyCodeList != null and po.ftyCodeList.size() > 0">
                AND df.fty_code in
                <foreach collection="po.ftyCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
