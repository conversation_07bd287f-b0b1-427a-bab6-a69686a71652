package com.inossem.wms.bizbasis.batch.dao;

import com.inossem.wms.common.model.batch.dto.BizBatchInfoMaintainDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfoMaintain;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 批次信息维保表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Mapper
public interface BizBatchInfoMaintainMapper extends WmsBaseMapper<BizBatchInfoMaintain> {

    /**
     * 根据批次id查询批次维保列表
     *
     * @param batchId 批次id
     * @return List<BizBatchInfoMaintain> 批次维保列表
     */
    List<BizBatchInfoMaintain> selectByBatchId(@Param("batchId") Long batchId);

    /**
     * 根据批次id列表查询批次维保列表
     *
     * @param batchIdList 批次id列表
     * @return List<BizBatchInfoMaintain> 批次维保列表
     */
    List<BizBatchInfoMaintainDTO> listByBatchIdList(@Param("batchIdList") List<Long> batchIdList);
}
