package com.inossem.wms.bizbasis.stock.service.biz;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockOccupyDataWrap;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockOccupy;
import com.inossem.wms.common.util.UtilNumber;

/**
 * 库存占用通用方法
 * 
 * <AUTHOR>
 */
@Service
public class StockOccupyService {
    @Autowired
    protected StockCommonService stockCommonService;
    @Autowired
    protected StockOccupyDataWrap stockOccupyDataWrap;

    /**
     * 根据单据,配货特征值,库存占用
     *
     * 必须传递的参数: stockStatus库存类型集合;featureCode特性代码;assembleList配货特性列表
     */
    public void saveByAssembleDTO(BizReceiptAssembleRuleSearchPO assembleRuleSearchPO) {
        // 根据配货库存生成占用
        List<StockOccupy> list = new ArrayList<>();
        // 根据特征批量查询库存
        List<StockBinDTO> stockBinDTOList = stockCommonService.selectStockBinByAssembleList(assembleRuleSearchPO);
        for (BizReceiptAssemble assembleDTO : assembleRuleSearchPO.getAssembleList()) {
            // 特征唯一键
            String assembleKey = assembleDTO.getFtyId() + "-" + assembleDTO.getLocationId() + "-" + assembleDTO.getMatId() + "-" + assembleDTO.getSpecValue();
            // 特征数量
            BigDecimal qty = assembleDTO.getQty();
            for (StockBinDTO stockBinDTO : stockBinDTOList) {
                // 库存唯一键
                String stockBinKey = stockBinDTO.getFtyId() + "-" + stockBinDTO.getLocationId() + "-" + stockBinDTO.getMatId() + "-" + UtilString.getStrIfNull(stockBinDTO.getSpecValue());
                // 唯一键相同时
                if (assembleKey.equals(stockBinKey)) {
                    // 拼装占用表
                    StockOccupy stockOccupy = new StockOccupy();
                    stockOccupy.setReceiptType(assembleDTO.getReceiptType());
                    stockOccupy.setReceiptHeadId(assembleDTO.getReceiptHeadId());
                    stockOccupy.setReceiptItemId(assembleDTO.getReceiptItemId());
                    stockOccupy.setStockBinId(stockBinDTO.getId());
                    stockOccupy.setStockBatchId(stockBinDTO.getStockBatchId());
                    if (stockBinDTO.getStockQty().compareTo(qty) >= 0) {
                        // 库存数量>=特征数量:占用数量=特征数量
                        stockOccupy.setQty(qty);
                        list.add(stockOccupy);
                        // 结束配货
                        break;

                    } else {
                        // 库存数量<特征数量:占用数量=库存数量
                        stockOccupy.setQty(stockBinDTO.getStockQty());
                        list.add(stockOccupy);
                        // 剩余特征数量=特征数量-库存数量
                        qty = qty.subtract(stockBinDTO.getStockQty());

                    }
                }
            }
        }

        // 占用表保存
        stockOccupyDataWrap.saveBatch(list);
    }

    /**
     * 根据单据,解除库存占用
     */
    public void deleteByReceipt(Integer receiptType, Long receiptHeadId, Long receiptItemId, Long receiptBinId) {
        QueryWrapper<StockOccupy> wrapper = new QueryWrapper<>();
        // 单据类型
        wrapper.lambda().eq(StockOccupy::getReceiptType, receiptType);
        // 单据head表id
        wrapper.lambda().eq(StockOccupy::getReceiptHeadId, receiptHeadId);
        if (UtilNumber.isNotEmpty(receiptItemId)) {
            // 单据item表id
            wrapper.lambda().eq(StockOccupy::getReceiptItemId, receiptItemId);
        }
        if (UtilNumber.isNotEmpty(receiptBinId)) {
            // 单据bin表id
            wrapper.lambda().eq(StockOccupy::getReceiptBinId, receiptBinId);
        }
        stockOccupyDataWrap.remove(wrapper);
    }


    /**
     * 根据单据,行项目，bin批量解除库存占用
     * @param receiptType
     * @param receiptHeadId
     * @param receiptItemIdList
     * @param receiptBinIdList
     */
    public void deleteBatchByReceipt(Integer receiptType, Long receiptHeadId, List<Long> receiptItemIdList, List<Long> receiptBinIdList) {
        QueryWrapper<StockOccupy> wrapper = new QueryWrapper<>();
        // 单据类型
        wrapper.lambda().eq(StockOccupy::getReceiptType, receiptType);
        // 单据head表id
        wrapper.lambda().eq(StockOccupy::getReceiptHeadId, receiptHeadId);
        if (UtilCollection.isNotEmpty(receiptItemIdList)) {
            // 单据item表id
            wrapper.lambda().in(StockOccupy::getReceiptItemId, receiptItemIdList);
        }
        if (UtilCollection.isNotEmpty(receiptBinIdList)) {
            // 单据bin表id
            wrapper.lambda().in(StockOccupy::getReceiptBinId, receiptBinIdList);
        }
        stockOccupyDataWrap.remove(wrapper);
    }
}
