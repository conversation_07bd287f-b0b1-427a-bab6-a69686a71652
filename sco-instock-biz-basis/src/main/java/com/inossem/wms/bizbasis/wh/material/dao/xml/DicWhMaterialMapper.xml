<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.wh.material.dao.DicWhMaterialMapper">

    <select id="getPage" resultType="com.inossem.wms.common.model.wh.material.vo.DicWhMaterialPageVO">

        SELECT
			dwm.id,
			dm.mat_name,
			dm.mat_code,
			dw.id AS whId,
			dw.wh_code,
			dmg.mat_group_name as materialGroupName,
			du.unit_name,
			dwm.is_used,
			dwm.create_time
		FROM
			dic_wh_material dwm
				left join dic_material dm on dwm.mat_id = dm.id
				left join dic_wh dw on dw.id= dwm.wh_id
				left join dic_material_group dmg on dmg.id = dm.mat_group_id
				left join dic_unit du on du.id = dm.unit_id
			where 1=1 and dwm.is_delete = 0
			<if test="po.matCode != '' and po.matCode != null">
				and dm.mat_code = #{po.matCode}
			</if>
    </select>
</mapper>
