package com.inossem.wms.bizbasis.masterdata.material.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFactory;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySearchPO;
import com.inossem.wms.common.model.masterdata.mat.fty.vo.DicMaterialFactoryPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料工厂数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Mapper
public interface DicMaterialFactoryMapper extends WmsBaseMapper<DicMaterialFactory> {

    /**
     * 物料工厂列表分页结果集查询
     * 
     * @param page
     * @param wrapper
     * <AUTHOR>
     * @return
     */
    List<DicMaterialFactoryPageVO> selectDicMaterialFactoryPageVOList(IPage<DicMaterialFactoryPageVO> page,
                                                                      @Param(Constants.WRAPPER) QueryWrapper<DicMaterialFactorySearchPO> wrapper);


    void updateUnitizedFlag(@Param("unitizedFlag") Integer unitizedFlag, @Param("ftyId") Long ftyId, @Param("matId") Long matId);
}
