package com.inossem.wms.bizbasis.rfid.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelReceiptRelDataWrap;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumDeleteFlag;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 标签关联记录表
 *
 * <AUTHOR>
 */
@Service
public class LabelReceiptRelService {

    @Autowired
    protected BizLabelReceiptRelDataWrap bizLabelReceiptRelDataWrap;
    @Autowired
    protected BizLabelDataDataWrap bizLabelDataDataWrap;
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 根据状态,单据类型,单据itemIdList查询RFID标签与各单据的关联信息并分组
     */
    public Map<String, List<BizLabelDataDTO>> getLabelReceiptRefBatch(Integer status, Integer receiptType, List<Long> itemIdList) {
        // 根据状态,单据类型,单据itemIdList查询码盘关联信息
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(null != status, BizLabelReceiptRel::getStatus, status)
                .eq(null != receiptType, BizLabelReceiptRel::getReceiptType, receiptType).in(BizLabelReceiptRel::getReceiptItemId, itemIdList);
        List<BizLabelReceiptRel> bizLabelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        // 根据labelid填充code
        List<BizLabelReceiptRelDTO> labelReceiptRelDTOList = UtilCollection.toList(bizLabelReceiptRelList, BizLabelReceiptRelDTO.class);
//        dataFillService.fillAttr(labelReceiptRelDTOList);
        // 转换返回labelData对象
        List<BizLabelDataDTO> labelDataDTList = UtilCollection.toList(labelReceiptRelDTOList, BizLabelDataDTO.class);
        dataFillService.fillAttr(labelDataDTList);
        Map<String, List<BizLabelDataDTO>> resultMap = new HashMap<>(labelReceiptRelDTOList.size());
        if (labelDataDTList.size() > 0) {
            resultMap = labelDataDTList.stream().collect(Collectors.groupingBy(vo -> vo.getReceiptHeadId().toString().concat("-")
                    .concat(vo.getReceiptItemId().toString().concat("-").concat(vo.getReceiptType().toString()))));
        }
        return resultMap;
    }

    /**
     * 根据条件获取RFID标签相关信息(DTO)
     *
     * @param headIdList      单据id/码盘id列表
     * @param labelIdList     标签id列表
     * @param gtReceiptStatus 大于等于状态
     * @param labelReceiptRel 要查询的关联单据信息
     */
    public List<BizLabelReceiptRelDTO> getDTOList(List<Long> headIdList, List<Long> labelIdList, EnumReceiptStatus gtReceiptStatus,
                                                  BizLabelReceiptRel labelReceiptRel) {
        List<BizLabelReceiptRel> labelReceiptRelList = this.getList(headIdList, labelIdList, gtReceiptStatus, labelReceiptRel);
        // 根据labelid填充code
        List<BizLabelReceiptRelDTO> labelReceiptRelDTOList = UtilCollection.toList(labelReceiptRelList, BizLabelReceiptRelDTO.class);
        dataFillService.fillAttr(labelReceiptRelDTOList);
        return labelReceiptRelDTOList;
    }

    /**
     * 更新标签关联关系
     */
    public void updateBatchList(List<BizLabelReceiptRel> bizLabelReceiptRelList) {
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            bizLabelReceiptRelDataWrap.updateBatchById(bizLabelReceiptRelList);
        }
    }


    /**
     * 根据条件获取RFID标签相关信息(Entity)
     *
     * @param headIdList      单据id/码盘id列表
     * @param labelIdList     标签id列表
     * @param gtReceiptStatus 大于等于状态
     * @param labelReceiptRel 要查询的关联单据信息
     */
    public List<BizLabelReceiptRel> getList(List<Long> headIdList, List<Long> labelIdList, EnumReceiptStatus gtReceiptStatus,
                                            BizLabelReceiptRel labelReceiptRel) {
        QueryWrapper<BizLabelReceiptRel> queryWrapper = new QueryWrapper<>();
        if (headIdList != null) {
            // 单据id/码盘id
            queryWrapper.lambda().in(BizLabelReceiptRel::getReceiptHeadId, headIdList);
        }
        if (labelIdList != null) {
            // 标签id列表
            queryWrapper.lambda().in(BizLabelReceiptRel::getLabelId, labelIdList);
        }
        if (gtReceiptStatus != null) {
            // 大于等于状态
            queryWrapper.lambda().gt(BizLabelReceiptRel::getStatus, gtReceiptStatus.getValue());
        }
        if (labelReceiptRel != null && labelReceiptRel.getStatus() != null) {
            // 等于状态
            queryWrapper.lambda().eq(BizLabelReceiptRel::getStatus, labelReceiptRel.getStatus());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptType() != null) {
            // 单据类型
            queryWrapper.lambda().eq(BizLabelReceiptRel::getReceiptType, labelReceiptRel.getReceiptType());
        }
        if (labelReceiptRel != null && labelReceiptRel.getPreReceiptHeadId() != null) {
            // 前置单据id
            queryWrapper.lambda().eq(BizLabelReceiptRel::getPreReceiptHeadId, labelReceiptRel.getPreReceiptHeadId());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptHeadId() != null) {
            // 单据head表id
            queryWrapper.lambda().eq(BizLabelReceiptRel::getReceiptHeadId, labelReceiptRel.getReceiptHeadId());
        }
        return bizLabelReceiptRelDataWrap.list(queryWrapper);
    }

    /**
     * 批量保存RFID标签与各单据之间的关联信息
     */
    public void saveBatch(List<BizLabelReceiptRel> bizLabelReceiptRelList) {

    	if(UtilCollection.isEmpty(bizLabelReceiptRelList)){
    		return;
		}

		// 填充默认值
    	for(BizLabelReceiptRel bizLabelReceiptRel : bizLabelReceiptRelList){
			bizLabelReceiptRel.setStatus(bizLabelReceiptRel.getStatus() == null ? EnumDbDefaultValueInteger.BIZ_LABEL_RECEIPT_REL_STATUS.getValue() : bizLabelReceiptRel.getStatus());
		}

        bizLabelReceiptRelDataWrap.saveBatch(bizLabelReceiptRelList);
    }

    /**
     * 复制标签关联关系
     * @param labelDataList
     */
    public void copyRel(List<BizLabelData> labelDataList){

        if(UtilCollection.isNotEmpty(labelDataList)){
            return;
        }
        List<Long> sourceLabelList = labelDataList.stream().map(e->e.getSourceLabelId()).collect(Collectors.toList());
        QueryWrapper<BizLabelReceiptRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BizLabelReceiptRel::getLabelId, sourceLabelList);
        List<BizLabelReceiptRel> soureRelList = bizLabelReceiptRelDataWrap.list(queryWrapper);
        if(UtilCollection.isNotEmpty(sourceLabelList)){
            Map<Long, List<BizLabelReceiptRel>> sourceRelMap = soureRelList.stream().collect(Collectors.groupingBy(e->e.getLabelId()));
            List<BizLabelReceiptRel> newList = new ArrayList<>();

            for(BizLabelData labelData : labelDataList){
                List<BizLabelReceiptRel> sourceList = sourceRelMap.get(labelData.getSourceLabelId());
                if(UtilCollection.isNotEmpty(sourceList)){
                    List<BizLabelReceiptRel> innerList = UtilCollection.toList(sourceList,BizLabelReceiptRel.class);
                    innerList.forEach(e->{
                        e.setId(null);
                        e.setLabelId(labelData.getId());
                    });
                    newList.addAll(innerList);
                }
            }
            if(UtilCollection.isNotEmpty(newList)){
                bizLabelReceiptRelDataWrap.saveBatchOptimize(newList, 100);
            }
        }

    }

    /**
     * 更新RFID标签与各单据关联表状态
     *
     * @param idList          主键id列表
     * @param labelIdList     标签id列表
     * @param toStatus        修改后状态
     * @param labelReceiptRel 要修改的对象
     */
    public void updateStatus(List<Long> idList, List<Long> labelIdList, EnumReceiptStatus toStatus, BizLabelReceiptRel labelReceiptRel) {
        UpdateWrapper<BizLabelReceiptRel> updateWrapper = new UpdateWrapper<>();
        // 更新状态
        updateWrapper.lambda().set(BizLabelReceiptRel::getStatus, toStatus.getValue());
        if (idList != null) {
            // 主键id列表
            updateWrapper.lambda().in(BizLabelReceiptRel::getId, idList);
        }
        if (labelIdList != null) {
            // 标签id列表
            updateWrapper.lambda().in(BizLabelReceiptRel::getLabelId, labelIdList);
        }
        if (labelReceiptRel != null && labelReceiptRel.getStatus() != null) {
            // 现在状态
            updateWrapper.lambda().eq(BizLabelReceiptRel::getStatus, labelReceiptRel.getStatus());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptType() != null) {
            // 单据类型
            updateWrapper.lambda().eq(BizLabelReceiptRel::getReceiptType, labelReceiptRel.getReceiptType());
        }
        if (labelReceiptRel != null && labelReceiptRel.getReceiptHeadId() != null) {
            // 关联单据head表id
            updateWrapper.lambda().in(BizLabelReceiptRel::getReceiptHeadId, labelReceiptRel.getReceiptHeadId());
        }

        bizLabelReceiptRelDataWrap.update(updateWrapper);
    }

    /**
     * 删除标签数据及标签关联数据
     *
     * @param receiptHeadId     单据head主键
     */
    public void multiRemoveLabel(Long receiptHeadId) {
        // 根据标签主键逻辑删除关联数据
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();

        wrapper.lambda()
                // 单据head主键
                .eq(UtilNumber.isNotEmpty(receiptHeadId), BizLabelReceiptRel::getReceiptHeadId, receiptHeadId);
        // 获取标签关联数据
        List<BizLabelReceiptRel> LabelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        if (UtilCollection.isNotEmpty(LabelReceiptRelList)) {
            // 转DTO
            List<BizLabelReceiptRelDTO> labelReceiptRelDTOList = UtilCollection.toList(LabelReceiptRelList, BizLabelReceiptRelDTO.class);

            /* **** 删除标签数据 **** */
            bizLabelDataDataWrap.multiPhysicalDeleteByIdList(labelReceiptRelDTOList.stream().map(BizLabelReceiptRelDTO::getLabelId).collect(Collectors.toList()));

            /* **** 删除标签关联数据 **** */
            bizLabelReceiptRelDataWrap.multiPhysicalDeleteByIdList(labelReceiptRelDTOList.stream().map(BizLabelReceiptRelDTO::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 删除RFID标签与各单据关联信息
     *
     * @param labelRelIdList 标签与各单据关联表主键
     */
    public void removeByIds(List<Long> labelRelIdList) {
        bizLabelReceiptRelDataWrap.removeByIds(labelRelIdList);
    }

    /**
     * 查询RFID标签关联结果
     *
     * @param receiptItemId 单据id/码盘id列表
     * @param status        状态
     * @param receiptType   单据类型
     * @return 结果行数
     */
    public List<BizLabelReceiptRelDTO> getLabelReceiptRel(Long receiptItemId, Integer status, Integer receiptType) {
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                // 状态
                .ge(null != status, BizLabelReceiptRel::getStatus, status)
                // 单据类型
                .eq(null != receiptType, BizLabelReceiptRel::getReceiptType, receiptType)
                // 单据id/码盘id列表
                .in(BizLabelReceiptRel::getReceiptItemId, receiptItemId);

        List<BizLabelReceiptRel> labelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);

        return UtilCollection.toList(labelReceiptRelList, BizLabelReceiptRelDTO.class);
    }

    /**
     * 批量删除RFID标签关联数据
     *
     * @param labelIds 标签主键
     */
    public void multiDeleteLabelReceiptRel(List<Long> labelIds) {
        if (UtilCollection.isNotEmpty(labelIds)) {
            // 根据标签主键逻辑删除关联数据
            QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();

            wrapper.lambda().in(BizLabelReceiptRel::getLabelId, labelIds);
            bizLabelReceiptRelDataWrap.remove(wrapper);
        }
    }

    /**
     * 物理删除RFID标签关联数据
     *
     * @param receiptHeadId 单据id
     */
    public void physicalDeleteLabelReceiptRelByHeadId(Long receiptHeadId) {
        // 根据标签主键逻辑删除关联数据
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();

        wrapper.lambda().in(BizLabelReceiptRel::getReceiptHeadId, receiptHeadId);
        bizLabelReceiptRelDataWrap.physicalDelete(wrapper);
    }

    /**
     * 物理删除RFID标签关联数据
     *
     * @param receiptItemId 行项目id
     */
    public void physicalDeleteLabelReceiptRelByItemId(Long receiptItemId) {
        // 根据标签主键逻辑删除关联数据
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();

        wrapper.lambda().in(BizLabelReceiptRel::getReceiptItemId, receiptItemId);
        bizLabelReceiptRelDataWrap.physicalDelete(wrapper);
    }
    /**
     * 物理删除RFID标签关联数据
     *
     * @param receiptHeadId 单据id
     */
    public void physicalDeleteLabelReceiptRelByHeadIdAndStatus(Long receiptHeadId, Integer status) {
        // 根据标签主键逻辑删除关联数据
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(BizLabelReceiptRel::getReceiptHeadId, receiptHeadId)
                .eq(Objects.nonNull(status), BizLabelReceiptRel::getStatus, status);
        bizLabelReceiptRelDataWrap.physicalDelete(wrapper);
    }

    /**
     * 查询单据的关联信息
     *
     * @param receiptHeadId
     * @return
     */
    public List<BizLabelReceiptRel> findByReceiptItemId(Long receiptHeadId, Long itemId) {
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizLabelReceiptRel::getReceiptHeadId, receiptHeadId).eq(BizLabelReceiptRel::getReceiptItemId, itemId);
        List<BizLabelReceiptRel> labelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        return labelReceiptRelList;
    }

    /**
     * 查询标签的关联信息
     *
     * @param labelIdList
     * @return
     */
    public List<BizLabelReceiptRel> findByLabelIds(List<Long> labelIdList) {
        QueryWrapper<BizLabelReceiptRel> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(BizLabelReceiptRel::getLabelId, labelIdList);
        List<BizLabelReceiptRel> labelReceiptRelList = bizLabelReceiptRelDataWrap.list(wrapper);
        return labelReceiptRelList;
    }

    /**
     * 撤销关联关系
     * @param idList
     */
    public void rollbackByIdList(List<Long> idList) {
        bizLabelReceiptRelDataWrap.deleteByIdList(idList, EnumDeleteFlag.ROLLBACK.getIntValue());
    }
}
