package com.inossem.wms.bizbasis.stock.service.biz;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.inossem.wms.bizbasis.stock.service.datawrap.StockHistoryBatchDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockHistoryBinDataWrap;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.model.stock.dto.StockBatchDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockHistoryBatch;
import com.inossem.wms.common.model.stock.entity.StockHistoryBin;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;

/**
 * 存储库存历史记录 保存库存修改后的库存数据，用于收发存统计等功能
 * 
 * <AUTHOR>
 */
@Service
public class StockHistoryService {
    @Autowired
    protected StockHistoryBinDataWrap stockHistoryBinDataWrap;
    @Autowired
    protected StockHistoryBatchDataWrap stockHistoryBatchDataWrap;

    /**
     * 批次库存历史表
     * 
     * @param insDocId 凭证id
     * @param stockBatchList 批次库存list
     */
    public void saveStockHistoryBatch(Long insDocId, List<StockBatchDTO> stockBatchList) {
        List<StockHistoryBatch> historyList = new ArrayList<>();
        for (StockBatchDTO stockBatch : stockBatchList) {
            StockHistoryBatch shb = UtilBean.newInstance(stockBatch, StockHistoryBatch.class);
            shb.setInsDocBatchId(insDocId);
            historyList.add(shb);
        }
        if (UtilCollection.isNotEmpty(historyList)) {
            stockHistoryBatchDataWrap.saveOrUpdateBatch(historyList);
        }
    }

    /**
     * 仓位库存历史表
     * 
     * @param insDocId 凭证id
     * @param stockBinList 仓位库存list
     */
    public void saveStockHistoryBin(Long insDocId, List<StockBinDTO> stockBinList) {
        List<StockHistoryBin> historyList = new ArrayList<>();
        Set<String> excludeStorageTypeSet = new HashSet<>(UtilConst.getInstance().getDefaultStorageTypeCodeSet());
        excludeStorageTypeSet.remove(EnumDefaultStorageType.UNREAL.getTypeCode());
        for (StockBinDTO stockBin : stockBinList) {
            // 临时区的库存对于收发存来说没有业务上的数据含义
            // 不统计临时存储区的库存，只计算实际仓位和800存储区的库存
            if (excludeStorageTypeSet.contains(stockBin.getTypeCode())) {
                continue;
            }
            StockHistoryBin shb = UtilBean.newInstance(stockBin, StockHistoryBin.class);
            shb.setInsDocBinId(insDocId);
            historyList.add(shb);
        }
        if (UtilCollection.isNotEmpty(historyList)) {
            stockHistoryBinDataWrap.saveOrUpdateBatch(historyList);
        }
    }
}
