package com.inossem.wms.bizbasis.masterdata.material.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.masterdata.mat.info.entity.LogMaterialNetWeightRecord;
import com.inossem.wms.common.model.masterdata.mat.info.po.LogMaterialNetWeightRecordSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.LogMaterialNetWeightRecordVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-17
 */
public interface LogMaterialNetWeightRecordMapper extends WmsBaseMapper<LogMaterialNetWeightRecord> {

    /**
     * 物料净重变更记录-列表
     *
     * @param pageData     分页数据
     * @param pageWrapper  分页查询条件
     * @return List<LogMaterialNetWeightRecordVO>
     */
    List<LogMaterialNetWeightRecordVO> getLogMaterialNetWeightRecordList(IPage<LogMaterialNetWeightRecordVO> pageData,
                                                                         @Param(Constants.WRAPPER) WmsQueryWrapper<LogMaterialNetWeightRecordSearchPO> pageWrapper);
}
