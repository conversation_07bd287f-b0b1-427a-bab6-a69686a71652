package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 仓储管理-退转库领料申请同部门审批流(维修部)-监听器
 **/
@Service
public class ListenerTransferReturnMaterialApplySameDeptOther extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_TRANSFERR_ETURN_MATERIAL_OUT_APPLY);//审批回调-退转库领料申请
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDept = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");
        // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
        Set<String> alreadySendUserSet = new HashSet<>();
        String mtdCode = EnumDept.MTD.getCode();
        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人，发起人所属部门四级审批人
            boolean deptCheck = false;
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门的四级审批人
                String deptCode = deptOfficePO.getDeptCode();
                if (mtdCode.equals(deptCode)) {
                    deptCheck = true;
                    break;
                }
            }
            if (!deptCheck)
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(mtdCode, null, EnumApprovalLevel.LEVEL_4);
            userList.removeAll(alreadySendUserSet);
            addApproveUser(delegateTask, userList);
            alreadySendUserSet.addAll(userList);
        }
    }

}
