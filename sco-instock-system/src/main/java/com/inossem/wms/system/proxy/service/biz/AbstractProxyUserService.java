package com.inossem.wms.system.proxy.service.biz;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.job.po.SysJobLogSearchPO;
import com.inossem.wms.common.model.job.vo.SysJobLogPageVO;
import com.inossem.wms.system.proxy.dao.ProxyUserMapper;
import com.inossem.wms.system.proxy.service.datawrap.ProxyUserDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.proxy.dto.ProxyUserDTO;
import com.inossem.wms.common.model.proxy.entity.ProxyUser;
import com.inossem.wms.common.model.proxy.po.ProxyUserSearchPO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


/**
* @Author: SCDM-Tools-constructor
* @Date:   2023-07-14
*/
@Slf4j
public abstract class AbstractProxyUserService {


    @Autowired
    protected ProxyUserDataWrap proxyUserDataWrap;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected DataFillService dataFillService;

    /**
    *
    * 获取数据 - 列表
    *
    */
    public List<ProxyUser> search(ProxyUserSearchPO po) {

        bizRebuildFilter(po);

//        String conditions = QueryRelGenerator.initQueryWrapper(po);
//
//
//        return ProxyUserMapper.search(conditions);
        return null;
    }

    /**
     * 用于重写，提供构建过滤条件的方法
     * @param po
    */
    protected void bizRebuildFilter(ProxyUserSearchPO po) {

    }

    /**
    *
    * 获取数据 - 分页列表
    *
    */
    public PageObjectVO<ProxyUserDTO> getPage(ProxyUserSearchPO po){

        QueryWrapper<ProxyUserSearchPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getDelUserName()), ProxyUserSearchPO::getDelUserName, po.getDelUserName())
                .eq(UtilString.isNotNullOrEmpty(po.getCreateUserName()), ProxyUserSearchPO::getCreateUserName, po.getCreateUserName());

        IPage<ProxyUserDTO> page = po.getPageObj(ProxyUserDTO.class);

        proxyUserDataWrap.getProxyUserPageVOList(page,wrapper);


        long totalCount = page.getTotal();
        List<ProxyUserDTO> updateList = new ArrayList<>();
        for (ProxyUserDTO record : page.getRecords()) {
            if (EnumReceiptStatus.RECEIPT_STATUS_CAN.getValue().equals(record.getReceiptStatus())&&new Date().after(record.getEndTime())){
                record.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_OVER.getValue());
                updateList.add(record);
            }
        }
        if (UtilCollection.isNotEmpty(updateList)){
            proxyUserDataWrap.updateBatchDtoById(updateList);
        }
        dataFillService.fillAttr(page.getRecords());
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }
    /**
    * 新建时设置单据数据
    * @param po
    */
    protected void bizDateInsertSetter(ProxyUserDTO po) {
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CAN.getValue());
    }
    /**
    * 保存时校验单据信息数据
    * @param po
    */
    protected void bizDateSaveChecker(ProxyUserDTO po) {
    }

    /**
    * 新增或修改方法
    */
    @Transactional
    public void addOrUpdate(BizContext ctx) {
        ProxyUserDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.addOrUpdate(po,ctx.getCurrentUser());
    }

    /**
    * 新增或修改方法
    */
    @Transactional
    public void addOrUpdate(ProxyUserDTO po, CurrentUser currentUser) {
        log.info("新增/修改信息 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        bizDateSaveChecker(po);
        //委派人是否是其他人的代理人
        QueryWrapper<ProxyUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ne(ProxyUser::getId,po.getId()).eq(ProxyUser::getDelegateUserId,po.getClientUserId()).eq(ProxyUser::getReceiptStatus,EnumReceiptStatus.RECEIPT_STATUS_CAN.getValue());
        ProxyUser clientProxyUser = proxyUserDataWrap.getOne(queryWrapper);

        //代理人是否为其他的委派人
        QueryWrapper<ProxyUser> deQueryWrapper = new QueryWrapper<>();
        deQueryWrapper.lambda().ne(ProxyUser::getId,po.getId()).eq(ProxyUser::getClientUserId,po.getDelegateUserId()).eq(ProxyUser::getReceiptStatus,EnumReceiptStatus.RECEIPT_STATUS_CAN.getValue());
        ProxyUser deProxyUser = proxyUserDataWrap.getOne(deQueryWrapper);

        //委派人是否已经有代理任务
        QueryWrapper<ProxyUser> clientQueryWrapper = new QueryWrapper<>();
        clientQueryWrapper.lambda().eq(ProxyUser::getClientUserId,po.getClientUserId()).eq(ProxyUser::getReceiptStatus,EnumReceiptStatus.RECEIPT_STATUS_CAN.getValue());
        ProxyUser clientInfo = proxyUserDataWrap.getOne(clientQueryWrapper);

        //如果存在被委托人，已经委托别人的情况 或 委托人已经被委托
        if (UtilObject.isNotEmpty(deProxyUser)||UtilObject.isNotEmpty(clientProxyUser)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_PROXYED);
        }
        if (UtilObject.isNotEmpty(clientInfo)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_PROXYED_CAN);
        }
        //新增
        if(UtilNumber.isEmpty(po.getId())){
            String receiptCode = bizCommonService.getNextSequenceValue("proxy");
            po.setReceiptCode(receiptCode);
            po.setCreateUserId(currentUser.getId());
            po.setCreateTime(new Date());
            po.setModifyUserId(currentUser.getId());
            po.setModifyTime(new Date());
            po.setCreateUserCode(currentUser.getUserCode());
            po.setCreateUserName(currentUser.getUserName());
            po.setModifyUserCode(currentUser.getUserCode());
            po.setModifyUserName(currentUser.getUserName());
            //设置单据编码或者其他业务字段
            bizDateInsertSetter(po);
        }else{//修改
            if (UtilDate.checkDate(new Date(),po.getStartTime(),po.getEndTime())||new Date().before(po.getStartTime())){
                po.setIsEnable(1);
                po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CAN.getValue());
            }
            po.setModifyUserId(currentUser.getId());
            po.setModifyTime(new Date());
            po.setModifyUserCode(currentUser.getUserCode());
            po.setModifyUserName(currentUser.getUserName());
        }
        proxyUserDataWrap.saveOrUpdateDto(po);
    }
    /**
     * 单据删除前校验
     */
    protected void bizCheckBeforeRemove(ProxyUser info){

    }

    /**
    * 删除方法
    *
    * @param proxyUserId ID
    *
    */
    @Transactional
    public void remove(Long proxyUserId) {
        proxyUserDataWrap.removeById(proxyUserId);
    }

    /**
     * 中止
     *
     */
    @Transactional
    public void stop(Long proxyUserId) {
        UpdateWrapper<ProxyUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ProxyUser::getId,proxyUserId)
                .set(ProxyUser::getIsEnable,0)
                .set(ProxyUser::getStopTime,new Date())
                .set(ProxyUser::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_STOP.getValue());
        proxyUserDataWrap.update(updateWrapper);
    }


    /**
    * EXCEL导入
    */
//    @Transactional
//    public void importProxyUser(MultipartFile file, CurrentUser user) {
//
//    //主键集合
//    Set<String> receiptCodeSet = new HashSet<>();
//
//    try {
//        //获取EXCEL数据
//        List<ProxyUserImportPO> ProxyUserList = EasyExcel.read(file.getInputStream(), ProxyUserImportPO.class, new AnalysisEventListener() {
//            @Override
//            public void invoke(Object data, AnalysisContext context) {
//                ProxyUserImportPO  lineData = (ProxyUserImportPO) data;
//                receiptCodeSet.add(String.valueOf(lineData.getReceiptCode()));
//            }
//            @Override
//            public void doAfterAllAnalysed(AnalysisContext context) {
//                log.info("excel解析完成,开始处理数据");
//            }
//        }).sheet().doReadSync();
//
//        //判断EXCEL中主键重复的值
//        Map<String, List<ProxyUserImportPO>> checkMap = ProxyUserList.stream().collect(Collectors.groupingBy(item ->  item.getReceiptCode()  ));
//            for (String key : checkMap.keySet()) {
//            List<ProxyUserImportPO> checkList = checkMap.get(key);
//                if(checkList.size() > 1){
//                throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE,key);
//            }
//        }
//
//
//        //查询数据库中原有数据
//        QueryWrapper<ProxyUser> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().in(ProxyUser::getReceiptCode,receiptCodeSet).select(ProxyUser::getId,ProxyUser::getReceiptCode);
//        List<ProxyUser> list = proxyUserDataWrap.list(queryWrapper);
//        Map<String, Long> finalProxyUserMap = list.stream().collect(Collectors.toMap(u->u.getReceiptCode(), ProxyUser::getId));
//
//        List<ProxyUser> ProxyUserListInsert = new ArrayList<>();
//        List<ProxyUser> ProxyUserListUpdate = new ArrayList<>();
//        List<ProxyUserImportPO> successData = ProxyUserList.stream().filter(u -> UtilString.isNullOrEmpty(u.getErrorMsg())).collect(Collectors.toList());
//        successData.forEach(
//            ProxyUser -> {
//                //已存在
//                ProxyUser ProxyUserEntity = UtilBean.newInstance(ProxyUser, ProxyUser.class);
//                ProxyUserEntity.setModifyUserId(user.getId());
//                ProxyUserEntity.setModifyTime(new Date());
//                ProxyUserEntity.setModifyUserCode(user.getUserCode());
//                ProxyUserEntity.setModifyUserName(user.getUserName());
//                String key = ProxyUser.getReceiptCode();
//                if(finalProxyUserMap.containsKey(key)){
//                    ProxyUserEntity.setId(finalProxyUserMap.get(key));
//                    ProxyUserListUpdate.add(ProxyUserEntity);
//                }else{
//                    ProxyUserEntity.setCreateUserId(user.getId());
//                    ProxyUserEntity.setCreateTime(new Date());
//                    ProxyUserEntity.setCreateUserCode(user.getUserCode());
//                    ProxyUserEntity.setCreateUserName(user.getUserName());
//                    ProxyUserListInsert.add(ProxyUserEntity);
//                }
//                ProxyUser.setIsProcessed(true);
//            }
//        );
//        //批量插入数据
//        proxyUserDataWrap.updateBatchDtoById(ProxyUserListUpdate);
//        proxyUserDataWrap.saveBatchDto(ProxyUserListInsert);
//
//        List<ProxyUserImportPO> ProxyUserErrorList = ProxyUserList.stream().filter(u->UtilString.isNotNullOrEmpty(u.getErrorMsg())).collect(Collectors.toList());
//            //推送导入结果
//            exportImportResult(user,ProxyUserErrorList);
//
//        } catch (IOException e) {
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
//        }
//    }
//    /**
//    * 推送导入结果到待下载列表
//    * @param user
//    * @param importResult
//    */
//    public void exportImportResult(CurrentUser user,List<ProxyUserImportPO> importResult){
//        BizCommonFile bizCommonFile = new BizCommonFile();
//        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
//        bizCommonFile.setFileCode(String.format("%s.%s", uuid, Const.XLSX));
//        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
//        bizCommonFile.setFileName("导入结果" + "-" + yyyyMmDd);
//        bizCommonFile.setFileExt(Const.XLSX);
//        bizCommonFile.setCreateUserId(user.getId());
//        bizCommonFile.setModifyUserId(user.getId());
//        // 推送MQ生成可下载文件数据
//        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
//        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
//
//
//        UtilExcel.writeExcel(ProxyUserImportPO.class,importResult,bizCommonFile);
//
//        // 推送MQ生成可下载文件数据
//        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
//        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
//    }

    /**
    * 获取详情
    *
    * @param ProxyUserId id
    * @return 详情
    *
    */
    public BizResultVO<ProxyUserDTO> get(Long ProxyUserId) {
        log.info("详情查询 ProxyUserId：{}", ProxyUserId);
        if (UtilNumber.isEmpty(ProxyUserId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        ProxyUser ProxyUser = proxyUserDataWrap.getById(ProxyUserId);
        log.info("id：{}，详情：{}", ProxyUserId, JSONObject.toJSONString(ProxyUser));
        ProxyUserDTO dto = UtilBean.newInstance(ProxyUser, ProxyUserDTO.class);
        // 数据填充
        dataFillService.fillAttr(dto);
        // 设置按钮组权限
        ButtonVO buttonVO = this.bizSetButton(dto);
        return new BizResultVO(dto,new ExtendVO().setAttachmentRequired(true).setOperationLogRequired(true).setRelationRequired(true),buttonVO);
    }
    /**
    * 设置按钮权限
    * @param dto
    * @return
    */
    protected ButtonVO bizSetButton(ProxyUserDTO dto) {
        return new ButtonVO();
    }

//    /**
//    * 导出
//    */
//    public List<ProxyUserExportVO> export(ProxyUserSearchPO po) {
//        List<ProxyUser> list = search(po);
//        List<ProxyUserExportVO> exportList = UtilCollection.toList(list, ProxyUserExportVO.class);
//        dataFillService.fillAttr(exportList);
//        return exportList;
//    }


}