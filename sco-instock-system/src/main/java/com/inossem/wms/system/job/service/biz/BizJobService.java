package com.inossem.wms.system.job.service.biz;

import com.inossem.wms.bizbasis.common.dao.SequenceMapper;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.erp.service.biz.ErpWbsService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.util.UtilSpring;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * 石岛湾定时任务Service类
 *
 * <AUTHOR>
 * @date 2022/06/10 15:09
 **/
@Service
@Slf4j
public class BizJobService {

    /**
     * 测试定时任务
     */
    public void testJob() {
        log.info("------------------------------测试定时任务执行开始---------------------------------------");
        System.out.println("------------------------------测试定时任务---------------------------------------");
        log.info("------------------------------测试定时任务执行完毕---------------------------------------");
    }

    /**
     * 同步物料主数据
     */
    public void syncMaterial() {
        log.info("执行定时任务-------->每天23点55分执行。开始同步物料主数据……………………………………");
        try {
            SapInterfaceService sapInterfaceService = UtilSpring.getBean("sapInterfaceService");
            DictionaryService dictionaryService = UtilSpring.getBean("dictionaryService");
            Collection<DicFactoryDTO> factories = dictionaryService.getAllFtyCache();
            factories.forEach(dicFactoryDTO -> {
                String ftyCode = dicFactoryDTO.getFtyCode();
                log.info("同步工厂：{}的物料主数据。", ftyCode);
                sapInterfaceService.synMaterialInfoNew(ftyCode, null, null, null);
            });


        } catch (Exception e) {
            log.error("定时任务：物料主数据同步失败。", e);
            e.printStackTrace();
        }
        log.info("定时任务执行完毕，物料主数据同步完成。");
    }

    /**
     * 物项返运交接单重置流水号
     */
    public void syncDeliveryNum() {
        String seqCode = "delivery_num";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：物项返运交接单重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }


    /**
     * 产生记录的编号重置流水号
     */
    public void syncGenerateRecord() {
        String seqCode = "generate_record";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：产生记录的编号重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }


    /**
     * 物项维护保养记录编码重置流水号
     */
    public void syncMaintenanceRecord() {
        String seqCode = "maintenance_record";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：物项维护保养记录编码重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }

    /**
     * 物项特殊维护保养要求编码重置流水号
     */
    public void syncSpecialMaintenance() {
        String seqCode = "special_maintenance";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：物项特殊维护保养要求编码重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }

    /**
     * 物项暂存申请单编码重置流水号
     */
    public void syncItemStorageApply() {
        String seqCode = "item_storage_apply";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：物项暂存申请单编码重置流水号执行失败。", e);
            e.printStackTrace();
        }
        // 同时重置成套设备暂存申请单编码
        seqCode = "unitized_temp_input_req";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：成套设备物项暂存申请单编码重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }

    /**
     * 暂存物项领用单编码重置流水号
     */
    public void syncTemporaryItemReq() {
        String seqCode = "temporary_item_req";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：暂存物项领用单编码重置流水号执行失败。", e);
            e.printStackTrace();
        }
        // 同时重置成套设备暂存领用单编码
        seqCode = "unitized_temp_output_req";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：成套设备物项暂存领用单编码重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }

    /**
     * 领料申请的编号重置流水号
     */
    public void syncMaterialReq() {
        String seqCode = "material_req";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：领料申请的编号重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }


    /**
     * 领料出库单的编号重置流水号
     */
    public void syncMaterialOut() {
        String seqCode = "material_out";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：领料出库单的编号重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }

    /**
     * 质检会签单的编号重置流水号
     */
    public void syncInspectOrder() {
        String seqCode = "inspect_order";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：质检会签单的编号重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }

    /**
     * 验收入库单编号重置流水号
     */
    public void syncInspectInput() {
        String seqCode = "inspect_input";
        try {
            SequenceMapper sequenceMapper = UtilSpring.getBean("sequenceMapper");
            sequenceMapper.resetCurValBySeqCode(seqCode, 0);
        } catch (Exception e) {
            log.error("定时任务：验收入库单编号重置流水号执行失败。", e);
            e.printStackTrace();
        }
    }

    /**
     * 同步WBS
     */
    public void syncWBS() {
        log.info("执行定时任务-------->每天05点00分执行。开始同步WBS……………………………………");
        try {
            ErpWbsService erpWbsService = UtilSpring.getBean("erpWbsService");
            erpWbsService.syncWBS(null, null);
            log.info("定时任务执行完毕，WBS同步完成。");
        } catch (Exception e) {
            log.error("定时任务：WBS同步失败。", e);
        }
    }


    /**
     * 计算 仓位库存是否超重
     */
    public void calculateStockBinOverWeight() {
        log.info("执行定时任务-------->每天23点55分执行。计算 仓位库存是否超重……………………………………");
        try {
            SapInterfaceService sapInterfaceService = UtilSpring.getBean("sapInterfaceService");
            DictionaryService dictionaryService = UtilSpring.getBean("dictionaryService");
            Collection<DicFactoryDTO> factories = dictionaryService.getAllFtyCache();
            factories.forEach(dicFactoryDTO -> {
                String ftyCode = dicFactoryDTO.getFtyCode();
                log.info("同步工厂：{}的物料主数据。", ftyCode);
                sapInterfaceService.synMaterialInfoNew(ftyCode, null, null, null);
            });


        } catch (Exception e) {
            log.error("定时任务：物料主数据同步失败。", e);
            e.printStackTrace();
        }
        log.info("定时任务执行完毕，物料主数据同步完成。");
    }
}
