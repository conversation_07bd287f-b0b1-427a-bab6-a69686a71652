package com.inossem.wms.system.announcement.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.notice.dto.DicNoticeDTO;
import com.inossem.wms.common.model.notice.po.DicNoticeSavePO;
import com.inossem.wms.common.model.notice.po.DicNoticeSearchPO;
import com.inossem.wms.system.announcement.service.biz.NoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 公告 Controller
 * 
 * <AUTHOR>
 */
@RestController
@Api(tags = "公告管理")
public class NoticeController {

    @Autowired
    private NoticeService noticeService;

    /**
     * 获取公告列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 公告集合列表
     */
    @ApiOperation(value = "获取公告列表", tags = {"公告管理"})
    @PostMapping(path = "/notice/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicNoticeDTO>> list(@RequestBody DicNoticeSearchPO po, BizContext ctx) {
        return BaseResult.success(noticeService.getList(ctx));
    }

    /**
     * 查看公告详情
     *
     * <AUTHOR>
     * @param id 公告Id
     * @return 公告详情
     */
    @ApiOperation(value = "按照公告编码查找公告", tags = {"公告管理"})
    @GetMapping(path = "/notice/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicNoticeDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(noticeService.get(ctx));
    }

    /**
     * 新增公告
     *
     * <AUTHOR>
     * @param po 公告入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增公告信息", notes = "对公告信息进行添加", tags = {"公告管理"})
    @PostMapping(path = "/notice", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicNoticeSavePO po, BizContext ctx) {
        // 储存存地点信息
        noticeService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_NOTICE_SAVE_SUCCESS);
    }

    /**
     * 修改公告
     *
     * <AUTHOR>
     * @param po 公告入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改公告信息", notes = "对公告信息进行修改", tags = {"公告管理"})
    @PutMapping(path = "/notice", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicNoticeSavePO po, BizContext ctx) {
        // 储存存地点信息
        noticeService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_NOTICE_SAVE_SUCCESS);
    }

    /**
     * 删除公告
     *
     * @param id 公告Id
     * <AUTHOR>
     * @param ctx 上下文对象
     * @return 删除结果
     */
    @ApiOperation(value = "按照公告编码删除公告", notes = "逻辑删除", tags = {"公告管理"})
    @DeleteMapping(path = "/notice/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除公告信息
        noticeService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_NOTICE_DELETE_SUCCESS);
    }
}
