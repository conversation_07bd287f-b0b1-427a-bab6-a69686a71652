package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 仓储管理-需求管理-内部-监听器
 *
 * <AUTHOR>
 **/
@Service
public class ListenerUnitizedRequireInside extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_REQUIRE);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDept = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");
        // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
        Set<String> alreadySendUserSet = new HashSet<>();
        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 发起人所属部门3级审批人-物资
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门的四级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
                // 排除掉已经发过待办信息的用户
                userList.removeAll(alreadySendUserSet);
                addApproveUser(delegateTask, userList);
                alreadySendUserSet.addAll(userList);
            }
        }
    }
}
