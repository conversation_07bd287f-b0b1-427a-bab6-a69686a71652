package com.inossem.wms.system.auth.controller;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.common.model.auth.user.po.SsoUserKey;
import com.inossem.wms.common.model.auth.user.vo.SysLoginVO;
import com.inossem.wms.common.model.auth.user.vo.WxSign;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.util.UtilWx;
import com.inossem.wms.system.auth.service.biz.AuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static com.inossem.wms.common.model.common.base.BaseResult.success;

/**
 * 权限校验
 *
 * <AUTHOR> 2021/3/11 16:54
 */
@Slf4j
@Api(tags = "权限管理")
@RestController
public class AuthController {

    @Autowired
    private AuthService authService;
    @Autowired
    private UtilWx utilWx;

    @PostMapping("/auth/token")
    @ApiOperation(value = "token续期", tags = {"权限管理"})
    public BaseResult<SysLoginVO> refreshToken(HttpServletRequest request) {
        String accessToken = authService.refreshToken(request);
        return success(new SysLoginVO(accessToken, "", null, null));
    }

    private final static String USER_ATTRNAME = "userName";
    private static final String ENCODE = "UTF-8";
    @SneakyThrows
    @PostMapping("/UMS/login.sso")
    @ApiOperation(value = "login", tags = {"sso"})
    public BaseResult<SysLoginVO>  ssoLogin(@RequestBody SsoUserKey key, HttpServletRequest request) {
        //get userName
        String loginUser=new String(Base64.decodeBase64(key.getUserName()),ENCODE);
        //set session
        if(!loginUser.equals(request.getSession().getAttribute(USER_ATTRNAME))){
            request.getSession().invalidate();
        }
        request.getSession(true).setAttribute(USER_ATTRNAME,loginUser);
        // return token
        return  success(new SysLoginVO(authService.createTokenBySysUser(request, authService.getUMSUser(loginUser)), "", null, null));
    }


    /**
     * 创建真实token
     * @param key
     * @param request
     * @return
     */
    @SneakyThrows
    @PostMapping("/sso/createToken")
    @ApiOperation(value = "createToken", tags = {"sso"})
    public BaseResult<SysLoginVO>  createToken(@RequestBody SsoUserKey key, HttpServletRequest request) {
        authService.delLoginToken(key,request);
        //get userName
        String loginUser=key.getUserName();
        //set session
        if(!loginUser.equals(request.getSession().getAttribute(USER_ATTRNAME))){
            request.getSession().invalidate();
        }
        request.getSession(true).setAttribute(USER_ATTRNAME,loginUser);
        // return token
        return  success(new SysLoginVO(authService.createTokenBySysUser(request, authService.getUMSUser(loginUser)), "", null, null));
    }

    /**
     * 校验token
     * @param key
     * @param request
     * @return
     */
    @SneakyThrows
    @PostMapping("/sso/checkLoginToken")
    @ApiOperation(value = "checkLoginToken", tags = {"sso"})
    public BaseResult<SsoUserKey>  checkLoginToken(@RequestBody SsoUserKey key, HttpServletRequest request) {
        return  success( authService.checkLoginToken(key,request));
    }
    /**
     * ihn认证集成
     * @param code 用户唯一身份编码，需调用ihn接口取用户真实编码
     * @param request
     * @return
     */
    @PostMapping("/ihn/login/{code}")
    public BaseResult<SysLoginVO> ihnLogin(@PathVariable("code") String code, HttpServletRequest request) {
        log.info("current login code: {}", code);
        String userCode = utilWx.getUserCode(code);
        request.getSession(true).setAttribute(USER_ATTRNAME,userCode);
        return  success(new SysLoginVO(authService.createTokenBySysUser(request, authService.getUserByUserCode(userCode)), "", null, null));
    }

    /**
     * 获取ihn授权签名
     * @param param
     * @param request
     * @return
     */
    @PostMapping("/ihn/getSign")
    public BaseResult<WxSign> getWxSign(@RequestBody JSONObject param, HttpServletRequest request) {
        WxSign result = new WxSign();
        String url = param.getString("url");
        if (StringUtils.isNotEmpty(url)) {
            result = authService.get_wx_ticket(url);
        }
        return success(result);
    }
}
