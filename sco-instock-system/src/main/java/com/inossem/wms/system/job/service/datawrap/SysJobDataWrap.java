package com.inossem.wms.system.job.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.job.entity.SysJob;
import com.inossem.wms.common.model.job.po.SysJobSearchPO;
import com.inossem.wms.common.model.job.vo.SysJobPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.system.job.dao.SysJobMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 定时任务调度表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-03
 */
@Service
public class SysJobDataWrap extends BaseDataWrap<SysJobMapper, SysJob> {

    /**
     * 定时任务控制列表分页
     *
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<SysJobPageVO> getSysJobPageVOList(IPage<SysJobPageVO> page, QueryWrapper<SysJobSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectSysJobPageVOList(page, wrapper));
    }
}
