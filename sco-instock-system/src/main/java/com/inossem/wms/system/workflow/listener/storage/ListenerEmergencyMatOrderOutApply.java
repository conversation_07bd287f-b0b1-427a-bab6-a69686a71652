package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/23 16:08
 * @desc 紧急领用出库审批监听器
 */
@Service
public class ListenerEmergencyMatOrderOutApply extends ApprovalListener implements TaskListener, ExecutionListener {

    private static final long serialVersionUID = 8779373051046089306L;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_EMERGENCY_MAT_ORDER_OUT_APPLY);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<String> userCodeList = (List<String>) delegateTask.getVariable("userCode");
        if (UtilCollection.isEmpty(userCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批人关联用户在基础信息中选择的审批人
            addApproveUser(delegateTask, userCodeList);
        }
    }
}
