package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 仓储管理-不符合项处置审批流-监听器
 *
 * <AUTHOR>
 * @date 2022/05/03 15:46
 **/
@Service
public class ListenerUnitizedNonItemMaintain extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_CALLBACK_UNITIZED_INCONFORMITY_MAINTAIN);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人，商务合同部-物项合同科-二级审批人
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD, EnumOffice.CCD03, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userCodeList);
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点动态配置审批人，商务合同部-设备仓储科-二级审批人
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD, EnumOffice.CCD05, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userCodeList);
        }
    }

}
