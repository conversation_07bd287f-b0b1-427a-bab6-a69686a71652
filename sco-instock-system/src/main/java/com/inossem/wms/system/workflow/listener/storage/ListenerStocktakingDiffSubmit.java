package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 仓储管理-盘点完成差异提交审批流-监听器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-06-12 08:36
 **/
@Service
public class ListenerStocktakingDiffSubmit extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    /**
     * 盘点差异提交审批流程结束回调
     */
    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_STOCKTAKING_DIFF_SUBMIT);
    }

    /**
     * 盘点差异提交审批流程，任务节点回调
     * 1. 财务部 - 成本科        【科室负责人】审批
     * 2. 商务合同部 - 设备仓储科 【科室负责人】审批
     * 3. 财务部 - 成本科        【科室负责人】审批
     * 4. 商务合同部             【部门负责人】审批
     * 5. 财务部                 【部门负责人】审批
     * 6. 财务部&商务部          【分管领导】会签
     *
     */
    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();

        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批：财务部 - 成本科 【科室负责人】审批
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FID, EnumOffice.FIDC, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userCodeList);
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点 商务合同部 - 设备仓储科 【科室负责人】审批
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD, EnumOffice.CCD05, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userCodeList);

        } else if (EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 三级审批节点 财务部 - 成本科 【科室负责人】审批
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FID, EnumOffice.FIDC, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userCodeList);

        } else if (EnumApprovalNode.LEVEL_4_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 四级审批节点 商务合同部 【部门负责人】审批
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD.getCode(), null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userCodeList);
        } else if (EnumApprovalNode.LEVEL_5_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 五级审批节点 财务部 【部门负责人】审批
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FID.getCode(), null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userCodeList);
        } else if (EnumApprovalNode.LEVEL_6_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 六级审批节点 财务部&商务部 【分管领导】会签
            List<String> ccdUserCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD.getCode(), null, EnumApprovalLevel.LEVEL_5);
            List<String> fidUserCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FID.getCode(), null, EnumApprovalLevel.LEVEL_5);
            Set<String> userCodeList = Stream.concat(ccdUserCodeList.stream(), fidUserCodeList.stream()).collect(Collectors.toSet());
            delegateTask.addCandidateUsers(userCodeList);
        }
    }

}
