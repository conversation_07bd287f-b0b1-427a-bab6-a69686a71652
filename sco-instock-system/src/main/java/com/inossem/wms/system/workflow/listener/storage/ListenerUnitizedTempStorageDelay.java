package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 仓储管理-成套暂存物项延期审批流-监听器
 */
@Service
public class ListenerUnitizedTempStorageDelay extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    /**
     * 成套暂存物项延期审批流程结束回调
     */
    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_UNITIZED_TS_DELAY);
    }

    /**
     * 成套暂存物项延期审批流程，任务节点回调
     */
    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDept = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");

        // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
        Set<String> alreadySendUserSet = new HashSet<>();

        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人，发起人所属部门负责人
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门的部门负责人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
                // 排除掉已经发过待办信息的用户
                alreadySendUserSet.addAll(userList);
            }
            addApproveUser(delegateTask, new ArrayList<>(alreadySendUserSet));

        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点动态配置审批人，工程部仓储管理人员 固定写死 唐娜 82000240 宋飞 82000247
            List<String> userList = Arrays.asList("82000240", "82000247");
            addApproveUser(delegateTask, userList);
        }
    }

}
