package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import java.util.HashSet;
import java.util.Set;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 仓储管理-成套领料申请外部部门审批流-监听器
 *
 **/
@Service
public class ListenerUnitizedMaterialApplyTransferOutside extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_CALLBACK_UNITIZED_MATERIAL_OUT_APPLY);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDept = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");

        // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
        Set<String> alreadySendUserSet = new HashSet<>();

        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人，发起人所属部门三级审批人
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门的三级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);

                // 排除掉已经发过待办信息的用户
                userList.removeAll(alreadySendUserSet);
                addApproveUser(delegateTask, userList);
                alreadySendUserSet.addAll(userList);

            }
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点动态配置审批人，对口部门对口科室的一级审批人
            String counterpartDeptCode = delegateTask.getVariable("counterpartDeptCode").toString();
            String counterpartOfficeCode = delegateTask.getVariable("counterpartOfficeCode").toString();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_1);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            String counterpartDeptCode = delegateTask.getVariable("counterpartDeptCode").toString();
            String counterpartOfficeCode = delegateTask.getVariable("counterpartOfficeCode").toString();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_4_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            String counterpartDeptCode = delegateTask.getVariable("counterpartDeptCode").toString();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_3);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_5_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            String counterpartDeptCode = delegateTask.getVariable("counterpartDeptCode").toString();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userList);
        }
    }

}
