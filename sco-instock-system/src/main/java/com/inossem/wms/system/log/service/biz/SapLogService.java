package com.inossem.wms.system.log.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumSapType;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.enums.ReceiptTypeMapVO;
import com.inossem.wms.common.model.log.dto.LogSapLogDTO;
import com.inossem.wms.common.model.log.entity.LogSapLog;
import com.inossem.wms.common.model.log.po.LogSearchPO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilGzip;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.log.service.datawrap.LogSapLogDataWrap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * SAP日志服务层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-01
 */
@Service
public class SapLogService {

    @Autowired
    LogSapLogDataWrap logSapLogDataWrap;

    public PageObjectVO<LogSapLogDTO> sapLogPageList(LogSearchPO po) {

        // 分页查询部分
        Page<LogSapLog> page = new Page<>(po.getPageIndex(), po.getPageSize());
        QueryWrapper<LogSapLog> queryWrapper = new QueryWrapper<>();
        // 判断日期是否为空
        if (UtilObject.isNotEmpty(po.getCreateDate()) && UtilObject.isNotEmpty(po.getEndDate())) {
            // 取日期范围内
            queryWrapper.lambda().between(LogSapLog::getCreateDate, po.getCreateDate(), po.getEndDate());
        }
        // 业务类型
        queryWrapper.lambda().eq(UtilNumber.isNotNull(po.getReceiptType()),LogSapLog::getReceiptType,po.getReceiptType());
        // 接口类型
        queryWrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getInterfaceType()), LogSapLog::getInterfaceType,
                po.getInterfaceType());
        // 单据号
        queryWrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getReceiptCode()),LogSapLog::getReceiptCode,po.getReceiptCode());
        // SAP调用状态
        queryWrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getSapResult()),LogSapLog::getSapResult,po.getSapResult());
        // SAP返回信息
        queryWrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getReturnMsg()),LogSapLog::getReturnMsg,po.getReturnMsg());
        // 接口描述
        queryWrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getInterfaceDescribe()),LogSapLog::getInterfaceDescribe,po.getInterfaceDescribe());
        // 用户编码
        queryWrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getUserName()),LogSapLog::getUserName,po.getUserName());
        queryWrapper.lambda().orderByDesc(LogSapLog::getCreateDate).orderByDesc(LogSapLog::getCreateTime);
        Page<LogSapLog> pageList = logSapLogDataWrap.page(page, queryWrapper);
        List<LogSapLogDTO> list= UtilCollection.toList(pageList.getRecords(), LogSapLogDTO.class);
        PageObjectVO<LogSapLogDTO> pageObjectVo = new PageObjectVO<>(list, pageList.getTotal());

        // 解压过程(sap由于入参及出参信息过长，所以压缩后再存mysql中)
        for (LogSapLogDTO sapLogModel : pageObjectVo.getResultList()) {
            String inParam = UtilGzip.uncompresscompressStringToString(sapLogModel.getInParam());
            String outParam = UtilGzip.uncompresscompressStringToString(sapLogModel.getOutParam());
            String url=sapLogModel.getUrl();
            if(StringUtils.isNotEmpty(url)){
                String interfaceName= EnumSapType.getInterfaceName(url);
                sapLogModel.setInterfaceName(interfaceName);
            }
            if(StringUtils.isNotEmpty(sapLogModel.getInterfaceType())){
                sapLogModel.setReceiptType(Integer.valueOf(sapLogModel.getInterfaceType()));
            }
            sapLogModel.setInParam(inParam);
            sapLogModel.setOutParam(outParam);
        }
        return pageObjectVo;
    }

    /**
     * sap日志下拉列表
     *
     */
    public void getReceiptTypeList(BizContext ctx) {
        MultiResultVO<ReceiptTypeMapVO> vo = new MultiResultVO<>(EnumReceiptType.toSapLogReceiptList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }
}
