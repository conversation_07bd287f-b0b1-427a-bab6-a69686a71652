package com.inossem.wms.system.workflow.listener.supplier;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商审批流监听器
 *
 * <AUTHOR>
 */
@Service
public class ListenerSupplier extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_CALLBACK_SUPPLIER);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        String deptCode = (String) delegateTask.getVariable("deptCode");

        // 根据节点配置审批人
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点 - 经营管理部部长
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userList);

        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点 - 经营管理部分管领导
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
            addApproveUser(delegateTask, userList);
        }
    }
} 
