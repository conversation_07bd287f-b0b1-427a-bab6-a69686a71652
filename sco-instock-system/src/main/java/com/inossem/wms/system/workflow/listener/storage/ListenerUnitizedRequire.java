package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 仓储管理-需求管理-监听器
 *
 * <AUTHOR>
 **/
@Service
public class ListenerUnitizedRequire extends ApprovalListener implements TaskListener, ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_REQUIRE);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        String professionalEngineerUserCode = delegateTask.getVariable("professionalEngineerUserCode").toString();
        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人，专业工程师
            List<String> userCodeList = new ArrayList<>();
            userCodeList.add(professionalEngineerUserCode);
            addApproveUser(delegateTask, userCodeList);
        }
    }

}
