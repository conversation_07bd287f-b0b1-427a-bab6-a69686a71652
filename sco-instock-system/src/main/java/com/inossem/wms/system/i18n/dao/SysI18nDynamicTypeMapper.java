package com.inossem.wms.system.i18n.dao;

import com.inossem.wms.common.model.i18n.entity.SysI18nDynamicType;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * SysI18nDynamicType mapstruct 接口
 *
 * <AUTHOR>
 * @since 2024-10-08
 * @see <a href="https://mapstruct.org/">mapstruct</a>
 */
@Mapper
public interface SysI18nDynamicTypeMapper extends WmsBaseMapper<SysI18nDynamicType> {


}
