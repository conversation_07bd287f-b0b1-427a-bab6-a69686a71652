package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 成套设备-闲置物资申请审批流-监听器
 *
 * <AUTHOR>
 **/

@Service
public class ListenerUnitizedIdleSuppliesApply extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_CALLBACK_UNITIZED_IDLE_MATERIAL_APPLY);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDeptList = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");
        if (UtilCollection.isEmpty(userDeptList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
            Set<String> sendUserSet = new HashSet<>();
            // 一级审批节点动态配置审批人 【科室负责人】审批
            for (MetaDataDeptOfficePO deptOfficePO : userDeptList) {
                String deptCode = deptOfficePO.getDeptCode();
                String officeCode = deptOfficePO.getDeptOfficeCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_2);
                // 排除掉已经发过待办信息的用户
                sendUserSet.addAll(userList);
            }
            addApproveUser(delegateTask, new ArrayList<>(sendUserSet));
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
            Set<String> sendUserSet = new HashSet<>();
            // 一级审批节点动态配置审批人 【科室负责人】审批
            for (MetaDataDeptOfficePO deptOfficePO : userDeptList) {
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
                // 排除掉已经发过待办信息的用户
                sendUserSet.addAll(userList);
            }
            addApproveUser(delegateTask, new ArrayList<>(sendUserSet));
        }
    }

}
