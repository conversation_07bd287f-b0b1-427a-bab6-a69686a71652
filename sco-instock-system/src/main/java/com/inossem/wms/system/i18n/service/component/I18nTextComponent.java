package com.inossem.wms.system.i18n.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.cache.ICacheService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.i18n.entity.SysIl8nText;
import com.inossem.wms.common.model.i18n.po.SysIl8nTextPO;
import com.inossem.wms.common.model.i18n.po.SysIl8nTextSearchPO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.i18n.service.datawrap.SysI18nTextDataWrap;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * I18nTextComponent设计用于作为国际化代码服务层组件类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021/5/14
 */
@Slf4j
@Service
public class I18nTextComponent {

    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    private SysI18nTextDataWrap sysI18nTextDataWrap;
    @Autowired
    protected ICacheService cacheServiceImpl;

    private enum EnumServerPlatformType {
        BACK_END(1), FRONT_END(2), HAND_HOLD(3);
        @Setter
        @Getter
        private int value;
        EnumServerPlatformType(int value) {
            this.value = value;
        }
    }

    public void getListOnPaging(BizContext ctx) {

        SysIl8nTextSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        log.info("获取列表 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new SysIl8nTextSearchPO();
        }
        QueryWrapper<SysIl8nText> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getSourceValue()), SysIl8nText::getSourceValue, po.getSourceValue())
                .eq(UtilString.isNotNullOrEmpty(po.getSourceKey()), SysIl8nText::getSourceKey, po.getSourceKey())
                .eq(UtilString.isNotNullOrEmpty(po.getSourceGroup()), SysIl8nText::getSourceGroup, po.getSourceGroup())
                .like(UtilString.isNotNullOrEmpty(po.getText()), SysIl8nText::getText, po.getText());
        queryWrapper.orderBy(UtilString.isNotNullOrEmpty(po.getDescSortColumn()), false, UtilString.getSnakeList(po.getDescSortColumn().split(",")))
                .orderBy(UtilString.isNotNullOrEmpty(po.getAscSortColumn()), true, UtilString.getSnakeList(po.getAscSortColumn().split(",")));
        log.info("获取列表 wrapper：{}", JSONObject.toJSONString(queryWrapper));
        // 分页处理
        IPage<SysIl8nText> page = new Page<>(po.getPageIndex(), po.getPageSize());
        sysI18nTextDataWrap.page(page, queryWrapper);
        List<SysIl8nText> sysIl8nTextList = page.getRecords();
        long totalCount = page.getTotal();
        log.info("获取列表  list：{}", JSONObject.toJSONString(sysIl8nTextList));

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(sysIl8nTextList, totalCount));
    }

    public void getListByServerPlatform(BizContext ctx) {
        Integer po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        SysIl8nTextSearchPO languageSearchPo = new SysIl8nTextSearchPO();
        languageSearchPo.setServerPlatform(po);
        this.getList(ctx);
    }

    public void getList(BizContext ctx) {
        SysIl8nTextSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("获取列表 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new SysIl8nTextSearchPO();
        }
        if (po.getServerPlatform().equals(2)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(new ArrayList<>(dictionaryService.getAllTextWebLanguageCache())));
        } else if (po.getServerPlatform().equals(3)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(new ArrayList<>(dictionaryService.getAllTextPdaLanguageCache())));
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>());
    }

    public void addOrUpdate(BizContext ctx) {
        SysIl8nTextPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("新增/ po：{}", JSONObject.toJSONString(po));
        if (null == po.getSysIl8nText()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        SysIl8nText sysIl8nText = po.getSysIl8nText();

        sysI18nTextDataWrap.saveOrUpdate(sysIl8nText);

        this.refreshLanguageCache(sysIl8nText);
    }

    public void remove(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("删除 id ：{}", id);
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 删除
        if (sysI18nTextDataWrap.removeById(id)) {
            log.info(" {}，删除成功", id);
            this.deleteLanguageCacheById(id);
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_LANGUAGE_DELETE_FAILURE);
        }

    }


    /* ******************** 国际化主数据缓存 ************************ */

    public void initLanguageCache() {
        dictionaryService.initLanguageMap();
        cacheServiceImpl.delete(Const.CACHE_TEXT_PDA_MESSAGES);
        cacheServiceImpl.delete(Const.CACHE_TEXT_WEB_MESSAGES);
        cacheServiceImpl.delete(Const.CACHE_RETURN_CODE_MESSAGES);
        cacheServiceImpl.delete(Const.CACHE_NAME_MESSAGE);
        List<SysIl8nText> languageList = sysI18nTextDataWrap.list();
        if (UtilCollection.isNotEmpty(languageList)) {
            for (SysIl8nText model : languageList) {
                if (UtilString.isNullOrEmpty(model.getMessageType())) {
                    log.error("初始化国际化异常，国际化 {}{} 没有给出类型", model.getSourceKey(), model.getSourceValue());
                    continue;
                }
                switch (model.getMessageType()) {
                    case Const.CACHE_RETURN_CODE_MESSAGES:
                        cacheServiceImpl.put(Const.CACHE_RETURN_CODE_MESSAGES,
                                model.getLanguageType() + model.getSourceKey() + model.getSourceValue(), model);
                        break;
                    case Const.CACHE_NAME_MESSAGE:
                        cacheServiceImpl.put(Const.CACHE_NAME_MESSAGE, model.getLanguageType() + model.getSourceKey() + model.getSourceValue(),
                                model);
                        break;
                    case Const.CACHE_TEXT_MESSAGES:
                        if (model.getServerPlatform().equals(EnumServerPlatformType.FRONT_END.getValue())) {
                            cacheServiceImpl.put(Const.CACHE_TEXT_PDA_MESSAGES,
                                    model.getLanguageType() + model.getSourceKey() + model.getSourceValue(), model);
                        } else if (model.getServerPlatform().equals(EnumServerPlatformType.HAND_HOLD.getValue())) {
                            cacheServiceImpl.put(Const.CACHE_TEXT_WEB_MESSAGES,
                                    model.getLanguageType() + model.getSourceKey() + model.getSourceValue(), model);
                        }
                        break;
                    default:
                        break;
                }

            }
        }
    }

    public void refreshLanguageCache(SysIl8nText model) {
        if (model != null) {
            switch (model.getMessageType()) {
                case Const.CACHE_RETURN_CODE_MESSAGES:
                    cacheServiceImpl.put(Const.CACHE_RETURN_CODE_MESSAGES, model.getLanguageType() + model.getSourceKey() + model.getSourceValue(),
                            model);
                    break;
                case Const.CACHE_NAME_MESSAGE:
                    cacheServiceImpl.put(Const.CACHE_NAME_MESSAGE, model.getLanguageType() + model.getSourceKey() + model.getSourceValue(), model);
                    break;
                case Const.CACHE_TEXT_MESSAGES:
                    if (model.getServerPlatform().equals(EnumServerPlatformType.FRONT_END.getValue())) {
                        cacheServiceImpl.put(Const.CACHE_TEXT_PDA_MESSAGES, model.getLanguageType() + model.getSourceKey() + model.getSourceValue(),
                                model);
                    } else if (model.getServerPlatform().equals(EnumServerPlatformType.HAND_HOLD.getValue())) {
                        cacheServiceImpl.put(Const.CACHE_TEXT_WEB_MESSAGES, model.getLanguageType() + model.getSourceKey() + model.getSourceValue(),
                                model);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    public void deleteLanguageCacheById(Long id) {
        if (UtilNumber.isNotEmpty(id)) {
            SysIl8nText model = sysI18nTextDataWrap.getById(id);
            switch (model.getMessageType()) {
                case Const.CACHE_RETURN_CODE_MESSAGES:
                    cacheServiceImpl.delete(Const.CACHE_RETURN_CODE_MESSAGES,
                            model.getLanguageType() + model.getSourceKey() + model.getSourceValue());
                    break;
                case Const.CACHE_NAME_MESSAGE:
                    cacheServiceImpl.delete(Const.CACHE_NAME_MESSAGE, model.getLanguageType() + model.getSourceKey() + model.getSourceValue());
                    break;
                case Const.CACHE_TEXT_MESSAGES:
                    if (model.getServerPlatform().equals(EnumServerPlatformType.FRONT_END.getValue())) {
                        cacheServiceImpl.delete(Const.CACHE_TEXT_PDA_MESSAGES,
                                model.getLanguageType() + model.getSourceKey() + model.getSourceValue());
                    } else if (model.getServerPlatform().equals(EnumServerPlatformType.HAND_HOLD.getValue())) {
                        cacheServiceImpl.delete(Const.CACHE_TEXT_WEB_MESSAGES,
                                model.getLanguageType() + model.getSourceKey() + model.getSourceValue());
                    }
                    break;
                default:
                    break;
            }
        }
    }
}
