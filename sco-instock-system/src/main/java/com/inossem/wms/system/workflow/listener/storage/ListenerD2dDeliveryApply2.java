package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/16
 */
@Service
public class ListenerD2dDeliveryApply2 extends App<PERSON>alL<PERSON>ener implements TaskListener, ExecutionListener {
    private static final long serialVersionUID = -7890948266533404013L;

    @Override
    public void notify(DelegateExecution execution) {
        approvalCallback(execution, TagConst.APPROVAL_D2D_DELIVERY_APPLY);
    }

    @Override
    public void notify(DelegateTask delegateTask) {

        // 如果是沟通任务，则直接返回
        if(this.isCommunicateTask(delegateTask)){
            return;
        }

        // 如果是转办任务，则直接返回
        if(this.isTransferTask(delegateTask)){
            return;
        }

        // 如果跳转审批节点，则直接返回
        if(this.jumpApprovalNode(delegateTask)){
            return;
        }

        String taskDefKey = delegateTask.getTaskDefinitionKey();
        String level1ApproveUserCode = (String) delegateTask.getVariable("level1ApproveUserCode");
        String level2ApproveUserCode = (String) delegateTask.getVariable("level2ApproveUserCode");
        String level3ApproveUserCode = (String) delegateTask.getVariable("level3ApproveUserCode");
        String level4ApproveUserCode = (String) delegateTask.getVariable("level4ApproveUserCode");
        String level5ApproveUserCode = (String) delegateTask.getVariable("level5ApproveUserCode");
        // 根据节点配置审批人
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点
            addApproveUser(delegateTask, Collections.singletonList(level1ApproveUserCode));
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点
            addApproveUser(delegateTask, Collections.singletonList(level2ApproveUserCode));
        } else if (EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 三级审批节点
            addApproveUser(delegateTask, Collections.singletonList(level3ApproveUserCode));
        } else if (EnumApprovalNode.LEVEL_4_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 四级审批节点
            addApproveUser(delegateTask, Collections.singletonList(level4ApproveUserCode));
        } else if (EnumApprovalNode.LEVEL_5_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 五级审批节点
            addApproveUser(delegateTask, Collections.singletonList(level5ApproveUserCode));
        }
    }
}
