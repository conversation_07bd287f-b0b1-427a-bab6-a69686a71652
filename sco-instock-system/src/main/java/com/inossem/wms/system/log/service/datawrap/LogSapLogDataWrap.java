package com.inossem.wms.system.log.service.datawrap;

import com.inossem.wms.common.model.log.entity.LogSapLog;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.system.log.dao.LogSapLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * SAP异常日志表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-01
 */
@Service
public class LogSapLogDataWrap extends BaseDataWrap<LogSapLogMapper, LogSapLog> {

}
