package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 仓储管理-成套领料申请外部部门-SO-审批流-监听器
 **/
@Service
public class ListenerUnitizedMaterialApplyOutsideSO extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_CALLBACK_UNITIZED_MATERIAL_OUT_APPLY);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 承包商主管部门专业工程师
            String counterpartDeptCode = delegateTask.getVariable("counterpartDeptCode").toString();
            String counterpartOfficeCode = delegateTask.getVariable("counterpartOfficeCode").toString();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(counterpartDeptCode, counterpartOfficeCode, EnumApprovalLevel.LEVEL_8);
            addApproveUser(delegateTask, userList);

        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 生产准备部部门负责人
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.PRD.getCode(), null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userList);
        }
    }
}
