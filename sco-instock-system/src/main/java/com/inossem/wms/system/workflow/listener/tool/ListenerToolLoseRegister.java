package com.inossem.wms.system.workflow.listener.tool;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import java.util.HashSet;
import java.util.Set;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工器具管理-遗失登记审批流-监听器
 *
 * <AUTHOR>
 * @date 2022/04/19 13:40
 **/
@Service
public class ListenerToolLoseRegister extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    /**
     * 工器具遗失登记审批结束回调处理(approvalCallBack限定了结束节点才做处理)
     * 通过MQ回调业务模块的审批结束处理
     */
    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_CALLBACK_LOSE_REGISTER);
    }

    /**
     * 工器具遗失登记审批任务流转
     * 任务定义4级审批（除发起人以外）
     * 第一级：发起人所在科室的【科室负责人】审批
     * 第二级：维修支持科的【科室人员】审批
     * 第三级：发起人所在部门的【部门负责人】审批
     * 第四级：维修部的【部门负责人】审批
     */
    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDept = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");

        // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
        Set<String> alreadySendUserSet = new HashSet<>();

        // 根据节点，自定义业务需求
        // 第一级：发起人所在科室的【科室负责人】审批
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人，发起人所属部门2级审批人（即：发起人所在科室的【科室负责人】审批）
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门的2级审批人
                String deptCode = deptOfficePO.getDeptCode();
                String officeCode = deptOfficePO.getDeptOfficeCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_2);

                // 排除掉已经发过待办信息的用户
                userList.removeAll(alreadySendUserSet);
                addApproveUser(delegateTask, userList);
                alreadySendUserSet.addAll(userList);
            }

        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点动态配置审批人，维修部-维修支持科-一级审批人
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.MTD, EnumOffice.MTD07, EnumApprovalLevel.LEVEL_1);
            addApproveUser(delegateTask, userCodeList);
        } else if (EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 三级审批节点动态配置审批人，发起人所在部门的【部门负责人】审批
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门的2级审批人
                String deptCode = deptOfficePO.getDeptCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);

                // 排除掉已经发过待办信息的用户
                userList.removeAll(alreadySendUserSet);
                addApproveUser(delegateTask, userList);
                alreadySendUserSet.addAll(userList);
            }
        }else if (EnumApprovalNode.LEVEL_4_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 四级审批节点配置审批人，维修部【部门负责人】审批
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.MTD.getCode(), null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userCodeList);
        }
    }


}
