package com.inossem.wms.system.print.controller;


import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.print.template.dto.DicPrintTemplateDTO;
import com.inossem.wms.common.model.print.template.po.DicPrintTemplatePO;
import com.inossem.wms.common.model.print.template.po.DicPrintTemplateQueryPO;
import com.inossem.wms.system.print.service.biz.PrintTemplateService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import org.springframework.web.bind.annotation.RestController;

import static com.inossem.wms.common.constant.Const.BIZ_CONTEXT_KEY_VO;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@RestController
public class PrintTemplateController {

    @Autowired
    PrintTemplateService printTemplateService;

    /**
     * 创建自定义打印模板
     *
     */
    @ApiOperation(value = "创建自定义打印模板", tags = {"打印管理"})
    @PostMapping(path = "/print/template", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> printTempCreate(@RequestBody DicPrintTemplateDTO po, BizContext ctx) {
        printTemplateService.printTempCreate(ctx);
        return BaseResult.success();
    }

    /**
     * 修改自定义打印模板
     * @param po po
     * @param ctx ctx
     * @return BaseResult<?>
     */
    @ApiOperation(value = "修改自定义打印模板", tags = {"打印管理"})
    @PutMapping(path = "/print/template", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> printTempUpdate(@RequestBody DicPrintTemplateDTO po, BizContext ctx) {
        printTemplateService.printTempUpdate(ctx);
        return BaseResult.success();
    }

    /**
     * 删除自定义打印模板
     * @param id id
     * @param ctx ctx
     * @return BaseResult
     */
    @ApiOperation(value = "删除自定义打印模板", tags = {"打印管理"})
    @DeleteMapping(path = "/print/template/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> printTempDelete(@PathVariable("id")Long id, BizContext ctx) {
        printTemplateService.printTempDelete(ctx);
        return BaseResult.success();
    }

    /**
     * 查看详情
     * @param id id
     * @param ctx ctx
     * @return BaseResult<DicPrintTemplateDTO>
     */
    @ApiOperation(value = "查看自定义打印模板详情", tags = {"打印管理"})
    @GetMapping(path = "/print/template/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicPrintTemplateDTO>> printTempQuery(@PathVariable("id")Long id, BizContext ctx) {
        printTemplateService.printTempQuery(ctx);
        return BaseResult.success(new SingleResultVO<>(ctx.getContextData(BIZ_CONTEXT_KEY_VO)));
    }

    /**
     * 根据单据类型查询打印模板结构
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "根据单据类型查询打印模板结构", tags = {"打印管理"})
    @PostMapping(path = "/print/template/struct", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<Object>> printTempStruct(@RequestBody DicPrintTemplateQueryPO po, BizContext ctx) {
        return BaseResult.success(printTemplateService.printTempStruct(ctx));
    }


    /**
     * 自定义打印模板列表
     * @param po po
     * @param ctx ctx
     * @return BaseResult<MultiResultVO<DicPrintTemplateDTO>>
     */
    @ApiOperation(value = "自定义打印模板列表", tags = {"打印管理"})
    @PostMapping(path = "/print/template/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicPrintTemplateDTO>> printTempList(@RequestBody DicPrintTemplatePO po, BizContext ctx) {
        return BaseResult.success(printTemplateService.printTempList(ctx));
    }


    /**
     * 根据单据类型查询打印模板详情
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "根据单据类型查询打印模板详情", tags = {"打印管理"})
    @PostMapping(path = "/print/template/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicPrintTemplateDTO>> printTempInfo(@RequestBody DicPrintTemplateQueryPO po, BizContext ctx) {
        return BaseResult.success(printTemplateService.printTempInfo(ctx));
    }

}

