package com.inossem.wms.system.workflow.service.business.biz;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.ActivitiCommentDTO;
import com.inossem.wms.common.util.UtilBean;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.UserTask;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.ExecutionEntityManager;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/05/15
 *
 * 创建转办任务Command
 *
 **/
public class CreateTransferTaskCommand implements Command<Void> {

    // 流程实例Id
    private final String processInstanceId;
    // bpmnModel
    private BpmnModel bpmnModel;
    private RuntimeService runtimeService;

    /**
     * 转办时提交的批注与附件信息
     */
    private ActivitiCommentDTO activitiCommentDTO;

    /**
     * 转给的目标用户名
     */
    private String transferUserName;

    public CreateTransferTaskCommand(String processInstanceId,
                                     BpmnModel bpmnModel,
                                     RuntimeService runtimeService, ActivitiCommentDTO activitiCommentDTO, String transferUserName) {
        this.processInstanceId = processInstanceId;
        this.bpmnModel = bpmnModel;
        this.runtimeService = runtimeService;
        this.activitiCommentDTO = activitiCommentDTO;
        this.transferUserName = transferUserName;

    }

    @Override
    public Void execute(CommandContext commandContext) {
        ExecutionEntityManager executionEntityManager = commandContext.getExecutionEntityManager();

        UserTask userTask = (UserTask) bpmnModel.getFlowElement(EnumApprovalNode.TRANSFER_NODE.getValue());

        if(userTask == null){
            throw new WmsException(EnumReturnMsg.NO_MSG_1881);
        }

        UserTask tempUserTask = UtilBean.newInstance(userTask, UserTask.class);
        tempUserTask.setName(StringUtils.join(userTask.getName(), " -> ", transferUserName));

        // 创建子执行流，开启任务
        ExecutionEntity processExecution = executionEntityManager.findById(processInstanceId);
        ExecutionEntity childExecution = executionEntityManager.createChildExecution(processExecution);
        childExecution.setCurrentFlowElement(tempUserTask);

        childExecution.setVariable("activitiCommentInfo", JSONObject.toJSONString(activitiCommentDTO));

        commandContext.getExecutionEntityManager().insert(childExecution);
        // 交给activiti流转
        commandContext.getAgenda().planContinueProcessOperation(childExecution);

        return null;
    }


}

