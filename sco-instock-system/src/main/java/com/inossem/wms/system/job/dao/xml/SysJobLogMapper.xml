<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.system.job.dao.SysJobLogMapper">
    <select id="truncateLog">
        truncate table sys_job_log
    </select>
    <select id="selectSysJobLogPageVOList" resultType="com.inossem.wms.common.model.job.vo.SysJobLogPageVO">
        select
        sjl.id, sjl.job_name, sjl.job_group, sjl.invoke_target, sjl.job_message, sjl.status,
        sjl.exception_info, sjl.start_time, sjl.stop_time, sjl.create_time, sjl.modify_time
        from sys_job_log sjl
         ${ew.customSqlSegment}
        ORDER BY `create_time` DESC
    </select>
</mapper>
