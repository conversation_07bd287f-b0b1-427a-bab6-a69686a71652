package com.inossem.wms.system.log.setting;

import com.inossem.wms.common.constant.Const;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.Logger;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.db.ColumnMapping;
import org.apache.logging.log4j.core.appender.db.jdbc.ColumnConfig;
import org.apache.logging.log4j.core.appender.db.jdbc.JdbcAppender;
import org.apache.logging.log4j.core.config.Configuration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 说明: 通过java代码给log4j2配置JdbcAppender 不通过log4j2.xml配置，因为数据源的初始化在log4j2.xml后才会加载 会导致初始化log4j2.xml初始化失败，数据源为空报空指针异常。
 * 
 * <AUTHOR> 创建时间: 2020年3月13日
 */
@Component
public class Log4j2JdbcConfig {

    @Autowired
    private WmsConnectionSource wmsConnection;

    @PostConstruct
    public void init() {
        initLoginAppender();
        initSapAppender();
        initExceptionAppender();
    }

    private void initLoginAppender() {
        final LoggerContext ctx = (LoggerContext)LogManager.getContext(false);
        final Configuration config = ctx.getConfiguration();
        // 获取对应的logger名
        final Logger loginLogger = ctx.getLogger(Const.LOGGER_NAME_LOGIN_LOG);

        ColumnConfig[] columnConfigs = {ColumnConfig.newBuilder().setName("user_id").setPattern("%X{userId}").build(),
            ColumnConfig.newBuilder().setName("user_name").setPattern("%X{userName}").build(),
            ColumnConfig.newBuilder().setName("request_url").setPattern("%X{requestUrl}").build(),
            ColumnConfig.newBuilder().setName("request_source").setPattern("%X{requestSource}").build(),
            ColumnConfig.newBuilder().setName("exception_name").setPattern("%X{exceptionName}").build(),
            ColumnConfig.newBuilder().setName("equipment_number").setPattern("%X{equipmentNumber}").build(),
            ColumnConfig.newBuilder().setName("operation_type").setPattern("%X{operationType}").build(),
            ColumnConfig.newBuilder().setName("param_map").setPattern("%X{paramMap}").build(),
            ColumnConfig.newBuilder().setName("create_date").setPattern("%d{yyyy-MM-dd}").build(),
            ColumnConfig.newBuilder().setName("create_time").setPattern("%d{HH:mm:ss.SSS}").build(),
            ColumnConfig.newBuilder().setName("exec_status").setPattern("%X{execStatus}").build(),
            ColumnConfig.newBuilder().setName("exec_code").setPattern("%X{execCode}").build(),
            ColumnConfig.newBuilder().setName("stack_trace").setPattern("%X{stackTrace}").build(),
            ColumnConfig.newBuilder().setName("exec_description").setPattern("%X{execDescription}").build()};

        /* ColumnMapping与ColumnConfig均为数据库表设置列对应字段的配置
         * 这是一个二选一的配置，这里使用了ColumnConfig的方式，故将 ColumnMapping设置为空数组
         * 不能直接设置null的原因是log4j2源码上初始化此配置时没有做空值判断。
         * */
        ColumnMapping[] columnMappings = {};

        Appender appender = JdbcAppender.newBuilder().setName("loginlog").setTableName("log_login_log").setColumnConfigs(columnConfigs)
            .setColumnMappings(columnMappings).setConnectionSource(wmsConnection).build();
        config.addAppender(appender);
        loginLogger.addAppender(appender);
        loginLogger.setLevel(Level.INFO);
        appender.start();
        ctx.updateLoggers();
    }

    private void initSapAppender() {
        final LoggerContext ctx = (LoggerContext)LogManager.getContext(false);
        final Configuration config = ctx.getConfiguration();
        // 需要写日志到数据库的包名
        final Logger sapLogger = ctx.getLogger(Const.LOGGER_NAME_SAP_LOG);

        ColumnConfig[] columnConfigs = {
                ColumnConfig.newBuilder().setName("sap_result").setPattern("%X{result}").build(),
                ColumnConfig.newBuilder().setName("return_msg").setPattern("%X{returnMsg}").build(),
                ColumnConfig.newBuilder().setName("request_source").setPattern("%X{requestSource}").build(),
                ColumnConfig.newBuilder().setName("interface_type").setPattern("%X{interfaceType}").build(),
                ColumnConfig.newBuilder().setName("equipment_number").setPattern("%X{equipmentNumber}").build(),
                ColumnConfig.newBuilder().setName("receipt_code").setPattern("%X{receiptCode}").build(),
                ColumnConfig.newBuilder().setName("data_flow_direction").setPattern("%X{dataFlowDirection}").build(),
                ColumnConfig.newBuilder().setName("url").setPattern("%X{url}").build(),
                ColumnConfig.newBuilder().setName("in_param").setPattern("%X{inParam}").build(),
                ColumnConfig.newBuilder().setName("out_param").setPattern("%X{outParam}").build(),
                ColumnConfig.newBuilder().setName("user_name").setPattern("%X{userName}").build(),
                ColumnConfig.newBuilder().setName("request_type").setPattern("%X{requestType}").build(),
                ColumnConfig.newBuilder().setName("create_date").setPattern("%d{yyyy-MM-dd}").build(),
                ColumnConfig.newBuilder().setName("create_time").setPattern("%d{HH:mm:ss.SSS}").build(),
                ColumnConfig.newBuilder().setName("exec_status").setPattern("%X{execStatus}").build(),
                ColumnConfig.newBuilder().setName("exec_code").setPattern("%X{execCode}").build(),
                ColumnConfig.newBuilder().setName("exec_description").setPattern("%X{execDescription}").build(),
                ColumnConfig.newBuilder().setName("receipt_id").setPattern("%X{receiptId}").build(),
                ColumnConfig.newBuilder().setName("receipt_type").setPattern("%X{receiptType}").build(),
                ColumnConfig.newBuilder().setName("interface_describe").setPattern("%X{interfaceDescribe}").build(),
                ColumnConfig.newBuilder().setName("interface_status").setPattern("%X{interfaceStatus}").build(),
        };

        ColumnMapping[] columnMappings = {};

        Appender appender = JdbcAppender.newBuilder().setName("saplog").setTableName("log_sap_log").setColumnConfigs(columnConfigs)
            .setColumnMappings(columnMappings).setConnectionSource(wmsConnection).build();
        config.addAppender(appender);
        sapLogger.addAppender(appender);
        sapLogger.setLevel(Level.INFO);
        appender.start();
        ctx.updateLoggers();
    }

    private void initExceptionAppender() {
        final LoggerContext ctx = (LoggerContext)LogManager.getContext(false);
        final Configuration config = ctx.getConfiguration();
        // 需要写日志到数据库的包名
        final Logger exceptionLogger = ctx.getLogger(Const.LOGGER_NAME_EXCEPTION_LOG);

        ColumnConfig[] columnConfigs = {ColumnConfig.newBuilder().setName("user_id").setPattern("%X{userId}").build(),
            ColumnConfig.newBuilder().setName("user_name").setPattern("%X{userName}").build(),
            ColumnConfig.newBuilder().setName("request_url").setPattern("%X{requestUrl}").build(),
            ColumnConfig.newBuilder().setName("exception_message").setPattern("%X{exceptionMessage}").build(),
            ColumnConfig.newBuilder().setName("stack_trace").setPattern("%X{stackTrace}").build(),
            ColumnConfig.newBuilder().setName("create_Date").setPattern("%d{yyyy-MM-dd}").build(),
            ColumnConfig.newBuilder().setName("create_Time").setPattern("%d{HH:mm:ss.SSS}").build(),
            ColumnConfig.newBuilder().setName("exec_status").setPattern("%X{execStatus}").build(),
            ColumnConfig.newBuilder().setName("exec_code").setPattern("%X{execCode}").build(),
            ColumnConfig.newBuilder().setName("exec_description").setPattern("%X{execDescription}").build()};

        ColumnMapping[] columnMappings = {};

        Appender appender = JdbcAppender.newBuilder().setName("exceptionlog").setTableName("log_exception_log").setColumnConfigs(columnConfigs)
            .setColumnMappings(columnMappings).setConnectionSource(wmsConnection).build();
        config.addAppender(appender);
        exceptionLogger.addAppender(appender);
        exceptionLogger.setLevel(Level.INFO);
        appender.start();
        ctx.updateLoggers();
    }

}
