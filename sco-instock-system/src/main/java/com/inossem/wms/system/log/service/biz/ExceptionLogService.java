package com.inossem.wms.system.log.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.log.entity.LogExceptionLog;
import com.inossem.wms.common.model.log.po.LogSearchPO;
import com.inossem.wms.common.util.UtilGzip;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.system.log.service.datawrap.LogExceptionLogDataWrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 异常日志服务层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-01
 */
@Component
public class ExceptionLogService {

    @Autowired
    LogExceptionLogDataWrap logExceptionLogDataWrap;

    public PageObjectVO<LogExceptionLog> exceptionLogPageList(LogSearchPO po) {

        // 分页查询部分
        Page<LogExceptionLog> page = new Page<>(po.getPageIndex(), po.getPageSize());
        QueryWrapper<LogExceptionLog> queryWrapper = new QueryWrapper<>();
        // 判断日期是否为空
        if (UtilObject.isNotEmpty(po.getCreateDate()) && UtilObject.isNotEmpty(po.getEndDate())) {
            // 取日期范围内
            queryWrapper.lambda().between(LogExceptionLog::getCreateDate, po.getCreateDate(), po.getEndDate());
        }
        queryWrapper.lambda().orderByDesc(LogExceptionLog::getCreateDate).orderByDesc(LogExceptionLog::getCreateTime);
        Page<LogExceptionLog> pageList = logExceptionLogDataWrap.page(page, queryWrapper);
        PageObjectVO<LogExceptionLog> pageObjectVo = new PageObjectVO<>(pageList.getRecords(), pageList.getTotal());

        // 解压(由于错误堆栈信息过多，所以将其压缩后再存储到mysql中)
        for (LogExceptionLog exceptionLogModel : pageObjectVo.getResultList()) {
            String stackTrace = UtilGzip.uncompresscompressStringToString(exceptionLogModel.getStackTrace());
            exceptionLogModel.setStackTrace(stackTrace);
        }
        return pageObjectVo;
    }

}
