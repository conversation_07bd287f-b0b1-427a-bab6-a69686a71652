package com.inossem.wms.system.file.util;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.file.img.entity.BizCommonImage;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.UUID;

/**
 * 文件操作
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021/5/18
 */
@Slf4j
public class UtilFile {

    /**
     * 获取文件路径
     */
    private static String getFilePath() {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        yyyyMmDd = yyyyMmDd.replaceAll("-", "");
        String yyyy = yyyyMmDd.substring(0, 4);
        String yyyyMm = yyyyMmDd.substring(0, 6);

        return yyyy + File.separatorChar + yyyyMm + File.separatorChar + yyyyMmDd + File.separatorChar;
    }

    /**
     * 获取文件名
     */
    private static String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 附件上传
     * @param fileInClient 文件
     */
    public static BizCommonFile uploadFile(MultipartFile fileInClient) {
        return uploadFile(fileInClient,null);
    }

    /**
     * 附件上传
     * @param fileInClient 文件
     */
    public static BizCommonFile uploadFile(MultipartFile fileInClient ,String reallyFileName) {
        //附件信息
        String fileName = fileInClient.getOriginalFilename();;
        Long fileSize = fileInClient.getSize();
        String ext = "";
        if(UtilString.isNotNullOrEmpty(fileName)){
            int extIndex = fileName.lastIndexOf('.');
            if (fileName.length() - extIndex < 10) {
                ext = fileName.substring(extIndex + 1);
            }
        }
        // 服务器端文件全路径
        String fileRootPath = UtilConst.getInstance().getFilePath();
        String filePath = getFilePath();
        String fileCode = getFileCode(ext);
        if(StringUtils.isNotEmpty(reallyFileName)){
            fileName=reallyFileName;
        }
        File fileInServerFolder = new File(fileRootPath + filePath);
        File fileInServer = new File(fileRootPath + filePath, fileCode);

        if (!fileInServerFolder.exists()) {
            fileInServerFolder.mkdirs();
        }
        // MultipartFile自带的解析方法
        try {
            fileInClient.transferTo(fileInServer);
        } catch (IOException e) {
            e.printStackTrace();
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }

        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(fileCode);
        bizCommonFile.setFileName(fileName);
        bizCommonFile.setFileSize(fileSize);
        bizCommonFile.setPath(filePath);
        bizCommonFile.setFileExt(ext);
        return bizCommonFile;
    }


    /**
     * 附件下载
     * @param bizCommonFile 附件对象
     * @param response response
     */
    public static void downloadFile(BizCommonFile bizCommonFile, HttpServletResponse response){
        InputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        try {
            String fileRootPath = UtilConst.getInstance().getFilePath();
            String filePath = bizCommonFile.getPath();
            String fileCode = bizCommonFile.getFileCode();
            String fileName = bizCommonFile.getFileName();
            String fileExt = bizCommonFile.getFileExt();
            Long fileSize = bizCommonFile.getFileSize();

            if (UtilString.isNotNullOrEmpty(fileRootPath) &&
                UtilString.isNotNullOrEmpty(filePath) &&
                UtilString.isNotNullOrEmpty(fileCode) ) {

                // 服务器端文件全路径
                File fileInServer = new File(fileRootPath + filePath, fileCode);
                // 获取输入流
                inputStream = new BufferedInputStream(new FileInputStream(fileInServer));

                // 转码，免得文件名中文乱码
                // String fileCodeForUTF8 = URLEncoder.encode(fileCode, "UTF-8");
                String fileNameForUTF8 = URLEncoder.encode(fileName, "UTF-8");
                String fileFullName = fileNameForUTF8 + '.' + fileExt;
                String[] fileNameArgs=fileNameForUTF8.split("\\.");
                if(fileNameArgs.length>1){
                    if(fileNameArgs[fileNameArgs.length-1].equals(fileExt)){
                        fileFullName=fileNameForUTF8;
                    }
                }
                // 设置文件下载头
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                response.addHeader("Content-Disposition", "attachment;filename=" + fileFullName);
                // 设置文件ContentType类型，这样设置，会自动判断下载文件类型
                response.setContentType("multipart/form-data");
                // 设置文件大小，用于前端显示文件大小和判断下载进度
                response.setContentLengthLong(fileSize);
                // 关闭http长连接，避免调用flush时将文件大小的设置自动取消，变成Transfer-Encoding: chunked
                response.setHeader("connection", "close");
                outputStream = new BufferedOutputStream(response.getOutputStream());
                int len = 0;
                while ((len = inputStream.read()) != -1) {
                    outputStream.write(len);
                }
                outputStream.flush();
            }
        } catch (ClientAbortException e) {
            log.info("文件下载取消");
        } catch (Exception e) {
            log.error("文件下载失败");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.info("客户端取消下载时，不再重复输出日志内容");
            }

        }
    }

    /**
     * 附件删除
     * @param bizCommonFile 附件对象
     */
    public static void deleteFile(BizCommonFile bizCommonFile) {
        try {
            String fileRootPath = UtilConst.getInstance().getFilePath();
            String filePath = bizCommonFile.getPath();
            String fileCode = bizCommonFile.getFileCode();

            if (UtilString.isNotNullOrEmpty(fileRootPath) &&
                    UtilString.isNotNullOrEmpty(filePath) &&
                    UtilString.isNotNullOrEmpty(fileCode) ) {

                // 服务器端文件全路径
                File file = new File(fileRootPath + filePath, fileCode);

                if (file.exists()) {
                    file.delete();
                }
            }
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FILE_DELETE_FAILURE);
        }
    }

    /**
     * 图片上传
     * @param fileInClient 图片
     */
    public static BizCommonImage uploadImg(MultipartFile fileInClient) {
        // file 转 Base64 字符串
        String preffix="data:image/jpg;base64,";
        String base64EncoderImg = null;
        try {
            base64EncoderImg = preffix + Base64.encodeBase64String(fileInClient.getBytes());
            if (fileInClient.getBytes().length > 15000000) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMAGES_SIZE_LARGER);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        //图片信息
        String fileName = fileInClient.getOriginalFilename();;
        Long fileSize = fileInClient.getSize();
        String ext = "";
        if(UtilString.isNotNullOrEmpty(fileName)){
            int extIndex = fileName.lastIndexOf('.');
            if (fileName.length() - extIndex < 10) {
                ext = fileName.substring(extIndex + 1);
            }
        }
        // 服务器端文件全路径
        String fileRootPath = UtilConst.getInstance().getImgPath();
        String filePath = getFilePath();
        String fileCode = getFileCode(ext);

        File fileInServerFolder = new File(fileRootPath + filePath);
        File fileInServer = new File(fileRootPath + filePath, fileCode);

        String fileMd5="";
        String padFileCode="";

        if (!fileInServerFolder.exists()) {
            fileInServerFolder.mkdirs();
        }
        // MultipartFile自带的解析方法
        try {
            fileInClient.transferTo(fileInServer);

            // 获取图片指纹
            fileMd5 = DigestUtils.md5Hex(new FileInputStream(fileInServer));

            /* *** web上传,判断图片大小,取宽高中最小值,先裁剪,从中心区域开始,然后进行200*200压缩 **********/
            Image img = ImageIO.read(fileInServer);
            int imgWidth = img.getWidth(null);
            int imgHeight = img.getHeight(null);

            /* *** pad图片,判断图片尺寸,然后取最大边与200为比例基数进行压缩 **********/
            int height = 200;
            int width = (imgWidth * 200 / imgHeight);
            if (imgWidth > imgHeight) {
                width = 200;
                height = (imgHeight * 200 / imgWidth);
            }
            //缩略图处理
            String targetPath = fileRootPath + filePath + File.separatorChar+ fileInServer.getName() + "_" + width + "X" + height + "."+ ext;
            File padFile = new File(targetPath);
            Thumbnails.of(fileInServer)
                    .size(width, height)
                    .keepAspectRatio(false)
                    .outputFormat(ext)
                    .toFile(padFile);
            if (padFile != null) {
                padFileCode = padFile.getName();
            }
            // ********end**********

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }

        BizCommonImage bizCommonImage = new BizCommonImage();
        bizCommonImage.setImgCode(fileCode);
        bizCommonImage.setImgName(fileName);
        bizCommonImage.setImgWebPath(fileCode);
        bizCommonImage.setImgPadPath(padFileCode);
        bizCommonImage.setImgMd5(fileMd5);
        bizCommonImage.setImgBase64(base64EncoderImg);
        bizCommonImage.setPath(filePath);
        bizCommonImage.setImgExt(ext);
        bizCommonImage.setImgSize(fileSize);

        return bizCommonImage;
    }

    /**
     * 图片下载
     * @param bizCommonImage 图片对象
     * @param response response
     */
    public static void downloadImg(BizCommonImage bizCommonImage, HttpServletResponse response){
        InputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        try {
            String fileRootPath = UtilConst.getInstance().getFilePath();
            String filePath = bizCommonImage.getPath();
            String fileCode = bizCommonImage.getImgCode();

            if (UtilString.isNotNullOrEmpty(fileRootPath) &&
                    UtilString.isNotNullOrEmpty(filePath) &&
                    UtilString.isNotNullOrEmpty(fileCode) ) {

                // 服务器端文件全路径
                File fileInServer = new File(fileRootPath + filePath, fileCode);
                // 获取输入流
                inputStream = new BufferedInputStream(new FileInputStream(fileInServer));

                // 转码，免得文件名中文乱码
                String fileCodeForUTF8 = URLEncoder.encode(fileCode, "UTF-8");
                // 设置文件下载头
                response.addHeader("Content-Disposition", "attachment;filename=" + fileCodeForUTF8);
                // 设置强制下载不打开
                response.setContentType("application/force-download");

                outputStream = new BufferedOutputStream(response.getOutputStream());
                int len = 0;
                while ((len = inputStream.read()) != -1) {
                    outputStream.write(len);
                }
                outputStream.flush();
            }
        } catch (ClientAbortException e) {
            log.info("图片下载取消");
        } catch (Exception e) {
            log.error("图片下载失败");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.info("客户端取消下载时，不再重复输出日志内容");
            }

        }
    }

    /**
     * 图片删除
     * @param bizCommonImage 图片对象
     */
    public static void deleteImg(BizCommonImage bizCommonImage) {
        try {
            String fileRootPath = UtilConst.getInstance().getImgPath();
            String filePath = bizCommonImage.getPath();
            String fileCode = bizCommonImage.getImgCode();

            if (UtilString.isNotNullOrEmpty(fileRootPath) &&
                    UtilString.isNotNullOrEmpty(filePath) &&
                    UtilString.isNotNullOrEmpty(fileCode) ) {

                // 服务器端文件全路径
                File file = new File(fileRootPath + filePath, fileCode);

                if (file.exists()) {
                    file.delete();
                }
            }
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FILE_DELETE_FAILURE);
        }
    }

    /**
     * 图片预览
     * @param bizCommonImage 图片文件
     * @param type 平台类型
     */
    public static byte[] previewImg(BizCommonImage bizCommonImage, String type){
        String fileRootPath = UtilConst.getInstance().getImgPath();
        String filePath = bizCommonImage.getPath();
        String fileCode = bizCommonImage.getImgCode();

        if ("pad".equals(type)) {
            fileCode = bizCommonImage.getImgPadPath();
        }

        File file = new File(fileRootPath + filePath, fileCode);

        try {
            FileInputStream inputStream = new FileInputStream(file);
            byte[] bytes = new byte[inputStream.available()];
            inputStream.read(bytes, 0, inputStream.available());
            inputStream.close();
            return bytes;
        } catch (IOException e){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }
}
