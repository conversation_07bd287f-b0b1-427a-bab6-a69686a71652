package com.inossem.wms.system.job.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.job.dto.SysJobDTO;
import com.inossem.wms.common.model.job.entity.SysJob;
import com.inossem.wms.common.model.job.po.SysJobSavePO;
import com.inossem.wms.common.model.job.po.SysJobSearchPO;
import com.inossem.wms.common.model.job.vo.SysJobPageVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.job.service.datawrap.SysJobDataWrap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;

/**
 * <AUTHOR>
 * @date 2021/3/3 15:30
 */
@Service
@Slf4j
public class JobServiceI {

    @Autowired
    private Scheduler scheduler;

    @Autowired
    private SysJobDataWrap sysJobDataWrap;

    /** 允许 */
    private static final String PERMIT = "1";
    /** 禁止 */
    private static final String FORBID = "0";

    /**
     * 项目启动时，初始化定时器 主要是防止手动修改数据库导致未同步到定时任务处理（注：不能手动修改数据库ID和任务组名，否则会导致脏数据）
     */
    @PostConstruct
    public void init() throws SchedulerException, SocketException {
        log.info("定时任务配置初始化开始..");
        scheduler.clear();
        List<SysJob> jobList = sysJobDataWrap.list();
        Map<String,SysJob> jobMap=new HashMap<>();
        for (SysJob job : jobList) {
            //未开启并发执行，需要输入执行定时任务的服务器IP
            //开启并发执行，会在多个机器上执行定时任务
            //0禁止 1允许
            if(job.getConcurrent().equals(PERMIT)){
                log.info("多机器并发job:"+job.getJobName());
                WmsQuartzSchedule.createScheduleJob(scheduler, job);
            }else if(job.getConcurrent().equals(FORBID)){
                Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
                InetAddress ip;
                List<String> ips=new ArrayList<>();
                while (allNetInterfaces.hasMoreElements()) {
                    NetworkInterface netInterface = allNetInterfaces.nextElement();
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        ip = addresses.nextElement();
                        if (ip instanceof Inet4Address) {
                            ips.add(ip.getHostAddress());
                            log.debug("本机的IP = " + ip.getHostAddress() + " job: " + job.getId() + " - " + job.getJobName());
                        }
                    }
                }
                for (String ipf : ips) {
                    if(job.getRemark().contains(ipf)){
                        jobMap.put(ipf+job.getId(),job);
                    }
                }
            }
        }
        //添加任务
        jobMap.forEach((key,job)->{
            try {
                log.debug("非并发添加定时任务 IP:"+key+"job:"+job.getJobName());
                WmsQuartzSchedule.createScheduleJob(scheduler, job);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info("定时任务配置初始化完成");
    }

    /**
     * 获取quartz调度器的计划任务列表
     * @param sysJobSearchPo 调度信息
     * @return PageObjectVO
     */
    public PageObjectVO<SysJobPageVO> getPage(SysJobSearchPO sysJobSearchPo) {
        if (sysJobSearchPo == null) {
            sysJobSearchPo = new SysJobSearchPO();
        }

        QueryWrapper<SysJobSearchPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(sysJobSearchPo.getJobName()), SysJobSearchPO::getJobName, sysJobSearchPo.getJobName())
                .eq(UtilString.isNotNullOrEmpty(sysJobSearchPo.getJobGroup()), SysJobSearchPO::getJobGroup, sysJobSearchPo.getJobGroup())
                .eq(UtilString.isNotNullOrEmpty(sysJobSearchPo.getStatus()), SysJobSearchPO::getStatus, sysJobSearchPo.getStatus())
                .like(UtilString.isNotNullOrEmpty(sysJobSearchPo.getInvokeTarget()), SysJobSearchPO::getInvokeTarget, sysJobSearchPo.getInvokeTarget());
        IPage<SysJobPageVO> page = sysJobSearchPo.getPageObj(SysJobPageVO.class);
        sysJobDataWrap.getSysJobPageVOList(page, wrapper);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 通过调度任务ID查询调度信息
     *
     * @param jobId 调度任务ID
     * @return 调度任务对象信息
     */
    public SysJobDTO getJobById(Long jobId) {
        return UtilBean.newInstance(sysJobDataWrap.getById(jobId), SysJobDTO.class);
    }

    /**
     * 暂停任务
     *
     * @param sysJobSavePo 调度信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void pauseJob(SysJobSavePO sysJobSavePo) throws SchedulerException {
        SysJobDTO sysJobDto = sysJobSavePo.getSysJobDto();
        Long jobId = sysJobDto.getId();
        String jobGroup = sysJobDto.getJobGroup();
        sysJobDto.setStatus(Const.TASK_STATUS_PAUSE);
        boolean b = sysJobDataWrap.updateById(UtilBean.newInstance(sysJobDto, SysJob.class));
        if (b) {
            scheduler.pauseJob(WmsQuartzSchedule.getJobKey(jobId, jobGroup));
        }
    }

    /**
     * 恢复任务
     *
     * @param sysJobSavePo 调度信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void resumeJob(SysJobSavePO sysJobSavePo) throws SchedulerException {
        SysJob sysJob = UtilBean.newInstance(sysJobSavePo.getSysJobDto(), SysJob.class);
        Long jobId = sysJob.getId();
        String jobGroup = sysJob.getJobGroup();
        sysJob.setStatus(Const.TASK_STATUS_NORMAL);
        boolean b = sysJobDataWrap.updateById(sysJob);
        if (b) {
            scheduler.resumeJob(WmsQuartzSchedule.getJobKey(jobId, jobGroup));
        }
    }

    /**
     * 删除任务后，所对应的trigger也将被删除
     *
     * @param job 调度信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteJob(SysJob job) throws SchedulerException {
        Long jobId = job.getId();
        String jobGroup = job.getJobGroup();
        boolean b = sysJobDataWrap.removeById(jobId);
        if (b) {
            scheduler.deleteJob(WmsQuartzSchedule.getJobKey(jobId, jobGroup));
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_JOB_DELETE_FAILURE);
        }
    }

    /**
     * 批量删除调度信息
     *
     * @param jobIds 需要删除的任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void multiDeleteJobByIds(Long[] jobIds) throws SchedulerException {
        List<Long> longList = Arrays.asList(jobIds);
        List<SysJob> sysJobList = sysJobDataWrap.listByIds(longList);
        for (SysJob sysJob : sysJobList) {
            deleteJob(sysJob);
        }
    }

    /**
     * 任务调度状态修改
     *
     * @param sysJobSavePo 调度信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(SysJobSavePO sysJobSavePo) throws SchedulerException {
        SysJob sysJob = UtilBean.newInstance(sysJobSavePo.getSysJobDto(), SysJob.class);
        sysJobDataWrap.updateById(sysJob);
        String status = sysJob.getStatus();
        if (Const.TASK_STATUS_NORMAL.equals(status)) {
            resumeJob(sysJobSavePo);
        } else if (Const.TASK_STATUS_PAUSE.equals(status)) {
            pauseJob(sysJobSavePo);
        }
    }

    /**
     * 立即运行任务
     *
     * @param job 调度信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void run(SysJob job) throws SchedulerException {
        Long jobId = job.getId();
        SysJob sysJob = sysJobDataWrap.getById(jobId);
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(Const.TASK_PROPERTIES, sysJob);
        scheduler.triggerJob(WmsQuartzSchedule.getJobKey(jobId, sysJob.getJobGroup()), dataMap);
    }


    /**
     * 新增任务
     *
     * @param sysJobSavePo 调度信息 调度信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveJob(SysJobSavePO sysJobSavePo, BizContext ctx) {
        SysJobDTO sysJobDto = sysJobSavePo.getSysJobDto();
        SysJob sysJob = UtilBean.newInstance(sysJobDto, SysJob.class);
        sysJob.setCreateUserId(ctx.getCurrentUser().getId());
        boolean save = sysJobDataWrap.save(sysJob);
        if (save) {
            try {
                WmsQuartzSchedule.createScheduleJob(scheduler, sysJob);
            } catch (Exception e){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INVOKE_TARGET_OR_CRON_EXPRESSION_FORMAT_ERROR);
            }
        }
    }

    /**
     * 更新任务的时间表达式
     *
     * @param sysJobSavePo 调度信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateJob(SysJobSavePO sysJobSavePo, BizContext ctx) {
        SysJob sysJob = UtilBean.newInstance(sysJobSavePo.getSysJobDto(), SysJob.class);
        sysJob.setModifyUserId(ctx.getCurrentUser().getId());
        boolean b = sysJobDataWrap.updateById(sysJob);
        if (b) {
            try {
                updateSchedulerJob(sysJob, sysJob.getJobGroup());
            } catch (Exception e){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INVOKE_TARGET_OR_CRON_EXPRESSION_FORMAT_ERROR);
            }
        }
    }

    /**
     * 更新任务
     *
     * @param job 任务对象
     * @param jobGroup 任务组名
     */
    public void updateSchedulerJob(SysJob job, String jobGroup) throws SchedulerException {
        Long jobId = job.getId();
        // 判断是否存在
        JobKey jobKey = WmsQuartzSchedule.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        WmsQuartzSchedule.createScheduleJob(scheduler, job);
    }

}
