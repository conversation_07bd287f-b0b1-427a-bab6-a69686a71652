package com.inossem.wms.system.job.service.biz;

import com.inossem.wms.common.model.job.entity.SysJob;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;

/**
 * 定时任务处理（禁止并发执行）
 * 
 * @date 2021/3/4 17:48
 * <AUTHOR>
 */
@DisallowConcurrentExecution
public class WmsQuartzDisallowConcurrentExecution extends AbstractQuartzJob {
    @Override
    protected void doExecute(JobExecutionContext context, SysJob sysJob) {
        WmsJobInvoke.invokeMethod(sysJob);
    }
}
