<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.system.workflow.dao.BizApprovalReceiptInstanceRelMapper">

    <update id="updateProcDef">
      update ACT_RE_PROCDEF
      set NAME_ = #{name,jdbcType=VARCHAR} ,DESCRIPTION_ = #{description,jdbcType=VARCHAR}
      where DEPLOYMENT_ID_ = #{deploymentId,jdbcType=VARCHAR}
    </update>

    <select id="getApproveRecordDTO" resultType="com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO">
        WITH T AS (
        SELECT AHA.ID_ AS 'instanceId',
               AHA.PROC_INST_ID_ AS 'proInstanceId',
               AHA.EXECUTION_ID_ AS 'executeId',
               ARP.NAME_ AS 'proInstanceName',
               AHA.ACT_ID_ AS 'actId',
               AHA.ACT_NAME_ AS 'actName',
               AHA.START_TIME_ AS 'startTime',
               AHA.END_TIME_ AS 'endTime',
               AHA.ASSIGNEE_ AS 'assignee',
               SU.user_name AS 'assigneeName',
               CASE WHEN AHC.MESSAGE_ LIKE '%_|_candidate%' THEN '' ELSE AHC.FULL_MSG_ END AS 'message',
               SU.common_img_id AS 'commonImgId',
               AHC.ACTION_
        FROM ACT_HI_ACTINST AHA
            JOIN ACT_RE_PROCDEF ARP ON AHA.PROC_DEF_ID_ = ARP.ID_
            LEFT JOIN sys_user SU ON SU.user_code = AHA.ASSIGNEE_
            LEFT JOIN ACT_HI_COMMENT AHC ON AHC.TASK_ID_ = AHA.TASK_ID_
        WHERE AHA.PROC_INST_ID_ IN (
            SELECT AHP.ID_
            FROM ACT_HI_PROCINST AHP
            WHERE AHP.BUSINESS_KEY_ = #{receiptCode}
        )
        <!-- 因自定义跳转而产生的垃圾数据不显示在审批记录中 -->
        AND (AHA.DELETE_REASON_ is null or AHA.DELETE_REASON_ != 'CumtomJumpGarbageData')
        ORDER BY AHA.START_TIME_, AHA.ID_, AHC.TIME_)
        SELECT * FROM T GROUP BY T.instanceId ORDER BY T.startTime, T.instanceId, T.endTime
    </select>

    <select id="getApproveFinshTime" resultType="com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO">

        WITH T AS (
        SELECT AHA.ID_ AS 'instanceId',
        AHA.PROC_INST_ID_ AS 'proInstanceId',
        AHA.EXECUTION_ID_ AS 'executeId',
        ARP.NAME_ AS 'proInstanceName',
        AHA.ACT_ID_ AS 'actId',
        AHA.ACT_NAME_ AS 'actName',
        AHA.START_TIME_ AS 'startTime',
        AHA.END_TIME_ AS 'endTime',
        AHA.ASSIGNEE_ AS 'assignee',
        SU.user_name AS 'assigneeName',
        CASE WHEN AHC.MESSAGE_ LIKE '%_|_candidate%' THEN '' ELSE AHC.MESSAGE_ END AS 'message',
        SU.common_img_id AS 'commonImgId',
        AHP.BUSINESS_KEY_ as 'receiptCode',
        AHC.ACTION_
        FROM ACT_HI_ACTINST AHA
        JOIN ACT_RE_PROCDEF ARP ON AHA.PROC_DEF_ID_ = ARP.ID_
        LEFT JOIN sys_user SU ON SU.user_code = AHA.ASSIGNEE_
        LEFT JOIN ACT_HI_COMMENT AHC ON AHC.TASK_ID_ = AHA.TASK_ID_
        left join  ACT_HI_PROCINST AHP on AHP.ID_ = AHA.PROC_INST_ID_

        WHERE AHP.BUSINESS_KEY_ in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and AHA.ACT_ID_ =  'Level3ApprovalNode' and AHA.ASSIGNEE_ != ''

        ORDER BY AHA.START_TIME_, AHA.ID_, AHC.TIME_)
        SELECT * FROM T GROUP BY T.instanceId ORDER BY T.startTime, T.instanceId, T.endTime
    </select>

    <select id="getApproveStatus" resultType="java.lang.String">
        SELECT
            AHV.LONG_
        FROM
            ACT_HI_VARINST AHV
        WHERE
            AHV.PROC_INST_ID_ = #{procInstanceId}
          AND AHV.NAME_ = 'agree'
    </select>


</mapper>
