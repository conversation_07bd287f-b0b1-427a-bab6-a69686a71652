package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 仓储管理-成套暂存退旧物资申请审批流-监听器
 */
@Service
public class ListenerUnitizedReturnOldApply extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    /**
     * 成套暂存物项申请审批流程结束回调
     */
    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_CALLBACK_RETURN_OLD_APPLY);
    }

    /**
     * 成套暂存物项申请审批流程，任务节点回调
     */
    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDeptList = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");
        if (UtilCollection.isEmpty(userDeptList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人 部门物资领用主管
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDeptList.get(0).getDeptCode(), null, EnumApprovalLevel.LEVEL_3);
            addApproveUser(delegateTask, userList);
        }
    }

}
